#!/usr/bin/env python3
"""
重启服务并验证修复效果
"""

import os
import subprocess
import sys
import time

import requests


def restart_api_service():
    """重启API服务"""
    print("🔄 重启API服务...")
    
    try:
        # 检查当前API状态
        try:
            response = requests.get("http://127.0.0.1:8888/api/v1/health", timeout=5)
            if response.status_code == 200:
                print("✅ API服务当前正在运行")
            else:
                print("⚠️  API服务状态异常")
        except:
            print("❌ API服务未运行")
        
        print("ℹ️  请手动重启API服务：")
        print("   1. 停止当前API服务（如果正在运行）")
        print("   2. 运行: python start_production_api.py")
        print("   3. 等待服务启动完成")
        
        return True
        
    except Exception as e:
        print(f"❌ API服务重启失败: {e}")
        return False

def verify_api_service():
    """验证API服务状态"""
    print("\n🔍 验证API服务状态...")
    
    max_retries = 10
    for i in range(max_retries):
        try:
            response = requests.get("http://127.0.0.1:8888/api/v1/health", timeout=5)
            if response.status_code == 200:
                print("✅ API服务运行正常")
                return True
        except:
            pass
        
        print(f"⏳ 等待API服务启动... ({i+1}/{max_retries})")
        time.sleep(3)
    
    print("❌ API服务启动超时")
    return False

def test_prediction_fix():
    """测试预测修复效果"""
    print("\n🔍 测试预测修复效果...")
    
    try:
        # 测试数据库状态
        response = requests.get("http://127.0.0.1:8888/api/v1/data/stats", timeout=10)
        if response.status_code == 200:
            stats = response.json()
            total_records = stats.get('total_records', 0)
            print(f"✅ 数据库记录数: {total_records}")
        else:
            print("❌ 无法获取数据库状态")
            return False
        
        # 测试智能融合预测
        response = requests.get(
            "http://127.0.0.1:8888/api/v1/prediction/intelligent-fusion/predict",
            params={
                "prediction_mode": "智能融合",
                "max_candidates": 10,
                "confidence_threshold": 0.5,
                "auto_train": True
            },
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success', False):
                prediction = result.get('prediction', {})
                predicted_number = prediction.get('predicted_number', 'N/A')
                confidence = prediction.get('confidence', 0)
                data_count = result.get('data_count', 0)
                
                print(f"✅ 预测成功")
                print(f"   预测号码: {predicted_number}")
                print(f"   置信度: {confidence:.1%}")
                print(f"   使用数据量: {data_count}")
                
                # 检查是否还是固定的056
                if predicted_number == "056":
                    print("⚠️  警告: 预测结果仍为056，可能需要手动重训练")
                    return False
                else:
                    print("✅ 预测结果已更新，修复成功！")
                    return True
            else:
                print(f"❌ 预测失败: {result.get('error', '未知错误')}")
                return False
        else:
            print(f"❌ 预测API调用失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 预测测试异常: {e}")
        return False

def test_manual_retrain():
    """测试手动重训练功能"""
    print("\n🔍 测试手动重训练功能...")
    
    try:
        response = requests.post(
            "http://127.0.0.1:8888/api/v1/prediction/intelligent-fusion/train",
            timeout=120
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success', False):
                print("✅ 手动重训练成功")
                return True
            else:
                print(f"❌ 手动重训练失败: {result.get('error', '未知错误')}")
                return False
        else:
            print(f"❌ 重训练API调用失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 重训练测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始部署和监控")
    print("=" * 60)
    
    # 重启API服务
    restart_api_service()
    
    # 等待用户手动重启
    input("\n⏸️  请按Enter键继续，确认API服务已重启...")
    
    # 验证API服务
    if not verify_api_service():
        print("❌ API服务验证失败，请检查服务状态")
        return
    
    # 测试预测修复效果
    prediction_success = test_prediction_fix()
    
    # 如果预测结果仍为056，尝试手动重训练
    if not prediction_success:
        print("\n🔄 预测结果仍为056，尝试手动重训练...")
        retrain_success = test_manual_retrain()
        
        if retrain_success:
            print("\n🔍 重新测试预测...")
            prediction_success = test_prediction_fix()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 部署和监控结果")
    print("=" * 60)
    
    if prediction_success:
        print("🎉 修复成功！预测模型数据同步问题已解决")
        print("✅ 预测结果不再固定为056")
        print("✅ 模型使用最新数据进行训练")
    else:
        print("⚠️  修复可能需要进一步调整")
        print("💡 建议：")
        print("   1. 检查数据库是否有最新数据")
        print("   2. 手动触发重训练")
        print("   3. 检查系统日志")
    
    print("\n🎯 下一步建议：")
    print("1. 在Streamlit界面测试重训练功能")
    print("2. 监控预测结果的变化")
    print("3. 验证自动重训练机制")

if __name__ == "__main__":
    main()
