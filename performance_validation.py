#!/usr/bin/env python3
"""
性能验证测试
"""

import sys
import os
import time
import sqlite3
sys.path.append('src')

from src.ui.data_update_components import DataUpdateManager

def test_parsing_performance():
    """测试数据解析性能"""
    print("=== 测试数据解析性能 ===")
    
    # 模拟大量数据解析
    test_lines = [
        "2025186 2025-07-15 2 2 7 1 3 7 1 1 113957536 0 1040 0 346 0 173",
        "2025185 2025-07-14 1 6 4 0 5 5 1 1 109890010 0 1040 0 346 0 173",
        "2025184 2025-07-13 7 9 9 6 3 3 1 1 110880482 0 1040 0 346 0 173"
    ] * 1000  # 重复1000次，总共3000条记录
    
    start_time = time.time()
    
    parsed_count = 0
    for line in test_lines:
        parts = line.split()
        if len(parts) >= 13:
            # 使用修复后的字段映射
            numbers = parts[2] + parts[3] + parts[4]
            trial_numbers = parts[5] + parts[6] + parts[7]
            
            record = {
                'period': parts[0],
                'date': parts[1],
                'numbers': numbers,
                'trial_numbers': trial_numbers,
                'draw_machine': int(parts[8]) if parts[8].isdigit() else 1,
                'trial_machine': int(parts[9]) if parts[9].isdigit() else 1,
                'sales_amount': int(parts[10]) if parts[10].isdigit() else 0,
                'direct_prize': int(parts[12]) if parts[12].isdigit() else 1040,
                'group3_prize': int(parts[14]) if len(parts) > 14 and parts[14].isdigit() else 346,
                'group6_prize': int(parts[16]) if len(parts) > 16 and parts[16].isdigit() else 173
            }
            parsed_count += 1
    
    end_time = time.time()
    parse_time = end_time - start_time
    
    print(f"解析记录数: {parsed_count}")
    print(f"解析时间: {parse_time:.3f}秒")
    print(f"平均每条记录: {parse_time/parsed_count*1000:.3f}毫秒")
    
    # 性能标准：每条记录解析时间应小于1毫秒
    if parse_time/parsed_count < 0.001:
        print("✅ 数据解析性能正常")
        return True
    else:
        print("⚠️ 数据解析性能可能有问题")
        return False

def test_database_performance():
    """测试数据库操作性能"""
    print("\n=== 测试数据库操作性能 ===")
    
    db_path = os.path.join('data', 'lottery.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 测试查询性能
        start_time = time.time()
        
        # 1. 全表计数查询
        cursor.execute("SELECT COUNT(*) FROM lottery_records")
        total_count = cursor.fetchone()[0]
        
        # 2. 最新记录查询
        cursor.execute("""
            SELECT period, date, numbers, direct_prize, group3_prize, group6_prize
            FROM lottery_records 
            ORDER BY date DESC, period DESC 
            LIMIT 10
        """)
        latest_records = cursor.fetchall()
        
        # 3. 日期范围查询
        cursor.execute("""
            SELECT COUNT(*) FROM lottery_records 
            WHERE date BETWEEN '2025-07-01' AND '2025-07-15'
        """)
        range_count = cursor.fetchone()[0]
        
        end_time = time.time()
        query_time = end_time - start_time
        
        print(f"数据库记录总数: {total_count}")
        print(f"最新记录数: {len(latest_records)}")
        print(f"范围查询结果: {range_count}")
        print(f"查询总时间: {query_time:.3f}秒")
        print(f"平均每次查询: {query_time/3:.3f}秒")
        
        conn.close()
        
        # 性能标准：查询时间应小于1秒
        if query_time < 1.0:
            print("✅ 数据库操作性能正常")
            return True
        else:
            print("⚠️ 数据库操作性能可能有问题")
            return False
            
    except Exception as e:
        print(f"❌ 数据库性能测试失败: {e}")
        return False

def test_manager_performance():
    """测试数据更新管理器性能"""
    print("\n=== 测试数据更新管理器性能 ===")
    
    try:
        start_time = time.time()
        
        manager = DataUpdateManager()
        
        # 测试衍生字段计算性能
        test_records = []
        for i in range(1000):
            record = {
                'period': f'2025{i:03d}',
                'date': '2025-07-15',
                'numbers': '227',
                'trial_numbers': '137',
                'draw_machine': 1,
                'trial_machine': 1,
                'sales_amount': 113957536,
                'direct_prize': 1040,
                'group3_prize': 346,
                'group6_prize': 173
            }
            enhanced_record = manager.calculate_derived_fields(record)
            test_records.append(enhanced_record)
        
        end_time = time.time()
        process_time = end_time - start_time
        
        print(f"处理记录数: {len(test_records)}")
        print(f"处理时间: {process_time:.3f}秒")
        print(f"平均每条记录: {process_time/len(test_records)*1000:.3f}毫秒")
        
        # 验证计算结果正确性
        sample_record = test_records[0]
        if (sample_record['sum_value'] == 11 and 
            sample_record['span_value'] == 5 and
            sample_record['direct_prize'] == 1040):
            print("✅ 计算结果正确")
            result_correct = True
        else:
            print("❌ 计算结果错误")
            result_correct = False
        
        # 性能标准：每条记录处理时间应小于1毫秒
        if process_time/len(test_records) < 0.001 and result_correct:
            print("✅ 数据更新管理器性能正常")
            return True
        else:
            print("⚠️ 数据更新管理器性能可能有问题")
            return False
            
    except Exception as e:
        print(f"❌ 数据更新管理器性能测试失败: {e}")
        return False

def test_field_mapping_performance():
    """测试修复后的字段映射性能"""
    print("\n=== 测试字段映射性能 ===")
    
    try:
        # 测试原始映射 vs 修复后映射的性能差异
        test_data = "2025186 2025-07-15 2 2 7 1 3 7 1 1 113957536 0 1040 0 346 0 173"
        iterations = 10000
        
        # 测试修复后的映射
        start_time = time.time()
        for _ in range(iterations):
            parts = test_data.split()
            if len(parts) >= 17:
                direct_prize = int(parts[12]) if parts[12].isdigit() else 1040
                group3_prize = int(parts[14]) if len(parts) > 14 and parts[14].isdigit() else 346
                group6_prize = int(parts[16]) if len(parts) > 16 and parts[16].isdigit() else 173
        
        end_time = time.time()
        new_mapping_time = end_time - start_time
        
        print(f"修复后映射测试:")
        print(f"  迭代次数: {iterations}")
        print(f"  总时间: {new_mapping_time:.3f}秒")
        print(f"  平均每次: {new_mapping_time/iterations*1000:.3f}毫秒")
        print(f"  解析结果: direct={direct_prize}, group3={group3_prize}, group6={group6_prize}")
        
        # 验证结果正确性
        if direct_prize == 1040 and group3_prize == 346 and group6_prize == 173:
            print("✅ 字段映射结果正确")
            mapping_correct = True
        else:
            print("❌ 字段映射结果错误")
            mapping_correct = False
        
        # 性能标准：每次映射时间应小于0.01毫秒
        if new_mapping_time/iterations < 0.00001 and mapping_correct:
            print("✅ 字段映射性能正常")
            return True
        else:
            print("⚠️ 字段映射性能可能有问题")
            return False
            
    except Exception as e:
        print(f"❌ 字段映射性能测试失败: {e}")
        return False

def test_overall_system_performance():
    """测试整体系统性能"""
    print("\n=== 测试整体系统性能 ===")
    
    try:
        start_time = time.time()
        
        # 模拟完整的数据处理流程
        manager = DataUpdateManager()
        
        # 1. 数据解析
        test_lines = [
            "2025186 2025-07-15 2 2 7 1 3 7 1 1 113957536 0 1040 0 346 0 173",
            "2025185 2025-07-14 1 6 4 0 5 5 1 1 109890010 0 1040 0 346 0 173",
            "2025184 2025-07-13 7 9 9 6 3 3 1 1 110880482 0 1040 0 346 0 173"
        ] * 100  # 300条记录
        
        processed_records = []
        for line in test_lines:
            parts = line.split()
            if len(parts) >= 13:
                numbers = parts[2] + parts[3] + parts[4]
                trial_numbers = parts[5] + parts[6] + parts[7]
                
                record = {
                    'period': parts[0],
                    'date': parts[1],
                    'numbers': numbers,
                    'trial_numbers': trial_numbers,
                    'draw_machine': int(parts[8]) if parts[8].isdigit() else 1,
                    'trial_machine': int(parts[9]) if parts[9].isdigit() else 1,
                    'sales_amount': int(parts[10]) if parts[10].isdigit() else 0,
                    'direct_prize': int(parts[12]) if parts[12].isdigit() else 1040,
                    'group3_prize': int(parts[14]) if len(parts) > 14 and parts[14].isdigit() else 346,
                    'group6_prize': int(parts[16]) if len(parts) > 16 and parts[16].isdigit() else 173
                }
                
                # 2. 衍生字段计算
                enhanced_record = manager.calculate_derived_fields(record)
                processed_records.append(enhanced_record)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"处理记录总数: {len(processed_records)}")
        print(f"总处理时间: {total_time:.3f}秒")
        print(f"平均每条记录: {total_time/len(processed_records)*1000:.3f}毫秒")
        print(f"处理速度: {len(processed_records)/total_time:.0f} 记录/秒")
        
        # 验证处理结果
        sample_record = processed_records[0]
        if (sample_record['direct_prize'] == 1040 and 
            sample_record['group3_prize'] == 346 and 
            sample_record['group6_prize'] == 173 and
            sample_record['sum_value'] == 11):
            print("✅ 处理结果正确")
            result_correct = True
        else:
            print("❌ 处理结果错误")
            result_correct = False
        
        # 性能标准：处理速度应大于100记录/秒
        if len(processed_records)/total_time > 100 and result_correct:
            print("✅ 整体系统性能正常")
            return True
        else:
            print("⚠️ 整体系统性能可能有问题")
            return False
            
    except Exception as e:
        print(f"❌ 整体系统性能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始性能验证测试...")
    print("=" * 50)
    
    tests = [
        test_parsing_performance,
        test_database_performance,
        test_manager_performance,
        test_field_mapping_performance,
        test_overall_system_performance
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"⚠️ 性能测试警告: {test.__name__}")
        except Exception as e:
            print(f"❌ 性能测试异常: {test.__name__} - {e}")
    
    print("\n" + "=" * 50)
    print(f"性能验证结果: {passed}/{total} 通过")
    
    if passed >= total - 1:
        print("🎉 性能验证通过！修复未影响系统性能！")
        return True
    else:
        print("⚠️ 部分性能测试未通过，需要关注")
        return False

if __name__ == "__main__":
    main()
