"""
验证修复效果
"""

import sys
import os

# 添加src路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def main():
    print("=== 验证修复效果 ===")
    
    success_count = 0
    total_tests = 4
    
    # 1. 测试特征维度
    try:
        from prediction.deep_learning.data_loader import LotteryDataLoader
        data_loader = LotteryDataLoader()
        numeric_data = data_loader._convert_to_numeric(["123", "456"])
        feature_count = len(numeric_data[0])
        print(f"✓ 特征维度: {feature_count} (期望: 20)")
        if feature_count == 20:
            success_count += 1
    except Exception as e:
        print(f"✗ 特征维度测试失败: {e}")
    
    # 2. 测试模型创建
    try:
        import torch
        from prediction.deep_learning.cnn_lstm_attention import CNNLSTMAttentionPredictor
        model = CNNLSTMAttentionPredictor(input_dim=20, num_classes=100)
        print(f"✓ 模型创建成功")
        success_count += 1
    except Exception as e:
        print(f"✗ 模型创建失败: {e}")
    
    # 3. 测试前向传播
    try:
        test_input = torch.randn(1, 5, 20)
        with torch.no_grad():
            output = model(test_input)
        print(f"✓ 前向传播: {test_input.shape} -> {output.shape}")
        success_count += 1
    except Exception as e:
        print(f"✗ 前向传播失败: {e}")
    
    # 4. 测试特征工程
    try:
        from prediction.feature_engineering import FeatureEngineeringPipeline
        pipeline = FeatureEngineeringPipeline()
        features = pipeline.extract_all_features(['123', '456'])
        print(f"✓ 特征工程: {len(features)} 个特征")
        success_count += 1
    except Exception as e:
        print(f"✗ 特征工程失败: {e}")
    
    print(f"\n结果: {success_count}/{total_tests} 测试通过")
    
    if success_count == total_tests:
        print("🎉 所有修复验证成功!")
        return True
    else:
        print("⚠️ 部分测试仍有问题")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
