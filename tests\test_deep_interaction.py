"""
深度交互功能综合测试套件
实施综合测试套件，单元测试覆盖所有核心功能、集成测试验证系统协作、性能测试
"""

import unittest
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime
import tempfile
import os
import sys
import json
import time

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

# 导入测试模块
try:
    from model_library.features.feature_ranking import MultiAlgorithmFeatureRanking
    from model_library.features.feature_selector import InteractiveFeatureSelector
    from model_library.data.adaptive_quality_engine import AdaptiveDataQualityEngine
    from model_library.data.realtime_monitor import RealTimeDataQualityMonitor
    from model_library.memory.training_record import TrainingRecord, Knowledge
    from model_library.memory.hierarchical_memory import HierarchicalTrainingMemoryDB
    from model_library.training.websocket_monitor import WebSocketTrainingMonitor
    from model_library.training.bayesian_recommender import BayesianHyperparameterRecommender
    from model_library.optimization.ab_testing import AdaptiveABTestingFramework, AllocationStrategy
    from model_library.optimization.experiment_config import ExperimentConfigManager, ExperimentConfig
    from model_library.meta_learning.meta_optimizer import MetaLearner, Task
    from model_library.meta_learning.task_encoder import TaskEncoder
    from model_library.visualization.multi_dimensional import MultiDimensionalTrainingVisualizer
    from optimization.performance_optimizer import PerformanceOptimizer
    IMPORTS_SUCCESSFUL = True
except ImportError as e:
    print(f"导入失败: {e}")
    IMPORTS_SUCCESSFUL = False


class TestFeatureEngineering(unittest.TestCase):
    """特征工程测试"""
    
    def setUp(self):
        if not IMPORTS_SUCCESSFUL:
            self.skipTest("模块导入失败")
        
        self.feature_ranking = MultiAlgorithmFeatureRanking()
        self.feature_selector = InteractiveFeatureSelector()
        
        # 生成测试数据
        np.random.seed(42)
        self.X = np.random.randn(100, 10)
        self.y = np.random.randint(0, 1000, 100)
        self.feature_names = [f"feature_{i}" for i in range(10)]
    
    def test_feature_ranking(self):
        """测试特征重要性排序"""
        rankings = self.feature_ranking.calculate_comprehensive_ranking(
            self.X, self.y, self.feature_names, "test_model"
        )
        
        self.assertIsInstance(rankings, dict)
        self.assertEqual(len(rankings), len(self.feature_names))
        self.assertTrue(all(isinstance(v, float) for v in rankings.values()))
    
    def test_feature_selector(self):
        """测试特征选择器"""
        categories = self.feature_selector.get_available_feature_categories()
        self.assertIsInstance(categories, dict)
        self.assertGreater(len(categories), 0)
        
        # 测试特征选择
        selected_features = self.feature_selector.select_features_by_category(
            "基础统计特征", {"enable_mean": True, "enable_std": True}
        )
        self.assertIsInstance(selected_features, list)


class TestDataManagement(unittest.TestCase):
    """数据管理测试"""
    
    def setUp(self):
        if not IMPORTS_SUCCESSFUL:
            self.skipTest("模块导入失败")
        
        self.quality_engine = AdaptiveDataQualityEngine()
        self.monitor = RealTimeDataQualityMonitor()
        
        # 生成测试数据
        self.test_data = pd.DataFrame({
            'period': range(100),
            'value1': np.random.randn(100),
            'value2': np.random.randint(0, 10, 100),
            'timestamp': pd.date_range('2024-01-01', periods=100)
        })
    
    def test_quality_assessment(self):
        """测试数据质量评估"""
        quality_score = self.quality_engine.calculate_adaptive_quality_score(
            "test_model", (0, 100), self.test_data
        )
        
        self.assertIsNotNone(quality_score)
        self.assertGreaterEqual(quality_score.overall_score, 0)
        self.assertLessEqual(quality_score.overall_score, 1)
    
    def test_realtime_monitoring(self):
        """测试实时监控"""
        # 启动监控
        monitor_task = asyncio.create_task(
            self.monitor.start_monitoring("test_model", (0, 100))
        )
        
        # 模拟数据更新
        self.monitor.update_data_quality("test_model", {
            "completeness": 0.95,
            "consistency": 0.90,
            "accuracy": 0.85
        })
        
        # 获取监控状态
        status = self.monitor.get_monitoring_status("test_model")
        self.assertIsInstance(status, dict)
        
        # 停止监控
        monitor_task.cancel()


class TestTrainingMemory(unittest.TestCase):
    """训练记忆测试"""
    
    def setUp(self):
        if not IMPORTS_SUCCESSFUL:
            self.skipTest("模块导入失败")
        
        self.temp_dir = tempfile.mkdtemp()
        self.memory_db = HierarchicalTrainingMemoryDB(
            redis_url=None,  # 不使用Redis进行测试
            sqlite_path=os.path.join(self.temp_dir, "test.db"),
            postgres_url=None  # 不使用PostgreSQL进行测试
        )
    
    def test_training_record(self):
        """测试训练记录"""
        record = TrainingRecord(
            record_id="test_record",
            model_id="test_model",
            hyperparameters={"lr": 0.001, "batch_size": 32},
            performance_metrics={"accuracy": 0.85, "loss": 0.25},
            training_duration=120.5
        )
        
        # 测试序列化
        record_dict = record.to_dict()
        self.assertIsInstance(record_dict, dict)
        
        # 测试反序列化
        restored_record = TrainingRecord.from_dict(record_dict)
        self.assertEqual(restored_record.record_id, record.record_id)
    
    def test_memory_storage(self):
        """测试记忆存储"""
        record = TrainingRecord(
            record_id="test_record",
            model_id="test_model",
            hyperparameters={"lr": 0.001},
            performance_metrics={"accuracy": 0.85}
        )
        
        # 存储记录
        success = self.memory_db.store_training_record(record)
        self.assertTrue(success)
        
        # 检索记录
        retrieved_records = self.memory_db.retrieve_similar_records(
            "test_model", {"lr": 0.001}, top_k=5
        )
        self.assertIsInstance(retrieved_records, list)
    
    def tearDown(self):
        # 清理临时文件
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)


class TestTrainingMonitoring(unittest.TestCase):
    """训练监控测试"""
    
    def setUp(self):
        if not IMPORTS_SUCCESSFUL:
            self.skipTest("模块导入失败")
        
        self.ws_monitor = WebSocketTrainingMonitor()
        self.bayesian_recommender = BayesianHyperparameterRecommender()
    
    def test_websocket_monitor(self):
        """测试WebSocket监控"""
        sessions = self.ws_monitor.get_active_sessions()
        self.assertIsInstance(sessions, dict)
    
    def test_bayesian_recommender(self):
        """测试贝叶斯推荐"""
        # 添加历史数据
        self.bayesian_recommender.update_optimization_history(
            "test_model",
            {"learning_rate": 0.001, "batch_size": 64},
            {"accuracy": 0.85, "loss": 0.25}
        )
        
        # 获取推荐
        recommendation = self.bayesian_recommender.recommend_next_hyperparameters("test_model")
        
        self.assertIsInstance(recommendation, dict)
        self.assertIn("recommended_parameters", recommendation)
        self.assertIn("confidence", recommendation)


class TestABTesting(unittest.TestCase):
    """A/B测试测试"""
    
    def setUp(self):
        if not IMPORTS_SUCCESSFUL:
            self.skipTest("模块导入失败")
        
        self.ab_framework = AdaptiveABTestingFramework()
        self.config_manager = ExperimentConfigManager()
    
    def test_experiment_creation(self):
        """测试实验创建"""
        arms = [
            {
                "arm_id": "control",
                "name": "控制组",
                "description": "基线配置",
                "configuration": {"lr": 0.001}
            },
            {
                "arm_id": "treatment",
                "name": "实验组",
                "description": "新配置",
                "configuration": {"lr": 0.005}
            }
        ]
        
        success = self.ab_framework.create_experiment(
            "test_experiment",
            "测试实验",
            "测试A/B框架",
            arms,
            AllocationStrategy.THOMPSON_SAMPLING
        )
        
        self.assertTrue(success)
    
    def test_experiment_config(self):
        """测试实验配置"""
        config = ExperimentConfig(
            name="测试配置",
            description="测试实验配置管理"
        )
        
        errors = config.validate()
        # 应该有错误，因为没有设置分支
        self.assertGreater(len(errors), 0)


class TestMetaLearning(unittest.TestCase):
    """元学习测试"""
    
    def setUp(self):
        if not IMPORTS_SUCCESSFUL:
            self.skipTest("模块导入失败")
        
        self.meta_learner = MetaLearner()
        self.task_encoder = TaskEncoder(encoding_dim=32)
    
    def test_task_creation(self):
        """测试任务创建"""
        task = Task(
            task_id="test_task",
            name="测试任务",
            description="测试元学习",
            task_type="time_series",
            data_size=1000,
            feature_count=10,
            target_type="categorical",
            complexity_score=0.7
        )
        
        self.meta_learner.add_task(task)
        self.assertIn("test_task", self.meta_learner.tasks)
    
    def test_task_encoding(self):
        """测试任务编码"""
        task_data = {
            "task_id": "test_task",
            "task_type": "time_series",
            "data_size": 1000,
            "feature_count": 10,
            "complexity_score": 0.7
        }
        
        task_vector = self.task_encoder.encode_task(task_data)
        
        self.assertIsNotNone(task_vector)
        self.assertEqual(task_vector.task_id, "test_task")
        self.assertIsInstance(task_vector.feature_vector, np.ndarray)


class TestVisualization(unittest.TestCase):
    """可视化测试"""
    
    def setUp(self):
        if not IMPORTS_SUCCESSFUL:
            self.skipTest("模块导入失败")
        
        self.visualizer = MultiDimensionalTrainingVisualizer()
    
    def test_3d_parameter_space(self):
        """测试3D参数空间"""
        # 生成测试数据
        parameter_data = []
        for i in range(20):
            parameter_data.append({
                "learning_rate": np.random.uniform(0.001, 0.01),
                "batch_size": np.random.choice([32, 64, 128]),
                "epochs": np.random.randint(50, 200),
                "accuracy": np.random.uniform(0.7, 0.9)
            })
        
        fig = self.visualizer.create_3d_parameter_space(
            parameter_data, "learning_rate", "batch_size", "epochs", "accuracy"
        )
        
        self.assertIsNotNone(fig)
    
    def test_convergence_analysis(self):
        """测试收敛分析"""
        # 生成训练历史
        training_history = []
        for epoch in range(50):
            training_history.append({
                "epoch": epoch,
                "loss": 1.0 * np.exp(-epoch * 0.05) + 0.1 * np.random.random(),
                "accuracy": 0.5 + 0.4 * (1 - np.exp(-epoch * 0.03))
            })
        
        fig = self.visualizer.create_convergence_analysis(
            training_history, ["loss", "accuracy"]
        )
        
        self.assertIsNotNone(fig)


class TestPerformanceOptimization(unittest.TestCase):
    """性能优化测试"""
    
    def setUp(self):
        if not IMPORTS_SUCCESSFUL:
            self.skipTest("模块导入失败")
        
        self.optimizer = PerformanceOptimizer()
    
    def test_cache_functionality(self):
        """测试缓存功能"""
        @self.optimizer.cache_result(expire=60)
        def test_function(x):
            time.sleep(0.01)  # 模拟计算时间
            return x * 2
        
        # 第一次调用
        start_time = time.time()
        result1 = test_function(5)
        first_call_time = time.time() - start_time
        
        # 第二次调用（应该从缓存获取）
        start_time = time.time()
        result2 = test_function(5)
        second_call_time = time.time() - start_time
        
        self.assertEqual(result1, result2)
        self.assertLess(second_call_time, first_call_time)
    
    def test_performance_monitoring(self):
        """测试性能监控"""
        @self.optimizer.monitor_performance
        def monitored_function(n):
            return sum(range(n))
        
        result = monitored_function(1000)
        self.assertEqual(result, sum(range(1000)))
        
        # 检查是否记录了性能指标
        self.assertGreater(len(self.optimizer.performance_history), 0)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        if not IMPORTS_SUCCESSFUL:
            self.skipTest("模块导入失败")
    
    def test_end_to_end_workflow(self):
        """测试端到端工作流"""
        # 1. 特征工程
        feature_ranking = MultiAlgorithmFeatureRanking()
        X = np.random.randn(100, 5)
        y = np.random.randint(0, 1000, 100)
        feature_names = [f"feature_{i}" for i in range(5)]
        
        rankings = feature_ranking.calculate_comprehensive_ranking(
            X, y, feature_names, "integration_test"
        )
        self.assertIsInstance(rankings, dict)
        
        # 2. 数据质量评估
        quality_engine = AdaptiveDataQualityEngine()
        test_data = pd.DataFrame({
            'col1': np.random.randn(100),
            'col2': np.random.randint(0, 10, 100)
        })
        
        quality_score = quality_engine.calculate_adaptive_quality_score(
            "integration_test", (0, 100), test_data
        )
        self.assertIsNotNone(quality_score)
        
        # 3. 超参数推荐
        recommender = BayesianHyperparameterRecommender()
        recommendation = recommender.recommend_next_hyperparameters("integration_test")
        self.assertIsInstance(recommendation, dict)
        
        # 4. 元学习
        meta_learner = MetaLearner()
        task = Task(
            task_id="integration_task",
            name="集成测试任务",
            description="端到端测试",
            task_type="time_series",
            data_size=100,
            feature_count=5,
            target_type="categorical",
            complexity_score=0.5
        )
        
        meta_learner.add_task(task)
        insights = meta_learner.get_meta_learning_insights()
        self.assertIsInstance(insights, dict)


def run_performance_tests():
    """运行性能测试"""
    print("🚀 运行性能测试...")
    
    # 测试响应时间
    start_time = time.time()
    
    # 模拟API调用
    feature_ranking = MultiAlgorithmFeatureRanking()
    X = np.random.randn(1000, 20)
    y = np.random.randint(0, 1000, 1000)
    feature_names = [f"feature_{i}" for i in range(20)]
    
    rankings = feature_ranking.calculate_comprehensive_ranking(
        X, y, feature_names, "performance_test"
    )
    
    response_time = time.time() - start_time
    
    print(f"📊 特征排序响应时间: {response_time:.3f}s")
    
    # 验证性能目标
    assert response_time < 2.0, f"响应时间超标: {response_time:.3f}s > 2.0s"
    
    print("✅ 性能测试通过！")


def main():
    """主测试函数"""
    print("🧪 开始深度交互功能综合测试...")
    
    if not IMPORTS_SUCCESSFUL:
        print("❌ 模块导入失败，跳过测试")
        return
    
    # 运行单元测试
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_classes = [
        TestFeatureEngineering,
        TestDataManagement,
        TestTrainingMemory,
        TestTrainingMonitoring,
        TestABTesting,
        TestMetaLearning,
        TestVisualization,
        TestPerformanceOptimization,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 运行性能测试
    try:
        run_performance_tests()
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
    
    # 输出测试结果
    print(f"\n📊 测试结果:")
    print(f"✅ 成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ 失败: {len(result.failures)}")
    print(f"🚫 错误: {len(result.errors)}")
    print(f"📈 测试覆盖率: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.wasSuccessful():
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
