#!/usr/bin/env python3
"""
初始化模型库数据库结构

运行此脚本来创建模型库所需的数据库表
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.model_library.database_manager import DatabaseManager


def main():
    """主函数"""
    print("🔧 初始化模型库数据库结构...")
    
    try:
        # 创建数据库管理器
        db_manager = DatabaseManager()
        
        # 初始化模型库表
        db_manager.init_model_library_tables()
        
        print("✅ 模型库数据库表创建成功！")
        print("\n📋 已创建的表：")
        print("  - model_library: 模型基本信息")
        print("  - model_states: 模型状态信息")
        print("  - model_predictions: 预测记录")
        print("  - model_performance: 性能统计")
        print("\n🎯 数据库初始化完成，模型库已准备就绪！")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
