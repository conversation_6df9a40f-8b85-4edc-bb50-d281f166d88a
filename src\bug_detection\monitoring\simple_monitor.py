#!/usr/bin/env python3
"""
简化的API性能监控实现
避免复杂的中间件问题，直接在API端点中记录性能数据
"""

import logging
import time
from functools import wraps
from typing import Optional

logger = logging.getLogger(__name__)

class SimpleAPIMonitor:
    """简化的API性能监控器"""
    
    def __init__(self, database_manager=None):
        self.db_manager = database_manager
        self.enabled = database_manager is not None
        
    def record_request(self, endpoint: str, response_time: float, status_code: int):
        """记录API请求性能数据"""
        if not self.enabled or not self.db_manager:
            return

        try:
            self.db_manager.save_performance_metric(endpoint, response_time, status_code)
            logger.debug(f"Recorded performance: {endpoint} - {response_time:.3f}s - {status_code}")
        except Exception as e:
            logger.error(f"Failed to record performance metric: {e}")

# 全局监控器实例
_global_monitor: Optional[SimpleAPIMonitor] = None

def init_simple_monitor(database_manager):
    """初始化全局监控器"""
    global _global_monitor
    _global_monitor = SimpleAPIMonitor(database_manager)
    logger.info("Simple API monitor initialized")
    return _global_monitor

def get_monitor() -> Optional[SimpleAPIMonitor]:
    """获取全局监控器实例"""
    return _global_monitor

def monitor_endpoint(endpoint_name: str = None):
    """装饰器：监控API端点性能"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            if not _global_monitor:
                return await func(*args, **kwargs)
                
            # 确定端点名称
            endpoint = endpoint_name or getattr(func, '__name__', 'unknown')
            
            # 记录开始时间
            start_time = time.time()
            status_code = 200
            
            try:
                # 执行原函数
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                status_code = 500
                raise
            finally:
                # 记录性能数据
                response_time = time.time() - start_time
                _global_monitor.record_request(endpoint, response_time, status_code)
                
        return wrapper
    return decorator

def record_manual_request(endpoint: str, response_time: float, status_code: int = 200):
    """手动记录请求性能数据"""
    if _global_monitor:
        _global_monitor.record_request(endpoint, response_time, status_code)

# 便捷函数
def start_timing():
    """开始计时"""
    return time.time()

def end_timing_and_record(start_time: float, endpoint: str, status_code: int = 200):
    """结束计时并记录"""
    response_time = time.time() - start_time
    record_manual_request(endpoint, response_time, status_code)
    return response_time
