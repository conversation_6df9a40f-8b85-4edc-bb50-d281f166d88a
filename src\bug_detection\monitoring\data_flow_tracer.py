"""
数据流追踪系统 - 用于诊断Bug检测系统的数据流问题
"""

import json
import time
import uuid
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum

# 配置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class FlowStage(Enum):
    """数据流阶段"""
    JAVASCRIPT_CAPTURE = "javascript_capture"
    WEBSOCKET_SEND = "websocket_send"
    WEBSOCKET_RECEIVE = "websocket_receive"
    EVENT_BUS_PUBLISH = "event_bus_publish"
    DATABASE_STORE = "database_store"
    DASHBOARD_QUERY = "dashboard_query"
    DASHBOARD_DISPLAY = "dashboard_display"

class FlowStatus(Enum):
    """流程状态"""
    SUCCESS = "success"
    FAILED = "failed"
    PENDING = "pending"
    TIMEOUT = "timeout"

@dataclass
class FlowTrace:
    """数据流追踪记录"""
    trace_id: str
    stage: FlowStage
    status: FlowStatus
    timestamp: float
    data: Dict[str, Any]
    error_message: Optional[str] = None
    duration_ms: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None

class DataFlowTracer:
    """数据流追踪器"""
    
    def __init__(self):
        self.traces: Dict[str, List[FlowTrace]] = {}
        self.active_traces: Dict[str, float] = {}  # trace_id -> start_time
        self.max_traces = 1000  # 最大保存的追踪记录数
        
    def start_trace(self, error_data: Dict[str, Any]) -> str:
        """开始追踪一个错误事件"""
        trace_id = str(uuid.uuid4())
        current_time = time.time()
        
        # 记录开始追踪
        self.active_traces[trace_id] = current_time
        self.traces[trace_id] = []
        
        # 添加初始追踪记录
        initial_trace = FlowTrace(
            trace_id=trace_id,
            stage=FlowStage.JAVASCRIPT_CAPTURE,
            status=FlowStatus.SUCCESS,
            timestamp=current_time,
            data=error_data,
            metadata={
                "user_agent": error_data.get("user_agent"),
                "url": error_data.get("url"),
                "session_id": error_data.get("session_id")
            }
        )
        
        self.traces[trace_id].append(initial_trace)
        
        logger.info(f"🔍 开始追踪错误事件: {trace_id}")
        logger.debug(f"📊 错误数据: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
        
        return trace_id
    
    def add_trace(self, trace_id: str, stage: FlowStage, status: FlowStatus, 
                  data: Dict[str, Any], error_message: Optional[str] = None,
                  metadata: Optional[Dict[str, Any]] = None):
        """添加追踪记录"""
        if trace_id not in self.traces:
            logger.warning(f"⚠️ 追踪ID不存在: {trace_id}")
            return
        
        current_time = time.time()
        start_time = self.active_traces.get(trace_id, current_time)
        duration_ms = (current_time - start_time) * 1000
        
        trace = FlowTrace(
            trace_id=trace_id,
            stage=stage,
            status=status,
            timestamp=current_time,
            data=data,
            error_message=error_message,
            duration_ms=duration_ms,
            metadata=metadata
        )
        
        self.traces[trace_id].append(trace)
        
        # 记录日志
        status_emoji = "✅" if status == FlowStatus.SUCCESS else "❌" if status == FlowStatus.FAILED else "⏳"
        logger.info(f"{status_emoji} [{trace_id[:8]}] {stage.value}: {status.value}")
        
        if error_message:
            logger.error(f"🐛 [{trace_id[:8]}] 错误: {error_message}")
        
        if status == FlowStatus.SUCCESS:
            logger.debug(f"📊 [{trace_id[:8]}] 数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
    
    def complete_trace(self, trace_id: str, final_status: FlowStatus = FlowStatus.SUCCESS):
        """完成追踪"""
        if trace_id in self.active_traces:
            total_duration = (time.time() - self.active_traces[trace_id]) * 1000
            del self.active_traces[trace_id]
            
            logger.info(f"🏁 追踪完成: {trace_id[:8]} (总耗时: {total_duration:.2f}ms)")
            
            # 清理旧的追踪记录
            self._cleanup_old_traces()
    
    def get_trace_summary(self, trace_id: str) -> Dict[str, Any]:
        """获取追踪摘要"""
        if trace_id not in self.traces:
            return {"error": "追踪ID不存在"}
        
        traces = self.traces[trace_id]
        if not traces:
            return {"error": "无追踪记录"}
        
        # 计算统计信息
        total_duration = 0
        success_count = 0
        failed_count = 0
        stages_completed = []
        
        for trace in traces:
            if trace.duration_ms:
                total_duration += trace.duration_ms
            
            if trace.status == FlowStatus.SUCCESS:
                success_count += 1
            elif trace.status == FlowStatus.FAILED:
                failed_count += 1
            
            stages_completed.append(trace.stage.value)
        
        return {
            "trace_id": trace_id,
            "total_stages": len(traces),
            "success_count": success_count,
            "failed_count": failed_count,
            "total_duration_ms": total_duration,
            "stages_completed": stages_completed,
            "traces": [asdict(trace) for trace in traces]
        }
    
    def get_all_traces_summary(self) -> Dict[str, Any]:
        """获取所有追踪的摘要"""
        active_count = len(self.active_traces)
        completed_count = len(self.traces) - active_count
        
        # 统计各阶段的成功率
        stage_stats = {}
        for trace_id, traces in self.traces.items():
            for trace in traces:
                stage = trace.stage.value
                if stage not in stage_stats:
                    stage_stats[stage] = {"success": 0, "failed": 0, "total": 0}
                
                stage_stats[stage]["total"] += 1
                if trace.status == FlowStatus.SUCCESS:
                    stage_stats[stage]["success"] += 1
                elif trace.status == FlowStatus.FAILED:
                    stage_stats[stage]["failed"] += 1
        
        # 计算成功率
        for stage, stats in stage_stats.items():
            if stats["total"] > 0:
                stats["success_rate"] = stats["success"] / stats["total"] * 100
            else:
                stats["success_rate"] = 0
        
        return {
            "active_traces": active_count,
            "completed_traces": completed_count,
            "total_traces": len(self.traces),
            "stage_statistics": stage_stats,
            "recent_traces": list(self.traces.keys())[-10:]  # 最近10个追踪ID
        }
    
    def _cleanup_old_traces(self):
        """清理旧的追踪记录"""
        if len(self.traces) > self.max_traces:
            # 保留最新的追踪记录
            sorted_traces = sorted(
                self.traces.items(),
                key=lambda x: max(trace.timestamp for trace in x[1]) if x[1] else 0,
                reverse=True
            )
            
            # 保留最新的记录
            self.traces = dict(sorted_traces[:self.max_traces])
            logger.info(f"🧹 清理旧追踪记录，保留 {len(self.traces)} 条")

# 全局追踪器实例
data_flow_tracer = DataFlowTracer()

def start_error_trace(error_data: Dict[str, Any]) -> str:
    """开始追踪错误事件的便捷函数"""
    return data_flow_tracer.start_trace(error_data)

def add_trace_point(trace_id: str, stage: FlowStage, status: FlowStatus, 
                   data: Dict[str, Any], error_message: Optional[str] = None,
                   metadata: Optional[Dict[str, Any]] = None):
    """添加追踪点的便捷函数"""
    data_flow_tracer.add_trace(trace_id, stage, status, data, error_message, metadata)

def complete_error_trace(trace_id: str, final_status: FlowStatus = FlowStatus.SUCCESS):
    """完成错误追踪的便捷函数"""
    data_flow_tracer.complete_trace(trace_id, final_status)

def get_trace_report(trace_id: Optional[str] = None) -> Dict[str, Any]:
    """获取追踪报告的便捷函数"""
    if trace_id:
        return data_flow_tracer.get_trace_summary(trace_id)
    else:
        return data_flow_tracer.get_all_traces_summary()
