#!/usr/bin/env python3
"""
综合功能测试脚本
测试所有服务的完整功能
"""

import requests
import time
import json
from datetime import datetime

def test_all_services():
    """综合测试所有服务"""
    print("🚀 开始综合功能测试...")
    print("=" * 60)
    
    results = {
        "streamlit": False,
        "api": False,
        "scheduler": False,
        "prediction": False,
        "data_sync": False
    }
    
    # 1. 测试Streamlit服务
    print("\n1️⃣ 测试Streamlit服务")
    try:
        response = requests.get("http://127.0.0.1:8501", timeout=10)
        if response.status_code == 200:
            print("✅ Streamlit服务正常")
            print(f"   📊 页面大小: {len(response.text)} 字符")
            results["streamlit"] = True
            
            # 检查健康状态
            try:
                health = requests.get("http://127.0.0.1:8501/healthz", timeout=5)
                if health.status_code == 200:
                    print("✅ Streamlit健康检查通过")
            except:
                print("ℹ️ Streamlit健康检查端点不可用")
        else:
            print(f"❌ Streamlit服务异常: {response.status_code}")
    except Exception as e:
        print(f"❌ Streamlit连接失败: {e}")
    
    # 2. 测试API服务
    print("\n2️⃣ 测试API服务")
    try:
        response = requests.get("http://127.0.0.1:8888/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print("✅ API服务正常")
            print(f"   📊 数据库记录: {health_data.get('database_records')} 条")
            print(f"   📅 数据范围: {health_data.get('date_range')}")
            print(f"   🕐 响应时间: {health_data.get('timestamp')}")
            results["api"] = True
            
            # 检查数据同步状态
            if health_data.get('database_records') == 8344:
                print("✅ 数据已同步到最新")
                results["data_sync"] = True
            else:
                print(f"⚠️ 数据可能未完全同步: {health_data.get('database_records')}")
        else:
            print(f"❌ API服务异常: {response.status_code}")
    except Exception as e:
        print(f"❌ API连接失败: {e}")
    
    # 3. 测试预测功能
    print("\n3️⃣ 测试预测功能")
    try:
        pred_url = "http://127.0.0.1:8888/api/v1/prediction/intelligent-fusion/predict"
        params = {"prediction_mode": "智能融合", "auto_train": "true"}
        
        print("   🔄 发送预测请求...")
        response = requests.get(pred_url, params=params, timeout=20)
        
        if response.status_code == 200:
            pred_data = response.json()
            print("✅ 预测功能正常")
            
            prediction = pred_data.get('prediction', {})
            print(f"   🎯 预测结果: {prediction.get('numbers', 'N/A')}")
            print(f"   📊 置信度: {prediction.get('confidence', 'N/A')}")
            print(f"   📈 数据计数: {pred_data.get('data_count', 'N/A')}")
            
            # 检查预测结果
            if prediction.get('numbers') == '056':
                print("⚠️ 预测结果仍为固定值，建议重训练模型")
            else:
                print("✅ 预测结果已变化，修复生效")
                results["prediction"] = True
            
            # 显示候选结果
            candidates = prediction.get('candidates', [])[:5]  # 前5个候选
            if candidates:
                print("   🏆 Top 5 候选:")
                for i, candidate in enumerate(candidates, 1):
                    print(f"      {i}. {candidate.get('numbers', 'N/A')} (置信度: {candidate.get('confidence', 0):.3f})")
        else:
            print(f"❌ 预测功能异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 预测测试失败: {e}")
    
    # 4. 测试调度器状态
    print("\n4️⃣ 测试调度器状态")
    try:
        # 检查调度器进程是否运行
        import subprocess
        result = subprocess.run(
            ["tasklist", "/FI", "IMAGENAME eq python.exe", "/FO", "CSV"],
            capture_output=True, text=True, timeout=10
        )
        
        if "python.exe" in result.stdout:
            print("✅ Python进程正在运行")
            
            # 检查调度器日志文件
            import os
            log_file = f"data/logs/scheduler_{datetime.now().strftime('%Y%m%d')}.log"
            if os.path.exists(log_file):
                print(f"✅ 调度器日志文件存在: {log_file}")
                results["scheduler"] = True
            else:
                print("⚠️ 调度器日志文件不存在")
        else:
            print("⚠️ 未检测到Python进程")
    except Exception as e:
        print(f"ℹ️ 调度器状态检查失败: {e}")
    
    # 5. 生成测试报告
    print("\n" + "=" * 60)
    print("📊 测试报告")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"📈 总体通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    print("\n📋 详细结果:")
    
    status_map = {
        "streamlit": "Streamlit界面服务",
        "api": "API后端服务", 
        "scheduler": "定时任务调度器",
        "prediction": "预测功能",
        "data_sync": "数据同步状态"
    }
    
    for key, status in results.items():
        icon = "✅" if status else "❌"
        print(f"   {icon} {status_map[key]}: {'通过' if status else '失败'}")
    
    # 6. 提供建议
    print("\n💡 建议和下一步:")
    if results["streamlit"] and results["api"]:
        print("✅ 基础服务运行正常")
        print("🌐 可以在浏览器中访问: http://127.0.0.1:8501")
        
        if not results["prediction"]:
            print("🔧 预测结果固定，建议:")
            print("   1. 访问智能融合优化页面")
            print("   2. 点击'重新训练'按钮")
            print("   3. 等待训练完成后重新测试")
        
        if results["data_sync"]:
            print("✅ 数据已同步，系统可正常使用")
        else:
            print("⚠️ 数据可能需要更新")
    else:
        print("❌ 基础服务有问题，需要检查:")
        if not results["streamlit"]:
            print("   - 重启Streamlit服务")
        if not results["api"]:
            print("   - 重启API服务")
    
    print(f"\n🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    return results

if __name__ == "__main__":
    test_all_services()
