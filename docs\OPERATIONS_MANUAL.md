# 福彩3D预测系统 - 运维监控手册

## 目录
1. [系统监控](#系统监控)
2. [性能监控](#性能监控)
3. [故障诊断](#故障诊断)
4. [维护操作](#维护操作)
5. [备份恢复](#备份恢复)
6. [安全管理](#安全管理)

## 系统监控

### 健康检查

#### 基本健康检查
```bash
# 检查API服务状态
curl http://localhost:8000/api/v1/health/

# 检查详细健康状态
curl http://localhost:8000/api/v1/health/detailed

# 检查特定组件
curl http://localhost:8000/api/v1/health/components/database
curl http://localhost:8000/api/v1/health/components/websocket
```

#### 健康检查指标
- **API响应时间**: < 2秒
- **数据库连接**: 正常
- **WebSocket状态**: 活跃
- **系统资源**: 内存 < 80%, CPU < 80%, 磁盘 < 90%

### 服务状态监控

#### 进程监控
```bash
# 检查API进程
ps aux | grep "production_main.py"

# 检查Streamlit进程
ps aux | grep "streamlit"

# 检查端口占用
netstat -tlnp | grep :8000
netstat -tlnp | grep :8501
```

#### 日志监控
```bash
# 查看API日志
tail -f logs/api.log

# 查看系统日志
tail -f logs/system.log

# 查看错误日志
tail -f logs/error.log
```

### 自动化监控脚本

#### 系统状态检查脚本
```bash
#!/bin/bash
# monitor_system.sh

echo "=== 系统监控报告 $(date) ==="

# 检查服务状态
echo "1. 服务状态检查"
if curl -s http://localhost:8000/api/v1/health/ > /dev/null; then
    echo "✅ API服务正常"
else
    echo "❌ API服务异常"
fi

if curl -s http://localhost:8501 > /dev/null; then
    echo "✅ Streamlit服务正常"
else
    echo "❌ Streamlit服务异常"
fi

# 检查系统资源
echo "2. 系统资源检查"
echo "内存使用率: $(free | grep Mem | awk '{printf("%.1f%%", $3/$2 * 100.0)}')"
echo "CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')"
echo "磁盘使用率: $(df -h . | tail -1 | awk '{print $5}')"

# 检查数据库
echo "3. 数据库检查"
if [ -f "lottery_data.db" ]; then
    echo "✅ 数据库文件存在"
    echo "数据库大小: $(du -h lottery_data.db | cut -f1)"
else
    echo "❌ 数据库文件不存在"
fi

echo "=== 监控报告结束 ==="
```

## 性能监控

### 性能指标监控

#### API性能监控
```bash
# 测试API响应时间
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8000/api/v1/health/

# curl-format.txt 内容:
#     time_namelookup:  %{time_namelookup}\n
#        time_connect:  %{time_connect}\n
#     time_appconnect:  %{time_appconnect}\n
#    time_pretransfer:  %{time_pretransfer}\n
#       time_redirect:  %{time_redirect}\n
#  time_starttransfer:  %{time_starttransfer}\n
#                     ----------\n
#          time_total:  %{time_total}\n
```

#### 数据库性能监控
```python
# 数据库性能检查脚本
import sqlite3
import time

def check_db_performance():
    start_time = time.time()
    
    conn = sqlite3.connect('lottery_data.db')
    cursor = conn.cursor()
    
    # 测试查询性能
    cursor.execute("SELECT COUNT(*) FROM lottery_records")
    count = cursor.fetchone()[0]
    
    query_time = (time.time() - start_time) * 1000
    
    print(f"数据库记录数: {count}")
    print(f"查询时间: {query_time:.2f}ms")
    
    conn.close()
    
    return query_time < 1000  # 1秒阈值

if __name__ == "__main__":
    if check_db_performance():
        print("✅ 数据库性能正常")
    else:
        print("❌ 数据库性能异常")
```

### 性能监控仪表板

#### 访问性能仪表板
```bash
# 获取性能仪表板数据
curl http://localhost:8000/api/v1/health/performance/dashboard

# 获取性能指标
curl http://localhost:8000/api/v1/health/performance/metrics

# 获取性能告警
curl http://localhost:8000/api/v1/health/performance/alerts
```

#### 性能阈值配置
- **API响应时间**: 2000ms
- **数据库查询时间**: 1000ms
- **WebSocket延迟**: 100ms
- **内存使用率**: 80%
- **CPU使用率**: 80%
- **磁盘使用率**: 90%

## 故障诊断

### 常见故障及解决方案

#### 1. API服务无法启动
**症状**: 无法访问 http://localhost:8000
**诊断步骤**:
```bash
# 检查端口占用
netstat -tlnp | grep :8000

# 检查进程状态
ps aux | grep production_main

# 查看错误日志
tail -f logs/api.log
```
**解决方案**:
- 杀死占用端口的进程
- 检查配置文件
- 重启API服务

#### 2. 数据库连接失败
**症状**: 健康检查显示数据库异常
**诊断步骤**:
```bash
# 检查数据库文件
ls -la lottery_data.db

# 检查文件权限
stat lottery_data.db

# 测试数据库连接
sqlite3 lottery_data.db ".tables"
```
**解决方案**:
- 修复文件权限
- 重建数据库索引
- 恢复数据库备份

#### 3. 内存使用过高
**症状**: 系统响应缓慢，内存使用率 > 90%
**诊断步骤**:
```bash
# 查看内存使用
free -h

# 查看进程内存使用
ps aux --sort=-%mem | head -10

# 查看系统负载
top -o %MEM
```
**解决方案**:
- 重启服务释放内存
- 优化代码减少内存使用
- 增加系统内存

#### 4. 数据更新失败
**症状**: 数据无法自动更新
**诊断步骤**:
```bash
# 检查网络连接
ping data.17500.cn

# 测试数据源访问
curl -I https://data.17500.cn/3d_asc.txt

# 查看数据更新日志
tail -f logs/data_update.log
```
**解决方案**:
- 检查网络配置
- 更新数据源地址
- 手动触发数据更新

### 故障诊断工具

#### 系统诊断脚本
```python
#!/usr/bin/env python3
# diagnose_system.py

import requests
import sqlite3
import psutil
import os
from pathlib import Path

def diagnose_system():
    print("🔍 系统诊断开始...")
    
    issues = []
    
    # 1. 检查API服务
    try:
        response = requests.get("http://localhost:8000/api/v1/health/", timeout=5)
        if response.status_code != 200:
            issues.append("API服务响应异常")
    except:
        issues.append("API服务无法访问")
    
    # 2. 检查数据库
    try:
        conn = sqlite3.connect('lottery_data.db', timeout=5)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM lottery_records")
        count = cursor.fetchone()[0]
        if count < 1000:
            issues.append("数据库记录数过少")
        conn.close()
    except:
        issues.append("数据库连接失败")
    
    # 3. 检查系统资源
    memory = psutil.virtual_memory()
    if memory.percent > 90:
        issues.append(f"内存使用率过高: {memory.percent}%")
    
    cpu = psutil.cpu_percent(interval=1)
    if cpu > 90:
        issues.append(f"CPU使用率过高: {cpu}%")
    
    disk = psutil.disk_usage('.')
    disk_percent = (disk.used / disk.total) * 100
    if disk_percent > 95:
        issues.append(f"磁盘使用率过高: {disk_percent:.1f}%")
    
    # 4. 检查关键文件
    critical_files = [
        'lottery_data.db',
        'src/api/production_main.py',
        'src/ui/main.py'
    ]
    
    for file_path in critical_files:
        if not Path(file_path).exists():
            issues.append(f"关键文件缺失: {file_path}")
    
    # 输出诊断结果
    if issues:
        print("❌ 发现以下问题:")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("✅ 系统状态正常")
    
    return len(issues) == 0

if __name__ == "__main__":
    diagnose_system()
```

## 维护操作

### 日常维护任务

#### 1. 数据库维护
```bash
# 数据库优化
sqlite3 lottery_data.db "VACUUM;"

# 重建索引
sqlite3 lottery_data.db "REINDEX;"

# 检查数据库完整性
sqlite3 lottery_data.db "PRAGMA integrity_check;"
```

#### 2. 日志清理
```bash
# 清理旧日志（保留最近7天）
find logs/ -name "*.log" -mtime +7 -delete

# 压缩日志文件
gzip logs/*.log.old
```

#### 3. 缓存清理
```bash
# 清理数据缓存
rm -rf data/cache/*

# 清理临时文件
find . -name "*.tmp" -delete
find . -name "__pycache__" -type d -exec rm -rf {} +
```

#### 4. 系统更新
```bash
# 更新Python依赖
pip install -r requirements.txt --upgrade

# 检查系统更新
python -m pip list --outdated
```

### 定期维护计划

#### 每日维护
- 检查系统健康状态
- 监控性能指标
- 查看错误日志
- 验证数据更新

#### 每周维护
- 数据库优化
- 日志清理
- 性能分析
- 备份验证

#### 每月维护
- 系统更新
- 安全检查
- 容量规划
- 文档更新

## 备份恢复

### 数据备份

#### 自动备份脚本
```bash
#!/bin/bash
# backup_system.sh

BACKUP_DIR="/backup/lottery_system"
DATE=$(date +%Y%m%d_%H%M%S)

echo "开始系统备份: $DATE"

# 创建备份目录
mkdir -p "$BACKUP_DIR/$DATE"

# 备份数据库
cp lottery_data.db "$BACKUP_DIR/$DATE/"

# 备份配置文件
cp -r src/config "$BACKUP_DIR/$DATE/" 2>/dev/null || true

# 备份日志文件
cp -r logs "$BACKUP_DIR/$DATE/"

# 压缩备份
cd "$BACKUP_DIR"
tar -czf "lottery_system_$DATE.tar.gz" "$DATE"
rm -rf "$DATE"

echo "备份完成: lottery_system_$DATE.tar.gz"

# 清理旧备份（保留30天）
find "$BACKUP_DIR" -name "*.tar.gz" -mtime +30 -delete
```

#### 手动备份
```bash
# 备份数据库
cp lottery_data.db backup/lottery_data_$(date +%Y%m%d).db

# 备份整个系统
tar -czf lottery_system_backup_$(date +%Y%m%d).tar.gz \
    --exclude='*.pyc' \
    --exclude='__pycache__' \
    --exclude='logs/*.log' \
    .
```

### 数据恢复

#### 数据库恢复
```bash
# 停止服务
pkill -f production_main.py

# 恢复数据库
cp backup/lottery_data_20250125.db lottery_data.db

# 验证数据库
sqlite3 lottery_data.db "PRAGMA integrity_check;"

# 重启服务
python src/api/production_main.py &
```

#### 系统恢复
```bash
# 解压备份
tar -xzf lottery_system_backup_20250125.tar.gz

# 恢复配置
cp -r backup/config/* src/config/

# 重启所有服务
./restart_all.sh
```

## 安全管理

### 安全检查清单

#### 1. 文件权限检查
```bash
# 检查关键文件权限
ls -la lottery_data.db
ls -la src/config/

# 设置适当权限
chmod 600 lottery_data.db
chmod 755 src/
```

#### 2. 网络安全检查
```bash
# 检查开放端口
netstat -tlnp

# 检查防火墙状态
ufw status
```

#### 3. 访问日志审计
```bash
# 分析访问日志
grep "ERROR" logs/api.log
grep "WARN" logs/system.log

# 检查异常访问
grep "404\|500" logs/access.log
```

### 安全配置

#### API安全配置
```python
# 在 production_main.py 中添加安全中间件
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:8501"],
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)

# 可信主机配置
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["localhost", "127.0.0.1"]
)
```

#### 数据库安全配置
```python
# 数据库连接安全配置
import sqlite3

def secure_db_connection():
    conn = sqlite3.connect(
        'lottery_data.db',
        timeout=30,
        check_same_thread=False
    )
    
    # 启用外键约束
    conn.execute("PRAGMA foreign_keys = ON")
    
    # 设置安全模式
    conn.execute("PRAGMA secure_delete = ON")
    
    return conn
```

---

**版本**: v1.0.0  
**更新日期**: 2025-01-25  
**适用系统**: 福彩3D预测系统
