#!/usr/bin/env python3
"""
测试修复后的预测系统
"""

import sys

sys.path.append('src')
from prediction.intelligent_fusion import IntelligentFusionSystem


def test_prediction_variability():
    """测试预测结果的变化性"""
    print('测试修复后的预测系统...')
    system = IntelligentFusionSystem()

    # 强制重新训练以应用数据排序修复
    print('强制重新训练模型以应用数据排序修复...')
    train_result = system.train_all_models(force_retrain=True)
    print('训练完成')

    # 进行多次预测测试，看是否有变化
    results = []
    for i in range(5):
        print(f'\n=== 第{i+1}次预测 ===')
        prediction = system.generate_fusion_prediction([], max_candidates=10, confidence_threshold=0.01)
        
        main_number = prediction.get('numbers', 'N/A')
        confidence = prediction.get('confidence', 0)
        
        print(f'推荐号码: {main_number}')
        print(f'置信度: {confidence:.3f}')
        
        results.append((main_number, confidence))
        
        if prediction.get('candidates'):
            print('前3个候选:')
            for j, c in enumerate(prediction.get('candidates', [])[:3]):
                numbers = c.get('numbers', 'N/A')
                conf = c.get('confidence', 0)
                print(f'  {j+1}. {numbers} (置信度: {conf:.3f})')
    
    # 分析结果变化性
    print('\n=== 结果分析 ===')
    unique_numbers = set(r[0] for r in results)
    unique_confidences = set(f"{r[1]:.3f}" for r in results)
    
    print(f'不同的推荐号码数量: {len(unique_numbers)}')
    print(f'不同的置信度数量: {len(unique_confidences)}')
    print(f'所有推荐号码: {list(unique_numbers)}')
    
    if len(unique_numbers) > 1:
        print('✅ 预测结果有变化性 - 问题已修复!')
    else:
        print('❌ 预测结果仍然固定 - 需要进一步修复')

if __name__ == "__main__":
    test_prediction_variability()
