"""
福彩3D调度器配置管理器

处理UI配置与调度器配置的同步，确保配置一致性
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器 - 处理UI配置与调度器配置的同步"""
    
    def __init__(self, scheduler_config_file: str = "scheduler_config.json", 
                 ui_config_file: str = "data/ui_config.json"):
        """
        初始化配置管理器
        
        Args:
            scheduler_config_file: 调度器配置文件路径
            ui_config_file: UI配置文件路径
        """
        self.scheduler_config_file = Path(scheduler_config_file)
        self.ui_config_file = Path(ui_config_file)
        
        # 确保目录存在
        self.ui_config_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 初始化配置文件
        self._ensure_config_files_exist()
    
    def _ensure_config_files_exist(self):
        """确保配置文件存在"""
        # 调度器配置文件
        if not self.scheduler_config_file.exists():
            self._create_default_scheduler_config()
        
        # UI配置文件
        if not self.ui_config_file.exists():
            self._create_default_ui_config()
    
    def _create_default_scheduler_config(self):
        """创建默认调度器配置"""
        default_config = {
            "update_schedule": {
                "enabled": True,
                "cron": "30 21 * * *",
                "timezone": "Asia/Shanghai",
                "description": "每天21:30执行数据更新"
            },
            "cleanup_schedule": {
                "enabled": True,
                "cron": "0 2 * * 0",
                "keep_files": 10,
                "description": "每周日02:00执行文件清理"
            },
            "monitoring": {
                "enabled": True,
                "max_failures": 3,
                "notification_email": None
            },
            "logging": {
                "level": "INFO",
                "max_log_files": 30,
                "log_rotation": "daily"
            }
        }
        
        self._save_json_file(self.scheduler_config_file, default_config)
        logger.info(f"创建默认调度器配置: {self.scheduler_config_file}")
    
    def _create_default_ui_config(self):
        """创建默认UI配置"""
        default_config = {
            "auto_update": {
                "enabled": True,
                "selected_time": "21:30 (推荐)",
                "custom_cron": "30 21 * * *",
                "last_updated": datetime.now().isoformat()
            },
            "preferences": {
                "show_advanced_options": False,
                "auto_refresh_status": True,
                "notification_enabled": True
            },
            "version": "1.0"
        }
        
        self._save_json_file(self.ui_config_file, default_config)
        logger.info(f"创建默认UI配置: {self.ui_config_file}")
    
    def _load_json_file(self, file_path: Path) -> Dict[str, Any]:
        """加载JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败 {file_path}: {e}")
            return {}
    
    def _save_json_file(self, file_path: Path, data: Dict[str, Any]) -> bool:
        """保存JSON文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            logger.error(f"保存配置文件失败 {file_path}: {e}")
            return False
    
    def load_scheduler_config(self) -> Dict[str, Any]:
        """加载调度器配置"""
        return self._load_json_file(self.scheduler_config_file)
    
    def save_scheduler_config(self, config: Dict[str, Any]) -> bool:
        """保存调度器配置"""
        return self._save_json_file(self.scheduler_config_file, config)
    
    def load_ui_config(self) -> Dict[str, Any]:
        """加载UI配置"""
        return self._load_json_file(self.ui_config_file)
    
    def save_ui_config(self, config: Dict[str, Any]) -> bool:
        """保存UI配置"""
        config["auto_update"]["last_updated"] = datetime.now().isoformat()
        return self._save_json_file(self.ui_config_file, config)
    
    def sync_ui_to_scheduler(self, ui_config: Dict[str, Any]) -> bool:
        """
        将UI配置同步到调度器配置
        
        Args:
            ui_config: UI配置字典
            
        Returns:
            是否同步成功
        """
        try:
            # 加载当前调度器配置
            scheduler_config = self.load_scheduler_config()
            
            # 提取UI中的自动更新配置
            auto_update = ui_config.get("auto_update", {})
            
            if auto_update.get("enabled"):
                # 更新调度器配置
                scheduler_config["update_schedule"]["enabled"] = True
                scheduler_config["update_schedule"]["cron"] = auto_update.get("custom_cron", "30 21 * * *")
                
                # 生成描述
                selected_time = auto_update.get("selected_time", "21:30")
                if "自定义" in selected_time:
                    description = f"自定义时间自动更新数据 ({auto_update.get('custom_cron')})"
                else:
                    description = f"每天{selected_time.split()[0]}自动更新数据"
                
                scheduler_config["update_schedule"]["description"] = description
            else:
                scheduler_config["update_schedule"]["enabled"] = False
            
            # 保存调度器配置
            success = self.save_scheduler_config(scheduler_config)
            
            if success:
                logger.info("UI配置已同步到调度器配置")
            
            return success
            
        except Exception as e:
            logger.error(f"同步UI配置到调度器失败: {e}")
            return False
    
    def sync_scheduler_to_ui(self) -> Dict[str, Any]:
        """
        将调度器配置同步到UI配置
        
        Returns:
            同步后的UI配置
        """
        try:
            # 加载配置
            scheduler_config = self.load_scheduler_config()
            ui_config = self.load_ui_config()
            
            # 提取调度器配置
            update_schedule = scheduler_config.get("update_schedule", {})
            
            # 更新UI配置
            ui_config["auto_update"]["enabled"] = update_schedule.get("enabled", False)
            
            # 解析cron表达式到UI选项
            cron = update_schedule.get("cron", "30 21 * * *")
            ui_config["auto_update"]["custom_cron"] = cron
            
            # 映射到UI选项
            time_mapping = {
                "30 21 * * *": "21:30 (推荐)",
                "0 21 * * *": "21:00",
                "0 22 * * *": "22:00", 
                "0 23 * * *": "23:00"
            }
            
            ui_config["auto_update"]["selected_time"] = time_mapping.get(cron, "自定义")
            
            # 保存UI配置
            self.save_ui_config(ui_config)
            
            logger.info("调度器配置已同步到UI配置")
            return ui_config
            
        except Exception as e:
            logger.error(f"同步调度器配置到UI失败: {e}")
            return self.load_ui_config()
    
    def validate_config(self, config: Dict[str, Any], config_type: str = "scheduler") -> Tuple[bool, List[str]]:
        """
        验证配置文件
        
        Args:
            config: 配置字典
            config_type: 配置类型 ("scheduler" 或 "ui")
            
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        try:
            if config_type == "scheduler":
                # 验证调度器配置
                if "update_schedule" not in config:
                    errors.append("缺少 update_schedule 配置")
                else:
                    update_schedule = config["update_schedule"]
                    
                    if "cron" not in update_schedule:
                        errors.append("缺少 cron 表达式")
                    else:
                        # 简单验证cron表达式格式
                        cron_parts = update_schedule["cron"].split()
                        if len(cron_parts) != 5:
                            errors.append("cron表达式格式错误，应为5个字段")
                    
                    if "enabled" not in update_schedule:
                        errors.append("缺少 enabled 配置")
            
            elif config_type == "ui":
                # 验证UI配置
                if "auto_update" not in config:
                    errors.append("缺少 auto_update 配置")
                else:
                    auto_update = config["auto_update"]
                    
                    if "enabled" not in auto_update:
                        errors.append("缺少 enabled 配置")
                    
                    if "custom_cron" not in auto_update:
                        errors.append("缺少 custom_cron 配置")
            
            return len(errors) == 0, errors
            
        except Exception as e:
            errors.append(f"配置验证异常: {e}")
            return False, errors
    
    def get_time_options(self) -> Dict[str, str]:
        """获取时间选项映射"""
        return {
            "21:30 (推荐)": "30 21 * * *",
            "21:00": "0 21 * * *",
            "22:00": "0 22 * * *", 
            "23:00": "0 23 * * *",
            "自定义": "custom"
        }
    
    def cron_to_description(self, cron_expression: str) -> str:
        """将cron表达式转换为描述"""
        time_descriptions = {
            "30 21 * * *": "每天21:30",
            "0 21 * * *": "每天21:00",
            "0 22 * * *": "每天22:00",
            "0 23 * * *": "每天23:00"
        }
        
        return time_descriptions.get(cron_expression, f"自定义时间 ({cron_expression})")
    
    def backup_config(self, config_type: str = "both") -> bool:
        """
        备份配置文件
        
        Args:
            config_type: 备份类型 ("scheduler", "ui", "both")
            
        Returns:
            是否备份成功
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if config_type in ["scheduler", "both"]:
                if self.scheduler_config_file.exists():
                    backup_path = self.scheduler_config_file.with_suffix(f".backup_{timestamp}.json")
                    backup_path.write_text(self.scheduler_config_file.read_text(encoding='utf-8'), encoding='utf-8')
                    logger.info(f"调度器配置已备份到: {backup_path}")
            
            if config_type in ["ui", "both"]:
                if self.ui_config_file.exists():
                    backup_path = self.ui_config_file.with_suffix(f".backup_{timestamp}.json")
                    backup_path.write_text(self.ui_config_file.read_text(encoding='utf-8'), encoding='utf-8')
                    logger.info(f"UI配置已备份到: {backup_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"配置备份失败: {e}")
            return False


# 全局配置管理器实例
_config_manager_instance = None

def get_config_manager() -> ConfigManager:
    """获取配置管理器单例"""
    global _config_manager_instance
    if _config_manager_instance is None:
        _config_manager_instance = ConfigManager()
    return _config_manager_instance
