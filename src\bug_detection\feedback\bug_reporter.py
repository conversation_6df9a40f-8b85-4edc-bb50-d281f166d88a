"""
Bug报告生成器
创建日期: 2025年7月24日
用途: 自动生成结构化Bug报告，包含错误信息、上下文和系统状态
"""

import json
import logging
import os
import platform
import sys
import traceback
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

import psutil
import streamlit as st

logger = logging.getLogger(__name__)

# 导入增强检测算法
try:
    from src.bug_detection.algorithms.enhanced_detection import \
        EnhancedBugDetector
    ENHANCED_DETECTION_AVAILABLE = True
except ImportError:
    ENHANCED_DETECTION_AVAILABLE = False
    logger.warning("Enhanced detection algorithm not available")

class BugReporter:
    """基础Bug报告生成器"""
    
    def __init__(self, database_manager=None):
        """初始化Bug报告生成器"""
        self.db_manager = database_manager
        self.system_info = self._collect_system_info()
    
    def _collect_system_info(self) -> Dict[str, Any]:
        """收集系统信息"""
        try:
            return {
                'platform': platform.platform(),
                'python_version': sys.version,
                'cpu_count': psutil.cpu_count(),
                'memory_total': psutil.virtual_memory().total,
                'disk_usage': psutil.disk_usage('/').percent if os.name != 'nt' else psutil.disk_usage('C:').percent,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error collecting system info: {e}")
            return {'error': str(e)}
    
    def generate_bug_report(self, 
                          error_data: Dict[str, Any],
                          context: Dict[str, Any] = None,
                          user_actions: List[str] = None) -> Dict[str, Any]:
        """
        生成Bug报告
        
        Args:
            error_data: 错误信息
            context: 上下文信息
            user_actions: 用户操作序列
        
        Returns:
            完整的Bug报告
        """
        
        bug_id = f"BUG_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        
        # 基础报告结构
        bug_report = {
            'id': bug_id,
            'timestamp': datetime.now().isoformat(),
            'version': '1.0.0',
            'reporter': 'AutoBugDetectionSystem',
            
            # 错误信息
            'error': {
                'type': error_data.get('type', 'unknown'),
                'message': error_data.get('message', ''),
                'severity': self._assess_severity(error_data),
                'stack_trace': error_data.get('stack_trace', ''),
                'source': error_data.get('source', ''),
                'line_number': error_data.get('line_number'),
                'column_number': error_data.get('column_number')
            },
            
            # 环境信息
            'environment': {
                'system': self.system_info,
                'browser': error_data.get('user_agent', ''),
                'page_url': error_data.get('page_url', ''),
                'session_id': error_data.get('session_id', '')
            },
            
            # 上下文信息
            'context': context or {},
            
            # 用户操作
            'user_actions': user_actions or [],
            
            # 重现步骤
            'reproduction_steps': self._generate_reproduction_steps(error_data, user_actions),
            
            # 系统状态
            'system_state': self._collect_system_state(),
            
            # 建议的修复方案
            'suggested_fixes': self._generate_fix_suggestions(error_data),
            
            # 优先级和分类
            'priority': self._calculate_priority(error_data),
            'category': self._categorize_error(error_data),
            'tags': self._generate_tags(error_data)
        }
        
        # 保存到数据库
        if self.db_manager:
            try:
                self.db_manager.save_bug_report(bug_report)
                logger.info(f"Bug report saved: {bug_id}")
            except Exception as e:
                logger.error(f"Error saving bug report: {e}")
        
        return bug_report
    
    def _assess_severity(self, error_data: Dict[str, Any]) -> str:
        """评估错误严重程度"""
        error_type = error_data.get('type', '').lower()
        error_message = error_data.get('message', '').lower()
        
        # 严重错误关键词
        critical_keywords = ['crash', 'fatal', 'critical', 'system', 'database']
        high_keywords = ['error', 'exception', 'failed', 'timeout']
        medium_keywords = ['warning', 'deprecated', 'slow']
        
        if any(keyword in error_message for keyword in critical_keywords):
            return 'critical'
        elif any(keyword in error_message for keyword in high_keywords):
            return 'high'
        elif any(keyword in error_message for keyword in medium_keywords):
            return 'medium'
        else:
            return 'low'
    
    def _collect_system_state(self) -> Dict[str, Any]:
        """收集当前系统状态"""
        try:
            return {
                'cpu_usage': psutil.cpu_percent(interval=1),
                'memory_usage': psutil.virtual_memory().percent,
                'disk_usage': psutil.disk_usage('/').percent if os.name != 'nt' else psutil.disk_usage('C:').percent,
                'active_processes': len(psutil.pids()),
                'network_connections': len(psutil.net_connections()),
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error collecting system state: {e}")
            return {'error': str(e)}
    
    def _generate_reproduction_steps(self, error_data: Dict[str, Any], user_actions: List[str] = None) -> List[str]:
        """生成重现步骤"""
        steps = []
        
        # 基础步骤
        if error_data.get('page_url'):
            steps.append(f"1. 访问页面: {error_data['page_url']}")
        
        # 用户操作步骤
        if user_actions:
            for i, action in enumerate(user_actions, 2):
                steps.append(f"{i}. {action}")
        
        # 错误触发步骤
        if error_data.get('type') == 'javascript':
            steps.append(f"{len(steps) + 1}. JavaScript错误自动触发")
        elif error_data.get('type') == 'api_error':
            steps.append(f"{len(steps) + 1}. API调用失败")
        
        return steps
    
    def _generate_fix_suggestions(self, error_data: Dict[str, Any]) -> List[str]:
        """生成修复建议"""
        suggestions = []
        error_type = error_data.get('type', '').lower()
        error_message = error_data.get('message', '').lower()
        
        # 基于错误类型的建议
        if 'javascript' in error_type:
            suggestions.extend([
                "检查JavaScript代码语法错误",
                "验证变量和函数定义",
                "检查浏览器兼容性问题"
            ])
        
        if 'api' in error_type or 'network' in error_type:
            suggestions.extend([
                "检查API端点是否可访问",
                "验证请求参数格式",
                "检查网络连接状态"
            ])
        
        if 'timeout' in error_message:
            suggestions.extend([
                "增加请求超时时间",
                "优化查询性能",
                "检查数据库连接"
            ])
        
        if 'memory' in error_message:
            suggestions.extend([
                "检查内存泄漏",
                "优化数据处理逻辑",
                "增加系统内存"
            ])
        
        return suggestions or ["需要进一步分析错误原因"]
    
    def _calculate_priority(self, error_data: Dict[str, Any]) -> str:
        """计算优先级"""
        severity = self._assess_severity(error_data)
        error_type = error_data.get('type', '').lower()
        
        # 优先级映射
        if severity == 'critical':
            return 'P1'
        elif severity == 'high':
            return 'P2'
        elif severity == 'medium':
            return 'P3'
        else:
            return 'P4'
    
    def _categorize_error(self, error_data: Dict[str, Any]) -> str:
        """错误分类"""
        error_type = error_data.get('type', '').lower()
        error_message = error_data.get('message', '').lower()
        
        if 'javascript' in error_type:
            return 'Frontend'
        elif 'api' in error_type:
            return 'Backend'
        elif 'database' in error_message:
            return 'Database'
        elif 'network' in error_type:
            return 'Network'
        else:
            return 'General'
    
    def _generate_tags(self, error_data: Dict[str, Any]) -> List[str]:
        """生成标签"""
        tags = []
        error_type = error_data.get('type', '').lower()
        error_message = error_data.get('message', '').lower()
        
        # 基础标签
        tags.append(f"type:{error_type}")
        tags.append(f"severity:{self._assess_severity(error_data)}")
        
        # 特征标签
        if 'timeout' in error_message:
            tags.append('timeout')
        if 'memory' in error_message:
            tags.append('memory')
        if 'network' in error_message:
            tags.append('network')
        
        return tags

class IntelligentBugReporter(BugReporter):
    """智能Bug报告生成器（扩展版）"""

    def __init__(self, database_manager=None):
        super().__init__(database_manager)
        self.historical_bugs = []

        # 初始化增强检测器
        if ENHANCED_DETECTION_AVAILABLE:
            self.enhanced_detector = EnhancedBugDetector(database_manager)
        else:
            self.enhanced_detector = None
    
    def generate_enhanced_report(self, error_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """生成增强的Bug报告"""

        # 使用增强检测算法（如果可用）
        if self.enhanced_detector:
            enhanced_analysis = self.enhanced_detector.detect_and_classify(error_data)

            # 生成基础报告
            base_report = self.generate_bug_report(error_data, **kwargs)

            # 合并增强分析结果
            base_report.update({
                'enhanced_analysis': enhanced_analysis,
                'severity': enhanced_analysis.get('severity', base_report.get('severity')),
                'category': enhanced_analysis.get('category', base_report.get('category')),
                'priority': enhanced_analysis.get('priority', base_report.get('priority')),
                'environment': enhanced_analysis.get('environment', 'production'),
                'impact_analysis': enhanced_analysis.get('impact_analysis', {}),
                'similar_errors': enhanced_analysis.get('similar_errors', []),
                'fix_suggestions': enhanced_analysis.get('fix_suggestions', []),
                'tags': enhanced_analysis.get('tags', []),
                'confidence_score': enhanced_analysis.get('confidence_score', 0.5)
            })

            return base_report
        else:
            # 回退到原有逻辑
            base_report = self.generate_bug_report(error_data, **kwargs)

            # 添加智能分析
            base_report.update({
                'similar_bugs': self._find_similar_bugs(error_data),
                'impact_analysis': self._analyze_impact(error_data),
                'root_cause_analysis': self._analyze_root_cause(error_data),
                'automated_tests': self._suggest_automated_tests(error_data)
            })

            return base_report
    
    def _find_similar_bugs(self, error_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """查找相似的Bug"""
        # 这里可以实现更复杂的相似性算法
        similar_bugs = []
        
        if self.db_manager:
            try:
                historical_bugs = self.db_manager.get_bug_reports(limit=100)
                
                for bug in historical_bugs:
                    similarity_score = self._calculate_similarity(error_data, bug)
                    if similarity_score > 0.7:  # 相似度阈值
                        similar_bugs.append({
                            'bug_id': bug.get('id'),
                            'similarity': similarity_score,
                            'status': bug.get('status'),
                            'resolution': bug.get('resolution_notes')
                        })
                
            except Exception as e:
                logger.error(f"Error finding similar bugs: {e}")
        
        return similar_bugs[:5]  # 返回最相似的5个
    
    def _calculate_similarity(self, error1: Dict[str, Any], error2: Dict[str, Any]) -> float:
        """计算错误相似度"""
        # 简单的相似度计算
        score = 0.0
        
        # 错误类型相似度
        if error1.get('type') == error2.get('error_type'):
            score += 0.3
        
        # 错误消息相似度（简化版）
        msg1 = error1.get('message', '').lower()
        msg2 = error2.get('error_message', '').lower()
        
        if msg1 and msg2:
            common_words = set(msg1.split()) & set(msg2.split())
            total_words = set(msg1.split()) | set(msg2.split())
            if total_words:
                score += 0.4 * (len(common_words) / len(total_words))
        
        # 页面相似度
        if error1.get('page_url') == error2.get('page_name'):
            score += 0.3
        
        return score
    
    def _analyze_impact(self, error_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析错误影响"""
        return {
            'user_impact': self._assess_user_impact(error_data),
            'system_impact': self._assess_system_impact(error_data),
            'business_impact': self._assess_business_impact(error_data)
        }
    
    def _assess_user_impact(self, error_data: Dict[str, Any]) -> str:
        """评估用户影响"""
        severity = self._assess_severity(error_data)
        
        if severity == 'critical':
            return 'High - Users cannot complete core functions'
        elif severity == 'high':
            return 'Medium - Users experience significant issues'
        elif severity == 'medium':
            return 'Low - Users experience minor inconvenience'
        else:
            return 'Minimal - Users barely notice the issue'
    
    def _assess_system_impact(self, error_data: Dict[str, Any]) -> str:
        """评估系统影响"""
        error_type = error_data.get('type', '').lower()
        
        if 'database' in error_type:
            return 'High - Database operations affected'
        elif 'api' in error_type:
            return 'Medium - API functionality impacted'
        else:
            return 'Low - Isolated component issue'
    
    def _assess_business_impact(self, error_data: Dict[str, Any]) -> str:
        """评估业务影响"""
        severity = self._assess_severity(error_data)
        
        if severity == 'critical':
            return 'High - Core business functions disrupted'
        elif severity == 'high':
            return 'Medium - Some business processes affected'
        else:
            return 'Low - Minimal business impact'
    
    def _analyze_root_cause(self, error_data: Dict[str, Any]) -> Dict[str, Any]:
        """根因分析"""
        return {
            'probable_causes': self._identify_probable_causes(error_data),
            'investigation_steps': self._suggest_investigation_steps(error_data),
            'prevention_measures': self._suggest_prevention_measures(error_data)
        }
    
    def _identify_probable_causes(self, error_data: Dict[str, Any]) -> List[str]:
        """识别可能的原因"""
        causes = []
        error_message = error_data.get('message', '').lower()
        
        if 'timeout' in error_message:
            causes.extend(['Network latency', 'Database slow query', 'Resource contention'])
        
        if 'memory' in error_message:
            causes.extend(['Memory leak', 'Large data processing', 'Insufficient memory allocation'])
        
        if 'undefined' in error_message:
            causes.extend(['Variable not initialized', 'Scope issue', 'Timing problem'])
        
        return causes or ['Unknown - requires investigation']
    
    def _suggest_investigation_steps(self, error_data: Dict[str, Any]) -> List[str]:
        """建议调查步骤"""
        return [
            'Review error logs around the time of occurrence',
            'Check system resource usage during the error',
            'Verify recent code changes',
            'Test in different environments',
            'Monitor for pattern recurrence'
        ]
    
    def _suggest_prevention_measures(self, error_data: Dict[str, Any]) -> List[str]:
        """建议预防措施"""
        return [
            'Add comprehensive error handling',
            'Implement input validation',
            'Add monitoring and alerting',
            'Create automated tests for this scenario',
            'Document the fix for future reference'
        ]
    
    def _suggest_automated_tests(self, error_data: Dict[str, Any]) -> List[str]:
        """建议自动化测试"""
        tests = []
        error_type = error_data.get('type', '').lower()
        
        if 'javascript' in error_type:
            tests.extend([
                'Unit test for the failing JavaScript function',
                'Integration test for the page functionality',
                'Browser compatibility test'
            ])
        
        if 'api' in error_type:
            tests.extend([
                'API endpoint unit test',
                'API integration test',
                'Load test for the endpoint'
            ])
        
        return tests or ['General regression test for the affected functionality']
