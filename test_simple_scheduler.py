#!/usr/bin/env python3
"""
测试简单调度器
"""

import sys
sys.path.append('src')

from scheduler.simple_scheduler import SimpleScheduler
import time

def test_simple_scheduler():
    """测试简单调度器"""
    print("开始测试简单定时任务调度器...")
    
    try:
        # 1. 创建调度器
        print("\n1. 创建调度器...")
        scheduler = SimpleScheduler()
        print("✅ 调度器创建成功")
        
        # 2. 检查状态
        print("\n2. 检查调度器状态...")
        status = scheduler.get_status()
        
        print(f"📊 调度器状态:")
        print(f"   - 运行状态: {'运行中' if status['running'] else '已停止'}")
        print(f"   - 任务数量: {len(status['tasks'])}")
        print(f"   - 活动线程: {status['active_threads']}")
        
        print(f"   - 任务列表:")
        for task in status['tasks']:
            print(f"     * {task['name']} (ID: {task['id']})")
            print(f"       启用状态: {'启用' if task['enabled'] else '禁用'}")
            print(f"       间隔时间: {task['interval']}秒")
            print(f"       上次运行: {task['last_run'] or '从未运行'}")
        
        # 3. 手动执行任务
        print("\n3. 手动执行数据更新任务...")
        success = scheduler.run_task_now("data_update")
        
        if success:
            print("✅ 数据更新任务执行成功")
        else:
            print("❌ 数据更新任务执行失败")
        
        # 4. 检查更新后状态
        print("\n4. 检查更新后状态...")
        new_status = scheduler.get_status()
        
        data_update_task = next(
            (task for task in new_status['tasks'] if task['id'] == 'data_update'),
            None
        )
        
        if data_update_task and data_update_task['last_run']:
            print(f"✅ 数据更新任务状态已更新")
            print(f"   上次运行: {data_update_task['last_run']}")
        
        # 5. 测试任务控制
        print("\n5. 测试任务控制...")
        
        # 禁用任务
        scheduler.disable_task("file_cleanup")
        print("✅ 文件清理任务已禁用")
        
        # 启用任务
        scheduler.enable_task("file_cleanup")
        print("✅ 文件清理任务已启用")
        
        # 设置任务间隔
        scheduler.set_task_interval("log_cleanup", 12 * 3600)  # 12小时
        print("✅ 日志清理任务间隔已设置为12小时")
        
        # 6. 启动调度器
        print("\n6. 启动调度器...")
        scheduler.start()
        print("✅ 调度器启动成功")
        
        # 运行一小段时间
        print("   调度器运行中，3秒后停止...")
        time.sleep(3)
        
        # 7. 停止调度器
        print("\n7. 停止调度器...")
        scheduler.stop()
        print("✅ 调度器停止成功")
        
        # 8. 最终状态检查
        print("\n8. 最终状态检查...")
        final_status = scheduler.get_status()
        print(f"📊 最终状态:")
        print(f"   - 运行状态: {'运行中' if final_status['running'] else '已停止'}")
        print(f"   - 活动线程: {final_status['active_threads']}")
        
        print(f"\n🎉 简单调度器测试成功！")
        print(f"📋 测试摘要:")
        print(f"   - 调度器创建: ✅")
        print(f"   - 状态检查: ✅")
        print(f"   - 手动执行任务: ✅")
        print(f"   - 任务控制: ✅")
        print(f"   - 启动/停止: ✅")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_simple_scheduler()
