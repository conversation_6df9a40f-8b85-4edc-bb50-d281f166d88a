#!/usr/bin/env python3
"""
HMM随机性建模器
使用隐马尔可夫模型建模随机成分，区分规律期和随机期
"""

import numpy as np
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime
import json
import os
from collections import Counter


class HMMRandomnessModeler:
    """HMM随机性建模器"""
    
    def __init__(self, cache_dir: str = "data/cache"):
        """
        初始化HMM建模器
        
        Args:
            cache_dir: 缓存目录
        """
        self.cache_dir = cache_dir
        
        # 确保缓存目录存在
        os.makedirs(cache_dir, exist_ok=True)
        
        # HMM参数
        self.n_states = 2  # 两个隐藏状态：规律期、随机期
        self.state_names = ['规律期', '随机期']
        
        # 模型参数
        self.transition_matrix = None
        self.emission_matrix = None
        self.initial_probs = None
        
        # 训练数据
        self.observations = []
        self.state_sequence = []
        
        # 模型是否已训练
        self.is_trained = False
    
    def prepare_observations(self, historical_data: List[str]) -> List[int]:
        """
        准备观测序列
        
        Args:
            historical_data: 历史号码数据
            
        Returns:
            观测序列
        """
        observations = []
        
        for numbers in historical_data:
            if len(numbers) >= 3:
                # 计算号码的复杂度作为观测值
                complexity = self._calculate_complexity(numbers)
                observations.append(complexity)
        
        return observations
    
    def _calculate_complexity(self, numbers: str) -> int:
        """
        计算号码复杂度
        
        Args:
            numbers: 三位数号码
            
        Returns:
            复杂度等级 (0-9)
        """
        if len(numbers) != 3:
            return 5  # 默认中等复杂度
        
        try:
            digits = [int(d) for d in numbers]
            
            # 计算多个复杂度指标
            unique_count = len(set(digits))  # 不同数字个数
            sum_value = sum(digits)          # 数字和
            span_value = max(digits) - min(digits)  # 跨度
            
            # 检查连续性
            sorted_digits = sorted(digits)
            is_consecutive = all(sorted_digits[i+1] - sorted_digits[i] == 1 for i in range(len(sorted_digits)-1))
            
            # 检查重复模式
            has_repeat = len(digits) != len(set(digits))
            
            # 综合复杂度计算
            complexity = 0
            
            # 基于不同数字个数
            if unique_count == 1:  # 豹子号
                complexity += 1
            elif unique_count == 2:  # 对子号
                complexity += 3
            else:  # 杂六号
                complexity += 5
            
            # 基于数字和
            if sum_value <= 5 or sum_value >= 22:
                complexity += 1  # 极端和值
            elif 8 <= sum_value <= 19:
                complexity += 2  # 中等和值
            
            # 基于跨度
            if span_value <= 2:
                complexity += 1  # 小跨度
            elif span_value >= 7:
                complexity += 2  # 大跨度
            
            # 连续性影响
            if is_consecutive:
                complexity += 1
            
            # 重复模式影响
            if has_repeat:
                complexity -= 1
            
            # 限制在0-9范围内
            complexity = max(0, min(9, complexity))
            
            return complexity
            
        except Exception as e:
            print(f"复杂度计算失败: {e}")
            return 5
    
    def train_hmm(self, historical_data: List[str]) -> Dict[str, Any]:
        """
        训练HMM模型
        
        Args:
            historical_data: 历史号码数据
            
        Returns:
            训练结果
        """
        print("开始训练HMM随机性模型...")
        
        # 准备观测序列
        self.observations = self.prepare_observations(historical_data)
        
        if len(self.observations) < 10:
            return {'error': 'HMM训练需要至少10个观测值'}
        
        # 简化的HMM训练（不使用hmmlearn库，避免依赖问题）
        self._simple_hmm_training()
        
        # 使用Viterbi算法解码状态序列
        self.state_sequence = self._viterbi_decode()
        
        self.is_trained = True
        
        # 分析结果
        analysis = self._analyze_states()
        
        result = {
            'success': True,
            'observations_count': len(self.observations),
            'states_decoded': len(self.state_sequence),
            'state_analysis': analysis,
            'model_params': {
                'transition_matrix': self.transition_matrix.tolist() if self.transition_matrix is not None else None,
                'emission_matrix': self.emission_matrix.tolist() if self.emission_matrix is not None else None,
                'initial_probs': self.initial_probs.tolist() if self.initial_probs is not None else None
            }
        }
        
        # 保存模型
        self._save_model(result)
        
        print(f"HMM模型训练完成，解码了{len(self.state_sequence)}个状态")
        
        return result
    
    def _simple_hmm_training(self) -> None:
        """简化的HMM训练"""
        # 初始化参数
        self.initial_probs = np.array([0.5, 0.5])  # 均匀初始概率
        
        # 转移概率矩阵（规律期->规律期, 规律期->随机期, 随机期->规律期, 随机期->随机期）
        self.transition_matrix = np.array([
            [0.7, 0.3],  # 规律期倾向于保持规律
            [0.4, 0.6]   # 随机期倾向于保持随机
        ])
        
        # 发射概率矩阵（状态->观测值）
        # 规律期倾向于产生低复杂度观测，随机期倾向于产生高复杂度观测
        self.emission_matrix = np.zeros((2, 10))  # 2个状态，10个观测值等级
        
        # 规律期：低复杂度概率高
        for i in range(10):
            self.emission_matrix[0, i] = np.exp(-(i-2)**2 / 8)  # 以复杂度2为中心的高斯分布
        
        # 随机期：高复杂度概率高
        for i in range(10):
            self.emission_matrix[1, i] = np.exp(-(i-7)**2 / 8)  # 以复杂度7为中心的高斯分布
        
        # 归一化
        self.emission_matrix[0] /= self.emission_matrix[0].sum()
        self.emission_matrix[1] /= self.emission_matrix[1].sum()
    
    def _viterbi_decode(self) -> List[int]:
        """
        使用Viterbi算法解码状态序列
        
        Returns:
            状态序列
        """
        if not self.observations:
            return []
        
        T = len(self.observations)
        N = self.n_states
        
        # 初始化
        delta = np.zeros((T, N))
        psi = np.zeros((T, N), dtype=int)
        
        # 初始化第一个时刻
        for i in range(N):
            obs_idx = min(self.observations[0], 9)  # 确保索引在范围内
            delta[0, i] = self.initial_probs[i] * self.emission_matrix[i, obs_idx]
        
        # 递推
        for t in range(1, T):
            obs_idx = min(self.observations[t], 9)
            for j in range(N):
                # 计算到达状态j的最大概率
                probs = [delta[t-1, i] * self.transition_matrix[i, j] for i in range(N)]
                delta[t, j] = max(probs) * self.emission_matrix[j, obs_idx]
                psi[t, j] = np.argmax(probs)
        
        # 回溯
        states = [0] * T
        states[T-1] = np.argmax(delta[T-1])
        
        for t in range(T-2, -1, -1):
            states[t] = psi[t+1, states[t+1]]
        
        return states
    
    def _analyze_states(self) -> Dict[str, Any]:
        """分析状态序列"""
        if not self.state_sequence:
            return {}
        
        # 统计各状态比例
        state_counts = Counter(self.state_sequence)
        total = len(self.state_sequence)
        
        state_ratios = {
            self.state_names[state]: count / total 
            for state, count in state_counts.items()
        }
        
        # 分析状态转换
        transitions = []
        for i in range(len(self.state_sequence) - 1):
            current_state = self.state_sequence[i]
            next_state = self.state_sequence[i + 1]
            transitions.append((current_state, next_state))
        
        transition_counts = Counter(transitions)
        
        # 计算平均状态持续时间
        state_durations = {0: [], 1: []}
        current_state = self.state_sequence[0]
        duration = 1
        
        for i in range(1, len(self.state_sequence)):
            if self.state_sequence[i] == current_state:
                duration += 1
            else:
                state_durations[current_state].append(duration)
                current_state = self.state_sequence[i]
                duration = 1
        
        # 添加最后一个状态的持续时间
        state_durations[current_state].append(duration)
        
        avg_durations = {}
        for state, durations in state_durations.items():
            if durations:
                avg_durations[self.state_names[state]] = sum(durations) / len(durations)
        
        return {
            'state_ratios': state_ratios,
            'transition_counts': {f"{self.state_names[t[0]]}->{self.state_names[t[1]]}": count 
                                for t, count in transition_counts.items()},
            'average_durations': avg_durations,
            'total_periods': len(self.state_sequence)
        }
    
    def predict_current_state(self, recent_data: List[str], window_size: int = 10) -> Dict[str, Any]:
        """
        预测当前状态
        
        Args:
            recent_data: 最近的数据
            window_size: 窗口大小
            
        Returns:
            状态预测结果
        """
        if not self.is_trained:
            return {'error': 'HMM模型未训练'}
        
        # 准备最近的观测
        recent_observations = self.prepare_observations(recent_data[-window_size:])
        
        if not recent_observations:
            return {'error': '没有有效的观测数据'}
        
        # 使用最近的观测预测当前状态
        recent_states = self._viterbi_decode_partial(recent_observations)
        
        if not recent_states:
            return {'error': '状态解码失败'}
        
        current_state = recent_states[-1]
        current_state_name = self.state_names[current_state]
        
        # 计算状态概率
        state_probs = self._calculate_state_probabilities(recent_observations[-1])
        
        # 预测下一个状态
        next_state_probs = self.transition_matrix[current_state]
        
        return {
            'current_state': current_state_name,
            'current_state_id': current_state,
            'state_probabilities': {
                self.state_names[i]: prob for i, prob in enumerate(state_probs)
            },
            'next_state_probabilities': {
                self.state_names[i]: prob for i, prob in enumerate(next_state_probs)
            },
            'recent_observations': recent_observations,
            'recent_states': [self.state_names[s] for s in recent_states]
        }
    
    def _viterbi_decode_partial(self, observations: List[int]) -> List[int]:
        """部分Viterbi解码"""
        if not observations:
            return []
        
        # 使用当前模型参数进行解码
        T = len(observations)
        N = self.n_states
        
        delta = np.zeros((T, N))
        psi = np.zeros((T, N), dtype=int)
        
        # 初始化
        for i in range(N):
            obs_idx = min(observations[0], 9)
            delta[0, i] = self.initial_probs[i] * self.emission_matrix[i, obs_idx]
        
        # 递推
        for t in range(1, T):
            obs_idx = min(observations[t], 9)
            for j in range(N):
                probs = [delta[t-1, i] * self.transition_matrix[i, j] for i in range(N)]
                delta[t, j] = max(probs) * self.emission_matrix[j, obs_idx]
                psi[t, j] = np.argmax(probs)
        
        # 回溯
        states = [0] * T
        states[T-1] = np.argmax(delta[T-1])
        
        for t in range(T-2, -1, -1):
            states[t] = psi[t+1, states[t+1]]
        
        return states
    
    def _calculate_state_probabilities(self, observation: int) -> np.ndarray:
        """计算给定观测下的状态概率"""
        obs_idx = min(observation, 9)
        
        # 计算每个状态的似然
        likelihoods = np.array([
            self.emission_matrix[i, obs_idx] for i in range(self.n_states)
        ])
        
        # 归一化为概率
        total = likelihoods.sum()
        if total > 0:
            return likelihoods / total
        else:
            return np.ones(self.n_states) / self.n_states
    
    def _save_model(self, result: Dict[str, Any]) -> None:
        """保存模型"""
        try:
            model_file = os.path.join(self.cache_dir, f"hmm_model_{int(datetime.now().timestamp())}.json")
            
            with open(model_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            print(f"HMM模型已保存到: {model_file}")
            
        except Exception as e:
            print(f"保存HMM模型失败: {e}")


def main():
    """主函数"""
    print("🎲 HMM随机性建模器")
    print("=" * 50)
    
    # 创建建模器
    modeler = HMMRandomnessModeler()
    
    # 模拟历史数据
    test_data = [
        "123", "456", "789", "012", "345", "678", "901", "234", "567", "890",
        "111", "222", "333", "444", "555", "666", "777", "888", "999", "000"
    ]
    
    # 训练模型
    result = modeler.train_hmm(test_data)
    
    if result.get('success'):
        print("✅ HMM模型训练成功")
        print(f"状态分析: {result['state_analysis']}")
        
        # 预测当前状态
        prediction = modeler.predict_current_state(test_data, window_size=5)
        print(f"\n当前状态预测: {prediction}")
    else:
        print(f"❌ HMM模型训练失败: {result.get('error')}")


if __name__ == "__main__":
    main()
