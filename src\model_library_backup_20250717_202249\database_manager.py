"""
模型库数据库管理器

负责数据库结构的创建和管理
"""

import sqlite3
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = Path(db_path)
        # 确保数据库目录存在
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
    
    def init_model_library_tables(self):
        """初始化模型库相关表"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建模型基本信息表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS model_library (
                    model_id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    model_type TEXT NOT NULL,
                    version TEXT DEFAULT '1.0.0',
                    author TEXT DEFAULT 'Augment Agent',
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    data_requirements TEXT,
                    feature_engineering TEXT,
                    parameters TEXT,
                    is_active BOOLEAN DEFAULT TRUE
                )
            ''')
            
            # 创建模型状态表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS model_states (
                    model_id TEXT PRIMARY KEY,
                    status TEXT NOT NULL,
                    data_ready BOOLEAN DEFAULT FALSE,
                    features_ready BOOLEAN DEFAULT FALSE,
                    trained BOOLEAN DEFAULT FALSE,
                    up_to_date BOOLEAN DEFAULT FALSE,
                    training_data_size INTEGER DEFAULT 0,
                    last_training_time TEXT,
                    last_check_time TEXT NOT NULL,
                    error_message TEXT,
                    FOREIGN KEY (model_id) REFERENCES model_library(model_id)
                )
            ''')
            
            # 创建预测记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS model_predictions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    model_id TEXT NOT NULL,
                    prediction_time TEXT NOT NULL,
                    target_period INTEGER NOT NULL,
                    prediction_result TEXT NOT NULL,
                    confidence_score REAL DEFAULT 0.0,
                    actual_result TEXT,
                    is_correct BOOLEAN,
                    metadata TEXT,
                    FOREIGN KEY (model_id) REFERENCES model_library(model_id)
                )
            ''')
            
            # 创建性能统计表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS model_performance (
                    model_id TEXT PRIMARY KEY,
                    total_predictions INTEGER DEFAULT 0,
                    correct_predictions INTEGER DEFAULT 0,
                    direct_accuracy REAL DEFAULT 0.0,
                    position_accuracy REAL DEFAULT 0.0,
                    group_accuracy REAL DEFAULT 0.0,
                    average_confidence REAL DEFAULT 0.0,
                    last_updated TEXT NOT NULL,
                    performance_trend TEXT,
                    detailed_metrics TEXT,
                    FOREIGN KEY (model_id) REFERENCES model_library(model_id)
                )
            ''')
            
            # 创建索引以提高查询性能
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_model_predictions_model_id 
                ON model_predictions(model_id)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_model_predictions_time 
                ON model_predictions(prediction_time)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_model_predictions_period 
                ON model_predictions(target_period)
            ''')
            
            conn.commit()
    
    def save_prediction_record(self, prediction_data: Dict[str, Any]):
        """保存预测记录"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO model_predictions 
                (model_id, prediction_time, target_period, prediction_result,
                 confidence_score, metadata)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                prediction_data['model_id'],
                prediction_data['prediction_time'],
                prediction_data['target_period'],
                json.dumps(prediction_data['prediction_result']),
                prediction_data.get('confidence_score', 0.0),
                json.dumps(prediction_data.get('metadata', {}))
            ))
            conn.commit()
            return cursor.lastrowid
    
    def update_prediction_result(self, prediction_id: int, actual_result: str, is_correct: bool):
        """更新预测结果"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE model_predictions 
                SET actual_result = ?, is_correct = ?
                WHERE id = ?
            ''', (actual_result, is_correct, prediction_id))
            conn.commit()
    
    def get_model_predictions(self, model_id: str, limit: int = 100) -> List[Dict[str, Any]]:
        """获取模型预测记录"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM model_predictions 
                WHERE model_id = ?
                ORDER BY prediction_time DESC
                LIMIT ?
            ''', (model_id, limit))
            
            rows = cursor.fetchall()
            columns = [description[0] for description in cursor.description]
            
            predictions = []
            for row in rows:
                prediction = dict(zip(columns, row))
                # 解析JSON字段
                if prediction['prediction_result']:
                    prediction['prediction_result'] = json.loads(prediction['prediction_result'])
                if prediction['metadata']:
                    prediction['metadata'] = json.loads(prediction['metadata'])
                predictions.append(prediction)
            
            return predictions
    
    def update_model_performance(self, model_id: str, performance_data: Dict[str, Any]):
        """更新模型性能统计"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO model_performance
                (model_id, total_predictions, correct_predictions,
                 direct_accuracy, position_accuracy, group_accuracy,
                 average_confidence, last_updated, performance_trend,
                 detailed_metrics)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                model_id,
                performance_data.get('total_predictions', 0),
                performance_data.get('correct_predictions', 0),
                performance_data.get('direct_accuracy', 0.0),
                performance_data.get('position_accuracy', 0.0),
                performance_data.get('group_accuracy', 0.0),
                performance_data.get('average_confidence', 0.0),
                datetime.now().isoformat(),
                json.dumps(performance_data.get('performance_trend', [])),
                json.dumps(performance_data.get('detailed_metrics', {}))
            ))
            conn.commit()
    
    def get_model_performance(self, model_id: str) -> Optional[Dict[str, Any]]:
        """获取模型性能统计"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM model_performance WHERE model_id = ?
            ''', (model_id,))
            
            row = cursor.fetchone()
            if row:
                columns = [description[0] for description in cursor.description]
                performance = dict(zip(columns, row))
                
                # 解析JSON字段
                if performance['performance_trend']:
                    performance['performance_trend'] = json.loads(performance['performance_trend'])
                if performance['detailed_metrics']:
                    performance['detailed_metrics'] = json.loads(performance['detailed_metrics'])
                
                return performance
        
        return None
    
    def get_performance_ranking(self, metric: str = 'direct_accuracy', limit: int = 10) -> List[Dict[str, Any]]:
        """获取性能排行榜"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 验证指标名称
            valid_metrics = ['direct_accuracy', 'position_accuracy', 'group_accuracy', 'average_confidence']
            if metric not in valid_metrics:
                metric = 'direct_accuracy'
            
            cursor.execute(f'''
                SELECT mp.*, ml.name, ml.model_type
                FROM model_performance mp
                JOIN model_library ml ON mp.model_id = ml.model_id
                WHERE ml.is_active = TRUE
                ORDER BY mp.{metric} DESC
                LIMIT ?
            ''', (limit,))
            
            rows = cursor.fetchall()
            columns = [description[0] for description in cursor.description]
            
            ranking = []
            for i, row in enumerate(rows, 1):
                performance = dict(zip(columns, row))
                performance['rank'] = i
                
                # 解析JSON字段
                if performance['performance_trend']:
                    performance['performance_trend'] = json.loads(performance['performance_trend'])
                if performance['detailed_metrics']:
                    performance['detailed_metrics'] = json.loads(performance['detailed_metrics'])
                
                ranking.append(performance)
            
            return ranking
    
    def calculate_model_accuracy(self, model_id: str) -> Dict[str, float]:
        """计算模型准确率"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 获取所有已验证的预测记录
            cursor.execute('''
                SELECT prediction_result, actual_result, is_correct
                FROM model_predictions 
                WHERE model_id = ? AND actual_result IS NOT NULL
            ''', (model_id,))
            
            rows = cursor.fetchall()
            
            if not rows:
                return {
                    'total_predictions': 0,
                    'direct_accuracy': 0.0,
                    'position_accuracy': 0.0,
                    'group_accuracy': 0.0
                }
            
            total = len(rows)
            direct_correct = sum(1 for row in rows if row[2])
            
            # 计算位置准确率和组选准确率
            position_correct = 0
            group_correct = 0
            
            for row in rows:
                try:
                    prediction = json.loads(row[0])
                    actual = row[1]
                    
                    # 这里可以添加更复杂的准确率计算逻辑
                    # 暂时使用简单的计算方式
                    if row[2]:  # 如果直选正确
                        position_correct += 1
                        group_correct += 1
                    
                except (json.JSONDecodeError, KeyError):
                    continue
            
            return {
                'total_predictions': total,
                'direct_accuracy': direct_correct / total if total > 0 else 0.0,
                'position_accuracy': position_correct / total if total > 0 else 0.0,
                'group_accuracy': group_correct / total if total > 0 else 0.0
            }
    
    def cleanup_old_predictions(self, days: int = 90):
        """清理旧的预测记录"""
        cutoff_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        cutoff_date = cutoff_date.replace(day=cutoff_date.day - days)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                DELETE FROM model_predictions 
                WHERE prediction_time < ?
            ''', (cutoff_date.isoformat(),))
            
            deleted_count = cursor.rowcount
            conn.commit()
            
            return deleted_count
