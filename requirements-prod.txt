# 福彩3D预测系统深度交互版 - 生产环境依赖

# Web服务器和ASGI
uvicorn[standard]==0.24.0
gunicorn==21.2.0

# 数据库连接
psycopg2-binary==2.9.9
redis==5.0.1

# 监控和日志
prometheus-client==0.19.0
structlog==23.2.0

# 安全
cryptography==41.0.8
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# 性能优化
cachetools==5.3.2
memory-profiler==0.61.0

# 系统监控
psutil==5.9.6

# 配置管理
python-dotenv==1.0.0
pydantic-settings==2.1.0

# HTTP客户端
httpx==0.25.2

# 任务队列（可选）
celery==5.3.4
kombu==5.3.4

# 消息队列（可选）
pika==1.3.2

# 文件存储（可选）
boto3==1.34.0
minio==7.2.0

# 邮件发送（可选）
emails==0.6.0

# 时间处理
python-dateutil==2.8.2
pytz==2023.3

# 数据验证
cerberus==1.3.5

# API文档增强
python-multipart==0.0.6

# 健康检查
healthcheck==1.3.3

# 限流
slowapi==0.1.9

# CORS处理
fastapi-cors==0.0.6

# 中间件
starlette-prometheus==0.9.0

# 配置文件解析
toml==0.10.2
PyYAML==6.0.1

# 环境变量管理
environs==10.0.0

# 进程管理
supervisor==4.2.5

# 系统信息
distro==1.8.0

# 网络工具
netifaces==0.11.0

# 压缩
zstandard==0.22.0

# 序列化
orjson==3.9.10
msgpack==1.0.7

# 异步支持
aiofiles==23.2.1
aioredis==2.0.1
asyncpg==0.29.0

# 测试工具（生产环境可选）
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0

# 代码质量
flake8==6.1.0
black==23.11.0
isort==5.12.0
mypy==1.7.1

# 文档生成
mkdocs==1.5.3
mkdocs-material==9.4.8

# 备份工具
pg_dump==1.0.0

# 容器化支持
docker==6.1.3

# 云服务SDK（可选）
azure-storage-blob==12.19.0
google-cloud-storage==2.10.0

# 机器学习生产化
mlflow==2.8.1
dvc==3.27.0

# 特征存储
feast==0.35.0

# 模型服务
bentoml==1.1.10

# 实验跟踪
wandb==0.16.0

# 数据漂移检测
evidently==0.4.12

# A/B测试
statsmodels==0.14.0
scipy==1.11.4

# 时间序列
prophet==1.1.5
statsforecast==1.6.0

# 深度学习推理
onnxruntime==1.16.3
tensorrt==8.6.1

# GPU支持（可选）
nvidia-ml-py==12.535.133

# 分布式计算（可选）
dask[complete]==2023.11.0
ray[default]==2.8.0

# 流处理（可选）
kafka-python==2.0.2
apache-beam[gcp]==2.52.0

# 搜索引擎（可选）
elasticsearch==8.11.0

# 图数据库（可选）
neo4j==5.14.1

# 时序数据库（可选）
influxdb-client==1.38.0

# 配置中心（可选）
consul==1.1.0
etcd3==0.12.0

# 服务发现（可选）
python-consul==1.1.0

# 链路追踪（可选）
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0

# 指标收集
statsd==4.0.1

# 告警
alertmanager-webhook-logger==0.1.0

# 负载均衡
haproxy-stats==2.4.0

# SSL/TLS
certifi==2023.11.17

# 网络安全
cryptography==41.0.8
pyopenssl==23.3.0

# 数据加密
fernet==1.0.1

# 访问控制
casbin==1.17.6

# 审计日志
audit-log==1.0.0

# 性能分析
py-spy==0.3.14
line-profiler==4.1.1

# 内存分析
pympler==0.9

# 系统资源监控
resource==0.2.1

# 网络监控
ping3==4.0.4

# 文件系统监控
watchdog==3.0.0

# 进程监控
supervisor==4.2.5

# 容器监控
docker-py==6.1.3

# Kubernetes支持
kubernetes==28.1.0

# 云原生工具
prometheus-client==0.19.0
grafana-api==1.0.3

# 服务网格
istio-client==1.0.0

# 微服务框架
nameko==2.14.1

# 事件驱动
pydantic-events==0.1.0

# 消息传递
pika==1.3.2
kombu==5.3.4

# 工作流引擎
prefect==2.14.11
airflow==2.7.3

# 数据管道
luigi==3.4.0
kedro==0.18.14

# 批处理
apache-beam==2.52.0

# 实时处理
faust-streaming==0.10.15

# 数据湖
delta-lake==0.15.0
iceberg==0.5.1

# 数据仓库
snowflake-connector-python==3.6.0
google-cloud-bigquery==3.13.0

# 数据质量
great-expectations==0.18.5
deequ==0.2.0

# 数据血缘
datahub==0.11.0
apache-atlas==0.1.0

# 元数据管理
amundsen-databuilder==7.4.3

# 数据目录
apache-atlas-client==1.0.0

# 数据治理
collibra-core==1.0.0

# 隐私保护
differential-privacy==1.1.5
anonymizedf==1.0.0

# 合规性
gdpr-tools==1.0.0
ccpa-compliance==1.0.0
