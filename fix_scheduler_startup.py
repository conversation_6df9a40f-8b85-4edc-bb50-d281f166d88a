#!/usr/bin/env python3
"""
修复调度器启动问题的脚本
解决"启动失败: None"错误
"""

import os
import sys
import subprocess
import time
import json
from pathlib import Path

def check_scheduler_dependencies():
    """检查调度器依赖"""
    print("🔍 检查调度器依赖...")
    
    try:
        import apscheduler
        print(f"✅ APScheduler版本: {apscheduler.__version__}")
    except ImportError:
        print("❌ APScheduler未安装")
        return False
    
    # 检查调度器脚本
    script_path = Path("scripts/start_scheduler.py")
    if script_path.exists():
        print(f"✅ 调度器脚本存在: {script_path}")
    else:
        print(f"❌ 调度器脚本不存在: {script_path}")
        return False
    
    # 检查配置文件
    config_path = Path("scheduler_config.json")
    if not config_path.exists():
        print("⚠️ 配置文件不存在，创建默认配置...")
        create_default_config()
    else:
        print(f"✅ 配置文件存在: {config_path}")
    
    return True

def create_default_config():
    """创建默认调度器配置"""
    default_config = {
        "scheduler": {
            "timezone": "Asia/Shanghai",
            "job_defaults": {
                "coalesce": True,
                "max_instances": 1,
                "misfire_grace_time": 300
            }
        },
        "jobs": [
            {
                "id": "data_update",
                "name": "定时数据更新",
                "func": "data_update_job",
                "trigger": "cron",
                "hour": 21,
                "minute": 30,
                "enabled": True,
                "description": "每天21:30执行数据更新"
            },
            {
                "id": "file_cleanup",
                "name": "定时文件清理",
                "func": "file_cleanup_job",
                "trigger": "cron",
                "hour": 2,
                "minute": 0,
                "enabled": True,
                "description": "每天2:00执行文件清理"
            }
        ],
        "logging": {
            "level": "INFO",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "file": "data/logs/scheduler_{date}.log"
        }
    }
    
    with open("scheduler_config.json", "w", encoding="utf-8") as f:
        json.dump(default_config, f, indent=2, ensure_ascii=False)
    
    print("✅ 默认配置文件已创建")

def test_scheduler_startup():
    """测试调度器启动"""
    print("\n🧪 测试调度器启动...")
    
    try:
        # 测试模式启动
        result = subprocess.run(
            [sys.executable, "scripts/start_scheduler.py", "--test"],
            capture_output=True,
            text=True,
            timeout=30,
            cwd=os.getcwd()
        )
        
        if result.returncode == 0:
            print("✅ 调度器测试模式启动成功")
            return True
        else:
            print(f"❌ 调度器测试失败:")
            print(f"   返回码: {result.returncode}")
            if result.stdout:
                print(f"   标准输出: {result.stdout[-500:]}")  # 只显示最后500字符
            if result.stderr:
                print(f"   错误输出: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 调度器测试超时")
        return False
    except Exception as e:
        print(f"❌ 调度器测试异常: {e}")
        return False

def fix_permissions():
    """修复文件权限"""
    print("\n🔧 检查和修复文件权限...")
    
    # 需要检查的路径
    paths_to_check = [
        "scripts/start_scheduler.py",
        "data",
        "data/logs",
        "scheduler_config.json"
    ]
    
    for path_str in paths_to_check:
        path = Path(path_str)
        if path.exists():
            try:
                # 在Windows上，检查文件是否可读写
                if path.is_file():
                    with open(path, 'r', encoding='utf-8') as f:
                        f.read(1)  # 尝试读取
                    print(f"✅ {path} 权限正常")
                else:
                    # 目录权限检查
                    list(path.iterdir())  # 尝试列出目录
                    print(f"✅ {path} 目录权限正常")
            except PermissionError:
                print(f"❌ {path} 权限不足")
                return False
            except Exception as e:
                print(f"⚠️ {path} 检查异常: {e}")
        else:
            if path_str.endswith('.py'):
                print(f"❌ {path} 文件不存在")
                return False
            else:
                # 创建目录
                try:
                    path.mkdir(parents=True, exist_ok=True)
                    print(f"✅ 创建目录: {path}")
                except Exception as e:
                    print(f"❌ 无法创建目录 {path}: {e}")
                    return False
    
    return True

def create_startup_wrapper():
    """创建启动包装脚本"""
    print("\n📝 创建启动包装脚本...")
    
    wrapper_content = '''#!/usr/bin/env python3
"""
调度器启动包装脚本
提供更好的错误处理和日志记录
"""

import sys
import os
import subprocess
import time
from pathlib import Path

def main():
    """主函数"""
    print("🚀 启动福彩3D调度器...")
    
    # 设置工作目录
    script_dir = Path(__file__).parent
    project_dir = script_dir.parent
    os.chdir(project_dir)
    
    try:
        # 启动调度器
        process = subprocess.Popen(
            [sys.executable, "scripts/start_scheduler.py", "--daemon"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=project_dir
        )
        
        # 等待启动
        time.sleep(5)
        
        # 检查进程状态
        if process.poll() is None:
            print("✅ 调度器启动成功")
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ 调度器启动失败:")
            if stdout:
                print(f"标准输出: {stdout}")
            if stderr:
                print(f"错误输出: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 启动异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
'''
    
    wrapper_path = Path("scripts/scheduler_wrapper.py")
    with open(wrapper_path, "w", encoding="utf-8") as f:
        f.write(wrapper_content)
    
    print(f"✅ 启动包装脚本已创建: {wrapper_path}")

def main():
    """主修复函数"""
    print("🔧 福彩3D调度器启动问题修复工具")
    print("=" * 50)
    
    # 1. 检查依赖
    if not check_scheduler_dependencies():
        print("\n❌ 依赖检查失败，请先安装必要的依赖")
        return False
    
    # 2. 修复权限
    if not fix_permissions():
        print("\n❌ 权限修复失败，请以管理员身份运行")
        return False
    
    # 3. 测试启动
    if not test_scheduler_startup():
        print("\n❌ 调度器测试失败")
        return False
    
    # 4. 创建包装脚本
    create_startup_wrapper()
    
    print("\n" + "=" * 50)
    print("🎉 调度器启动问题修复完成！")
    print("\n💡 使用建议:")
    print("1. 在Streamlit界面中点击'🚀 启动调度器'")
    print("2. 如果仍然失败，请点击'🧪 测试调度器'查看详细信息")
    print("3. 可以手动运行: python scripts/scheduler_wrapper.py")
    print("4. 查看日志文件: data/logs/scheduler_*.log")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        input("\n按Enter键退出...")
    sys.exit(0 if success else 1)
