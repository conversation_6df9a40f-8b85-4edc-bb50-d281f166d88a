#!/usr/bin/env python3
"""
检查生产版服务状态
"""

import requests
import time

def check_production_services():
    print("🔍 检查生产版服务状态...")
    
    # 检查FastAPI
    try:
        response = requests.get("http://127.0.0.1:8888/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ FastAPI生产版服务 (127.0.0.1:8888): 正常运行")
            print(f"   📊 数据库记录: {data['database_records']}")
            print(f"   📅 数据范围: {data['date_range']}")
            print(f"   📖 API文档: http://127.0.0.1:8888/docs")
        else:
            print(f"❌ FastAPI生产版服务 (127.0.0.1:8888): 状态码 {response.status_code}")
    except Exception as e:
        print(f"❌ FastAPI生产版服务 (127.0.0.1:8888): 连接失败 - {e}")
    
    # 检查Streamlit
    try:
        response = requests.get("http://127.0.0.1:8501", timeout=5)
        if response.status_code == 200:
            print("✅ Streamlit完整版应用 (127.0.0.1:8501): 正常运行")
        else:
            print(f"❌ Streamlit完整版应用 (127.0.0.1:8501): 状态码 {response.status_code}")
    except Exception as e:
        print(f"❌ Streamlit完整版应用 (127.0.0.1:8501): 连接失败 - {e}")
    
    print(f"\n🎯 生产版服务状态:")
    print(f"📱 Streamlit完整版: http://127.0.0.1:8501")
    print(f"🔗 FastAPI服务: http://127.0.0.1:8888")
    print(f"📖 API文档: http://127.0.0.1:8888/docs")
    print(f"✨ 演示版已删除，只保留完整版")
    print(f"🔧 API绑定127.0.0.1，确保本地访问")

if __name__ == "__main__":
    check_production_services()
