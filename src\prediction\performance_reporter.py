"""
性能报告生成器

自动生成详细的模型评估报告和可视化图表
"""

import json
import os
from collections import defaultdict
from datetime import datetime
from typing import Any, Dict, List, Optional

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns

from .markov_validator import MarkovModelValidator
from .model_validation import MarkovCrossValidator


class PerformanceReporter:
    """性能报告生成器"""
    
    def __init__(self, db_path: str = None):
        """
        初始化报告生成器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path or os.path.join('data', 'lottery.db')
        self.validator = MarkovModelValidator(db_path)
        self.cross_validator = self.validator.cross_validator
        
        # 报告输出目录
        self.report_dir = os.path.join('data', 'performance_reports')
        os.makedirs(self.report_dir, exist_ok=True)
        
        # 设置matplotlib中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 设置seaborn样式
        sns.set_style("whitegrid")
        sns.set_palette("husl")
    
    def generate_comprehensive_report(self, 
                                    validation_results: Dict[str, Any],
                                    report_name: str = None) -> str:
        """
        生成综合性能报告
        
        Args:
            validation_results: 验证结果
            report_name: 报告名称
            
        Returns:
            报告文件路径
        """
        if report_name is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_name = f"performance_report_{timestamp}"
        
        print(f"📊 生成综合性能报告: {report_name}")
        
        # 创建报告目录
        report_path = os.path.join(self.report_dir, report_name)
        os.makedirs(report_path, exist_ok=True)
        
        # 生成各种图表
        charts_generated = []
        
        # 1. 准确率分析图
        accuracy_chart = self._generate_accuracy_chart(validation_results, report_path)
        if accuracy_chart:
            charts_generated.append(accuracy_chart)
        
        # 2. 多样性分析图
        diversity_chart = self._generate_diversity_chart(validation_results, report_path)
        if diversity_chart:
            charts_generated.append(diversity_chart)
        
        # 3. AIC/BIC比较图
        aic_bic_chart = self._generate_aic_bic_chart(validation_results, report_path)
        if aic_bic_chart:
            charts_generated.append(aic_bic_chart)
        
        # 4. 交叉验证结果图
        cv_chart = self._generate_cross_validation_chart(validation_results, report_path)
        if cv_chart:
            charts_generated.append(cv_chart)
        
        # 5. 生成HTML报告
        html_report = self._generate_html_report(validation_results, charts_generated, report_path)
        
        # 6. 生成JSON摘要
        json_summary = self._generate_json_summary(validation_results, report_path)
        
        print(f"✅ 报告生成完成: {report_path}")
        print(f"   HTML报告: {html_report}")
        print(f"   JSON摘要: {json_summary}")
        print(f"   图表数量: {len(charts_generated)}")
        
        return report_path
    
    def _generate_accuracy_chart(self, validation_results: Dict[str, Any], output_dir: str) -> Optional[str]:
        """生成准确率分析图"""
        try:
            # 提取准确率数据
            fold_results = validation_results.get('fold_results', [])
            if not fold_results:
                return None
            
            # 准备数据
            fold_indices = [f"折{i+1}" for i in range(len(fold_results))]
            exact_match_rates = [fold.get('accuracy_metrics', {}).get('exact_match', 0) for fold in fold_results]
            digit_accuracies = [fold.get('accuracy_metrics', {}).get('digit_accuracy', 0) for fold in fold_results]
            
            # 创建图表
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
            
            # 完全匹配率
            ax1.bar(fold_indices, exact_match_rates, color='skyblue', alpha=0.7)
            ax1.set_title('各折完全匹配准确率')
            ax1.set_ylabel('准确率')
            ax1.set_ylim(0, max(max(exact_match_rates) * 1.2, 0.01))
            
            # 添加数值标签
            for i, v in enumerate(exact_match_rates):
                ax1.text(i, v + max(exact_match_rates) * 0.01, f'{v:.4f}', ha='center')
            
            # 数字准确率
            ax2.bar(fold_indices, digit_accuracies, color='lightcoral', alpha=0.7)
            ax2.set_title('各折数字准确率')
            ax2.set_ylabel('准确率')
            ax2.set_ylim(0, max(max(digit_accuracies) * 1.2, 0.1))
            
            # 添加数值标签
            for i, v in enumerate(digit_accuracies):
                ax2.text(i, v + max(digit_accuracies) * 0.01, f'{v:.4f}', ha='center')
            
            plt.tight_layout()
            
            # 保存图表
            chart_path = os.path.join(output_dir, 'accuracy_analysis.png')
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return chart_path
            
        except Exception as e:
            print(f"生成准确率图表失败: {e}")
            return None
    
    def _generate_diversity_chart(self, validation_results: Dict[str, Any], output_dir: str) -> Optional[str]:
        """生成多样性分析图"""
        try:
            # 提取多样性数据
            fold_results = validation_results.get('fold_results', [])
            if not fold_results:
                return None
            
            # 准备数据
            fold_indices = [f"折{i+1}" for i in range(len(fold_results))]
            simpson_indices = [fold.get('diversity_metrics', {}).get('simpson_diversity', 0) for fold in fold_results]
            unique_ratios = [fold.get('diversity_metrics', {}).get('unique_ratio', 0) for fold in fold_results]
            entropies = [fold.get('diversity_metrics', {}).get('entropy', 0) for fold in fold_results]
            
            # 创建图表
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
            
            # 辛普森多样性指数
            ax1.bar(fold_indices, simpson_indices, color='green', alpha=0.7)
            ax1.set_title('辛普森多样性指数')
            ax1.set_ylabel('多样性指数')
            ax1.set_ylim(0, 1)
            ax1.axhline(y=0.9, color='red', linestyle='--', label='目标线(0.9)')
            ax1.legend()
            
            # 唯一预测比率
            ax2.bar(fold_indices, unique_ratios, color='orange', alpha=0.7)
            ax2.set_title('唯一预测比率')
            ax2.set_ylabel('比率')
            ax2.set_ylim(0, 1)
            
            # 信息熵
            ax3.bar(fold_indices, entropies, color='purple', alpha=0.7)
            ax3.set_title('信息熵')
            ax3.set_ylabel('熵值')
            
            # 多样性指标对比
            x = np.arange(len(fold_indices))
            width = 0.25
            
            ax4.bar(x - width, simpson_indices, width, label='辛普森指数', alpha=0.7)
            ax4.bar(x, unique_ratios, width, label='唯一比率', alpha=0.7)
            ax4.bar(x + width, [e/max(entropies) if max(entropies) > 0 else 0 for e in entropies], 
                   width, label='标准化熵', alpha=0.7)
            
            ax4.set_title('多样性指标对比')
            ax4.set_ylabel('标准化值')
            ax4.set_xticks(x)
            ax4.set_xticklabels(fold_indices)
            ax4.legend()
            
            plt.tight_layout()
            
            # 保存图表
            chart_path = os.path.join(output_dir, 'diversity_analysis.png')
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return chart_path
            
        except Exception as e:
            print(f"生成多样性图表失败: {e}")
            return None
    
    def _generate_aic_bic_chart(self, validation_results: Dict[str, Any], output_dir: str) -> Optional[str]:
        """生成AIC/BIC比较图"""
        try:
            # 提取AIC/BIC数据
            fold_results = validation_results.get('fold_results', [])
            if not fold_results:
                return None
            
            # 准备数据
            fold_indices = [f"折{i+1}" for i in range(len(fold_results))]
            aic_values = [fold.get('aic_bic', {}).get('aic', 0) for fold in fold_results]
            bic_values = [fold.get('aic_bic', {}).get('bic', 0) for fold in fold_results]
            
            # 创建图表
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
            
            # AIC值
            ax1.plot(fold_indices, aic_values, 'o-', color='blue', linewidth=2, markersize=8)
            ax1.set_title('AIC值变化')
            ax1.set_ylabel('AIC')
            ax1.grid(True, alpha=0.3)
            
            # BIC值
            ax2.plot(fold_indices, bic_values, 'o-', color='red', linewidth=2, markersize=8)
            ax2.set_title('BIC值变化')
            ax2.set_ylabel('BIC')
            ax2.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # 保存图表
            chart_path = os.path.join(output_dir, 'aic_bic_analysis.png')
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return chart_path
            
        except Exception as e:
            print(f"生成AIC/BIC图表失败: {e}")
            return None
    
    def _generate_cross_validation_chart(self, validation_results: Dict[str, Any], output_dir: str) -> Optional[str]:
        """生成交叉验证结果图"""
        try:
            # 提取交叉验证数据
            fold_results = validation_results.get('fold_results', [])
            if not fold_results:
                return None
            
            # 准备数据
            metrics_data = defaultdict(list)
            fold_names = []
            
            for i, fold in enumerate(fold_results):
                fold_names.append(f"折{i+1}")
                metrics_data['准确率'].append(fold.get('accuracy_metrics', {}).get('exact_match', 0))
                metrics_data['多样性'].append(fold.get('diversity_metrics', {}).get('simpson_diversity', 0))
                metrics_data['预测数量'].append(fold.get('predictions_count', 0))
            
            # 创建雷达图
            fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
            
            # 设置角度
            angles = np.linspace(0, 2 * np.pi, len(fold_names), endpoint=False).tolist()
            angles += angles[:1]  # 闭合图形
            
            # 绘制每个指标
            colors = ['blue', 'red', 'green']
            for i, (metric, values) in enumerate(metrics_data.items()):
                if metric == '预测数量':
                    # 标准化预测数量
                    max_val = max(values) if values else 1
                    values = [v / max_val for v in values]
                
                values += values[:1]  # 闭合图形
                ax.plot(angles, values, 'o-', linewidth=2, label=metric, color=colors[i])
                ax.fill(angles, values, alpha=0.25, color=colors[i])
            
            # 设置标签
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(fold_names)
            ax.set_ylim(0, 1)
            ax.set_title('交叉验证结果雷达图', size=16, y=1.1)
            ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
            
            plt.tight_layout()
            
            # 保存图表
            chart_path = os.path.join(output_dir, 'cross_validation_radar.png')
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return chart_path
            
        except Exception as e:
            print(f"生成交叉验证图表失败: {e}")
            return None

    def _generate_html_report(self, validation_results: Dict[str, Any],
                            chart_paths: List[str], output_dir: str) -> str:
        """生成HTML报告"""
        try:
            # 提取关键指标
            overall_results = validation_results.get('overall_results', {})
            model_params = validation_results.get('model_params', {})
            validation_params = validation_results.get('validation_params', {})

            # 生成HTML内容
            html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>马尔可夫模型性能报告</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f8ff; padding: 20px; border-radius: 10px; }}
        .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
        .metric {{ display: inline-block; margin: 10px; padding: 10px; background-color: #f9f9f9; border-radius: 5px; }}
        .chart {{ text-align: center; margin: 20px 0; }}
        .chart img {{ max-width: 100%; height: auto; border: 1px solid #ddd; }}
        table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .success {{ color: #28a745; }}
        .warning {{ color: #ffc107; }}
        .danger {{ color: #dc3545; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 马尔可夫模型性能报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>

    <div class="section">
        <h2>📊 总体性能指标</h2>
        <div class="metric">
            <strong>总预测数量:</strong> {overall_results.get('total_predictions', 0)}
        </div>
        <div class="metric">
            <strong>完全匹配准确率:</strong>
            <span class="{'success' if overall_results.get('accuracy_metrics', {}).get('exact_match', 0) > 0.001 else 'warning'}">
                {overall_results.get('accuracy_metrics', {}).get('exact_match', 0):.4f}
            </span>
        </div>
        <div class="metric">
            <strong>数字准确率:</strong> {overall_results.get('accuracy_metrics', {}).get('digit_accuracy', 0):.4f}
        </div>
        <div class="metric">
            <strong>辛普森多样性指数:</strong>
            <span class="{'success' if overall_results.get('diversity_metrics', {}).get('simpson_diversity', 0) >= 0.9 else 'warning'}">
                {overall_results.get('diversity_metrics', {}).get('simpson_diversity', 0):.4f}
            </span>
        </div>
        <div class="metric">
            <strong>AIC:</strong> {overall_results.get('aic_bic', {}).get('aic', 0):.2f}
        </div>
        <div class="metric">
            <strong>BIC:</strong> {overall_results.get('aic_bic', {}).get('bic', 0):.2f}
        </div>
    </div>

    <div class="section">
        <h2>⚙️ 模型参数</h2>
        <table>
            <tr><th>参数</th><th>值</th></tr>
            <tr><td>转移矩阵窗口大小</td><td>{model_params.get('transition_window_size', 'N/A')}</td></tr>
            <tr><td>概率计算窗口大小</td><td>{model_params.get('probability_window_size', 'N/A')}</td></tr>
            <tr><td>拉普拉斯平滑参数</td><td>{model_params.get('smoothing_alpha', 'N/A')}</td></tr>
        </table>
    </div>

    <div class="section">
        <h2>🔍 验证参数</h2>
        <table>
            <tr><th>参数</th><th>值</th></tr>
            <tr><td>交叉验证折数</td><td>{validation_params.get('k_folds', 'N/A')}</td></tr>
            <tr><td>数据限制</td><td>{validation_params.get('data_limit', 'N/A')}</td></tr>
        </table>
    </div>
"""

            # 添加图表
            if chart_paths:
                html_content += """
    <div class="section">
        <h2>📈 性能分析图表</h2>
"""
                for chart_path in chart_paths:
                    chart_name = os.path.basename(chart_path)
                    html_content += f"""
        <div class="chart">
            <h3>{chart_name.replace('_', ' ').replace('.png', '').title()}</h3>
            <img src="{chart_name}" alt="{chart_name}">
        </div>
"""
                html_content += "    </div>"

            # 添加详细结果表格
            fold_results = validation_results.get('fold_results', [])
            if fold_results:
                html_content += """
    <div class="section">
        <h2>📋 详细验证结果</h2>
        <table>
            <tr>
                <th>验证折</th>
                <th>训练集大小</th>
                <th>验证集大小</th>
                <th>预测数量</th>
                <th>准确率</th>
                <th>多样性</th>
                <th>AIC</th>
                <th>BIC</th>
            </tr>
"""
                for i, fold in enumerate(fold_results):
                    accuracy = fold.get('accuracy_metrics', {}).get('exact_match', 0)
                    diversity = fold.get('diversity_metrics', {}).get('simpson_diversity', 0)
                    aic = fold.get('aic_bic', {}).get('aic', 0)
                    bic = fold.get('aic_bic', {}).get('bic', 0)

                    html_content += f"""
            <tr>
                <td>折 {i+1}</td>
                <td>{fold.get('train_size', 0)}</td>
                <td>{fold.get('val_size', 0)}</td>
                <td>{fold.get('predictions_count', 0)}</td>
                <td>{accuracy:.4f}</td>
                <td>{diversity:.4f}</td>
                <td>{aic:.2f}</td>
                <td>{bic:.2f}</td>
            </tr>
"""
                html_content += """
        </table>
    </div>
"""

            html_content += """
    <div class="section">
        <h2>📝 结论与建议</h2>
        <ul>
"""

            # 生成结论
            accuracy = overall_results.get('accuracy_metrics', {}).get('exact_match', 0)
            diversity = overall_results.get('diversity_metrics', {}).get('simpson_diversity', 0)

            if accuracy > 0.001:
                html_content += f"<li class='success'>✅ 模型准确率为 {accuracy:.4f}，表现良好</li>"
            else:
                html_content += f"<li class='warning'>⚠️ 模型准确率较低 ({accuracy:.4f})，建议优化参数</li>"

            if diversity >= 0.9:
                html_content += f"<li class='success'>✅ 预测多样性优秀 ({diversity:.4f})，避免了过度拟合</li>"
            elif diversity >= 0.7:
                html_content += f"<li class='warning'>⚠️ 预测多样性良好 ({diversity:.4f})，可进一步优化</li>"
            else:
                html_content += f"<li class='danger'>❌ 预测多样性不足 ({diversity:.4f})，需要改进模型</li>"

            html_content += """
        </ul>
    </div>

    <div class="section">
        <h2>🔗 相关文件</h2>
        <ul>
            <li>验证报告JSON: validation_summary.json</li>
            <li>详细验证数据: 请查看data/validation_reports目录</li>
        </ul>
    </div>

</body>
</html>
"""

            # 保存HTML文件
            html_path = os.path.join(output_dir, 'performance_report.html')
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            return html_path

        except Exception as e:
            print(f"生成HTML报告失败: {e}")
            return ""

    def _generate_json_summary(self, validation_results: Dict[str, Any], output_dir: str) -> str:
        """生成JSON摘要"""
        try:
            # 创建摘要数据
            summary = {
                'report_metadata': {
                    'generated_at': datetime.now().isoformat(),
                    'generator': 'PerformanceReporter v1.0',
                    'database_path': self.db_path
                },
                'model_performance': validation_results.get('overall_results', {}),
                'model_parameters': validation_results.get('model_params', {}),
                'validation_parameters': validation_results.get('validation_params', {}),
                'fold_count': len(validation_results.get('fold_results', [])),
                'recommendations': self._generate_recommendations(validation_results)
            }

            # 保存JSON文件
            json_path = os.path.join(output_dir, 'validation_summary.json')
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)

            return json_path

        except Exception as e:
            print(f"生成JSON摘要失败: {e}")
            return ""

    def _generate_recommendations(self, validation_results: Dict[str, Any]) -> List[str]:
        """生成优化建议"""
        recommendations = []

        overall_results = validation_results.get('overall_results', {})
        accuracy = overall_results.get('accuracy_metrics', {}).get('exact_match', 0)
        diversity = overall_results.get('diversity_metrics', {}).get('simpson_diversity', 0)

        # 准确率建议
        if accuracy < 0.001:
            recommendations.append("建议增加数据窗口大小或调整平滑参数以提高准确率")
        elif accuracy < 0.005:
            recommendations.append("准确率有提升空间，可尝试二阶马尔可夫链")

        # 多样性建议
        if diversity < 0.7:
            recommendations.append("预测多样性不足，建议增加随机性或调整温度参数")
        elif diversity < 0.9:
            recommendations.append("多样性良好，可微调参数进一步优化")

        # 模型复杂度建议
        aic = overall_results.get('aic_bic', {}).get('aic', 0)
        bic = overall_results.get('aic_bic', {}).get('bic', 0)

        if aic > 10000:
            recommendations.append("AIC值较高，考虑简化模型或增加数据量")

        if not recommendations:
            recommendations.append("模型性能良好，可继续使用当前参数")

        return recommendations


if __name__ == "__main__":
    # 测试代码
    reporter = PerformanceReporter()

    # 创建测试验证结果
    test_results = {
        'overall_results': {
            'total_predictions': 100,
            'accuracy_metrics': {'exact_match': 0.02, 'digit_accuracy': 0.25},
            'diversity_metrics': {'simpson_diversity': 0.95, 'unique_ratio': 0.8},
            'aic_bic': {'aic': 1500.0, 'bic': 1600.0}
        },
        'model_params': {
            'transition_window_size': 1000,
            'probability_window_size': 500,
            'smoothing_alpha': 1.0
        },
        'validation_params': {
            'k_folds': 3,
            'data_limit': 1000
        },
        'fold_results': [
            {
                'fold_idx': 0,
                'train_size': 600,
                'val_size': 200,
                'predictions_count': 50,
                'accuracy_metrics': {'exact_match': 0.02, 'digit_accuracy': 0.24},
                'diversity_metrics': {'simpson_diversity': 0.94, 'unique_ratio': 0.8},
                'aic_bic': {'aic': 750.0, 'bic': 800.0}
            }
        ]
    }

    # 生成报告
    report_path = reporter.generate_comprehensive_report(test_results, "test_report")
    print(f"测试报告生成完成: {report_path}")
