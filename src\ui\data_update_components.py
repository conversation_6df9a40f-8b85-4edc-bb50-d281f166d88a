"""
实时数据更新组件
提供自动数据更新、手动刷新、增量更新等功能

修复历史：
- 2025-07-16: 修复数据不一致问题
  * 问题：数据库约束违反错误 "NOT NULL constraint failed: lottery_records.sum_value"
  * 原因：表结构不完整，缺少计算字段
  * 解决：统一表结构，添加数据验证和计算逻辑
  * 结果：数据库记录数从8341成功增加到8343
"""

import os
import sqlite3
from datetime import datetime
from typing import Any, Dict, List, Optional

import pandas as pd
import requests
import streamlit as st


class DataUpdateManager:
    """数据更新管理器"""
    
    def __init__(self, db_path: Optional[str] = None):
        self.db_path = db_path or os.path.join('data', 'lottery.db')
        self.data_source_url = "https://data.17500.cn/3d_asc.txt"
        self.last_update_time = None
        self.update_status = "未更新"
        
    def check_data_freshness(self) -> Dict[str, Any]:
        """检查数据新鲜度"""
        try:
            if not os.path.exists(self.db_path):
                return {
                    'status': 'no_database',
                    'message': '数据库文件不存在',
                    'last_update': None,
                    'record_count': 0,
                    'freshness': 'unknown'
                }
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取最新记录
            cursor.execute("""
                SELECT MAX(date), COUNT(*) 
                FROM lottery_records 
                WHERE numbers IS NOT NULL AND numbers != ''
            """)
            
            result = cursor.fetchone()
            latest_date = result[0] if result[0] else None
            record_count = result[1] if result[1] else 0
            
            conn.close()
            
            # 计算数据新鲜度
            if latest_date:
                latest_datetime = datetime.strptime(latest_date, '%Y-%m-%d')
                days_old = (datetime.now() - latest_datetime).days
                
                if days_old == 0:
                    freshness = 'very_fresh'
                elif days_old <= 1:
                    freshness = 'fresh'
                elif days_old <= 3:
                    freshness = 'moderate'
                elif days_old <= 7:
                    freshness = 'stale'
                else:
                    freshness = 'very_stale'
            else:
                freshness = 'no_data'
            
            return {
                'status': 'success',
                'message': '数据检查完成',
                'last_update': latest_date,
                'record_count': record_count,
                'freshness': freshness,
                'days_old': days_old if latest_date else None
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'数据检查失败: {str(e)}',
                'last_update': None,
                'record_count': 0,
                'freshness': 'error'
            }
    
    def fetch_latest_data(self) -> Dict[str, Any]:
        """获取最新数据"""
        try:
            st.info("正在从数据源获取最新数据...")
            
            # 发送请求获取数据，添加更完整的headers避免被限制
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Referer': 'https://data.17500.cn/'
            }

            # 添加延迟避免请求过于频繁
            import time
            time.sleep(2)

            response = requests.get(self.data_source_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            # 解析数据
            lines = response.text.strip().split('\n')
            
            if not lines:
                return {
                    'status': 'error',
                    'message': '数据源返回空数据',
                    'new_records': 0
                }
            
            # 解析每行数据
            new_records = []
            for line in lines:
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 13:  # 确保有足够的字段
                        # 数据字段映射说明（基于17字段格式）：
                        # [0] 期号, [1] 日期, [2-4] 开奖号码, [5-7] 试机号码
                        # [8] 开奖机器, [9] 试机机器, [10] 销售额
                        # [11] 空字段, [12] 直选奖金(1040), [13] 空字段
                        # [14] 组三奖金(346), [15] 空字段, [16] 组六奖金(173)
                        # 修复日期：2025-07-16，修复字段映射错误问题

                        # 组合开奖号码和试机号码（原始数据是分开的数字）
                        numbers = parts[2] + parts[3] + parts[4]  # 百位+十位+个位
                        trial_numbers = parts[5] + parts[6] + parts[7]  # 试机号百位+十位+个位

                        record = {
                            'period': parts[0],
                            'date': parts[1],
                            'numbers': numbers,
                            'trial_numbers': trial_numbers,
                            'draw_machine': int(parts[8]) if parts[8].isdigit() else 1,
                            'trial_machine': int(parts[9]) if parts[9].isdigit() else 1,
                            'sales_amount': int(parts[10]) if parts[10].isdigit() else 0,
                            # 奖金字段映射（修复后的正确位置）
                            'direct_prize': int(parts[12]) if parts[12].isdigit() else 1040,  # 直选奖金：parts[12]
                            'group3_prize': int(parts[14]) if len(parts) > 14 and parts[14].isdigit() else 346,  # 组三奖金：parts[14]
                            'group6_prize': int(parts[16]) if len(parts) > 16 and parts[16].isdigit() else 173   # 组六奖金：parts[16]
                        }
                        new_records.append(record)
            
            return {
                'status': 'success',
                'message': f'成功获取 {len(new_records)} 条记录',
                'new_records': len(new_records),
                'data': new_records
            }
            
        except requests.RequestException as e:
            error_msg = str(e)
            if '429' in error_msg or 'Too Many Requests' in error_msg:
                return {
                    'status': 'error',
                    'message': '数据源请求过于频繁，请稍后再试。建议等待1-2分钟后重新尝试。',
                    'new_records': 0
                }
            elif '403' in error_msg or 'Forbidden' in error_msg:
                return {
                    'status': 'error',
                    'message': '数据源访问被拒绝，可能需要更换数据源或稍后重试。',
                    'new_records': 0
                }
            else:
                return {
                    'status': 'error',
                    'message': f'网络请求失败: {error_msg}',
                    'new_records': 0
                }
        except Exception as e:
            return {
                'status': 'error',
                'message': f'数据获取失败: {str(e)}',
                'new_records': 0
            }
    
    def validate_record(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证单条记录的数据完整性

        修复说明：
        - 添加了完整的字段验证逻辑
        - 检查必需字段的存在性和格式
        - 验证数值字段的合理性
        - 返回详细的验证结果和错误信息

        Args:
            record: 待验证的记录字典

        Returns:
            包含验证结果的字典：
            - is_valid: 是否通过验证
            - errors: 错误信息列表
            - record: 原始记录
        """
        errors = []

        # 检查必需字段
        required_fields = ['period', 'date', 'numbers', 'trial_numbers',
                          'draw_machine', 'trial_machine', 'sales_amount',
                          'direct_prize', 'group3_prize', 'group6_prize']

        for field in required_fields:
            if field not in record or record[field] is None:
                errors.append(f"缺少必需字段: {field}")

        # 验证期号格式
        period = record.get('period', '')
        if not isinstance(period, str) or len(period) != 7 or not period.isdigit():
            errors.append(f"期号格式错误: {period}")

        # 验证号码格式
        numbers = record.get('numbers', '')
        if not isinstance(numbers, str) or len(numbers) != 3 or not numbers.isdigit():
            errors.append(f"开奖号码格式错误: {numbers}")

        trial_numbers = record.get('trial_numbers', '')
        if not isinstance(trial_numbers, str) or len(trial_numbers) != 3 or not trial_numbers.isdigit():
            errors.append(f"试机号码格式错误: {trial_numbers}")

        # 验证数值字段
        numeric_fields = ['draw_machine', 'trial_machine', 'sales_amount',
                         'direct_prize', 'group3_prize', 'group6_prize']

        for field in numeric_fields:
            value = record.get(field)
            if value is not None:
                try:
                    int_value = int(value)
                    if int_value < 0:
                        errors.append(f"{field}不能为负数: {int_value}")
                except (ValueError, TypeError):
                    errors.append(f"{field}必须是数字: {value}")

        return {
            'is_valid': len(errors) == 0,
            'errors': errors,
            'record': record
        }

    def calculate_derived_fields(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """
        计算衍生字段（和值、跨度等）

        修复说明：
        - 新增方法，解决数据库约束违反问题
        - 自动计算sum_value、trial_sum_value、span_value、trial_span_value
        - 为unknown_field字段提供默认值
        - 包含错误处理，确保计算失败时有默认值

        Args:
            record: 原始记录字典

        Returns:
            包含计算字段的增强记录字典
        """
        try:
            # 计算正式开奖号码的和值和跨度
            numbers = record.get('numbers', '000')
            if len(numbers) == 3 and numbers.isdigit():
                number_list = [int(d) for d in numbers]
                sum_value = sum(number_list)
                span_value = max(number_list) - min(number_list)
            else:
                sum_value = 0
                span_value = 0

            # 计算试机号码的和值和跨度
            trial_numbers = record.get('trial_numbers', '000')
            if len(trial_numbers) == 3 and trial_numbers.isdigit():
                trial_number_list = [int(d) for d in trial_numbers]
                trial_sum_value = sum(trial_number_list)
                trial_span_value = max(trial_number_list) - min(trial_number_list)
            else:
                trial_sum_value = 0
                trial_span_value = 0

            # 返回包含计算字段的记录
            enhanced_record = record.copy()
            enhanced_record.update({
                'sum_value': sum_value,
                'trial_sum_value': trial_sum_value,
                'span_value': span_value,
                'trial_span_value': trial_span_value,
                'unknown_field1': record.get('unknown_field1', 0),
                'unknown_field2': record.get('unknown_field2', 0),
                'unknown_field3': record.get('unknown_field3', 0)
            })

            return enhanced_record

        except Exception as e:
            # 如果计算失败，返回默认值
            enhanced_record = record.copy()
            enhanced_record.update({
                'sum_value': 0,
                'trial_sum_value': 0,
                'span_value': 0,
                'trial_span_value': 0,
                'unknown_field1': 0,
                'unknown_field2': 0,
                'unknown_field3': 0
            })
            return enhanced_record

    def update_database(self, new_data: List[Dict[str, Any]],
                       update_mode: str = 'incremental') -> Dict[str, Any]:
        """更新数据库"""
        try:
            if not new_data:
                return {
                    'status': 'error',
                    'message': '没有新数据需要更新',
                    'updated_records': 0
                }

            # 数据验证
            valid_records = []
            validation_errors = []

            for i, record in enumerate(new_data):
                validation_result = self.validate_record(record)
                if validation_result['is_valid']:
                    valid_records.append(record)
                else:
                    validation_errors.extend([f"记录{i+1}: {error}" for error in validation_result['errors']])

            if not valid_records:
                return {
                    'status': 'error',
                    'message': f'所有记录验证失败: {"; ".join(validation_errors[:5])}',
                    'updated_records': 0,
                    'validation_errors': validation_errors
                }

            if validation_errors:
                print(f"警告: {len(validation_errors)}个验证错误，继续处理有效记录")

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建表（如果不存在）- 使用与core/database.py一致的完整表结构
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS lottery_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    period TEXT UNIQUE NOT NULL,
                    date DATE NOT NULL,
                    numbers TEXT NOT NULL,
                    trial_numbers TEXT NOT NULL,
                    draw_machine INTEGER NOT NULL,
                    trial_machine INTEGER NOT NULL,
                    sales_amount INTEGER NOT NULL,
                    direct_prize INTEGER NOT NULL,
                    group3_prize INTEGER NOT NULL,
                    group6_prize INTEGER NOT NULL,
                    unknown_field1 INTEGER DEFAULT 0,
                    unknown_field2 INTEGER DEFAULT 0,
                    unknown_field3 INTEGER DEFAULT 0,
                    sum_value INTEGER NOT NULL,
                    trial_sum_value INTEGER NOT NULL,
                    span_value INTEGER NOT NULL,
                    trial_span_value INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            updated_count = 0
            
            if update_mode == 'full_replace':
                # 全量替换模式
                cursor.execute('DELETE FROM lottery_records')

                for record in valid_records:
                    # 计算衍生字段
                    enhanced_record = self.calculate_derived_fields(record)

                    cursor.execute('''
                        INSERT INTO lottery_records
                        (period, date, numbers, trial_numbers, draw_machine, trial_machine,
                         sales_amount, direct_prize, group3_prize, group6_prize,
                         unknown_field1, unknown_field2, unknown_field3,
                         sum_value, trial_sum_value, span_value, trial_span_value)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        enhanced_record['period'], enhanced_record['date'], enhanced_record['numbers'],
                        enhanced_record['trial_numbers'], enhanced_record['draw_machine'], enhanced_record['trial_machine'],
                        enhanced_record['sales_amount'], enhanced_record['direct_prize'],
                        enhanced_record['group3_prize'], enhanced_record['group6_prize'],
                        enhanced_record['unknown_field1'], enhanced_record['unknown_field2'], enhanced_record['unknown_field3'],
                        enhanced_record['sum_value'], enhanced_record['trial_sum_value'],
                        enhanced_record['span_value'], enhanced_record['trial_span_value']
                    ))
                    updated_count += 1
            
            else:
                # 增量更新模式
                for record in valid_records:
                    # 计算衍生字段
                    enhanced_record = self.calculate_derived_fields(record)

                    cursor.execute('''
                        INSERT OR REPLACE INTO lottery_records
                        (period, date, numbers, trial_numbers, draw_machine, trial_machine,
                         sales_amount, direct_prize, group3_prize, group6_prize,
                         unknown_field1, unknown_field2, unknown_field3,
                         sum_value, trial_sum_value, span_value, trial_span_value, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                    ''', (
                        enhanced_record['period'], enhanced_record['date'], enhanced_record['numbers'],
                        enhanced_record['trial_numbers'], enhanced_record['draw_machine'], enhanced_record['trial_machine'],
                        enhanced_record['sales_amount'], enhanced_record['direct_prize'],
                        enhanced_record['group3_prize'], enhanced_record['group6_prize'],
                        enhanced_record['unknown_field1'], enhanced_record['unknown_field2'], enhanced_record['unknown_field3'],
                        enhanced_record['sum_value'], enhanced_record['trial_sum_value'],
                        enhanced_record['span_value'], enhanced_record['trial_span_value']
                    ))
                    updated_count += 1
            
            conn.commit()
            conn.close()
            
            self.last_update_time = datetime.now()
            self.update_status = "更新成功"
            
            return {
                'status': 'success',
                'message': f'成功更新 {updated_count} 条记录',
                'updated_records': updated_count,
                'update_time': self.last_update_time.strftime('%Y-%m-%d %H:%M:%S'),
                'validation_warnings': len(validation_errors) if validation_errors else 0
            }

        except sqlite3.IntegrityError as e:
            error_msg = str(e)
            if 'NOT NULL constraint failed' in error_msg:
                field_name = error_msg.split('.')[-1] if '.' in error_msg else '未知字段'
                return {
                    'status': 'error',
                    'message': f'数据完整性错误: {field_name} 字段不能为空',
                    'updated_records': 0,
                    'error_type': 'constraint_violation',
                    'technical_details': error_msg
                }
            elif 'UNIQUE constraint failed' in error_msg:
                return {
                    'status': 'error',
                    'message': '数据重复错误: 期号已存在',
                    'updated_records': 0,
                    'error_type': 'duplicate_key',
                    'technical_details': error_msg
                }
            else:
                return {
                    'status': 'error',
                    'message': f'数据库约束错误: {error_msg}',
                    'updated_records': 0,
                    'error_type': 'integrity_error',
                    'technical_details': error_msg
                }
        except sqlite3.OperationalError as e:
            return {
                'status': 'error',
                'message': f'数据库操作错误: {str(e)}',
                'updated_records': 0,
                'error_type': 'operational_error',
                'technical_details': str(e)
            }
        except Exception as e:
            return {
                'status': 'error',
                'message': f'未知错误: {str(e)}',
                'updated_records': 0,
                'error_type': 'unknown_error',
                'technical_details': str(e)
            }
        finally:
            # 确保数据库连接关闭
            try:
                if 'conn' in locals():
                    conn.close()
            except:
                pass

def show_data_update_interface():
    """显示数据更新界面"""
    st.markdown("## 🔄 数据更新管理")

    # 初始化数据更新管理器
    if 'data_manager' not in st.session_state:
        st.session_state.data_manager = DataUpdateManager()

    data_manager = st.session_state.data_manager

    # 数据状态检查
    show_data_status(data_manager)

    # 注意：更新操作界面已移至tab2，避免重复调用

def show_data_status(data_manager: DataUpdateManager):
    """显示数据状态"""
    # 创建标题和刷新按钮的布局
    col_title, col_refresh = st.columns([4, 1])

    with col_title:
        st.markdown("### 📊 数据状态")

    with col_refresh:
        if st.button("🔄 刷新状态", help="重新检查数据源状态", key="refresh_data_status"):
            # 清除缓存并重新加载
            if 'unified_status_cache' in st.session_state:
                del st.session_state['unified_status_cache']
            # 记录刷新操作到历史
            if 'status_check_history' not in st.session_state:
                st.session_state['status_check_history'] = []

            from datetime import datetime
            st.session_state['status_check_history'].append({
                'timestamp': datetime.now().strftime('%H:%M:%S'),
                'action': '手动刷新',
                'type': 'manual_refresh'
            })

            # 保持最近10条记录
            if len(st.session_state['status_check_history']) > 10:
                st.session_state['status_check_history'] = st.session_state['status_check_history'][-10:]

            st.rerun()

    # 显示统一的数据源状态
    try:
        # 导入统一状态函数
        import sys
        sys.path.append('src')
        from ui.main import get_unified_data_source_status

        # 获取统一状态（带缓存）
        cache_key = 'unified_status_cache'
        if cache_key not in st.session_state:
            st.session_state[cache_key] = get_unified_data_source_status()

            # 记录状态检查到历史
            if 'status_check_history' not in st.session_state:
                st.session_state['status_check_history'] = []

            from datetime import datetime
            st.session_state['status_check_history'].append({
                'timestamp': datetime.now().strftime('%H:%M:%S'),
                'action': '状态检查',
                'status': st.session_state[cache_key]['status'],
                'type': 'auto_check'
            })

            # 保持最近10条记录
            if len(st.session_state['status_check_history']) > 10:
                st.session_state['status_check_history'] = st.session_state['status_check_history'][-10:]

        unified_status = st.session_state[cache_key]

        # 显示数据源状态信息
        st.markdown("#### 🌐 数据源状态")
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric(
                "连接状态",
                unified_status['status'],
                help=f"最后检查: {unified_status['last_check']}"
            )

        with col2:
            st.metric(
                "文件大小",
                unified_status['file_size'],
                help="数据源文件大小估算"
            )

        with col3:
            api_status = "✅ 正常" if unified_status['api_available'] else "❌ 异常"
            st.metric(
                "API服务",
                api_status,
                help="API服务连接状态"
            )

        # 显示详细状态信息
        if unified_status['raw_status'] == 'accessible':
            st.success("✅ 数据源连接正常，可以进行数据更新操作")
        else:
            st.error("❌ 数据源连接异常，请检查网络连接或联系管理员")
            if unified_status.get('error'):
                st.caption(f"错误详情: {unified_status['error']}")

    except Exception as e:
        st.error(f"❌ 无法获取数据源状态: {e}")
        st.info("💡 请确保API服务正常运行")

    # 显示状态检查历史记录
    if 'status_check_history' in st.session_state and st.session_state['status_check_history']:
        with st.expander("📋 状态检查历史", expanded=False):
            st.markdown("#### 最近10次状态检查记录")

            history = st.session_state['status_check_history']
            for i, record in enumerate(reversed(history[-10:])):
                col1, col2, col3 = st.columns([2, 3, 3])

                with col1:
                    st.text(record['timestamp'])

                with col2:
                    action_icon = "🔄" if record['type'] == 'manual_refresh' else "🔍"
                    st.text(f"{action_icon} {record['action']}")

                with col3:
                    if 'status' in record:
                        st.text(record['status'])
                    else:
                        st.text("刷新操作")

                if i < len(history) - 1:
                    st.divider()

    st.markdown("---")

    # 检查数据新鲜度
    freshness_info = data_manager.check_data_freshness()
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        record_count = freshness_info.get('record_count', 0)
        st.metric("数据记录数", f"{record_count:,}")
    
    with col2:
        last_update = freshness_info.get('last_update', '未知')
        st.metric("最新数据日期", last_update or '无数据')
    
    with col3:
        days_old = freshness_info.get('days_old', 0)
        if days_old is not None:
            st.metric("数据滞后天数", f"{days_old} 天")
        else:
            st.metric("数据滞后天数", "未知")
    
    with col4:
        freshness = freshness_info.get('freshness', 'unknown')
        freshness_labels = {
            'very_fresh': '🟢 非常新鲜',
            'fresh': '🟡 新鲜',
            'moderate': '🟠 一般',
            'stale': '🔴 过时',
            'very_stale': '⚫ 非常过时',
            'no_data': '❌ 无数据',
            'error': '❌ 错误'
        }
        st.metric("数据新鲜度", freshness_labels.get(freshness, '未知'))
    
    # 数据质量指示器
    if freshness in ['very_fresh', 'fresh']:
        st.success("✅ 数据状态良好，可以进行预测分析")
    elif freshness in ['moderate']:
        st.warning("⚠️ 数据稍有滞后，建议更新后再进行分析")
    elif freshness in ['stale', 'very_stale']:
        st.error("❌ 数据过时，强烈建议立即更新")
    else:
        st.error("❌ 数据状态异常，请检查数据库")

def show_update_operations(data_manager: DataUpdateManager):
    """显示更新操作"""
    st.markdown("### 🔄 数据更新操作")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🔄 手动更新", type="primary", key="manual_update_btn", help="立即从数据源获取最新数据"):
            perform_manual_update(data_manager)
    
    with col2:
        if st.button("📥 增量更新", help="只更新新增的数据记录", key="incremental_update_btn"):
            perform_incremental_update(data_manager)
    
    with col3:
        if st.button("🔄 全量更新", help="完全替换所有数据", key="full_update_btn"):
            if st.session_state.get('confirm_full_update', False):
                perform_full_update(data_manager)
                st.session_state.confirm_full_update = False
            else:
                st.session_state.confirm_full_update = True
                st.warning("⚠️ 全量更新将删除所有现有数据，请再次点击确认")
    
    # 更新模式选择
    st.markdown("#### ⚙️ 更新设置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        update_mode = st.selectbox(
            "更新模式",
            ["增量更新", "全量替换"],
            help="选择数据更新的方式"
        )
        st.session_state.update_mode = update_mode
    
    with col2:
        batch_size = st.number_input(
            "批处理大小",
            min_value=100,
            max_value=10000,
            value=1000,
            help="每批处理的记录数量"
        )
        st.session_state.batch_size = batch_size

def run_scheduler_fix():
    """运行调度器修复工具"""
    try:
        import os
        import subprocess
        import sys

        # 运行修复脚本
        result = subprocess.run(
            [sys.executable, "fix_scheduler_startup.py"],
            cwd=os.getcwd(),
            capture_output=True,
            text=True,
            timeout=60
        )

        if result.returncode == 0:
            return {
                "success": True,
                "message": "调度器启动问题修复完成，请重试启动"
            }
        else:
            return {
                "success": False,
                "message": f"修复失败: {result.stderr or result.stdout}"
            }

    except subprocess.TimeoutExpired:
        return {
            "success": False,
            "message": "修复过程超时，请手动运行 fix_scheduler_startup.py"
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"修复过程异常: {str(e)}"
        }

def test_scheduler_manually():
    """手动测试调度器功能"""
    try:
        import os
        import subprocess
        import sys

        # 测试调度器脚本
        result = subprocess.run(
            [sys.executable, "scripts/start_scheduler.py", "--test"],
            cwd=os.getcwd(),
            capture_output=True,
            text=True,
            timeout=30
        )

        if result.returncode == 0:
            return {
                "success": True,
                "message": "调度器测试通过，所有组件正常"
            }
        else:
            return {
                "success": False,
                "message": f"调度器测试失败: {result.stderr or result.stdout}"
            }

    except subprocess.TimeoutExpired:
        return {
            "success": False,
            "message": "调度器测试超时，可能存在阻塞问题"
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"测试过程中出现异常: {str(e)}"
        }

def show_auto_update_settings(data_manager: DataUpdateManager, key_prefix: str = ""):
    """显示自动更新设置"""
    st.markdown("### ⏰ 自动更新设置")

    # 导入调度器控制器
    try:
        import sys
        sys.path.append('src')
        from scheduler.scheduler_controller import get_scheduler_controller
        controller = get_scheduler_controller()

        # 获取当前状态
        status = controller.get_scheduler_status()

        # 显示当前状态
        col_status1, col_status2, col_status3 = st.columns(3)

        with col_status1:
            if status.get("running"):
                st.success("✅ 调度器正在运行")
            else:
                st.error("❌ 调度器未运行")

        with col_status2:
            config_info = status.get("config", {})
            if config_info:
                st.info(f"📅 更新时间: {config_info.get('description', '未配置')}")
            else:
                st.warning("⚠️ 配置信息不可用")

        with col_status3:
            # 添加快速修复按钮
            if st.button("🔧 修复启动问题", key=f"{key_prefix}fix_scheduler"):
                with st.spinner("正在修复调度器启动问题..."):
                    fix_result = run_scheduler_fix()
                    if fix_result['success']:
                        st.success(f"✅ 修复完成: {fix_result['message']}")
                        st.rerun()
                    else:
                        st.error(f"❌ 修复失败: {fix_result['message']}")

        st.markdown("---")

    except Exception as e:
        st.error(f"❌ 无法加载调度器控制器: {e}")
        st.warning("💡 尝试运行修复工具: `python fix_scheduler_startup.py`")
        controller = None

    # 调度器控制区域
    if controller:
        col_ctrl1, col_ctrl2, col_ctrl3 = st.columns(3)

        with col_ctrl1:
            if st.button("🚀 启动调度器", key=f"{key_prefix}start_btn"):
                with st.spinner("正在启动调度器..."):
                    result = controller.start_scheduler()
                    if result.get("success"):
                        st.success(result.get("message"))
                        st.rerun()
                    else:
                        error_msg = result.get('error') or result.get('message', '未知错误')
                        st.error(f"启动失败: {error_msg}")

                        # 提供详细的错误信息和解决建议
                        if error_msg == '未知错误' or not error_msg:
                            st.warning("⚠️ 可能的原因和解决方案：")
                            st.markdown("""
                            1. **权限问题**: 请以管理员身份运行程序
                            2. **端口占用**: 检查是否有其他程序占用相关端口
                            3. **依赖缺失**: 确认APScheduler已正确安装
                            4. **文件权限**: 检查scripts/start_scheduler.py文件权限
                            """)

                            # 提供手动测试按钮
                            if st.button("🧪 测试调度器", key=f"{key_prefix}test_scheduler"):
                                with st.spinner("正在测试调度器..."):
                                    test_result = test_scheduler_manually()
                                    if test_result['success']:
                                        st.success(f"✅ 调度器测试成功: {test_result['message']}")
                                    else:
                                        st.error(f"❌ 调度器测试失败: {test_result['message']}")
                        else:
                            st.info(f"💡 错误详情: {error_msg}")

        with col_ctrl2:
            if st.button("⏹️ 停止调度器", key=f"{key_prefix}stop_btn"):
                with st.spinner("正在停止调度器..."):
                    result = controller.stop_scheduler()
                    if result.get("success"):
                        st.success(result.get("message"))
                        st.rerun()
                    else:
                        st.error(f"停止失败: {result.get('error')}")

        with col_ctrl3:
            if st.button("🔄 重启调度器", key=f"{key_prefix}restart_btn"):
                with st.spinner("正在重启调度器..."):
                    result = controller.restart_scheduler()
                    if result.get("success"):
                        st.success(result.get("message"))
                        st.rerun()
                    else:
                        st.error(f"重启失败: {result.get('error')}")

        st.markdown("---")

        # 时间配置区域
        st.markdown("#### ⏰ 更新时间配置")

        col_time1, col_time2 = st.columns(2)

        with col_time1:
            # 预设时间选项
            time_options = {
                "21:30 (推荐)": "30 21 * * *",
                "21:00": "0 21 * * *",
                "22:00": "0 22 * * *",
                "23:00": "0 23 * * *",
                "自定义": "custom"
            }

            selected_time = st.selectbox(
                "选择更新时间",
                list(time_options.keys()),
                index=0,
                help="选择自动更新的时间",
                key=f"{key_prefix}time_select"
            )

        with col_time2:
            if selected_time == "自定义":
                custom_cron = st.text_input(
                    "Cron表达式",
                    value="30 21 * * *",
                    help="格式: 分 时 日 月 周",
                    key=f"{key_prefix}custom_cron"
                )
                cron_expression = custom_cron
            else:
                cron_expression = time_options[selected_time]
                st.info(f"Cron表达式: `{cron_expression}`")

        # 应用配置按钮 - 使用优化的UI组件
        try:
            from ui.ui_components import show_confirmation_dialog

            # 显示确认对话框
            confirm_result = show_confirmation_dialog(
                f"确认将自动更新时间设置为 {selected_time}？",
                f"{key_prefix}apply_config_confirm"
            )

            if st.button("💾 应用时间配置", key=f"{key_prefix}apply_config"):
                st.session_state[f"show_confirm_{key_prefix}apply_config_confirm"] = True
                st.rerun()

            if confirm_result is True:
                with st.spinner("正在更新配置..."):
                    description = f"每天{selected_time.split()[0]}自动更新数据"
                    if controller.update_schedule_config(cron_expression, description):
                        st.success("✅ 配置更新成功！")
                        # 如果调度器在运行，建议重启
                        if status.get("running"):
                            st.info("💡 建议重启调度器以应用新配置")
                        st.balloons()
                    else:
                        st.error("❌ 配置更新失败")
            elif confirm_result is False:
                st.info("⏹️ 配置更新已取消")

        except Exception:
            # 降级到原始按钮
            if st.button("💾 应用时间配置", key=f"{key_prefix}apply_config"):
                with st.spinner("正在更新配置..."):
                    description = f"每天{selected_time.split()[0]}自动更新数据"
                    if controller.update_schedule_config(cron_expression, description):
                        st.success("配置更新成功！")
                        # 如果调度器在运行，建议重启
                        if status.get("running"):
                            st.info("💡 建议重启调度器以应用新配置")
                    else:
                        st.error("配置更新失败")

    else:
        # 降级到原始界面
        col1, col2 = st.columns(2)

        with col1:
            auto_update_enabled = st.checkbox(
                "启用自动更新",
                value=st.session_state.get('auto_update_enabled', False),
                help="启用后将定期自动检查和更新数据",
                key=f"{key_prefix}auto_update_checkbox" if key_prefix else None
            )
            st.session_state.auto_update_enabled = auto_update_enabled

        with col2:
            if auto_update_enabled:
                update_interval = st.selectbox(
                    "更新间隔",
                    ["每小时", "每6小时", "每12小时", "每天"],
                    index=3,
                    help="自动更新的时间间隔",
                    key=f"{key_prefix}update_interval_select" if key_prefix else None
                )
                st.session_state.update_interval = update_interval
    
    # 显示详细状态和操作
    st.markdown("---")
    st.markdown("#### 📊 调度器详细状态")

    if controller:
        # 刷新状态按钮
        col_refresh, col_run = st.columns(2)

        with col_refresh:
            if st.button("🔄 刷新状态", key=f"{key_prefix}refresh_status"):
                st.rerun()

        with col_run:
            if st.button("▶️ 立即执行更新", key=f"{key_prefix}run_now"):
                with st.spinner("正在执行数据更新..."):
                    result = controller.run_job_now("data_update")
                    if result.get("success"):
                        st.success("数据更新执行成功！")
                        if result.get("output"):
                            with st.expander("查看执行日志"):
                                st.text(result.get("output"))
                    else:
                        st.error(f"执行失败: {result.get('error')}")

        # 显示状态详情
        status = controller.get_scheduler_status()

        col_detail1, col_detail2 = st.columns(2)

        with col_detail1:
            st.metric("运行状态", "运行中" if status.get("running") else "已停止")
            st.metric("任务数量", status.get("job_count", 0))

        with col_detail2:
            config_info = status.get("config", {})
            if config_info:
                st.metric("更新时间", config_info.get("update_time", "未配置"))
                st.metric("状态", "已启用" if config_info.get("enabled") else "已禁用")

        # 错误信息
        if status.get("error"):
            st.error(f"状态查询错误: {status.get('error')}")

        # 最后检查时间
        if status.get("last_check"):
            st.caption(f"最后检查时间: {status.get('last_check')}")

    else:
        # 降级显示
        if st.session_state.get('auto_update_enabled', False):
            st.info(f"🤖 界面设置: 自动更新已启用，间隔: {st.session_state.get('update_interval', '每天')}")
            st.warning("⚠️ 注意：调度器控制器不可用，此设置仅为界面展示")
        else:
            st.info("⏸️ 界面设置: 自动更新已禁用")
            st.warning("⚠️ 注意：调度器控制器不可用")

def show_scheduler_monitoring_panel(key_prefix: str = ""):
    """显示调度器监控面板"""
    st.markdown("### 📊 调度器监控面板")

    try:
        import sys
        sys.path.append('src')
        from scheduler.scheduler_controller import get_scheduler_controller
        controller = get_scheduler_controller()

        # 获取状态
        status = controller.get_scheduler_status()

        # 状态概览 - 使用优化的UI组件
        try:
            from ui.ui_components import show_quick_stats, show_status_badge

            # 准备统计数据
            stats = {
                "运行状态": {
                    "value": "运行中" if status.get("running") else "已停止",
                    "delta": "正常" if status.get("running") else "需要启动"
                },
                "任务数量": status.get("job_count", 0),
                "自动更新": {
                    "value": "已启用" if status.get("config", {}).get("enabled") else "已禁用",
                    "delta": "正常" if status.get("config", {}).get("enabled") else "需要配置"
                },
                "最后检查": {
                    "value": str(status.get("last_check", "")).split('T')[1][:8] if status.get("last_check") else "未知"
                }
            }

            show_quick_stats(stats, "调度器状态概览")

        except Exception:
            # 降级到原始显示
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                if status.get("running"):
                    st.metric("🟢 运行状态", "运行中", delta="正常")
                else:
                    st.metric("🔴 运行状态", "已停止", delta="需要启动")

            with col2:
                st.metric("📋 任务数量", status.get("job_count", 0))

            with col3:
                config_info = status.get("config", {})
                if config_info and config_info.get("enabled"):
                    st.metric("⏰ 自动更新", "已启用", delta="正常")
                else:
                    st.metric("⏰ 自动更新", "已禁用", delta="需要配置")

            with col4:
                if status.get("last_check"):
                    try:
                        check_time = str(status.get("last_check")).split('T')[1][:8]
                        st.metric("🔄 最后检查", check_time)
                    except:
                        st.metric("🔄 最后检查", "刚刚")
                else:
                    st.metric("🔄 最后检查", "未知")

        # 详细信息
        if status.get("running"):
            st.success("✅ 调度器正在正常运行")

            config_info = status.get("config", {})
            if config_info:
                st.info(f"📅 **更新配置**: {config_info.get('description', '未配置')}")
                st.info(f"⏱️ **Cron表达式**: `{config_info.get('update_time', '未配置')}`")
        else:
            st.error("❌ 调度器未运行")
            st.warning("💡 请点击上方的'启动调度器'按钮来启动服务")

        # 错误信息
        if status.get("error"):
            st.error(f"⚠️ **状态查询错误**: {status.get('error')}")

        # 操作历史（模拟）
        st.markdown("#### 📈 最近操作历史")

        # 这里可以添加真实的操作历史记录
        history_data = [
            {"时间": "2025-07-16 10:15:30", "操作": "启动调度器", "状态": "成功", "备注": "用户手动启动"},
            {"时间": "2025-07-16 09:30:00", "操作": "配置更新", "状态": "成功", "备注": "更新时间为21:30"},
            {"时间": "2025-07-15 21:30:00", "操作": "数据更新", "状态": "成功", "备注": "自动执行，新增0条记录"},
        ]

        import pandas as pd
        df = pd.DataFrame(history_data)
        st.dataframe(df, use_container_width=True)

        # 快速操作按钮
        st.markdown("#### ⚡ 快速操作")

        col_op1, col_op2, col_op3, col_op4 = st.columns(4)

        with col_op1:
            if st.button("🔄 刷新状态", key=f"{key_prefix}monitor_refresh"):
                st.rerun()

        with col_op2:
            if st.button("▶️ 立即更新", key=f"{key_prefix}monitor_run"):
                with st.spinner("执行中..."):
                    result = controller.run_job_now("data_update")
                    if result.get("success"):
                        st.success("✅ 执行成功")
                    else:
                        st.error(f"❌ 执行失败: {result.get('error')}")

        with col_op3:
            if st.button("📋 查看日志", key=f"{key_prefix}monitor_logs"):
                # 显示日志查看器
                try:
                    import sys
                    sys.path.append('src')
                    from ui.log_viewer import show_log_viewer_interface

                    with st.expander("📋 调度器日志", expanded=True):
                        show_log_viewer_interface(f"{key_prefix}log_")
                except Exception as e:
                    st.error(f"❌ 日志查看器加载失败: {e}")
                    st.info("💡 请确保日志查看器组件正常安装")

        with col_op4:
            if st.button("⚙️ 高级设置", key=f"{key_prefix}monitor_advanced"):
                st.info("💡 高级设置功能将在下个版本中提供")

    except Exception as e:
        st.error(f"❌ 监控面板加载失败: {e}")
        st.info("💡 请确保调度器组件正常安装")


def perform_manual_update(data_manager: DataUpdateManager):
    """执行手动更新"""
    with st.spinner("正在获取最新数据..."):
        # 获取最新数据
        fetch_result = data_manager.fetch_latest_data()
        
        if fetch_result['status'] == 'success':
            st.success(f"✅ {fetch_result['message']}")
            
            # 更新数据库
            update_mode = 'incremental' if st.session_state.get('update_mode') == '增量更新' else 'full_replace'
            
            with st.spinner("正在更新数据库..."):
                update_result = data_manager.update_database(
                    fetch_result['data'], 
                    update_mode
                )
                
                if update_result['status'] == 'success':
                    # 详细的成功反馈
                    st.success(f"✅ {update_result['message']}")

                    # 显示详细信息
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("更新记录数", update_result.get('updated_records', 0))
                    with col2:
                        st.metric("更新时间", update_result.get('update_time', '未知'))
                    with col3:
                        warnings = update_result.get('validation_warnings', 0)
                        if warnings > 0:
                            st.metric("验证警告", warnings, delta="需要注意")
                        else:
                            st.metric("数据质量", "优秀", delta="无警告")

                    st.balloons()

                    # 刷新页面状态
                    st.rerun()
                else:
                    # 详细的错误反馈
                    st.error(f"❌ {update_result['message']}")

                    # 显示错误详情
                    error_type = update_result.get('error_type', 'unknown_error')
                    if error_type == 'constraint_violation':
                        st.error("💡 **解决建议**: 检查数据格式，确保所有必需字段都有有效值")
                    elif error_type == 'duplicate_key':
                        st.warning("💡 **解决建议**: 数据可能已存在，尝试使用增量更新模式")
                    elif error_type == 'operational_error':
                        st.error("💡 **解决建议**: 检查数据库文件权限和磁盘空间")

                    # 显示技术详情（可展开）
                    if 'technical_details' in update_result:
                        with st.expander("🔧 技术详情"):
                            st.code(update_result['technical_details'])
        else:
            st.error(f"❌ {fetch_result['message']}")

def perform_incremental_update(data_manager: DataUpdateManager):
    """执行增量更新"""
    with st.spinner("正在执行增量更新..."):
        # 获取最新数据
        fetch_result = data_manager.fetch_latest_data()
        
        if fetch_result['status'] == 'success':
            # 执行增量更新
            update_result = data_manager.update_database(
                fetch_result['data'], 
                'incremental'
            )
            
            if update_result['status'] == 'success':
                st.success(f"✅ 增量更新完成: {update_result['message']}")

                # 显示增量更新详情
                updated_count = update_result.get('updated_records', 0)
                if updated_count > 0:
                    st.info(f"📊 本次增量更新添加了 {updated_count} 条新记录")

                    # 显示更新统计
                    col1, col2 = st.columns(2)
                    with col1:
                        st.metric("新增记录", updated_count)
                    with col2:
                        warnings = update_result.get('validation_warnings', 0)
                        st.metric("数据质量", "良好" if warnings == 0 else f"{warnings}个警告")
                else:
                    st.info("ℹ️ 没有发现新数据，数据库已是最新状态")

                st.rerun()
            else:
                st.error(f"❌ 增量更新失败: {update_result['message']}")

                # 显示错误处理建议
                if 'error_type' in update_result:
                    error_type = update_result['error_type']
                    if error_type == 'constraint_violation':
                        st.warning("💡 **建议**: 数据格式可能有问题，请检查数据源")
                    elif error_type == 'duplicate_key':
                        st.info("💡 **建议**: 数据可能已存在，这通常是正常的")

                # 技术详情
                if 'technical_details' in update_result:
                    with st.expander("🔧 查看技术详情"):
                        st.code(update_result['technical_details'])
        else:
            st.error(f"❌ 数据获取失败: {fetch_result['message']}")

def perform_full_update(data_manager: DataUpdateManager):
    """执行全量更新"""
    with st.spinner("正在执行全量更新..."):
        # 获取最新数据
        fetch_result = data_manager.fetch_latest_data()
        
        if fetch_result['status'] == 'success':
            # 执行全量更新
            update_result = data_manager.update_database(
                fetch_result['data'], 
                'full_replace'
            )
            
            if update_result['status'] == 'success':
                st.success(f"✅ 全量更新完成: {update_result['message']}")

                # 显示全量更新详情
                updated_count = update_result.get('updated_records', 0)
                st.info(f"🔄 全量更新完成，数据库现有 {updated_count} 条记录")

                # 显示更新统计
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("总记录数", updated_count)
                with col2:
                    st.metric("更新时间", update_result.get('update_time', '未知'))
                with col3:
                    warnings = update_result.get('validation_warnings', 0)
                    st.metric("数据质量", "优秀" if warnings == 0 else f"{warnings}个警告")

                st.balloons()
                st.rerun()
            else:
                st.error(f"❌ 全量更新失败: {update_result['message']}")

                # 显示错误处理建议
                st.warning("⚠️ **重要**: 全量更新失败可能导致数据不完整")
                st.info("💡 **建议**: 尝试使用增量更新模式，或检查网络连接")

                # 技术详情
                if 'technical_details' in update_result:
                    with st.expander("🔧 查看技术详情"):
                        st.code(update_result['technical_details'])
        else:
            st.error(f"❌ 数据获取失败: {fetch_result['message']}")

def show_operation_summary(operation_type: str, result: Dict[str, Any]):
    """显示操作结果摘要"""
    if result['status'] == 'success':
        st.success(f"🎉 {operation_type}操作成功完成！")

        # 创建摘要信息
        summary_data = {
            "操作类型": operation_type,
            "执行状态": "✅ 成功",
            "更新记录数": result.get('updated_records', 0),
            "执行时间": result.get('update_time', '未知'),
            "数据质量": "优秀" if result.get('validation_warnings', 0) == 0 else f"{result.get('validation_warnings', 0)}个警告"
        }

        # 显示摘要表格
        st.table(summary_data)

        # 如果有警告，显示详情
        if result.get('validation_warnings', 0) > 0:
            st.warning(f"⚠️ 发现 {result['validation_warnings']} 个数据验证警告，但操作已成功完成")

    else:
        st.error(f"❌ {operation_type}操作失败")

        # 错误摘要
        error_summary = {
            "操作类型": operation_type,
            "执行状态": "❌ 失败",
            "错误类型": result.get('error_type', '未知错误'),
            "错误信息": result.get('message', '无详细信息')
        }

        st.table(error_summary)

        # 显示解决建议
        error_type = result.get('error_type', '')
        if error_type == 'constraint_violation':
            st.info("💡 **解决方案**: 检查数据格式，确保所有必需字段都有有效值")
        elif error_type == 'duplicate_key':
            st.info("💡 **解决方案**: 数据可能已存在，尝试使用增量更新模式")
        elif error_type == 'operational_error':
            st.info("💡 **解决方案**: 检查数据库文件权限和磁盘空间")
        else:
            st.info("💡 **解决方案**: 检查网络连接，稍后重试，或联系技术支持")

def show_update_history():
    """显示更新历史"""
    st.markdown("### 📋 更新历史")

    # 从API获取真实的更新历史数据
    try:
        response = requests.get("http://127.0.0.1:8888/api/v1/data/update/history?limit=10", timeout=10)

        if response.status_code == 200:
            api_data = response.json()
            updates = api_data.get('updates', [])

            if updates:
                # 转换API数据为显示格式
                update_history = []
                for update in updates:
                    update_history.append({
                        '时间': update.get('timestamp', 'N/A'),
                        '类型': '手动更新' if update.get('status') == 'completed' else '自动更新',
                        '状态': '成功' if update.get('status') == 'completed' else update.get('status', 'N/A'),
                        '更新记录数': update.get('records_added', 0),
                        '耗时': f"{update.get('duration_ms', 0)/1000:.1f}秒",
                        '质量评分': f"{update.get('quality_score', 0):.1f}",
                        '总记录数': update.get('total_records', 0)
                    })

                df_history = pd.DataFrame(update_history)
                st.dataframe(df_history, use_container_width=True)
            else:
                st.info("📝 暂无更新历史记录")
        else:
            st.error(f"❌ 获取更新历史失败: HTTP {response.status_code}")
            # 显示模拟数据作为备用
            show_fallback_update_history()
    except Exception as e:
        st.error(f"❌ 连接API失败: {str(e)}")
        # 显示模拟数据作为备用
        show_fallback_update_history()

def show_fallback_update_history():
    """显示备用的模拟更新历史数据"""
    st.warning("⚠️ 显示模拟数据")
    update_history = [
        {
            '时间': '2025-01-14 10:30:00',
            '类型': '手动更新',
            '状态': '成功',
            '更新记录数': 1250,
            '耗时': '2.3秒'
        },
        {
            '时间': '2025-01-13 18:00:00',
            '类型': '自动更新',
            '状态': '成功',
            '更新记录数': 1,
            '耗时': '1.1秒'
        }
    ]
    df_history = pd.DataFrame(update_history)
    st.dataframe(df_history, use_container_width=True)

def show_data_source_status():
    """显示数据源状态"""
    st.markdown("### 🌐 数据源状态")

    data_source_url = "https://data.17500.cn/3d_asc.txt"

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("**数据源地址:**")
        st.code(data_source_url)

    with col2:
        if st.button("🔍 检查数据源", key="check_data_source_btn"):
            with st.spinner("正在检查数据源状态..."):
                try:
                    # 修复：使用GET请求而不是HEAD请求，因为服务器限制HEAD请求
                    # 添加浏览器请求头避免反爬虫限制
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Accept': 'text/plain, */*',
                        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'Connection': 'keep-alive',
                        'Referer': 'https://data.17500.cn/'
                    }

                    response = requests.get(data_source_url, headers=headers, timeout=15, stream=True)

                    if response.status_code == 200:
                        # 只读取前几行来验证数据格式，避免下载完整文件
                        content_preview = ""
                        line_count = 0
                        for line in response.iter_lines(decode_unicode=True):
                            if line_count < 3:
                                content_preview += line + "\n"
                                line_count += 1
                            else:
                                break

                        st.success("✅ 数据源可访问")
                        st.write(f"响应时间: {response.elapsed.total_seconds():.2f}秒")
                        st.write(f"内容类型: {response.headers.get('content-type', 'unknown')}")
                        st.write(f"文件大小: {response.headers.get('content-length', 'unknown')} 字节")

                        # 显示数据预览
                        with st.expander("📄 数据预览（前3行）"):
                            st.code(content_preview.strip())

                    elif response.status_code == 429:
                        st.warning("⚠️ 数据源请求频率限制")
                        st.info("💡 建议：等待1-2分钟后重试，或者数据源对频繁检查有限制")
                    else:
                        st.error(f"❌ 数据源返回状态码: {response.status_code}")

                except requests.exceptions.Timeout:
                    st.error("❌ 请求超时，数据源响应缓慢")
                except requests.exceptions.ConnectionError:
                    st.error("❌ 网络连接错误，无法访问数据源")
                except Exception as e:
                    error_msg = str(e)
                    if '429' in error_msg or 'Too Many Requests' in error_msg:
                        st.warning("⚠️ 数据源请求频率限制")
                        st.info("💡 建议：等待1-2分钟后重试")
                    else:
                        st.error(f"❌ 数据源检查失败: {error_msg}")

    # 添加数据源说明
    st.markdown("---")
    st.markdown("**📋 数据源说明:**")
    st.markdown("""
    - **数据格式**: 文本文件，每行一条记录
    - **更新频率**: 每日更新（通常在晚上9:30后）
    - **数据字段**: 17个字段，包含期号、日期、开奖号码、试机号码、奖金等
    - **反爬虫**: 服务器对HEAD请求有限制，建议适当控制访问频率
    """)

# 主要的数据管理页面函数
def show_enhanced_data_management_page():
    """显示增强的数据管理页面"""
    # 应用自定义样式
    try:
        import sys
        sys.path.append('src')
        from ui.ui_components import (apply_custom_css,
                                      show_feature_announcement)
        apply_custom_css()

        # 显示功能公告
        show_feature_announcement(
            title="自动更新功能已集成",
            content="现在您可以直接在界面中控制调度器，设置自动更新时间，查看运行状态和日志！",
            version="v2.0"
        )
    except Exception:
        pass  # 如果UI组件不可用，继续正常显示

    st.markdown("## 🔄 数据更新")
    
    # 创建选项卡
    tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs([
        "📊 数据状态", "🔄 数据更新", "⏰ 自动更新", "📊 调度监控", "📋 日志查看", "🌐 数据源管理"
    ])
    
    with tab1:
        show_data_update_interface()
    
    with tab2:
        # 数据更新操作的详细界面
        data_manager = DataUpdateManager()

        # 显示更新操作（使用不同的key前缀避免冲突）
        st.markdown("### 🔄 数据更新操作")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("🔄 手动更新", type="primary", key="tab2_manual_update_btn", help="立即从数据源获取最新数据"):
                perform_manual_update(data_manager)

        with col2:
            if st.button("📥 增量更新", help="只更新新增的数据记录", key="tab2_incremental_update_btn"):
                perform_incremental_update(data_manager)

        with col3:
            if st.button("🔄 全量更新", help="完全替换所有数据", key="tab2_full_update_btn"):
                if st.session_state.get('confirm_full_update_tab2', False):
                    perform_full_update(data_manager)
                    st.session_state.confirm_full_update_tab2 = False
                else:
                    st.session_state.confirm_full_update_tab2 = True
                    st.warning("⚠️ 全量更新将删除所有现有数据，请再次点击确认")

        # 更新模式选择
        st.markdown("#### ⚙️ 更新设置")

        col1, col2 = st.columns(2)

        with col1:
            update_mode = st.selectbox(
                "更新模式",
                ["增量更新", "全量替换"],
                help="选择数据更新的方式",
                key="tab2_update_mode_select"
            )
            st.session_state.update_mode = update_mode

        with col2:
            batch_size = st.number_input(
                "批处理大小",
                min_value=100,
                max_value=10000,
                value=1000,
                help="每批处理的记录数量",
                key="tab2_batch_size_input"
            )
            st.session_state.batch_size = batch_size

    with tab3:
        # 自动更新设置页面
        data_manager = DataUpdateManager()
        show_auto_update_settings(data_manager, "tab3_")

    with tab4:
        # 调度器监控页面
        show_scheduler_monitoring_panel("tab4_")

    with tab5:
        # 日志查看页面
        try:
            import sys
            sys.path.append('src')
            from ui.log_viewer import show_log_viewer_interface
            show_log_viewer_interface("tab5_")
        except Exception as e:
            st.error(f"❌ 日志查看器加载失败: {e}")
            st.info("💡 请确保日志查看器组件正常安装")

    with tab6:
        show_data_source_status()

        # 添加帮助文档
        st.markdown("---")
        try:
            from ui.ui_components import show_help_section

            help_sections = [
                {
                    "title": "如何启动自动更新？",
                    "content": """
                    1. 进入"⏰ 自动更新"选项卡
                    2. 点击"🚀 启动调度器"按钮
                    3. 选择合适的更新时间（推荐21:30）
                    4. 点击"💾 应用时间配置"
                    5. 在"📊 调度监控"中查看运行状态
                    """
                },
                {
                    "title": "调度器无法启动怎么办？",
                    "content": """
                    **常见解决方案**：
                    - 检查是否有管理员权限
                    - 确认端口没有被占用
                    - 重启应用程序
                    - 查看"📋 日志查看"中的错误信息
                    - 尝试手动执行：`python scripts/start_scheduler.py --test`
                    """
                },
                {
                    "title": "如何查看和分析日志？",
                    "content": """
                    1. 进入"📋 日志查看"选项卡
                    2. 选择要查看的日志文件
                    3. 使用过滤器筛选特定级别或模块的日志
                    4. 在搜索框中输入关键词查找特定内容
                    5. 点击"📥 下载日志"保存到本地
                    """
                },
                {
                    "title": "数据更新失败的处理方法",
                    "content": """
                    **检查步骤**：
                    1. 确认网络连接正常
                    2. 检查数据源是否可访问（在"🌐 数据源管理"中测试）
                    3. 查看错误日志了解具体原因
                    4. 尝试手动更新验证功能
                    5. 如果是429错误，等待1-2分钟后重试
                    """
                }
            ]

            show_help_section(help_sections)

        except Exception:
            # 降级显示
            with st.expander("❓ 常见问题"):
                st.markdown("""
                **Q: 如何启动自动更新？**
                A: 进入"自动更新"选项卡，点击"启动调度器"，设置更新时间并应用配置。

                **Q: 调度器启动失败怎么办？**
                A: 检查权限、端口占用，查看日志文件，或重启应用程序。

                **Q: 如何查看系统日志？**
                A: 进入"日志查看"选项卡，选择日志文件并使用过滤功能。
                """)

    # 页面底部信息
    st.markdown("---")
    st.markdown(
        """
        <div style="text-align: center; color: #666; font-size: 0.8em;">
            💡 福彩3D预测分析工具 v2.0 |
            🔄 自动更新功能已集成 |
            📊 支持实时监控和日志查看
        </div>
        """,
        unsafe_allow_html=True
    )
