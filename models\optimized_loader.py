#!/usr/bin/env python3
"""
优化的AI模型加载器
实现懒加载和缓存机制
"""

import os
import logging
from pathlib import Path
from typing import Optional, Any

logger = logging.getLogger(__name__)

class OptimizedModelLoader:
    """优化的模型加载器"""
    
    def __init__(self):
        self.models_cache = {}
        self.models_dir = Path("models")
        self.cache_dir = Path("models\cache")
        
        # 设置离线模式
        self._configure_offline_mode()
    
    def _configure_offline_mode(self):
        """配置离线模式"""
        os.environ['TRANSFORMERS_OFFLINE'] = '1'
        os.environ['HF_DATASETS_OFFLINE'] = '1'
        os.environ['TRANSFORMERS_CACHE'] = str(self.cache_dir)
        os.environ['HF_HOME'] = str(self.cache_dir)
    
    def load_sentence_transformer(self, model_name: str = 'all-MiniLM-L6-v2') -> Optional[Any]:
        """懒加载Sentence Transformer模型"""
        if model_name in self.models_cache:
            return self.models_cache[model_name]
        
        try:
            # 尝试从本地加载
            local_path = self.models_dir / "sentence-transformers" / model_name
            if local_path.exists():
                from sentence_transformers import SentenceTransformer
                model = SentenceTransformer(str(local_path))
                self.models_cache[model_name] = model
                logger.info(f"✅ 从本地加载模型: {model_name}")
                return model
            
            # 尝试在线加载
            from sentence_transformers import SentenceTransformer
            model = SentenceTransformer(model_name)
            self.models_cache[model_name] = model
            logger.info(f"✅ 在线加载模型: {model_name}")
            return model
            
        except Exception as e:
            logger.warning(f"⚠️ 模型加载失败: {e}")
            return None
    
    def get_tfidf_vectorizer(self):
        """获取TF-IDF向量化器"""
        if 'tfidf' not in self.models_cache:
            try:
                from sklearn.feature_extraction.text import TfidfVectorizer
                vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
                self.models_cache['tfidf'] = vectorizer
                logger.info("✅ TF-IDF向量化器已加载")
            except Exception as e:
                logger.error(f"❌ TF-IDF向量化器加载失败: {e}")
                return None
        
        return self.models_cache.get('tfidf')

# 全局模型加载器实例
_model_loader = None

def get_model_loader():
    """获取全局模型加载器实例"""
    global _model_loader
    if _model_loader is None:
        _model_loader = OptimizedModelLoader()
    return _model_loader
