#!/usr/bin/env python3
"""
API依赖项

定义FastAPI的依赖注入
"""

from fastapi import Depends, HTTPException, Header
from typing import Optional
import sys
sys.path.append('src')

from core.data_engine import DataEngine

# 全局数据引擎实例
_data_engine: Optional[DataEngine] = None

def get_data_engine() -> DataEngine:
    """获取数据引擎实例"""
    global _data_engine
    if _data_engine is None:
        _data_engine = DataEngine("data/lottery.db")
    return _data_engine

def verify_api_key(x_api_key: Optional[str] = Header(None)) -> bool:
    """验证API密钥（可选）"""
    # 在生产环境中可以启用API密钥验证
    # if x_api_key != "your-secret-api-key":
    #     raise HTTPException(status_code=401, detail="Invalid API key")
    return True

def rate_limit_check() -> bool:
    """速率限制检查（可选）"""
    # 在生产环境中可以实现速率限制
    return True
