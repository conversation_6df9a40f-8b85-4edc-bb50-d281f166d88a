@echo off
echo ========================================
echo 福彩3D预测系统一键启动脚本
echo ========================================
echo.

echo [1/4] 检查端口占用情况...
netstat -ano | findstr :8888 >nul
if %errorlevel% equ 0 (
    echo ⚠️ 端口8888已被占用，正在尝试释放...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8888') do (
        taskkill /pid %%a /f >nul 2>&1
    )
    timeout /t 2 >nul
)

netstat -ano | findstr :8501 >nul
if %errorlevel% equ 0 (
    echo ⚠️ 端口8501已被占用，正在尝试释放...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8501') do (
        taskkill /pid %%a /f >nul 2>&1
    )
    timeout /t 2 >nul
)

echo ✅ 端口检查完成

echo.
echo [2/4] 启动API服务...
start "福彩3D API服务" cmd /k "python -m uvicorn src.api.production_main:app --host 127.0.0.1 --port 8888"

echo ⏳ 等待API服务启动...
timeout /t 8 >nul

echo.
echo [3/4] 验证API服务状态...
curl -s http://127.0.0.1:8888/health >nul
if %errorlevel% equ 0 (
    echo ✅ API服务启动成功
) else (
    echo ❌ API服务启动失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo [4/4] 启动Streamlit界面...
start "福彩3D用户界面" cmd /k "streamlit run src/ui/main.py --server.port 8501 --server.address 127.0.0.1"

echo ⏳ 等待界面启动...
timeout /t 5 >nul

echo.
echo ========================================
echo 🎉 系统启动完成！
echo ========================================
echo.
echo 📊 API服务地址: http://127.0.0.1:8888
echo 🖥️ 用户界面地址: http://127.0.0.1:8501
echo.
echo 💡 提示：
echo - 如果界面没有自动打开，请手动访问上述地址
echo - 关闭此窗口不会影响服务运行
echo - 要停止服务，请关闭对应的命令行窗口
echo.

echo 🔍 正在验证系统状态...
curl -s http://127.0.0.1:8888/health
echo.

echo ✅ 启动脚本执行完成
echo 按任意键退出...
pause >nul
