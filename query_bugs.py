#!/usr/bin/env python3
"""
查询Bug报告详细信息的脚本
"""

import sys
import os
sys.path.append('.')

from src.bug_detection.core.database_manager import DatabaseManager
import json

def main():
    try:
        # 创建数据库管理器
        db_manager = DatabaseManager()
        
        # 获取所有Bug报告
        bug_reports = db_manager.get_bug_reports(limit=50)
        
        print('=== Bug报告详细信息 ===')
        print(f'总Bug数量: {len(bug_reports)}')
        print()
        
        if not bug_reports:
            print('没有找到Bug报告')
            return
        
        for i, bug in enumerate(bug_reports, 1):
            print(f'Bug #{i}:')
            print(f'  ID: {bug.get("id", "N/A")}')
            print(f'  错误类型: {bug.get("error_type", "N/A")}')
            print(f'  严重程度: {bug.get("severity", "N/A")}')
            print(f'  页面名称: {bug.get("page_name", "N/A")}')
            print(f'  错误消息: {bug.get("error_message", "N/A")}')
            print(f'  堆栈跟踪: {bug.get("stack_trace", "N/A")[:100]}...' if bug.get("stack_trace") else "N/A")
            print(f'  创建时间: {bug.get("created_at", "N/A")}')
            print(f'  状态: {bug.get("status", "N/A")}')
            print('  ---')
            
        # 统计分析
        print('\n=== 统计分析 ===')
        
        # 按错误类型统计
        error_types = {}
        for bug in bug_reports:
            error_type = bug.get('error_type', 'unknown')
            error_types[error_type] = error_types.get(error_type, 0) + 1
        
        print('错误类型分布:')
        for error_type, count in error_types.items():
            print(f'  {error_type}: {count}个')
        
        # 按严重程度统计
        severities = {}
        for bug in bug_reports:
            severity = bug.get('severity', 'unknown')
            severities[severity] = severities.get(severity, 0) + 1
        
        print('\n严重程度分布:')
        for severity, count in severities.items():
            print(f'  {severity}: {count}个')
        
        # 按状态统计
        statuses = {}
        for bug in bug_reports:
            status = bug.get('status', 'unknown')
            statuses[status] = statuses.get(status, 0) + 1
        
        print('\n状态分布:')
        for status, count in statuses.items():
            print(f'  {status}: {count}个')
            
    except Exception as e:
        print(f'查询Bug报告时出错: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
