#!/usr/bin/env python3
"""
统一预测记录存储系统
Unified Prediction Storage System

提供统一的预测记录存储、查询和管理功能
"""

import sqlite3
import json
import logging
from datetime import datetime
from typing import List, Dict, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class PredictionRecord:
    """预测记录数据模型"""
    id: Optional[int] = None
    period_number: str = ""
    model_name: str = ""
    predicted_numbers: str = ""
    confidence: float = 0.0
    prediction_time: Optional[datetime] = None
    actual_numbers: Optional[str] = None
    is_verified: bool = False
    accuracy_score: Optional[float] = None
    deviation_analysis: Optional[Dict] = None
    metadata: Optional[Dict] = None
    created_at: Optional[datetime] = None


@dataclass
class AnalysisRecord:
    """分析记录数据模型"""
    id: Optional[int] = None
    prediction_id: int = 0
    analysis_type: str = ""
    analysis_data: Dict = None
    created_at: Optional[datetime] = None


class UnifiedPredictionStorage:
    """统一预测记录存储系统"""
    
    def __init__(self, db_path: str = "data/unified_predictions.db"):
        """
        初始化统一预测存储系统
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        
        # 确保数据目录存在
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库
        self.init_database()
    
    def init_database(self):
        """初始化数据库表结构"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建统一预测记录表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS unified_predictions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        period_number TEXT NOT NULL,
                        model_name TEXT NOT NULL,
                        predicted_numbers TEXT NOT NULL,
                        confidence REAL NOT NULL,
                        prediction_time DATETIME NOT NULL,
                        actual_numbers TEXT,
                        is_verified BOOLEAN DEFAULT FALSE,
                        accuracy_score REAL,
                        deviation_analysis TEXT,
                        metadata TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(period_number, model_name)
                    )
                """)
                
                # 创建预测分析记录表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS prediction_analysis (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        prediction_id INTEGER,
                        analysis_type TEXT NOT NULL,
                        analysis_data TEXT NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (prediction_id) REFERENCES unified_predictions(id)
                    )
                """)
                
                # 创建索引以提高查询性能
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_period_model 
                    ON unified_predictions(period_number, model_name)
                """)
                
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_prediction_time 
                    ON unified_predictions(prediction_time)
                """)
                
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_is_verified 
                    ON unified_predictions(is_verified)
                """)
                
                conn.commit()
                self.logger.info("数据库初始化完成")
                
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    def save_prediction_record(self, record: PredictionRecord) -> int:
        """
        保存预测记录
        
        Args:
            record: 预测记录对象
            
        Returns:
            预测记录ID
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 准备数据
                prediction_time = record.prediction_time or datetime.now()
                deviation_analysis_json = json.dumps(record.deviation_analysis) if record.deviation_analysis else None
                metadata_json = json.dumps(record.metadata) if record.metadata else None
                
                # 插入或更新记录
                cursor.execute("""
                    INSERT OR REPLACE INTO unified_predictions 
                    (period_number, model_name, predicted_numbers, confidence, 
                     prediction_time, actual_numbers, is_verified, accuracy_score, 
                     deviation_analysis, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    record.period_number,
                    record.model_name,
                    record.predicted_numbers,
                    record.confidence,
                    prediction_time.isoformat(),
                    record.actual_numbers,
                    record.is_verified,
                    record.accuracy_score,
                    deviation_analysis_json,
                    metadata_json
                ))
                
                prediction_id = cursor.lastrowid
                self.logger.info(f"预测记录保存成功，ID: {prediction_id}")
                return prediction_id
                
        except Exception as e:
            self.logger.error(f"保存预测记录失败: {e}")
            raise
    
    def get_period_predictions(self, period_number: str) -> List[PredictionRecord]:
        """
        获取指定期号的所有模型预测
        
        Args:
            period_number: 期号
            
        Returns:
            预测记录列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT * FROM unified_predictions 
                    WHERE period_number = ?
                    ORDER BY prediction_time DESC
                """, (period_number,))
                
                records = []
                for row in cursor.fetchall():
                    record = self._row_to_prediction_record(row)
                    records.append(record)
                
                self.logger.info(f"获取期号 {period_number} 的预测记录 {len(records)} 条")
                return records
                
        except Exception as e:
            self.logger.error(f"获取期号预测记录失败: {e}")
            return []
    
    def update_prediction_result(self, period_number: str, actual_numbers: str):
        """
        更新预测结果的实际开奖号码
        
        Args:
            period_number: 期号
            actual_numbers: 实际开奖号码
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 更新所有该期号的预测记录
                cursor.execute("""
                    UPDATE unified_predictions 
                    SET actual_numbers = ?, is_verified = TRUE
                    WHERE period_number = ?
                """, (actual_numbers, period_number))
                
                updated_count = cursor.rowcount
                self.logger.info(f"更新期号 {period_number} 的 {updated_count} 条预测记录")
                
                # 计算准确率分数
                self._calculate_accuracy_scores(period_number, actual_numbers)
                
        except Exception as e:
            self.logger.error(f"更新预测结果失败: {e}")
            raise
    
    def get_model_predictions(self, model_name: str, limit: int = 100) -> List[PredictionRecord]:
        """
        获取指定模型的预测记录
        
        Args:
            model_name: 模型名称
            limit: 返回记录数量限制
            
        Returns:
            预测记录列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT * FROM unified_predictions 
                    WHERE model_name = ?
                    ORDER BY prediction_time DESC
                    LIMIT ?
                """, (model_name, limit))
                
                records = []
                for row in cursor.fetchall():
                    record = self._row_to_prediction_record(row)
                    records.append(record)
                
                return records
                
        except Exception as e:
            self.logger.error(f"获取模型预测记录失败: {e}")
            return []
    
    def save_analysis_result(self, prediction_id: int, analysis_type: str, analysis_data: Dict) -> int:
        """
        保存分析结果
        
        Args:
            prediction_id: 预测记录ID
            analysis_type: 分析类型
            analysis_data: 分析数据
            
        Returns:
            分析记录ID
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO prediction_analysis 
                    (prediction_id, analysis_type, analysis_data)
                    VALUES (?, ?, ?)
                """, (
                    prediction_id,
                    analysis_type,
                    json.dumps(analysis_data)
                ))
                
                analysis_id = cursor.lastrowid
                self.logger.info(f"分析结果保存成功，ID: {analysis_id}")
                return analysis_id
                
        except Exception as e:
            self.logger.error(f"保存分析结果失败: {e}")
            raise
    
    def get_unverified_predictions(self) -> List[PredictionRecord]:
        """
        获取未验证的预测记录
        
        Returns:
            未验证的预测记录列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT * FROM unified_predictions 
                    WHERE is_verified = FALSE
                    ORDER BY prediction_time DESC
                """)
                
                records = []
                for row in cursor.fetchall():
                    record = self._row_to_prediction_record(row)
                    records.append(record)
                
                return records
                
        except Exception as e:
            self.logger.error(f"获取未验证预测记录失败: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取预测记录统计信息
        
        Returns:
            统计信息字典
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                stats = {}
                
                # 总记录数
                cursor.execute("SELECT COUNT(*) FROM unified_predictions")
                stats['total_predictions'] = cursor.fetchone()[0]
                
                # 已验证记录数
                cursor.execute("SELECT COUNT(*) FROM unified_predictions WHERE is_verified = TRUE")
                stats['verified_predictions'] = cursor.fetchone()[0]
                
                # 各模型记录数
                cursor.execute("""
                    SELECT model_name, COUNT(*) 
                    FROM unified_predictions 
                    GROUP BY model_name
                """)
                stats['model_counts'] = dict(cursor.fetchall())
                
                # 平均准确率
                cursor.execute("""
                    SELECT AVG(accuracy_score) 
                    FROM unified_predictions 
                    WHERE accuracy_score IS NOT NULL
                """)
                avg_accuracy = cursor.fetchone()[0]
                stats['average_accuracy'] = avg_accuracy if avg_accuracy else 0.0
                
                return stats
                
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {}
    
    def _row_to_prediction_record(self, row) -> PredictionRecord:
        """将数据库行转换为预测记录对象"""
        return PredictionRecord(
            id=row[0],
            period_number=row[1],
            model_name=row[2],
            predicted_numbers=row[3],
            confidence=row[4],
            prediction_time=datetime.fromisoformat(row[5]) if row[5] else None,
            actual_numbers=row[6],
            is_verified=bool(row[7]),
            accuracy_score=row[8],
            deviation_analysis=json.loads(row[9]) if row[9] else None,
            metadata=json.loads(row[10]) if row[10] else None,
            created_at=datetime.fromisoformat(row[11]) if row[11] else None
        )
    
    def _calculate_accuracy_scores(self, period_number: str, actual_numbers: str):
        """计算准确率分数"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取该期号的所有预测
                cursor.execute("""
                    SELECT id, predicted_numbers FROM unified_predictions 
                    WHERE period_number = ?
                """, (period_number,))
                
                for prediction_id, predicted_numbers in cursor.fetchall():
                    # 计算准确率分数
                    accuracy_score = self._calculate_single_accuracy(predicted_numbers, actual_numbers)
                    
                    # 更新准确率分数
                    cursor.execute("""
                        UPDATE unified_predictions 
                        SET accuracy_score = ?
                        WHERE id = ?
                    """, (accuracy_score, prediction_id))
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"计算准确率分数失败: {e}")
    
    def _calculate_single_accuracy(self, predicted: str, actual: str) -> float:
        """计算单个预测的准确率分数"""
        if predicted == actual:
            return 1.0  # 完全正确
        
        # 计算位置匹配度
        matches = sum(1 for p, a in zip(predicted, actual) if p == a)
        position_accuracy = matches / len(actual)
        
        # 计算数字接近度
        pred_digits = [int(d) for d in predicted]
        actual_digits = [int(d) for d in actual]
        
        proximity_score = 0.0
        for p, a in zip(pred_digits, actual_digits):
            diff = abs(p - a)
            proximity_score += max(0, (10 - diff) / 10)
        proximity_score /= len(actual_digits)
        
        # 综合分数
        return (position_accuracy * 0.7 + proximity_score * 0.3)


if __name__ == "__main__":
    # 测试代码
    storage = UnifiedPredictionStorage()
    
    # 创建测试记录
    test_record = PredictionRecord(
        period_number="2025194",
        model_name="test_model",
        predicted_numbers="123",
        confidence=0.85,
        prediction_time=datetime.now()
    )
    
    # 保存记录
    record_id = storage.save_prediction_record(test_record)
    print(f"保存记录ID: {record_id}")
    
    # 获取统计信息
    stats = storage.get_statistics()
    print(f"统计信息: {stats}")
