"""
WebSocket训练监控系统
实现WebSocket连接管理、实时训练指标传输、异步进度监控
"""

import asyncio
import json
import time
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from collections import defaultdict
import logging

try:
    from fastapi import WebSocket, WebSocketDisconnect
    from fastapi.websockets import WebSocketState
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    # 模拟WebSocket类
    class WebSocket:
        def __init__(self):
            self.client_state = "connected"
        async def accept(self): pass
        async def send_json(self, data): pass
        async def receive_json(self): return {}
        async def close(self): pass
    
    class WebSocketDisconnect(Exception):
        pass


@dataclass
class TrainingSession:
    """训练会话"""
    session_id: str
    model_id: str
    websocket: WebSocket
    start_time: datetime
    config: Dict[str, Any]
    status: str = "initializing"  # initializing, training, paused, completed, failed
    current_epoch: int = 0
    total_epochs: int = 100
    metrics_history: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.metrics_history is None:
            self.metrics_history = []


@dataclass
class TrainingMetrics:
    """训练指标"""
    epoch: int
    loss: float
    accuracy: float
    val_loss: Optional[float] = None
    val_accuracy: Optional[float] = None
    learning_rate: Optional[float] = None
    batch_time: Optional[float] = None
    eta: Optional[str] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


class RealtimeMetricsCallback:
    """实时指标回调"""
    
    def __init__(self, callback_func: Callable, session_id: str):
        self.callback_func = callback_func
        self.session_id = session_id
        self.last_update_time = time.time()
        self.update_interval = 1.0  # 最小更新间隔（秒）
    
    async def on_epoch_end(self, epoch: int, metrics: Dict[str, float]):
        """训练轮次结束回调"""
        current_time = time.time()
        
        # 限制更新频率
        if current_time - self.last_update_time < self.update_interval:
            return
        
        self.last_update_time = current_time
        
        # 调用回调函数
        await self.callback_func(self.session_id, epoch, metrics)
    
    async def on_batch_end(self, batch: int, metrics: Dict[str, float]):
        """批次结束回调"""
        # 只在特定批次更新（避免过于频繁）
        if batch % 10 == 0:
            await self.callback_func(self.session_id, -1, metrics)  # -1表示批次更新


class EarlyStoppingCallback:
    """早停回调"""
    
    def __init__(self, patience: int = 10, min_delta: float = 0.001):
        self.patience = patience
        self.min_delta = min_delta
        self.best_score = None
        self.wait = 0
        self.stopped_epoch = 0
    
    async def on_epoch_end(self, epoch: int, metrics: Dict[str, float]) -> bool:
        """检查是否需要早停"""
        current_score = metrics.get('val_accuracy', metrics.get('accuracy', 0))
        
        if self.best_score is None:
            self.best_score = current_score
        elif current_score > self.best_score + self.min_delta:
            self.best_score = current_score
            self.wait = 0
        else:
            self.wait += 1
            if self.wait >= self.patience:
                self.stopped_epoch = epoch
                return True  # 触发早停
        
        return False


class ModelCheckpointCallback:
    """模型检查点回调"""
    
    def __init__(self, save_best_only: bool = True, save_path: str = "checkpoints/"):
        self.save_best_only = save_best_only
        self.save_path = save_path
        self.best_score = None
    
    async def on_epoch_end(self, epoch: int, metrics: Dict[str, float], model_state: Any = None):
        """保存模型检查点"""
        current_score = metrics.get('val_accuracy', metrics.get('accuracy', 0))
        
        should_save = False
        
        if self.save_best_only:
            if self.best_score is None or current_score > self.best_score:
                self.best_score = current_score
                should_save = True
        else:
            should_save = True
        
        if should_save and model_state:
            # 这里应该保存模型状态
            checkpoint_path = f"{self.save_path}epoch_{epoch}_acc_{current_score:.4f}.pt"
            # torch.save(model_state, checkpoint_path)  # 实际保存逻辑
            pass


class WebSocketTrainingMonitor:
    """WebSocket训练监控系统"""
    
    def __init__(self):
        self.active_sessions: Dict[str, TrainingSession] = {}
        self.connection_pool: Dict[str, WebSocket] = {}
        self.metrics_buffer: Dict[str, List[TrainingMetrics]] = defaultdict(list)
        self.logger = logging.getLogger("WebSocketTrainingMonitor")
        
        # 配置日志
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    async def start_training_monitor(self, model_id: str, websocket: WebSocket, 
                                   training_config: Dict[str, Any]) -> str:
        """启动训练监控"""
        session_id = f"{model_id}_{int(time.time())}_{str(uuid.uuid4())[:8]}"
        
        try:
            # 建立WebSocket连接
            await websocket.accept()
            self.connection_pool[session_id] = websocket
            
            # 创建训练会话
            session = TrainingSession(
                session_id=session_id,
                model_id=model_id,
                websocket=websocket,
                start_time=datetime.now(),
                config=training_config,
                total_epochs=training_config.get("epochs", 100)
            )
            
            self.active_sessions[session_id] = session
            
            # 发送初始化消息
            await self._send_message(session_id, {
                "type": "session_started",
                "session_id": session_id,
                "model_id": model_id,
                "config": training_config,
                "timestamp": datetime.now().isoformat()
            })
            
            self.logger.info(f"训练监控会话已启动: {session_id}")
            
            # 启动监控任务
            asyncio.create_task(self._monitor_session(session))
            
            return session_id
            
        except Exception as e:
            self.logger.error(f"启动训练监控失败: {e}")
            await self._cleanup_session(session_id)
            raise
    
    async def _monitor_session(self, session: TrainingSession):
        """监控训练会话"""
        try:
            while session.status in ["initializing", "training", "paused"]:
                try:
                    # 检查WebSocket连接状态
                    if not await self._is_websocket_connected(session.websocket):
                        self.logger.warning(f"WebSocket连接断开: {session.session_id}")
                        break
                    
                    # 接收客户端消息
                    try:
                        message = await asyncio.wait_for(
                            session.websocket.receive_json(), 
                            timeout=1.0
                        )
                        await self._handle_client_message(session, message)
                    except asyncio.TimeoutError:
                        # 超时是正常的，继续监控
                        pass
                    
                    # 发送心跳
                    await self._send_heartbeat(session.session_id)
                    
                    await asyncio.sleep(1)
                    
                except WebSocketDisconnect:
                    self.logger.info(f"客户端断开连接: {session.session_id}")
                    break
                except Exception as e:
                    self.logger.error(f"监控会话出错: {e}")
                    break
        
        finally:
            await self._cleanup_session(session.session_id)
    
    async def _handle_client_message(self, session: TrainingSession, message: Dict[str, Any]):
        """处理客户端消息"""
        msg_type = message.get("type")
        
        if msg_type == "start_training":
            session.status = "training"
            await self._send_message(session.session_id, {
                "type": "training_started",
                "timestamp": datetime.now().isoformat()
            })
        
        elif msg_type == "pause_training":
            session.status = "paused"
            await self._send_message(session.session_id, {
                "type": "training_paused",
                "timestamp": datetime.now().isoformat()
            })
        
        elif msg_type == "resume_training":
            session.status = "training"
            await self._send_message(session.session_id, {
                "type": "training_resumed",
                "timestamp": datetime.now().isoformat()
            })
        
        elif msg_type == "stop_training":
            session.status = "completed"
            await self._send_message(session.session_id, {
                "type": "training_stopped",
                "timestamp": datetime.now().isoformat()
            })
        
        elif msg_type == "get_status":
            await self._send_session_status(session.session_id)
    
    async def update_training_metrics(self, session_id: str, epoch: int, 
                                    metrics: Dict[str, float]):
        """更新训练指标"""
        if session_id not in self.active_sessions:
            return
        
        session = self.active_sessions[session_id]
        
        # 创建指标对象
        training_metrics = TrainingMetrics(
            epoch=epoch,
            loss=metrics.get("loss", 0.0),
            accuracy=metrics.get("accuracy", 0.0),
            val_loss=metrics.get("val_loss"),
            val_accuracy=metrics.get("val_accuracy"),
            learning_rate=metrics.get("learning_rate"),
            batch_time=metrics.get("batch_time"),
            eta=metrics.get("eta")
        )
        
        # 更新会话状态
        session.current_epoch = epoch
        session.metrics_history.append(training_metrics.to_dict())
        
        # 缓存指标
        self.metrics_buffer[session_id].append(training_metrics)
        
        # 发送实时更新
        await self._send_message(session_id, {
            "type": "metrics_update",
            "data": {
                "epoch": epoch,
                "metrics": metrics,
                "progress": epoch / session.total_epochs,
                "charts": self._generate_real_time_charts(session_id),
                "timestamp": datetime.now().isoformat()
            }
        })
        
        self.logger.debug(f"指标已更新 - 会话: {session_id}, 轮次: {epoch}")
    
    def _generate_real_time_charts(self, session_id: str) -> Dict[str, Any]:
        """生成实时图表数据"""
        if session_id not in self.metrics_buffer:
            return {}
        
        metrics_history = self.metrics_buffer[session_id]
        
        if not metrics_history:
            return {}
        
        # 提取时间序列数据
        epochs = [m.epoch for m in metrics_history if m.epoch >= 0]
        losses = [m.loss for m in metrics_history if m.epoch >= 0]
        accuracies = [m.accuracy for m in metrics_history if m.epoch >= 0]
        
        charts = {
            "loss_curve": {
                "x": epochs,
                "y": losses,
                "type": "line",
                "name": "Training Loss",
                "color": "red"
            },
            "accuracy_curve": {
                "x": epochs,
                "y": accuracies,
                "type": "line",
                "name": "Training Accuracy",
                "color": "blue"
            }
        }
        
        # 添加验证曲线（如果有）
        val_losses = [m.val_loss for m in metrics_history if m.val_loss is not None and m.epoch >= 0]
        val_accuracies = [m.val_accuracy for m in metrics_history if m.val_accuracy is not None and m.epoch >= 0]
        
        if val_losses:
            charts["val_loss_curve"] = {
                "x": epochs[:len(val_losses)],
                "y": val_losses,
                "type": "line",
                "name": "Validation Loss",
                "color": "orange"
            }
        
        if val_accuracies:
            charts["val_accuracy_curve"] = {
                "x": epochs[:len(val_accuracies)],
                "y": val_accuracies,
                "type": "line",
                "name": "Validation Accuracy",
                "color": "green"
            }
        
        return charts
    
    async def _send_message(self, session_id: str, message: Dict[str, Any]):
        """发送消息到客户端"""
        if session_id not in self.connection_pool:
            return
        
        websocket = self.connection_pool[session_id]
        
        try:
            await websocket.send_json(message)
        except Exception as e:
            self.logger.error(f"发送消息失败 - 会话: {session_id}, 错误: {e}")
            await self._cleanup_session(session_id)
    
    async def _send_heartbeat(self, session_id: str):
        """发送心跳"""
        await self._send_message(session_id, {
            "type": "heartbeat",
            "timestamp": datetime.now().isoformat()
        })
    
    async def _send_session_status(self, session_id: str):
        """发送会话状态"""
        if session_id not in self.active_sessions:
            return
        
        session = self.active_sessions[session_id]
        
        await self._send_message(session_id, {
            "type": "session_status",
            "data": {
                "session_id": session_id,
                "model_id": session.model_id,
                "status": session.status,
                "current_epoch": session.current_epoch,
                "total_epochs": session.total_epochs,
                "progress": session.current_epoch / session.total_epochs,
                "start_time": session.start_time.isoformat(),
                "metrics_count": len(session.metrics_history)
            }
        })
    
    async def _is_websocket_connected(self, websocket: WebSocket) -> bool:
        """检查WebSocket连接状态"""
        try:
            if FASTAPI_AVAILABLE:
                return websocket.client_state == WebSocketState.CONNECTED
            else:
                return websocket.client_state == "connected"
        except:
            return False
    
    async def _cleanup_session(self, session_id: str):
        """清理会话"""
        try:
            # 关闭WebSocket连接
            if session_id in self.connection_pool:
                websocket = self.connection_pool[session_id]
                try:
                    await websocket.close()
                except:
                    pass
                del self.connection_pool[session_id]
            
            # 清理会话数据
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
            
            # 保留指标缓存一段时间（用于后续分析）
            # 可以考虑将指标持久化到数据库
            
            self.logger.info(f"会话已清理: {session_id}")
            
        except Exception as e:
            self.logger.error(f"清理会话失败: {e}")
    
    def get_active_sessions(self) -> Dict[str, Dict[str, Any]]:
        """获取活跃会话列表"""
        sessions_info = {}
        
        for session_id, session in self.active_sessions.items():
            sessions_info[session_id] = {
                "model_id": session.model_id,
                "status": session.status,
                "current_epoch": session.current_epoch,
                "total_epochs": session.total_epochs,
                "start_time": session.start_time.isoformat(),
                "progress": session.current_epoch / session.total_epochs
            }
        
        return sessions_info
    
    def get_session_metrics(self, session_id: str) -> List[Dict[str, Any]]:
        """获取会话指标历史"""
        if session_id in self.active_sessions:
            return self.active_sessions[session_id].metrics_history
        return []
    
    async def stop_session(self, session_id: str):
        """停止指定会话"""
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            session.status = "completed"
            
            await self._send_message(session_id, {
                "type": "session_terminated",
                "timestamp": datetime.now().isoformat()
            })
            
            await self._cleanup_session(session_id)


# 测试函数
async def test_websocket_monitor():
    """测试WebSocket训练监控"""
    print("🧪 测试WebSocket训练监控系统...")
    
    monitor = WebSocketTrainingMonitor()
    
    # 模拟WebSocket连接
    mock_websocket = WebSocket()
    
    # 启动监控会话
    session_id = await monitor.start_training_monitor(
        "test_model", 
        mock_websocket, 
        {"epochs": 10, "batch_size": 32}
    )
    
    print(f"✅ 监控会话已启动: {session_id}")
    
    # 模拟训练指标更新
    for epoch in range(5):
        metrics = {
            "loss": 1.0 - epoch * 0.1,
            "accuracy": 0.5 + epoch * 0.08,
            "val_loss": 1.1 - epoch * 0.09,
            "val_accuracy": 0.45 + epoch * 0.07
        }
        
        await monitor.update_training_metrics(session_id, epoch, metrics)
        await asyncio.sleep(0.1)
    
    # 获取会话信息
    sessions = monitor.get_active_sessions()
    print(f"📊 活跃会话: {len(sessions)}")
    
    # 停止会话
    await monitor.stop_session(session_id)
    
    print("✅ WebSocket训练监控系统测试完成！")


if __name__ == "__main__":
    asyncio.run(test_websocket_monitor())
