"""
检查试机号码数据
"""

import sqlite3
import os

def main():
    print("=== 检查试机号码数据 ===")
    
    db_path = os.path.join('data', 'lottery.db')
    if not os.path.exists(db_path):
        print("✗ 数据库文件不存在")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 检查表结构
    cursor.execute("PRAGMA table_info(lottery_records)")
    columns = cursor.fetchall()
    column_names = [col[1] for col in columns]
    
    print(f"数据库列: {column_names}")
    
    # 检查是否有试机号码列
    if 'trial_numbers' in column_names:
        print("✓ 发现trial_numbers列")
        
        # 检查试机号码数据
        cursor.execute("SELECT COUNT(*) FROM lottery_records WHERE trial_numbers IS NOT NULL AND trial_numbers != ''")
        trial_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT numbers, trial_numbers FROM lottery_records WHERE trial_numbers IS NOT NULL AND trial_numbers != '' LIMIT 10")
        samples = cursor.fetchall()
        
        print(f"有试机号码的记录数: {trial_count}")
        print("试机号码示例:")
        for i, (numbers, trial) in enumerate(samples):
            print(f"  {i+1}. 正式: {numbers}, 试机: {trial}")
    
    else:
        print("✗ 未发现trial_numbers列")
        print("可用列:", column_names)
        
        # 检查是否有其他可能的试机号码列
        possible_trial_columns = [col for col in column_names if 'trial' in col.lower() or 'test' in col.lower()]
        if possible_trial_columns:
            print(f"可能的试机号码列: {possible_trial_columns}")
    
    conn.close()

if __name__ == "__main__":
    main()
