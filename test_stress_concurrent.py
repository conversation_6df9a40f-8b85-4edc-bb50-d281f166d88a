#!/usr/bin/env python3
"""
并发和压力测试

进行并发用户访问测试、数据库连接池压力测试、网络中断恢复测试，验证系统稳定性
"""

import asyncio
import json
import logging
import random
import statistics
import sys
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
from typing import Dict, List, Any, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 尝试导入所需库
try:
    import requests
    HAS_REQUESTS = True
except ImportError:
    HAS_REQUESTS = False
    logger.warning("requests库未安装，将跳过HTTP压力测试")

try:
    import websockets
    HAS_WEBSOCKETS = True
except ImportError:
    HAS_WEBSOCKETS = False
    logger.warning("websockets库未安装，将跳过WebSocket压力测试")

class StressConcurrentTester:
    """并发和压力测试器"""
    
    def __init__(self):
        self.api_base_url = "http://127.0.0.1:8000"
        self.websocket_url = "ws://127.0.0.1:8000"
        
        # 测试配置
        self.test_config = {
            "light_load": {"concurrent_users": 10, "duration_seconds": 30},
            "medium_load": {"concurrent_users": 25, "duration_seconds": 60},
            "heavy_load": {"concurrent_users": 50, "duration_seconds": 90},
            "burst_load": {"concurrent_users": 100, "duration_seconds": 30}
        }
        
        # 成功率阈值
        self.success_thresholds = {
            "light_load": 0.95,    # 95%成功率
            "medium_load": 0.90,   # 90%成功率
            "heavy_load": 0.85,    # 85%成功率
            "burst_load": 0.80     # 80%成功率
        }
    
    def create_session(self):
        """创建HTTP会话"""
        if HAS_REQUESTS:
            session = requests.Session()
            session.timeout = 30
            # 配置连接池
            adapter = requests.adapters.HTTPAdapter(
                pool_connections=100,
                pool_maxsize=100,
                max_retries=3
            )
            session.mount('http://', adapter)
            session.mount('https://', adapter)
            return session
        return None
    
    async def simulate_user_session(self, user_id: int, duration: int, session) -> Dict[str, Any]:
        """模拟单个用户会话"""
        start_time = time.time()
        end_time = start_time + duration
        
        user_stats = {
            "user_id": user_id,
            "requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "total_response_time": 0,
            "errors": []
        }
        
        # 定义用户可能访问的端点
        endpoints = [
            "/api/v1/health/",
            "/api/v1/health/ping",
            "/api/v1/health/summary",
            "/api/v1/data/basic-stats",
            "/api/v1/prediction/status"
        ]
        
        while time.time() < end_time:
            try:
                # 随机选择端点
                endpoint = random.choice(endpoints)
                url = f"{self.api_base_url}{endpoint}"
                
                # 发起请求
                request_start = time.time()
                response = await asyncio.to_thread(session.get, url)
                request_end = time.time()
                
                user_stats["requests"] += 1
                user_stats["total_response_time"] += (request_end - request_start) * 1000
                
                if response.status_code in [200, 503]:  # 503也可能是正常的健康检查响应
                    user_stats["successful_requests"] += 1
                else:
                    user_stats["failed_requests"] += 1
                    user_stats["errors"].append(f"HTTP {response.status_code}")
                
                # 模拟用户思考时间
                await asyncio.sleep(random.uniform(0.1, 2.0))
                
            except Exception as e:
                user_stats["failed_requests"] += 1
                user_stats["errors"].append(str(e))
                await asyncio.sleep(1)  # 错误后稍作等待
        
        return user_stats
    
    async def test_concurrent_load(self, load_type: str) -> Dict[str, Any]:
        """测试并发负载"""
        logger.info(f"🔥 测试{load_type}并发负载...")
        
        config = self.test_config[load_type]
        concurrent_users = config["concurrent_users"]
        duration = config["duration_seconds"]
        
        test_result = {
            "test_name": f"concurrent_load_{load_type}",
            "config": config,
            "user_stats": [],
            "summary": {},
            "passed": True
        }
        
        if not HAS_REQUESTS:
            test_result["note"] = "跳过测试 - requests库未安装"
            return test_result
        
        logger.info(f"启动 {concurrent_users} 个并发用户，持续 {duration} 秒")
        
        start_time = time.time()
        
        # 创建用户会话任务
        tasks = []
        sessions = []
        
        for user_id in range(concurrent_users):
            session = self.create_session()
            sessions.append(session)
            task = self.simulate_user_session(user_id, duration, session)
            tasks.append(task)
        
        try:
            # 并发执行所有用户会话
            user_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            successful_users = 0
            total_requests = 0
            total_successful_requests = 0
            total_failed_requests = 0
            all_response_times = []
            all_errors = []
            
            for result in user_results:
                if isinstance(result, dict):
                    test_result["user_stats"].append(result)
                    successful_users += 1
                    total_requests += result["requests"]
                    total_successful_requests += result["successful_requests"]
                    total_failed_requests += result["failed_requests"]
                    
                    if result["requests"] > 0:
                        avg_response_time = result["total_response_time"] / result["requests"]
                        all_response_times.append(avg_response_time)
                    
                    all_errors.extend(result["errors"])
                else:
                    logger.error(f"用户会话失败: {result}")
            
            # 计算总体统计
            total_time = time.time() - start_time
            success_rate = total_successful_requests / total_requests if total_requests > 0 else 0
            requests_per_second = total_requests / total_time if total_time > 0 else 0
            
            test_result["summary"] = {
                "total_execution_time": round(total_time, 2),
                "successful_users": successful_users,
                "total_users": concurrent_users,
                "total_requests": total_requests,
                "successful_requests": total_successful_requests,
                "failed_requests": total_failed_requests,
                "success_rate": round(success_rate, 4),
                "requests_per_second": round(requests_per_second, 2),
                "avg_response_time_ms": round(statistics.mean(all_response_times), 2) if all_response_times else 0,
                "error_count": len(all_errors),
                "unique_errors": len(set(all_errors))
            }
            
            # 判断是否通过
            threshold = self.success_thresholds.get(load_type, 0.8)
            test_result["passed"] = success_rate >= threshold
            test_result["threshold"] = threshold
            
        except Exception as e:
            logger.error(f"并发负载测试失败: {e}")
            test_result["passed"] = False
            test_result["error"] = str(e)
        
        finally:
            # 关闭所有会话
            for session in sessions:
                if session:
                    session.close()
        
        return test_result
    
    async def test_database_connection_pool(self) -> Dict[str, Any]:
        """测试数据库连接池压力"""
        logger.info("🗄️  测试数据库连接池压力...")
        
        test_result = {
            "test_name": "database_connection_pool",
            "tests": [],
            "summary": {},
            "passed": True
        }
        
        if not HAS_REQUESTS:
            test_result["note"] = "跳过测试 - requests库未安装"
            return test_result
        
        # 测试不同并发级别的数据库查询
        concurrent_levels = [5, 10, 20, 30]
        
        for concurrent_count in concurrent_levels:
            logger.info(f"测试 {concurrent_count} 个并发数据库查询")
            
            async def database_query(session, query_id):
                """执行数据库查询"""
                try:
                    start_time = time.time()
                    response = await asyncio.to_thread(
                        session.get, 
                        f"{self.api_base_url}/api/v1/data/basic-stats"
                    )
                    end_time = time.time()
                    
                    return {
                        "query_id": query_id,
                        "success": response.status_code == 200,
                        "response_time": (end_time - start_time) * 1000,
                        "status_code": response.status_code
                    }
                except Exception as e:
                    return {
                        "query_id": query_id,
                        "success": False,
                        "error": str(e),
                        "response_time": 0
                    }
            
            # 创建并发查询任务
            sessions = [self.create_session() for _ in range(concurrent_count)]
            tasks = [database_query(sessions[i], i) for i in range(concurrent_count)]
            
            try:
                start_time = time.time()
                results = await asyncio.gather(*tasks, return_exceptions=True)
                end_time = time.time()
                
                # 分析结果
                successful_queries = sum(1 for r in results if isinstance(r, dict) and r.get("success", False))
                response_times = [r.get("response_time", 0) for r in results if isinstance(r, dict) and r.get("success", False)]
                
                success_rate = successful_queries / concurrent_count
                total_time = (end_time - start_time) * 1000
                
                test_result["tests"].append({
                    "concurrent_queries": concurrent_count,
                    "successful_queries": successful_queries,
                    "success_rate": round(success_rate, 4),
                    "total_time_ms": round(total_time, 2),
                    "avg_response_time_ms": round(statistics.mean(response_times), 2) if response_times else 0,
                    "max_response_time_ms": round(max(response_times), 2) if response_times else 0,
                    "passed": success_rate >= 0.9  # 90%成功率阈值
                })
                
                if success_rate < 0.9:
                    test_result["passed"] = False
                
            except Exception as e:
                logger.error(f"数据库连接池测试失败 (并发数: {concurrent_count}): {e}")
                test_result["tests"].append({
                    "concurrent_queries": concurrent_count,
                    "error": str(e),
                    "passed": False
                })
                test_result["passed"] = False
            
            finally:
                # 关闭会话
                for session in sessions:
                    if session:
                        session.close()
            
            # 测试间隔
            await asyncio.sleep(2)
        
        return test_result
    
    async def test_websocket_stress(self) -> Dict[str, Any]:
        """测试WebSocket压力"""
        logger.info("🔌 测试WebSocket压力...")
        
        test_result = {
            "test_name": "websocket_stress",
            "tests": [],
            "summary": {},
            "passed": True
        }
        
        if not HAS_WEBSOCKETS:
            test_result["note"] = "跳过测试 - websockets库未安装"
            return test_result
        
        async def websocket_client(client_id, duration):
            """WebSocket客户端"""
            try:
                uri = f"{self.websocket_url}/ws/bug-detection"
                
                async with websockets.connect(uri, timeout=10) as websocket:
                    start_time = time.time()
                    end_time = start_time + duration
                    
                    messages_sent = 0
                    messages_received = 0
                    errors = 0
                    
                    while time.time() < end_time:
                        try:
                            # 发送ping消息
                            ping_msg = {
                                "type": "ping",
                                "client_id": client_id,
                                "timestamp": time.time()
                            }
                            await websocket.send(json.dumps(ping_msg))
                            messages_sent += 1
                            
                            # 等待响应
                            response = await asyncio.wait_for(websocket.recv(), timeout=5)
                            messages_received += 1
                            
                            # 短暂延迟
                            await asyncio.sleep(random.uniform(0.1, 1.0))
                            
                        except asyncio.TimeoutError:
                            errors += 1
                        except Exception as e:
                            errors += 1
                    
                    return {
                        "client_id": client_id,
                        "messages_sent": messages_sent,
                        "messages_received": messages_received,
                        "errors": errors,
                        "success_rate": messages_received / messages_sent if messages_sent > 0 else 0
                    }
            
            except Exception as e:
                return {
                    "client_id": client_id,
                    "error": str(e),
                    "success_rate": 0
                }
        
        # 测试不同数量的并发WebSocket连接
        concurrent_levels = [5, 10, 15]
        
        for concurrent_count in concurrent_levels:
            logger.info(f"测试 {concurrent_count} 个并发WebSocket连接")
            
            try:
                # 创建并发WebSocket客户端
                tasks = [websocket_client(i, 30) for i in range(concurrent_count)]
                
                start_time = time.time()
                results = await asyncio.gather(*tasks, return_exceptions=True)
                end_time = time.time()
                
                # 分析结果
                successful_clients = sum(1 for r in results if isinstance(r, dict) and r.get("success_rate", 0) > 0.8)
                total_messages_sent = sum(r.get("messages_sent", 0) for r in results if isinstance(r, dict))
                total_messages_received = sum(r.get("messages_received", 0) for r in results if isinstance(r, dict))
                total_errors = sum(r.get("errors", 0) for r in results if isinstance(r, dict))
                
                success_rate = successful_clients / concurrent_count
                message_success_rate = total_messages_received / total_messages_sent if total_messages_sent > 0 else 0
                
                test_result["tests"].append({
                    "concurrent_connections": concurrent_count,
                    "successful_clients": successful_clients,
                    "client_success_rate": round(success_rate, 4),
                    "total_messages_sent": total_messages_sent,
                    "total_messages_received": total_messages_received,
                    "message_success_rate": round(message_success_rate, 4),
                    "total_errors": total_errors,
                    "test_duration_ms": round((end_time - start_time) * 1000, 2),
                    "passed": success_rate >= 0.8 and message_success_rate >= 0.9
                })
                
                if success_rate < 0.8 or message_success_rate < 0.9:
                    test_result["passed"] = False
                
            except Exception as e:
                logger.error(f"WebSocket压力测试失败 (并发数: {concurrent_count}): {e}")
                test_result["tests"].append({
                    "concurrent_connections": concurrent_count,
                    "error": str(e),
                    "passed": False
                })
                test_result["passed"] = False
            
            # 测试间隔
            await asyncio.sleep(3)
        
        return test_result
    
    async def test_network_interruption_recovery(self) -> Dict[str, Any]:
        """测试网络中断恢复"""
        logger.info("🌐 测试网络中断恢复...")
        
        test_result = {
            "test_name": "network_interruption_recovery",
            "tests": [],
            "summary": {},
            "passed": True
        }
        
        if not HAS_REQUESTS:
            test_result["note"] = "跳过测试 - requests库未安装"
            return test_result
        
        # 模拟网络中断恢复测试
        session = self.create_session()
        
        try:
            # 1. 正常连接测试
            logger.info("测试正常连接...")
            start_time = time.time()
            response = await asyncio.to_thread(session.get, f"{self.api_base_url}/api/v1/health/ping")
            normal_response_time = (time.time() - start_time) * 1000
            
            test_result["tests"].append({
                "test_type": "normal_connection",
                "success": response.status_code == 200,
                "response_time_ms": round(normal_response_time, 2),
                "passed": response.status_code == 200
            })
            
            # 2. 模拟网络延迟
            logger.info("模拟网络延迟...")
            session.timeout = 1  # 设置短超时
            
            delay_errors = 0
            delay_successes = 0
            
            for i in range(5):
                try:
                    start_time = time.time()
                    response = await asyncio.to_thread(session.get, f"{self.api_base_url}/api/v1/health/ping")
                    response_time = (time.time() - start_time) * 1000
                    
                    if response.status_code == 200:
                        delay_successes += 1
                    
                except Exception:
                    delay_errors += 1
                
                await asyncio.sleep(0.5)
            
            test_result["tests"].append({
                "test_type": "network_delay_simulation",
                "successes": delay_successes,
                "errors": delay_errors,
                "success_rate": delay_successes / (delay_successes + delay_errors) if (delay_successes + delay_errors) > 0 else 0,
                "passed": delay_successes > 0  # 至少有一次成功
            })
            
            # 3. 恢复正常超时并测试恢复
            logger.info("测试网络恢复...")
            session.timeout = 30  # 恢复正常超时
            
            recovery_successes = 0
            recovery_times = []
            
            for i in range(3):
                try:
                    start_time = time.time()
                    response = await asyncio.to_thread(session.get, f"{self.api_base_url}/api/v1/health/ping")
                    response_time = (time.time() - start_time) * 1000
                    
                    if response.status_code == 200:
                        recovery_successes += 1
                        recovery_times.append(response_time)
                    
                except Exception:
                    pass
                
                await asyncio.sleep(1)
            
            avg_recovery_time = statistics.mean(recovery_times) if recovery_times else 0
            
            test_result["tests"].append({
                "test_type": "network_recovery",
                "recovery_successes": recovery_successes,
                "total_attempts": 3,
                "recovery_rate": recovery_successes / 3,
                "avg_recovery_time_ms": round(avg_recovery_time, 2),
                "passed": recovery_successes >= 2  # 至少2/3成功
            })
            
            # 判断整体是否通过
            failed_tests = [t for t in test_result["tests"] if not t.get("passed", False)]
            test_result["passed"] = len(failed_tests) == 0
            
        except Exception as e:
            logger.error(f"网络中断恢复测试失败: {e}")
            test_result["passed"] = False
            test_result["error"] = str(e)
        
        finally:
            if session:
                session.close()
        
        return test_result
    
    async def run_stress_tests(self) -> Dict[str, Any]:
        """运行所有压力测试"""
        logger.info("🚀 开始并发和压力测试套件")
        
        test_start_time = time.time()
        
        # 定义测试套件
        test_suite = [
            ("轻负载并发测试", lambda: self.test_concurrent_load("light_load")),
            ("中负载并发测试", lambda: self.test_concurrent_load("medium_load")),
            ("重负载并发测试", lambda: self.test_concurrent_load("heavy_load")),
            ("突发负载测试", lambda: self.test_concurrent_load("burst_load")),
            ("数据库连接池压力测试", self.test_database_connection_pool),
            ("WebSocket压力测试", self.test_websocket_stress),
            ("网络中断恢复测试", self.test_network_interruption_recovery)
        ]
        
        results = {}
        overall_passed = True
        
        for test_name, test_func in test_suite:
            try:
                logger.info(f"执行测试: {test_name}")
                result = await test_func()
                results[test_name] = result
                
                if not result.get("passed", False):
                    overall_passed = False
                
                # 测试间隔，让系统恢复
                await asyncio.sleep(5)
                
            except Exception as e:
                logger.error(f"测试 '{test_name}' 执行失败: {e}")
                results[test_name] = {
                    "test_name": test_name.lower().replace(" ", "_"),
                    "passed": False,
                    "error": str(e)
                }
                overall_passed = False
        
        total_execution_time = int((time.time() - test_start_time) * 1000)
        
        # 生成压力测试报告
        report = {
            "timestamp": datetime.now().isoformat(),
            "overall_passed": overall_passed,
            "total_execution_time_ms": total_execution_time,
            "test_config": self.test_config,
            "success_thresholds": self.success_thresholds,
            "test_results": results,
            "summary": self._generate_stress_summary(results)
        }
        
        # 输出测试结果
        self._print_stress_results(report)
        
        return report
    
    def _generate_stress_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成压力测试摘要"""
        total_test_categories = len(results)
        passed_test_categories = sum(1 for r in results.values() if r.get("passed", False))
        
        # 统计总请求数和成功率
        total_requests = 0
        total_successful_requests = 0
        
        for result in results.values():
            if "summary" in result and "total_requests" in result["summary"]:
                total_requests += result["summary"]["total_requests"]
                total_successful_requests += result["summary"]["successful_requests"]
        
        overall_success_rate = total_successful_requests / total_requests if total_requests > 0 else 0
        
        return {
            "total_test_categories": total_test_categories,
            "passed_test_categories": passed_test_categories,
            "total_requests_processed": total_requests,
            "overall_success_rate": round(overall_success_rate, 4),
            "stress_test_score": passed_test_categories / total_test_categories if total_test_categories > 0 else 0
        }
    
    def _print_stress_results(self, report: Dict[str, Any]):
        """打印压力测试结果"""
        logger.info("\n" + "="*60)
        logger.info("🔥 并发和压力测试结果")
        logger.info("="*60)
        
        summary = report["summary"]
        logger.info(f"总体状态: {'✅ 通过' if report['overall_passed'] else '❌ 未通过'}")
        logger.info(f"执行时间: {report['total_execution_time_ms']}ms")
        logger.info(f"测试类别: {summary['passed_test_categories']}/{summary['total_test_categories']} 通过")
        logger.info(f"总请求数: {summary['total_requests_processed']:,}")
        logger.info(f"整体成功率: {summary['overall_success_rate']:.1%}")
        logger.info(f"压力测试得分: {summary['stress_test_score']:.1%}")
        
        logger.info("\n详细结果:")
        for test_name, result in report["test_results"].items():
            status = "✅" if result.get("passed", False) else "❌"
            logger.info(f"{status} {test_name}")
            
            # 显示关键指标
            if "summary" in result:
                summary_data = result["summary"]
                if "success_rate" in summary_data:
                    logger.info(f"   成功率: {summary_data['success_rate']:.1%}")
                if "requests_per_second" in summary_data:
                    logger.info(f"   RPS: {summary_data['requests_per_second']:.1f}")

async def main():
    """主函数"""
    tester = StressConcurrentTester()
    
    try:
        report = await tester.run_stress_tests()
        
        # 保存压力测试报告
        from pathlib import Path
        report_file = Path("stress_concurrent_test_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"\n📄 压力测试报告已保存到: {report_file}")
        
        # 返回退出码
        if report["overall_passed"]:
            return 0
        else:
            return 1
    except Exception as e:
        logger.error(f"压力测试执行失败: {e}")
        return 2

if __name__ == "__main__":
    if not HAS_REQUESTS:
        logger.warning("⚠️  requests库未安装，大部分压力测试将被跳过")
        logger.info("安装命令: pip install requests")
    
    if not HAS_WEBSOCKETS:
        logger.warning("⚠️  websockets库未安装，WebSocket压力测试将被跳过")
        logger.info("安装命令: pip install websockets")
    
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
