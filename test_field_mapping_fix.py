#!/usr/bin/env python3
"""
测试字段映射修复结果
"""

import sys
import os
sys.path.append('src')

from src.ui.data_update_components import DataUpdateManager

def test_data_parsing():
    """测试数据解析逻辑"""
    print("测试数据解析逻辑...")
    
    # 模拟原始数据行（基于实际数据格式）
    test_lines = [
        "2025186 2025-07-15 2 2 7 1 3 7 1 1 113957536 0 1040 0 346 0 173",
        "2025185 2025-07-14 1 6 4 0 5 5 1 1 109890010 0 1040 0 346 0 173",
        "2025184 2025-07-13 7 9 9 6 3 3 1 1 110880482 0 1040 0 346 0 173"
    ]
    
    manager = DataUpdateManager()
    
    for i, line in enumerate(test_lines, 1):
        print(f"\n=== 测试记录 {i} ===")
        parts = line.split()
        print(f"原始数据: {line}")
        print(f"字段数量: {len(parts)}")
        
        # 手动解析验证
        if len(parts) >= 17:
            period = parts[0]
            date = parts[1]
            numbers = parts[2] + parts[3] + parts[4]
            trial_numbers = parts[5] + parts[6] + parts[7]
            
            # 测试修复后的字段映射
            direct_prize = int(parts[12]) if parts[12].isdigit() else 1040
            group3_prize = int(parts[14]) if len(parts) > 14 and parts[14].isdigit() else 346
            group6_prize = int(parts[16]) if len(parts) > 16 and parts[16].isdigit() else 173
            
            print(f"期号: {period}")
            print(f"日期: {date}")
            print(f"开奖号码: {numbers}")
            print(f"试机号码: {trial_numbers}")
            print(f"直选奖金: {direct_prize} (期望: 1040)")
            print(f"组三奖金: {group3_prize} (期望: 346)")
            print(f"组六奖金: {group6_prize} (期望: 173)")
            
            # 验证结果
            if direct_prize == 1040 and group3_prize == 346 and group6_prize == 173:
                print("✅ 字段映射正确")
            else:
                print("❌ 字段映射错误")
                return False
        else:
            print("❌ 字段数量不足")
            return False
    
    return True

def test_manager_parsing():
    """测试管理器的解析功能"""
    print("\n=== 测试管理器解析功能 ===")
    
    manager = DataUpdateManager()
    
    # 模拟数据获取（使用测试数据）
    test_data = """2025186 2025-07-15 2 2 7 1 3 7 1 1 113957536 0 1040 0 346 0 173
2025185 2025-07-14 1 6 4 0 5 5 1 1 109890010 0 1040 0 346 0 173"""
    
    # 模拟解析过程
    lines = test_data.strip().split('\n')
    new_records = []
    
    for line in lines:
        parts = line.split()
        if len(parts) >= 13:
            # 组合开奖号码和试机号码
            numbers = parts[2] + parts[3] + parts[4]
            trial_numbers = parts[5] + parts[6] + parts[7]
            
            record = {
                'period': parts[0],
                'date': parts[1],
                'numbers': numbers,
                'trial_numbers': trial_numbers,
                'draw_machine': int(parts[8]) if parts[8].isdigit() else 1,
                'trial_machine': int(parts[9]) if parts[9].isdigit() else 1,
                'sales_amount': int(parts[10]) if parts[10].isdigit() else 0,
                'direct_prize': int(parts[12]) if parts[12].isdigit() else 1040,
                'group3_prize': int(parts[14]) if len(parts) > 14 and parts[14].isdigit() else 346,
                'group6_prize': int(parts[16]) if len(parts) > 16 and parts[16].isdigit() else 173
            }
            new_records.append(record)
    
    print(f"解析得到 {len(new_records)} 条记录")
    
    for i, record in enumerate(new_records, 1):
        print(f"\n记录 {i}:")
        print(f"  期号: {record['period']}")
        print(f"  开奖号码: {record['numbers']}")
        print(f"  直选奖金: {record['direct_prize']} (期望: 1040)")
        print(f"  组三奖金: {record['group3_prize']} (期望: 346)")
        print(f"  组六奖金: {record['group6_prize']} (期望: 173)")
        
        # 验证奖金字段
        if (record['direct_prize'] == 1040 and 
            record['group3_prize'] == 346 and 
            record['group6_prize'] == 173):
            print("  ✅ 奖金字段正确")
        else:
            print("  ❌ 奖金字段错误")
            return False
    
    return True

def test_derived_fields():
    """测试衍生字段计算"""
    print("\n=== 测试衍生字段计算 ===")
    
    manager = DataUpdateManager()
    
    test_record = {
        'period': '2025186',
        'date': '2025-07-15',
        'numbers': '227',
        'trial_numbers': '137',
        'draw_machine': 1,
        'trial_machine': 1,
        'sales_amount': 113957536,
        'direct_prize': 1040,
        'group3_prize': 346,
        'group6_prize': 173
    }
    
    enhanced_record = manager.calculate_derived_fields(test_record)
    
    print(f"原始记录: {test_record}")
    print(f"增强记录: {enhanced_record}")
    
    # 验证计算字段
    expected_sum = 2 + 2 + 7  # 11
    expected_span = 7 - 2     # 5
    expected_trial_sum = 1 + 3 + 7  # 11
    expected_trial_span = 7 - 1     # 6
    
    if (enhanced_record['sum_value'] == expected_sum and
        enhanced_record['span_value'] == expected_span and
        enhanced_record['trial_sum_value'] == expected_trial_sum and
        enhanced_record['trial_span_value'] == expected_trial_span and
        enhanced_record['direct_prize'] == 1040 and
        enhanced_record['group3_prize'] == 346 and
        enhanced_record['group6_prize'] == 173):
        print("✅ 衍生字段计算正确，奖金字段保持正确")
        return True
    else:
        print("❌ 衍生字段计算或奖金字段错误")
        return False

def main():
    """主测试函数"""
    print("开始测试字段映射修复结果...")
    print("=" * 50)
    
    tests = [
        test_data_parsing,
        test_manager_parsing,
        test_derived_fields
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ 测试失败: {test.__name__}")
        except Exception as e:
            print(f"❌ 测试异常: {test.__name__} - {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 字段映射修复验证成功！")
        print("✅ 直选奖金正确显示: 1040")
        print("✅ 组三奖金正确显示: 346")
        print("✅ 组六奖金正确显示: 173")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    main()
