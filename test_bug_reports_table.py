#!/usr/bin/env python3
"""
测试Bug报告表格数据显示
"""

import pandas as pd
from src.bug_detection.core.database_manager import DatabaseManager

def test_bug_reports_data():
    """测试Bug报告数据获取"""
    try:
        print("🔍 测试Bug报告数据获取...")
        
        # 创建数据库管理器
        db_manager = DatabaseManager()
        
        # 获取Bug报告
        bug_reports = db_manager.get_bug_reports(limit=10)
        
        print(f"📊 获取到的Bug报告数量: {len(bug_reports)}")
        
        if bug_reports:
            print("\n📋 Bug报告数据结构:")
            first_report = bug_reports[0]
            for key, value in first_report.items():
                print(f"  - {key}: {value}")
            
            print("\n📊 转换为DataFrame测试:")
            df = pd.DataFrame(bug_reports)
            print(f"DataFrame形状: {df.shape}")
            print(f"DataFrame列名: {list(df.columns)}")
            
            # 检查显示列
            display_columns = ['id', 'error_type', 'severity', 'status', 'created_at']
            available_columns = [col for col in display_columns if col in df.columns]
            print(f"可用显示列: {available_columns}")
            
            if available_columns:
                print("\n📋 表格数据预览:")
                preview_df = df[available_columns].head(3)
                print(preview_df.to_string(index=False))
            else:
                print("\n📋 完整数据预览:")
                print(df.head(3).to_string(index=False))
                
        else:
            print("❌ 没有获取到Bug报告数据")
            
        return bug_reports
        
    except Exception as e:
        print(f"❌ 测试Bug报告数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_bug_table_display():
    """测试Bug表格显示逻辑"""
    try:
        print("\n" + "="*60)
        print("🔍 测试Bug表格显示逻辑")
        
        # 模拟Streamlit显示逻辑
        db_manager = DatabaseManager()
        bug_reports = db_manager.get_bug_reports(limit=500)
        
        if bug_reports:
            # 转换为DataFrame
            df = pd.DataFrame(bug_reports)
            print(f"✅ DataFrame创建成功，形状: {df.shape}")
            
            # 最新Bug列表
            latest_bugs = df.head(10)
            display_columns = ['id', 'error_type', 'severity', 'status', 'created_at']
            available_columns = [col for col in display_columns if col in df.columns]
            
            print(f"📊 显示列配置: {display_columns}")
            print(f"📊 可用列: {available_columns}")
            
            if available_columns:
                display_df = latest_bugs[available_columns]
                print(f"✅ 使用指定列显示，数据形状: {display_df.shape}")
                print("\n📋 显示数据预览:")
                print(display_df.to_string(index=False))
            else:
                print(f"⚠️ 使用全部列显示，数据形状: {latest_bugs.shape}")
                print("\n📋 显示数据预览:")
                print(latest_bugs.to_string(index=False))
        else:
            print("❌ 暂无Bug报告数据")
            
    except Exception as e:
        print(f"❌ 测试Bug表格显示时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 Bug报告表格数据测试")
    print("=" * 60)
    
    # 测试数据获取
    bug_reports = test_bug_reports_data()
    
    # 测试表格显示
    test_bug_table_display()
