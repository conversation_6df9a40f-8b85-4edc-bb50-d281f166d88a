"""
阶段B调试检查脚本
"""

import sys
import os

# 添加src路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def check_imports():
    """检查模块导入"""
    print("=== 检查模块导入 ===")
    
    issues = []
    
    # 1. 检查试机号码分析
    try:
        from prediction.trial_number_analysis import TrialNumberAnalyzer
        analyzer = TrialNumberAnalyzer()
        print("✓ 试机号码分析模块导入成功")
    except Exception as e:
        issues.append(f"试机号码分析模块: {e}")
        print(f"✗ 试机号码分析模块导入失败: {e}")
    
    # 2. 检查销售额分析
    try:
        from prediction.sales_impact_analysis import SalesImpactAnalyzer
        analyzer = SalesImpactAnalyzer()
        print("✓ 销售额分析模块导入成功")
    except Exception as e:
        issues.append(f"销售额分析模块: {e}")
        print(f"✗ 销售额分析模块导入失败: {e}")
    
    # 3. 检查机器偏好分析
    try:
        from prediction.machine_preference_analysis import MachinePreferenceAnalyzer
        analyzer = MachinePreferenceAnalyzer()
        print("✓ 机器偏好分析模块导入成功")
    except Exception as e:
        issues.append(f"机器偏好分析模块: {e}")
        print(f"✗ 机器偏好分析模块导入失败: {e}")
    
    # 4. 检查创新特征集成
    try:
        from prediction.innovative_features import InnovativeFeatureExtractor
        extractor = InnovativeFeatureExtractor()
        print("✓ 创新特征集成模块导入成功")
    except Exception as e:
        issues.append(f"创新特征集成模块: {e}")
        print(f"✗ 创新特征集成模块导入失败: {e}")
    
    return issues

def check_database_access():
    """检查数据库访问"""
    print("\n=== 检查数据库访问 ===")
    
    issues = []
    
    try:
        import sqlite3
        db_path = os.path.join('data', 'lottery.db')
        
        if not os.path.exists(db_path):
            issues.append("数据库文件不存在")
            print("✗ 数据库文件不存在")
            return issues
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查试机号码数据
        cursor.execute("SELECT COUNT(*) FROM lottery_records WHERE trial_numbers IS NOT NULL AND trial_numbers != '' AND trial_numbers != '000'")
        trial_count = cursor.fetchone()[0]
        print(f"✓ 试机号码记录: {trial_count} 条")
        
        # 检查销售额数据
        cursor.execute("SELECT COUNT(*) FROM lottery_records WHERE sales_amount IS NOT NULL AND sales_amount > 0")
        sales_count = cursor.fetchone()[0]
        print(f"✓ 销售额记录: {sales_count} 条")
        
        # 检查机器数据
        cursor.execute("SELECT COUNT(*) FROM lottery_records WHERE draw_machine IS NOT NULL AND trial_machine IS NOT NULL")
        machine_count = cursor.fetchone()[0]
        print(f"✓ 机器记录: {machine_count} 条")
        
        conn.close()
        
        if trial_count < 10:
            issues.append("试机号码数据不足")
        if sales_count < 10:
            issues.append("销售额数据不足")
        if machine_count < 10:
            issues.append("机器数据不足")
            
    except Exception as e:
        issues.append(f"数据库访问错误: {e}")
        print(f"✗ 数据库访问失败: {e}")
    
    return issues

def check_feature_extraction():
    """检查特征提取功能"""
    print("\n=== 检查特征提取功能 ===")
    
    issues = []
    
    try:
        from prediction.innovative_features import InnovativeFeatureExtractor
        
        extractor = InnovativeFeatureExtractor()
        
        # 测试基本功能
        test_data = ['123', '456', '789']
        test_context = {
            'trial_numbers': '234',
            'sales_amount': 50000000,
            'draw_machine': 1,
            'trial_machine': 2
        }
        
        # 不训练模型，直接测试特征提取框架
        try:
            # 测试各个分析器的基本功能
            trial_analyzer = extractor.trial_analyzer
            sales_analyzer = extractor.sales_analyzer
            machine_analyzer = extractor.machine_analyzer
            
            print("✓ 分析器初始化成功")
            
            # 测试数据加载（不执行，只检查方法存在）
            if hasattr(trial_analyzer, 'load_trial_data'):
                print("✓ 试机号码数据加载方法存在")
            else:
                issues.append("试机号码数据加载方法缺失")
            
            if hasattr(sales_analyzer, 'load_sales_data'):
                print("✓ 销售额数据加载方法存在")
            else:
                issues.append("销售额数据加载方法缺失")
            
            if hasattr(machine_analyzer, 'load_machine_data'):
                print("✓ 机器数据加载方法存在")
            else:
                issues.append("机器数据加载方法缺失")
            
        except Exception as e:
            issues.append(f"特征提取框架错误: {e}")
            print(f"✗ 特征提取框架测试失败: {e}")
            
    except Exception as e:
        issues.append(f"特征提取模块错误: {e}")
        print(f"✗ 特征提取模块测试失败: {e}")
    
    return issues

def check_method_signatures():
    """检查方法签名和参数"""
    print("\n=== 检查方法签名 ===")
    
    issues = []
    
    try:
        from prediction.trial_number_analysis import TrialNumberAnalyzer
        from prediction.sales_impact_analysis import SalesImpactAnalyzer
        from prediction.machine_preference_analysis import MachinePreferenceAnalyzer
        
        # 检查关键方法是否存在
        trial_analyzer = TrialNumberAnalyzer()
        required_trial_methods = [
            'load_trial_data', 'analyze_position_differences', 
            'analyze_preheating_effect', 'analyze_machine_correlation'
        ]
        
        for method_name in required_trial_methods:
            if hasattr(trial_analyzer, method_name):
                print(f"✓ 试机分析方法 {method_name} 存在")
            else:
                issues.append(f"试机分析方法 {method_name} 缺失")
        
        sales_analyzer = SalesImpactAnalyzer()
        required_sales_methods = [
            'load_sales_data', 'analyze_sales_distribution',
            'analyze_sales_number_correlation', 'analyze_sales_prediction_value'
        ]
        
        for method_name in required_sales_methods:
            if hasattr(sales_analyzer, method_name):
                print(f"✓ 销售额分析方法 {method_name} 存在")
            else:
                issues.append(f"销售额分析方法 {method_name} 缺失")
        
        machine_analyzer = MachinePreferenceAnalyzer()
        required_machine_methods = [
            'load_machine_data', 'analyze_machine_number_preferences',
            'analyze_machine_combinations', 'analyze_machine_temporal_patterns'
        ]
        
        for method_name in required_machine_methods:
            if hasattr(machine_analyzer, method_name):
                print(f"✓ 机器分析方法 {method_name} 存在")
            else:
                issues.append(f"机器分析方法 {method_name} 缺失")
                
    except Exception as e:
        issues.append(f"方法签名检查错误: {e}")
        print(f"✗ 方法签名检查失败: {e}")
    
    return issues

def main():
    """主函数"""
    print("阶段B调试检查")
    print("=" * 50)
    
    all_issues = []
    
    # 执行各项检查
    all_issues.extend(check_imports())
    all_issues.extend(check_database_access())
    all_issues.extend(check_feature_extraction())
    all_issues.extend(check_method_signatures())
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("调试检查结果:")
    print("=" * 50)
    
    if not all_issues:
        print("🎉 未发现bug，阶段B实施质量良好!")
        print("\n✅ 检查项目:")
        print("- ✓ 模块导入正常")
        print("- ✓ 数据库访问正常")
        print("- ✓ 特征提取框架完整")
        print("- ✓ 方法签名正确")
        
        print("\n📊 阶段B质量评估:")
        print("- 代码结构: 良好")
        print("- 模块集成: 正常")
        print("- 数据访问: 稳定")
        print("- 功能完整性: 完整")
        
        return True
    else:
        print(f"⚠️ 发现 {len(all_issues)} 个问题:")
        for i, issue in enumerate(all_issues, 1):
            print(f"  {i}. {issue}")
        
        print("\n🔧 建议修复措施:")
        print("- 检查依赖库安装")
        print("- 验证数据库完整性")
        print("- 确认模块路径配置")
        print("- 测试方法调用")
        
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n状态: {'✅ 无bug' if success else '❌ 需要修复'}")
    exit(0 if success else 1)
