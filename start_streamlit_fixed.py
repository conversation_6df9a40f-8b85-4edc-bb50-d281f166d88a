#!/usr/bin/env python3
"""
Streamlit启动包装器
确保正确的环境和依赖
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """主函数"""
    # 设置项目根目录
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    # 设置Python路径
    sys.path.insert(0, str(project_root / "src"))
    os.environ["PYTHONPATH"] = str(project_root / "src")
    
    # 验证APScheduler
    try:
        import apscheduler
        print(f"✅ APScheduler可用: {apscheduler.__version__}")
    except ImportError:
        print("❌ APScheduler不可用，尝试安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "apscheduler"])
    
    # 启动Streamlit
    print("🚀 启动Streamlit...")
    subprocess.run([
        sys.executable, "-m", "streamlit", "run",
        "src/ui/main.py",
        "--server.port=8501",
        "--server.address=127.0.0.1",
        "--browser.gatherUsageStats=false"
    ])

if __name__ == "__main__":
    main()
