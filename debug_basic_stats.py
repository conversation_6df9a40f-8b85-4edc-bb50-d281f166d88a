#!/usr/bin/env python3
"""
调试基础统计数据结构
"""

import sys
sys.path.append('src')

from core.data_engine import DataEngine
import json

def debug_basic_stats():
    print("🔍 调试基础统计数据结构...")
    
    try:
        # 初始化数据引擎
        engine = DataEngine("data/lottery.db")
        
        # 检查数据库记录数
        db_count = engine.db_manager.get_records_count()
        print(f"数据库记录数: {db_count}")
        
        # 从数据库加载数据到Polars引擎
        engine.load_data_from_database()
        
        # 检查Polars引擎DataFrame
        if engine.polars_engine.df is not None:
            print(f"Polars DataFrame记录数: {len(engine.polars_engine.df)}")
        else:
            print("❌ Polars DataFrame为空")
            return
        
        print("\n📊 测试Polars引擎的get_basic_stats方法...")
        polars_stats = engine.polars_engine.get_basic_stats()
        
        print("Polars引擎返回的数据结构:")
        print(json.dumps(polars_stats, indent=2, ensure_ascii=False, default=str))
        
        print("\n📊 测试数据引擎的get_basic_stats方法...")
        engine_stats = engine.get_basic_stats()
        
        print("数据引擎返回的数据结构:")
        print(json.dumps(engine_stats, indent=2, ensure_ascii=False, default=str))
        
        # 检查关键字段
        print("\n🔍 字段检查:")
        required_fields = ["total_records", "date_range", "sum_value_stats", "span_value_stats", "sales_amount_stats"]
        
        for field in required_fields:
            if field in polars_stats:
                print(f"✅ Polars引擎有字段: {field}")
            else:
                print(f"❌ Polars引擎缺少字段: {field}")
                
            if field in engine_stats:
                print(f"✅ 数据引擎有字段: {field}")
            else:
                print(f"❌ 数据引擎缺少字段: {field}")
        
    except Exception as e:
        print(f"❌ 调试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_basic_stats()
