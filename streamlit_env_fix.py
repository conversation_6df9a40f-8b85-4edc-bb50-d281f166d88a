
import sys
import os
from pathlib import Path

# 确保使用正确的Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

# 设置环境变量
os.environ["PYTHONPATH"] = str(project_root / "src")

# 验证APScheduler
try:
    import apscheduler
    print(f"✅ Streamlit环境中APScheduler可用: {apscheduler.__version__}")
except ImportError as e:
    print(f"❌ Streamlit环境中APScheduler不可用: {e}")
    # 尝试安装
    import subprocess
    subprocess.run([sys.executable, "-m", "pip", "install", "apscheduler"])
