#!/usr/bin/env python3
"""
AI智能Bug检测系统用户验收测试
使用Playwright进行自动化功能验证
"""

import asyncio
import time
import json
from datetime import datetime
from playwright.async_api import async_playwright, Page, Browser
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UserAcceptanceTest:
    """用户验收测试类"""
    
    def __init__(self):
        self.browser = None
        self.page = None
        self.test_results = {}
        self.api_url = "http://127.0.0.1:8888"
        self.streamlit_url = "http://127.0.0.1:8501"
        
    async def setup(self):
        """测试环境设置"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=False)
        self.page = await self.browser.new_page()
        
        # 设置视口大小
        await self.page.set_viewport_size({"width": 1920, "height": 1080})
        
        logger.info("🚀 测试环境设置完成")
    
    async def teardown(self):
        """清理测试环境"""
        if self.browser:
            await self.browser.close()
        logger.info("🧹 测试环境清理完成")
    
    async def test_stage1_system_startup(self):
        """阶段1：系统启动和基础验证"""
        logger.info("🔥 开始阶段1：系统启动和基础验证")
        stage_results = {}
        
        # 1.1：API服务验证
        try:
            await self.page.goto(f"{self.api_url}/health")
            content = await self.page.content()
            if "healthy" in content.lower() or "ok" in content.lower():
                stage_results["api_health"] = True
                logger.info("✅ API健康检查通过")
            else:
                stage_results["api_health"] = False
                logger.error("❌ API健康检查失败")
        except Exception as e:
            stage_results["api_health"] = False
            logger.error(f"❌ API服务连接失败: {e}")
        
        # 1.2：Streamlit服务验证
        try:
            start_time = time.time()
            await self.page.goto(self.streamlit_url)
            await self.page.wait_for_load_state("networkidle", timeout=10000)
            load_time = time.time() - start_time
            
            # 检查页面标题
            title = await self.page.title()
            if "bug" in title.lower() or "检测" in title:
                stage_results["streamlit_load"] = True
                stage_results["load_time"] = load_time
                logger.info(f"✅ Streamlit页面加载成功，耗时: {load_time:.2f}秒")
            else:
                stage_results["streamlit_load"] = False
                logger.error("❌ Streamlit页面标题不正确")
                
        except Exception as e:
            stage_results["streamlit_load"] = False
            logger.error(f"❌ Streamlit页面加载失败: {e}")
        
        # 1.3：界面元素检查
        try:
            # 检查侧边栏
            sidebar = await self.page.query_selector(".sidebar")
            if sidebar:
                stage_results["sidebar_present"] = True
                logger.info("✅ 侧边栏显示正常")
            else:
                stage_results["sidebar_present"] = False
                logger.warning("⚠️ 侧边栏未找到")
            
            # 检查主要内容区域
            main_content = await self.page.query_selector(".main")
            if main_content:
                stage_results["main_content_present"] = True
                logger.info("✅ 主内容区域显示正常")
            else:
                stage_results["main_content_present"] = False
                logger.warning("⚠️ 主内容区域未找到")
                
        except Exception as e:
            logger.error(f"❌ 界面元素检查失败: {e}")
        
        # 截图记录
        await self.page.screenshot(path="test_screenshots/stage1_homepage.png")
        
        self.test_results["stage1"] = stage_results
        logger.info("🎉 阶段1测试完成")
        return stage_results
    
    async def test_stage2_navigation(self):
        """阶段2：主界面和导航验证"""
        logger.info("🌐 开始阶段2：主界面和导航验证")
        stage_results = {}
        
        # 确保在主页
        await self.page.goto(self.streamlit_url)
        await self.page.wait_for_load_state("networkidle")
        
        # 2.1：导航菜单测试
        navigation_items = [
            "主页", "Bug检测仪表板", "实时监控", "数据统计", 
            "系统设置", "Bug报告管理", "AI分析", "性能监控"
        ]
        
        successful_navigations = 0
        for item in navigation_items:
            try:
                # 尝试点击导航项
                nav_element = await self.page.query_selector(f"text={item}")
                if nav_element:
                    await nav_element.click()
                    await self.page.wait_for_timeout(2000)  # 等待页面加载
                    successful_navigations += 1
                    logger.info(f"✅ 导航到 {item} 成功")
                else:
                    logger.warning(f"⚠️ 导航项 {item} 未找到")
            except Exception as e:
                logger.error(f"❌ 导航到 {item} 失败: {e}")
        
        stage_results["navigation_success_rate"] = successful_navigations / len(navigation_items)
        
        # 2.2：页面响应性测试
        try:
            # 测试页面刷新
            start_time = time.time()
            await self.page.reload()
            await self.page.wait_for_load_state("networkidle")
            refresh_time = time.time() - start_time
            
            stage_results["page_refresh_time"] = refresh_time
            stage_results["responsive"] = refresh_time < 5.0
            logger.info(f"✅ 页面刷新耗时: {refresh_time:.2f}秒")
            
        except Exception as e:
            stage_results["responsive"] = False
            logger.error(f"❌ 页面响应性测试失败: {e}")
        
        # 截图记录
        await self.page.screenshot(path="test_screenshots/stage2_navigation.png")
        
        self.test_results["stage2"] = stage_results
        logger.info("🎉 阶段2测试完成")
        return stage_results
    
    async def test_stage3_bug_detection(self):
        """阶段3：Bug检测功能核心验证"""
        logger.info("🔍 开始阶段3：Bug检测功能核心验证")
        stage_results = {}
        
        # 3.1：Bug检测仪表板测试
        try:
            # 导航到Bug检测仪表板
            dashboard_link = await self.page.query_selector("text=Bug检测仪表板")
            if dashboard_link:
                await dashboard_link.click()
                await self.page.wait_for_timeout(3000)
                
                # 检查仪表板元素
                metrics = await self.page.query_selector_all(".metric-card")
                charts = await self.page.query_selector_all(".chart-container")
                
                stage_results["dashboard_metrics"] = len(metrics)
                stage_results["dashboard_charts"] = len(charts)
                stage_results["dashboard_functional"] = len(metrics) > 0 and len(charts) > 0
                
                logger.info(f"✅ 仪表板显示 {len(metrics)} 个指标卡片，{len(charts)} 个图表")
            else:
                stage_results["dashboard_functional"] = False
                logger.warning("⚠️ Bug检测仪表板链接未找到")
                
        except Exception as e:
            stage_results["dashboard_functional"] = False
            logger.error(f"❌ Bug检测仪表板测试失败: {e}")
        
        # 3.2：实时监控功能测试
        try:
            # 查找实时监控相关元素
            monitor_elements = await self.page.query_selector_all("[class*='monitor']")
            realtime_elements = await self.page.query_selector_all("[class*='realtime']")
            
            stage_results["realtime_monitoring"] = len(monitor_elements) > 0 or len(realtime_elements) > 0
            
            if stage_results["realtime_monitoring"]:
                logger.info("✅ 实时监控功能元素存在")
            else:
                logger.warning("⚠️ 实时监控功能元素未找到")
                
        except Exception as e:
            stage_results["realtime_monitoring"] = False
            logger.error(f"❌ 实时监控功能测试失败: {e}")
        
        # 3.3：数据显示验证
        try:
            # 检查是否有数据表格
            tables = await self.page.query_selector_all("table")
            data_containers = await self.page.query_selector_all("[class*='data']")
            
            stage_results["data_display"] = len(tables) > 0 or len(data_containers) > 0
            
            if stage_results["data_display"]:
                logger.info(f"✅ 数据显示正常，发现 {len(tables)} 个表格")
            else:
                logger.warning("⚠️ 数据显示元素未找到")
                
        except Exception as e:
            stage_results["data_display"] = False
            logger.error(f"❌ 数据显示验证失败: {e}")
        
        # 截图记录
        await self.page.screenshot(path="test_screenshots/stage3_bug_detection.png")
        
        self.test_results["stage3"] = stage_results
        logger.info("🎉 阶段3测试完成")
        return stage_results
    
    async def test_stage4_data_management(self):
        """阶段4：数据管理和统计验证"""
        logger.info("📊 开始阶段4：数据管理和统计验证")
        stage_results = {}
        
        # 4.1：统计图表验证
        try:
            # 查找图表元素
            charts = await self.page.query_selector_all("canvas")
            svg_charts = await self.page.query_selector_all("svg")
            plotly_charts = await self.page.query_selector_all("[class*='plotly']")
            
            total_charts = len(charts) + len(svg_charts) + len(plotly_charts)
            stage_results["chart_count"] = total_charts
            stage_results["charts_present"] = total_charts > 0
            
            logger.info(f"✅ 发现 {total_charts} 个图表元素")
            
        except Exception as e:
            stage_results["charts_present"] = False
            logger.error(f"❌ 图表验证失败: {e}")
        
        # 4.2：交互功能测试
        try:
            # 查找按钮和交互元素
            buttons = await self.page.query_selector_all("button")
            inputs = await self.page.query_selector_all("input")
            selects = await self.page.query_selector_all("select")
            
            interactive_elements = len(buttons) + len(inputs) + len(selects)
            stage_results["interactive_elements"] = interactive_elements
            stage_results["interactive_functional"] = interactive_elements > 0
            
            logger.info(f"✅ 发现 {interactive_elements} 个交互元素")
            
        except Exception as e:
            stage_results["interactive_functional"] = False
            logger.error(f"❌ 交互功能测试失败: {e}")
        
        # 4.3：数据更新功能测试
        try:
            # 查找刷新或更新按钮
            refresh_buttons = await self.page.query_selector_all("button:has-text('刷新')")
            update_buttons = await self.page.query_selector_all("button:has-text('更新')")
            
            if refresh_buttons or update_buttons:
                stage_results["data_update_available"] = True
                logger.info("✅ 数据更新功能可用")
            else:
                stage_results["data_update_available"] = False
                logger.warning("⚠️ 数据更新功能未找到")
                
        except Exception as e:
            stage_results["data_update_available"] = False
            logger.error(f"❌ 数据更新功能测试失败: {e}")
        
        # 截图记录
        await self.page.screenshot(path="test_screenshots/stage4_data_management.png")
        
        self.test_results["stage4"] = stage_results
        logger.info("🎉 阶段4测试完成")
        return stage_results
    
    async def test_stage5_advanced_features(self):
        """阶段5：高级功能和集成验证"""
        logger.info("🚀 开始阶段5：高级功能和集成验证")
        stage_results = {}
        
        # 5.1：AI功能验证
        try:
            # 查找AI相关元素
            ai_elements = await self.page.query_selector_all("[class*='ai']")
            ai_text = await self.page.query_selector_all("text=AI")
            
            stage_results["ai_features_present"] = len(ai_elements) > 0 or len(ai_text) > 0
            
            if stage_results["ai_features_present"]:
                logger.info("✅ AI功能元素存在")
            else:
                logger.warning("⚠️ AI功能元素未找到")
                
        except Exception as e:
            stage_results["ai_features_present"] = False
            logger.error(f"❌ AI功能验证失败: {e}")
        
        # 5.2：性能监控验证
        try:
            # 查找性能相关元素
            perf_elements = await self.page.query_selector_all("[class*='performance']")
            monitor_elements = await self.page.query_selector_all("[class*='monitor']")
            
            stage_results["performance_monitoring"] = len(perf_elements) > 0 or len(monitor_elements) > 0
            
            if stage_results["performance_monitoring"]:
                logger.info("✅ 性能监控功能存在")
            else:
                logger.warning("⚠️ 性能监控功能未找到")
                
        except Exception as e:
            stage_results["performance_monitoring"] = False
            logger.error(f"❌ 性能监控验证失败: {e}")
        
        # 5.3：系统稳定性测试
        try:
            # 执行多次页面操作测试稳定性
            stability_score = 0
            test_operations = 5
            
            for i in range(test_operations):
                try:
                    await self.page.reload()
                    await self.page.wait_for_load_state("networkidle", timeout=10000)
                    stability_score += 1
                except:
                    pass
            
            stage_results["stability_score"] = stability_score / test_operations
            stage_results["system_stable"] = stage_results["stability_score"] >= 0.8
            
            logger.info(f"✅ 系统稳定性评分: {stage_results['stability_score']:.2f}")
            
        except Exception as e:
            stage_results["system_stable"] = False
            logger.error(f"❌ 系统稳定性测试失败: {e}")
        
        # 截图记录
        await self.page.screenshot(path="test_screenshots/stage5_advanced_features.png")
        
        self.test_results["stage5"] = stage_results
        logger.info("🎉 阶段5测试完成")
        return stage_results
    
    async def generate_test_report(self):
        """生成测试报告"""
        logger.info("📊 生成测试报告...")
        
        # 计算总体评分
        total_score = 0
        max_score = 0
        
        for stage, results in self.test_results.items():
            for key, value in results.items():
                if isinstance(value, bool):
                    max_score += 1
                    if value:
                        total_score += 1
                elif isinstance(value, (int, float)) and 0 <= value <= 1:
                    max_score += 1
                    total_score += value
        
        overall_score = (total_score / max_score * 100) if max_score > 0 else 0
        
        # 生成报告
        report = {
            "test_timestamp": datetime.now().isoformat(),
            "overall_score": round(overall_score, 2),
            "total_tests": max_score,
            "passed_tests": round(total_score, 2),
            "test_results": self.test_results,
            "summary": {
                "status": "PASS" if overall_score >= 80 else "FAIL",
                "recommendation": "系统可以投入使用" if overall_score >= 80 else "需要修复问题后重新测试"
            }
        }
        
        # 保存报告
        with open("user_acceptance_test_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📋 测试报告已生成，总体评分: {overall_score:.2f}%")
        return report
    
    async def run_full_test(self):
        """运行完整的用户验收测试"""
        logger.info("🚀 开始AI智能Bug检测系统用户验收测试")
        
        try:
            await self.setup()
            
            # 创建截图目录
            import os
            os.makedirs("test_screenshots", exist_ok=True)
            
            # 执行各阶段测试
            await self.test_stage1_system_startup()
            await self.test_stage2_navigation()
            await self.test_stage3_bug_detection()
            await self.test_stage4_data_management()
            await self.test_stage5_advanced_features()
            
            # 生成测试报告
            report = await self.generate_test_report()
            
            logger.info("🎉 用户验收测试完成！")
            return report
            
        except Exception as e:
            logger.error(f"❌ 测试执行失败: {e}")
            return None
        finally:
            await self.teardown()

async def main():
    """主函数"""
    test = UserAcceptanceTest()
    report = await test.run_full_test()
    
    if report:
        print(f"\n📊 测试结果:")
        print(f"总体评分: {report['overall_score']}%")
        print(f"测试状态: {report['summary']['status']}")
        print(f"建议: {report['summary']['recommendation']}")
    else:
        print("\n❌ 测试执行失败")

if __name__ == "__main__":
    asyncio.run(main())
