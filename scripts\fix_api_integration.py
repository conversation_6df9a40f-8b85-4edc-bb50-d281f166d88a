#!/usr/bin/env python3
"""
福彩3D预测系统API集成问题修复脚本
自动化执行修复计划中的关键步骤
"""

import os
import sys
import json
import time
import requests
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

class APIIntegrationFixer:
    """API集成问题修复器"""
    
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root) if project_root else Path(__file__).parent.parent
        self.api_base = "http://127.0.0.1:8888"
        self.ui_base = "http://127.0.0.1:8501"
        self.fix_log = []
        
    def log_action(self, action: str, status: str, details: str = ""):
        """记录修复操作"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'action': action,
            'status': status,
            'details': details
        }
        self.fix_log.append(log_entry)
        print(f"[{log_entry['timestamp']}] {action}: {status}")
        if details:
            print(f"  详情: {details}")
    
    def stage1_diagnose_problems(self):
        """阶段1：深度问题诊断"""
        print("\n🔍 开始阶段1：深度问题诊断")
        
        # 1.1 API服务状态检查
        self.check_api_service_status()
        
        # 1.2 Streamlit界面代码分析
        self.analyze_streamlit_code()
        
        # 1.3 新算法模块集成检查
        self.check_algorithm_modules()
        
        # 1.4 配置和环境对比
        self.check_configuration()
        
        print("✅ 阶段1：深度问题诊断完成")
    
    def check_api_service_status(self):
        """检查API服务状态"""
        print("  1.1 检查API服务状态...")
        
        try:
            # 检查健康端点
            response = requests.get(f"{self.api_base}/health", timeout=5)
            if response.status_code == 200:
                self.log_action("API健康检查", "成功", f"状态码: {response.status_code}")
            else:
                self.log_action("API健康检查", "异常", f"状态码: {response.status_code}")
            
            # 检查新预测端点
            test_payload = {
                "candidate_count": 10,
                "confidence_threshold": 0.3,
                "window_size": 50
            }
            response = requests.post(f"{self.api_base}/api/v1/prediction/single-best", 
                                   json=test_payload, timeout=10)
            if response.status_code == 200:
                self.log_action("预测API检查", "成功", "新API端点正常工作")
            else:
                self.log_action("预测API检查", "失败", f"状态码: {response.status_code}")
                
        except Exception as e:
            self.log_action("API服务检查", "错误", str(e))
    
    def analyze_streamlit_code(self):
        """分析Streamlit界面代码"""
        print("  1.2 分析Streamlit界面代码...")
        
        prediction_page = self.project_root / "src/ui/pages/prediction_result.py"
        
        if prediction_page.exists():
            with open(prediction_page, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查API调用
            if "/api/v1/prediction/single-best" in content:
                self.log_action("界面API调用检查", "正确", "使用了正确的API端点")
            else:
                self.log_action("界面API调用检查", "需要修复", "API端点路径需要更新")
                
            # 检查错误处理
            if "try:" in content and "except:" in content:
                self.log_action("错误处理检查", "存在", "有基本的错误处理")
            else:
                self.log_action("错误处理检查", "缺失", "需要添加错误处理")
        else:
            self.log_action("界面文件检查", "错误", "预测结果页面文件不存在")
    
    def check_algorithm_modules(self):
        """检查新算法模块集成"""
        print("  1.3 检查新算法模块集成...")
        
        modules_to_check = [
            "src/core/accuracy_focused_fusion.py",
            "src/core/number_ranking_system.py", 
            "src/core/model_performance_tracker.py"
        ]
        
        for module_path in modules_to_check:
            module_file = self.project_root / module_path
            if module_file.exists():
                self.log_action(f"模块检查 {module_path}", "存在", "模块文件已创建")
            else:
                self.log_action(f"模块检查 {module_path}", "缺失", "模块文件不存在")
    
    def check_configuration(self):
        """检查配置和环境"""
        print("  1.4 检查配置和环境...")
        
        # 检查Python环境
        python_version = sys.version
        self.log_action("Python版本检查", "信息", python_version)
        
        # 检查关键依赖
        try:
            import streamlit
            self.log_action("Streamlit依赖", "正常", f"版本: {streamlit.__version__}")
        except ImportError:
            self.log_action("Streamlit依赖", "缺失", "需要安装Streamlit")
        
        try:
            import requests
            self.log_action("Requests依赖", "正常", "HTTP请求库可用")
        except ImportError:
            self.log_action("Requests依赖", "缺失", "需要安装requests")
    
    def stage2_fix_api_interface(self):
        """阶段2：API接口修复"""
        print("\n🔧 开始阶段2：API接口修复")
        
        # 2.1 API路由配置修复
        self.fix_api_routing()
        
        # 2.2 核心算法集成
        self.integrate_core_algorithms()
        
        # 2.3 API响应格式标准化
        self.standardize_api_response()
        
        print("✅ 阶段2：API接口修复完成")
    
    def fix_api_routing(self):
        """修复API路由配置"""
        print("  2.1 修复API路由配置...")
        
        api_file = self.project_root / "src/api/prediction_api.py"
        
        if api_file.exists():
            self.log_action("API路由修复", "开始", "检查和更新API路由配置")
            # 这里应该包含具体的代码修复逻辑
            # 由于这是一个演示脚本，实际修复需要根据具体代码结构进行
            self.log_action("API路由修复", "需要手动", "请手动检查API路由配置")
        else:
            self.log_action("API路由修复", "错误", "API文件不存在")
    
    def integrate_core_algorithms(self):
        """集成核心算法"""
        print("  2.2 集成核心算法...")
        
        self.log_action("核心算法集成", "开始", "集成新的预测算法")
        # 实际的算法集成逻辑应该在这里实现
        self.log_action("核心算法集成", "需要手动", "请手动集成核心算法模块")
    
    def standardize_api_response(self):
        """标准化API响应格式"""
        print("  2.3 标准化API响应格式...")
        
        expected_format = {
            "best_prediction": {
                "number": "string",
                "confidence": "float",
                "method": "string"
            },
            "ranking_list": "array",
            "model_performance": "object"
        }
        
        self.log_action("API响应格式", "定义", f"标准格式: {json.dumps(expected_format, indent=2)}")
    
    def stage3_test_api_functionality(self):
        """阶段3：API功能测试"""
        print("\n🔄 开始阶段3：API功能测试")
        
        # 3.1 API端点测试
        self.test_api_endpoints()
        
        # 3.2 API集成测试
        self.test_api_integration()
        
        print("✅ 阶段3：API功能测试完成")
    
    def test_api_endpoints(self):
        """测试API端点"""
        print("  3.1 测试API端点...")
        
        endpoints_to_test = [
            ("GET", "/health", None),
            ("POST", "/api/v1/prediction/single-best", {
                "candidate_count": 10,
                "confidence_threshold": 0.3,
                "window_size": 50
            }),
            ("GET", "/api/v1/models/performance", None)
        ]
        
        for method, endpoint, payload in endpoints_to_test:
            try:
                url = f"{self.api_base}{endpoint}"
                if method == "GET":
                    response = requests.get(url, timeout=10)
                else:
                    response = requests.post(url, json=payload, timeout=10)
                
                self.log_action(f"API测试 {method} {endpoint}", 
                              "成功" if response.status_code == 200 else "失败",
                              f"状态码: {response.status_code}")
            except Exception as e:
                self.log_action(f"API测试 {method} {endpoint}", "错误", str(e))
    
    def test_api_integration(self):
        """测试API集成"""
        print("  3.2 测试API集成...")
        
        self.log_action("API集成测试", "开始", "测试完整的预测流程")
        # 这里应该包含完整的集成测试逻辑
        self.log_action("API集成测试", "需要手动", "请手动验证完整预测流程")
    
    def generate_fix_report(self):
        """生成修复报告"""
        print("\n📊 生成修复报告...")
        
        report = {
            "fix_timestamp": datetime.now().isoformat(),
            "total_actions": len(self.fix_log),
            "successful_actions": len([log for log in self.fix_log if log['status'] in ['成功', '正常', '存在']]),
            "failed_actions": len([log for log in self.fix_log if log['status'] in ['失败', '错误', '缺失']]),
            "actions_log": self.fix_log
        }
        
        # 保存报告
        report_file = self.project_root / "evaluation" / "api_fix_report.json"
        report_file.parent.mkdir(exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 修复报告已保存到: {report_file}")
        
        # 打印摘要
        print(f"\n📋 修复摘要:")
        print(f"  - 总操作数: {report['total_actions']}")
        print(f"  - 成功操作: {report['successful_actions']}")
        print(f"  - 失败操作: {report['failed_actions']}")
        print(f"  - 成功率: {report['successful_actions']/report['total_actions']*100:.1f}%")
        
        return report
    
    def run_diagnosis_and_planning(self):
        """运行诊断和规划阶段"""
        print("🚀 开始API集成问题修复...")
        
        # 执行诊断阶段
        self.stage1_diagnose_problems()
        
        # 执行API修复规划
        self.stage2_fix_api_interface()
        
        # 执行API测试
        self.stage3_test_api_functionality()
        
        # 生成报告
        report = self.generate_fix_report()
        
        print("\n🎉 API集成问题诊断和修复规划完成！")
        print("📋 下一步：根据诊断结果手动执行具体的代码修复")
        
        return report

def main():
    """主函数"""
    fixer = APIIntegrationFixer()
    report = fixer.run_diagnosis_and_planning()
    return report

if __name__ == "__main__":
    main()
