# 阶段4：服务启动和连接测试

**阶段状态**: 未开始  
**预计耗时**: 30分钟  
**依赖关系**: 阶段3完成  

## 📋 阶段目标

重启服务，测试WebSocket连接和功能，验证修复效果。

## 🔧 具体任务

### 4.1 重启API服务

**操作内容**: 重启后端服务并检查WebSocket服务启动

**具体步骤**:
```bash
# 停止当前API服务（如果正在运行）
# 使用Ctrl+C或kill命令停止

# 重新启动API服务
cd /path/to/project
python src/api/production_main.py

# 检查服务启动日志
# 查找以下关键信息：
# - "✅ WebSocket管理器已初始化"
# - "✅ 事件总线初始化成功"
# - "WebSocket路由已注册"
```

**预期结果**: 
- API服务成功启动
- WebSocket服务正常初始化
- 所有WebSocket路由正确注册
- 日志显示无错误信息

**涉及端口**: 
- API服务: 8888
- WebSocket端点: ws://127.0.0.1:8888/ws/*

**依赖库**: 
- fastapi
- uvicorn
- websockets

### 4.2 测试WebSocket端点

**操作内容**: 测试所有WebSocket端点的可访问性

**具体步骤**:
```bash
# 运行WebSocket连接测试脚本
python test_websocket_connection.py

# 手动测试主要端点
curl -i -N -H "Connection: Upgrade" \
     -H "Upgrade: websocket" \
     -H "Sec-WebSocket-Version: 13" \
     -H "Sec-WebSocket-Key: test" \
     http://127.0.0.1:8888/ws/bug-detection

# 检查健康检查端点
curl http://127.0.0.1:8888/api/v1/health/websocket
```

**测试端点列表**:
1. `/ws/bug-detection` - Bug检测WebSocket
2. `/ws/realtime-stats` - 实时统计WebSocket  
3. `/ws/training/{model_id}` - 训练监控WebSocket
4. `/api/v1/health/websocket` - WebSocket健康检查

**预期结果**: 
- 所有WebSocket端点响应正常
- 连接建立成功
- 测试脚本返回100%成功率
- 健康检查返回正常状态

**依赖库**: 
- websockets
- curl (系统工具)

### 4.3 重启Streamlit界面

**操作内容**: 重启前端界面并检查连接状态

**具体步骤**:
```bash
# 停止当前Streamlit服务（如果正在运行）
# 使用Ctrl+C停止

# 重新启动Streamlit界面
streamlit run src/ui/main.py --server.port 8501

# 等待服务启动完成
# 查看启动日志确认无错误
```

**预期结果**: 
- Streamlit服务成功启动
- 界面可以正常访问
- 无启动错误或警告
- 页面加载正常

**涉及端口**: 
- Streamlit服务: 8501
- 访问地址: http://127.0.0.1:8501

**依赖库**: 
- streamlit
- 所有UI相关依赖

### 4.4 验证连接状态显示

**操作内容**: 确认页面显示'WebSocket: 已连接'

**具体步骤**:
1. 打开浏览器访问 http://127.0.0.1:8501
2. 等待页面完全加载
3. 检查页面底部的连接状态显示
4. 验证状态从"已断开"变为"已连接"
5. 测试重连按钮功能（如果连接失败）

**检查要点**:
- 连接状态显示区域
- 状态文本内容
- 状态图标颜色
- 功能说明文本
- 重连按钮可用性

**预期结果**: 
- 显示"🔗 WebSocket: 已连接"
- 显示"✨ 实时功能已启用：Bug检测、训练监控、状态推送"
- 状态为绿色成功样式
- 无错误或警告信息

**依赖库**: 
- streamlit
- websockets (间接)

## 📋 创建健康检查脚本

**文件路径**: `websocket_health_check.py` (新建)

**脚本内容**:
```python
#!/usr/bin/env python3
"""
WebSocket健康检查脚本
用于监控WebSocket服务健康状态
"""

import json
import logging
import time
import requests
from typing import Dict, Any

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebSocketHealthChecker:
    """WebSocket健康检查器"""
    
    def __init__(self, api_base: str = "http://127.0.0.1:8888"):
        self.api_base = api_base
    
    def check_api_health(self) -> Dict[str, Any]:
        """检查API服务健康状态"""
        try:
            response = requests.get(f"{self.api_base}/health", timeout=5)
            return {
                'status': 'healthy' if response.status_code == 200 else 'unhealthy',
                'data': response.json() if response.status_code == 200 else None,
                'error': None
            }
        except Exception as e:
            return {'status': 'error', 'data': None, 'error': str(e)}
    
    def check_websocket_health(self) -> Dict[str, Any]:
        """检查WebSocket服务健康状态"""
        try:
            response = requests.get(f"{self.api_base}/api/v1/health/websocket", timeout=5)
            return {
                'status': 'healthy' if response.status_code == 200 else 'unhealthy',
                'data': response.json() if response.status_code == 200 else None,
                'error': None
            }
        except Exception as e:
            return {'status': 'error', 'data': None, 'error': str(e)}
    
    def generate_health_report(self) -> Dict[str, Any]:
        """生成健康检查报告"""
        api_health = self.check_api_health()
        websocket_health = self.check_websocket_health()
        
        overall_status = 'healthy'
        if api_health['status'] != 'healthy' or websocket_health['status'] != 'healthy':
            overall_status = 'unhealthy'
        
        return {
            'timestamp': time.time(),
            'overall_status': overall_status,
            'api_health': api_health,
            'websocket_health': websocket_health
        }

def main():
    """主函数"""
    checker = WebSocketHealthChecker()
    report = checker.generate_health_report()
    
    print(json.dumps(report, indent=2))
    
    # 保存报告
    with open(f'health_report_{int(time.time())}.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    return 0 if report['overall_status'] == 'healthy' else 1

if __name__ == "__main__":
    exit(main())
```

## ✅ 验收标准

- [ ] API服务成功重启
- [ ] WebSocket服务正常初始化
- [ ] 所有WebSocket端点测试通过
- [ ] Streamlit界面成功重启
- [ ] 页面显示"WebSocket: 已连接"
- [ ] 健康检查脚本创建完成
- [ ] 健康检查返回正常状态
- [ ] 无错误或警告信息

## 🚨 注意事项

1. **服务顺序**: 先启动API服务，再启动Streamlit界面
2. **端口冲突**: 确保8888和8501端口未被占用
3. **网络连接**: 确保本地网络连接正常
4. **防火墙**: 检查防火墙是否阻止WebSocket连接
5. **浏览器缓存**: 清除浏览器缓存以确保显示最新状态

## 🔧 故障排除

### 常见问题

1. **API服务启动失败**
   - 检查端口8888是否被占用
   - 检查依赖是否正确安装
   - 查看详细错误日志

2. **WebSocket连接失败**
   - 检查防火墙设置
   - 验证WebSocket路由注册
   - 检查网络连接

3. **Streamlit界面异常**
   - 清除浏览器缓存
   - 检查端口8501可用性
   - 重启Streamlit服务

### 解决方案

```bash
# 检查端口占用
netstat -an | grep :8888
netstat -an | grep :8501

# 强制停止占用端口的进程
lsof -ti:8888 | xargs kill -9
lsof -ti:8501 | xargs kill -9

# 清除Python缓存
find . -name "*.pyc" -delete
find . -name "__pycache__" -type d -exec rm -rf {} +
```

## 📝 执行记录

### 执行时间
- 开始时间: ___________
- 结束时间: ___________
- 实际耗时: ___________

### 执行结果
- [ ] 任务4.1完成
- [ ] 任务4.2完成
- [ ] 任务4.3完成
- [ ] 任务4.4完成
- [ ] 阶段验收通过

### 问题记录
- 遇到的问题: ___________
- 解决方案: ___________
- 经验教训: ___________

## 🔄 下一阶段

完成本阶段后，继续执行 [阶段5：验证和优化](./阶段5-验证和优化.md)
