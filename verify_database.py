#!/usr/bin/env python3
"""
验证数据库功能
"""

import sqlite3
import uuid

def verify_database():
    """验证数据库功能"""
    print("🗄️ 验证数据库功能...")
    
    try:
        conn = sqlite3.connect('data/bug_detection.db')
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(bug_reports)")
        columns = cursor.fetchall()
        print(f"📊 数据库列数: {len(columns)}")
        
        # 检查关键新增列
        column_names = [col[1] for col in columns]
        required_columns = ['environment', 'category', 'priority', 'tags', 'source']
        
        missing_columns = [col for col in required_columns if col not in column_names]
        if missing_columns:
            print(f"❌ 缺失列: {missing_columns}")
            return False
        else:
            print("✅ 所有必需列都存在")
        
        # 测试插入功能
        test_id = str(uuid.uuid4())[:8]
        cursor.execute("""
            INSERT INTO bug_reports 
            (id, error_type, severity, environment, category, priority, tags, source)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (test_id, 'test_error', 'medium', 'test', 'validation', 'high', 'test', 'ai_verification'))
        
        conn.commit()
        
        # 验证插入
        cursor.execute("SELECT COUNT(*) FROM bug_reports WHERE id = ?", (test_id,))
        count = cursor.fetchone()[0]
        
        if count > 0:
            print("✅ 数据插入测试成功")
            
            # 清理测试数据
            cursor.execute("DELETE FROM bug_reports WHERE id = ?", (test_id,))
            conn.commit()
            print("🧹 测试数据已清理")
        else:
            print("❌ 数据插入测试失败")
            return False
        
        conn.close()
        print("🎉 数据库功能验证完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据库验证失败: {e}")
        return False

if __name__ == "__main__":
    verify_database()
