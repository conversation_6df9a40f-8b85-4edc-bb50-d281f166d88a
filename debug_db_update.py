#!/usr/bin/env python3
"""
调试数据库更新问题
"""

import sqlite3
import sys
import os
from pathlib import Path
from datetime import datetime

def find_project_root():
    """查找项目根目录"""
    # 从当前文件位置开始向上查找
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 向上查找，直到找到包含data目录的目录
    check_dir = current_dir
    while check_dir != os.path.dirname(check_dir):
        data_dir = os.path.join(check_dir, 'data')
        if os.path.exists(data_dir):
            return check_dir
        check_dir = os.path.dirname(check_dir)
    
    # 如果找不到，使用当前工作目录
    return os.getcwd()

def resolve_db_path():
    """解析数据库文件的绝对路径"""
    project_root = find_project_root()
    return os.path.join(project_root, "data", "model_library.db")

def test_database_update():
    """测试数据库更新功能"""
    print("🔍 测试数据库更新功能...")
    print("=" * 60)
    
    # 获取数据库路径
    db_path = resolve_db_path()
    print(f"📁 项目根目录: {find_project_root()}")
    print(f"📁 数据库路径: {db_path}")
    print(f"📁 数据库存在: {Path(db_path).exists()}")
    
    if not Path(db_path).exists():
        print("❌ 数据库文件不存在")
        return
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='model_states'")
            table_schema = cursor.fetchone()
            if table_schema:
                print(f"\n📋 model_states表结构:")
                print(table_schema[0])
            else:
                print("❌ model_states表不存在")
                return
            
            print("\n" + "=" * 60)
            
            # 查询当前状态
            cursor.execute('''
                SELECT model_id, status, data_ready, features_ready, trained, 
                       training_data_size, last_training_time, last_check_time, error_message
                FROM model_states
                WHERE model_id IN ('markov_enhanced', 'deep_learning_cnn_lstm')
                ORDER BY model_id
            ''')
            
            results = cursor.fetchall()
            print("📋 当前数据库状态:")
            
            for row in results:
                model_id, status, data_ready, features_ready, trained, training_data_size, last_training_time, last_check_time, error_message = row
                
                print(f"\n🤖 模型: {model_id}")
                print(f"  状态: {status}")
                print(f"  数据就绪: {data_ready}")
                print(f"  特征就绪: {features_ready}")
                print(f"  已训练: {trained} (类型: {type(trained)})")
                print(f"  训练数据量: {training_data_size}")
                print(f"  最后训练时间: {last_training_time}")
                print(f"  最后检查时间: {last_check_time}")
                print(f"  错误信息: {error_message}")
            
            print("\n" + "=" * 60)
            
            # 测试手动更新markov_enhanced模型状态
            print("🔧 测试手动更新markov_enhanced模型状态...")
            
            cursor.execute('''
                INSERT OR REPLACE INTO model_states
                (model_id, status, data_ready, features_ready, trained,
                 up_to_date, training_data_size, last_training_time,
                 last_check_time, error_message)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                'markov_enhanced',
                'trained',  # 状态设为已训练
                True,  # 数据就绪
                True,  # 特征就绪
                True,  # 已训练
                True,  # 最新
                8346,  # 训练数据量
                datetime.now().isoformat(),  # 训练时间
                datetime.now().isoformat(),  # 检查时间
                None  # 无错误
            ))
            conn.commit()
            print("✅ 手动更新成功")
            
            # 验证更新结果
            cursor.execute('''
                SELECT model_id, status, trained, last_training_time
                FROM model_states 
                WHERE model_id = 'markov_enhanced'
            ''')
            
            result = cursor.fetchone()
            if result:
                model_id, status, trained, last_training_time = result
                print(f"\n✅ 验证更新结果:")
                print(f"  模型ID: {model_id}")
                print(f"  状态: {status}")
                print(f"  已训练: {trained} (类型: {type(trained)})")
                print(f"  最后训练时间: {last_training_time}")
            else:
                print("❌ 验证失败：未找到更新后的记录")
                
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_database_update()
