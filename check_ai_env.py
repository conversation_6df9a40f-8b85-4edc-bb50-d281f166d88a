#!/usr/bin/env python3
"""
检查AI依赖库环境
"""

import sys
import pkg_resources

def check_ai_environment():
    """检查AI环境"""
    print("🔍 检查AI依赖库环境...")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    # 需要检查的AI库
    ai_libraries = [
        'transformers',
        'sentence-transformers', 
        'scikit-learn',
        'sklearn',
        'torch',
        'numpy',
        'pandas',
        'nltk',
        'spacy',
        'accelerate',
        'datasets',
        'tokenizers'
    ]
    
    print("\n📦 检查AI库安装状态:")
    installed = []
    missing = []
    
    for lib in ai_libraries:
        try:
            __import__(lib)
            print(f"✅ {lib} - 已安装")
            installed.append(lib)
        except ImportError:
            print(f"❌ {lib} - 缺失")
            missing.append(lib)
    
    print(f"\n📊 统计:")
    print(f"已安装: {len(installed)}")
    print(f"缺失: {len(missing)}")
    
    if missing:
        print(f"\n🚨 缺失的库: {missing}")
    
    # 检查关键功能
    print("\n🧪 检查关键功能:")
    
    # 检查TfidfVectorizer
    try:
        from sklearn.feature_extraction.text import TfidfVectorizer
        print("✅ TfidfVectorizer - 可用")
    except ImportError:
        print("❌ TfidfVectorizer - 不可用")
    
    # 检查transformers
    try:
        import transformers
        print(f"✅ Transformers - 版本 {transformers.__version__}")
    except ImportError:
        print("❌ Transformers - 不可用")
    
    return missing

if __name__ == "__main__":
    missing_libs = check_ai_environment()
    if not missing_libs:
        print("\n🎉 所有AI库都已安装！")
    else:
        print(f"\n⚠️ 需要安装 {len(missing_libs)} 个库")
