#!/usr/bin/env python3
"""
Streamlit演示应用

展示完整的福彩3D预测分析工具界面功能
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import sys
sys.path.append('src')

from ui.demo_data import (
    get_demo_basic_stats, get_demo_frequency_data, 
    get_demo_sum_distribution, get_demo_sales_data,
    get_demo_query_data, get_demo_trends_data
)

# 页面配置
st.set_page_config(
    page_title="福彩3D预测分析工具",
    page_icon="🎯",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .demo-badge {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 0.25rem;
        padding: 0.5rem;
        margin: 1rem 0;
        text-align: center;
        font-weight: bold;
        color: #856404;
    }
    .success-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 0.25rem;
        padding: 0.75rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def main():
    """主应用函数"""
    
    # 主标题
    st.markdown('<h1 class="main-header">🎯 福彩3D预测分析工具</h1>', unsafe_allow_html=True)
    
    # 演示模式提示
    st.markdown('<div class="demo-badge">🚀 演示模式 - 展示完整功能界面</div>', unsafe_allow_html=True)
    st.markdown('<div class="success-box">✅ Streamlit应用运行正常 - 界面功能完整</div>', unsafe_allow_html=True)
    
    # 侧边栏导航
    st.sidebar.title("📊 功能导航")
    page = st.sidebar.selectbox(
        "选择功能页面",
        ["📈 数据概览", "🔢 频率分析", "📊 和值分布", "💰 销售分析", "🔍 数据查询", "🎯 预测分析"]
    )
    
    # 显示系统状态
    st.sidebar.markdown("### 📋 系统状态")
    st.sidebar.metric("数据库记录", "8,341")
    st.sidebar.text("数据范围: 2002-2025")
    st.sidebar.markdown("### 🔧 技术栈")
    st.sidebar.text("• Streamlit 1.46.1")
    st.sidebar.text("• Plotly 6.2.0")
    st.sidebar.text("• Pandas 2.3.1")
    st.sidebar.text("• FastAPI 0.115.6")
    
    # 根据选择显示不同页面
    if page == "📈 数据概览":
        show_overview_demo()
    elif page == "🔢 频率分析":
        show_frequency_demo()
    elif page == "📊 和值分布":
        show_sum_distribution_demo()
    elif page == "💰 销售分析":
        show_sales_demo()
    elif page == "🔍 数据查询":
        show_query_demo()
    elif page == "🎯 预测分析":
        show_prediction_demo()

def show_overview_demo():
    """显示数据概览演示页面"""
    st.header("📈 数据概览")
    
    demo_stats = get_demo_basic_stats()
    
    # 显示关键指标
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "总记录数",
            f"{demo_stats['total_records']:,}",
            help="历史开奖记录总数"
        )
    
    with col2:
        st.metric(
            "数据范围",
            f"{demo_stats['date_range']['end'][:4]}年",
            f"从{demo_stats['date_range']['start'][:4]}年开始",
            help="数据覆盖的年份范围"
        )
    
    with col3:
        st.metric(
            "平均和值",
            f"{demo_stats['sum_value_stats']['mean']:.1f}",
            help="所有开奖号码的平均和值"
        )
    
    with col4:
        st.metric(
            "总销售额",
            f"{demo_stats['sales_amount_stats']['total']/100000000:.0f}亿",
            help="历史总销售额"
        )
    
    # 详细统计信息
    st.subheader("📊 详细统计")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 和值统计")
        sum_stats = demo_stats['sum_value_stats']
        st.write(f"- 最小值: {sum_stats['min']}")
        st.write(f"- 最大值: {sum_stats['max']}")
        st.write(f"- 平均值: {sum_stats['mean']:.2f}")
        st.write(f"- 中位数: {sum_stats['median']}")
    
    with col2:
        st.markdown("#### 跨度统计")
        span_stats = demo_stats['span_value_stats']
        st.write(f"- 最小值: {span_stats['min']}")
        st.write(f"- 最大值: {span_stats['max']}")
        st.write(f"- 平均值: {span_stats['mean']:.2f}")
    
    st.success(f"⚡ 查询耗时: {demo_stats['query_time_ms']:.2f}ms")

def show_frequency_demo():
    """显示频率分析演示页面"""
    st.header("🔢 频率分析")
    
    # 位置选择
    position = st.selectbox(
        "选择分析位置",
        ["all", "hundreds", "tens", "units"],
        format_func=lambda x: {"all": "全部位置", "hundreds": "百位", "tens": "十位", "units": "个位"}[x]
    )
    
    demo_freq = get_demo_frequency_data()
    
    # 根据选择显示不同位置的分析
    positions_to_show = ["hundreds", "tens", "units"] if position == "all" else [position]
    
    for pos in positions_to_show:
        if pos in demo_freq:
            pos_name = {"hundreds": "百位", "tens": "十位", "units": "个位"}[pos]
            st.subheader(f"📊 {pos_name}数字频率")
            
            # 转换数据为DataFrame
            df = pd.DataFrame(demo_freq[pos])
            if not df.empty:
                # 创建柱状图
                fig = px.bar(
                    df, 
                    x='digit', 
                    y='count',
                    title=f"{pos_name}数字出现频率",
                    labels={'digit': '数字', 'count': '出现次数'},
                    color='count',
                    color_continuous_scale='viridis'
                )
                fig.update_layout(xaxis_type='category')
                st.plotly_chart(fig, use_container_width=True)
                
                # 显示数据表
                st.dataframe(df, use_container_width=True)
    
    st.success(f"⚡ 查询耗时: {demo_freq['query_time_ms']:.2f}ms")

def show_sum_distribution_demo():
    """显示和值分布演示页面"""
    st.header("📊 和值分布分析")
    
    demo_sum = get_demo_sum_distribution()
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("🎯 正式开奖号码和值分布")
        df = pd.DataFrame(demo_sum['sum_distribution'])
        
        # 创建分布图
        fig = px.line(
            df, 
            x='sum_value', 
            y='count',
            title="正式开奖号码和值分布",
            labels={'sum_value': '和值', 'count': '出现次数'},
            markers=True
        )
        fig.update_traces(line=dict(width=3))
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.subheader("🎲 试机号码和值分布")
        df = pd.DataFrame(demo_sum['trial_sum_distribution'])
        
        # 创建分布图
        fig = px.line(
            df, 
            x='sum_value', 
            y='count',
            title="试机号码和值分布",
            labels={'sum_value': '和值', 'count': '出现次数'},
            line_shape='spline',
            markers=True
        )
        fig.update_traces(line=dict(width=3, color='orange'))
        st.plotly_chart(fig, use_container_width=True)
    
    st.success(f"⚡ 查询耗时: {demo_sum['query_time_ms']:.2f}ms")

def show_sales_demo():
    """显示销售分析演示页面"""
    st.header("💰 销售额分析")
    
    demo_sales = get_demo_sales_data()
    
    # 总体统计
    overall = demo_sales['overall']
    
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("总销售额", f"{overall['total_sales']/100000000:.1f}亿元")
    with col2:
        st.metric("平均销售额", f"{overall['avg_sales']/10000:.1f}万元")
    with col3:
        st.metric("最高销售额", f"{overall['max_sales']/10000:.1f}万元")
    
    # 年度销售趋势
    st.subheader("📈 年度销售趋势")
    df = pd.DataFrame(demo_sales['yearly'])
    
    fig = go.Figure()
    fig.add_trace(go.Scatter(
        x=df['year'],
        y=df['total_sales']/100000000,
        mode='lines+markers',
        name='年度销售额(亿元)',
        line=dict(width=4),
        marker=dict(size=8)
    ))
    
    fig.update_layout(
        title="年度销售额趋势",
        xaxis_title="年份",
        yaxis_title="销售额(亿元)",
        hovermode='x'
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 显示数据表
    st.dataframe(df, use_container_width=True)
    
    st.success(f"⚡ 查询耗时: {demo_sales['query_time_ms']:.2f}ms")

def show_query_demo():
    """显示数据查询演示页面"""
    st.header("🔍 数据查询")
    
    # 查询选项
    query_type = st.radio(
        "选择查询类型",
        ["按日期范围", "按和值范围"]
    )
    
    if query_type == "按日期范围":
        col1, col2 = st.columns(2)
        with col1:
            start_date = st.date_input("开始日期", value=pd.to_datetime("2025-07-01").date())
        with col2:
            end_date = st.date_input("结束日期", value=pd.to_datetime("2025-07-13").date())
        
        if st.button("查询数据"):
            demo_result = get_demo_query_data("date", 10)
            
            st.success(f"查询到 {demo_result['total_count']} 条记录")
            
            # 转换为DataFrame并显示
            df = pd.DataFrame(demo_result['records'])
            st.dataframe(df, use_container_width=True)
            
            st.success(f"⚡ 查询耗时: {demo_result['query_time_ms']:.2f}ms")
    
    else:  # 按和值范围
        col1, col2 = st.columns(2)
        with col1:
            min_sum = st.number_input("最小和值", min_value=0, max_value=27, value=10)
        with col2:
            max_sum = st.number_input("最大和值", min_value=0, max_value=27, value=15)
        
        if st.button("查询数据"):
            demo_result = get_demo_query_data("sum", 8)
            
            st.success(f"查询到 {demo_result['total_count']} 条记录")
            
            # 转换为DataFrame并显示
            df = pd.DataFrame(demo_result['records'])
            st.dataframe(df, use_container_width=True)
            
            st.success(f"⚡ 查询耗时: {demo_result['query_time_ms']:.2f}ms")

def show_prediction_demo():
    """显示预测分析演示页面"""
    st.header("🎯 预测分析")
    
    st.info("🚧 预测功能演示界面")
    
    # 预测功能的界面框架
    st.subheader("📊 预测模型选择")
    
    col1, col2 = st.columns(2)
    with col1:
        model_type = st.selectbox("选择预测模型", ["频率分析模型", "趋势分析模型", "机器学习模型"])
    with col2:
        predict_periods = st.number_input("预测期数", min_value=1, max_value=10, value=1)
    
    if st.button("开始预测"):
        st.success("🎯 预测功能演示")
        
        # 显示模拟预测结果
        st.subheader("📈 预测结果")
        
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("推荐号码", "3 7 9", help="基于历史数据分析的推荐号码")
        with col2:
            st.metric("置信度", "78%", help="预测结果的置信度")
        with col3:
            st.metric("预测期号", "2025195", help="下一期期号")
        
        # 预测分析图表
        demo_trends = get_demo_trends_data()
        
        st.subheader("📊 趋势分析")
        trend_data = {
            "指标": ["和值趋势", "销售额趋势", "跨度趋势"],
            "当前值": [13.2, 4500, 4.9],
            "预测值": [13.8, 4650, 5.1],
            "变化": ["+0.6", "+150", "+0.2"]
        }
        
        df = pd.DataFrame(trend_data)
        st.dataframe(df, use_container_width=True)
        
        st.success(f"⚡ 预测耗时: {demo_trends['query_time_ms']:.2f}ms")
    
    st.markdown("""
    ### 🔮 预测功能特性
    
    - **频率分析预测**: 基于历史频率数据的统计预测
    - **趋势分析预测**: 基于数据趋势变化的预测
    - **机器学习预测**: 基于深度学习模型的智能预测
    - **组合预测**: 多模型融合的综合预测
    - **实时更新**: 随着新数据自动更新预测模型
    """)

if __name__ == "__main__":
    main()
