"""
模型库自定义异常类
"""


class ModelLibraryError(Exception):
    """模型库基础异常类"""
    pass


class ModelNotFoundError(ModelLibraryError):
    """模型未找到异常"""
    def __init__(self, model_id: str):
        self.model_id = model_id
        super().__init__(f"模型 '{model_id}' 未找到")


class ModelTrainingError(ModelLibraryError):
    """模型训练异常"""
    def __init__(self, model_id: str, message: str):
        self.model_id = model_id
        super().__init__(f"模型 '{model_id}' 训练失败: {message}")


class PredictionError(ModelLibraryError):
    """预测异常"""
    def __init__(self, model_id: str, message: str):
        self.model_id = model_id
        super().__init__(f"模型 '{model_id}' 预测失败: {message}")


class ModelValidationError(ModelLibraryError):
    """模型验证异常"""
    def __init__(self, model_id: str, message: str):
        self.model_id = model_id
        super().__init__(f"模型 '{model_id}' 验证失败: {message}")


class ModelRegistrationError(ModelLibraryError):
    """模型注册异常"""
    def __init__(self, model_id: str, message: str):
        self.model_id = model_id
        super().__init__(f"模型 '{model_id}' 注册失败: {message}")


class ModelStatusError(ModelLibraryError):
    """模型状态异常"""
    def __init__(self, model_id: str, message: str):
        self.model_id = model_id
        super().__init__(f"模型 '{model_id}' 状态异常: {message}")
