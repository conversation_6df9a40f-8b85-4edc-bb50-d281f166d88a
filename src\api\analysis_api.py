#!/usr/bin/env python3
"""
分析API端点
Analysis API Endpoints

提供预测分析相关的API接口
"""

import asyncio
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import BackgroundTasks, FastAPI, HTTPException
from pydantic import BaseModel

from ..analysis.model_weakness_identifier import ModelWeaknessIdentifier
from ..analysis.prediction_deviation_analyzer import \
    PredictionDeviationAnalyzer
from ..analysis.success_factor_extractor import SuccessFactorExtractor
from ..core.draw_trigger_system import DrawTriggerSystem
# 导入系统组件
from ..core.unified_prediction_storage import UnifiedPredictionStorage
from ..optimization.auto_parameter_applier import AutoParameterApplier
from ..optimization.optimization_advisor import OptimizationAdvisor
from ..optimization.parameter_backtesting_engine import \
    ParameterBacktestingEngine


# 请求模型
class ValidationRequest(BaseModel):
    """验证分析请求"""
    period_number: str
    actual_numbers: str
    source: str = "manual"


class OptimizationRequest(BaseModel):
    """优化请求"""
    model_name: str
    analysis_results: Dict[str, Any]


class ParameterApplicationRequest(BaseModel):
    """参数应用请求"""
    optimizations: Dict[str, Any]


# 响应模型
class AnalysisResponse(BaseModel):
    """分析响应"""
    success: bool
    period_number: str
    total_predictions: int
    successful_predictions: int
    analysis_details: Dict[str, Any]
    optimization_suggestions: Dict[str, Any]
    timestamp: datetime


class OptimizationResponse(BaseModel):
    """优化响应"""
    success: bool
    model_name: str
    suggestions_count: int
    priority_level: str
    expected_improvements: Dict[str, float]
    confidence_score: float


# 初始化系统组件
storage = UnifiedPredictionStorage()
draw_trigger_system = DrawTriggerSystem(storage)
deviation_analyzer = PredictionDeviationAnalyzer()
weakness_identifier = ModelWeaknessIdentifier()
success_extractor = SuccessFactorExtractor()
optimization_advisor = OptimizationAdvisor()
backtesting_engine = ParameterBacktestingEngine()
parameter_applier = AutoParameterApplier()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="预测分析API",
    description="福彩3D预测-验证-优化闭环智能系统API",
    version="1.0.0"
)


@app.post("/api/v1/analysis/trigger-validation", response_model=AnalysisResponse)
async def trigger_validation_analysis(request: ValidationRequest, background_tasks: BackgroundTasks):
    """
    手动触发验证分析
    
    Args:
        request: 验证分析请求
        background_tasks: 后台任务
        
    Returns:
        分析响应
    """
    try:
        logger.info(f"触发验证分析: {request.period_number} - {request.actual_numbers}")
        
        # 执行分析
        result = await draw_trigger_system.on_draw_announced(
            request.period_number,
            request.actual_numbers,
            request.source
        )
        
        return AnalysisResponse(
            success=True,
            period_number=result.period_number,
            total_predictions=result.total_predictions,
            successful_predictions=result.successful_predictions,
            analysis_details=result.analysis_details,
            optimization_suggestions=result.optimization_suggestions,
            timestamp=result.timestamp
        )
        
    except Exception as e:
        logger.error(f"触发验证分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")


@app.get("/api/v1/analysis/period/{period_number}")
async def get_period_analysis(period_number: str):
    """
    获取指定期号的分析结果
    
    Args:
        period_number: 期号
        
    Returns:
        分析结果
    """
    try:
        logger.info(f"获取期号分析: {period_number}")
        
        # 获取预测记录
        predictions = storage.get_period_predictions(period_number)
        
        if not predictions:
            raise HTTPException(status_code=404, detail=f"期号 {period_number} 没有找到预测记录")
        
        # 构建分析结果
        analysis_result = {
            'period_number': period_number,
            'total_predictions': len(predictions),
            'verified_predictions': len([p for p in predictions if p.is_verified]),
            'predictions': []
        }
        
        # 添加预测详情
        for prediction in predictions:
            pred_detail = {
                'model_name': prediction.model_name,
                'predicted_numbers': prediction.predicted_numbers,
                'confidence': prediction.confidence,
                'actual_numbers': prediction.actual_numbers,
                'is_verified': prediction.is_verified,
                'accuracy_score': prediction.accuracy_score,
                'prediction_time': prediction.prediction_time.isoformat() if prediction.prediction_time else None
            }
            
            # 如果已验证，添加偏差分析
            if prediction.is_verified and prediction.actual_numbers:
                try:
                    deviation_result = deviation_analyzer.analyze_deviation(prediction, prediction.actual_numbers)
                    pred_detail['deviation_analysis'] = {
                        'overall_score': deviation_result.overall_score,
                        'numerical_deviation': deviation_result.numerical_deviation,
                        'confidence_calibration': deviation_result.confidence_calibration
                    }
                except Exception as e:
                    logger.error(f"偏差分析失败: {e}")
                    pred_detail['deviation_analysis'] = {'error': str(e)}
            
            analysis_result['predictions'].append(pred_detail)
        
        return analysis_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取期号分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取分析失败: {str(e)}")


@app.get("/api/v1/analysis/model/{model_name}/weaknesses")
async def get_model_weaknesses(model_name: str, limit: int = 100):
    """
    获取模型弱点分析
    
    Args:
        model_name: 模型名称
        limit: 历史记录限制
        
    Returns:
        弱点分析结果
    """
    try:
        logger.info(f"获取模型弱点分析: {model_name}")
        
        # 获取模型预测历史
        prediction_history = storage.get_model_predictions(model_name, limit)
        
        if not prediction_history:
            raise HTTPException(status_code=404, detail=f"模型 {model_name} 没有找到预测记录")
        
        # 执行弱点识别
        weakness_result = weakness_identifier.identify_weaknesses(model_name, prediction_history)
        
        return {
            'model_name': weakness_result.model_name,
            'identified_weaknesses': weakness_result.identified_weaknesses,
            'severity_assessment': weakness_result.severity_assessment,
            'improvement_suggestions': weakness_result.improvement_suggestions,
            'confidence_score': weakness_result.confidence_score,
            'analysis_timestamp': weakness_result.analysis_timestamp.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模型弱点分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"弱点分析失败: {str(e)}")


@app.get("/api/v1/analysis/model/{model_name}/success-factors")
async def get_model_success_factors(model_name: str, limit: int = 100):
    """
    获取模型成功因子分析
    
    Args:
        model_name: 模型名称
        limit: 历史记录限制
        
    Returns:
        成功因子分析结果
    """
    try:
        logger.info(f"获取模型成功因子: {model_name}")
        
        # 获取模型预测历史
        prediction_history = storage.get_model_predictions(model_name, limit)
        
        if not prediction_history:
            raise HTTPException(status_code=404, detail=f"模型 {model_name} 没有找到预测记录")
        
        # 执行成功因子提取
        success_result = success_extractor.extract_success_factors(model_name, prediction_history)
        
        return {
            'model_name': success_result.model_name,
            'top_factors': success_result.top_factors,
            'detailed_analysis': success_result.detailed_analysis,
            'amplification_suggestions': success_result.amplification_suggestions,
            'replication_strategy': success_result.replication_strategy,
            'confidence_score': success_result.confidence_score,
            'analysis_timestamp': success_result.analysis_timestamp.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取成功因子分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"成功因子分析失败: {str(e)}")


@app.post("/api/v1/optimization/suggestions", response_model=OptimizationResponse)
async def get_optimization_suggestions(request: OptimizationRequest):
    """
    获取优化建议
    
    Args:
        request: 优化请求
        
    Returns:
        优化建议响应
    """
    try:
        logger.info(f"生成优化建议: {request.model_name}")
        
        # 生成优化建议
        optimization_result = optimization_advisor.generate_suggestions(
            request.model_name,
            request.analysis_results
        )
        
        return OptimizationResponse(
            success=True,
            model_name=optimization_result.model_name,
            suggestions_count=len(optimization_result.optimization_strategies),
            priority_level=optimization_result.priority_level.value,
            expected_improvements=optimization_result.expected_improvements,
            confidence_score=optimization_result.confidence_score
        )
        
    except Exception as e:
        logger.error(f"生成优化建议失败: {e}")
        raise HTTPException(status_code=500, detail=f"优化建议生成失败: {str(e)}")


@app.post("/api/v1/optimization/apply-parameters")
async def apply_optimization_parameters(request: ParameterApplicationRequest):
    """
    应用优化参数
    
    Args:
        request: 参数应用请求
        
    Returns:
        应用结果
    """
    try:
        logger.info("应用优化参数")
        
        # 应用优化参数
        result = await parameter_applier.apply_optimizations(request.optimizations)
        
        return {
            'success': True,
            'application_results': {
                model_name: {
                    'success': app_result.success,
                    'error_message': app_result.error_message,
                    'rollback_available': app_result.rollback_available,
                    'application_timestamp': app_result.application_timestamp.isoformat() if app_result.application_timestamp else None
                }
                for model_name, app_result in result.items()
            }
        }
        
    except Exception as e:
        logger.error(f"应用优化参数失败: {e}")
        raise HTTPException(status_code=500, detail=f"参数应用失败: {str(e)}")


@app.get("/api/v1/system/status")
async def get_system_status():
    """
    获取系统状态
    
    Returns:
        系统状态信息
    """
    try:
        # 获取存储统计
        storage_stats = storage.get_statistics()
        
        # 获取触发系统状态
        trigger_status = draw_trigger_system.get_system_status()
        
        return {
            'system_status': 'healthy',
            'storage_statistics': storage_stats,
            'trigger_system_status': trigger_status,
            'components': {
                'unified_storage': 'active',
                'draw_trigger_system': 'active',
                'deviation_analyzer': 'active',
                'weakness_identifier': 'active',
                'success_extractor': 'active',
                'optimization_advisor': 'active',
                'backtesting_engine': 'active',
                'parameter_applier': 'active'
            },
            'timestamp': datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取系统状态失败: {str(e)}")


@app.get("/api/v1/system/health")
async def health_check():
    """
    健康检查端点
    
    Returns:
        健康状态
    """
    return {
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    }


if __name__ == "__main__":
    import uvicorn

    # 运行API服务器
    uvicorn.run(
        "analysis_api:app",
        host="127.0.0.1",
        port=8888,
        reload=True,
        log_level="info"
    )
