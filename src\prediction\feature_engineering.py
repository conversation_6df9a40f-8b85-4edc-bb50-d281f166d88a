"""
特征工程模块
整合基础统计特征、时间序列特征、组合特征和高级数学特征
基于参考文档的成功经验实现完整的特征工程管道
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

from .advanced_features import AdvancedFeatureExtractor


class FeatureEngineeringPipeline:
    """
    完整的特征工程管道
    整合基础统计、时间序列、组合特征和高级数学特征
    """
    
    def __init__(self):
        self.advanced_extractor = AdvancedFeatureExtractor()
        self.window_sizes = [5, 10, 20, 30]
        
    def extract_all_features(self, data: List[str], max_records: int = 1000) -> Dict[str, float]:
        """
        提取所有特征
        
        Args:
            data: 开奖号码列表
            max_records: 最大处理记录数
            
        Returns:
            包含所有特征的字典
        """
        if not data:
            return self._get_default_features()
            
        # 限制数据量
        if len(data) > max_records:
            data = data[-max_records:]
            
        features = {}
        
        try:
            # 1. 基础统计特征
            features.update(self._extract_basic_statistical_features(data))
            
            # 2. 时间序列特征
            features.update(self._extract_time_series_features(data))
            
            # 3. 组合特征
            features.update(self._extract_combination_features(data))
            
            # 4. 高级数学特征
            advanced_features = self.advanced_extractor.extract_all_features(data, max_records)
            features.update(advanced_features)
            
            # 5. 特征后处理
            features = self._post_process_features(features)
            
        except Exception as e:
            print(f"Error in feature extraction: {e}")
            features = self._get_default_features()
            
        return features
    
    def _extract_basic_statistical_features(self, data: List[str]) -> Dict[str, float]:
        """提取基础统计特征"""
        features = {}
        
        # 转换为数值序列
        sequences = self._convert_to_sequences(data)
        
        for seq_name, seq_data in sequences.items():
            if len(seq_data) > 0:
                # 基础统计量
                features[f'{seq_name}_mean'] = float(np.mean(seq_data))
                features[f'{seq_name}_std'] = float(np.std(seq_data))
                features[f'{seq_name}_var'] = float(np.var(seq_data))
                features[f'{seq_name}_min'] = float(np.min(seq_data))
                features[f'{seq_name}_max'] = float(np.max(seq_data))
                features[f'{seq_name}_range'] = float(np.max(seq_data) - np.min(seq_data))
                features[f'{seq_name}_median'] = float(np.median(seq_data))
                
                # 分位数
                features[f'{seq_name}_q25'] = float(np.percentile(seq_data, 25))
                features[f'{seq_name}_q75'] = float(np.percentile(seq_data, 75))
                features[f'{seq_name}_iqr'] = float(np.percentile(seq_data, 75) - np.percentile(seq_data, 25))
                
                # 偏度和峰度
                if len(seq_data) > 2:
                    features[f'{seq_name}_skewness'] = float(self._calculate_skewness(seq_data))
                    features[f'{seq_name}_kurtosis'] = float(self._calculate_kurtosis(seq_data))
                
                # 变异系数
                if np.mean(seq_data) != 0:
                    features[f'{seq_name}_cv'] = float(np.std(seq_data) / np.mean(seq_data))
                else:
                    features[f'{seq_name}_cv'] = 0.0
        
        return features
    
    def _extract_time_series_features(self, data: List[str]) -> Dict[str, float]:
        """提取时间序列特征"""
        features = {}
        
        sequences = self._convert_to_sequences(data)
        
        for seq_name, seq_data in sequences.items():
            if len(seq_data) < 5:
                continue
                
            # 滞后特征
            for lag in [1, 2, 3, 5, 10]:
                if len(seq_data) > lag:
                    lag_corr = self._calculate_lag_correlation(seq_data, lag)
                    features[f'{seq_name}_lag{lag}_corr'] = float(lag_corr)
            
            # 移动平均特征
            for window in self.window_sizes:
                if len(seq_data) >= window:
                    ma = self._calculate_moving_average(seq_data, window)
                    features[f'{seq_name}_ma{window}'] = float(ma[-1])  # 最新的移动平均值
                    
                    # 移动平均的趋势
                    if len(ma) > 1:
                        ma_trend = ma[-1] - ma[-2]
                        features[f'{seq_name}_ma{window}_trend'] = float(ma_trend)
            
            # 波动率特征
            for window in [5, 10, 20]:
                if len(seq_data) >= window:
                    volatility = self._calculate_volatility(seq_data, window)
                    features[f'{seq_name}_volatility{window}'] = float(volatility)
            
            # 动量特征
            for period in [3, 5, 10]:
                if len(seq_data) > period:
                    momentum = seq_data[-1] - seq_data[-period-1]
                    features[f'{seq_name}_momentum{period}'] = float(momentum)
        
        return features
    
    def _extract_combination_features(self, data: List[str]) -> Dict[str, float]:
        """提取组合特征"""
        features = {}
        
        if not data:
            return features
        
        # 最近N期的组合特征
        for window in [5, 10, 20]:
            recent_data = data[-window:] if len(data) >= window else data
            
            if recent_data:
                # 和值特征
                sums = [sum(int(d) for d in num) for num in recent_data]
                features[f'sum_mean_{window}'] = float(np.mean(sums))
                features[f'sum_std_{window}'] = float(np.std(sums))
                
                # 跨度特征
                spans = [max(int(d) for d in num) - min(int(d) for d in num) for num in recent_data]
                features[f'span_mean_{window}'] = float(np.mean(spans))
                features[f'span_std_{window}'] = float(np.std(spans))
                
                # 奇偶比特征
                odd_even_ratios = []
                for num in recent_data:
                    odd_count = sum(1 for d in num if int(d) % 2 == 1)
                    odd_even_ratios.append(odd_count / 3.0)
                features[f'odd_ratio_mean_{window}'] = float(np.mean(odd_even_ratios))
                
                # 大小比特征（>=5为大数）
                big_small_ratios = []
                for num in recent_data:
                    big_count = sum(1 for d in num if int(d) >= 5)
                    big_small_ratios.append(big_count / 3.0)
                features[f'big_ratio_mean_{window}'] = float(np.mean(big_small_ratios))
                
                # 质合比特征
                prime_composite_ratios = []
                primes = {2, 3, 5, 7}
                for num in recent_data:
                    prime_count = sum(1 for d in num if int(d) in primes)
                    prime_composite_ratios.append(prime_count / 3.0)
                features[f'prime_ratio_mean_{window}'] = float(np.mean(prime_composite_ratios))
                
                # 012路分布
                mod3_distributions = []
                for num in recent_data:
                    mod3_count = [0, 0, 0]
                    for d in num:
                        mod3_count[int(d) % 3] += 1
                    mod3_distributions.append(mod3_count)
                
                if mod3_distributions:
                    mod3_array = np.array(mod3_distributions)
                    for i in range(3):
                        features[f'mod3_{i}_mean_{window}'] = float(np.mean(mod3_array[:, i]))
        
        return features
    
    def _convert_to_sequences(self, data: List[str]) -> Dict[str, np.ndarray]:
        """转换为数值序列"""
        sequences = {
            'hundreds': [],
            'tens': [],
            'units': [],
            'sums': [],
            'spans': []
        }
        
        for number_str in data:
            if len(number_str) == 3:
                h, t, u = int(number_str[0]), int(number_str[1]), int(number_str[2])
                sequences['hundreds'].append(h)
                sequences['tens'].append(t)
                sequences['units'].append(u)
                sequences['sums'].append(h + t + u)
                sequences['spans'].append(max(h, t, u) - min(h, t, u))
        
        # 转换为numpy数组
        for key in sequences:
            sequences[key] = np.array(sequences[key])
        
        return sequences
    
    def _calculate_skewness(self, data: np.ndarray) -> float:
        """计算偏度"""
        if len(data) < 3:
            return 0.0
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0.0
        return np.mean(((data - mean) / std) ** 3)
    
    def _calculate_kurtosis(self, data: np.ndarray) -> float:
        """计算峰度"""
        if len(data) < 4:
            return 0.0
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0.0
        return np.mean(((data - mean) / std) ** 4) - 3
    
    def _calculate_lag_correlation(self, data: np.ndarray, lag: int) -> float:
        """计算滞后相关性"""
        if len(data) <= lag:
            return 0.0
        try:
            corr = np.corrcoef(data[:-lag], data[lag:])[0, 1]
            return corr if not np.isnan(corr) else 0.0
        except:
            return 0.0
    
    def _calculate_moving_average(self, data: np.ndarray, window: int) -> np.ndarray:
        """计算移动平均"""
        if len(data) < window:
            return np.array([np.mean(data)])
        
        ma = []
        for i in range(window-1, len(data)):
            ma.append(np.mean(data[i-window+1:i+1]))
        return np.array(ma)
    
    def _calculate_volatility(self, data: np.ndarray, window: int) -> float:
        """计算波动率"""
        if len(data) < window:
            return np.std(data)
        
        recent_data = data[-window:]
        return np.std(recent_data)
    
    def _post_process_features(self, features: Dict[str, float]) -> Dict[str, float]:
        """特征后处理"""
        processed_features = {}
        
        for key, value in features.items():
            # 处理异常值
            if np.isnan(value) or np.isinf(value):
                processed_features[key] = 0.0
            else:
                # 限制特征值范围，避免极端值
                processed_features[key] = float(np.clip(value, -1e6, 1e6))
        
        return processed_features
    
    def _get_default_features(self) -> Dict[str, float]:
        """返回默认特征值"""
        default_features = {}
        
        # 基础统计特征默认值
        seq_types = ['hundreds', 'tens', 'units', 'sums', 'spans']
        stats = ['mean', 'std', 'var', 'min', 'max', 'range', 'median', 'q25', 'q75', 'iqr', 'skewness', 'kurtosis', 'cv']
        
        for seq_type in seq_types:
            for stat in stats:
                default_features[f'{seq_type}_{stat}'] = 0.0
        
        # 时间序列特征默认值
        for seq_type in seq_types:
            for lag in [1, 2, 3, 5, 10]:
                default_features[f'{seq_type}_lag{lag}_corr'] = 0.0
            
            for window in self.window_sizes:
                default_features[f'{seq_type}_ma{window}'] = 0.0
                default_features[f'{seq_type}_ma{window}_trend'] = 0.0
                default_features[f'{seq_type}_volatility{window}'] = 0.0
            
            for period in [3, 5, 10]:
                default_features[f'{seq_type}_momentum{period}'] = 0.0
        
        # 组合特征默认值
        for window in [5, 10, 20]:
            default_features[f'sum_mean_{window}'] = 0.0
            default_features[f'sum_std_{window}'] = 0.0
            default_features[f'span_mean_{window}'] = 0.0
            default_features[f'span_std_{window}'] = 0.0
            default_features[f'odd_ratio_mean_{window}'] = 0.0
            default_features[f'big_ratio_mean_{window}'] = 0.0
            default_features[f'prime_ratio_mean_{window}'] = 0.0
            
            for i in range(3):
                default_features[f'mod3_{i}_mean_{window}'] = 0.0
        
        # 添加高级特征默认值
        advanced_defaults = self.advanced_extractor._get_default_features()
        default_features.update(advanced_defaults)
        
        return default_features


def test_feature_engineering():
    """测试特征工程管道"""
    print("=== 测试特征工程管道 ===")
    
    # 创建测试数据
    test_data = [
        '123', '456', '789', '012', '345', 
        '678', '901', '234', '567', '890',
        '135', '246', '357', '468', '579'
    ]
    
    print(f"测试数据: {len(test_data)} 条")
    
    # 创建特征工程管道
    pipeline = FeatureEngineeringPipeline()
    
    # 提取特征
    features = pipeline.extract_all_features(test_data)
    
    print(f"提取特征数量: {len(features)}")
    
    # 按类别统计特征
    categories = {
        'basic': 0,
        'time_series': 0,
        'combination': 0,
        'advanced': 0
    }
    
    for key in features.keys():
        if any(stat in key for stat in ['mean', 'std', 'var', 'min', 'max', 'range', 'median', 'q25', 'q75', 'iqr', 'skewness', 'kurtosis', 'cv']):
            categories['basic'] += 1
        elif any(ts in key for ts in ['lag', 'ma', 'volatility', 'momentum']):
            categories['time_series'] += 1
        elif any(comb in key for comb in ['sum_', 'span_', 'odd_', 'big_', 'prime_', 'mod3_']):
            categories['combination'] += 1
        else:
            categories['advanced'] += 1
    
    print("\n特征分类统计:")
    for category, count in categories.items():
        print(f"  {category}: {count} 个特征")
    
    # 显示一些示例特征
    print("\n示例特征:")
    feature_items = list(features.items())
    for i in range(0, min(10, len(feature_items))):
        key, value = feature_items[i]
        print(f"  {key}: {value:.6f}")
    
    return features


if __name__ == "__main__":
    test_feature_engineering()
