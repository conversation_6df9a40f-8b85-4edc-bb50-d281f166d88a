#!/usr/bin/env python3
"""
导入状态和模块可用性验证脚本
验证INTELLIGENT_FUSION_AVAILABLE标志的准确性
"""

import sys
import os
import traceback

# 添加src路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_individual_imports():
    """测试各个模块的单独导入"""
    print("=== 测试各个模块的单独导入 ===")
    
    modules_to_test = [
        ('prediction.adaptive_fusion', 'AdaptiveFusionSystem'),
        ('prediction.intelligent_fusion', 'IntelligentFusionSystem'),
        ('prediction.pattern_prediction', 'PatternPredictor'),
        ('prediction.trend_analysis', 'TrendAnalyzer'),
    ]
    
    import_results = {}
    
    for module_name, class_name in modules_to_test:
        try:
            module = __import__(module_name, fromlist=[class_name])
            cls = getattr(module, class_name)
            import_results[module_name] = True
            print(f"✓ {module_name}.{class_name} 导入成功")
            
            # 测试类实例化
            try:
                if class_name == 'AdaptiveFusionSystem':
                    instance = cls(fusion_window=100)
                else:
                    instance = cls()
                print(f"  ✓ {class_name} 实例化成功")
            except Exception as e:
                print(f"  ⚠ {class_name} 实例化失败: {e}")
                
        except ImportError as e:
            import_results[module_name] = False
            print(f"✗ {module_name}.{class_name} 导入失败: {e}")
        except Exception as e:
            import_results[module_name] = False
            print(f"✗ {module_name}.{class_name} 意外错误: {e}")
    
    return import_results

def test_ui_components_import():
    """测试UI组件的导入状态"""
    print("\n=== 测试UI组件的导入状态 ===")
    
    try:
        # 模拟UI组件的导入逻辑
        from prediction.adaptive_fusion import AdaptiveFusionSystem
        from prediction.intelligent_fusion import IntelligentFusionSystem
        from prediction.pattern_prediction import PatternPredictor
        from prediction.trend_analysis import TrendAnalyzer
        INTELLIGENT_FUSION_AVAILABLE = True
        print("✓ UI组件导入逻辑模拟成功")
        print(f"  INTELLIGENT_FUSION_AVAILABLE = {INTELLIGENT_FUSION_AVAILABLE}")
        
        return True, INTELLIGENT_FUSION_AVAILABLE
        
    except ImportError as e:
        INTELLIGENT_FUSION_AVAILABLE = False
        print(f"✗ UI组件导入逻辑模拟失败: {e}")
        print(f"  INTELLIGENT_FUSION_AVAILABLE = {INTELLIGENT_FUSION_AVAILABLE}")
        
        return False, INTELLIGENT_FUSION_AVAILABLE

def test_actual_ui_components_import():
    """测试实际UI组件文件的导入"""
    print("\n=== 测试实际UI组件文件的导入 ===")
    
    try:
        # 导入实际的UI组件
        from ui.intelligent_fusion_components import INTELLIGENT_FUSION_AVAILABLE
        print(f"✓ 实际UI组件导入成功")
        print(f"  实际INTELLIGENT_FUSION_AVAILABLE = {INTELLIGENT_FUSION_AVAILABLE}")
        
        # 测试具体函数导入
        from ui.intelligent_fusion_components import show_adaptive_fusion_tab
        print("✓ show_adaptive_fusion_tab 函数导入成功")
        
        return True, INTELLIGENT_FUSION_AVAILABLE
        
    except ImportError as e:
        print(f"✗ 实际UI组件导入失败: {e}")
        traceback.print_exc()
        return False, None

def test_database_dependency():
    """测试数据库依赖"""
    print("\n=== 测试数据库依赖 ===")
    
    try:
        from core.database import DatabaseManager
        print("✓ DatabaseManager 导入成功")
        
        # 测试数据库文件存在性
        db_path = "data/lottery.db"
        if os.path.exists(db_path):
            print(f"✓ 数据库文件存在: {db_path}")
            
            # 测试数据库连接
            try:
                from prediction.intelligent_fusion import IntelligentFusionSystem
                system = IntelligentFusionSystem()
                db_manager = DatabaseManager(system.db_path)
                print("✓ 数据库连接测试成功")
                return True
            except Exception as e:
                print(f"⚠ 数据库连接测试失败: {e}")
                return False
        else:
            print(f"✗ 数据库文件不存在: {db_path}")
            return False
            
    except ImportError as e:
        print(f"✗ DatabaseManager 导入失败: {e}")
        return False

def test_streamlit_dependency():
    """测试Streamlit依赖"""
    print("\n=== 测试Streamlit依赖 ===")
    
    try:
        import streamlit as st
        print("✓ Streamlit 导入成功")
        
        # 测试Streamlit功能（在非Streamlit环境中会有警告，但不会失败）
        try:
            # 这些调用在非Streamlit环境中会产生警告但不会抛出异常
            st.markdown("测试")
            st.error("测试")
            st.success("测试")
            print("✓ Streamlit 基本功能测试通过")
            return True
        except Exception as e:
            print(f"⚠ Streamlit 功能测试异常（可能是正常的）: {e}")
            return True  # Streamlit在非web环境中的警告是正常的
            
    except ImportError as e:
        print(f"✗ Streamlit 导入失败: {e}")
        return False

def verify_intelligent_fusion_availability():
    """验证智能融合可用性的完整性"""
    print("\n=== 验证智能融合可用性的完整性 ===")
    
    # 收集所有测试结果
    individual_imports = test_individual_imports()
    ui_import_success, ui_available_flag = test_ui_components_import()
    actual_ui_success, actual_available_flag = test_actual_ui_components_import()
    db_success = test_database_dependency()
    streamlit_success = test_streamlit_dependency()
    
    # 分析结果
    print("\n" + "="*60)
    print("🎯 综合分析结果:")
    print("="*60)
    
    # 检查各个模块导入状态
    all_modules_available = all(individual_imports.values())
    print(f"所有核心模块可用: {'✓' if all_modules_available else '✗'}")
    
    for module, status in individual_imports.items():
        status_icon = "✓" if status else "✗"
        print(f"  {status_icon} {module}")
    
    print(f"\nUI组件导入逻辑: {'✓' if ui_import_success else '✗'}")
    print(f"实际UI组件导入: {'✓' if actual_ui_success else '✗'}")
    print(f"数据库依赖: {'✓' if db_success else '✗'}")
    print(f"Streamlit依赖: {'✓' if streamlit_success else '✗'}")
    
    # 标志一致性检查
    if ui_available_flag is not None and actual_available_flag is not None:
        flag_consistent = ui_available_flag == actual_available_flag
        print(f"\nINTELLIGENT_FUSION_AVAILABLE标志一致性: {'✓' if flag_consistent else '✗'}")
        print(f"  模拟值: {ui_available_flag}")
        print(f"  实际值: {actual_available_flag}")
    
    # 总体评估
    overall_status = (all_modules_available and ui_import_success and 
                     actual_ui_success and db_success and streamlit_success)
    
    print(f"\n📊 总体状态: {'✅ 完全可用' if overall_status else '❌ 存在问题'}")
    
    if not overall_status:
        print("\n🔧 建议的修复措施:")
        if not all_modules_available:
            print("1. 检查缺失的模块文件和依赖")
        if not ui_import_success or not actual_ui_success:
            print("2. 修复UI组件导入问题")
        if not db_success:
            print("3. 检查数据库文件和连接")
        if not streamlit_success:
            print("4. 安装或修复Streamlit依赖")
    
    return overall_status

def main():
    """主验证函数"""
    print("🔍 开始导入状态和模块可用性验证...")
    print("="*70)
    
    try:
        result = verify_intelligent_fusion_availability()
        
        if result:
            print("\n🎉 所有模块和依赖验证通过！")
            print("✅ INTELLIGENT_FUSION_AVAILABLE标志应该为True")
        else:
            print("\n⚠️ 发现模块或依赖问题")
            print("❌ 需要修复相关问题以确保功能正常")
            
    except Exception as e:
        print(f"\n💥 验证过程中发生意外错误: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
