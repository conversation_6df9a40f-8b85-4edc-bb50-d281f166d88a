#!/usr/bin/env python3
"""
优化建议展示界面
Optimization Suggestions Interface

展示优化建议和参数回测结果
"""

import os
# 导入系统组件
import sys

import pandas as pd
import plotly.express as px
import streamlit as st

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from optimization.auto_parameter_applier import AutoParameterApplier
from optimization.optimization_advisor import (OptimizationAdvisor,
                                               OptimizationPriority)
from optimization.parameter_backtesting_engine import \
    ParameterBacktestingEngine


def show_optimization_suggestions():
    """显示优化建议界面"""
    st.header("💡 优化建议与参数回测")
    st.markdown("---")
    
    # 初始化组件
    if 'optimization_advisor' not in st.session_state:
        st.session_state.optimization_advisor = OptimizationAdvisor()
        st.session_state.backtesting_engine = ParameterBacktestingEngine()
        st.session_state.parameter_applier = AutoParameterApplier()
    
    # 侧边栏控制
    with st.sidebar:
        st.subheader("🎛️ 优化控制")

        # 模型选择
        model_options = [
            "intelligent_fusion",
            "markov_enhanced",
            "deep_learning_cnn_lstm",
            "trend_analyzer"
        ]
        selected_model = st.selectbox("选择模型", model_options)

        # 检查是否有特定的分析类型请求
        default_analysis_type = "优化建议"
        if 'requested_analysis_type' in st.session_state:
            if st.session_state.requested_analysis_type == "parameter_backtest":
                default_analysis_type = "参数回测"
            elif st.session_state.requested_analysis_type == "application_history":
                default_analysis_type = "应用历史"
            # 清除请求状态
            del st.session_state.requested_analysis_type

        # 分析类型选择
        analysis_options = ["优化建议", "参数回测", "应用历史"]
        default_index = analysis_options.index(default_analysis_type)
        analysis_type = st.radio(
            "分析类型",
            analysis_options,
            index=default_index,
            key="analysis_type_radio"
        )
        
        # 刷新按钮
        if st.button("🔄 刷新数据", type="primary"):
            st.rerun()
    
    # 主要内容区域
    st.write(f"🔍 调试信息: 当前分析类型 = '{analysis_type}'")  # 临时调试信息

    if analysis_type == "优化建议":
        show_optimization_suggestions_tab(selected_model)
    elif analysis_type == "参数回测":
        show_parameter_backtesting_tab(selected_model)
    else:
        show_application_history_tab(selected_model)


def show_optimization_suggestions_tab(model_name):
    """显示优化建议标签页"""
    st.subheader(f"🎯 {model_name} 模型优化建议")
    
    # 模拟分析结果
    mock_analysis = generate_mock_analysis_results(model_name)
    
    try:
        advisor = st.session_state.optimization_advisor
        result = advisor.generate_suggestions(model_name, mock_analysis)
        
        # 显示优化概览
        show_optimization_overview(result)
        
        # 显示详细建议
        show_detailed_suggestions(result)
        
        # 显示实施路线图
        show_implementation_roadmap(result)
        
    except Exception as e:
        st.error(f"生成优化建议失败: {e}")


def show_optimization_overview(result):
    """显示优化概览"""
    st.subheader("📊 优化概览")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "优先级",
            result.priority_level.value.upper(),
            help="优化的紧急程度"
        )
    
    with col2:
        st.metric(
            "建议数量",
            len(result.optimization_strategies),
            help="生成的优化建议总数"
        )
    
    with col3:
        st.metric(
            "置信度",
            f"{result.confidence_score:.2f}",
            help="建议的可信度评分"
        )
    
    with col4:
        expected_acc_improvement = result.expected_improvements.get('accuracy_improvement', 0)
        st.metric(
            "预期准确率提升",
            f"{expected_acc_improvement:.1%}",
            help="预期的准确率改进幅度"
        )
    
    # 预期改进图表
    if result.expected_improvements:
        st.subheader("📈 预期改进分布")
        
        improvements_df = pd.DataFrame([
            {"改进类型": "准确率提升", "改进幅度": result.expected_improvements.get('accuracy_improvement', 0)},
            {"改进类型": "置信度校准", "改进幅度": result.expected_improvements.get('confidence_calibration', 0)},
            {"改进类型": "稳定性增强", "改进幅度": result.expected_improvements.get('stability_enhancement', 0)},
            {"改进类型": "效率提升", "改进幅度": result.expected_improvements.get('efficiency_gain', 0)}
        ])
        
        fig = px.bar(
            improvements_df,
            x="改进类型",
            y="改进幅度",
            title="各维度预期改进幅度",
            color="改进幅度",
            color_continuous_scale="viridis"
        )
        st.plotly_chart(fig, use_container_width=True)


def show_detailed_suggestions(result):
    """显示详细建议"""
    st.subheader("📋 详细优化建议")
    
    # 按优先级分组显示
    high_priority = [s for s in result.optimization_strategies if s.priority == OptimizationPriority.HIGH]
    medium_priority = [s for s in result.optimization_strategies if s.priority == OptimizationPriority.MEDIUM]
    low_priority = [s for s in result.optimization_strategies if s.priority == OptimizationPriority.LOW]
    
    # 高优先级建议
    if high_priority:
        st.markdown("### 🔴 高优先级建议")
        for suggestion in high_priority:
            show_suggestion_card(suggestion, "🔴")
    
    # 中优先级建议
    if medium_priority:
        st.markdown("### 🟡 中优先级建议")
        for suggestion in medium_priority:
            show_suggestion_card(suggestion, "🟡")
    
    # 低优先级建议
    if low_priority:
        st.markdown("### 🟢 低优先级建议")
        for suggestion in low_priority:
            show_suggestion_card(suggestion, "🟢")


def show_suggestion_card(suggestion, priority_icon):
    """显示单个建议卡片"""
    with st.expander(f"{priority_icon} {suggestion.strategy}", expanded=False):
        col1, col2 = st.columns([2, 1])
        
        with col1:
            st.write(f"**描述**: {suggestion.description}")
            st.write(f"**预期改进**: {suggestion.expected_improvement}")
            
            st.write("**具体行动**:")
            for action in suggestion.specific_actions:
                st.write(f"• {action}")
            
            if suggestion.prerequisites:
                st.write("**前置条件**:")
                for prereq in suggestion.prerequisites:
                    st.write(f"• {prereq}")
        
        with col2:
            # 建议指标
            st.metric("预期影响", f"{suggestion.estimated_impact:.1%}")
            st.metric("实施复杂度", suggestion.implementation_effort.value.upper())
            st.metric("风险级别", suggestion.risk_level.upper())
            
            # 成功指标
            if suggestion.success_metrics:
                st.write("**成功指标**:")
                for metric in suggestion.success_metrics:
                    st.write(f"✓ {metric}")
            
            # 实施按钮
            if st.button(f"实施建议", key=f"implement_{suggestion.strategy}"):
                st.success(f"建议 '{suggestion.strategy}' 已加入实施队列")


def show_implementation_roadmap(result):
    """显示实施路线图"""
    st.subheader("🗺️ 实施路线图")
    
    if result.implementation_roadmap:
        for phase in result.implementation_roadmap:
            with st.expander(f"阶段 {phase['phase']}: {phase['name']}", expanded=True):
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.write(f"**持续时间**: {phase['duration']}")
                
                with col2:
                    st.write(f"**预期影响**: {phase['expected_impact']:.1%}")
                
                with col3:
                    st.write(f"**风险级别**: {phase['risk_level'].upper()}")
                
                st.write("**包含建议**:")
                for suggestion in phase['suggestions']:
                    st.write(f"• {suggestion}")


def show_parameter_backtesting_tab(model_name):
    """显示参数回测标签页 - 基于真实历史数据"""
    st.subheader(f"🔬 {model_name} 参数回测")

    # 回测配置
    col1, col2, col3 = st.columns(3)

    with col1:
        backtest_periods = st.number_input(
            "回测期数",
            min_value=10,
            max_value=100,
            value=30,
            help="用于回测的历史期数"
        )

    with col2:
        optimization_method = st.selectbox(
            "优化方法",
            ["grid_search", "bayesian_optimization", "genetic_algorithm"],
            help="参数优化算法"
        )

    with col3:
        validation_split = st.slider(
            "验证集比例",
            min_value=0.1,
            max_value=0.5,
            value=0.2,
            step=0.1,
            help="用于验证的数据比例"
        )

    # 参数范围设置
    st.subheader("⚙️ 参数搜索范围")

    col1, col2 = st.columns(2)

    with col1:
        st.write("**模型参数:**")
        learning_rate_range = st.slider(
            "学习率范围",
            min_value=0.0001,
            max_value=0.01,
            value=(0.001, 0.005),
            step=0.0001,
            format="%.4f"
        )

        batch_size_options = st.multiselect(
            "批次大小选项",
            options=[16, 32, 64, 128],
            default=[32, 64]
        )

    with col2:
        st.write("**网络结构:**")
        hidden_units_range = st.slider(
            "隐藏单元数范围",
            min_value=64,
            max_value=512,
            value=(128, 256),
            step=32
        )

        dropout_rate_range = st.slider(
            "Dropout率范围",
            min_value=0.1,
            max_value=0.5,
            value=(0.2, 0.4),
            step=0.1
        )

    if st.button("🚀 开始回测", type="primary"):
        with st.spinner("正在执行参数回测..."):
            # 调用真实的回测API
            execute_real_backtesting(
                model_name,
                backtest_periods,
                optimization_method,
                validation_split,
                learning_rate_range,
                batch_size_options,
                hidden_units_range,
                dropout_rate_range
            )


def execute_real_backtesting(model_name, backtest_periods, optimization_method,
                           validation_split, learning_rate_range, batch_size_options,
                           hidden_units_range, dropout_rate_range):
    """执行真实的参数回测"""
    try:
        # 调用真实的回测API
        api_url = "http://127.0.0.1:8888/api/v1/optimization/backtest"

        backtest_config = {
            'model_name': model_name,
            'backtest_periods': backtest_periods,
            'optimization_method': optimization_method,
            'validation_split': validation_split,
            'parameter_ranges': {
                'learning_rate': {
                    'min': learning_rate_range[0],
                    'max': learning_rate_range[1]
                },
                'batch_size': batch_size_options,
                'hidden_units': {
                    'min': hidden_units_range[0],
                    'max': hidden_units_range[1]
                },
                'dropout_rate': {
                    'min': dropout_rate_range[0],
                    'max': dropout_rate_range[1]
                }
            }
        }

        import requests
        response = requests.post(api_url, json=backtest_config, timeout=120)

        if response.status_code == 200:
            backtest_data = response.json()
            if backtest_data.get('success'):
                show_real_backtesting_results(backtest_data)
            else:
                st.error(f"回测失败: {backtest_data.get('message', '未知错误')}")
        else:
            st.error(f"回测请求失败: HTTP {response.status_code}")

    except requests.exceptions.RequestException as e:
        st.error(f"无法连接到回测API: {str(e)}")
        st.info("请确保API服务(127.0.0.1:8888)正在运行")
    except Exception as e:
        st.error(f"回测过程出现错误: {str(e)}")


def show_real_backtesting_results(backtest_data):
    """显示真实的回测结果"""
    st.subheader("📊 回测结果")

    results = backtest_data.get('results', {})

    # 显示最优参数配置
    st.write("**🎯 最优参数配置**:")
    optimal_params = results.get('optimal_parameters', {})

    if optimal_params:
        params_df = pd.DataFrame([
            {"参数名称": k, "最优值": v}
            for k, v in optimal_params.items()
        ])
        st.dataframe(params_df, use_container_width=True)

    # 显示性能指标
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        best_score = results.get('best_score', 0)
        st.metric("最佳评分", f"{best_score:.3f}")

    with col2:
        accuracy = results.get('accuracy', 0)
        st.metric("回测准确率", f"{accuracy:.2%}")

    with col3:
        improvement = results.get('improvement', 0)
        st.metric("性能改进", f"{improvement:+.1%}")

    with col4:
        confidence = results.get('confidence', 0)
        st.metric("置信度", f"{confidence:.3f}")

    # 回测详情
    if 'backtest_details' in results:
        st.subheader("📈 回测详情")

        details = results['backtest_details']

        col1, col2 = st.columns(2)

        with col1:
            st.write("**回测配置:**")
            st.write(f"- 回测期数: {details.get('periods_tested', 0)}")
            st.write(f"- 验证集大小: {details.get('validation_size', 0)}")
            st.write(f"- 优化方法: {details.get('optimization_method', 'N/A')}")
            st.write(f"- 搜索次数: {details.get('search_iterations', 0)}")

        with col2:
            st.write("**性能统计:**")
            st.write(f"- 平均准确率: {details.get('avg_accuracy', 0):.2%}")
            st.write(f"- 标准差: {details.get('std_accuracy', 0):.3f}")
            st.write(f"- 最佳期号: {details.get('best_period', 'N/A')}")
            st.write(f"- 最差期号: {details.get('worst_period', 'N/A')}")

    # 参数搜索历史
    if 'search_history' in results:
        st.subheader("🔍 参数搜索历史")

        search_history = results['search_history']

        if search_history:
            # 创建搜索历史数据框
            history_df = pd.DataFrame(search_history)

            # 显示前10个最佳结果
            if not history_df.empty:
                top_results = history_df.nlargest(10, 'score')

                st.dataframe(
                    top_results,
                    column_config={
                        "iteration": "迭代次数",
                        "score": st.column_config.NumberColumn("评分", format="%.3f"),
                        "accuracy": st.column_config.NumberColumn("准确率", format="%.2%"),
                        "parameters": "参数配置"
                    },
                    hide_index=True,
                    use_container_width=True
                )

                # 搜索过程可视化
                if len(search_history) > 1:
                    import plotly.express as px

                    fig = px.line(
                        history_df,
                        x='iteration',
                        y='score',
                        title="参数搜索过程",
                        labels={'iteration': '迭代次数', 'score': '评分'},
                        markers=True
                    )
                    fig.update_layout(height=400)
                    st.plotly_chart(fig, use_container_width=True)

    # 参数应用按钮
    if optimal_params:
        if st.button("✅ 应用最优参数", type="primary"):
            try:
                # 调用参数应用API
                apply_url = "http://127.0.0.1:8888/api/v1/optimization/apply-parameters"
                apply_config = {
                    'model_name': backtest_data.get('model_name'),
                    'parameters': optimal_params
                }

                import requests
                apply_response = requests.post(apply_url, json=apply_config, timeout=30)

                if apply_response.status_code == 200:
                    apply_result = apply_response.json()
                    if apply_result.get('success'):
                        st.success("✅ 最优参数已成功应用到模型配置中")
                        st.info("💡 建议重新训练模型以使参数生效")

                        # 显示应用详情
                        if 'details' in apply_result:
                            details = apply_result['details']
                            st.write("**应用详情:**")
                            st.write(f"- 应用时间: {details.get('applied_at', 'N/A')}")
                            st.write(f"- 配置文件: {details.get('config_file', 'N/A')}")
                            st.write(f"- 备份文件: {details.get('backup_file', 'N/A')}")
                    else:
                        st.error(f"参数应用失败: {apply_result.get('message', '未知错误')}")
                else:
                    st.error("参数应用请求失败")

            except Exception as e:
                st.error(f"应用参数时出错: {str(e)}")


# 旧的模拟回测函数已被删除，现在使用真实的回测API


def show_application_history_tab(model_name):
    """显示应用历史标签页"""
    st.subheader(f"📜 {model_name} 参数应用历史")
    
    # 模拟应用历史
    history_data = [
        {
            "时间": "2025-07-22 10:30:00",
            "操作": "参数优化应用",
            "状态": "成功",
            "改进效果": "+12%",
            "备注": "学习率优化"
        },
        {
            "时间": "2025-07-21 15:45:00", 
            "操作": "参数回滚",
            "状态": "成功",
            "改进效果": "恢复稳定",
            "备注": "批量大小调整失败"
        },
        {
            "时间": "2025-07-20 09:15:00",
            "操作": "参数优化应用",
            "状态": "成功", 
            "改进效果": "+8%",
            "备注": "Dropout率调整"
        }
    ]
    
    history_df = pd.DataFrame(history_data)
    
    # 显示历史记录表格
    st.dataframe(history_df, use_container_width=True)
    
    # 显示应用统计
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("总应用次数", "15")
    
    with col2:
        st.metric("成功率", "87%")
    
    with col3:
        st.metric("平均改进", "+9.5%")
    
    # 应用趋势图
    st.subheader("📈 参数应用趋势")
    
    trend_data = pd.DataFrame({
        "日期": pd.date_range("2025-07-15", periods=7),
        "准确率": [0.65, 0.67, 0.69, 0.71, 0.68, 0.73, 0.75]
    })
    
    fig = px.line(
        trend_data,
        x="日期",
        y="准确率",
        title="模型准确率变化趋势",
        markers=True
    )
    st.plotly_chart(fig, use_container_width=True)


def generate_mock_analysis_results(model_name):
    """生成模拟分析结果"""
    return {
        'deviation_analysis': {
            'overall_score': 0.4,
            'numerical_deviation': {
                'sum_deviation': {'absolute_deviation': 5}
            }
        },
        'weakness_identification': {
            'identified_weaknesses': {
                'overfitting': {'severity_score': 0.7},
                'confidence_miscalibration': {'severity_score': 0.5}
            }
        },
        'success_factors': {
            'top_factors': [
                {'factor_name': 'high_confidence', 'importance_score': 0.8}
            ],
            'confidence_score': 0.6
        }
    }


# 直接调用主函数（Streamlit多页面应用要求）
show_optimization_suggestions()
