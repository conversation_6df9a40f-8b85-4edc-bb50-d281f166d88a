#!/usr/bin/env python3
"""
WebSocket功能测试脚本
创建日期: 2025年7月25日
用途: 测试WebSocket端点连接、实时数据更新、前端显示正常性、Bug检测功能
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Dict, List

import requests
import websockets

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebSocketTester:
    """WebSocket功能测试器"""
    
    def __init__(self):
        self.api_base_url = "http://127.0.0.1:8888"
        self.websocket_url = "ws://127.0.0.1:8888"
        self.test_results = {}
        
    async def run_all_tests(self):
        """运行所有WebSocket测试"""
        print("🧪 开始WebSocket功能测试")
        print("=" * 50)
        
        # 测试1: API服务可用性
        await self.test_api_availability()
        
        # 测试2: WebSocket端点连接
        await self.test_websocket_endpoints()
        
        # 测试3: 实时数据更新
        await self.test_realtime_data_updates()
        
        # 测试4: Bug检测功能
        await self.test_bug_detection()
        
        # 测试5: 降级机制
        await self.test_fallback_mechanisms()
        
        # 生成测试报告
        self.generate_test_report()
    
    async def test_api_availability(self):
        """测试API服务可用性"""
        print("\n📡 测试1: API服务可用性")
        
        endpoints_to_test = [
            "/health",
            "/api/v1/health/websocket",
            "/api/v1/health/websocket/connections",
            "/api/v1/health/websocket/performance",
            "/api/v1/stats/basic",
            "/api/v1/bug-detection/statistics"
        ]
        
        api_results = {}
        
        for endpoint in endpoints_to_test:
            try:
                response = requests.get(f"{self.api_base_url}{endpoint}", timeout=5)
                status = "✅ 正常" if response.status_code == 200 else f"❌ 错误({response.status_code})"
                api_results[endpoint] = {
                    'status': response.status_code,
                    'success': response.status_code == 200,
                    'response_time': response.elapsed.total_seconds()
                }
                print(f"  {endpoint}: {status}")
                
            except Exception as e:
                api_results[endpoint] = {
                    'status': 'error',
                    'success': False,
                    'error': str(e)
                }
                print(f"  {endpoint}: ❌ 连接失败 - {e}")
        
        self.test_results['api_availability'] = api_results
        
        # 统计结果
        total_endpoints = len(endpoints_to_test)
        successful_endpoints = sum(1 for result in api_results.values() if result['success'])
        print(f"\n📊 API测试结果: {successful_endpoints}/{total_endpoints} 端点正常")
    
    async def test_websocket_endpoints(self):
        """测试WebSocket端点连接"""
        print("\n🔌 测试2: WebSocket端点连接")
        
        websocket_endpoints = [
            "/ws/bug-detection",
            "/ws/realtime-stats"
        ]
        
        websocket_results = {}
        
        for endpoint in websocket_endpoints:
            try:
                uri = f"{self.websocket_url}{endpoint}"
                print(f"  正在测试: {uri}")
                
                async with websockets.connect(uri, timeout=5) as websocket:
                    # 发送测试消息
                    test_message = {
                        "type": "test",
                        "timestamp": datetime.now().isoformat(),
                        "message": "WebSocket连接测试"
                    }
                    
                    await websocket.send(json.dumps(test_message))
                    
                    # 等待响应
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=3)
                        websocket_results[endpoint] = {
                            'success': True,
                            'status': '✅ 连接成功',
                            'response': response
                        }
                        print(f"    {endpoint}: ✅ 连接成功")
                        
                    except asyncio.TimeoutError:
                        websocket_results[endpoint] = {
                            'success': True,
                            'status': '✅ 连接成功（无响应）',
                            'note': '连接建立但无响应消息'
                        }
                        print(f"    {endpoint}: ✅ 连接成功（无响应）")
                        
            except Exception as e:
                websocket_results[endpoint] = {
                    'success': False,
                    'status': f'❌ 连接失败',
                    'error': str(e)
                }
                print(f"    {endpoint}: ❌ 连接失败 - {e}")
        
        self.test_results['websocket_endpoints'] = websocket_results
        
        # 统计结果
        total_endpoints = len(websocket_endpoints)
        successful_endpoints = sum(1 for result in websocket_results.values() if result['success'])
        print(f"\n📊 WebSocket测试结果: {successful_endpoints}/{total_endpoints} 端点连接成功")
    
    async def test_realtime_data_updates(self):
        """测试实时数据更新"""
        print("\n📊 测试3: 实时数据更新")
        
        try:
            # 测试Bug检测统计数据
            response = requests.get(f"{self.api_base_url}/api/v1/bug-detection/statistics", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print("  ✅ Bug检测统计数据获取成功")
                print(f"    - 总Bug数: {data.get('total_bugs', 'N/A')}")
                print(f"    - 严重Bug: {data.get('critical_bugs', 'N/A')}")
                print(f"    - 解决率: {data.get('resolution_rate', 'N/A')}")
                
                self.test_results['realtime_data'] = {
                    'bug_stats': {
                        'success': True,
                        'data': data
                    }
                }
            else:
                print(f"  ❌ Bug检测统计数据获取失败: {response.status_code}")
                self.test_results['realtime_data'] = {
                    'bug_stats': {
                        'success': False,
                        'error': f'HTTP {response.status_code}'
                    }
                }
                
        except Exception as e:
            print(f"  ❌ 实时数据测试失败: {e}")
            self.test_results['realtime_data'] = {
                'bug_stats': {
                    'success': False,
                    'error': str(e)
                }
            }
    
    async def test_bug_detection(self):
        """测试Bug检测功能"""
        print("\n🐛 测试4: Bug检测功能")
        
        try:
            # 测试JavaScript错误报告端点
            test_error_data = {
                "sessionId": "test-session-123",
                "type": "TypeError",
                "message": "Cannot read property 'test' of null",
                "url": "http://test.com/test.js",
                "line": 10,
                "column": 5,
                "stack": "TypeError: Cannot read property 'test' of null\n    at test.js:10:5",
                "pageName": "test_page",
                "timestamp": datetime.now().isoformat()
            }
            
            response = requests.post(
                f"{self.api_base_url}/api/v1/bug-detection/js-error",
                json=test_error_data,
                timeout=5
            )
            
            if response.status_code == 200:
                print("  ✅ JavaScript错误报告功能正常")
                self.test_results['bug_detection'] = {
                    'js_error_reporting': {
                        'success': True,
                        'response': response.json()
                    }
                }
            else:
                print(f"  ❌ JavaScript错误报告失败: {response.status_code}")
                self.test_results['bug_detection'] = {
                    'js_error_reporting': {
                        'success': False,
                        'error': f'HTTP {response.status_code}'
                    }
                }
                
        except Exception as e:
            print(f"  ❌ Bug检测测试失败: {e}")
            self.test_results['bug_detection'] = {
                'js_error_reporting': {
                    'success': False,
                    'error': str(e)
                }
            }
    
    async def test_fallback_mechanisms(self):
        """测试降级机制"""
        print("\n🔄 测试5: 降级机制")
        
        # 测试API轮询降级
        print("  测试API轮询降级机制...")
        
        try:
            # 模拟WebSocket不可用的情况，测试API是否可以作为降级
            response = requests.get(f"{self.api_base_url}/api/v1/stats/basic", timeout=5)
            if response.status_code == 200:
                print("  ✅ API轮询降级机制可用")
                self.test_results['fallback_mechanisms'] = {
                    'api_polling': {
                        'success': True,
                        'note': 'API可以作为WebSocket的降级方案'
                    }
                }
            else:
                print("  ❌ API轮询降级机制不可用")
                self.test_results['fallback_mechanisms'] = {
                    'api_polling': {
                        'success': False,
                        'error': f'HTTP {response.status_code}'
                    }
                }
                
        except Exception as e:
            print(f"  ❌ 降级机制测试失败: {e}")
            self.test_results['fallback_mechanisms'] = {
                'api_polling': {
                    'success': False,
                    'error': str(e)
                }
            }
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n" + "=" * 50)
        print("📋 WebSocket功能测试报告")
        print("=" * 50)
        
        total_tests = 0
        passed_tests = 0
        
        for test_category, results in self.test_results.items():
            print(f"\n🔍 {test_category.replace('_', ' ').title()}:")
            
            if isinstance(results, dict):
                for test_name, result in results.items():
                    total_tests += 1
                    if result.get('success', False):
                        passed_tests += 1
                        status = "✅ 通过"
                    else:
                        status = "❌ 失败"
                    
                    print(f"  - {test_name}: {status}")
                    if 'error' in result:
                        print(f"    错误: {result['error']}")
        
        # 总体结果
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        print(f"\n📊 总体测试结果:")
        print(f"  - 总测试数: {total_tests}")
        print(f"  - 通过测试: {passed_tests}")
        print(f"  - 成功率: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("🎉 WebSocket功能测试整体通过！")
        elif success_rate >= 60:
            print("⚠️ WebSocket功能部分正常，需要关注失败项")
        else:
            print("❌ WebSocket功能存在严重问题，需要修复")
        
        # 保存测试结果到文件
        with open('websocket_test_results.json', 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'success_rate': success_rate,
                'detailed_results': self.test_results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 详细测试结果已保存到: websocket_test_results.json")

async def main():
    """主函数"""
    tester = WebSocketTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
