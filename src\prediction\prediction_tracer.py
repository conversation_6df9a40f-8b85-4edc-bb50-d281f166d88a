#!/usr/bin/env python3
"""
预测过程追踪系统
记录每次预测的详细过程、数据使用情况、权重计算过程，提供预测可解释性和透明度
"""

import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
import os


class PredictionTracer:
    """预测过程追踪器"""
    
    def __init__(self, trace_dir: str = "data/prediction_traces"):
        """
        初始化追踪器
        
        Args:
            trace_dir: 追踪日志存储目录
        """
        self.trace_dir = trace_dir
        self.current_trace = None
        self.trace_id = None
        
        # 确保目录存在
        os.makedirs(trace_dir, exist_ok=True)
    
    def start_trace(self, prediction_type: str, context: Dict[str, Any] = None) -> str:
        """
        开始一次预测追踪
        
        Args:
            prediction_type: 预测类型（如"intelligent_fusion", "trend_analysis"等）
            context: 预测上下文信息
            
        Returns:
            追踪ID
        """
        self.trace_id = f"{prediction_type}_{int(time.time() * 1000)}"
        
        self.current_trace = {
            'trace_id': self.trace_id,
            'prediction_type': prediction_type,
            'start_time': datetime.now().isoformat(),
            'context': context or {},
            'steps': [],
            'data_usage': {},
            'model_weights': {},
            'performance_metrics': {},
            'final_result': None,
            'execution_time': None
        }
        
        return self.trace_id
    
    def log_step(self, step_name: str, details: Dict[str, Any], step_type: str = "process"):
        """
        记录预测步骤
        
        Args:
            step_name: 步骤名称
            details: 步骤详情
            step_type: 步骤类型（process, data, calculation, result等）
        """
        if not self.current_trace:
            return
        
        step = {
            'step_name': step_name,
            'step_type': step_type,
            'timestamp': datetime.now().isoformat(),
            'details': details
        }
        
        self.current_trace['steps'].append(step)
    
    def log_data_usage(self, data_source: str, record_count: int, 
                      date_range: Dict[str, str] = None, 
                      data_characteristics: Dict[str, Any] = None):
        """
        记录数据使用情况
        
        Args:
            data_source: 数据源名称
            record_count: 记录数量
            date_range: 数据日期范围
            data_characteristics: 数据特征
        """
        if not self.current_trace:
            return
        
        self.current_trace['data_usage'][data_source] = {
            'record_count': record_count,
            'date_range': date_range,
            'characteristics': data_characteristics,
            'timestamp': datetime.now().isoformat()
        }
    
    def log_model_weights(self, model_name: str, weights: Dict[str, float], 
                         calculation_method: str = None):
        """
        记录模型权重
        
        Args:
            model_name: 模型名称
            weights: 权重字典
            calculation_method: 权重计算方法
        """
        if not self.current_trace:
            return
        
        self.current_trace['model_weights'][model_name] = {
            'weights': weights,
            'calculation_method': calculation_method,
            'timestamp': datetime.now().isoformat()
        }
    
    def log_performance_metrics(self, model_name: str, metrics: Dict[str, float]):
        """
        记录性能指标
        
        Args:
            model_name: 模型名称
            metrics: 性能指标
        """
        if not self.current_trace:
            return
        
        self.current_trace['performance_metrics'][model_name] = {
            'metrics': metrics,
            'timestamp': datetime.now().isoformat()
        }
    
    def end_trace(self, final_result: Dict[str, Any]) -> str:
        """
        结束预测追踪
        
        Args:
            final_result: 最终预测结果
            
        Returns:
            追踪文件路径
        """
        if not self.current_trace:
            return None
        
        # 记录结束时间和执行时长
        end_time = datetime.now()
        start_time = datetime.fromisoformat(self.current_trace['start_time'])
        execution_time = (end_time - start_time).total_seconds()
        
        self.current_trace['end_time'] = end_time.isoformat()
        self.current_trace['execution_time'] = execution_time
        self.current_trace['final_result'] = final_result
        
        # 保存追踪文件
        trace_file = os.path.join(self.trace_dir, f"{self.trace_id}.json")
        
        try:
            with open(trace_file, 'w', encoding='utf-8') as f:
                json.dump(self.current_trace, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存追踪文件失败: {e}")
        
        # 清理当前追踪
        trace_id = self.trace_id
        self.current_trace = None
        self.trace_id = None
        
        return trace_file
    
    def get_trace_summary(self) -> Dict[str, Any]:
        """
        获取当前追踪摘要
        
        Returns:
            追踪摘要
        """
        if not self.current_trace:
            return {}
        
        return {
            'trace_id': self.trace_id,
            'prediction_type': self.current_trace['prediction_type'],
            'start_time': self.current_trace['start_time'],
            'steps_count': len(self.current_trace['steps']),
            'data_sources': list(self.current_trace['data_usage'].keys()),
            'models_used': list(self.current_trace['model_weights'].keys())
        }
    
    def load_trace(self, trace_id: str) -> Optional[Dict[str, Any]]:
        """
        加载历史追踪记录
        
        Args:
            trace_id: 追踪ID
            
        Returns:
            追踪记录
        """
        trace_file = os.path.join(self.trace_dir, f"{trace_id}.json")
        
        if not os.path.exists(trace_file):
            return None
        
        try:
            with open(trace_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载追踪文件失败: {e}")
            return None
    
    def list_traces(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        列出最近的追踪记录
        
        Args:
            limit: 返回数量限制
            
        Returns:
            追踪记录列表
        """
        traces = []
        
        try:
            # 获取所有追踪文件
            trace_files = [f for f in os.listdir(self.trace_dir) if f.endswith('.json')]
            
            # 按修改时间排序
            trace_files.sort(key=lambda x: os.path.getmtime(os.path.join(self.trace_dir, x)), reverse=True)
            
            # 加载追踪摘要
            for trace_file in trace_files[:limit]:
                trace_path = os.path.join(self.trace_dir, trace_file)
                try:
                    with open(trace_path, 'r', encoding='utf-8') as f:
                        trace_data = json.load(f)
                        
                    # 提取摘要信息
                    summary = {
                        'trace_id': trace_data.get('trace_id'),
                        'prediction_type': trace_data.get('prediction_type'),
                        'start_time': trace_data.get('start_time'),
                        'execution_time': trace_data.get('execution_time'),
                        'final_result': trace_data.get('final_result', {}).get('numbers'),
                        'confidence': trace_data.get('final_result', {}).get('confidence')
                    }
                    traces.append(summary)
                    
                except Exception as e:
                    print(f"读取追踪文件 {trace_file} 失败: {e}")
                    continue
                    
        except Exception as e:
            print(f"列出追踪记录失败: {e}")
        
        return traces


# 全局追踪器实例
_global_tracer = None

def get_tracer() -> PredictionTracer:
    """获取全局追踪器实例"""
    global _global_tracer
    if _global_tracer is None:
        _global_tracer = PredictionTracer()
    return _global_tracer


def trace_prediction(prediction_type: str, context: Dict[str, Any] = None):
    """
    预测追踪装饰器
    
    Args:
        prediction_type: 预测类型
        context: 预测上下文
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            tracer = get_tracer()
            trace_id = tracer.start_trace(prediction_type, context)
            
            try:
                # 记录函数调用
                tracer.log_step(
                    f"调用{func.__name__}",
                    {
                        'function': func.__name__,
                        'args_count': len(args),
                        'kwargs': list(kwargs.keys())
                    },
                    "function_call"
                )
                
                # 执行函数
                result = func(*args, **kwargs)
                
                # 记录结果
                tracer.end_trace(result if isinstance(result, dict) else {'result': result})
                
                return result
                
            except Exception as e:
                # 记录错误
                tracer.log_step(
                    "执行错误",
                    {
                        'error': str(e),
                        'error_type': type(e).__name__
                    },
                    "error"
                )
                
                tracer.end_trace({'error': str(e)})
                raise
        
        return wrapper
    return decorator
