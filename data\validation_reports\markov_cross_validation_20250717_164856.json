{"model_params": {"transition_window_size": 1000, "probability_window_size": 500, "smoothing_alpha": 1.0}, "validation_params": {"k_folds": 2, "data_limit": 200}, "fold_results": [{"fold_idx": 0, "train_size": 100, "val_size": 100, "predictions_count": 100, "accuracy_metrics": {"exact_match": 0.0, "digit_accuracy": 0.25666666666666665, "position_accuracy": [0.14, 0.11, 0.08], "total_predictions": 100}, "diversity_metrics": {"simpson_diversity": 0.9864, "unique_ratio": 0.85, "entropy": 6.321209564709833, "unique_count": 85, "total_count": 100}, "stability_metrics": {"variance": 81641.04590000001, "std_dev": 285.7289728046493, "coefficient_of_variation": 0.5404266475093138, "mean": 528.71}, "aic_bic": {"aic": 4825.170185988091, "bic": 5111.738906446782, "log_likelihood": -2302.5850929940457, "num_params": 110, "num_samples": 100}}], "overall_results": {"accuracy_metrics": {"exact_match": 0.0, "digit_accuracy": 0.25666666666666665, "position_accuracy": [0.14, 0.11, 0.08], "total_predictions": 100}, "diversity_metrics": {"simpson_diversity": 0.9864, "unique_ratio": 0.85, "entropy": 6.321209564709833, "unique_count": 85, "total_count": 100}, "stability_metrics": {"variance": 81641.04590000001, "std_dev": 285.7289728046493, "coefficient_of_variation": 0.5404266475093138, "mean": 528.71}, "aic_bic": {"aic": 4825.170185988091, "bic": 5111.738906446782, "log_likelihood": -2302.5850929940457, "num_params": 110, "num_samples": 100}, "total_predictions": 100}, "metadata": {"generated_at": "2025-07-17T16:48:56.085878", "validator_version": "1.0", "database_path": "data\\lottery.db"}}