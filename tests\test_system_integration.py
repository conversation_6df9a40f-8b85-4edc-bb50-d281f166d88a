#!/usr/bin/env python3
"""
系统集成测试
System Integration Tests

测试整个闭环系统的端到端功能
"""

import asyncio
import os
# 导入系统组件
import sys
import tempfile
import time
from datetime import datetime

import pytest
import requests

sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from src.core.draw_trigger_system import DrawTriggerSystem
from src.core.unified_prediction_storage import (PredictionRecord,
                                                 UnifiedPredictionStorage)


class TestSystemIntegration:
    """系统集成测试类"""
    
    @pytest.fixture
    def temp_db_path(self):
        """创建临时数据库路径"""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            temp_path = f.name
        yield temp_path
        # 清理
        if os.path.exists(temp_path):
            os.unlink(temp_path)
    
    @pytest.fixture
    def integrated_system(self, temp_db_path):
        """创建集成系统"""
        storage = UnifiedPredictionStorage(temp_db_path)
        trigger_system = DrawTriggerSystem(storage)
        return {
            'storage': storage,
            'trigger_system': trigger_system
        }
    
    @pytest.fixture
    def sample_predictions(self):
        """创建示例预测数据"""
        predictions = []
        for i in range(5):
            prediction = PredictionRecord(
                period_number=f"202519{i:02d}",
                model_name="intelligent_fusion",
                predicted_numbers=f"{i%10}{(i+1)%10}{(i+2)%10}",
                confidence=0.7 + (i % 3) * 0.1,
                prediction_time=datetime.now(),
                metadata={'test_data': True}
            )
            predictions.append(prediction)
        return predictions
    
    def test_end_to_end_prediction_workflow(self, integrated_system, sample_predictions):
        """测试端到端预测工作流程"""
        storage = integrated_system['storage']
        trigger_system = integrated_system['trigger_system']
        
        # 1. 保存预测记录
        prediction_ids = []
        for prediction in sample_predictions:
            record_id = storage.save_prediction_record(prediction)
            prediction_ids.append(record_id)
            assert record_id > 0
        
        # 2. 验证预测记录已保存
        stats = storage.get_statistics()
        assert stats['total_predictions'] == len(sample_predictions)
        
        # 3. 获取期号预测
        period_predictions = storage.get_period_predictions("2025190")
        assert len(period_predictions) > 0
        
        # 4. 模拟开奖触发
        async def run_trigger_test():
            result = await trigger_system.on_draw_announced("2025190", "123")
            assert result.period_number == "2025190"
            assert result.total_predictions > 0
            return result
        
        # 运行异步测试
        result = asyncio.run(run_trigger_test())
        
        # 5. 验证分析结果
        assert result.analysis_details is not None
        
        # 6. 验证预测记录已更新
        updated_predictions = storage.get_period_predictions("2025190")
        for pred in updated_predictions:
            assert pred.is_verified == True
            assert pred.actual_numbers == "123"
    
    @pytest.mark.asyncio
    async def test_concurrent_analysis_processing(self, integrated_system, sample_predictions):
        """测试并发分析处理"""
        storage = integrated_system['storage']
        trigger_system = integrated_system['trigger_system']
        
        # 保存测试数据
        for prediction in sample_predictions:
            storage.save_prediction_record(prediction)
        
        # 注册多个分析器
        async def analyzer1(predictions, actual_numbers):
            await asyncio.sleep(0.1)  # 模拟处理时间
            return {'analyzer1': 'completed'}
        
        async def analyzer2(predictions, actual_numbers):
            await asyncio.sleep(0.2)  # 模拟处理时间
            return {'analyzer2': 'completed'}
        
        trigger_system.register_analyzer('analyzer1', analyzer1)
        trigger_system.register_analyzer('analyzer2', analyzer2)
        
        # 并发触发分析
        tasks = []
        for i in range(3):
            task = asyncio.create_task(
                trigger_system.on_draw_announced(f"202519{i:02d}", f"{i}{i+1}{i+2}")
            )
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks)
        
        # 验证结果
        assert len(results) == 3
        for result in results:
            assert 'analyzer1' in result.analysis_details
            assert 'analyzer2' in result.analysis_details
    
    def test_data_consistency_across_operations(self, integrated_system, sample_predictions):
        """测试操作间数据一致性"""
        storage = integrated_system['storage']
        
        # 1. 批量保存预测
        original_count = storage.get_statistics()['total_predictions']
        
        for prediction in sample_predictions:
            storage.save_prediction_record(prediction)
        
        # 2. 验证数量一致性
        new_count = storage.get_statistics()['total_predictions']
        assert new_count == original_count + len(sample_predictions)
        
        # 3. 更新预测结果
        storage.update_prediction_result("2025190", "123")
        
        # 4. 验证更新一致性
        updated_predictions = storage.get_period_predictions("2025190")
        for pred in updated_predictions:
            assert pred.actual_numbers == "123"
            assert pred.is_verified == True
        
        # 5. 验证统计信息一致性
        stats = storage.get_statistics()
        assert stats['verified_predictions'] > 0
    
    def test_error_handling_and_recovery(self, integrated_system):
        """测试错误处理和恢复"""
        storage = integrated_system['storage']
        trigger_system = integrated_system['trigger_system']
        
        # 1. 测试无效数据处理
        invalid_prediction = PredictionRecord(
            period_number="",  # 无效期号
            model_name="",     # 无效模型名
            predicted_numbers="",  # 无效预测号码
            confidence=-1.0    # 无效置信度
        )
        
        # 系统应该能处理无效数据而不崩溃
        try:
            record_id = storage.save_prediction_record(invalid_prediction)
            # 如果保存成功，验证记录ID
            assert record_id > 0
        except Exception as e:
            # 如果抛出异常，应该是可预期的异常
            assert isinstance(e, (ValueError, TypeError))
        
        # 2. 测试分析器异常处理
        async def failing_analyzer(predictions, actual_numbers):
            raise Exception("模拟分析器失败")
        
        trigger_system.register_analyzer('failing_analyzer', failing_analyzer)
        
        # 保存一个有效预测
        valid_prediction = PredictionRecord(
            period_number="2025195",
            model_name="test_model",
            predicted_numbers="123",
            confidence=0.8,
            prediction_time=datetime.now()
        )
        storage.save_prediction_record(valid_prediction)
        
        # 触发分析，应该能处理分析器失败
        async def test_error_handling():
            result = await trigger_system.on_draw_announced("2025195", "456")
            # 系统应该继续运行，即使某个分析器失败
            assert result.period_number == "2025195"
            # 失败的分析器应该在结果中标记错误
            assert 'failing_analyzer' in result.analysis_details
            assert 'error' in result.analysis_details['failing_analyzer']
        
        asyncio.run(test_error_handling())
    
    def test_performance_under_load(self, integrated_system):
        """测试负载下的性能"""
        storage = integrated_system['storage']
        
        # 1. 批量数据插入性能测试
        start_time = time.time()
        
        batch_size = 100
        for i in range(batch_size):
            prediction = PredictionRecord(
                period_number=f"2025{i:03d}",
                model_name=f"model_{i%5}",
                predicted_numbers=f"{i%10}{(i+1)%10}{(i+2)%10}",
                confidence=0.5 + (i % 5) * 0.1,
                prediction_time=datetime.now()
            )
            storage.save_prediction_record(prediction)
        
        insert_time = time.time() - start_time
        
        # 插入应该在合理时间内完成（每条记录<50ms）
        assert insert_time < batch_size * 0.05
        
        # 2. 批量查询性能测试
        start_time = time.time()
        
        for i in range(20):
            period_predictions = storage.get_period_predictions(f"2025{i:03d}")
        
        query_time = time.time() - start_time
        
        # 查询应该很快（每次查询<10ms）
        assert query_time < 20 * 0.01
        
        # 3. 统计查询性能测试
        start_time = time.time()
        
        for _ in range(10):
            stats = storage.get_statistics()
        
        stats_time = time.time() - start_time
        
        # 统计查询应该很快（每次<50ms）
        assert stats_time < 10 * 0.05
    
    def test_system_state_persistence(self, temp_db_path):
        """测试系统状态持久化"""
        # 1. 创建第一个存储实例并保存数据
        storage1 = UnifiedPredictionStorage(temp_db_path)
        
        test_prediction = PredictionRecord(
            period_number="2025200",
            model_name="persistence_test",
            predicted_numbers="789",
            confidence=0.9,
            prediction_time=datetime.now()
        )
        
        record_id = storage1.save_prediction_record(test_prediction)
        stats1 = storage1.get_statistics()
        
        # 2. 关闭第一个实例，创建第二个实例
        del storage1
        
        storage2 = UnifiedPredictionStorage(temp_db_path)
        
        # 3. 验证数据持久化
        stats2 = storage2.get_statistics()
        assert stats2['total_predictions'] == stats1['total_predictions']
        
        # 4. 验证具体记录
        predictions = storage2.get_period_predictions("2025200")
        assert len(predictions) > 0
        
        found_prediction = None
        for pred in predictions:
            if pred.model_name == "persistence_test":
                found_prediction = pred
                break
        
        assert found_prediction is not None
        assert found_prediction.predicted_numbers == "789"
        assert found_prediction.confidence == 0.9
    
    def test_api_integration(self):
        """测试API集成（需要API服务器运行）"""
        # 注意：这个测试需要API服务器在运行
        # 在实际环境中，可以使用测试服务器或模拟
        
        api_base_url = "http://127.0.0.1:8888"
        
        try:
            # 测试健康检查端点
            response = requests.get(f"{api_base_url}/api/v1/system/health", timeout=5)
            
            if response.status_code == 200:
                health_data = response.json()
                assert health_data['status'] == 'healthy'
                
                # 测试系统状态端点
                response = requests.get(f"{api_base_url}/api/v1/system/status", timeout=5)
                if response.status_code == 200:
                    status_data = response.json()
                    assert 'system_status' in status_data
                    assert 'storage_statistics' in status_data
            else:
                pytest.skip("API服务器未运行，跳过API集成测试")
                
        except requests.exceptions.RequestException:
            pytest.skip("无法连接到API服务器，跳过API集成测试")
    
    def test_memory_usage_stability(self, integrated_system):
        """测试内存使用稳定性"""
        import gc

        import psutil
        
        storage = integrated_system['storage']
        
        # 获取初始内存使用
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        # 执行大量操作
        for batch in range(10):
            # 批量插入
            for i in range(50):
                prediction = PredictionRecord(
                    period_number=f"2025{batch:02d}{i:02d}",
                    model_name=f"memory_test_{batch}",
                    predicted_numbers=f"{i%10}{(i+1)%10}{(i+2)%10}",
                    confidence=0.5,
                    prediction_time=datetime.now()
                )
                storage.save_prediction_record(prediction)
            
            # 批量查询
            for i in range(10):
                storage.get_period_predictions(f"2025{batch:02d}{i:02d}")
            
            # 强制垃圾回收
            gc.collect()
        
        # 获取最终内存使用
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # 内存增长应该在合理范围内（<100MB）
        assert memory_increase < 100 * 1024 * 1024


if __name__ == "__main__":
    # 运行集成测试
    pytest.main([__file__, "-v", "--tb=short"])
