{"error_message": {"required": true, "max_length": 1000, "min_length": 5}, "error_type": {"required": true, "allowed_values": ["ui", "api", "database", "network", "performance", "security", "data", "integration", "general"]}, "severity": {"required": true, "allowed_values": ["low", "medium", "high", "critical"]}, "status": {"required": true, "allowed_values": ["open", "in_progress", "resolved", "closed"]}, "priority": {"required": false, "pattern": "^P[1-4]$"}, "environment": {"required": true, "allowed_values": ["development", "testing", "staging", "production"]}}