#!/usr/bin/env python3
"""
测试数据质量检查功能
"""

import sys
sys.path.append('src')

from data.parser import DataParser
from data.quality_checker import QualityChecker, check_data_quality
import json

def test_quality_checker():
    """测试数据质量检查器"""
    print("开始测试数据质量检查器...")
    
    try:
        # 1. 读取已处理的数据
        print("\n1. 读取数据...")
        with open('data/raw/3d_data_20250714_144231.txt', 'r', encoding='utf-8') as f:
            raw_data = f.read()
        
        # 2. 解析数据
        parser = DataParser()
        records, _ = parser.parse_data(raw_data)
        
        print(f"✅ 数据读取成功，记录数: {len(records)}")
        
        # 3. 执行质量检查
        print("\n2. 执行质量检查...")
        checker = QualityChecker()
        
        # 完整性检查
        print("   - 完整性检查...")
        completeness = checker.check_data_completeness(records)
        
        # 准确性检查
        print("   - 准确性检查...")
        accuracy = checker.check_data_accuracy(records)
        
        # 异常检测
        print("   - 异常检测...")
        anomalies = checker.detect_anomalies(records)
        
        # 生成综合报告
        print("   - 生成综合报告...")
        quality_report = checker.generate_quality_report(records)
        
        print(f"✅ 质量检查完成")
        
        # 4. 显示检查结果
        print(f"\n3. 质量检查结果:")
        print(f"📊 综合评分: {quality_report['summary']['overall_score']}")
        
        print(f"\n🔍 完整性检查:")
        print(f"   - 总记录数: {completeness['total_records']}")
        print(f"   - 期号范围: {completeness['period_range']['start']} - {completeness['period_range']['end']}")
        print(f"   - 缺失期号: {completeness['missing_periods']['count']}")
        print(f"   - 日期间隔: {completeness['date_gaps']['count']}")
        print(f"   - 重复记录: {completeness['duplicates']['count']}")
        print(f"   - 完整性评分: {completeness['completeness_score']}")
        
        print(f"\n✅ 准确性检查:")
        print(f"   - 准确性问题: {accuracy['accuracy_issues']['count']}")
        print(f"   - 准确性评分: {accuracy['accuracy_score']}")
        
        if accuracy['accuracy_issues']['count'] > 0:
            print(f"   - 问题示例:")
            for issue in accuracy['accuracy_issues']['list'][:3]:
                print(f"     期号 {issue['period']}: {', '.join(issue['issues'])}")
        
        print(f"\n🚨 异常检测:")
        print(f"   - 异常记录: {anomalies['anomalies']['count']}")
        print(f"   - 异常评分: {anomalies['anomaly_score']}")
        
        if anomalies['anomalies']['count'] > 0:
            print(f"   - 异常示例:")
            for anomaly in anomalies['anomalies']['list'][:3]:
                print(f"     期号 {anomaly['period']} ({anomaly['numbers']}): {', '.join(anomaly['anomaly_flags'])}")
        
        # 统计信息
        stats = anomalies['statistical_info']
        print(f"\n📈 统计信息:")
        print(f"   - 和值: 均值 {stats['sum_value']['mean']}, 标准差 {stats['sum_value']['std']}")
        print(f"   - 跨度: 均值 {stats['span_value']['mean']}, 标准差 {stats['span_value']['std']}")
        
        # 频率异常
        freq_anomalies = anomalies['frequency_anomalies']
        print(f"\n🔢 频率分析:")
        print(f"   - 总数字数: {freq_anomalies['total_digits']}")
        print(f"   - 期望频率: {freq_anomalies['expected_frequency']}")
        print(f"   - 频率异常: {len(freq_anomalies['anomalies'])}")
        
        if freq_anomalies['anomalies']:
            print(f"   - 异常数字:")
            for fa in freq_anomalies['anomalies'][:5]:
                print(f"     数字 {fa['digit']}: 出现 {fa['count']} 次 (偏差 {fa['deviation']}%)")
        
        # 改进建议
        print(f"\n💡 改进建议:")
        for i, rec in enumerate(quality_report['recommendations'], 1):
            print(f"   {i}. {rec}")
        
        # 5. 保存质量报告
        print(f"\n4. 保存质量报告...")
        with open('data/processed/quality_report.json', 'w', encoding='utf-8') as f:
            json.dump(quality_report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 质量报告已保存到: data/processed/quality_report.json")
        
        print(f"\n🎉 数据质量检查测试成功！")
        print(f"📋 检查摘要:")
        print(f"   - 数据记录: {len(records)} 条")
        print(f"   - 综合评分: {quality_report['summary']['overall_score']}")
        print(f"   - 完整性: {completeness['completeness_score']}")
        print(f"   - 准确性: {accuracy['accuracy_score']}")
        print(f"   - 异常检测: {anomalies['anomaly_score']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_quality_checker()
