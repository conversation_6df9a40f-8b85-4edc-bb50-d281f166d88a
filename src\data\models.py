"""
福彩3D数据模型定义

定义数据结构和验证规则
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from dataclasses import dataclass
import re


@dataclass
class LotteryRecord:
    """福彩3D开奖记录（完整版）"""

    # 基础信息
    period: str  # 期号 (7位数字，格式：YYYYNNN)
    date: date   # 开奖日期 (格式：YYYY-MM-DD)

    # 开奖号码信息
    numbers: str        # 正式开奖号码 (3位数字，百位-十位-个位)
    trial_numbers: str  # 试机号码 (3位数字，百位-十位-个位)

    # 机器信息
    draw_machine: int   # 开奖机器号
    trial_machine: int  # 试机机器号

    # 销售和奖金信息
    sales_amount: int   # 销售额 (单位：元)
    direct_prize: int   # 直选奖金 (单位：元)
    group3_prize: int   # 组三奖金 (单位：元)
    group6_prize: int   # 组六奖金 (单位：元)

    # 未知字段（保留用于后续分析）
    unknown_field1: int = 0  # 第10个字段
    unknown_field2: int = 0  # 第12个字段
    unknown_field3: int = 0  # 第14个字段

    def __post_init__(self):
        """数据验证"""
        # 验证期号格式
        if not re.match(r'^\d{7}$', self.period):
            raise ValueError(f"期号格式错误: {self.period}")

        # 验证正式开奖号码格式
        if not re.match(r'^\d{3}$', self.numbers):
            raise ValueError(f"开奖号码格式错误: {self.numbers}")

        # 验证试机号码格式
        if not re.match(r'^\d{3}$', self.trial_numbers):
            raise ValueError(f"试机号码格式错误: {self.trial_numbers}")

        # 验证每个数字都在0-9范围内
        for digit in self.numbers + self.trial_numbers:
            if not (0 <= int(digit) <= 9):
                raise ValueError(f"号码包含无效数字: {digit}")

        # 验证机器号范围
        if not (1 <= self.draw_machine <= 9):
            raise ValueError(f"开奖机器号超出范围: {self.draw_machine}")

        if not (1 <= self.trial_machine <= 9):
            raise ValueError(f"试机机器号超出范围: {self.trial_machine}")

        # 验证金额为非负数
        if self.sales_amount < 0:
            raise ValueError(f"销售额不能为负数: {self.sales_amount}")

        for prize_name, prize_value in [
            ("直选奖金", self.direct_prize),
            ("组三奖金", self.group3_prize),
            ("组六奖金", self.group6_prize)
        ]:
            if prize_value < 0:
                raise ValueError(f"{prize_name}不能为负数: {prize_value}")

    @property
    def number_list(self) -> List[int]:
        """返回正式开奖号码列表"""
        return [int(d) for d in self.numbers]

    @property
    def trial_number_list(self) -> List[int]:
        """返回试机号码列表"""
        return [int(d) for d in self.trial_numbers]

    @property
    def sum_value(self) -> int:
        """返回正式开奖号码和值"""
        return sum(self.number_list)

    @property
    def trial_sum_value(self) -> int:
        """返回试机号码和值"""
        return sum(self.trial_number_list)

    @property
    def span_value(self) -> int:
        """返回正式开奖号码跨度"""
        nums = self.number_list
        return max(nums) - min(nums)

    @property
    def trial_span_value(self) -> int:
        """返回试机号码跨度"""
        nums = self.trial_number_list
        return max(nums) - min(nums)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典（完整版）"""
        return {
            # 基础信息
            "period": self.period,
            "date": self.date.isoformat(),

            # 开奖号码信息
            "numbers": self.numbers,
            "trial_numbers": self.trial_numbers,
            "number_list": self.number_list,
            "trial_number_list": self.trial_number_list,

            # 统计信息
            "sum_value": self.sum_value,
            "trial_sum_value": self.trial_sum_value,
            "span_value": self.span_value,
            "trial_span_value": self.trial_span_value,

            # 机器信息
            "draw_machine": self.draw_machine,
            "trial_machine": self.trial_machine,

            # 销售和奖金信息
            "sales_amount": self.sales_amount,
            "direct_prize": self.direct_prize,
            "group3_prize": self.group3_prize,
            "group6_prize": self.group6_prize,

            # 未知字段
            "unknown_field1": self.unknown_field1,
            "unknown_field2": self.unknown_field2,
            "unknown_field3": self.unknown_field3,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "LotteryRecord":
        """从字典创建实例（完整版）"""
        return cls(
            period=data["period"],
            date=date.fromisoformat(data["date"]),
            numbers=data["numbers"],
            trial_numbers=data["trial_numbers"],
            draw_machine=data["draw_machine"],
            trial_machine=data["trial_machine"],
            sales_amount=data["sales_amount"],
            direct_prize=data["direct_prize"],
            group3_prize=data["group3_prize"],
            group6_prize=data["group6_prize"],
            unknown_field1=data.get("unknown_field1", 0),
            unknown_field2=data.get("unknown_field2", 0),
            unknown_field3=data.get("unknown_field3", 0)
        )


@dataclass
class DataQualityReport:
    """数据质量报告"""
    
    total_records: int
    valid_records: int
    invalid_records: int
    duplicate_records: int
    missing_dates: int
    date_range_start: Optional[date]
    date_range_end: Optional[date]
    errors: List[str]
    
    @property
    def quality_score(self) -> float:
        """数据质量评分 (0-100)"""
        if self.total_records == 0:
            return 0.0
        
        base_score = (self.valid_records / self.total_records) * 100
        
        # 扣分项
        duplicate_penalty = (self.duplicate_records / self.total_records) * 10
        missing_penalty = (self.missing_dates / self.total_records) * 5
        
        final_score = max(0, base_score - duplicate_penalty - missing_penalty)
        return round(final_score, 2)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "total_records": self.total_records,
            "valid_records": self.valid_records,
            "invalid_records": self.invalid_records,
            "duplicate_records": self.duplicate_records,
            "missing_dates": self.missing_dates,
            "date_range_start": self.date_range_start.isoformat() if self.date_range_start else None,
            "date_range_end": self.date_range_end.isoformat() if self.date_range_end else None,
            "quality_score": self.quality_score,
            "errors": self.errors,
        }


class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def validate_period(period: str) -> bool:
        """验证期号格式"""
        return bool(re.match(r'^\d{7}$', period))
    
    @staticmethod
    def validate_numbers(numbers: str) -> bool:
        """验证号码格式"""
        if not re.match(r'^\d{3}$', numbers):
            return False
        
        # 检查每个数字是否在有效范围内
        for digit in numbers:
            if not (0 <= int(digit) <= 9):
                return False
        
        return True
    
    @staticmethod
    def validate_date(date_str: str) -> bool:
        """验证日期格式"""
        try:
            datetime.strptime(date_str, '%Y-%m-%d')
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_record_line(line: str) -> Optional[Dict[str, Any]]:
        """
        验证单行记录格式（完整版13字段）

        Args:
            line: 原始数据行

        Returns:
            解析后的数据字典，无效时返回None
        """
        # 移除空白字符
        line = line.strip()
        if not line:
            return None

        # 按空格分割（实际数据格式）
        parts = line.split()

        # 完整格式：13个字段
        if len(parts) >= 13:
            try:
                # 解析所有字段
                period = parts[0]                    # 期号
                date_str = parts[1]                  # 日期
                digit1, digit2, digit3 = parts[2], parts[3], parts[4]  # 正式开奖号码
                trial1, trial2, trial3 = parts[5], parts[6], parts[7]  # 试机号码
                draw_machine = int(parts[8])         # 开奖机器号
                trial_machine = int(parts[9])        # 试机机器号
                sales_amount = int(parts[10])        # 销售额
                unknown_field1 = int(parts[11])      # 未知字段1
                direct_prize = int(parts[12])        # 直选奖金

                # 处理可能的额外字段
                unknown_field2 = int(parts[13]) if len(parts) > 13 else 0
                group3_prize = int(parts[14]) if len(parts) > 14 else 0
                unknown_field3 = int(parts[15]) if len(parts) > 15 else 0
                group6_prize = int(parts[16]) if len(parts) > 16 else 0

                # 组合号码
                numbers = digit1 + digit2 + digit3
                trial_numbers = trial1 + trial2 + trial3

                # 验证基础字段
                if (DataValidator.validate_period(period) and
                    DataValidator.validate_date(date_str) and
                    DataValidator.validate_numbers(numbers) and
                    DataValidator.validate_numbers(trial_numbers)):

                    return {
                        "period": period,
                        "date": date_str,
                        "numbers": numbers,
                        "trial_numbers": trial_numbers,
                        "draw_machine": draw_machine,
                        "trial_machine": trial_machine,
                        "sales_amount": sales_amount,
                        "direct_prize": direct_prize,
                        "group3_prize": group3_prize,
                        "group6_prize": group6_prize,
                        "unknown_field1": unknown_field1,
                        "unknown_field2": unknown_field2,
                        "unknown_field3": unknown_field3
                    }
            except (ValueError, IndexError):
                pass  # 继续尝试其他格式

        # 简化格式：只有前5个字段（向后兼容）
        elif len(parts) >= 5:
            try:
                period = parts[0]
                date_str = parts[1]
                digit1, digit2, digit3 = parts[2], parts[3], parts[4]
                numbers = digit1 + digit2 + digit3

                if (DataValidator.validate_period(period) and
                    DataValidator.validate_date(date_str) and
                    DataValidator.validate_numbers(numbers)):

                    return {
                        "period": period,
                        "date": date_str,
                        "numbers": numbers,
                        "trial_numbers": "000",  # 默认值
                        "draw_machine": 1,
                        "trial_machine": 1,
                        "sales_amount": 0,
                        "direct_prize": 1000,
                        "group3_prize": 333,
                        "group6_prize": 166,
                        "unknown_field1": 0,
                        "unknown_field2": 0,
                        "unknown_field3": 0
                    }
            except (ValueError, IndexError):
                pass

        # 尝试传统格式（向后兼容）
        separators = ['\t', ',', '|']
        for sep in separators:
            parts = [p.strip() for p in line.split(sep) if p.strip()]

            if len(parts) >= 3:
                try:
                    period, date_str, numbers = parts[0], parts[1], parts[2]

                    if (DataValidator.validate_period(period) and
                        DataValidator.validate_date(date_str) and
                        DataValidator.validate_numbers(numbers)):

                        return {
                            "period": period,
                            "date": date_str,
                            "numbers": numbers,
                            "trial_numbers": "000",  # 默认值
                            "draw_machine": 1,
                            "trial_machine": 1,
                            "sales_amount": 0,
                            "direct_prize": 1000,
                            "group3_prize": 333,
                            "group6_prize": 166,
                            "unknown_field1": 0,
                            "unknown_field2": 0,
                            "unknown_field3": 0
                        }
                except (ValueError, IndexError):
                    continue

        return None
    
    @staticmethod
    def create_quality_report(
        records: List[LotteryRecord],
        raw_lines: List[str],
        errors: List[str]
    ) -> DataQualityReport:
        """
        创建数据质量报告
        
        Args:
            records: 有效记录列表
            raw_lines: 原始数据行列表
            errors: 错误信息列表
            
        Returns:
            数据质量报告
        """
        total_records = len(raw_lines)
        valid_records = len(records)
        invalid_records = total_records - valid_records
        
        # 检查重复记录
        periods = [r.period for r in records]
        duplicate_records = len(periods) - len(set(periods))
        
        # 检查日期范围
        dates = [r.date for r in records]
        date_range_start = min(dates) if dates else None
        date_range_end = max(dates) if dates else None
        
        # 检查缺失日期（简化版本）
        missing_dates = 0
        if date_range_start and date_range_end:
            expected_days = (date_range_end - date_range_start).days + 1
            actual_days = len(set(dates))
            missing_dates = max(0, expected_days - actual_days)
        
        return DataQualityReport(
            total_records=total_records,
            valid_records=valid_records,
            invalid_records=invalid_records,
            duplicate_records=duplicate_records,
            missing_dates=missing_dates,
            date_range_start=date_range_start,
            date_range_end=date_range_end,
            errors=errors
        )


# 常用的数据格式模式
DATA_PATTERNS = {
    "standard": r"^(\d{7})\s+(\d{4}-\d{2}-\d{2})\s+(\d{3})$",
    "tab_separated": r"^(\d{7})\t(\d{4}-\d{2}-\d{2})\t(\d{3})$",
    "comma_separated": r"^(\d{7}),(\d{4}-\d{2}-\d{2}),(\d{3})$",
    "pipe_separated": r"^(\d{7})\|(\d{4}-\d{2}-\d{2})\|(\d{3})$",
}


def detect_data_format(sample_lines: List[str]) -> Optional[str]:
    """
    检测数据格式
    
    Args:
        sample_lines: 样本数据行
        
    Returns:
        检测到的格式名称，未检测到返回None
    """
    for format_name, pattern in DATA_PATTERNS.items():
        matches = 0
        for line in sample_lines[:10]:  # 检查前10行
            if re.match(pattern, line.strip()):
                matches += 1
        
        # 如果大部分行都匹配，认为是该格式
        if matches >= len(sample_lines[:10]) * 0.8:
            return format_name
    
    return None


if __name__ == "__main__":
    # 测试代码
    from datetime import date
    
    # 测试记录创建
    try:
        record = LotteryRecord(
            period="2024001",
            date=date(2024, 1, 1),
            numbers="123"
        )
        print(f"记录创建成功: {record}")
        print(f"号码列表: {record.number_list}")
        print(f"和值: {record.sum_value}")
        print(f"跨度: {record.span_value}")
        print(f"字典格式: {record.to_dict()}")
        
    except ValueError as e:
        print(f"记录创建失败: {e}")
    
    # 测试数据验证
    test_lines = [
        "2024001\t2024-01-01\t123",
        "2024002 2024-01-02 456",
        "invalid line",
        "2024003,2024-01-03,789"
    ]
    
    print("\n测试数据验证:")
    for line in test_lines:
        result = DataValidator.validate_record_line(line)
        print(f"'{line}' -> {result}")
    
    # 测试格式检测
    print(f"\n检测到的数据格式: {detect_data_format(test_lines)}")
