#!/usr/bin/env python3
"""
修复版FastAPI主应用
"""

from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime
import time
import sys
import logging
import os
sys.path.append('src')

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="福彩3D数据分析API",
    description="高性能彩票数据查询和分析服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局数据引擎实例
data_engine = None

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global data_engine
    logger.info("🚀 FastAPI应用启动中...")
    
    try:
        # 延迟导入，避免初始化问题
        from core.data_engine import DataEngine
        
        # 确保数据目录存在
        os.makedirs("data", exist_ok=True)
        
        # 初始化数据引擎
        data_engine = DataEngine("data/lottery.db")
        
        # 检查数据库是否有数据
        record_count = data_engine.db_manager.get_records_count()
        if record_count == 0:
            logger.info("数据库为空，从文件加载数据...")
            try:
                from data.parser import DataParser
                
                data_file = 'data/raw/3d_data_20250714_144231.txt'
                if os.path.exists(data_file):
                    with open(data_file, 'r', encoding='utf-8') as f:
                        raw_data = f.read()
                    
                    parser = DataParser()
                    records, quality_report = parser.parse_data(raw_data)
                    data_engine.load_data_from_records(records, save_to_db=True)
                    logger.info(f"数据加载完成: {len(records)} 条记录")
                else:
                    logger.warning(f"数据文件不存在: {data_file}")
            except Exception as e:
                logger.error(f"数据加载失败: {e}")
        else:
            logger.info(f"数据库已有数据: {record_count} 条记录")
        
        logger.info("✅ FastAPI应用启动完成")
    except Exception as e:
        logger.error(f"❌ 应用启动失败: {e}")
        # 即使初始化失败，也不阻止应用启动
        data_engine = None

@app.get("/health")
async def health_check():
    """健康检查接口"""
    try:
        if data_engine is None:
            return {
                "status": "partial",
                "message": "API running but data engine not initialized",
                "timestamp": datetime.now().isoformat(),
                "database_records": 0,
                "date_range": "No data"
            }
        
        record_count = data_engine.db_manager.get_records_count()
        date_range = data_engine.db_manager.get_date_range()
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "database_records": record_count,
            "date_range": f"{date_range[0]} to {date_range[1]}" if date_range and date_range[0] else "No data"
        }
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return {
            "status": "error", 
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.get("/api/v1/stats/basic")
async def get_basic_stats(use_cache: bool = Query(True, description="是否使用缓存")):
    """获取基础统计信息"""
    if data_engine is None:
        return {"error": "Data engine not initialized"}
    
    try:
        start_time = time.time()
        stats = data_engine.get_basic_stats(use_cache=use_cache)
        query_time = time.time() - start_time
        
        return {
            "total_records": stats["total_records"],
            "date_range": stats["date_range"],
            "sum_value_stats": stats["sum_value_stats"],
            "span_value_stats": stats["span_value_stats"],
            "sales_amount_stats": stats["sales_amount_stats"],
            "query_time_ms": round(query_time * 1000, 2),
            "cached": use_cache
        }
    except Exception as e:
        logger.error(f"Basic stats error: {e}")
        return {"error": str(e)}

@app.get("/api/v1/analysis/frequency")
async def get_frequency_analysis(
    position: str = Query("all", description="分析位置: all, hundreds, tens, units"),
    use_cache: bool = Query(True, description="是否使用缓存")
):
    """获取号码频率分析"""
    if data_engine is None:
        return {"error": "Data engine not initialized"}
    
    try:
        if position not in ["all", "hundreds", "tens", "units"]:
            return {"error": "Invalid position parameter"}
        
        start_time = time.time()
        analysis = data_engine.get_frequency_analysis(position, use_cache=use_cache)
        query_time = time.time() - start_time
        
        return {
            "position": position,
            "analysis": analysis,
            "query_time_ms": round(query_time * 1000, 2),
            "cached": use_cache
        }
    except Exception as e:
        logger.error(f"Frequency analysis error: {e}")
        return {"error": str(e)}

@app.get("/api/v1/analysis/sum-distribution")
async def get_sum_distribution(use_cache: bool = Query(True, description="是否使用缓存")):
    """获取和值分布分析"""
    if data_engine is None:
        return {"error": "Data engine not initialized"}
    
    try:
        start_time = time.time()
        distribution = data_engine.get_sum_distribution(use_cache=use_cache)
        query_time = time.time() - start_time
        
        return {
            "sum_distribution": distribution["sum_distribution"],
            "trial_sum_distribution": distribution["trial_sum_distribution"],
            "query_time_ms": round(query_time * 1000, 2),
            "cached": use_cache
        }
    except Exception as e:
        logger.error(f"Sum distribution error: {e}")
        return {"error": str(e)}

@app.get("/api/v1/analysis/sales")
async def get_sales_analysis(use_cache: bool = Query(True, description="是否使用缓存")):
    """获取销售额分析"""
    if data_engine is None:
        return {"error": "Data engine not initialized"}
    
    try:
        start_time = time.time()
        analysis = data_engine.get_sales_analysis(use_cache=use_cache)
        query_time = time.time() - start_time
        
        return {
            "overall": analysis["overall"],
            "yearly": analysis["yearly"],
            "query_time_ms": round(query_time * 1000, 2),
            "cached": use_cache
        }
    except Exception as e:
        logger.error(f"Sales analysis error: {e}")
        return {"error": str(e)}

@app.get("/api/v1/data/query")
async def query_data(
    start_date: str = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: str = Query(None, description="结束日期 (YYYY-MM-DD)"),
    min_sum: int = Query(None, description="最小和值"),
    max_sum: int = Query(None, description="最大和值"),
    limit: int = Query(100, description="返回记录数限制", le=1000),
    use_polars: bool = Query(False, description="是否强制使用Polars引擎")
):
    """数据查询接口"""
    if data_engine is None:
        return {"error": "Data engine not initialized"}
    
    try:
        start_time = time.time()
        
        if start_date and end_date:
            df = data_engine.query_by_date_range(start_date, end_date, use_polars=use_polars)
        elif min_sum is not None and max_sum is not None:
            df = data_engine.query_by_sum_range(min_sum, max_sum, use_polars=use_polars)
        else:
            return {"error": "Must provide either date range or sum range"}
        
        if len(df) > limit:
            df = df.head(limit)
        
        records = df.to_dicts()
        query_time = time.time() - start_time
        
        return {
            "records": records,
            "total_count": len(records),
            "query_time_ms": round(query_time * 1000, 2),
            "query_params": {
                "start_date": start_date,
                "end_date": end_date,
                "min_sum": min_sum,
                "max_sum": max_sum,
                "limit": limit,
                "use_polars": use_polars
            }
        }
    except Exception as e:
        logger.error(f"Data query error: {e}")
        return {"error": str(e)}

@app.get("/api/v1/system/performance")
async def get_performance_stats():
    """获取系统性能统计"""
    if data_engine is None:
        return {"error": "Data engine not initialized"}
    
    try:
        perf_stats = data_engine.get_performance_stats()
        db_info = data_engine.db_manager.get_database_info()
        
        return {
            "performance_stats": perf_stats,
            "database_info": db_info,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Performance stats error: {e}")
        return {"error": str(e)}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=False)
