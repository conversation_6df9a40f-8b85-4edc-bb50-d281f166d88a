#!/usr/bin/env python3
"""
测试AI库安装状态
"""

def test_ai_libraries():
    """测试AI库"""
    print("🧪 测试AI库安装状态...")
    
    success_count = 0
    total_count = 0
    
    # 测试scikit-learn
    total_count += 1
    try:
        from sklearn.feature_extraction.text import TfidfVectorizer
        print("✅ scikit-learn - TfidfVectorizer可用")
        success_count += 1
    except Exception as e:
        print(f"❌ scikit-learn - 错误: {e}")
    
    # 测试transformers
    total_count += 1
    try:
        import transformers
        print(f"✅ transformers - 版本 {transformers.__version__}")
        success_count += 1
    except Exception as e:
        print(f"❌ transformers - 错误: {e}")
    
    # 测试torch
    total_count += 1
    try:
        import torch
        print(f"✅ torch - 版本 {torch.__version__}")
        success_count += 1
    except Exception as e:
        print(f"❌ torch - 错误: {e}")
    
    # 测试sentence-transformers
    total_count += 1
    try:
        from sentence_transformers import SentenceTransformer
        print("✅ sentence-transformers可用")
        success_count += 1
    except Exception as e:
        print(f"❌ sentence-transformers - 错误: {e}")
    
    # 测试numpy
    total_count += 1
    try:
        import numpy as np
        print(f"✅ numpy - 版本 {np.__version__}")
        success_count += 1
    except Exception as e:
        print(f"❌ numpy - 错误: {e}")
    
    print(f"\n📊 测试结果: {success_count}/{total_count} 成功")
    
    if success_count == total_count:
        print("🎉 所有AI库测试通过！")
        return True
    else:
        print("⚠️ 部分AI库测试失败")
        return False

if __name__ == "__main__":
    test_ai_libraries()
