#!/usr/bin/env python3
"""
Bug数据验证工具
"""

import json
import re


def validate_bug_data(bug_data, validation_rules):
    """验证Bug数据是否符合规则"""
    errors = []
    
    for field, rules in validation_rules.items():
        value = bug_data.get(field)
        
        # 检查必填字段
        if rules.get('required', False) and (value is None or value == ''):
            errors.append(f"Field '{field}' is required")
            continue
        
        if value is not None and value != '':
            # 检查长度限制
            if 'max_length' in rules and len(str(value)) > rules['max_length']:
                errors.append(f"Field '{field}' exceeds maximum length of {rules['max_length']}")
            
            if 'min_length' in rules and len(str(value)) < rules['min_length']:
                errors.append(f"Field '{field}' is below minimum length of {rules['min_length']}")
            
            # 检查允许的值
            if 'allowed_values' in rules and value not in rules['allowed_values']:
                errors.append(f"Field '{field}' has invalid value '{value}'. Allowed: {rules['allowed_values']}")
            
            # 检查正则模式
            if 'pattern' in rules:
                import re
                if not re.match(rules['pattern'], str(value)):
                    errors.append(f"Field '{field}' does not match required pattern '{rules['pattern']}'")
    
    return errors
