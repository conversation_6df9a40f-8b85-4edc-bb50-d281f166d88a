#!/usr/bin/env python3
"""
最终变量作用域修复验证
确保所有修复都正常工作且无副作用
"""

import sys
import os
import requests
import time
import subprocess

def verify_api_service():
    """验证API服务状态"""
    print("=== 验证API服务状态 ===")
    
    try:
        response = requests.get("http://127.0.0.1:8888/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API服务正常运行")
            print(f"   状态: {data.get('status', 'N/A')}")
            print(f"   数据库记录: {data.get('database_records', 'N/A')}")
            print(f"   数据范围: {data.get('date_range', 'N/A')}")
            return True
        else:
            print(f"❌ API服务异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False

def verify_intelligent_fusion_fix():
    """验证智能融合修复效果"""
    print("\n=== 验证智能融合修复效果 ===")
    
    try:
        # 测试所有融合模式
        fusion_modes = ["性能评估", "权重计算", "预测融合", "置信度校准"]
        
        for mode in fusion_modes:
            response = requests.get(
                "http://127.0.0.1:8888/api/v1/prediction/intelligent-fusion/predict",
                params={
                    "max_candidates": 15,
                    "confidence_threshold": 0.6,
                    "auto_train": True,
                    "mode": mode.lower()
                },
                timeout=90
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success', False):
                    prediction = result.get('prediction', {})
                    
                    if 'error' not in prediction:
                        print(f"✅ {mode}模式正常工作")
                        
                        # 验证关键字段
                        numbers = prediction.get('numbers')
                        confidence = prediction.get('confidence', 0)
                        candidates = prediction.get('candidates', [])
                        
                        if numbers and confidence > 0 and candidates:
                            print(f"   推荐号码: {numbers}")
                            print(f"   置信度: {confidence:.1%}")
                            print(f"   候选数量: {len(candidates)}")
                            
                            # 验证候选数据结构（这是修复的关键点）
                            first_candidate = candidates[0]
                            required_fields = ['numbers', 'confidence']
                            
                            missing_fields = [f for f in required_fields if f not in first_candidate]
                            if not missing_fields:
                                print(f"   ✅ 候选数据结构完整")
                            else:
                                print(f"   ❌ 缺少字段: {missing_fields}")
                                return False
                        else:
                            print(f"   ❌ {mode}模式数据不完整")
                            return False
                    else:
                        print(f"   ❌ {mode}模式预测失败: {prediction['error']}")
                        return False
                else:
                    print(f"   ❌ {mode}模式API调用失败: {result.get('error', '未知错误')}")
                    return False
            else:
                print(f"   ❌ {mode}模式HTTP请求失败: {response.status_code}")
                return False
            
            time.sleep(2)  # 避免请求过于频繁
        
        print("✅ 所有智能融合模式验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 智能融合修复验证失败: {e}")
        return False

def verify_ui_components():
    """验证UI组件修复"""
    print("\n=== 验证UI组件修复 ===")
    
    try:
        # 测试修复后的UI组件导入
        from ui.intelligent_fusion_components import (
            show_adaptive_fusion_tab,
            safe_initialize_intelligent_system,
            safe_initialize_adaptive_fusion,
            safe_initialize_database_manager,
            validate_variable_initialization,
            safe_variable_access,
            check_runtime_state,
            create_safe_execution_context,
            handle_fusion_error,
            validate_fusion_prerequisites,
            INTELLIGENT_FUSION_AVAILABLE
        )
        
        print("✅ 所有修复函数导入成功")
        print(f"   INTELLIGENT_FUSION_AVAILABLE: {INTELLIGENT_FUSION_AVAILABLE}")
        
        # 测试安全初始化函数
        context = create_safe_execution_context()
        print("✅ 安全执行上下文创建成功")
        
        # 测试运行时状态检查
        state = check_runtime_state()
        print("✅ 运行时状态检查正常")
        print(f"   智能融合可用: {state['intelligent_fusion_available']}")
        print(f"   数据库可访问: {state['database_accessible']}")
        
        # 测试变量验证
        uninitialized = validate_variable_initialization(None, "test", None)
        if len(uninitialized) == 2:
            print("✅ 变量验证函数工作正常")
        else:
            print("❌ 变量验证函数异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ UI组件验证失败: {e}")
        return False

def verify_no_regression():
    """验证无回归问题"""
    print("\n=== 验证无回归问题 ===")
    
    try:
        # 运行之前的测试确保没有破坏现有功能
        test_files = [
            "test_variable_scope_fix.py",
            "test_intelligent_fusion_complete.py"
        ]
        
        for test_file in test_files:
            if os.path.exists(test_file):
                print(f"运行 {test_file}...")
                
                result = subprocess.run(
                    [sys.executable, test_file],
                    capture_output=True,
                    text=True,
                    timeout=120
                )
                
                if result.returncode == 0:
                    print(f"✅ {test_file} 通过")
                else:
                    print(f"❌ {test_file} 失败")
                    print(f"   错误: {result.stderr}")
                    return False
            else:
                print(f"⚠️ {test_file} 不存在，跳过")
        
        print("✅ 无回归问题检测通过")
        return True
        
    except Exception as e:
        print(f"❌ 回归测试失败: {e}")
        return False

def verify_error_prevention():
    """验证错误预防机制"""
    print("\n=== 验证错误预防机制 ===")
    
    try:
        # 模拟原始错误场景，确保不再出现
        def test_original_error_scenario():
            # 这是修复前会出现错误的模式
            try:
                if False:  # 条件不满足
                    intelligentFusionSystem = "defined"
                
                # 尝试访问变量
                return intelligentFusionSystem
            except (NameError, UnboundLocalError) as e:
                return f"错误被正确捕获: {type(e).__name__}"
        
        result = test_original_error_scenario()
        if "错误被正确捕获" in result:
            print("✅ 原始错误模式被正确处理")
        else:
            print("❌ 原始错误模式处理异常")
            return False
        
        # 测试修复后的安全模式
        from ui.intelligent_fusion_components import create_safe_execution_context
        
        context = create_safe_execution_context()
        if context and 'fusion_system' in context:
            print("✅ 修复后的安全模式工作正常")
        else:
            print("❌ 修复后的安全模式异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 错误预防验证失败: {e}")
        return False

def verify_performance_impact():
    """验证性能影响"""
    print("\n=== 验证性能影响 ===")
    
    try:
        import time
        
        # 测试修复后的性能
        start_time = time.time()
        
        # 执行一些修复后的操作
        from ui.intelligent_fusion_components import (
            create_safe_execution_context,
            safe_initialize_intelligent_system
        )
        
        for i in range(10):
            context = create_safe_execution_context()
            system = safe_initialize_intelligent_system()
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"✅ 性能测试完成")
        print(f"   10次操作耗时: {execution_time:.3f}秒")
        print(f"   平均每次: {execution_time/10:.3f}秒")
        
        # 如果每次操作超过1秒，可能有性能问题
        if execution_time/10 > 1.0:
            print("⚠️ 性能可能受到影响")
            return False
        else:
            print("✅ 性能影响在可接受范围内")
            return True
        
    except Exception as e:
        print(f"❌ 性能验证失败: {e}")
        return False

def verify_documentation():
    """验证文档完整性"""
    print("\n=== 验证文档完整性 ===")
    
    try:
        # 检查关键文档是否存在
        required_docs = [
            "intelligentFusionSystem变量作用域错误修复报告_2025-07-16.md",
            "variable_scope_analysis_report.md"
        ]
        
        for doc in required_docs:
            if os.path.exists(doc):
                print(f"✅ {doc} 存在")
                
                # 检查文档内容
                with open(doc, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if len(content) > 1000:  # 至少1000字符
                    print(f"   内容长度: {len(content)} 字符")
                else:
                    print(f"   ⚠️ 内容较短: {len(content)} 字符")
            else:
                print(f"❌ {doc} 不存在")
                return False
        
        print("✅ 文档完整性验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 文档验证失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🔍 开始最终变量作用域修复验证...")
    print("="*70)
    
    verifications = [
        ("API服务状态", verify_api_service),
        ("智能融合修复效果", verify_intelligent_fusion_fix),
        ("UI组件修复", verify_ui_components),
        ("无回归问题", verify_no_regression),
        ("错误预防机制", verify_error_prevention),
        ("性能影响", verify_performance_impact),
        ("文档完整性", verify_documentation)
    ]
    
    results = []
    
    for verification_name, verification_func in verifications:
        try:
            result = verification_func()
            results.append((verification_name, result))
        except Exception as e:
            print(f"❌ {verification_name}验证执行失败: {e}")
            results.append((verification_name, False))
    
    # 汇总结果
    print("\n" + "="*70)
    print("🎯 最终变量作用域修复验证结果:")
    print("="*70)
    
    passed = 0
    total = len(results)
    
    for verification_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{verification_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n📊 验证统计: {passed}/{total} 项通过")
    
    if passed == total:
        print("\n🎉 最终变量作用域修复验证完全通过！")
        print("✅ intelligentFusionSystem变量作用域错误已彻底修复")
        print("✅ 所有智能融合功能正常工作")
        print("✅ 系统稳定性得到保证")
        print("✅ 无性能影响和回归问题")
        print("✅ 防护机制完善")
        print("✅ 文档完整")
        print("\n🚀 系统现在完全可用，用户可以正常使用智能融合优化功能！")
        return True
    else:
        print(f"\n⚠️ 最终验证部分失败 ({total-passed}项)")
        print("❌ 需要进一步检查和修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
