"""
贝叶斯超参数推荐系统
实现高斯过程代理模型、采集函数优化、推荐置信度计算
"""

import numpy as np
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from scipy.optimize import minimize
from scipy.stats import norm
import warnings
warnings.filterwarnings('ignore')

try:
    from sklearn.gaussian_process import GaussianProcessRegressor
    from sklearn.gaussian_process.kernels import Matern, RBF, ConstantKernel
    from sklearn.preprocessing import StandardScaler
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False


@dataclass
class HyperparameterSpace:
    """超参数空间定义"""
    name: str
    param_type: str  # 'continuous', 'discrete', 'categorical'
    bounds: Optional[Tuple[float, float]] = None
    choices: Optional[List[Any]] = None
    log_scale: bool = False
    
    def sample(self) -> Any:
        """从参数空间采样"""
        if self.param_type == 'continuous':
            if self.log_scale:
                log_low, log_high = np.log(self.bounds[0]), np.log(self.bounds[1])
                return np.exp(np.random.uniform(log_low, log_high))
            else:
                return np.random.uniform(self.bounds[0], self.bounds[1])
        elif self.param_type == 'discrete':
            return np.random.randint(self.bounds[0], self.bounds[1] + 1)
        elif self.param_type == 'categorical':
            return np.random.choice(self.choices)
        else:
            raise ValueError(f"未知参数类型: {self.param_type}")
    
    def encode(self, value: Any) -> float:
        """编码参数值为数值"""
        if self.param_type == 'continuous':
            if self.log_scale:
                return np.log(value)
            return float(value)
        elif self.param_type == 'discrete':
            return float(value)
        elif self.param_type == 'categorical':
            return float(self.choices.index(value))
        else:
            return 0.0
    
    def decode(self, encoded_value: float) -> Any:
        """解码数值为参数值"""
        if self.param_type == 'continuous':
            if self.log_scale:
                return np.exp(encoded_value)
            return encoded_value
        elif self.param_type == 'discrete':
            return int(round(encoded_value))
        elif self.param_type == 'categorical':
            idx = int(round(encoded_value))
            idx = max(0, min(idx, len(self.choices) - 1))
            return self.choices[idx]
        else:
            return encoded_value


class BayesianHyperparameterRecommender:
    """贝叶斯超参数推荐系统"""
    
    def __init__(self):
        self.parameter_spaces = self._initialize_parameter_spaces()
        self.optimization_history: Dict[str, List[Dict[str, Any]]] = {}
        self.surrogate_models: Dict[str, Any] = {}
        self.scaler = StandardScaler() if SKLEARN_AVAILABLE else None
        
        # 采集函数配置
        self.acquisition_functions = {
            "expected_improvement": self._expected_improvement,
            "upper_confidence_bound": self._upper_confidence_bound,
            "probability_improvement": self._probability_improvement
        }
        
        self.default_acquisition = "expected_improvement"
    
    def _initialize_parameter_spaces(self) -> Dict[str, Dict[str, HyperparameterSpace]]:
        """初始化参数空间"""
        return {
            "markov_enhanced": {
                "learning_rate": HyperparameterSpace("learning_rate", "continuous", (1e-5, 1e-1), log_scale=True),
                "batch_size": HyperparameterSpace("batch_size", "discrete", (16, 128)),
                "epochs": HyperparameterSpace("epochs", "discrete", (50, 300)),
                "optimizer": HyperparameterSpace("optimizer", "categorical", choices=["adam", "sgd", "rmsprop"]),
                "dropout_rate": HyperparameterSpace("dropout_rate", "continuous", (0.0, 0.5)),
                "l2_regularization": HyperparameterSpace("l2_regularization", "continuous", (1e-6, 1e-2), log_scale=True)
            },
            "deep_learning_cnn_lstm": {
                "learning_rate": HyperparameterSpace("learning_rate", "continuous", (1e-5, 1e-2), log_scale=True),
                "batch_size": HyperparameterSpace("batch_size", "discrete", (32, 256)),
                "epochs": HyperparameterSpace("epochs", "discrete", (100, 500)),
                "optimizer": HyperparameterSpace("optimizer", "categorical", choices=["adam", "adamw", "rmsprop"]),
                "dropout_rate": HyperparameterSpace("dropout_rate", "continuous", (0.1, 0.6)),
                "lstm_units": HyperparameterSpace("lstm_units", "discrete", (64, 512)),
                "cnn_filters": HyperparameterSpace("cnn_filters", "discrete", (32, 256)),
                "l2_regularization": HyperparameterSpace("l2_regularization", "continuous", (1e-6, 1e-3), log_scale=True)
            },
            "trend_analyzer": {
                "learning_rate": HyperparameterSpace("learning_rate", "continuous", (1e-4, 1e-2), log_scale=True),
                "batch_size": HyperparameterSpace("batch_size", "discrete", (16, 64)),
                "epochs": HyperparameterSpace("epochs", "discrete", (50, 200)),
                "optimizer": HyperparameterSpace("optimizer", "categorical", choices=["adam", "sgd"]),
                "window_size": HyperparameterSpace("window_size", "discrete", (10, 50)),
                "hidden_units": HyperparameterSpace("hidden_units", "discrete", (32, 256))
            },
            "intelligent_fusion": {
                "learning_rate": HyperparameterSpace("learning_rate", "continuous", (1e-4, 1e-2), log_scale=True),
                "batch_size": HyperparameterSpace("batch_size", "discrete", (32, 128)),
                "epochs": HyperparameterSpace("epochs", "discrete", (100, 300)),
                "optimizer": HyperparameterSpace("optimizer", "categorical", choices=["adam", "adamw"]),
                "ensemble_weights": HyperparameterSpace("ensemble_weights", "continuous", (0.1, 0.9)),
                "fusion_method": HyperparameterSpace("fusion_method", "categorical", choices=["weighted", "voting", "stacking"])
            }
        }
    
    def recommend_next_hyperparameters(self, model_id: str, 
                                     optimization_target: str = "accuracy",
                                     acquisition_function: str = None) -> Dict[str, Any]:
        """推荐下一组超参数"""
        
        if acquisition_function is None:
            acquisition_function = self.default_acquisition
        
        # 获取参数空间
        parameter_space = self.parameter_spaces.get(model_id)
        if not parameter_space:
            return self._heuristic_recommendation(model_id)
        
        # 获取历史优化数据
        history = self.optimization_history.get(model_id, [])
        
        if len(history) < 3:
            # 历史数据不足，使用随机采样 + 启发式规则
            return self._heuristic_recommendation(model_id)
        
        if not SKLEARN_AVAILABLE:
            # sklearn不可用，使用启发式方法
            return self._heuristic_recommendation(model_id)
        
        try:
            # 构建代理模型
            surrogate_model = self._build_surrogate_model(history, parameter_space, optimization_target)
            self.surrogate_models[model_id] = surrogate_model
            
            # 使用采集函数寻找最优参数
            next_params = self._optimize_acquisition_function(
                surrogate_model, parameter_space, optimization_target, acquisition_function
            )
            
            # 计算推荐置信度
            confidence = self._calculate_recommendation_confidence(surrogate_model, next_params, parameter_space)
            
            # 预测性能改进
            expected_improvement = self._predict_improvement(surrogate_model, next_params, parameter_space, history)
            
            return {
                "recommended_parameters": next_params,
                "confidence": confidence,
                "expected_improvement": expected_improvement,
                "acquisition_function": acquisition_function,
                "reasoning": self._generate_recommendation_reasoning(history, next_params, model_id),
                "surrogate_model_score": getattr(surrogate_model, 'score_', 0.0)
            }
            
        except Exception as e:
            print(f"贝叶斯优化失败，使用启发式方法: {e}")
            return self._heuristic_recommendation(model_id)
    
    def _build_surrogate_model(self, history: List[Dict], parameter_space: Dict[str, HyperparameterSpace], 
                             target: str) -> Any:
        """构建高斯过程代理模型"""
        
        # 准备训练数据
        X = []  # 参数组合
        y = []  # 目标值
        
        for record in history:
            params = record["parameters"]
            target_value = record["results"].get(target, 0)
            
            # 将参数编码为数值向量
            param_vector = self._encode_parameters(params, parameter_space)
            X.append(param_vector)
            y.append(target_value)
        
        X = np.array(X)
        y = np.array(y)
        
        # 标准化特征
        if self.scaler:
            X = self.scaler.fit_transform(X)
        
        # 构建高斯过程模型
        kernel = ConstantKernel(1.0) * Matern(length_scale=1.0, nu=2.5)
        
        gp_model = GaussianProcessRegressor(
            kernel=kernel,
            alpha=1e-6,
            normalize_y=True,
            n_restarts_optimizer=5,
            random_state=42
        )
        
        gp_model.fit(X, y)
        
        return gp_model
    
    def _encode_parameters(self, params: Dict[str, Any], 
                         parameter_space: Dict[str, HyperparameterSpace]) -> List[float]:
        """将参数编码为数值向量"""
        encoded = []
        
        for param_name, param_space in parameter_space.items():
            if param_name in params:
                encoded_value = param_space.encode(params[param_name])
                encoded.append(encoded_value)
            else:
                # 使用默认值
                default_value = param_space.sample()
                encoded_value = param_space.encode(default_value)
                encoded.append(encoded_value)
        
        return encoded
    
    def _decode_parameters(self, encoded_vector: np.ndarray, 
                         parameter_space: Dict[str, HyperparameterSpace]) -> Dict[str, Any]:
        """将数值向量解码为参数"""
        decoded = {}
        
        for i, (param_name, param_space) in enumerate(parameter_space.items()):
            if i < len(encoded_vector):
                decoded_value = param_space.decode(encoded_vector[i])
                decoded[param_name] = decoded_value
        
        return decoded
    
    def _optimize_acquisition_function(self, surrogate_model: Any, 
                                     parameter_space: Dict[str, HyperparameterSpace],
                                     target: str, acquisition_function: str) -> Dict[str, Any]:
        """优化采集函数寻找最优参数"""
        
        # 获取历史最佳值
        history = self.optimization_history.get(list(parameter_space.keys())[0], [])
        if history:
            current_best = max(record["results"].get(target, 0) for record in history)
        else:
            current_best = 0.0
        
        # 定义采集函数
        acquisition_func = self.acquisition_functions[acquisition_function]
        
        def objective(x):
            """优化目标函数"""
            if self.scaler:
                x_scaled = self.scaler.transform([x])[0]
            else:
                x_scaled = x
            
            return -acquisition_func(surrogate_model, x_scaled, current_best)
        
        # 参数边界
        bounds = []
        for param_space in parameter_space.values():
            if param_space.param_type == 'continuous':
                if param_space.log_scale:
                    bounds.append((np.log(param_space.bounds[0]), np.log(param_space.bounds[1])))
                else:
                    bounds.append(param_space.bounds)
            elif param_space.param_type == 'discrete':
                bounds.append((float(param_space.bounds[0]), float(param_space.bounds[1])))
            elif param_space.param_type == 'categorical':
                bounds.append((0.0, float(len(param_space.choices) - 1)))
        
        # 多次随机初始化优化
        best_result = None
        best_value = float('inf')
        
        for _ in range(10):  # 10次随机初始化
            x0 = [np.random.uniform(bound[0], bound[1]) for bound in bounds]
            
            try:
                result = minimize(
                    objective, 
                    x0, 
                    bounds=bounds, 
                    method='L-BFGS-B'
                )
                
                if result.fun < best_value:
                    best_value = result.fun
                    best_result = result
                    
            except Exception:
                continue
        
        if best_result is None:
            # 优化失败，使用随机采样
            optimal_encoded = [np.random.uniform(bound[0], bound[1]) for bound in bounds]
        else:
            optimal_encoded = best_result.x
        
        # 解码参数
        optimal_params = self._decode_parameters(optimal_encoded, parameter_space)
        
        return optimal_params
    
    def _expected_improvement(self, model: Any, x: np.ndarray, current_best: float) -> float:
        """期望改进采集函数"""
        try:
            mu, sigma = model.predict([x], return_std=True)
            mu, sigma = mu[0], sigma[0]
            
            if sigma == 0:
                return 0
            
            improvement = mu - current_best
            z = improvement / sigma
            ei = improvement * norm.cdf(z) + sigma * norm.pdf(z)
            
            return ei
        except:
            return 0.0
    
    def _upper_confidence_bound(self, model: Any, x: np.ndarray, current_best: float, 
                              kappa: float = 2.576) -> float:
        """置信上界采集函数"""
        try:
            mu, sigma = model.predict([x], return_std=True)
            return mu[0] + kappa * sigma[0]
        except:
            return 0.0
    
    def _probability_improvement(self, model: Any, x: np.ndarray, current_best: float) -> float:
        """改进概率采集函数"""
        try:
            mu, sigma = model.predict([x], return_std=True)
            mu, sigma = mu[0], sigma[0]
            
            if sigma == 0:
                return 0
            
            z = (mu - current_best) / sigma
            return norm.cdf(z)
        except:
            return 0.0
    
    def _calculate_recommendation_confidence(self, model: Any, params: Dict[str, Any], 
                                          parameter_space: Dict[str, HyperparameterSpace]) -> float:
        """计算推荐置信度"""
        try:
            # 编码参数
            encoded_params = self._encode_parameters(params, parameter_space)
            
            if self.scaler:
                encoded_params = self.scaler.transform([encoded_params])[0]
            
            # 获取预测不确定性
            mu, sigma = model.predict([encoded_params], return_std=True)
            
            # 置信度基于预测不确定性
            confidence = 1 / (1 + sigma[0])
            
            return min(confidence, 1.0)
        except:
            return 0.5
    
    def _predict_improvement(self, model: Any, params: Dict[str, Any], 
                           parameter_space: Dict[str, HyperparameterSpace],
                           history: List[Dict]) -> float:
        """预测性能改进"""
        try:
            # 编码参数
            encoded_params = self._encode_parameters(params, parameter_space)
            
            if self.scaler:
                encoded_params = self.scaler.transform([encoded_params])[0]
            
            # 预测性能
            predicted_performance = model.predict([encoded_params])[0]
            
            # 计算相对于历史最佳的改进
            if history:
                best_historical = max(record["results"].get("accuracy", 0) for record in history)
                improvement = predicted_performance - best_historical
            else:
                improvement = predicted_performance
            
            return max(improvement, 0.0)
        except:
            return 0.0
    
    def _heuristic_recommendation(self, model_id: str) -> Dict[str, Any]:
        """启发式推荐（当贝叶斯优化不可用时）"""
        parameter_space = self.parameter_spaces.get(model_id, {})
        
        if not parameter_space:
            # 默认参数
            return {
                "recommended_parameters": {
                    "learning_rate": 0.001,
                    "batch_size": 64,
                    "epochs": 100,
                    "optimizer": "adam"
                },
                "confidence": 0.5,
                "expected_improvement": 0.0,
                "reasoning": "使用默认参数配置",
                "method": "heuristic"
            }
        
        # 从参数空间随机采样
        recommended_params = {}
        for param_name, param_space in parameter_space.items():
            recommended_params[param_name] = param_space.sample()
        
        return {
            "recommended_parameters": recommended_params,
            "confidence": 0.6,
            "expected_improvement": 0.05,
            "reasoning": f"基于{model_id}模型特性的启发式推荐",
            "method": "heuristic"
        }
    
    def _generate_recommendation_reasoning(self, history: List[Dict], params: Dict[str, Any], 
                                         model_id: str) -> str:
        """生成推荐理由"""
        reasoning_parts = []
        
        # 基于历史数据的分析
        if len(history) >= 3:
            reasoning_parts.append(f"基于{len(history)}次历史训练结果的贝叶斯优化")
            
            # 分析趋势
            recent_results = [record["results"].get("accuracy", 0) for record in history[-3:]]
            if len(recent_results) >= 2:
                trend = recent_results[-1] - recent_results[0]
                if trend > 0:
                    reasoning_parts.append("近期训练效果呈上升趋势")
                else:
                    reasoning_parts.append("近期训练效果需要优化")
        
        # 参数特点分析
        if "learning_rate" in params:
            lr = params["learning_rate"]
            if lr < 0.0001:
                reasoning_parts.append("采用较小学习率以确保稳定收敛")
            elif lr > 0.01:
                reasoning_parts.append("采用较大学习率以加速训练")
        
        if "batch_size" in params:
            bs = params["batch_size"]
            if bs >= 128:
                reasoning_parts.append("使用大批次以提高训练稳定性")
            elif bs <= 32:
                reasoning_parts.append("使用小批次以提高梯度估计精度")
        
        return "；".join(reasoning_parts) if reasoning_parts else f"针对{model_id}模型的优化推荐"
    
    def update_optimization_history(self, model_id: str, parameters: Dict[str, Any], 
                                  results: Dict[str, float]):
        """更新优化历史"""
        if model_id not in self.optimization_history:
            self.optimization_history[model_id] = []
        
        record = {
            "timestamp": np.datetime64('now').astype(str),
            "parameters": parameters,
            "results": results
        }
        
        self.optimization_history[model_id].append(record)
        
        # 保持历史记录数量限制
        if len(self.optimization_history[model_id]) > 100:
            self.optimization_history[model_id] = self.optimization_history[model_id][-100:]
    
    def get_optimization_statistics(self, model_id: str) -> Dict[str, Any]:
        """获取优化统计信息"""
        history = self.optimization_history.get(model_id, [])
        
        if not history:
            return {"total_experiments": 0}
        
        accuracies = [record["results"].get("accuracy", 0) for record in history]
        
        return {
            "total_experiments": len(history),
            "best_accuracy": max(accuracies),
            "average_accuracy": np.mean(accuracies),
            "accuracy_std": np.std(accuracies),
            "improvement_trend": accuracies[-1] - accuracies[0] if len(accuracies) > 1 else 0,
            "has_surrogate_model": model_id in self.surrogate_models
        }


def test_bayesian_recommender():
    """测试贝叶斯超参数推荐系统"""
    print("🧪 测试贝叶斯超参数推荐系统...")
    
    recommender = BayesianHyperparameterRecommender()
    
    # 模拟历史优化数据
    for i in range(5):
        params = {
            "learning_rate": np.random.uniform(0.0001, 0.01),
            "batch_size": np.random.choice([32, 64, 128]),
            "epochs": np.random.randint(50, 200),
            "optimizer": np.random.choice(["adam", "sgd", "rmsprop"])
        }
        
        # 模拟训练结果
        results = {
            "accuracy": np.random.uniform(0.7, 0.9),
            "loss": np.random.uniform(0.1, 0.5)
        }
        
        recommender.update_optimization_history("intelligent_fusion", params, results)
    
    # 获取推荐
    recommendation = recommender.recommend_next_hyperparameters("intelligent_fusion")
    
    print(f"📊 推荐参数: {recommendation['recommended_parameters']}")
    print(f"🎯 置信度: {recommendation['confidence']:.3f}")
    print(f"📈 预期改进: {recommendation['expected_improvement']:.3f}")
    print(f"💡 推荐理由: {recommendation['reasoning']}")
    
    # 获取统计信息
    stats = recommender.get_optimization_statistics("intelligent_fusion")
    print(f"📈 优化统计: {stats}")
    
    print("✅ 贝叶斯超参数推荐系统测试完成！")


if __name__ == "__main__":
    test_bayesian_recommender()
