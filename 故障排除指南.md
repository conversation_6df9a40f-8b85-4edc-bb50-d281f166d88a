# 🛠️ 福彩3D预测系统 - 故障排除指南

## 📋 目录
- [常见问题](#常见问题)
- [页面问题](#页面问题)
- [API服务问题](#api服务问题)
- [数据库问题](#数据库问题)
- [性能问题](#性能问题)
- [系统维护](#系统维护)

## 🔧 常见问题

### 问题1: 页面显示空白或只显示标题
**症状**: 访问页面时只看到标题，没有内容显示

**解决方案**:
1. **刷新浏览器页面**: 按F5或Ctrl+R
2. **清除浏览器缓存**: 
   - Chrome: Ctrl+Shift+Delete → 选择"缓存的图片和文件"
   - 或直接清除127.0.0.1:8501的缓存
3. **重启Streamlit服务**:
   ```bash
   # 停止当前服务（Ctrl+C）
   # 重新启动
   cd D:\github\3dyuce
   python start_streamlit.py
   ```
4. **检查控制台错误**: 打开浏览器开发者工具(F12)查看错误信息

### 问题2: 侧边栏显示多个相似页面
**症状**: 看到"real time monitoring backup"、"real time monitoring old"等重复页面

**解决方案**:
1. **重启Streamlit服务**: 这通常是缓存问题
2. **检查pages目录**: 确保没有重复的.py文件
   ```bash
   cd D:\github\3dyuce\src\ui\pages
   dir *.py
   ```
3. **清理Python缓存**:
   ```bash
   cd D:\github\3dyuce\src\ui\pages
   rmdir /s __pycache__
   ```

### 问题3: API连接失败
**症状**: 页面显示"连接API失败"或"HTTPConnectionPool"错误

**解决方案**:
1. **检查API服务状态**:
   ```bash
   # 访问健康检查端点
   curl http://127.0.0.1:8888/health
   ```
2. **启动API服务**:
   ```bash
   cd D:\github\3dyuce
   python start_production_api.py
   ```
3. **检查端口占用**:
   ```bash
   netstat -ano | findstr :8888
   ```

## 📊 页面问题

### 实时监控页面问题
**问题**: 监控页面不显示数据或显示错误

**诊断步骤**:
1. 检查页面是否正确加载
2. 验证刷新按钮是否工作
3. 测试复选框交互功能

**解决方案**:
- 确保`show_real_time_monitoring()`函数正确定义
- 检查导入语句是否正确
- 验证随机数据生成是否正常

### 数据管理页面问题
**问题**: 数据质量分析显示0.000或无数据

**解决方案**:
1. **检查API服务**: 确保API服务正在运行
2. **验证数据库连接**: 检查lottery.db文件是否存在
3. **测试数据获取**: 手动调用API端点测试

### 优化建议页面问题
**问题**: 优化建议不显示或显示错误

**解决方案**:
1. 检查模型文件是否存在
2. 验证优化算法是否正常工作
3. 确认数据格式是否正确

## 🌐 API服务问题

### 启动失败
**错误信息**: "Address already in use" 或端口占用

**解决方案**:
1. **查找占用进程**:
   ```bash
   netstat -ano | findstr :8888
   taskkill /PID <进程ID> /F
   ```
2. **更改端口**: 修改配置文件中的端口设置
3. **重启系统**: 如果进程无法终止

### 响应超时
**症状**: API请求超时或响应缓慢

**解决方案**:
1. **检查系统资源**: CPU和内存使用情况
2. **优化数据库查询**: 检查是否有慢查询
3. **增加超时时间**: 修改客户端超时设置

## 💾 数据库问题

### 数据库文件缺失
**错误**: "no such file or directory: lottery.db"

**解决方案**:
1. **检查文件路径**: 确认data/lottery.db存在
2. **重新创建数据库**: 运行数据初始化脚本
3. **恢复备份**: 从备份文件恢复数据库

### 数据同步问题
**症状**: 数据不是最新的或同步失败

**解决方案**:
1. **手动触发同步**:
   ```bash
   python scripts/update_data.py
   ```
2. **检查网络连接**: 确保能访问数据源
3. **验证数据格式**: 检查数据源格式是否变化

## ⚡ 性能问题

### 页面加载缓慢
**症状**: 页面加载时间超过5秒

**优化方案**:
1. **启用缓存**: 确保Streamlit缓存正常工作
2. **优化查询**: 减少不必要的数据库查询
3. **压缩资源**: 优化图片和静态资源

### 内存使用过高
**症状**: 系统内存使用率超过90%

**解决方案**:
1. **重启服务**: 定期重启Streamlit和API服务
2. **优化代码**: 检查内存泄漏
3. **增加系统内存**: 如果条件允许

## 🔄 系统维护

### 日常维护任务
1. **日志清理**: 定期清理日志文件
   ```bash
   # 清理7天前的日志
   forfiles /p "data\logs" /m *.log /d -7 /c "cmd /c del @path"
   ```

2. **数据备份**: 定期备份数据库
   ```bash
   copy data\lottery.db data\backup\lottery_backup_%date%.db
   ```

3. **性能监控**: 检查系统资源使用情况
   ```bash
   # 检查磁盘空间
   dir data /s
   # 检查内存使用
   tasklist /fi "imagename eq python.exe"
   ```

### 定期更新
1. **数据更新**: 每天21:30自动更新（通过APScheduler）
2. **模型重训练**: 根据新数据定期重训练模型
3. **系统更新**: 定期更新依赖包和系统组件

## 📞 获取帮助

### 日志文件位置
- **Streamlit日志**: 控制台输出
- **API日志**: `data/logs/api_*.log`
- **调度器日志**: `data/logs/scheduler_*.log`
- **错误日志**: `data/logs/error_*.log`

### 联系支持
如果以上解决方案都无法解决问题，请：
1. 收集相关日志文件
2. 记录错误复现步骤
3. 提供系统环境信息
4. 联系技术支持团队

## 🎯 预防措施

### 最佳实践
1. **定期备份**: 每天备份重要数据
2. **监控资源**: 定期检查系统资源使用情况
3. **更新维护**: 保持系统和依赖包更新
4. **测试验证**: 重要更改前先在测试环境验证

### 系统要求
- **操作系统**: Windows 10/11
- **Python版本**: 3.11.9
- **内存**: 最少4GB，推荐8GB
- **磁盘空间**: 最少2GB可用空间
- **网络**: 稳定的互联网连接（用于数据更新）

---

**最后更新**: 2025-07-23
**版本**: v2.0 (页面修复版)
