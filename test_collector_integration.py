#!/usr/bin/env python3
"""
数据采集器集成测试
"""

import sys
sys.path.append('src')

from data.collector import LotteryDataCollector

def test_collector_integration():
    print("🔍 测试数据采集器集成...")
    
    # 测试1: 创建数据采集器
    try:
        collector = LotteryDataCollector()
        print("✅ 数据采集器创建成功")
    except Exception as e:
        print(f"❌ 数据采集器创建失败: {e}")
        return False
    
    # 测试2: 获取数据源状态
    try:
        status = collector.get_data_source_status()
        print("✅ 数据源状态获取成功")
        
        if 'cache_status' in status:
            cache_status = status['cache_status']
            print(f"   缓存命中率: {cache_status.get('cache_hit_rate', 0):.2%}")
            print(f"   缓存文件数: {cache_status.get('cache_files', 0)}")
        
        if 'health_status' in status:
            health_status = status['health_status']
            healthy_text = "健康" if health_status.get('healthy', False) else "不健康"
            print(f"   健康状态: {healthy_text}")
            print(f"   检查项目数: {len(health_status.get('checks', []))}")
            
    except Exception as e:
        print(f"❌ 数据源状态获取失败: {e}")
        return False
    
    # 测试3: 使用增强方法获取数据（仅测试缓存，不实际网络请求）
    try:
        # 先测试缓存获取
        cached_data = collector.fetch_data_enhanced(use_cache=True, parse=False)
        if cached_data:
            print(f"✅ 缓存数据获取成功: {len(cached_data)} 字符")
        else:
            print("ℹ️  无缓存数据，这是正常的")
            
    except Exception as e:
        print(f"❌ 增强数据获取失败: {e}")
        return False
    
    # 测试4: 清理缓存
    try:
        cache_cleared = collector.clear_cache()
        if cache_cleared:
            print("✅ 缓存清理成功")
        else:
            print("⚠️  缓存清理失败")
    except Exception as e:
        print(f"❌ 缓存清理失败: {e}")
        return False
    
    # 清理
    try:
        collector.close()
        print("✅ 数据采集器关闭成功")
    except Exception as e:
        print(f"⚠️  关闭数据采集器时出现问题: {e}")
    
    print("🎉 数据采集器集成测试完成！")
    return True

if __name__ == "__main__":
    test_collector_integration()
