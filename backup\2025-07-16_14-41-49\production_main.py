#!/usr/bin/env python3
"""
生产版FastAPI主应用

完整功能的高性能RESTful API服务
"""

import logging
import os
import sys
import time
from datetime import datetime

from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse

sys.path.append('src')

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="福彩3D数据分析API",
    description="高性能彩票数据查询和分析服务",
    version="1.0.0",
    docs_url=None,  # 禁用默认docs
    redoc_url="/redoc"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:8501", "http://127.0.0.1:8501"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局数据引擎实例
data_engine = None
data_update_service = None
prediction_service = None

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global data_engine
    logger.info("🚀 FastAPI应用启动中...")
    
    try:
        # 延迟导入，避免初始化问题
        from core.data_engine import DataEngine

        # 确保数据目录存在
        os.makedirs("data", exist_ok=True)
        
        # 初始化数据引擎
        data_engine = DataEngine("data/lottery.db")

        # 检查数据库是否有数据
        record_count = data_engine.db_manager.get_records_count()
        logger.info(f"数据库记录数: {record_count}")

        if record_count == 0:
            logger.info("数据库为空，从文件加载数据...")
            try:
                from data.parser import DataParser
                
                data_file = 'data/raw/3d_data_20250714_144231.txt'
                if os.path.exists(data_file):
                    with open(data_file, 'r', encoding='utf-8') as f:
                        raw_data = f.read()
                    
                    parser = DataParser()
                    records, quality_report = parser.parse_data(raw_data)
                    data_engine.load_data_from_records(records, save_to_db=True)
                    logger.info(f"数据加载完成: {len(records)} 条记录")
                else:
                    logger.warning(f"数据文件不存在: {data_file}")
            except Exception as e:
                logger.error(f"数据加载失败: {e}")
        else:
            # 数据库有数据，加载到Polars引擎
            try:
                data_engine.load_data_from_database()
                logger.info("数据已从数据库加载到Polars引擎")
            except Exception as e:
                logger.error(f"从数据库加载数据到Polars引擎失败: {e}")

        # 初始化数据更新服务
        try:
            from services.data_update_service import DataUpdateService
            global data_update_service
            data_update_service = DataUpdateService(data_engine=data_engine)
            logger.info("数据更新服务初始化完成")
        except Exception as e:
            logger.error(f"数据更新服务初始化失败: {e}")

        # 初始化预测服务
        try:
            from prediction.prediction_service import PredictionService
            global prediction_service
            prediction_service = PredictionService(data_engine=data_engine)
            logger.info("预测服务初始化完成")
        except Exception as e:
            logger.error(f"预测服务初始化失败: {e}")

        logger.info("✅ FastAPI应用启动完成")
    except Exception as e:
        logger.error(f"❌ 应用启动失败: {e}")
        import traceback
        traceback.print_exc()

@app.get("/health")
async def health_check():
    """健康检查接口"""
    try:
        if data_engine is None:
            return {
                "status": "error",
                "message": "Data engine not initialized",
                "timestamp": datetime.now().isoformat(),
                "database_records": 0,
                "date_range": "No data"
            }
        
        record_count = data_engine.db_manager.get_records_count()
        date_range = data_engine.db_manager.get_date_range()
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "database_records": record_count,
            "date_range": f"{date_range[0]} to {date_range[1]}" if date_range and date_range[0] else "No data"
        }
    except Exception as e:
        logger.error(f"Health check error: {e}")
        import traceback
        traceback.print_exc()
        return {
            "status": "error", 
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.get("/api/v1/stats/basic")
async def get_basic_stats(use_cache: bool = Query(True, description="是否使用缓存")):
    """获取基础统计信息"""
    if data_engine is None:
        raise HTTPException(status_code=503, detail="Data engine not initialized")
    
    try:
        start_time = time.time()
        stats = data_engine.get_basic_stats(use_cache=use_cache)
        query_time = time.time() - start_time

        # 防御性编程：检查返回的数据结构
        if not stats:
            logger.error("get_basic_stats returned empty result")
            raise HTTPException(status_code=500, detail="No statistics data available")

        # 记录调试信息
        logger.info(f"Stats keys: {list(stats.keys())}")

        # 安全地提取字段，提供默认值
        result = {
            "total_records": stats.get("total_records", 0),
            "date_range": stats.get("date_range", {"start": "N/A", "end": "N/A"}),
            "sum_value_stats": stats.get("sum_value_stats", {}),
            "span_value_stats": stats.get("span_value_stats", {}),
            "sales_amount_stats": stats.get("sales_amount_stats", {}),
            "query_time_ms": round(query_time * 1000, 2),
            "cached": use_cache
        }

        return result

    except Exception as e:
        logger.error(f"Basic stats error: {e}")
        logger.error(f"Data engine state: {data_engine is not None}")
        if data_engine and hasattr(data_engine, 'polars_engine'):
            logger.error(f"Polars engine DF state: {data_engine.polars_engine.df is not None}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/analysis/frequency")
async def get_frequency_analysis(
    position: str = Query("all", description="分析位置: all, hundreds, tens, units"),
    use_cache: bool = Query(True, description="是否使用缓存")
):
    """获取号码频率分析"""
    if data_engine is None:
        raise HTTPException(status_code=503, detail="Data engine not initialized")
    
    try:
        if position not in ["all", "hundreds", "tens", "units"]:
            raise HTTPException(status_code=400, detail="Invalid position parameter")
        
        start_time = time.time()
        analysis = data_engine.get_frequency_analysis(position, use_cache=use_cache)
        query_time = time.time() - start_time
        
        return {
            "position": position,
            "analysis": analysis,
            "query_time_ms": round(query_time * 1000, 2),
            "cached": use_cache
        }
    except Exception as e:
        logger.error(f"Frequency analysis error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/analysis/sum-distribution")
async def get_sum_distribution(use_cache: bool = Query(True, description="是否使用缓存")):
    """获取和值分布分析"""
    if data_engine is None:
        raise HTTPException(status_code=503, detail="Data engine not initialized")
    
    try:
        start_time = time.time()
        distribution = data_engine.get_sum_distribution(use_cache=use_cache)
        query_time = time.time() - start_time
        
        return {
            "sum_distribution": distribution["sum_distribution"],
            "trial_sum_distribution": distribution["trial_sum_distribution"],
            "query_time_ms": round(query_time * 1000, 2),
            "cached": use_cache
        }
    except Exception as e:
        logger.error(f"Sum distribution error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/analysis/sales")
async def get_sales_analysis(use_cache: bool = Query(True, description="是否使用缓存")):
    """获取销售额分析"""
    if data_engine is None:
        raise HTTPException(status_code=503, detail="Data engine not initialized")
    
    try:
        start_time = time.time()
        analysis = data_engine.get_sales_analysis(use_cache=use_cache)
        query_time = time.time() - start_time
        
        return {
            "overall": analysis["overall"],
            "yearly": analysis["yearly"],
            "query_time_ms": round(query_time * 1000, 2),
            "cached": use_cache
        }
    except Exception as e:
        logger.error(f"Sales analysis error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/data/query")
async def query_data(
    start_date: str = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: str = Query(None, description="结束日期 (YYYY-MM-DD)"),
    min_sum: int = Query(None, description="最小和值"),
    max_sum: int = Query(None, description="最大和值"),
    limit: int = Query(100, description="返回记录数限制", le=1000),
    use_polars: bool = Query(False, description="是否强制使用Polars引擎")
):
    """数据查询接口"""
    if data_engine is None:
        raise HTTPException(status_code=503, detail="Data engine not initialized")
    
    try:
        start_time = time.time()
        
        if start_date and end_date:
            df = data_engine.query_by_date_range(start_date, end_date, use_polars=use_polars)
        elif min_sum is not None and max_sum is not None:
            df = data_engine.query_by_sum_range(min_sum, max_sum, use_polars=use_polars)
        else:
            raise HTTPException(status_code=400, detail="Must provide either date range or sum range")
        
        if len(df) > limit:
            df = df.head(limit)
        
        records = df.to_dicts()
        query_time = time.time() - start_time
        
        return {
            "records": records,
            "total_count": len(records),
            "query_time_ms": round(query_time * 1000, 2),
            "query_params": {
                "start_date": start_date,
                "end_date": end_date,
                "min_sum": min_sum,
                "max_sum": max_sum,
                "limit": limit,
                "use_polars": use_polars
            }
        }
    except Exception as e:
        logger.error(f"Data query error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/analysis/trends")
async def get_trends_analysis(
    days: int = Query(30, description="分析天数", ge=1, le=365),
    use_cache: bool = Query(True, description="是否使用缓存")
):
    """获取趋势分析"""
    if data_engine is None:
        raise HTTPException(status_code=503, detail="Data engine not initialized")
    
    try:
        start_time = time.time()
        trends = data_engine.get_recent_trends(days, use_cache=use_cache)
        query_time = time.time() - start_time
        
        return {
            "period": trends.get("period", f"最近{days}期"),
            "trends": trends,
            "query_time_ms": round(query_time * 1000, 2),
            "cached": use_cache
        }
    except Exception as e:
        logger.error(f"Trends analysis error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/system/performance")
async def get_performance_stats():
    """获取系统性能统计"""
    if data_engine is None:
        raise HTTPException(status_code=503, detail="Data engine not initialized")
    
    try:
        perf_stats = data_engine.get_performance_stats()
        db_info = data_engine.db_manager.get_database_info()
        
        return {
            "performance_stats": perf_stats,
            "database_info": db_info,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Performance stats error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/system/cache/clear")
async def clear_cache():
    """清理过期缓存"""
    if data_engine is None:
        raise HTTPException(status_code=503, detail="Data engine not initialized")
    
    try:
        start_time = time.time()
        result = data_engine.optimize_performance()
        operation_time = time.time() - start_time
        
        return {
            "message": "Cache cleared successfully",
            "expired_cache_cleared": result["expired_cache_cleared"],
            "operation_time_ms": round(operation_time * 1000, 2),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Cache clear error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

# 数据更新相关API端点

@app.get("/api/v1/data/update/status")
async def get_update_status():
    """获取数据更新状态"""
    if data_update_service is None:
        raise HTTPException(status_code=503, detail="Data update service not initialized")

    try:
        status = await data_update_service.check_update_status()
        return status
    except Exception as e:
        logger.error(f"Get update status error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/data/update/trigger")
async def trigger_update(force_update: bool = Query(False, description="是否强制更新")):
    """手动触发数据更新"""
    if data_update_service is None:
        raise HTTPException(status_code=503, detail="Data update service not initialized")

    try:
        result = await data_update_service.trigger_update(force_update=force_update)
        return result
    except Exception as e:
        logger.error(f"Trigger update error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/data/update/history")
async def get_update_history(limit: int = Query(10, description="返回记录数限制", ge=1, le=100)):
    """获取更新历史记录"""
    if data_update_service is None:
        raise HTTPException(status_code=503, detail="Data update service not initialized")

    try:
        history = data_update_service.get_update_history(limit=limit)
        return history
    except Exception as e:
        logger.error(f"Get update history error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/data/update/progress/{update_id}")
async def get_update_progress(update_id: str):
    """获取特定更新的进度"""
    if data_update_service is None:
        raise HTTPException(status_code=503, detail="Data update service not initialized")

    try:
        progress = data_update_service.get_update_progress(update_id)
        return progress
    except Exception as e:
        logger.error(f"Get update progress error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

# 预测相关API端点

@app.post("/api/v1/prediction/train")
async def train_predictors(force_retrain: bool = Query(False, description="是否强制重新训练")):
    """训练预测模型"""
    if prediction_service is None:
        raise HTTPException(status_code=503, detail="Prediction service not initialized")

    try:
        result = prediction_service.train_all_predictors(force_retrain=force_retrain)
        return result
    except Exception as e:
        logger.error(f"Train predictors error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/prediction/predict")
async def get_predictions(predictors: str = Query(None, description="指定预测器名称，多个用逗号分隔")):
    """获取预测结果"""
    if prediction_service is None:
        raise HTTPException(status_code=503, detail="Prediction service not initialized")

    try:
        predictor_names = None
        if predictors:
            predictor_names = [name.strip() for name in predictors.split(",")]

        result = prediction_service.get_predictions(predictor_names)
        return result
    except Exception as e:
        logger.error(f"Get predictions error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/prediction/info")
async def get_predictor_info():
    """获取预测器信息"""
    if prediction_service is None:
        raise HTTPException(status_code=503, detail="Prediction service not initialized")

    try:
        info = prediction_service.get_predictor_info()
        return info
    except Exception as e:
        logger.error(f"Get predictor info error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/prediction/history")
async def get_prediction_history(limit: int = Query(10, description="返回记录数限制", ge=1, le=100)):
    """获取预测历史"""
    if prediction_service is None:
        raise HTTPException(status_code=503, detail="Prediction service not initialized")

    try:
        history = prediction_service.get_prediction_history(limit=limit)
        return history
    except Exception as e:
        logger.error(f"Get prediction history error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/prediction/evaluate")
async def evaluate_predictions(
    start_date: str = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: str = Query(None, description="结束日期 (YYYY-MM-DD)")
):
    """评估预测准确率"""
    if prediction_service is None:
        raise HTTPException(status_code=503, detail="Prediction service not initialized")

    try:
        result = prediction_service.evaluate_predictions(start_date, end_date)
        return result
    except Exception as e:
        logger.error(f"Evaluate predictions error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

# 智能融合预测API端点

@app.post("/api/v1/prediction/intelligent-fusion/train")
async def train_intelligent_fusion():
    """训练智能融合模型"""
    try:
        from prediction.intelligent_fusion import IntelligentFusionSystem

        intelligent_system = IntelligentFusionSystem()
        training_result = intelligent_system.train_all_models()

        return {
            "success": training_result.get('success', False),
            "message": "智能融合模型训练完成" if training_result.get('success') else "训练失败",
            "training_details": training_result,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Train intelligent fusion error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/prediction/intelligent-fusion/predict")
async def intelligent_fusion_predict(
    prediction_mode: str = Query("智能融合", description="预测模式"),
    auto_train: bool = Query(True, description="是否自动训练模型")
):
    """智能融合预测"""
    try:
        from core.database import DatabaseManager
        from prediction.intelligent_fusion import IntelligentFusionSystem

        # 初始化系统
        intelligent_system = IntelligentFusionSystem()

        # 检查是否需要训练
        if auto_train and not intelligent_system.fusion_ready:
            logger.info("模型未训练，开始自动训练...")
            training_result = intelligent_system.train_all_models()

            if not training_result.get('success', False):
                return {
                    "success": False,
                    "error": "模型训练失败",
                    "training_details": training_result,
                    "timestamp": datetime.now().isoformat()
                }

        # 获取历史数据
        db_manager = DatabaseManager("data/lottery.db")
        recent_records = db_manager.get_recent_records(50)

        if not recent_records:
            raise HTTPException(status_code=400, detail="数据库中没有足够的历史数据")

        test_data = [record.numbers for record in recent_records]

        # 根据模式进行预测
        if prediction_mode == "智能融合":
            prediction = intelligent_system.generate_fusion_prediction(test_data)
        elif prediction_mode == "趋势分析":
            prediction = intelligent_system.generate_trend_predictions(test_data)
        elif prediction_mode == "形态预测":
            prediction = intelligent_system.generate_pattern_predictions(test_data)
        else:
            prediction = intelligent_system.generate_fusion_prediction(test_data)

        if 'error' in prediction:
            return {
                "success": False,
                "error": prediction['error'],
                "timestamp": datetime.now().isoformat()
            }

        return {
            "success": True,
            "prediction": prediction,
            "prediction_mode": prediction_mode,
            "data_count": len(test_data),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Intelligent fusion predict error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/prediction/intelligent-fusion/status")
async def intelligent_fusion_status():
    """获取智能融合系统状态"""
    try:
        from prediction.intelligent_fusion import IntelligentFusionSystem

        intelligent_system = IntelligentFusionSystem()
        system_summary = intelligent_system.get_system_summary()

        return {
            "success": True,
            "status": system_summary,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Get intelligent fusion status error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

# 自定义Swagger UI页面，使用本地CDN
@app.get("/docs", response_class=HTMLResponse)
async def custom_swagger_ui_html():
    """自定义Swagger UI页面，使用本地CDN资源"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>福彩3D数据分析API - Swagger UI</title>
        <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui.css" />
        <style>
            html {
                box-sizing: border-box;
                overflow: -moz-scrollbars-vertical;
                overflow-y: scroll;
            }
            *, *:before, *:after {
                box-sizing: inherit;
            }
            body {
                margin:0;
                background: #fafafa;
            }
        </style>
    </head>
    <body>
        <div id="swagger-ui"></div>
        <script src="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui-bundle.js"></script>
        <script src="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui-standalone-preset.js"></script>
        <script>
            window.onload = function() {
                const ui = SwaggerUIBundle({
                    url: '/openapi.json',
                    dom_id: '#swagger-ui',
                    deepLinking: true,
                    presets: [
                        SwaggerUIBundle.presets.apis,
                        SwaggerUIStandalonePreset
                    ],
                    plugins: [
                        SwaggerUIBundle.plugins.DownloadUrl
                    ],
                    layout: "StandaloneLayout"
                });
            };
        </script>
    </body>
    </html>
    """

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000, reload=False)
