# 福彩3D预测工具 - Cursor AI助手规则

## 项目概述
这是一个基于Python 3.11.9的福彩3D预测分析工具，使用现代化技术栈：
- 前端：Streamlit 1.28+
- 数据处理：Polars + Pandas 2.1+
- 机器学习：PyTorch 2.1+
- 数据采集：httpx + BeautifulSoup4
- 开发工具：ruff + pytest + mypy

## 代码风格规范

### Python代码规范
- 严格遵循PEP 8标准
- 使用ruff进行代码检查和格式化
- 行长度限制：88字符
- 使用类型提示（Type Hints）
- 函数和类必须有docstring

### 命名规范
- 变量和函数：snake_case
- 类名：PascalCase
- 常量：UPPER_SNAKE_CASE
- 私有成员：以单下划线开头
- 模块名：小写，避免下划线

### 导入规范
```python
# 标准库导入
import os
import sys
from typing import Dict, List, Optional

# 第三方库导入
import pandas as pd
import polars as pl
import streamlit as st

# 本地导入
from src.core import DataProcessor
from src.utils import logger
```

## 项目结构
```
src/
├── __init__.py
├── core/           # 核心业务逻辑
├── data/           # 数据处理模块
├── models/         # 机器学习模型
├── ui/             # Streamlit界面
└── utils/          # 工具函数

tests/
├── unit/           # 单元测试
└── integration/    # 集成测试

docs/               # 文档
scripts/            # 脚本文件
data/               # 数据文件
logs/               # 日志文件
```

## 开发指导原则

### 1. 代码质量
- 每个函数都要有单元测试
- 测试覆盖率要求 > 80%
- 使用mypy进行类型检查
- 代码必须通过ruff检查

### 2. 错误处理
- 使用loguru进行日志记录
- 异常处理要具体，避免裸露的except
- 关键操作要有重试机制
- 用户友好的错误提示

### 3. 性能优化
- 优先使用Polars处理大数据
- 异步操作使用httpx
- 缓存频繁计算的结果
- 避免不必要的数据复制

### 4. 安全考虑
- 输入验证使用Pydantic
- 敏感信息不要硬编码
- API调用要有频率限制
- 数据库操作防止注入

## AI助手行为规范

### 代码生成
1. 总是包含完整的类型提示
2. 生成的代码要有详细的docstring
3. 包含适当的错误处理
4. 遵循项目的命名规范
5. 生成对应的单元测试

### 代码审查
1. 检查是否符合PEP 8
2. 验证类型提示的正确性
3. 确认错误处理的完整性
4. 评估性能影响
5. 检查安全性问题

### 建议优先级
1. 功能正确性
2. 代码可读性
3. 性能优化
4. 安全性
5. 可维护性

## 技术栈特定规则

### Streamlit开发
- 使用st.cache_data缓存数据
- 组件化UI设计
- 响应式布局
- 用户体验优先

### Polars使用
- 优先使用lazy evaluation
- 链式操作提高可读性
- 合理使用并行处理
- 内存使用优化

### PyTorch模型
- 模型代码要模块化
- 训练和推理分离
- 模型版本管理
- 性能监控

### 数据采集
- 遵守robots.txt
- 实现请求频率控制
- 异常重试机制
- 数据质量验证

## 禁止事项
- 不要使用已弃用的API
- 不要忽略类型检查警告
- 不要提交未测试的代码
- 不要硬编码配置信息
- 不要使用全局变量

## 推荐实践
- 使用上下文管理器处理资源
- 优先使用组合而非继承
- 编写自文档化的代码
- 定期重构提高代码质量
- 保持函数简洁（<50行）

## 版本兼容性
- 严格使用Python 3.11.9
- 所有依赖必须兼容Python 3.11
- 避免使用实验性功能
- 定期更新依赖版本
