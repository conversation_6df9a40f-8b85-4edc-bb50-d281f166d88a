#!/usr/bin/env python3
"""
事件总线系统
实现Redis Pub/Sub事件总线、事件路由和分发、事件持久化存储
"""

import asyncio
import json
import logging
import time
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Dict, List, Optional

try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    redis = None
    REDIS_AVAILABLE = False
import uuid

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EventType(Enum):
    """事件类型枚举"""
    JAVASCRIPT_ERROR = "javascript_error"
    API_PERFORMANCE = "api_performance"
    USER_BEHAVIOR = "user_behavior"
    SYSTEM_HEALTH = "system_health"
    SYSTEM_ERROR = "system_error"
    INFRASTRUCTURE_ERROR = "infrastructure_error"
    PREDICTION_ACCURACY = "prediction_accuracy"
    BUG_DETECTED = "bug_detected"
    ALERT_TRIGGERED = "alert_triggered"

class EventPriority(Enum):
    """事件优先级"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class Event:
    """事件数据结构"""
    id: str
    type: EventType
    priority: EventPriority
    timestamp: float
    source: str
    data: Dict[str, Any]
    tags: Optional[List[str]] = None
    correlation_id: Optional[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.correlation_id is None:
            self.correlation_id = str(uuid.uuid4())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = asdict(self)
        result['type'] = self.type.value
        result['priority'] = self.priority.value
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Event':
        """从字典创建事件"""
        data['type'] = EventType(data['type'])
        data['priority'] = EventPriority(data['priority'])
        return cls(**data)

class EventBus:
    """事件总线核心类"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379/0"):
        self.redis_url = redis_url
        self.redis_client = None
        self.subscribers: Dict[str, List[Callable]] = {}
        self.running = False
        self.subscription_tasks: List[asyncio.Task] = []

        # 内存存储（当Redis不可用时使用）
        self.memory_events: List[Event] = []
        self.max_memory_events = 1000

        # 配置
        self.max_event_age = timedelta(hours=24)  # 事件最大保存时间
        self.batch_size = 100  # 批处理大小
        self.retry_attempts = 3  # 重试次数
        
    async def initialize(self):
        """初始化Redis连接"""
        if not REDIS_AVAILABLE:
            logger.warning("⚠️ Redis模块不可用，使用内存存储")
            self.redis_client = None
            return True

        try:
            self.redis_client = redis.from_url(self.redis_url, decode_responses=True)
            await self.redis_client.ping()
            logger.info("✅ Redis连接成功建立")
            return True
        except Exception as e:
            logger.warning(f"⚠️ Redis连接失败: {e}，使用内存存储")
            # 降级方案：使用内存存储
            self.redis_client = None
            return True
    
    async def publish(self, event: Event) -> bool:
        """发布事件"""
        try:
            # 序列化事件
            event_data = json.dumps(event.to_dict(), ensure_ascii=False)
            
            if self.redis_client:
                # 发布到Redis
                channel = f"events:{event.type.value}"
                await self.redis_client.publish(channel, event_data)
                
                # 持久化存储
                await self._store_event(event)
                
                logger.debug(f"📤 事件已发布: {event.id} -> {channel}")
            else:
                # 降级方案：直接调用本地订阅者
                await self._notify_local_subscribers(event)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 事件发布失败: {e}")
            return False
    
    async def subscribe(self, event_type: EventType, callback: Callable[[Event], None]):
        """订阅事件类型"""
        channel = f"events:{event_type.value}"
        
        if channel not in self.subscribers:
            self.subscribers[channel] = []
        
        self.subscribers[channel].append(callback)
        
        # 如果Redis可用，创建Redis订阅
        if self.redis_client and self.running:
            await self._create_redis_subscription(channel)
        
        logger.info(f"📥 已订阅事件类型: {event_type.value}")
    
    async def _create_redis_subscription(self, channel: str):
        """创建Redis订阅"""
        try:
            pubsub = self.redis_client.pubsub()
            await pubsub.subscribe(channel)
            
            # 创建订阅任务
            task = asyncio.create_task(self._handle_redis_messages(pubsub, channel))
            self.subscription_tasks.append(task)
            
        except Exception as e:
            logger.error(f"❌ Redis订阅创建失败: {e}")
    
    async def _handle_redis_messages(self, pubsub, channel: str):
        """处理Redis消息"""
        try:
            async for message in pubsub.listen():
                if message['type'] == 'message':
                    try:
                        # 反序列化事件
                        event_data = json.loads(message['data'])
                        event = Event.from_dict(event_data)
                        
                        # 通知订阅者
                        await self._notify_subscribers(channel, event)
                        
                    except Exception as e:
                        logger.error(f"❌ 消息处理失败: {e}")
                        
        except Exception as e:
            logger.error(f"❌ Redis消息监听失败: {e}")
    
    async def _notify_subscribers(self, channel: str, event: Event):
        """通知订阅者"""
        if channel in self.subscribers:
            for callback in self.subscribers[channel]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(event)
                    else:
                        callback(event)
                except Exception as e:
                    logger.error(f"❌ 订阅者回调失败: {e}")
    
    async def _notify_local_subscribers(self, event: Event):
        """通知本地订阅者（降级方案）"""
        channel = f"events:{event.type.value}"
        await self._notify_subscribers(channel, event)
    
    async def _store_event(self, event: Event):
        """持久化存储事件"""
        try:
            if self.redis_client:
                # 存储到Redis Hash
                key = f"event:{event.id}"
                event_data = event.to_dict()

                await self.redis_client.hset(key, mapping=event_data)

                # 设置过期时间
                await self.redis_client.expire(key, int(self.max_event_age.total_seconds()))

                # 添加到时间索引
                timestamp_key = f"events:by_time:{event.type.value}"
                await self.redis_client.zadd(timestamp_key, {event.id: event.timestamp})

                # 添加到优先级索引
                priority_key = f"events:by_priority:{event.priority.value}"
                await self.redis_client.sadd(priority_key, event.id)
            else:
                # 降级方案：存储到内存
                self.memory_events.append(event)

                # 限制内存中的事件数量
                if len(self.memory_events) > self.max_memory_events:
                    self.memory_events = self.memory_events[-self.max_memory_events:]

                logger.debug(f"📝 事件已存储到内存: {event.id}")

        except Exception as e:
            logger.error(f"❌ 事件存储失败: {e}")
    
    async def get_events(self, 
                        event_type: Optional[EventType] = None,
                        priority: Optional[EventPriority] = None,
                        start_time: Optional[float] = None,
                        end_time: Optional[float] = None,
                        limit: int = 100) -> List[Event]:
        """查询事件"""
        try:
            if not self.redis_client:
                return []
            
            # 构建查询
            if event_type:
                timestamp_key = f"events:by_time:{event_type.value}"
            else:
                timestamp_key = "events:by_time:*"
            
            # 时间范围查询
            if start_time is None:
                start_time = 0
            if end_time is None:
                end_time = time.time()
            
            # 获取事件ID列表
            event_ids = await self.redis_client.zrangebyscore(
                timestamp_key, start_time, end_time, start=0, num=limit
            )
            
            # 获取事件详情
            events = []
            for event_id in event_ids:
                event_key = f"event:{event_id}"
                event_data = await self.redis_client.hgetall(event_key)
                
                if event_data:
                    # 转换数据类型
                    event_data['timestamp'] = float(event_data['timestamp'])
                    event_data['priority'] = int(event_data['priority'])
                    event_data['data'] = json.loads(event_data.get('data', '{}'))
                    event_data['tags'] = json.loads(event_data.get('tags', '[]'))
                    
                    event = Event.from_dict(event_data)
                    
                    # 优先级过滤
                    if priority is None or event.priority == priority:
                        events.append(event)
            
            return events
            
        except Exception as e:
            logger.error(f"❌ 事件查询失败: {e}")
            return []
    
    async def start(self):
        """启动事件总线 - 增强版"""
        try:
            self.running = True

            # 初始化Redis连接
            await self.initialize()

            # 创建现有订阅的Redis订阅
            for channel in self.subscribers.keys():
                if self.redis_client:
                    try:
                        await self._create_redis_subscription(channel)
                    except Exception as e:
                        logger.warning(f"⚠️ 创建Redis订阅失败 {channel}: {e}")

            logger.info("🚀 事件总线已启动")

        except Exception as e:
            logger.error(f"❌ 事件总线启动失败: {e}")
            self.running = False
            raise
    
    async def stop(self):
        """停止事件总线"""
        self.running = False
        
        # 取消所有订阅任务
        for task in self.subscription_tasks:
            task.cancel()
        
        # 等待任务完成
        if self.subscription_tasks:
            await asyncio.gather(*self.subscription_tasks, return_exceptions=True)
        
        # 关闭Redis连接
        if self.redis_client:
            await self.redis_client.close()
        
        logger.info("🛑 事件总线已停止")
    
    async def cleanup_old_events(self):
        """清理过期事件"""
        try:
            if not self.redis_client:
                return
            
            cutoff_time = time.time() - self.max_event_age.total_seconds()
            
            # 清理各个时间索引中的过期事件
            for event_type in EventType:
                timestamp_key = f"events:by_time:{event_type.value}"
                
                # 获取过期事件ID
                expired_ids = await self.redis_client.zrangebyscore(
                    timestamp_key, 0, cutoff_time
                )
                
                if expired_ids:
                    # 删除过期事件
                    for event_id in expired_ids:
                        await self.redis_client.delete(f"event:{event_id}")
                    
                    # 从索引中移除
                    await self.redis_client.zremrangebyscore(timestamp_key, 0, cutoff_time)
                    
                    logger.info(f"🧹 清理了 {len(expired_ids)} 个过期事件")
            
        except Exception as e:
            logger.error(f"❌ 事件清理失败: {e}")

class SimpleEventBus:
    """简化的事件总线（内存模式）"""

    def __init__(self):
        self.subscribers: Dict[str, List[Callable]] = {}
        self.running = False
        self.events: List[Event] = []

    async def start(self):
        """启动简化事件总线"""
        self.running = True
        logger.info("🚀 简化事件总线已启动（内存模式）")

    async def stop(self):
        """停止简化事件总线"""
        self.running = False
        logger.info("🛑 简化事件总线已停止")

    async def publish(self, event: Event) -> bool:
        """发布事件（内存模式）"""
        try:
            self.events.append(event)
            # 保持最近1000个事件
            if len(self.events) > 1000:
                self.events = self.events[-1000:]

            # 通知订阅者
            channel = f"events:{event.type.value}"
            if channel in self.subscribers:
                for callback in self.subscribers[channel]:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(event)
                        else:
                            callback(event)
                    except Exception as e:
                        logger.error(f"❌ 事件处理器错误: {e}")

            return True
        except Exception as e:
            logger.error(f"❌ 事件发布失败: {e}")
            return False

    async def subscribe(self, event_type: EventType, callback: Callable[[Event], None]):
        """订阅事件"""
        channel = f"events:{event_type.value}"
        if channel not in self.subscribers:
            self.subscribers[channel] = []
        self.subscribers[channel].append(callback)

    async def get_events(self, event_type: Optional[EventType] = None,
                        limit: int = 100) -> List[Event]:
        """获取事件"""
        if event_type:
            filtered_events = [e for e in self.events if e.type == event_type]
        else:
            filtered_events = self.events

        return filtered_events[-limit:]

# 全局事件总线实例
event_bus = EventBus()

# 便捷函数
async def publish_event(event_type: EventType, 
                       data: Dict[str, Any], 
                       priority: EventPriority = EventPriority.MEDIUM,
                       source: str = "system",
                       tags: Optional[List[str]] = None) -> bool:
    """发布事件的便捷函数"""
    event = Event(
        id=str(uuid.uuid4()),
        type=event_type,
        priority=priority,
        timestamp=time.time(),
        source=source,
        data=data,
        tags=tags or []
    )
    
    return await event_bus.publish(event)

async def subscribe_to_events(event_type: EventType, callback: Callable[[Event], None]):
    """订阅事件的便捷函数"""
    await event_bus.subscribe(event_type, callback)

# 初始化函数
async def initialize_event_bus():
    """初始化事件总线 - 增强版"""
    try:
        await event_bus.start()
        logger.info("✅ 事件总线初始化成功")
        return event_bus
    except Exception as e:
        logger.error(f"❌ 事件总线初始化失败: {e}")
        logger.warning("⚠️ 切换到简化事件总线模式")

        # 降级方案：使用简化事件总线
        try:
            simple_bus = SimpleEventBus()
            await simple_bus.start()
            logger.info("✅ 简化事件总线初始化成功")
            return simple_bus
        except Exception as e2:
            logger.error(f"❌ 简化事件总线初始化也失败: {e2}")
            return None

if __name__ == "__main__":
    # 测试代码
    async def test_event_bus():
        # 初始化
        await initialize_event_bus()
        
        # 测试事件处理器
        async def handle_js_error(event: Event):
            print(f"🐛 JavaScript错误: {event.data}")
        
        # 订阅事件
        await subscribe_to_events(EventType.JAVASCRIPT_ERROR, handle_js_error)
        
        # 发布测试事件
        await publish_event(
            EventType.JAVASCRIPT_ERROR,
            {"message": "测试JavaScript错误", "line": 42},
            EventPriority.HIGH,
            "test_client"
        )
        
        # 等待处理
        await asyncio.sleep(1)
        
        # 停止
        await event_bus.stop()
    
    # 运行测试
    asyncio.run(test_event_bus())
