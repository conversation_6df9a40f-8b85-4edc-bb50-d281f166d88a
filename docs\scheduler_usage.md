# 定时任务调度器使用说明

## 概述

福彩3D预测工具提供了两种定时任务调度器：

1. **APScheduler调度器** (`task_scheduler.py`) - 功能完整的专业调度器
2. **简单调度器** (`simple_scheduler.py`) - 轻量级的基础调度器

## 功能特性

### 核心功能
- 自动数据更新：定时从数据源获取最新数据
- 文件清理：定期清理旧的数据文件
- 日志管理：自动清理过期日志文件
- 任务监控：记录任务执行状态和错误信息

### 调度任务
- **数据更新任务**：每天21:00自动更新数据
- **文件清理任务**：每周日02:00清理旧文件
- **日志清理任务**：每天03:00清理过期日志

## 使用方法

### 1. APScheduler调度器（推荐）

#### 安装依赖
```bash
pip install apscheduler
```

#### 启动调度器
```bash
# 启动调度器（后台运行）
python scripts/start_scheduler.py --daemon

# 查看状态
python scripts/start_scheduler.py --status

# 立即执行数据更新任务
python scripts/start_scheduler.py --run-job data_update

# 测试模式
python scripts/start_scheduler.py --test
```

#### 配置文件
编辑 `scheduler_config.json` 文件：

```json
{
  "update_schedule": {
    "enabled": true,
    "cron": "0 21 * * *",
    "timezone": "Asia/Shanghai"
  },
  "cleanup_schedule": {
    "enabled": true,
    "cron": "0 2 * * 0",
    "keep_files": 10
  }
}
```

### 2. 简单调度器

#### 使用代码
```python
from scheduler.simple_scheduler import SimpleScheduler

# 创建调度器
scheduler = SimpleScheduler()

# 启动调度器
scheduler.start()

# 手动执行任务
scheduler.run_task_now("data_update")

# 停止调度器
scheduler.stop()
```

#### 任务控制
```python
# 禁用任务
scheduler.disable_task("file_cleanup")

# 启用任务
scheduler.enable_task("file_cleanup")

# 设置任务间隔（秒）
scheduler.set_task_interval("data_update", 12 * 3600)  # 12小时
```

## 配置说明

### Cron表达式格式
```
分 时 日 月 周
0  21 *  *  *    # 每天21:00
0  2  *  *  0    # 每周日02:00
*/30 * * * *     # 每30分钟
```

### 任务间隔设置
- 数据更新：建议每天1次（86400秒）
- 文件清理：建议每周1次（604800秒）
- 日志清理：建议每天1次（86400秒）

## 监控和日志

### 日志文件位置
- APScheduler日志：`data/logs/scheduler_YYYYMMDD.log`
- 简单调度器日志：`data/logs/simple_scheduler_YYYYMMDD.log`

### 状态检查
```python
# 获取调度器状态
status = scheduler.get_status()
print(f"运行状态: {status['running']}")
print(f"任务数量: {len(status['tasks'])}")
```

### 失败处理
- 任务失败会记录到日志文件
- APScheduler支持失败重试机制
- 可以通过日志监控任务执行情况

## 最佳实践

### 1. 生产环境部署
```bash
# 使用systemd服务（Linux）
sudo systemctl enable lottery-scheduler
sudo systemctl start lottery-scheduler

# 使用Windows服务
python scripts/install_service.py
```

### 2. 监控建议
- 定期检查日志文件
- 监控数据更新频率
- 设置磁盘空间告警
- 配置邮件通知（APScheduler）

### 3. 性能优化
- 合理设置任务间隔
- 定期清理旧文件
- 监控内存使用情况
- 避免任务重叠执行

## 故障排除

### 常见问题

#### 1. APScheduler安装失败
```bash
# 解决方案
pip install --upgrade pip
pip install apscheduler
```

#### 2. 任务不执行
- 检查任务是否启用
- 验证Cron表达式格式
- 查看日志文件错误信息

#### 3. 数据更新失败
- 检查网络连接
- 验证数据源可用性
- 查看增量更新器日志

#### 4. 文件权限问题
```bash
# 确保目录权限正确
chmod 755 data/
chmod 755 data/logs/
```

### 调试模式
```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 手动执行任务进行调试
scheduler.run_task_now("data_update")
```

## API参考

### TaskScheduler类
```python
class TaskScheduler:
    def __init__(data_dir, config_file)
    def start()
    def stop()
    def get_job_status()
    def run_job_now(job_id)
```

### SimpleScheduler类
```python
class SimpleScheduler:
    def __init__(data_dir)
    def start()
    def stop()
    def get_status()
    def run_task_now(task_id)
    def enable_task(task_id)
    def disable_task(task_id)
    def set_task_interval(task_id, interval)
```

## 更新历史

- v1.0.0: 初始版本，支持基本定时任务
- v1.1.0: 添加APScheduler支持
- v1.2.0: 增加简单调度器选项
- v1.3.0: 完善监控和日志功能
