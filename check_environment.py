#!/usr/bin/env python3
"""
检查运行环境和依赖
"""

import sys
import os
from pathlib import Path

def check_environment():
    """检查运行环境"""
    print("🔍 环境检查报告")
    print("=" * 50)
    
    # Python信息
    print(f"🐍 Python版本: {sys.version}")
    print(f"📁 Python路径: {sys.executable}")
    print(f"📂 工作目录: {os.getcwd()}")
    print(f"🛤️ Python路径: {sys.path[:3]}...")  # 只显示前3个路径
    
    print("\n📦 依赖检查:")
    
    # 检查APScheduler
    try:
        import apscheduler
        print(f"✅ APScheduler: {apscheduler.__version__}")
    except ImportError as e:
        print(f"❌ APScheduler: 未安装 ({e})")
    
    # 检查Streamlit
    try:
        import streamlit
        print(f"✅ Streamlit: {streamlit.__version__}")
    except ImportError as e:
        print(f"❌ Streamlit: 未安装 ({e})")
    
    # 检查其他依赖
    dependencies = [
        'requests', 'pandas', 'numpy', 'plotly', 'sqlite3'
    ]
    
    for dep in dependencies:
        try:
            module = __import__(dep)
            version = getattr(module, '__version__', '未知版本')
            print(f"✅ {dep}: {version}")
        except ImportError:
            print(f"❌ {dep}: 未安装")
    
    print("\n🗂️ 项目文件检查:")
    
    # 检查关键文件
    key_files = [
        'src/scheduler/task_scheduler.py',
        'scripts/start_scheduler.py',
        'scheduler_config.json',
        'src/ui/main.py'
    ]
    
    for file_path in key_files:
        path = Path(file_path)
        if path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}: 不存在")
    
    print("\n🔧 调度器模块检查:")
    
    # 检查调度器模块
    try:
        sys.path.append('src')
        from scheduler.task_scheduler import TaskScheduler, APSCHEDULER_AVAILABLE
        print(f"✅ 调度器模块导入成功")
        print(f"✅ APScheduler可用状态: {APSCHEDULER_AVAILABLE}")
        
        # 尝试创建调度器实例
        scheduler = TaskScheduler()
        print(f"✅ 调度器实例创建成功")
        
    except Exception as e:
        print(f"❌ 调度器模块错误: {e}")
    
    print("\n🌐 网络检查:")
    
    # 检查API服务
    try:
        import requests
        response = requests.get("http://127.0.0.1:8888/api/v1/health", timeout=5)
        if response.status_code == 200:
            print("✅ API服务正常运行")
        else:
            print(f"⚠️ API服务响应异常: {response.status_code}")
    except Exception as e:
        print(f"❌ API服务不可用: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 环境检查完成")

if __name__ == "__main__":
    check_environment()
