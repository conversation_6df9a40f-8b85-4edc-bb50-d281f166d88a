#!/usr/bin/env python3
"""
简单性能验证测试
创建日期: 2025年7月25日
用途: 验证WebSocket修复后的代码性能和稳定性
"""

import json
import sys
import time
from datetime import datetime

def test_performance_and_stability():
    """测试性能和稳定性"""
    print("🚀 WebSocket性能和稳定性验证")
    print("=" * 50)
    
    results = {
        'module_import_performance': {},
        'initialization_performance': {},
        'memory_efficiency': {},
        'error_handling': {}
    }
    
    # 1. 模块导入性能测试
    print("\n📦 1. 模块导入性能测试")
    
    modules_to_test = [
        ('事件总线', 'src.bug_detection.realtime.event_bus'),
        ('WebSocket管理器', 'src.bug_detection.realtime.websocket_manager'),
        ('降级管理器', 'src.ui.components.fallback_manager'),
        ('JavaScript监控器', 'src.bug_detection.monitoring.js_monitor')
    ]
    
    sys.path.append('src')
    
    for module_name, module_path in modules_to_test:
        try:
            start_time = time.time()
            __import__(module_path)
            end_time = time.time()
            
            import_time = (end_time - start_time) * 1000
            results['module_import_performance'][module_name] = {
                'import_time_ms': import_time,
                'success': True
            }
            
            print(f"  {module_name}: ✅ {import_time:.2f}ms")
            
        except Exception as e:
            results['module_import_performance'][module_name] = {
                'error': str(e),
                'success': False
            }
            print(f"  {module_name}: ❌ 导入失败 - {e}")
    
    # 2. 初始化性能测试
    print("\n⚡ 2. 初始化性能测试")
    
    # 测试降级管理器初始化
    try:
        from ui.components.fallback_manager import FallbackManager
        
        start_time = time.time()
        manager = FallbackManager()
        end_time = time.time()
        
        init_time = (end_time - start_time) * 1000
        results['initialization_performance']['降级管理器'] = {
            'init_time_ms': init_time,
            'success': True
        }
        
        print(f"  降级管理器: ✅ {init_time:.2f}ms")
        
        # 测试缓存操作性能
        start_time = time.time()
        for i in range(100):
            manager._update_cache(f"test_{i}", {"data": f"value_{i}"})
            manager.get_cached_data(f"test_{i}")
        end_time = time.time()
        
        cache_time = (end_time - start_time) * 1000
        results['initialization_performance']['缓存操作'] = {
            'cache_time_ms': cache_time,
            'operations': 200,
            'avg_time_per_op': cache_time / 200
        }
        
        print(f"  缓存操作(200次): ✅ {cache_time:.2f}ms (平均 {cache_time/200:.4f}ms/次)")
        
    except Exception as e:
        results['initialization_performance']['降级管理器'] = {
            'error': str(e),
            'success': False
        }
        print(f"  降级管理器: ❌ 初始化失败 - {e}")
    
    # 测试JavaScript监控器初始化
    try:
        from bug_detection.monitoring.js_monitor import JavaScriptMonitor
        
        start_time = time.time()
        monitor = JavaScriptMonitor("test_session")
        end_time = time.time()
        
        init_time = (end_time - start_time) * 1000
        results['initialization_performance']['JavaScript监控器'] = {
            'init_time_ms': init_time,
            'success': True
        }
        
        print(f"  JavaScript监控器: ✅ {init_time:.2f}ms")
        
    except Exception as e:
        results['initialization_performance']['JavaScript监控器'] = {
            'error': str(e),
            'success': False
        }
        print(f"  JavaScript监控器: ❌ 初始化失败 - {e}")
    
    # 3. 内存效率测试
    print("\n💾 3. 内存效率测试")
    
    try:
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024
        
        print(f"  初始内存使用: {initial_memory:.2f} MB")
        
        # 创建多个对象测试内存使用
        objects = []
        for i in range(50):
            try:
                manager = FallbackManager()
                for j in range(5):
                    manager._update_cache(f"key_{i}_{j}", {"data": f"test_{i}_{j}" * 50})
                objects.append(manager)
            except:
                pass
        
        peak_memory = process.memory_info().rss / 1024 / 1024
        memory_increase = peak_memory - initial_memory
        
        print(f"  峰值内存使用: {peak_memory:.2f} MB")
        print(f"  内存增长: {memory_increase:.2f} MB")
        
        # 清理对象
        del objects
        import gc
        gc.collect()
        
        final_memory = process.memory_info().rss / 1024 / 1024
        memory_recovered = peak_memory - final_memory
        recovery_rate = (memory_recovered / memory_increase * 100) if memory_increase > 0 else 100
        
        results['memory_efficiency'] = {
            'initial_memory_mb': initial_memory,
            'peak_memory_mb': peak_memory,
            'final_memory_mb': final_memory,
            'memory_increase_mb': memory_increase,
            'recovery_rate_percent': recovery_rate
        }
        
        print(f"  清理后内存: {final_memory:.2f} MB")
        print(f"  内存回收率: {recovery_rate:.1f}%")
        
        if memory_increase < 20 and recovery_rate > 70:
            print("  内存效率: ✅ 优秀")
        elif memory_increase < 50 and recovery_rate > 50:
            print("  内存效率: ✅ 良好")
        else:
            print("  内存效率: ⚠️ 需要关注")
            
    except Exception as e:
        results['memory_efficiency'] = {'error': str(e)}
        print(f"  内存效率测试: ❌ 失败 - {e}")
    
    # 4. 错误处理测试
    print("\n🛠️ 4. 错误处理测试")
    
    error_handling_tests = [
        {
            'name': '空参数处理',
            'test': lambda: FallbackManager().get_cached_data(None),
            'expected': 'no_error_or_none'
        },
        {
            'name': '无效缓存键',
            'test': lambda: FallbackManager().get_cached_data("non_existent_key"),
            'expected': 'none_result'
        }
    ]
    
    for test_case in error_handling_tests:
        try:
            result = test_case['test']()
            if test_case['expected'] == 'none_result' and result is None:
                print(f"  {test_case['name']}: ✅ 正确处理")
                results['error_handling'][test_case['name']] = {'success': True}
            elif test_case['expected'] == 'no_error_or_none':
                print(f"  {test_case['name']}: ✅ 无异常")
                results['error_handling'][test_case['name']] = {'success': True}
            else:
                print(f"  {test_case['name']}: ⚠️ 结果异常")
                results['error_handling'][test_case['name']] = {'success': False, 'result': str(result)}
                
        except Exception as e:
            print(f"  {test_case['name']}: ❌ 异常 - {e}")
            results['error_handling'][test_case['name']] = {'success': False, 'error': str(e)}
    
    # 5. 生成总结报告
    print("\n" + "=" * 50)
    print("📋 性能和稳定性测试总结")
    print("=" * 50)
    
    # 计算总体得分
    total_score = 0
    max_score = 0
    
    # 模块导入得分 (25分)
    import_success = sum(1 for result in results['module_import_performance'].values() if result.get('success', False))
    import_total = len(results['module_import_performance'])
    if import_total > 0:
        import_score = (import_success / import_total) * 25
        total_score += import_score
        max_score += 25
        print(f"\n📦 模块导入: {import_success}/{import_total} 成功 ({import_score:.1f}/25分)")
    
    # 初始化性能得分 (25分)
    init_success = sum(1 for result in results['initialization_performance'].values() if result.get('success', False))
    init_total = len(results['initialization_performance'])
    if init_total > 0:
        init_score = (init_success / init_total) * 25
        total_score += init_score
        max_score += 25
        print(f"⚡ 初始化性能: {init_success}/{init_total} 成功 ({init_score:.1f}/25分)")
    
    # 内存效率得分 (25分)
    if 'error' not in results['memory_efficiency']:
        memory_data = results['memory_efficiency']
        memory_increase = memory_data.get('memory_increase_mb', 100)
        recovery_rate = memory_data.get('recovery_rate_percent', 0)
        
        if memory_increase < 20 and recovery_rate > 70:
            memory_score = 25
        elif memory_increase < 50 and recovery_rate > 50:
            memory_score = 20
        else:
            memory_score = 15
        
        total_score += memory_score
        max_score += 25
        print(f"💾 内存效率: {memory_score}/25分")
    
    # 错误处理得分 (25分)
    error_success = sum(1 for result in results['error_handling'].values() if result.get('success', False))
    error_total = len(results['error_handling'])
    if error_total > 0:
        error_score = (error_success / error_total) * 25
        total_score += error_score
        max_score += 25
        print(f"🛠️ 错误处理: {error_success}/{error_total} 成功 ({error_score:.1f}/25分)")
    
    # 总体评估
    final_score = (total_score / max_score * 100) if max_score > 0 else 0
    
    print(f"\n📊 总体评估:")
    print(f"  总得分: {total_score:.1f}/{max_score} ({final_score:.1f}%)")
    
    if final_score >= 90:
        print("  🎉 性能评级: 优秀")
        rating = "优秀"
    elif final_score >= 80:
        print("  ✅ 性能评级: 良好")
        rating = "良好"
    elif final_score >= 70:
        print("  ⚠️ 性能评级: 一般")
        rating = "一般"
    else:
        print("  ❌ 性能评级: 需要优化")
        rating = "需要优化"
    
    print(f"\n🎯 关键性能指标:")
    
    # 显示关键指标
    if results['module_import_performance']:
        avg_import_time = sum(
            result.get('import_time_ms', 0) 
            for result in results['module_import_performance'].values() 
            if result.get('success', False)
        ) / max(1, sum(1 for result in results['module_import_performance'].values() if result.get('success', False)))
        print(f"  - 平均模块导入时间: {avg_import_time:.2f}ms")
    
    if 'memory_efficiency' in results and 'error' not in results['memory_efficiency']:
        memory_data = results['memory_efficiency']
        print(f"  - 内存增长: {memory_data.get('memory_increase_mb', 0):.2f}MB")
        print(f"  - 内存回收率: {memory_data.get('recovery_rate_percent', 0):.1f}%")
    
    # 保存测试结果
    test_report = {
        'timestamp': datetime.now().isoformat(),
        'overall_score': final_score,
        'overall_rating': rating,
        'detailed_results': results,
        'summary': {
            'module_import_success_rate': (import_success / import_total * 100) if import_total > 0 else 0,
            'initialization_success_rate': (init_success / init_total * 100) if init_total > 0 else 0,
            'error_handling_success_rate': (error_success / error_total * 100) if error_total > 0 else 0
        }
    }
    
    with open('performance_stability_results.json', 'w', encoding='utf-8') as f:
        json.dump(test_report, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 详细测试结果已保存到: performance_stability_results.json")
    
    return rating

if __name__ == "__main__":
    test_performance_and_stability()
