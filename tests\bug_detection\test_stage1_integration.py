"""
阶段1集成测试
创建日期: 2025年7月24日
用途: 测试基础监控系统的集成功能
"""

import pytest
import json
import tempfile
import os
from datetime import datetime
from unittest.mock import Mock, patch, MagicMock

# 导入被测试的模块
from src.bug_detection.core.database_manager import DatabaseManager
from src.bug_detection.monitoring.js_monitor import JavaScriptMonitor
from src.bug_detection.monitoring.api_monitor import APIPerformanceMonitor
from src.bug_detection.feedback.bug_reporter import BugReporter, IntelligentBugReporter

class TestDatabaseManager:
    """数据库管理器测试"""
    
    def setup_method(self):
        """测试前设置"""
        # 创建临时数据库文件
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db_manager = DatabaseManager(self.temp_db.name)
    
    def teardown_method(self):
        """测试后清理"""
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)
    
    def test_database_initialization(self):
        """测试数据库初始化"""
        # 验证数据库文件存在
        assert os.path.exists(self.temp_db.name)
        
        # 验证表是否创建成功
        import sqlite3
        conn = sqlite3.connect(self.temp_db.name)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        expected_tables = ['bug_reports', 'user_behaviors', 'performance_metrics', 'js_errors']
        for table in expected_tables:
            assert table in tables, f"Table {table} not found"
        
        conn.close()
    
    def test_save_bug_report(self):
        """测试保存Bug报告"""
        bug_data = {
            'error_type': 'javascript',
            'severity': 'high',
            'page_name': 'test_page',
            'error_message': 'Test error message',
            'stack_trace': 'Test stack trace'
        }
        
        bug_id = self.db_manager.save_bug_report(bug_data)
        
        # 验证返回的bug_id不为空
        assert bug_id != ""
        assert bug_id.startswith("BUG_")
        
        # 验证数据是否正确保存
        bug_reports = self.db_manager.get_bug_reports(limit=1)
        assert len(bug_reports) == 1
        assert bug_reports[0]['error_type'] == 'javascript'
        assert bug_reports[0]['severity'] == 'high'
    
    def test_save_performance_metric(self):
        """测试保存性能指标"""
        self.db_manager.save_performance_metric('/api/test', 0.5, 200)
        
        # 验证数据保存
        summary = self.db_manager.get_performance_summary()
        assert '/api/test' in summary
        assert summary['/api/test']['avg_time'] == 0.5
        assert summary['/api/test']['count'] == 1
    
    def test_save_js_error(self):
        """测试保存JavaScript错误"""
        session_id = "test_session_123"
        error_message = "Test JS error"
        page_url = "http://test.com/page"
        
        self.db_manager.save_js_error(session_id, error_message, page_url)
        
        # 验证数据保存（需要直接查询数据库）
        import sqlite3
        conn = sqlite3.connect(self.temp_db.name)
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM js_errors WHERE session_id = ?", (session_id,))
        result = cursor.fetchone()
        
        assert result is not None
        assert result[1] == session_id  # session_id字段
        assert result[2] == error_message  # error_message字段
        
        conn.close()

class TestJavaScriptMonitor:
    """JavaScript监控器测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.mock_db = Mock(spec=DatabaseManager)
        self.js_monitor = JavaScriptMonitor(self.mock_db)
    
    @patch('streamlit.session_state', {})
    def test_session_id_generation(self):
        """测试会话ID生成"""
        session_id = self.js_monitor._get_session_id()
        
        # 验证会话ID格式
        assert isinstance(session_id, str)
        assert len(session_id) > 0
        
        # 验证会话ID一致性
        session_id2 = self.js_monitor._get_session_id()
        assert session_id == session_id2
    
    @patch('streamlit.components.v1.html')
    def test_inject_error_monitor(self, mock_html):
        """测试错误监控脚本注入"""
        page_name = "test_page"
        
        self.js_monitor.inject_error_monitor(page_name)
        
        # 验证HTML组件被调用
        mock_html.assert_called_once()
        
        # 验证脚本内容包含必要元素
        call_args = mock_html.call_args[0][0]
        assert 'window.bugDetector' in call_args
        assert 'window.onerror' in call_args
        assert 'unhandledrejection' in call_args
        assert page_name in call_args

class TestAPIPerformanceMonitor:
    """API性能监控器测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.mock_db = Mock(spec=DatabaseManager)
        self.api_monitor = APIPerformanceMonitor(
            app=None,
            database_manager=self.mock_db
        )
    
    def test_should_exclude_path(self):
        """测试路径排除逻辑"""
        # 应该排除的路径
        assert self.api_monitor._should_exclude_path('/docs')
        assert self.api_monitor._should_exclude_path('/redoc')
        assert self.api_monitor._should_exclude_path('/openapi.json')
        
        # 不应该排除的路径
        assert not self.api_monitor._should_exclude_path('/api/test')
        assert not self.api_monitor._should_exclude_path('/health')
    
    def test_get_client_ip(self):
        """测试客户端IP获取"""
        # 模拟请求对象
        mock_request = Mock()
        mock_request.headers = {
            'x-forwarded-for': '***********, ********',
            'x-real-ip': '***********'
        }
        mock_request.client.host = '127.0.0.1'
        
        # 测试x-forwarded-for优先级
        ip = self.api_monitor._get_client_ip(mock_request)
        assert ip == '***********'
        
        # 测试x-real-ip备选
        mock_request.headers = {'x-real-ip': '***********'}
        ip = self.api_monitor._get_client_ip(mock_request)
        assert ip == '***********'
        
        # 测试直接连接IP
        mock_request.headers = {}
        ip = self.api_monitor._get_client_ip(mock_request)
        assert ip == '127.0.0.1'
    
    def test_performance_summary(self):
        """测试性能摘要生成"""
        # 添加一些测试数据
        test_data = [
            {
                'path': '/api/test1',
                'response_time': 0.1,
                'status_code': 200,
                'has_error': False
            },
            {
                'path': '/api/test1',
                'response_time': 0.2,
                'status_code': 200,
                'has_error': False
            },
            {
                'path': '/api/test2',
                'response_time': 0.5,
                'status_code': 500,
                'has_error': True
            }
        ]
        
        self.api_monitor.performance_data = test_data
        self.api_monitor.request_count = 3
        self.api_monitor.error_count = 1
        
        summary = self.api_monitor.get_performance_summary()
        
        # 验证总体统计
        assert summary['total_requests'] == 3
        assert summary['error_count'] == 1
        assert summary['error_rate'] == 1/3
        
        # 验证端点统计
        assert '/api/test1' in summary['endpoints']
        assert '/api/test2' in summary['endpoints']
        
        test1_stats = summary['endpoints']['/api/test1']
        assert test1_stats['count'] == 2
        assert test1_stats['avg_response_time'] == 0.15
        assert test1_stats['errors'] == 0

class TestBugReporter:
    """Bug报告生成器测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.mock_db = Mock(spec=DatabaseManager)
        self.bug_reporter = BugReporter(self.mock_db)
    
    def test_assess_severity(self):
        """测试严重程度评估"""
        # 测试严重错误
        error_data = {'message': 'critical system failure'}
        assert self.bug_reporter._assess_severity(error_data) == 'critical'
        
        # 测试高级错误
        error_data = {'message': 'database error occurred'}
        assert self.bug_reporter._assess_severity(error_data) == 'high'
        
        # 测试中级错误
        error_data = {'message': 'deprecated function warning'}
        assert self.bug_reporter._assess_severity(error_data) == 'medium'
        
        # 测试低级错误
        error_data = {'message': 'minor issue'}
        assert self.bug_reporter._assess_severity(error_data) == 'low'
    
    def test_categorize_error(self):
        """测试错误分类"""
        # 前端错误
        error_data = {'type': 'javascript'}
        assert self.bug_reporter._categorize_error(error_data) == 'Frontend'
        
        # 后端错误
        error_data = {'type': 'api_error'}
        assert self.bug_reporter._categorize_error(error_data) == 'Backend'
        
        # 数据库错误
        error_data = {'message': 'database connection failed'}
        assert self.bug_reporter._categorize_error(error_data) == 'Database'
    
    def test_generate_bug_report(self):
        """测试Bug报告生成"""
        error_data = {
            'type': 'javascript',
            'message': 'Test error message',
            'source': 'test.js',
            'line_number': 10,
            'page_url': 'http://test.com'
        }
        
        context = {'user_id': 'test_user'}
        user_actions = ['clicked button', 'filled form']
        
        report = self.bug_reporter.generate_bug_report(
            error_data, context, user_actions
        )
        
        # 验证报告结构
        assert 'id' in report
        assert 'timestamp' in report
        assert 'error' in report
        assert 'environment' in report
        assert 'context' in report
        assert 'user_actions' in report
        
        # 验证错误信息
        assert report['error']['type'] == 'javascript'
        assert report['error']['message'] == 'Test error message'
        
        # 验证上下文和用户操作
        assert report['context'] == context
        assert report['user_actions'] == user_actions
        
        # 验证数据库保存被调用
        self.mock_db.save_bug_report.assert_called_once()

class TestIntelligentBugReporter:
    """智能Bug报告生成器测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.mock_db = Mock(spec=DatabaseManager)
        self.intelligent_reporter = IntelligentBugReporter(self.mock_db)
    
    def test_calculate_similarity(self):
        """测试相似度计算"""
        error1 = {
            'type': 'javascript',
            'message': 'undefined variable error',
            'page_url': 'http://test.com/page1'
        }
        
        error2 = {
            'error_type': 'javascript',
            'error_message': 'undefined variable error',
            'page_name': 'http://test.com/page1'
        }
        
        similarity = self.intelligent_reporter._calculate_similarity(error1, error2)
        
        # 验证相似度计算
        assert 0 <= similarity <= 1
        assert similarity > 0.5  # 应该有较高相似度
    
    def test_assess_user_impact(self):
        """测试用户影响评估"""
        # 严重影响
        error_data = {'message': 'critical system failure'}
        impact = self.intelligent_reporter._assess_user_impact(error_data)
        assert 'High' in impact
        
        # 中等影响
        error_data = {'message': 'database error'}
        impact = self.intelligent_reporter._assess_user_impact(error_data)
        assert 'Medium' in impact
    
    def test_identify_probable_causes(self):
        """测试可能原因识别"""
        # 超时错误
        error_data = {'message': 'request timeout occurred'}
        causes = self.intelligent_reporter._identify_probable_causes(error_data)
        assert any('timeout' in cause.lower() or 'latency' in cause.lower() for cause in causes)
        
        # 内存错误
        error_data = {'message': 'out of memory error'}
        causes = self.intelligent_reporter._identify_probable_causes(error_data)
        assert any('memory' in cause.lower() for cause in causes)

class TestIntegration:
    """集成测试"""
    
    def setup_method(self):
        """测试前设置"""
        # 创建临时数据库
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        # 初始化组件
        self.db_manager = DatabaseManager(self.temp_db.name)
        self.js_monitor = JavaScriptMonitor(self.db_manager)
        self.bug_reporter = IntelligentBugReporter(self.db_manager)
    
    def teardown_method(self):
        """测试后清理"""
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)
    
    def test_end_to_end_bug_reporting(self):
        """测试端到端Bug报告流程"""
        # 1. 模拟JavaScript错误
        error_data = {
            'type': 'javascript',
            'message': 'TypeError: Cannot read property of undefined',
            'source': 'main.js',
            'line_number': 42,
            'page_url': 'http://localhost:8501/test_page',
            'session_id': 'test_session_123'
        }
        
        # 2. 保存JavaScript错误
        self.db_manager.save_js_error(
            error_data['session_id'],
            error_data['message'],
            error_data['page_url']
        )
        
        # 3. 生成Bug报告
        bug_report = self.bug_reporter.generate_enhanced_report(error_data)
        
        # 4. 验证完整流程
        assert bug_report['id'].startswith('BUG_')
        assert bug_report['error']['type'] == 'javascript'
        assert bug_report['error']['severity'] in ['low', 'medium', 'high', 'critical']
        
        # 5. 验证数据库中的数据
        bug_reports = self.db_manager.get_bug_reports(limit=1)
        assert len(bug_reports) == 1
        assert bug_reports[0]['error_type'] == 'javascript'
    
    def test_performance_monitoring_integration(self):
        """测试性能监控集成"""
        # 1. 保存性能指标
        endpoints = ['/api/test1', '/api/test2', '/api/test1']
        response_times = [0.1, 0.5, 0.2]
        status_codes = [200, 500, 200]
        
        for endpoint, time, status in zip(endpoints, response_times, status_codes):
            self.db_manager.save_performance_metric(endpoint, time, status)
        
        # 2. 获取性能摘要
        summary = self.db_manager.get_performance_summary()
        
        # 3. 验证统计结果
        assert '/api/test1' in summary
        assert '/api/test2' in summary
        assert summary['/api/test1']['count'] == 2
        assert summary['/api/test2']['count'] == 1
        
        # 4. 验证平均响应时间计算
        assert abs(summary['/api/test1']['avg_time'] - 0.15) < 0.01

# 运行测试的便捷函数
def run_stage1_tests():
    """运行阶段1的所有测试"""
    import subprocess
    import sys
    
    try:
        # 运行pytest
        result = subprocess.run([
            sys.executable, '-m', 'pytest', 
            'tests/bug_detection/test_stage1_integration.py',
            '-v', '--tb=short'
        ], capture_output=True, text=True, cwd='.')
        
        print("=== 阶段1集成测试结果 ===")
        print(result.stdout)
        if result.stderr:
            print("错误信息:")
            print(result.stderr)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"运行测试时出错: {e}")
        return False

if __name__ == "__main__":
    # 直接运行测试
    success = run_stage1_tests()
    if success:
        print("✅ 阶段1集成测试通过")
    else:
        print("❌ 阶段1集成测试失败")
