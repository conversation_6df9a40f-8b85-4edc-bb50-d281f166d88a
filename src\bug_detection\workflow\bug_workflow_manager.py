#!/usr/bin/env python3
"""
Bug工作流管理器
实现完整的Bug管理流程，包括创建、分配、跟踪、解决等环节
"""

import json
import logging
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)

class BugStatus(Enum):
    """Bug状态枚举"""
    OPEN = "open"
    IN_PROGRESS = "in_progress"
    RESOLVED = "resolved"
    CLOSED = "closed"
    REOPENED = "reopened"

class BugPriority(Enum):
    """Bug优先级枚举"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class BugSeverity(Enum):
    """Bug严重程度枚举"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class WorkflowAction(Enum):
    """工作流动作枚举"""
    CREATE = "create"
    ASSIGN = "assign"
    START_WORK = "start_work"
    RESOLVE = "resolve"
    CLOSE = "close"
    REOPEN = "reopen"
    COMMENT = "comment"
    UPDATE_PRIORITY = "update_priority"
    UPDATE_SEVERITY = "update_severity"

class BugWorkflowManager:
    """Bug工作流管理器"""
    
    def __init__(self, database_manager=None):
        self.db_manager = database_manager
        self.workflow_rules = self._load_workflow_rules()
        self.notification_handlers = []
        
    def _load_workflow_rules(self) -> Dict[str, Any]:
        """加载工作流规则"""
        return {
            'status_transitions': {
                BugStatus.OPEN.value: [BugStatus.IN_PROGRESS.value, BugStatus.CLOSED.value],
                BugStatus.IN_PROGRESS.value: [BugStatus.RESOLVED.value, BugStatus.OPEN.value],
                BugStatus.RESOLVED.value: [BugStatus.CLOSED.value, BugStatus.REOPENED.value],
                BugStatus.CLOSED.value: [BugStatus.REOPENED.value],
                BugStatus.REOPENED.value: [BugStatus.IN_PROGRESS.value, BugStatus.CLOSED.value]
            },
            'auto_assignment_rules': {
                'ui': ['frontend_team'],
                'api': ['backend_team'],
                'database': ['database_team'],
                'performance': ['performance_team'],
                'security': ['security_team'],
                'integration': ['integration_team']
            },
            'escalation_rules': {
                'critical': {'hours': 1, 'notify': ['manager', 'team_lead']},
                'high': {'hours': 4, 'notify': ['team_lead']},
                'medium': {'hours': 24, 'notify': []},
                'low': {'hours': 72, 'notify': []}
            },
            'sla_rules': {
                'critical': {'response_hours': 1, 'resolution_hours': 4},
                'high': {'response_hours': 4, 'resolution_hours': 24},
                'medium': {'response_hours': 24, 'resolution_hours': 72},
                'low': {'response_hours': 72, 'resolution_hours': 168}
            }
        }
    
    def create_bug(self, bug_data: Dict[str, Any], creator: str = "system") -> Dict[str, Any]:
        """创建Bug并启动工作流"""
        try:
            # 生成Bug ID
            bug_id = f"BUG_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 准备Bug数据
            workflow_data = {
                'id': bug_id,
                'status': BugStatus.OPEN.value,
                'created_by': creator,
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat(),
                'workflow_history': []
            }
            
            # 合并用户提供的数据
            workflow_data.update(bug_data)
            
            # 自动分配
            if 'assigned_to' not in workflow_data or not workflow_data['assigned_to']:
                workflow_data['assigned_to'] = self._auto_assign_bug(workflow_data)
            
            # 记录工作流历史
            self._add_workflow_history(
                workflow_data,
                WorkflowAction.CREATE,
                creator,
                f"Bug created with status: {workflow_data['status']}"
            )
            
            # 保存到数据库
            if self.db_manager:
                self.db_manager.save_bug_report(workflow_data)
            
            # 发送通知
            self._send_notification('bug_created', workflow_data)
            
            logger.info(f"Bug created: {bug_id}")
            return workflow_data
            
        except Exception as e:
            logger.error(f"Error creating bug: {e}")
            raise
    
    def update_bug_status(self, bug_id: str, new_status: str, user: str, comment: str = "") -> bool:
        """更新Bug状态"""
        try:
            if not self.db_manager:
                return False
            
            # 获取当前Bug信息
            bug = self.db_manager.get_bug_by_id(bug_id)
            if not bug:
                logger.error(f"Bug not found: {bug_id}")
                return False
            
            current_status = bug.get('status', BugStatus.OPEN.value)
            
            # 验证状态转换是否合法
            if not self._is_valid_status_transition(current_status, new_status):
                logger.error(f"Invalid status transition: {current_status} -> {new_status}")
                return False
            
            # 更新Bug状态
            success = self.db_manager.update_bug_status(bug_id, new_status, user)
            if not success:
                return False
            
            # 记录工作流历史
            workflow_history = json.loads(bug.get('workflow_history', '[]'))
            self._add_workflow_history_entry(
                workflow_history,
                WorkflowAction.START_WORK if new_status == BugStatus.IN_PROGRESS.value else WorkflowAction.RESOLVE if new_status == BugStatus.RESOLVED.value else WorkflowAction.CLOSE,
                user,
                f"Status changed from {current_status} to {new_status}. {comment}".strip()
            )
            
            # 更新工作流历史到数据库
            self._update_workflow_history(bug_id, workflow_history)
            
            # 发送通知
            self._send_notification('status_changed', {
                'bug_id': bug_id,
                'old_status': current_status,
                'new_status': new_status,
                'user': user,
                'comment': comment
            })
            
            logger.info(f"Bug status updated: {bug_id} -> {new_status}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating bug status: {e}")
            return False
    
    def assign_bug(self, bug_id: str, assignee: str, assigner: str, comment: str = "") -> bool:
        """分配Bug"""
        try:
            if not self.db_manager:
                return False
            
            # 获取当前Bug信息
            bug = self.db_manager.get_bug_by_id(bug_id)
            if not bug:
                logger.error(f"Bug not found: {bug_id}")
                return False
            
            # 更新分配信息（这里需要扩展数据库管理器的功能）
            # 暂时记录在工作流历史中
            workflow_history = json.loads(bug.get('workflow_history', '[]'))
            self._add_workflow_history_entry(
                workflow_history,
                WorkflowAction.ASSIGN,
                assigner,
                f"Bug assigned to {assignee}. {comment}".strip()
            )
            
            # 更新工作流历史到数据库
            self._update_workflow_history(bug_id, workflow_history)
            
            # 发送通知
            self._send_notification('bug_assigned', {
                'bug_id': bug_id,
                'assignee': assignee,
                'assigner': assigner,
                'comment': comment
            })
            
            logger.info(f"Bug assigned: {bug_id} -> {assignee}")
            return True
            
        except Exception as e:
            logger.error(f"Error assigning bug: {e}")
            return False
    
    def add_comment(self, bug_id: str, comment: str, user: str) -> bool:
        """添加Bug评论"""
        try:
            if not self.db_manager:
                return False
            
            # 获取当前Bug信息
            bug = self.db_manager.get_bug_by_id(bug_id)
            if not bug:
                logger.error(f"Bug not found: {bug_id}")
                return False
            
            # 记录评论到工作流历史
            workflow_history = json.loads(bug.get('workflow_history', '[]'))
            self._add_workflow_history_entry(
                workflow_history,
                WorkflowAction.COMMENT,
                user,
                comment
            )
            
            # 更新工作流历史到数据库
            self._update_workflow_history(bug_id, workflow_history)
            
            # 发送通知
            self._send_notification('comment_added', {
                'bug_id': bug_id,
                'user': user,
                'comment': comment
            })
            
            logger.info(f"Comment added to bug: {bug_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding comment: {e}")
            return False
    
    def get_bug_workflow_history(self, bug_id: str) -> List[Dict[str, Any]]:
        """获取Bug工作流历史"""
        try:
            if not self.db_manager:
                return []
            
            bug = self.db_manager.get_bug_by_id(bug_id)
            if not bug:
                return []
            
            return json.loads(bug.get('workflow_history', '[]'))
            
        except Exception as e:
            logger.error(f"Error getting workflow history: {e}")
            return []
    
    def check_sla_violations(self) -> List[Dict[str, Any]]:
        """检查SLA违规"""
        try:
            if not self.db_manager:
                return []
            
            violations = []
            bugs = self.db_manager.get_bug_reports(limit=1000)
            
            for bug in bugs:
                if bug.get('status') in [BugStatus.CLOSED.value, BugStatus.RESOLVED.value]:
                    continue
                
                severity = bug.get('severity', 'medium')
                created_at = datetime.fromisoformat(bug.get('created_at', ''))
                now = datetime.now()
                
                sla_rules = self.workflow_rules['sla_rules'].get(severity, {})
                response_hours = sla_rules.get('response_hours', 24)
                resolution_hours = sla_rules.get('resolution_hours', 72)
                
                hours_since_creation = (now - created_at).total_seconds() / 3600
                
                # 检查响应时间违规
                if hours_since_creation > response_hours and bug.get('status') == BugStatus.OPEN.value:
                    violations.append({
                        'bug_id': bug['id'],
                        'type': 'response_time',
                        'severity': severity,
                        'hours_overdue': hours_since_creation - response_hours,
                        'sla_hours': response_hours
                    })
                
                # 检查解决时间违规
                if hours_since_creation > resolution_hours:
                    violations.append({
                        'bug_id': bug['id'],
                        'type': 'resolution_time',
                        'severity': severity,
                        'hours_overdue': hours_since_creation - resolution_hours,
                        'sla_hours': resolution_hours
                    })
            
            return violations
            
        except Exception as e:
            logger.error(f"Error checking SLA violations: {e}")
            return []
    
    def _auto_assign_bug(self, bug_data: Dict[str, Any]) -> str:
        """自动分配Bug"""
        category = bug_data.get('category', 'general')
        assignment_rules = self.workflow_rules['auto_assignment_rules']
        
        if category in assignment_rules:
            teams = assignment_rules[category]
            return teams[0] if teams else "unassigned"
        
        return "unassigned"
    
    def _is_valid_status_transition(self, current_status: str, new_status: str) -> bool:
        """验证状态转换是否合法"""
        transitions = self.workflow_rules['status_transitions']
        allowed_transitions = transitions.get(current_status, [])
        return new_status in allowed_transitions
    
    def _add_workflow_history(self, bug_data: Dict[str, Any], action: WorkflowAction, user: str, description: str):
        """添加工作流历史记录"""
        if 'workflow_history' not in bug_data:
            bug_data['workflow_history'] = []
        
        self._add_workflow_history_entry(bug_data['workflow_history'], action, user, description)
    
    def _add_workflow_history_entry(self, history: List[Dict], action: WorkflowAction, user: str, description: str):
        """添加工作流历史条目"""
        history.append({
            'action': action.value,
            'user': user,
            'timestamp': datetime.now().isoformat(),
            'description': description
        })
    
    def _update_workflow_history(self, bug_id: str, history: List[Dict]):
        """更新工作流历史到数据库"""
        try:
            if not self.db_manager:
                return

            # 将历史记录转换为JSON字符串存储
            import json
            history_json = json.dumps(history, ensure_ascii=False)

            # 更新数据库中的工作流历史字段
            import sqlite3
            conn = sqlite3.connect(self.db_manager.db_path)
            cursor = conn.cursor()

            # 检查是否有workflow_history字段，如果没有则添加
            cursor.execute("PRAGMA table_info(bug_reports)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'workflow_history' not in columns:
                cursor.execute("ALTER TABLE bug_reports ADD COLUMN workflow_history TEXT DEFAULT '[]'")

            # 更新工作流历史
            cursor.execute("""
                UPDATE bug_reports
                SET workflow_history = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (history_json, bug_id))

            conn.commit()
            conn.close()

            logger.info(f"Workflow history updated for bug: {bug_id}")

        except Exception as e:
            logger.error(f"Error updating workflow history: {e}")
            import traceback
            traceback.print_exc()
    
    def _send_notification(self, event_type: str, data: Dict[str, Any]):
        """发送通知"""
        for handler in self.notification_handlers:
            try:
                handler(event_type, data)
            except Exception as e:
                logger.error(f"Error sending notification: {e}")
    
    def add_notification_handler(self, handler):
        """添加通知处理器"""
        self.notification_handlers.append(handler)
