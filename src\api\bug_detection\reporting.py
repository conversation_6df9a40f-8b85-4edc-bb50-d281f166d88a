"""
Bug检测系统报告API端点
创建日期: 2025年7月24日
用途: 提供Bug报告和分析相关的API接口
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import logging
import json
import io
import csv

from src.bug_detection.core.database_manager import DatabaseManager
from src.bug_detection.feedback.bug_reporter import IntelligentBugReporter

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/v1/bug-detection/reports", tags=["bug-reports"])

# 数据模型
class ReportRequest(BaseModel):
    """报告请求模型"""
    report_type: str = Field(..., description="报告类型")
    date_range: Optional[str] = Field("7d", description="日期范围")
    filters: Optional[Dict[str, Any]] = Field(None, description="过滤条件")
    format: Optional[str] = Field("json", description="输出格式")

class BugAnalysisResponse(BaseModel):
    """Bug分析响应模型"""
    total_bugs: int = Field(..., description="总Bug数量")
    open_bugs: int = Field(..., description="未解决Bug数量")
    resolved_bugs: int = Field(..., description="已解决Bug数量")
    critical_bugs: int = Field(..., description="严重Bug数量")
    trends: Dict[str, Any] = Field(..., description="趋势分析")
    top_issues: List[Dict[str, Any]] = Field(..., description="主要问题")

# 依赖注入
def get_database_manager():
    """获取数据库管理器"""
    return DatabaseManager()

def get_bug_reporter():
    """获取Bug报告生成器"""
    return IntelligentBugReporter(get_database_manager())

@router.get("/analysis", response_model=BugAnalysisResponse)
async def get_bug_analysis(
    date_range: str = Query("7d", description="日期范围 (1d, 7d, 30d)"),
    db: DatabaseManager = Depends(get_database_manager)
):
    """
    获取Bug分析报告
    """
    try:
        # 获取Bug报告数据
        bug_reports = db.get_bug_reports(limit=1000)
        
        # 日期过滤
        cutoff_date = datetime.now() - timedelta(
            days=1 if date_range == "1d" else 7 if date_range == "7d" else 30
        )
        
        filtered_bugs = [
            bug for bug in bug_reports 
            if datetime.fromisoformat(bug.get('created_at', '1970-01-01')) >= cutoff_date
        ]
        
        # 统计分析
        total_bugs = len(filtered_bugs)
        open_bugs = len([bug for bug in filtered_bugs if bug.get('status') == 'open'])
        resolved_bugs = len([bug for bug in filtered_bugs if bug.get('status') == 'resolved'])
        critical_bugs = len([bug for bug in filtered_bugs if bug.get('severity') == 'critical'])
        
        # 趋势分析
        trends = _analyze_trends(filtered_bugs)
        
        # 主要问题
        top_issues = _identify_top_issues(filtered_bugs)
        
        return BugAnalysisResponse(
            total_bugs=total_bugs,
            open_bugs=open_bugs,
            resolved_bugs=resolved_bugs,
            critical_bugs=critical_bugs,
            trends=trends,
            top_issues=top_issues
        )
        
    except Exception as e:
        logger.error(f"Error getting bug analysis: {e}")
        raise HTTPException(status_code=500, detail="Failed to get bug analysis")

@router.get("/summary")
async def get_report_summary(
    report_type: str = Query("daily", description="报告类型"),
    db: DatabaseManager = Depends(get_database_manager)
):
    """
    获取报告摘要
    """
    try:
        if report_type == "daily":
            return await _generate_daily_summary(db)
        elif report_type == "weekly":
            return await _generate_weekly_summary(db)
        elif report_type == "monthly":
            return await _generate_monthly_summary(db)
        else:
            raise HTTPException(status_code=400, detail="Invalid report type")
            
    except Exception as e:
        logger.error(f"Error generating report summary: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate report summary")

@router.post("/generate")
async def generate_custom_report(
    request: ReportRequest,
    db: DatabaseManager = Depends(get_database_manager)
):
    """
    生成自定义报告
    """
    try:
        # 获取数据
        bug_reports = db.get_bug_reports(limit=5000)
        
        # 应用过滤器
        if request.filters:
            bug_reports = _apply_filters(bug_reports, request.filters)
        
        # 生成报告
        report_data = _generate_report_data(bug_reports, request.report_type)
        
        # 格式化输出
        if request.format == "csv":
            return await _generate_csv_report(report_data)
        elif request.format == "json":
            return JSONResponse(
                status_code=200,
                content={
                    "status": "success",
                    "report_type": request.report_type,
                    "generated_at": datetime.now().isoformat(),
                    "data": report_data
                }
            )
        else:
            raise HTTPException(status_code=400, detail="Unsupported format")
            
    except Exception as e:
        logger.error(f"Error generating custom report: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate custom report")

@router.get("/export/{report_id}")
async def export_report(report_id: str, format: str = "json"):
    """
    导出报告
    """
    try:
        # 这里应该从存储中获取报告数据
        # 为了演示，我们生成一个示例报告
        
        if format == "csv":
            # 生成CSV文件
            output = io.StringIO()
            writer = csv.writer(output)
            
            # 写入标题行
            writer.writerow(['Bug ID', 'Type', 'Severity', 'Status', 'Created At'])
            
            # 写入示例数据
            writer.writerow(['BUG_001', 'JavaScript', 'High', 'Open', '2025-07-24'])
            
            # 创建响应
            response = FileResponse(
                path=None,
                filename=f"bug_report_{report_id}.csv",
                media_type="text/csv"
            )
            response.body = output.getvalue().encode()
            return response
            
        else:
            return JSONResponse(
                status_code=200,
                content={
                    "report_id": report_id,
                    "format": format,
                    "exported_at": datetime.now().isoformat()
                }
            )
            
    except Exception as e:
        logger.error(f"Error exporting report: {e}")
        raise HTTPException(status_code=500, detail="Failed to export report")

# 辅助函数
def _analyze_trends(bug_reports: List[Dict[str, Any]]) -> Dict[str, Any]:
    """分析Bug趋势"""
    trends = {
        "daily_counts": {},
        "severity_trends": {},
        "resolution_rate": 0
    }
    
    # 按日期统计
    for bug in bug_reports:
        date = bug.get('created_at', '')[:10]  # 获取日期部分
        trends["daily_counts"][date] = trends["daily_counts"].get(date, 0) + 1
    
    # 严重程度趋势
    for bug in bug_reports:
        severity = bug.get('severity', 'unknown')
        trends["severity_trends"][severity] = trends["severity_trends"].get(severity, 0) + 1
    
    # 解决率
    total_bugs = len(bug_reports)
    resolved_bugs = len([bug for bug in bug_reports if bug.get('status') == 'resolved'])
    trends["resolution_rate"] = resolved_bugs / total_bugs if total_bugs > 0 else 0
    
    return trends

def _identify_top_issues(bug_reports: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """识别主要问题"""
    # 按错误类型分组
    error_types = {}
    for bug in bug_reports:
        error_type = bug.get('error_type', 'unknown')
        if error_type not in error_types:
            error_types[error_type] = {
                'count': 0,
                'severity_counts': {},
                'latest_occurrence': None
            }
        
        error_types[error_type]['count'] += 1
        
        severity = bug.get('severity', 'unknown')
        error_types[error_type]['severity_counts'][severity] = \
            error_types[error_type]['severity_counts'].get(severity, 0) + 1
        
        # 更新最新发生时间
        created_at = bug.get('created_at')
        if created_at and (not error_types[error_type]['latest_occurrence'] or 
                          created_at > error_types[error_type]['latest_occurrence']):
            error_types[error_type]['latest_occurrence'] = created_at
    
    # 按数量排序并返回前5个
    top_issues = sorted(error_types.items(), key=lambda x: x[1]['count'], reverse=True)[:5]
    
    return [
        {
            'error_type': error_type,
            'count': data['count'],
            'severity_distribution': data['severity_counts'],
            'latest_occurrence': data['latest_occurrence']
        }
        for error_type, data in top_issues
    ]

async def _generate_daily_summary(db: DatabaseManager) -> Dict[str, Any]:
    """生成日报摘要"""
    today = datetime.now().date()
    yesterday = today - timedelta(days=1)
    
    # 获取今日和昨日的Bug数据
    all_bugs = db.get_bug_reports(limit=1000)
    
    today_bugs = [
        bug for bug in all_bugs 
        if bug.get('created_at', '')[:10] == str(today)
    ]
    
    yesterday_bugs = [
        bug for bug in all_bugs 
        if bug.get('created_at', '')[:10] == str(yesterday)
    ]
    
    return {
        "report_type": "daily",
        "date": str(today),
        "summary": {
            "new_bugs_today": len(today_bugs),
            "new_bugs_yesterday": len(yesterday_bugs),
            "change": len(today_bugs) - len(yesterday_bugs),
            "critical_bugs_today": len([bug for bug in today_bugs if bug.get('severity') == 'critical']),
            "top_error_types": _get_top_error_types(today_bugs)
        }
    }

async def _generate_weekly_summary(db: DatabaseManager) -> Dict[str, Any]:
    """生成周报摘要"""
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=7)
    
    all_bugs = db.get_bug_reports(limit=2000)
    
    week_bugs = [
        bug for bug in all_bugs 
        if start_date <= datetime.fromisoformat(bug.get('created_at', '1970-01-01')).date() <= end_date
    ]
    
    return {
        "report_type": "weekly",
        "period": f"{start_date} to {end_date}",
        "summary": {
            "total_bugs": len(week_bugs),
            "resolved_bugs": len([bug for bug in week_bugs if bug.get('status') == 'resolved']),
            "open_bugs": len([bug for bug in week_bugs if bug.get('status') == 'open']),
            "resolution_rate": len([bug for bug in week_bugs if bug.get('status') == 'resolved']) / len(week_bugs) if week_bugs else 0,
            "trends": _analyze_trends(week_bugs)
        }
    }

async def _generate_monthly_summary(db: DatabaseManager) -> Dict[str, Any]:
    """生成月报摘要"""
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=30)
    
    all_bugs = db.get_bug_reports(limit=5000)
    
    month_bugs = [
        bug for bug in all_bugs 
        if start_date <= datetime.fromisoformat(bug.get('created_at', '1970-01-01')).date() <= end_date
    ]
    
    return {
        "report_type": "monthly",
        "period": f"{start_date} to {end_date}",
        "summary": {
            "total_bugs": len(month_bugs),
            "severity_distribution": _get_severity_distribution(month_bugs),
            "category_distribution": _get_category_distribution(month_bugs),
            "resolution_trends": _get_resolution_trends(month_bugs),
            "performance_impact": _assess_performance_impact(month_bugs)
        }
    }

def _apply_filters(bug_reports: List[Dict[str, Any]], filters: Dict[str, Any]) -> List[Dict[str, Any]]:
    """应用过滤器"""
    filtered_reports = bug_reports
    
    if 'severity' in filters:
        filtered_reports = [bug for bug in filtered_reports if bug.get('severity') == filters['severity']]
    
    if 'status' in filters:
        filtered_reports = [bug for bug in filtered_reports if bug.get('status') == filters['status']]
    
    if 'error_type' in filters:
        filtered_reports = [bug for bug in filtered_reports if bug.get('error_type') == filters['error_type']]
    
    return filtered_reports

def _generate_report_data(bug_reports: List[Dict[str, Any]], report_type: str) -> Dict[str, Any]:
    """生成报告数据"""
    if report_type == "summary":
        return {
            "total_count": len(bug_reports),
            "severity_breakdown": _get_severity_distribution(bug_reports),
            "status_breakdown": _get_status_distribution(bug_reports),
            "trends": _analyze_trends(bug_reports)
        }
    elif report_type == "detailed":
        return {
            "bugs": bug_reports,
            "analysis": _analyze_trends(bug_reports),
            "recommendations": _generate_recommendations(bug_reports)
        }
    else:
        return {"bugs": bug_reports}

async def _generate_csv_report(report_data: Dict[str, Any]) -> FileResponse:
    """生成CSV报告"""
    output = io.StringIO()
    writer = csv.writer(output)
    
    # 写入标题
    writer.writerow(['Report Generated At', datetime.now().isoformat()])
    writer.writerow([])  # 空行
    
    # 写入数据
    if 'bugs' in report_data:
        writer.writerow(['Bug ID', 'Type', 'Severity', 'Status', 'Created At', 'Message'])
        for bug in report_data['bugs']:
            writer.writerow([
                bug.get('id', ''),
                bug.get('error_type', ''),
                bug.get('severity', ''),
                bug.get('status', ''),
                bug.get('created_at', ''),
                bug.get('error_message', '')[:100]  # 限制消息长度
            ])
    
    # 创建临时文件响应
    filename = f"bug_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    response = FileResponse(
        path=None,
        filename=filename,
        media_type="text/csv"
    )
    response.body = output.getvalue().encode()
    return response

def _get_top_error_types(bug_reports: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """获取主要错误类型"""
    error_counts = {}
    for bug in bug_reports:
        error_type = bug.get('error_type', 'unknown')
        error_counts[error_type] = error_counts.get(error_type, 0) + 1
    
    return [
        {'error_type': error_type, 'count': count}
        for error_type, count in sorted(error_counts.items(), key=lambda x: x[1], reverse=True)[:5]
    ]

def _get_severity_distribution(bug_reports: List[Dict[str, Any]]) -> Dict[str, int]:
    """获取严重程度分布"""
    distribution = {}
    for bug in bug_reports:
        severity = bug.get('severity', 'unknown')
        distribution[severity] = distribution.get(severity, 0) + 1
    return distribution

def _get_status_distribution(bug_reports: List[Dict[str, Any]]) -> Dict[str, int]:
    """获取状态分布"""
    distribution = {}
    for bug in bug_reports:
        status = bug.get('status', 'unknown')
        distribution[status] = distribution.get(status, 0) + 1
    return distribution

def _get_category_distribution(bug_reports: List[Dict[str, Any]]) -> Dict[str, int]:
    """获取分类分布"""
    distribution = {}
    for bug in bug_reports:
        category = bug.get('category', 'unknown')
        distribution[category] = distribution.get(category, 0) + 1
    return distribution

def _get_resolution_trends(bug_reports: List[Dict[str, Any]]) -> Dict[str, Any]:
    """获取解决趋势"""
    total = len(bug_reports)
    resolved = len([bug for bug in bug_reports if bug.get('status') == 'resolved'])
    
    return {
        "total_bugs": total,
        "resolved_bugs": resolved,
        "resolution_rate": resolved / total if total > 0 else 0,
        "average_resolution_time": "N/A"  # 需要更复杂的计算
    }

def _assess_performance_impact(bug_reports: List[Dict[str, Any]]) -> Dict[str, Any]:
    """评估性能影响"""
    critical_bugs = len([bug for bug in bug_reports if bug.get('severity') == 'critical'])
    high_bugs = len([bug for bug in bug_reports if bug.get('severity') == 'high'])
    
    return {
        "critical_impact_bugs": critical_bugs,
        "high_impact_bugs": high_bugs,
        "total_high_priority": critical_bugs + high_bugs,
        "impact_score": (critical_bugs * 3 + high_bugs * 2) / len(bug_reports) if bug_reports else 0
    }

def _generate_recommendations(bug_reports: List[Dict[str, Any]]) -> List[str]:
    """生成建议"""
    recommendations = []
    
    # 基于Bug数量的建议
    if len(bug_reports) > 50:
        recommendations.append("考虑增加自动化测试覆盖率")
    
    # 基于严重程度的建议
    critical_count = len([bug for bug in bug_reports if bug.get('severity') == 'critical'])
    if critical_count > 5:
        recommendations.append("优先处理严重级别的Bug")
    
    # 基于错误类型的建议
    js_errors = len([bug for bug in bug_reports if 'javascript' in bug.get('error_type', '').lower()])
    if js_errors > len(bug_reports) * 0.3:
        recommendations.append("加强前端代码质量检查")
    
    return recommendations or ["继续保持当前的Bug处理流程"]
