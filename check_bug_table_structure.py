#!/usr/bin/env python3
"""
检查Bug报告表结构
"""

import sqlite3
from src.bug_detection.core.database_manager import DatabaseManager

def main():
    try:
        # 创建数据库管理器
        db_manager = DatabaseManager()
        
        # 连接数据库
        conn = sqlite3.connect(db_manager.db_path)
        cursor = conn.cursor()
        
        print("🔍 检查bug_reports表结构")
        print("=" * 50)
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(bug_reports)")
        columns = cursor.fetchall()
        
        print("📊 当前表结构:")
        for col in columns:
            col_id, name, data_type, not_null, default_value, pk = col
            print(f"  - {name} ({data_type}) {'NOT NULL' if not_null else 'NULL'} {'PK' if pk else ''}")
        
        # 检查是否有updated_at和updated_by字段
        column_names = [col[1] for col in columns]
        
        missing_columns = []
        if 'updated_at' not in column_names:
            missing_columns.append('updated_at')
        if 'updated_by' not in column_names:
            missing_columns.append('updated_by')
        
        if missing_columns:
            print(f"\n⚠️ 缺少字段: {', '.join(missing_columns)}")
            print("🔧 需要添加这些字段以支持状态更新功能")
            
            # 添加缺少的字段
            for column in missing_columns:
                if column == 'updated_at':
                    cursor.execute("ALTER TABLE bug_reports ADD COLUMN updated_at TIMESTAMP")
                    print(f"✅ 已添加字段: {column}")
                elif column == 'updated_by':
                    cursor.execute("ALTER TABLE bug_reports ADD COLUMN updated_by TEXT")
                    print(f"✅ 已添加字段: {column}")
            
            conn.commit()
            print("✅ 表结构更新完成")
        else:
            print("✅ 表结构完整，包含所有必要字段")
        
        # 再次检查表结构
        print("\n📊 更新后的表结构:")
        cursor.execute("PRAGMA table_info(bug_reports)")
        columns = cursor.fetchall()
        
        for col in columns:
            col_id, name, data_type, not_null, default_value, pk = col
            print(f"  - {name} ({data_type}) {'NOT NULL' if not_null else 'NULL'} {'PK' if pk else ''}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查表结构时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
