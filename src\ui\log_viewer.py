"""
福彩3D系统日志查看器

提供日志文件的查看、过滤、搜索和下载功能
"""

import os
import re
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional
import streamlit as st
import pandas as pd


class LogViewer:
    """日志查看器"""
    
    def __init__(self, log_dir: str = "data/logs"):
        """
        初始化日志查看器
        
        Args:
            log_dir: 日志目录路径
        """
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
    
    def get_log_files(self) -> List[Dict[str, Any]]:
        """
        获取日志文件列表
        
        Returns:
            日志文件信息列表
        """
        log_files = []
        
        if not self.log_dir.exists():
            return log_files
        
        for log_file in self.log_dir.glob("*.log"):
            try:
                stat = log_file.stat()
                log_files.append({
                    "name": log_file.name,
                    "path": str(log_file),
                    "size": stat.st_size,
                    "modified": datetime.fromtimestamp(stat.st_mtime),
                    "size_mb": round(stat.st_size / 1024 / 1024, 2)
                })
            except Exception:
                continue
        
        # 按修改时间排序
        log_files.sort(key=lambda x: x["modified"], reverse=True)
        return log_files
    
    def read_log_file(self, file_path: str, max_lines: int = 1000) -> List[str]:
        """
        读取日志文件内容
        
        Args:
            file_path: 日志文件路径
            max_lines: 最大读取行数
            
        Returns:
            日志行列表
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                # 返回最后的max_lines行
                return lines[-max_lines:] if len(lines) > max_lines else lines
        except Exception as e:
            st.error(f"读取日志文件失败: {e}")
            return []
    
    def parse_log_line(self, line: str) -> Dict[str, Any]:
        """
        解析日志行
        
        Args:
            line: 日志行内容
            
        Returns:
            解析后的日志信息
        """
        # 匹配常见的日志格式
        patterns = [
            # 格式: 2025-07-16 10:10:09,964 - scheduler.task_scheduler - INFO - 消息
            r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - ([^-]+) - (\w+) - (.+)',
            # 格式: 2025-07-16 10:10:09.964 | INFO | module:function:line - 消息
            r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}) \| (\w+) \| ([^-]+) - (.+)',
            # 简单格式: INFO:module:消息
            r'(\w+):([^:]+):(.+)'
        ]
        
        for pattern in patterns:
            match = re.match(pattern, line.strip())
            if match:
                groups = match.groups()
                if len(groups) == 4:
                    return {
                        "timestamp": groups[0],
                        "module": groups[1].strip(),
                        "level": groups[2].strip(),
                        "message": groups[3].strip(),
                        "raw": line.strip()
                    }
                elif len(groups) == 3:
                    return {
                        "timestamp": "",
                        "module": groups[1].strip(),
                        "level": groups[0].strip(),
                        "message": groups[2].strip(),
                        "raw": line.strip()
                    }
        
        # 如果无法解析，返回原始内容
        return {
            "timestamp": "",
            "module": "",
            "level": "UNKNOWN",
            "message": line.strip(),
            "raw": line.strip()
        }
    
    def filter_logs(self, logs: List[Dict[str, Any]], 
                   level_filter: Optional[str] = None,
                   module_filter: Optional[str] = None,
                   search_text: Optional[str] = None,
                   time_range: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        过滤日志
        
        Args:
            logs: 日志列表
            level_filter: 日志级别过滤
            module_filter: 模块过滤
            search_text: 搜索文本
            time_range: 时间范围（小时）
            
        Returns:
            过滤后的日志列表
        """
        filtered_logs = logs.copy()
        
        # 级别过滤
        if level_filter and level_filter != "全部":
            filtered_logs = [log for log in filtered_logs if log["level"] == level_filter]
        
        # 模块过滤
        if module_filter and module_filter != "全部":
            filtered_logs = [log for log in filtered_logs if module_filter.lower() in log["module"].lower()]
        
        # 文本搜索
        if search_text:
            search_text = search_text.lower()
            filtered_logs = [
                log for log in filtered_logs 
                if search_text in log["message"].lower() or search_text in log["module"].lower()
            ]
        
        # 时间范围过滤
        if time_range and time_range > 0:
            cutoff_time = datetime.now() - timedelta(hours=time_range)
            filtered_logs = [
                log for log in filtered_logs
                if log["timestamp"] and self._parse_timestamp(log["timestamp"]) >= cutoff_time
            ]
        
        return filtered_logs
    
    def _parse_timestamp(self, timestamp_str: str) -> Optional[datetime]:
        """解析时间戳字符串"""
        try:
            # 尝试不同的时间格式
            formats = [
                "%Y-%m-%d %H:%M:%S,%f",
                "%Y-%m-%d %H:%M:%S.%f",
                "%Y-%m-%d %H:%M:%S"
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(timestamp_str, fmt)
                except ValueError:
                    continue
            return None
        except Exception:
            return None


def show_log_viewer_interface(key_prefix: str = ""):
    """显示日志查看器界面"""
    st.markdown("### 📋 日志查看器")
    
    log_viewer = LogViewer()
    
    # 获取日志文件列表
    log_files = log_viewer.get_log_files()
    
    if not log_files:
        st.warning("📝 没有找到日志文件")
        st.info("💡 日志文件通常在调度器运行后生成")
        return
    
    # 文件选择
    col1, col2 = st.columns(2)
    
    with col1:
        selected_file = st.selectbox(
            "选择日志文件",
            options=[f["name"] for f in log_files],
            format_func=lambda x: f"{x} ({next(f['size_mb'] for f in log_files if f['name'] == x)} MB)",
            key=f"{key_prefix}log_file_select"
        )
    
    with col2:
        max_lines = st.number_input(
            "最大显示行数",
            min_value=100,
            max_value=10000,
            value=1000,
            step=100,
            key=f"{key_prefix}max_lines"
        )
    
    # 过滤选项
    st.markdown("#### 🔍 过滤选项")
    
    col_filter1, col_filter2, col_filter3, col_filter4 = st.columns(4)
    
    with col_filter1:
        level_filter = st.selectbox(
            "日志级别",
            ["全部", "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
            key=f"{key_prefix}level_filter"
        )
    
    with col_filter2:
        module_filter = st.text_input(
            "模块过滤",
            placeholder="输入模块名",
            key=f"{key_prefix}module_filter"
        )
    
    with col_filter3:
        search_text = st.text_input(
            "搜索文本",
            placeholder="搜索日志内容",
            key=f"{key_prefix}search_text"
        )
    
    with col_filter4:
        time_range = st.selectbox(
            "时间范围",
            [0, 1, 6, 12, 24, 48],
            format_func=lambda x: "全部" if x == 0 else f"最近{x}小时",
            key=f"{key_prefix}time_range"
        )
    
    # 读取和解析日志
    if selected_file:
        selected_file_info = next(f for f in log_files if f["name"] == selected_file)
        
        # 显示文件信息
        st.markdown("#### 📄 文件信息")
        col_info1, col_info2, col_info3 = st.columns(3)
        
        with col_info1:
            st.metric("文件大小", f"{selected_file_info['size_mb']} MB")
        
        with col_info2:
            st.metric("修改时间", selected_file_info["modified"].strftime("%H:%M:%S"))
        
        with col_info3:
            if st.button("📥 下载日志", key=f"{key_prefix}download_log"):
                try:
                    with open(selected_file_info["path"], 'rb') as f:
                        st.download_button(
                            label="点击下载",
                            data=f.read(),
                            file_name=selected_file,
                            mime="text/plain"
                        )
                except Exception as e:
                    st.error(f"下载失败: {e}")
        
        # 读取日志内容
        with st.spinner("正在读取日志文件..."):
            log_lines = log_viewer.read_log_file(selected_file_info["path"], max_lines)
        
        if log_lines:
            # 解析日志
            parsed_logs = [log_viewer.parse_log_line(line) for line in log_lines]
            
            # 应用过滤
            filtered_logs = log_viewer.filter_logs(
                parsed_logs,
                level_filter if level_filter != "全部" else None,
                module_filter if module_filter else None,
                search_text if search_text else None,
                time_range if time_range > 0 else None
            )
            
            # 显示统计信息
            st.markdown("#### 📊 日志统计")
            col_stat1, col_stat2, col_stat3, col_stat4 = st.columns(4)
            
            with col_stat1:
                st.metric("总行数", len(log_lines))
            
            with col_stat2:
                st.metric("过滤后", len(filtered_logs))
            
            with col_stat3:
                error_count = len([log for log in filtered_logs if log["level"] in ["ERROR", "CRITICAL"]])
                st.metric("错误数", error_count, delta="需要关注" if error_count > 0 else "正常")
            
            with col_stat4:
                warning_count = len([log for log in filtered_logs if log["level"] == "WARNING"])
                st.metric("警告数", warning_count)
            
            # 显示日志内容
            st.markdown("#### 📝 日志内容")
            
            if filtered_logs:
                # 创建DataFrame用于显示
                df_data = []
                for log in filtered_logs[-100:]:  # 只显示最后100条
                    df_data.append({
                        "时间": log["timestamp"][:19] if log["timestamp"] else "",
                        "级别": log["level"],
                        "模块": log["module"][:20] + "..." if len(log["module"]) > 20 else log["module"],
                        "消息": log["message"][:100] + "..." if len(log["message"]) > 100 else log["message"]
                    })
                
                df = pd.DataFrame(df_data)
                st.dataframe(df, use_container_width=True, height=400)
                
                # 详细日志查看
                with st.expander("🔍 查看详细日志"):
                    for log in filtered_logs[-20:]:  # 显示最后20条的详细信息
                        level_color = {
                            "ERROR": "🔴",
                            "CRITICAL": "🔴",
                            "WARNING": "🟡",
                            "INFO": "🔵",
                            "DEBUG": "⚪"
                        }.get(log["level"], "⚪")
                        
                        st.markdown(f"{level_color} **{log['timestamp']}** [{log['level']}] {log['module']}")
                        st.code(log["message"])
                        st.markdown("---")
            else:
                st.info("🔍 没有找到匹配的日志记录")
        else:
            st.warning("📝 日志文件为空或无法读取")
