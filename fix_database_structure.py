#!/usr/bin/env python3
"""
数据库结构修复脚本
修复environment列缺失问题，确保数据库结构完整
"""

import sqlite3
import os
import shutil
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseStructureFixer:
    """数据库结构修复器"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or self._find_database()
        self.backup_path = None
        
    def _find_database(self) -> str:
        """查找数据库文件"""
        possible_paths = [
            "data/bug_detection.db",
            "bug_detection.db",
            "lottery_data.db"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"📁 找到数据库文件: {path}")
                return path
        
        # 如果没找到，使用默认路径
        default_path = "data/bug_detection.db"
        os.makedirs(os.path.dirname(default_path), exist_ok=True)
        logger.info(f"📁 使用默认数据库路径: {default_path}")
        return default_path
    
    def backup_database(self) -> bool:
        """备份数据库"""
        try:
            if not os.path.exists(self.db_path):
                logger.info("📄 数据库文件不存在，无需备份")
                return True
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.backup_path = f"{self.db_path}.fix_backup_{timestamp}"
            
            shutil.copy2(self.db_path, self.backup_path)
            logger.info(f"💾 数据库已备份到: {self.backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库备份失败: {e}")
            return False
    
    def diagnose_structure(self) -> dict:
        """诊断数据库结构"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取表结构
            cursor.execute("PRAGMA table_info(bug_reports)")
            columns = cursor.fetchall()
            
            # 获取表创建语句
            cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='bug_reports'")
            create_sql = cursor.fetchone()
            
            conn.close()
            
            # 分析结果
            column_names = [col[1] for col in columns]
            
            # 期望的列
            expected_columns = [
                'id', 'timestamp', 'error_type', 'severity', 'page_name', 
                'error_message', 'stack_trace', 'status', 'created_at',
                'environment', 'category', 'priority', 'tags', 'source',
                'component_name', 'reproduction_steps', 'system_context',
                'user_journey', 'screenshots'
            ]
            
            missing_columns = [col for col in expected_columns if col not in column_names]
            extra_columns = [col for col in column_names if col not in expected_columns]
            duplicate_columns = [col for col in column_names if column_names.count(col) > 1]
            
            diagnosis = {
                'total_columns': len(columns),
                'column_names': column_names,
                'missing_columns': missing_columns,
                'extra_columns': extra_columns,
                'duplicate_columns': list(set(duplicate_columns)),
                'create_sql': create_sql[0] if create_sql else None,
                'expected_columns': expected_columns
            }
            
            logger.info(f"📊 数据库诊断完成:")
            logger.info(f"   总列数: {diagnosis['total_columns']}")
            logger.info(f"   缺失列: {diagnosis['missing_columns']}")
            logger.info(f"   多余列: {diagnosis['extra_columns']}")
            logger.info(f"   重复列: {diagnosis['duplicate_columns']}")
            
            return diagnosis
            
        except Exception as e:
            logger.error(f"❌ 数据库诊断失败: {e}")
            return {}
    
    def fix_structure(self, diagnosis: dict) -> bool:
        """修复数据库结构"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 如果有重复列或结构严重问题，重建表
            if diagnosis.get('duplicate_columns') or len(diagnosis.get('missing_columns', [])) > 5:
                logger.info("🔧 检测到严重结构问题，开始重建表...")
                return self._rebuild_table(cursor, conn)
            
            # 否则只添加缺失的列
            missing_columns = diagnosis.get('missing_columns', [])
            if missing_columns:
                logger.info(f"🔧 添加缺失的列: {missing_columns}")
                return self._add_missing_columns(cursor, conn, missing_columns)
            
            logger.info("✅ 数据库结构已是最新，无需修复")
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库结构修复失败: {e}")
            return False
    
    def _rebuild_table(self, cursor, conn) -> bool:
        """重建表结构"""
        try:
            # 备份现有数据
            cursor.execute("SELECT * FROM bug_reports")
            existing_data = cursor.fetchall()
            logger.info(f"📦 备份了 {len(existing_data)} 条现有数据")
            
            # 删除旧表
            cursor.execute("DROP TABLE IF EXISTS bug_reports_old")
            cursor.execute("ALTER TABLE bug_reports RENAME TO bug_reports_old")
            
            # 创建新表
            create_sql = """
            CREATE TABLE bug_reports (
                id TEXT PRIMARY KEY,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                error_type TEXT NOT NULL,
                severity TEXT DEFAULT 'medium',
                page_name TEXT,
                error_message TEXT,
                stack_trace TEXT,
                status TEXT DEFAULT 'open',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                environment TEXT DEFAULT 'production',
                category TEXT DEFAULT 'general',
                priority TEXT DEFAULT 'medium',
                tags TEXT,
                source TEXT DEFAULT 'user',
                component_name TEXT,
                reproduction_steps TEXT,
                system_context TEXT,
                user_journey TEXT,
                screenshots TEXT
            )
            """
            cursor.execute(create_sql)
            logger.info("✅ 新表结构创建成功")
            
            # 迁移数据
            if existing_data:
                # 获取旧表结构
                cursor.execute("PRAGMA table_info(bug_reports_old)")
                old_columns = [col[1] for col in cursor.fetchall()]
                
                # 构建插入语句
                common_columns = ['id', 'timestamp', 'error_type', 'severity', 'page_name', 
                                'error_message', 'stack_trace', 'status', 'created_at']
                available_columns = [col for col in common_columns if col in old_columns]
                
                if available_columns:
                    placeholders = ', '.join(['?' for _ in available_columns])
                    columns_str = ', '.join(available_columns)
                    
                    insert_sql = f"INSERT INTO bug_reports ({columns_str}) SELECT {columns_str} FROM bug_reports_old"
                    cursor.execute(insert_sql)
                    
                    migrated_count = cursor.rowcount
                    logger.info(f"✅ 成功迁移 {migrated_count} 条数据")
            
            # 删除旧表
            cursor.execute("DROP TABLE bug_reports_old")
            conn.commit()
            
            logger.info("🎉 表结构重建完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 表结构重建失败: {e}")
            conn.rollback()
            return False
    
    def _add_missing_columns(self, cursor, conn, missing_columns: list) -> bool:
        """添加缺失的列"""
        try:
            column_definitions = {
                'environment': 'TEXT DEFAULT "production"',
                'category': 'TEXT DEFAULT "general"',
                'priority': 'TEXT DEFAULT "medium"',
                'tags': 'TEXT',
                'source': 'TEXT DEFAULT "user"',
                'component_name': 'TEXT',
                'reproduction_steps': 'TEXT',
                'system_context': 'TEXT',
                'user_journey': 'TEXT',
                'screenshots': 'TEXT'
            }
            
            added_count = 0
            for column in missing_columns:
                if column in column_definitions:
                    try:
                        sql = f"ALTER TABLE bug_reports ADD COLUMN {column} {column_definitions[column]}"
                        cursor.execute(sql)
                        logger.info(f"✅ 添加列: {column}")
                        added_count += 1
                    except Exception as e:
                        logger.warning(f"⚠️ 添加列失败 {column}: {e}")
            
            conn.commit()
            logger.info(f"📈 总共添加了 {added_count} 个列")
            return added_count > 0
            
        except Exception as e:
            logger.error(f"❌ 添加列操作失败: {e}")
            conn.rollback()
            return False
    
    def verify_structure(self) -> bool:
        """验证数据库结构"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute("PRAGMA table_info(bug_reports)")
            columns = cursor.fetchall()
            
            logger.info("📋 修复后的表结构:")
            for col in columns:
                logger.info(f"   {col[1]} {col[2]} (默认值: {col[4]})")
            
            # 检查必需列
            required_columns = ['environment', 'category', 'priority', 'tags', 'source']
            column_names = [col[1] for col in columns]
            
            missing_required = [col for col in required_columns if col not in column_names]
            
            if missing_required:
                logger.error(f"❌ 仍缺失必需列: {missing_required}")
                return False
            
            # 测试数据操作
            test_id = f"test_fix_{datetime.now().strftime('%H%M%S')}"
            cursor.execute("""
                INSERT INTO bug_reports 
                (id, error_type, severity, environment, category, priority, tags, source)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (test_id, 'test_error', 'low', 'test', 'fix_validation', 'low', 'test,fix', 'fix_script'))
            
            # 查询验证
            cursor.execute("SELECT environment, category, priority, tags, source FROM bug_reports WHERE id = ?", (test_id,))
            result = cursor.fetchone()
            
            if result:
                logger.info("✅ 数据操作测试成功")
                logger.info(f"   测试数据: {result}")
                
                # 清理测试数据
                cursor.execute("DELETE FROM bug_reports WHERE id = ?", (test_id,))
                conn.commit()
                logger.info("🧹 测试数据已清理")
            else:
                logger.error("❌ 数据操作测试失败")
                return False
            
            conn.close()
            logger.info("✅ 数据库结构验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库结构验证失败: {e}")
            return False
    
    def fix_database(self) -> bool:
        """执行完整的数据库修复流程"""
        logger.info("🚀 开始数据库结构修复...")
        logger.info("=" * 60)
        
        # 1. 备份数据库
        if not self.backup_database():
            return False
        
        # 2. 诊断结构
        diagnosis = self.diagnose_structure()
        if not diagnosis:
            return False
        
        # 3. 修复结构
        if not self.fix_structure(diagnosis):
            logger.error("❌ 结构修复失败，修复中止")
            return False
        
        # 4. 验证结构
        if not self.verify_structure():
            logger.error("❌ 结构验证失败")
            return False
        
        logger.info("=" * 60)
        logger.info("🎉 数据库结构修复完成！")
        
        if self.backup_path:
            logger.info(f"💾 备份文件: {self.backup_path}")
        
        return True

def main():
    """主函数"""
    logger.info("🗄️ 数据库结构修复工具")
    logger.info("=" * 60)
    
    fixer = DatabaseStructureFixer()
    
    success = fixer.fix_database()
    
    if success:
        print("\n🚀 下一步:")
        print("1. 重启API和Streamlit服务")
        print("2. 验证Bug分类统计功能")
        print("3. 检查environment列相关功能")
    else:
        print("\n❌ 数据库修复失败")
        if fixer.backup_path:
            print(f"💾 可以从备份恢复: {fixer.backup_path}")
    
    return success

if __name__ == "__main__":
    main()
