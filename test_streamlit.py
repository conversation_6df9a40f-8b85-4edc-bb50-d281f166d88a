#!/usr/bin/env python3
"""
测试Streamlit应用功能
"""

import requests
import time

def test_streamlit_app():
    """测试Streamlit应用是否正常运行"""
    print("🧪 测试Streamlit应用...")
    
    # 测试应用是否启动
    try:
        response = requests.get("http://localhost:8501", timeout=10)
        if response.status_code == 200:
            print("✅ Streamlit应用正常运行")
            print(f"   状态码: {response.status_code}")
            print(f"   响应大小: {len(response.content)} bytes")
            return True
        else:
            print(f"❌ Streamlit应用响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到Streamlit应用: {e}")
        return False

def test_api_integration():
    """测试API集成"""
    print("\n🔗 测试API集成...")
    
    # 测试FastAPI是否运行
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ FastAPI服务正常")
            print(f"   数据库记录: {data.get('database_records', 0)}")
            print(f"   数据范围: {data.get('date_range', 'N/A')}")
            return True
        else:
            print(f"❌ FastAPI服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到FastAPI服务: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 Streamlit应用测试套件")
    print("=" * 40)
    
    # 测试Streamlit应用
    streamlit_ok = test_streamlit_app()
    
    # 测试API集成
    api_ok = test_api_integration()
    
    # 总结
    print(f"\n📋 测试结果:")
    print(f"   Streamlit应用: {'✅ 正常' if streamlit_ok else '❌ 异常'}")
    print(f"   FastAPI集成: {'✅ 正常' if api_ok else '❌ 异常'}")
    
    if streamlit_ok and api_ok:
        print(f"\n🎉 所有测试通过！")
        print(f"📱 Streamlit应用: http://localhost:8501")
        print(f"🔗 FastAPI文档: http://localhost:8000/docs")
        return True
    else:
        print(f"\n⚠️ 部分测试失败，请检查服务状态")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
