"""
准确性导向融合算法
实现AccuracyFocusedFusion类，开发四层融合策略
"""

import logging
from collections import defaultdict
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple

import numpy as np

from .model_performance_tracker import ModelPerformanceTracker


@dataclass
class ModelPrediction:
    """模型预测结果数据结构"""
    model_name: str
    top_candidate: str
    top_confidence: float
    all_candidates: Dict[str, float]  # 号码 -> 置信度
    
    def get_top_candidates(self, n: int = 3) -> List[str]:
        """获取前N个候选号码"""
        sorted_candidates = sorted(self.all_candidates.items(), key=lambda x: x[1], reverse=True)
        return [candidate for candidate, _ in sorted_candidates[:n]]
    
    def get_confidence(self, number: str) -> float:
        """获取指定号码的置信度"""
        return self.all_candidates.get(number, 0.0)
    
    def get_all_candidates(self) -> List[Tuple[str, float]]:
        """获取所有候选号码和置信度"""
        return list(self.all_candidates.items())

@dataclass
class PredictionResult:
    """预测结果数据结构"""
    number: str
    confidence: float
    method: str
    rank: int
    model_support: List[str] = None
    fusion_details: Dict[str, Any] = None

class AccuracyFocusedFusion:
    """准确性导向融合算法"""
    
    def __init__(self, performance_tracker: ModelPerformanceTracker):
        """
        初始化融合算法
        
        Args:
            performance_tracker: 模型性能跟踪器
        """
        self.tracker = performance_tracker
        self.logger = logging.getLogger(__name__)
        
        # 四层融合策略
        self.fusion_strategies = [
            self._intersection_analysis,
            self._weighted_voting,
            self._confidence_ranking,
            self._best_model_fallback
        ]
    
    def get_single_best_prediction(self, model_predictions: List[ModelPrediction]) -> PredictionResult:
        """
        获取单一最优预测 - 四层融合策略
        
        Args:
            model_predictions: 各模型的预测结果列表
            
        Returns:
            单一最优预测结果
        """
        try:
            self.logger.info("开始执行四层融合策略")
            
            # 依次执行四层融合策略
            for i, strategy in enumerate(self.fusion_strategies, 1):
                result = strategy(model_predictions)
                if result:
                    self.logger.info(f"第{i}层策略成功: {result.method}")
                    return result
            
            # 如果所有策略都失败，返回默认结果
            self.logger.warning("所有融合策略都失败，返回默认结果")
            return PredictionResult(
                number="000",
                confidence=0.1,
                method="默认回退",
                rank=1
            )
            
        except Exception as e:
            self.logger.error(f"融合预测失败: {e}")
            return PredictionResult(
                number="000",
                confidence=0.1,
                method="错误回退",
                rank=1
            )
    
    def _intersection_analysis(self, model_predictions: List[ModelPrediction]) -> Optional[PredictionResult]:
        """
        第一层：多模型交集分析
        找出多个模型都预测的共同号码
        
        Args:
            model_predictions: 模型预测列表
            
        Returns:
            交集分析结果，如果没有交集返回None
        """
        try:
            if len(model_predictions) < 2:
                return None
            
            # 获取每个模型的前3个候选
            model_candidates = []
            for pred in model_predictions:
                candidates = set(pred.get_top_candidates(3))
                model_candidates.append(candidates)
            
            # 找交集
            intersection = set.intersection(*model_candidates)
            
            if intersection:
                # 选择在交集中置信度最高的号码
                best_number = None
                max_total_confidence = 0
                
                for number in intersection:
                    total_confidence = sum(pred.get_confidence(number) for pred in model_predictions)
                    if total_confidence > max_total_confidence:
                        max_total_confidence = total_confidence
                        best_number = number
                
                if best_number:
                    avg_confidence = max_total_confidence / len(model_predictions)
                    support_models = [pred.model_name for pred in model_predictions 
                                    if best_number in pred.get_top_candidates(3)]
                    
                    self.logger.info(f"交集分析成功: {best_number}, 支持模型: {support_models}")
                    
                    return PredictionResult(
                        number=best_number,
                        confidence=avg_confidence,
                        method="多模型交集分析",
                        rank=1,
                        model_support=support_models,
                        fusion_details={
                            "intersection_size": len(intersection),
                            "total_models": len(model_predictions),
                            "support_count": len(support_models)
                        }
                    )
            
            return None
            
        except Exception as e:
            self.logger.error(f"交集分析失败: {e}")
            return None
    
    def _weighted_voting(self, model_predictions: List[ModelPrediction]) -> Optional[PredictionResult]:
        """
        第二层：基于历史准确率的加权投票
        
        Args:
            model_predictions: 模型预测列表
            
        Returns:
            加权投票结果
        """
        try:
            # 获取动态权重
            weights = self.tracker.calculate_dynamic_weights()
            
            vote_scores = defaultdict(float)
            vote_details = defaultdict(list)
            
            # 加权投票
            for pred in model_predictions:
                model_weight = weights.get(pred.model_name, 0.25)
                
                for candidate, confidence in pred.get_all_candidates():
                    weighted_score = confidence * model_weight
                    vote_scores[candidate] += weighted_score
                    vote_details[candidate].append({
                        'model': pred.model_name,
                        'confidence': confidence,
                        'weight': model_weight,
                        'weighted_score': weighted_score
                    })
            
            if vote_scores:
                best_number = max(vote_scores, key=vote_scores.get)
                best_score = vote_scores[best_number]
                
                # 计算支持模型
                support_models = [detail['model'] for detail in vote_details[best_number]]
                
                self.logger.info(f"加权投票成功: {best_number}, 得分: {best_score:.3f}")
                
                return PredictionResult(
                    number=best_number,
                    confidence=best_score,
                    method="历史准确率加权投票",
                    rank=1,
                    model_support=support_models,
                    fusion_details={
                        "vote_score": best_score,
                        "weights_used": weights,
                        "vote_details": dict(vote_details[best_number])
                    }
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"加权投票失败: {e}")
            return None
    
    def _confidence_ranking(self, model_predictions: List[ModelPrediction]) -> Optional[PredictionResult]:
        """
        第三层：综合置信度排序
        
        Args:
            model_predictions: 模型预测列表
            
        Returns:
            置信度排序结果
        """
        try:
            weights = self.tracker.calculate_dynamic_weights()
            all_candidates = defaultdict(list)

            # 执行一致性检查
            consensus_analysis = self.check_model_consensus(model_predictions)

            # 收集所有候选号码及其置信度
            for pred in model_predictions:
                model_weight = weights.get(pred.model_name, 0.25)

                for candidate, confidence in pred.get_all_candidates():
                    weighted_confidence = confidence * model_weight
                    all_candidates[candidate].append({
                        'model': pred.model_name,
                        'confidence': confidence,
                        'weighted_confidence': weighted_confidence
                    })
            
            # 计算综合置信度并排序
            ranked_candidates = []
            for number, confidences in all_candidates.items():
                # 计算平均置信度和最高置信度
                avg_confidence = float(np.mean([c['weighted_confidence'] for c in confidences]))
                max_confidence = float(max([c['weighted_confidence'] for c in confidences]))
                model_count = len(confidences)
                
                # 改进的综合评分计算，包含一致性分析
                composite_score = self._calculate_enhanced_confidence(
                    avg_confidence, max_confidence, model_count, confidences, consensus_analysis
                )
                
                ranked_candidates.append({
                    'number': number,
                    'composite_score': composite_score,
                    'avg_confidence': avg_confidence,
                    'max_confidence': max_confidence,
                    'model_count': model_count,
                    'details': confidences
                })
            
            # 按综合评分排序
            ranked_candidates.sort(key=lambda x: x['composite_score'], reverse=True)
            
            if ranked_candidates:
                best = ranked_candidates[0]
                support_models = [detail['model'] for detail in best['details']]
                
                self.logger.info(f"置信度排序成功: {best['number']}, 综合评分: {best['composite_score']:.3f}")
                
                return PredictionResult(
                    number=best['number'],
                    confidence=best['composite_score'],
                    method="综合置信度排序",
                    rank=1,
                    model_support=support_models,
                    fusion_details={
                        "composite_score": best['composite_score'],
                        "avg_confidence": best['avg_confidence'],
                        "max_confidence": best['max_confidence'],
                        "model_count": best['model_count']
                    }
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"置信度排序失败: {e}")
            return None
    
    def _best_model_fallback(self, model_predictions: List[ModelPrediction]) -> PredictionResult:
        """
        第四层：最佳模型回退策略
        如果前三层都无法确定，选择历史表现最佳的模型结果
        
        Args:
            model_predictions: 模型预测列表
            
        Returns:
            最佳模型的预测结果
        """
        try:
            # 获取所有模型的准确率
            model_accuracies = {}
            for pred in model_predictions:
                accuracy = self.tracker.get_model_accuracy(pred.model_name)
                model_accuracies[pred.model_name] = accuracy
            
            # 找到准确率最高的模型
            best_model = max(model_accuracies, key=model_accuracies.get)
            best_accuracy = model_accuracies[best_model]
            
            # 找到对应的预测结果
            best_prediction = None
            for pred in model_predictions:
                if pred.model_name == best_model:
                    best_prediction = pred
                    break
            
            if best_prediction:
                self.logger.info(f"最佳模型回退: {best_model}, 准确率: {best_accuracy:.3f}")
                
                return PredictionResult(
                    number=best_prediction.top_candidate,
                    confidence=best_prediction.top_confidence,
                    method=f"最佳模型({best_model})回退",
                    rank=1,
                    model_support=[best_model],
                    fusion_details={
                        "best_model": best_model,
                        "model_accuracy": best_accuracy,
                        "all_accuracies": model_accuracies
                    }
                )
            
            # 如果找不到最佳预测，返回第一个
            if model_predictions:
                first_pred = model_predictions[0]
                return PredictionResult(
                    number=first_pred.top_candidate,
                    confidence=first_pred.top_confidence,
                    method="默认第一模型回退",
                    rank=1,
                    model_support=[first_pred.model_name]
                )
            
            # 最后的默认回退
            return PredictionResult(
                number="000",
                confidence=0.1,
                method="系统默认回退",
                rank=1
            )
            
        except Exception as e:
            self.logger.error(f"最佳模型回退失败: {e}")
            return PredictionResult(
                number="000",
                confidence=0.1,
                method="错误回退",
                rank=1
            )

    def _calculate_enhanced_confidence(self, avg_confidence: float, max_confidence: float,
                                     model_count: int, confidences: List[Dict],
                                     consensus_analysis: Optional[Dict[str, Any]] = None) -> float:
        """
        计算增强的置信度，包含多因素加权和一致性分析

        Args:
            avg_confidence: 平均置信度
            max_confidence: 最高置信度
            model_count: 支持模型数量
            confidences: 置信度详情列表
            consensus_analysis: 一致性分析结果

        Returns:
            增强的置信度评分
        """
        try:
            # 基础置信度 (权重: 0.4)
            base_confidence = avg_confidence * 0.7 + max_confidence * 0.3

            # 模型支持度加权 (权重: 0.2)
            support_bonus = min(model_count * 0.1, 0.3)

            # 一致性奖励 (权重: 0.2)
            if len(confidences) > 1:
                confidence_values = [c['confidence'] for c in confidences]
                std_dev = float(np.std(confidence_values))
                consistency_bonus = max(0.1 - std_dev, 0.0) * 0.5
            else:
                consistency_bonus = 0.0

            # 共识奖励 (权重: 0.1)
            consensus_bonus = 0.0
            if consensus_analysis:
                consensus_level = consensus_analysis.get('consensus_level', 0.0)
                consensus_bonus = consensus_level * 0.2

            # 历史性能加权 (权重: 0.1)
            weights = self.tracker.calculate_dynamic_weights()
            performance_bonus = 0
            for conf in confidences:
                model_weight = weights.get(conf['model'], 0.25)
                performance_bonus += model_weight * 0.1
            performance_bonus = min(performance_bonus, 0.2)

            # 最终置信度
            final_confidence = min(
                base_confidence + support_bonus + consistency_bonus + consensus_bonus + performance_bonus,
                0.95
            )

            return final_confidence

        except Exception as e:
            self.logger.error(f"计算增强置信度失败: {e}")
            return avg_confidence * 0.7 + max_confidence * 0.3

    def check_model_consensus(self, model_predictions: List[ModelPrediction]) -> Dict[str, Any]:
        """
        检查模型预测一致性

        Args:
            model_predictions: 模型预测列表

        Returns:
            一致性分析结果
        """
        try:
            all_predictions = []
            model_contributions = {}

            # 收集所有预测
            for pred in model_predictions:
                top_predictions = pred.get_top_candidates(3)  # 取每个模型前3个预测
                all_predictions.extend(top_predictions)
                model_contributions[pred.model_name] = top_predictions

            # 统计预测频次
            from collections import Counter
            prediction_counts = Counter(all_predictions)

            # 计算一致性指标
            total_predictions = len(all_predictions)
            if total_predictions == 0:
                return {
                    'consensus_level': 0.0,
                    'most_agreed': ('000', 0),
                    'agreement_distribution': {},
                    'model_contributions': model_contributions,
                    'consistency_scores': {},
                    'total_predictions': 0
                }

            most_common = prediction_counts.most_common(1)[0]
            consensus_level = most_common[1] / total_predictions

            # 计算模型间的一致性得分
            consistency_scores = {}
            for model, preds in model_contributions.items():
                # 计算该模型与整体共识的一致性
                consensus_matches = sum(1 for pred in preds if pred == most_common[0])
                consistency_scores[model] = consensus_matches / len(preds) if preds else 0.0

            return {
                'consensus_level': consensus_level,
                'most_agreed': most_common,
                'agreement_distribution': dict(prediction_counts.most_common(5)),
                'model_contributions': model_contributions,
                'consistency_scores': consistency_scores,
                'total_predictions': total_predictions
            }

        except Exception as e:
            self.logger.error(f"模型一致性检查失败: {e}")
            return {
                'consensus_level': 0.0,
                'most_agreed': ('000', 0),
                'agreement_distribution': {},
                'model_contributions': {},
                'consistency_scores': {},
                'total_predictions': 0
            }
    
    def get_fusion_explanation(self, result: PredictionResult) -> str:
        """
        获取融合预测的解释说明
        
        Args:
            result: 预测结果
            
        Returns:
            解释说明文本
        """
        try:
            explanation = f"预测方法：{result.method}\n"
            explanation += f"推荐号码：{result.number}\n"
            explanation += f"预测置信度：{result.confidence:.1%}\n"
            
            if result.model_support:
                explanation += f"支持模型：{', '.join(result.model_support)}\n"
            
            if result.fusion_details:
                explanation += "技术详情：\n"
                for key, value in result.fusion_details.items():
                    if isinstance(value, (int, float)):
                        explanation += f"  {key}: {value:.3f}\n"
                    else:
                        explanation += f"  {key}: {value}\n"
            
            return explanation
            
        except Exception as e:
            self.logger.error(f"生成解释说明失败: {e}")
            return f"预测号码：{result.number}，置信度：{result.confidence:.1%}"
