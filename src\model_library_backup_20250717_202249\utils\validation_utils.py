"""
模型验证工具函数
"""

import numpy as np
from typing import List, Dict, Any, Tu<PERSON>, Optional
from datetime import datetime
import random
from collections import defaultdict


class ModelValidator:
    """模型验证器"""
    
    @staticmethod
    def validate_prediction_format(prediction: Dict[str, Any]) -> Dict[str, Any]:
        """验证预测结果格式"""
        required_fields = ['百位', '十位', '个位']
        
        for field in required_fields:
            if field not in prediction:
                return {
                    "valid": False,
                    "error": f"预测结果缺少字段: {field}"
                }
            
            # 检查字段值是否为字典（概率分布）
            if not isinstance(prediction[field], dict):
                return {
                    "valid": False,
                    "error": f"字段 {field} 应为概率分布字典"
                }
            
            # 检查数字范围
            for digit, prob in prediction[field].items():
                try:
                    digit_int = int(digit)
                    if not (0 <= digit_int <= 9):
                        return {
                            "valid": False,
                            "error": f"数字 {digit} 超出范围 [0-9]"
                        }
                    
                    if not (0 <= prob <= 1):
                        return {
                            "valid": False,
                            "error": f"概率 {prob} 超出范围 [0-1]"
                        }
                except ValueError:
                    return {
                        "valid": False,
                        "error": f"无效的数字格式: {digit}"
                    }
        
        return {"valid": True}
    
    @staticmethod
    def calculate_prediction_accuracy(predictions: List[Dict[str, Any]], actuals: List[str]) -> Dict[str, float]:
        """计算预测准确率"""
        if len(predictions) != len(actuals):
            raise ValueError("预测结果和实际结果数量不匹配")
        
        if not predictions:
            return {
                "direct_accuracy": 0.0,
                "position_accuracy": 0.0,
                "group_accuracy": 0.0,
                "total_predictions": 0
            }
        
        direct_correct = 0
        position_correct = 0
        group_correct = 0
        
        for pred, actual in zip(predictions, actuals):
            if len(actual) != 3:
                continue
            
            actual_digits = [int(d) for d in actual]
            
            # 获取预测的最高概率数字
            pred_digits = []
            for pos in ['百位', '十位', '个位']:
                if pos in pred and pred[pos]:
                    best_digit = max(pred[pos].items(), key=lambda x: x[1])[0]
                    pred_digits.append(int(best_digit))
                else:
                    pred_digits.append(0)
            
            # 直选准确率
            if pred_digits == actual_digits:
                direct_correct += 1
                position_correct += 1
                group_correct += 1
            else:
                # 位置准确率（至少一位正确）
                if any(pred_digits[i] == actual_digits[i] for i in range(3)):
                    position_correct += 1
                
                # 组选准确率（数字相同但顺序不同）
                if sorted(pred_digits) == sorted(actual_digits):
                    group_correct += 1
        
        total = len(predictions)
        return {
            "direct_accuracy": direct_correct / total,
            "position_accuracy": position_correct / total,
            "group_accuracy": group_correct / total,
            "total_predictions": total,
            "direct_correct": direct_correct,
            "position_correct": position_correct,
            "group_correct": group_correct
        }
    
    @staticmethod
    def calculate_confidence_calibration(predictions: List[Dict[str, Any]], actuals: List[str], confidence_scores: List[float]) -> Dict[str, Any]:
        """计算置信度校准"""
        if len(predictions) != len(actuals) or len(predictions) != len(confidence_scores):
            raise ValueError("输入数据长度不匹配")
        
        # 将置信度分组
        bins = np.linspace(0, 1, 11)  # 10个区间
        bin_accuracies = []
        bin_confidences = []
        bin_counts = []
        
        for i in range(len(bins) - 1):
            bin_mask = (confidence_scores >= bins[i]) & (confidence_scores < bins[i + 1])
            bin_indices = np.where(bin_mask)[0]
            
            if len(bin_indices) > 0:
                bin_preds = [predictions[j] for j in bin_indices]
                bin_actuals = [actuals[j] for j in bin_indices]
                bin_confs = [confidence_scores[j] for j in bin_indices]
                
                accuracy_result = ModelValidator.calculate_prediction_accuracy(bin_preds, bin_actuals)
                
                bin_accuracies.append(accuracy_result['direct_accuracy'])
                bin_confidences.append(np.mean(bin_confs))
                bin_counts.append(len(bin_indices))
            else:
                bin_accuracies.append(0.0)
                bin_confidences.append((bins[i] + bins[i + 1]) / 2)
                bin_counts.append(0)
        
        # 计算校准误差
        calibration_error = np.mean([
            abs(acc - conf) * count / len(predictions)
            for acc, conf, count in zip(bin_accuracies, bin_confidences, bin_counts)
        ])
        
        return {
            "calibration_error": calibration_error,
            "bin_accuracies": bin_accuracies,
            "bin_confidences": bin_confidences,
            "bin_counts": bin_counts,
            "bins": bins.tolist()
        }


class CrossValidator:
    """交叉验证器"""
    
    @staticmethod
    def k_fold_split(data: List[Dict[str, Any]], k: int = 5, shuffle: bool = True) -> List[Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]]:
        """K折交叉验证数据分割"""
        if k <= 1 or k > len(data):
            raise ValueError(f"K值 {k} 无效，应在 [2, {len(data)}] 范围内")
        
        data_copy = data.copy()
        if shuffle:
            random.shuffle(data_copy)
        
        fold_size = len(data_copy) // k
        folds = []
        
        for i in range(k):
            start_idx = i * fold_size
            end_idx = start_idx + fold_size if i < k - 1 else len(data_copy)
            
            test_data = data_copy[start_idx:end_idx]
            train_data = data_copy[:start_idx] + data_copy[end_idx:]
            
            folds.append((train_data, test_data))
        
        return folds
    
    @staticmethod
    def time_series_split(data: List[Dict[str, Any]], n_splits: int = 5, test_size: Optional[int] = None) -> List[Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]]:
        """时间序列交叉验证分割"""
        if n_splits <= 1:
            raise ValueError("n_splits 必须大于 1")
        
        if test_size is None:
            test_size = len(data) // (n_splits + 1)
        
        splits = []
        
        for i in range(n_splits):
            # 计算测试集的结束位置
            test_end = len(data) - (n_splits - i - 1) * test_size
            test_start = test_end - test_size
            
            # 确保有足够的训练数据
            if test_start <= 0:
                continue
            
            train_data = data[:test_start]
            test_data = data[test_start:test_end]
            
            if len(train_data) > 0 and len(test_data) > 0:
                splits.append((train_data, test_data))
        
        return splits
    
    @staticmethod
    def validate_model_performance(model, train_test_splits: List[Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]]) -> Dict[str, Any]:
        """验证模型性能"""
        fold_results = []
        
        for i, (train_data, test_data) in enumerate(train_test_splits):
            try:
                # 训练模型
                training_result = model.train(train_data)
                
                if not training_result.success:
                    fold_results.append({
                        "fold": i,
                        "success": False,
                        "error": training_result.error_message
                    })
                    continue
                
                # 预测测试数据
                predictions = []
                actuals = []
                
                for record in test_data:
                    if 'number' in record:
                        # 使用历史数据进行预测
                        history = train_data[-100:]  # 使用最近100期作为历史
                        prediction = model.predict(history, top_n=1)
                        
                        predictions.append({
                            '百位': prediction.百位,
                            '十位': prediction.十位,
                            '个位': prediction.个位
                        })
                        actuals.append(record['number'])
                
                # 计算准确率
                if predictions and actuals:
                    accuracy_result = ModelValidator.calculate_prediction_accuracy(predictions, actuals)
                    
                    fold_results.append({
                        "fold": i,
                        "success": True,
                        "accuracy": accuracy_result,
                        "training_time": training_result.training_duration,
                        "test_size": len(test_data)
                    })
                else:
                    fold_results.append({
                        "fold": i,
                        "success": False,
                        "error": "无有效预测结果"
                    })
                    
            except Exception as e:
                fold_results.append({
                    "fold": i,
                    "success": False,
                    "error": str(e)
                })
        
        # 汇总结果
        successful_folds = [r for r in fold_results if r.get("success", False)]
        
        if not successful_folds:
            return {
                "success": False,
                "error": "所有折都验证失败",
                "fold_results": fold_results
            }
        
        # 计算平均性能
        avg_direct_accuracy = np.mean([r["accuracy"]["direct_accuracy"] for r in successful_folds])
        avg_position_accuracy = np.mean([r["accuracy"]["position_accuracy"] for r in successful_folds])
        avg_group_accuracy = np.mean([r["accuracy"]["group_accuracy"] for r in successful_folds])
        avg_training_time = np.mean([r["training_time"] for r in successful_folds])
        
        # 计算标准差
        std_direct_accuracy = np.std([r["accuracy"]["direct_accuracy"] for r in successful_folds])
        std_position_accuracy = np.std([r["accuracy"]["position_accuracy"] for r in successful_folds])
        std_group_accuracy = np.std([r["accuracy"]["group_accuracy"] for r in successful_folds])
        
        return {
            "success": True,
            "n_folds": len(train_test_splits),
            "successful_folds": len(successful_folds),
            "average_performance": {
                "direct_accuracy": avg_direct_accuracy,
                "position_accuracy": avg_position_accuracy,
                "group_accuracy": avg_group_accuracy,
                "training_time": avg_training_time
            },
            "performance_std": {
                "direct_accuracy": std_direct_accuracy,
                "position_accuracy": std_position_accuracy,
                "group_accuracy": std_group_accuracy
            },
            "fold_results": fold_results
        }
    
    @staticmethod
    def bootstrap_validation(model, data: List[Dict[str, Any]], n_bootstrap: int = 100, sample_ratio: float = 0.8) -> Dict[str, Any]:
        """自助法验证"""
        bootstrap_results = []
        
        for i in range(n_bootstrap):
            try:
                # 自助采样
                sample_size = int(len(data) * sample_ratio)
                bootstrap_sample = random.choices(data, k=sample_size)
                
                # 分割训练和测试集
                split_point = int(len(bootstrap_sample) * 0.8)
                train_data = bootstrap_sample[:split_point]
                test_data = bootstrap_sample[split_point:]
                
                # 训练和测试
                training_result = model.train(train_data)
                
                if training_result.success and test_data:
                    predictions = []
                    actuals = []
                    
                    for record in test_data:
                        if 'number' in record:
                            history = train_data[-50:]
                            prediction = model.predict(history, top_n=1)
                            
                            predictions.append({
                                '百位': prediction.百位,
                                '十位': prediction.十位,
                                '个位': prediction.个位
                            })
                            actuals.append(record['number'])
                    
                    if predictions and actuals:
                        accuracy_result = ModelValidator.calculate_prediction_accuracy(predictions, actuals)
                        bootstrap_results.append(accuracy_result["direct_accuracy"])
                
            except Exception:
                continue
        
        if not bootstrap_results:
            return {
                "success": False,
                "error": "所有自助样本验证失败"
            }
        
        return {
            "success": True,
            "n_bootstrap": n_bootstrap,
            "successful_samples": len(bootstrap_results),
            "mean_accuracy": np.mean(bootstrap_results),
            "std_accuracy": np.std(bootstrap_results),
            "confidence_interval_95": [
                np.percentile(bootstrap_results, 2.5),
                np.percentile(bootstrap_results, 97.5)
            ],
            "accuracy_distribution": bootstrap_results
        }
