#!/usr/bin/env python3
"""
性能监控配置脚本

配置和启动完整的性能监控体系
"""

import asyncio
import logging
import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.append('src')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PerformanceMonitoringSetup:
    """性能监控配置器"""
    
    def __init__(self):
        self.performance_monitor = None
        self.monitoring_tasks = []
        self.setup_complete = False
    
    async def setup_performance_monitoring(self):
        """设置性能监控"""
        logger.info("🔧 开始配置性能监控系统...")
        
        try:
            # 1. 初始化性能监控器
            await self._initialize_performance_monitor()
            
            # 2. 配置监控规则
            await self._configure_monitoring_rules()
            
            # 3. 启动监控任务
            await self._start_monitoring_tasks()
            
            # 4. 验证监控系统
            await self._verify_monitoring_system()
            
            self.setup_complete = True
            logger.info("✅ 性能监控系统配置完成")
            
        except Exception as e:
            logger.error(f"❌ 性能监控系统配置失败: {e}")
            raise
    
    async def _initialize_performance_monitor(self):
        """初始化性能监控器"""
        logger.info("初始化性能监控器...")
        
        try:
            from monitoring.performance_monitor import get_performance_monitor
            self.performance_monitor = get_performance_monitor()
            
            # 启用监控
            self.performance_monitor.enable_monitoring()
            
            logger.info("✅ 性能监控器初始化成功")
            
        except ImportError as e:
            logger.error(f"❌ 无法导入性能监控器: {e}")
            raise
        except Exception as e:
            logger.error(f"❌ 性能监控器初始化失败: {e}")
            raise
    
    async def _configure_monitoring_rules(self):
        """配置监控规则"""
        logger.info("配置监控规则...")
        
        try:
            from monitoring.performance_monitor import AlertRule
            
            # 添加自定义告警规则
            custom_rules = [
                AlertRule("api_response_time", 5000, ">", 120, "critical"),
                AlertRule("database_query_time", 3000, ">", 60, "high"),
                AlertRule("websocket_latency", 500, ">", 60, "medium"),
                AlertRule("memory_usage", 95, ">", 180, "critical"),
                AlertRule("error_rate", 0.05, ">", 30, "high"),
                AlertRule("connection_count", 200, ">", 120, "high")
            ]
            
            for rule in custom_rules:
                self.performance_monitor.add_alert_rule(rule)
            
            logger.info(f"✅ 添加了 {len(custom_rules)} 个自定义告警规则")
            
        except Exception as e:
            logger.error(f"❌ 配置监控规则失败: {e}")
            raise
    
    async def _start_monitoring_tasks(self):
        """启动监控任务"""
        logger.info("启动监控任务...")
        
        try:
            # 启动系统资源监控任务
            system_monitor_task = asyncio.create_task(self._system_resource_monitor())
            self.monitoring_tasks.append(system_monitor_task)
            
            # 启动API性能监控任务
            api_monitor_task = asyncio.create_task(self._api_performance_monitor())
            self.monitoring_tasks.append(api_monitor_task)
            
            # 启动数据库性能监控任务
            db_monitor_task = asyncio.create_task(self._database_performance_monitor())
            self.monitoring_tasks.append(db_monitor_task)
            
            logger.info(f"✅ 启动了 {len(self.monitoring_tasks)} 个监控任务")
            
        except Exception as e:
            logger.error(f"❌ 启动监控任务失败: {e}")
            raise
    
    async def _system_resource_monitor(self):
        """系统资源监控任务"""
        logger.info("启动系统资源监控...")
        
        while True:
            try:
                # 尝试获取系统资源信息
                try:
                    import psutil
                    
                    # 内存使用率
                    memory = psutil.virtual_memory()
                    self.performance_monitor.record_system_resource("memory", memory.percent)
                    
                    # CPU使用率
                    cpu_percent = psutil.cpu_percent(interval=1)
                    self.performance_monitor.record_system_resource("cpu", cpu_percent)
                    
                    # 磁盘使用率
                    disk = psutil.disk_usage('.')
                    disk_percent = (disk.used / disk.total) * 100
                    self.performance_monitor.record_system_resource("disk", disk_percent)
                    
                except ImportError:
                    # 如果psutil不可用，使用简化的监控
                    import shutil
                    
                    # 磁盘空间
                    total, used, free = shutil.disk_usage(".")
                    disk_percent = (used / total) * 100
                    self.performance_monitor.record_system_resource("disk", disk_percent)
                
                # 每30秒监控一次
                await asyncio.sleep(30)
                
            except Exception as e:
                logger.warning(f"系统资源监控错误: {e}")
                await asyncio.sleep(60)  # 错误时延长间隔
    
    async def _api_performance_monitor(self):
        """API性能监控任务"""
        logger.info("启动API性能监控...")
        
        # 尝试导入requests
        try:
            import requests
        except ImportError:
            logger.warning("requests库未安装，跳过API性能监控")
            return
        
        api_endpoints = [
            "/api/v1/health/",
            "/api/v1/health/ping",
            "/api/v1/data/basic-stats"
        ]
        
        session = requests.Session()
        session.timeout = 10
        
        while True:
            try:
                for endpoint in api_endpoints:
                    try:
                        start_time = time.time()
                        response = session.get(f"http://127.0.0.1:8000{endpoint}")
                        response_time = (time.time() - start_time) * 1000
                        
                        self.performance_monitor.record_api_response_time(
                            endpoint, response_time, response.status_code
                        )
                        
                    except Exception as e:
                        logger.debug(f"API监控请求失败 {endpoint}: {e}")
                
                # 每60秒监控一次
                await asyncio.sleep(60)
                
            except Exception as e:
                logger.warning(f"API性能监控错误: {e}")
                await asyncio.sleep(120)  # 错误时延长间隔
    
    async def _database_performance_monitor(self):
        """数据库性能监控任务"""
        logger.info("启动数据库性能监控...")
        
        while True:
            try:
                # 模拟数据库查询监控
                start_time = time.time()
                
                # 这里可以添加实际的数据库查询
                # 目前使用模拟数据
                await asyncio.sleep(0.1)  # 模拟查询时间
                
                query_time = (time.time() - start_time) * 1000
                self.performance_monitor.record_database_query_time(
                    "health_check", query_time, "system"
                )
                
                # 每120秒监控一次
                await asyncio.sleep(120)
                
            except Exception as e:
                logger.warning(f"数据库性能监控错误: {e}")
                await asyncio.sleep(180)  # 错误时延长间隔
    
    async def _verify_monitoring_system(self):
        """验证监控系统"""
        logger.info("验证监控系统...")
        
        try:
            # 检查监控器状态
            status = self.performance_monitor.get_monitoring_status()
            logger.info(f"监控状态: {status}")
            
            # 记录一些测试指标
            self.performance_monitor.record_metric("test_metric", 100, "ms", "test")
            
            # 获取仪表板数据
            dashboard_data = self.performance_monitor.get_performance_dashboard_data()
            logger.info(f"仪表板数据获取成功: {len(dashboard_data)} 个字段")
            
            logger.info("✅ 监控系统验证通过")
            
        except Exception as e:
            logger.error(f"❌ 监控系统验证失败: {e}")
            raise
    
    async def run_monitoring_demo(self):
        """运行监控演示"""
        logger.info("🎯 运行性能监控演示...")
        
        try:
            # 模拟一些性能数据
            for i in range(10):
                # API响应时间
                self.performance_monitor.record_api_response_time(
                    "/api/test", 100 + i * 50, 200
                )
                
                # 数据库查询时间
                self.performance_monitor.record_database_query_time(
                    "SELECT", 50 + i * 20, "lottery_data"
                )
                
                # WebSocket延迟
                self.performance_monitor.record_websocket_latency(
                    f"conn_{i}", 20 + i * 5, "ping"
                )
                
                await asyncio.sleep(1)
            
            # 获取并显示监控数据
            summary = self.performance_monitor.get_metrics_summary(time_range_minutes=5)
            logger.info(f"监控数据摘要: {summary}")
            
            alerts = self.performance_monitor.get_active_alerts()
            logger.info(f"活跃告警数量: {len(alerts)}")
            
            logger.info("✅ 监控演示完成")
            
        except Exception as e:
            logger.error(f"❌ 监控演示失败: {e}")
    
    async def stop_monitoring(self):
        """停止监控"""
        logger.info("停止性能监控...")
        
        try:
            # 取消所有监控任务
            for task in self.monitoring_tasks:
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
            
            # 禁用监控器
            if self.performance_monitor:
                self.performance_monitor.disable_monitoring()
            
            logger.info("✅ 性能监控已停止")
            
        except Exception as e:
            logger.error(f"❌ 停止监控失败: {e}")
    
    def get_monitoring_status(self):
        """获取监控状态"""
        if not self.performance_monitor:
            return {"status": "not_initialized"}
        
        return {
            "setup_complete": self.setup_complete,
            "monitoring_tasks": len(self.monitoring_tasks),
            "monitor_status": self.performance_monitor.get_monitoring_status()
        }

async def main():
    """主函数"""
    setup = PerformanceMonitoringSetup()
    
    try:
        # 配置性能监控
        await setup.setup_performance_monitoring()
        
        # 运行演示
        await setup.run_monitoring_demo()
        
        # 显示状态
        status = setup.get_monitoring_status()
        logger.info(f"最终状态: {status}")
        
        logger.info("🎉 性能监控配置完成！")
        logger.info("监控系统现在正在后台运行...")
        logger.info("可以通过 /api/v1/health/performance/dashboard 查看监控数据")
        
        # 保持运行一段时间以演示监控
        logger.info("运行监控系统60秒...")
        await asyncio.sleep(60)
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在停止...")
    except Exception as e:
        logger.error(f"配置失败: {e}")
        return 1
    finally:
        await setup.stop_monitoring()
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
