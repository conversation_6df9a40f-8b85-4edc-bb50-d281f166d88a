{"model_params": {"transition_window_size": 1000, "probability_window_size": 500, "smoothing_alpha": 1.0}, "validation_params": {"k_folds": 2, "data_limit": 300}, "fold_results": [{"fold_idx": 0, "train_size": 150, "val_size": 150, "predictions_count": 150, "accuracy_metrics": {"exact_match": 0.0, "digit_accuracy": 0.23555555555555555, "position_accuracy": [0.09333333333333334, 0.1, 0.10666666666666667], "total_predictions": 150}, "diversity_metrics": {"simpson_diversity": 0.9886222222222223, "unique_ratio": 0.76, "entropy": 6.672924837260843, "unique_count": 114, "total_count": 150}, "stability_metrics": {"variance": 94954.10848888889, "std_dev": 308.14624529416045, "coefficient_of_variation": 0.6496681067947218, "mean": 474.31333333333333}, "aic_bic": {"aic": 7127.7552789821375, "bic": 7458.925161332725, "log_likelihood": -3453.8776394910687, "num_params": 110, "num_samples": 150}}], "overall_results": {"accuracy_metrics": {"exact_match": 0.0, "digit_accuracy": 0.23555555555555555, "position_accuracy": [0.09333333333333334, 0.1, 0.10666666666666667], "total_predictions": 150}, "diversity_metrics": {"simpson_diversity": 0.9886222222222223, "unique_ratio": 0.76, "entropy": 6.672924837260843, "unique_count": 114, "total_count": 150}, "stability_metrics": {"variance": 94954.10848888889, "std_dev": 308.14624529416045, "coefficient_of_variation": 0.6496681067947218, "mean": 474.31333333333333}, "aic_bic": {"aic": 7127.7552789821375, "bic": 7458.925161332725, "log_likelihood": -3453.8776394910687, "num_params": 110, "num_samples": 150}, "total_predictions": 150}, "metadata": {"generated_at": "2025-07-17T16:52:24.193103", "validator_version": "1.0", "database_path": "data\\lottery.db"}}