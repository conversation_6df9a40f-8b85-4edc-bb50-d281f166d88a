#!/usr/bin/env python3
"""
验证修复状态
"""

import sys
import os
sys.path.append('src')

def check_intelligent_fusion_fix():
    """检查智能融合系统修复状态"""
    print("🔍 检查智能融合系统修复状态")
    
    try:
        # 检查文件是否存在修复
        with open('src/prediction/intelligent_fusion.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查硬编码是否已修复
        if 'self.training_data_count = 8343' in content:
            print("❌ 硬编码问题未修复")
            return False
        elif 'self.training_data_count = self._get_current_data_count()' in content:
            print("✅ 硬编码问题已修复")
        else:
            print("⚠️  无法确认硬编码修复状态")
        
        # 检查动态数据计数方法是否存在
        if '_get_current_data_count' in content:
            print("✅ 动态数据计数方法已添加")
        else:
            print("❌ 动态数据计数方法未添加")
            return False
        
        # 检查数据变化检测方法是否存在
        if '_check_data_changed' in content:
            print("✅ 数据变化检测方法已添加")
        else:
            print("❌ 数据变化检测方法未添加")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_data_update_service_fix():
    """检查数据更新服务修复状态"""
    print("\n🔍 检查数据更新服务修复状态")
    
    try:
        with open('src/services/data_update_service.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查自动重训练逻辑是否存在
        if '_trigger_model_retrain' in content:
            print("✅ 自动重训练方法已添加")
        else:
            print("❌ 自动重训练方法未添加")
            return False
        
        # 检查重训练触发逻辑是否存在
        if 'records_added > 0' in content and 'trigger_model_retrain' in content:
            print("✅ 自动重训练触发逻辑已添加")
        else:
            print("❌ 自动重训练触发逻辑未添加")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_api_fix():
    """检查API修复状态"""
    print("\n🔍 检查API修复状态")
    
    try:
        with open('src/api/production_main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查auto_train逻辑是否完善
        if '_check_data_changed' in content and 'training_needed' in content:
            print("✅ API自动训练逻辑已完善")
        else:
            print("❌ API自动训练逻辑未完善")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_ui_fix():
    """检查UI修复状态"""
    print("\n🔍 检查UI修复状态")
    
    try:
        with open('src/ui/main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查重训练功能是否存在
        if 'show_training_status_and_controls' in content:
            print("✅ UI重训练功能已添加")
        else:
            print("❌ UI重训练功能未添加")
            return False
        
        # 检查重训练按钮是否存在
        if '重新训练' in content and 'intelligent-fusion/train' in content:
            print("✅ 重训练按钮已添加")
        else:
            print("❌ 重训练按钮未添加")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 验证修复状态")
    print("=" * 50)
    
    # 检查各个组件的修复状态
    fusion_fix = check_intelligent_fusion_fix()
    service_fix = check_data_update_service_fix()
    api_fix = check_api_fix()
    ui_fix = check_ui_fix()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 修复状态总结")
    print("=" * 50)
    
    fixes = [
        ("智能融合系统", fusion_fix),
        ("数据更新服务", service_fix),
        ("API接口", api_fix),
        ("UI界面", ui_fix)
    ]
    
    success_count = sum(1 for _, status in fixes if status)
    total_count = len(fixes)
    
    for name, status in fixes:
        status_text = "✅ 已修复" if status else "❌ 未修复"
        print(f"{name}: {status_text}")
    
    print(f"\n修复进度: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 所有修复都已完成！")
        print("\n📋 修复内容总结：")
        print("1. ✅ 修复了智能融合系统的硬编码问题")
        print("2. ✅ 添加了动态数据计数功能")
        print("3. ✅ 实现了数据变化检测机制")
        print("4. ✅ 增强了数据更新后自动重训练")
        print("5. ✅ 完善了API自动训练逻辑")
        print("6. ✅ 添加了UI手动重训练功能")
        
        print("\n🎯 现在可以：")
        print("- 重启API服务测试修复效果")
        print("- 在Streamlit界面使用重训练功能")
        print("- 验证预测结果不再固定为056")
    else:
        print("⚠️  部分修复未完成，请检查相关文件")

if __name__ == "__main__":
    main()
