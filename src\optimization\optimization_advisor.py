#!/usr/bin/env python3
"""
优化建议生成器
Optimization Advisor

基于分析结果生成具体的优化建议
"""

import logging
import os
import sys
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple

sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from core.unified_prediction_storage import PredictionRecord


class OptimizationPriority(Enum):
    """优化优先级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class OptimizationComplexity(Enum):
    """实施复杂度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


@dataclass
class OptimizationSuggestion:
    """优化建议"""
    strategy: str
    description: str
    specific_actions: List[str]
    expected_improvement: str
    implementation_effort: OptimizationComplexity
    priority: OptimizationPriority
    estimated_impact: float  # 0-1
    risk_level: str
    prerequisites: List[str]
    success_metrics: List[str]


@dataclass
class OptimizationResult:
    """优化建议结果"""
    model_name: str
    priority_level: OptimizationPriority
    optimization_strategies: List[OptimizationSuggestion]
    expected_improvements: Dict[str, float]
    implementation_roadmap: List[Dict[str, Any]]
    risk_assessment: Dict[str, Any]
    confidence_score: float
    analysis_timestamp: datetime


class OptimizationAdvisor:
    """优化建议生成器"""
    
    def __init__(self):
        """初始化优化建议生成器"""
        self.logger = logging.getLogger(__name__)
        
        # 优化策略配置
        self.optimization_strategies = {
            'parameter_tuning': self._generate_parameter_tuning_suggestions,
            'feature_engineering': self._generate_feature_engineering_suggestions,
            'model_architecture': self._generate_model_architecture_suggestions,
            'ensemble_optimization': self._generate_ensemble_optimization_suggestions,
            'data_preprocessing': self._generate_data_preprocessing_suggestions
        }
        
        # 影响评估权重
        self.impact_weights = {
            'accuracy_improvement': 0.4,
            'confidence_calibration': 0.25,
            'stability_enhancement': 0.2,
            'efficiency_gain': 0.15
        }
    
    def generate_suggestions(self, model_name: str, analysis_results: Dict[str, Any]) -> OptimizationResult:
        """
        生成优化建议
        
        Args:
            model_name: 模型名称
            analysis_results: 分析结果
            
        Returns:
            优化建议结果
        """
        try:
            self.logger.info(f"为模型 {model_name} 生成优化建议")
            
            # 评估优化优先级
            priority_level = self._assess_optimization_priority(analysis_results)
            
            # 生成各类优化建议
            all_suggestions = []
            for strategy_name, strategy_func in self.optimization_strategies.items():
                try:
                    suggestions = strategy_func(analysis_results)
                    all_suggestions.extend(suggestions)
                except Exception as e:
                    self.logger.error(f"策略 {strategy_name} 生成建议失败: {e}")
            
            # 按优先级和影响排序
            all_suggestions.sort(key=lambda x: (x.priority.value, -x.estimated_impact))
            
            # 计算预期改进
            expected_improvements = self._calculate_expected_improvements(all_suggestions)
            
            # 生成实施路线图
            implementation_roadmap = self._generate_implementation_roadmap(all_suggestions)
            
            # 风险评估
            risk_assessment = self._assess_implementation_risks(all_suggestions)
            
            # 计算置信度
            confidence_score = self._calculate_suggestion_confidence(analysis_results, all_suggestions)
            
            return OptimizationResult(
                model_name=model_name,
                priority_level=priority_level,
                optimization_strategies=all_suggestions,
                expected_improvements=expected_improvements,
                implementation_roadmap=implementation_roadmap,
                risk_assessment=risk_assessment,
                confidence_score=confidence_score,
                analysis_timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"生成优化建议失败: {e}")
            raise
    
    def _assess_optimization_priority(self, analysis_results: Dict[str, Any]) -> OptimizationPriority:
        """评估优化优先级"""
        priority_score = 0.0
        
        # 基于偏差分析评估
        if 'deviation_analysis' in analysis_results:
            deviation = analysis_results['deviation_analysis']
            if isinstance(deviation, dict) and 'overall_score' in deviation:
                if deviation['overall_score'] < 0.3:
                    priority_score += 0.4
                elif deviation['overall_score'] < 0.6:
                    priority_score += 0.2
        
        # 基于弱点识别评估
        if 'weakness_identification' in analysis_results:
            weaknesses = analysis_results['weakness_identification']
            if isinstance(weaknesses, dict) and 'identified_weaknesses' in weaknesses:
                weakness_count = len(weaknesses['identified_weaknesses'])
                priority_score += min(weakness_count * 0.15, 0.6)
        
        # 基于成功因子评估
        if 'success_factors' in analysis_results:
            factors = analysis_results['success_factors']
            if isinstance(factors, dict) and 'confidence_score' in factors:
                if factors['confidence_score'] < 0.5:
                    priority_score += 0.3
        
        # 转换为优先级枚举
        if priority_score >= 0.8:
            return OptimizationPriority.CRITICAL
        elif priority_score >= 0.6:
            return OptimizationPriority.HIGH
        elif priority_score >= 0.3:
            return OptimizationPriority.MEDIUM
        else:
            return OptimizationPriority.LOW
    
    def _generate_parameter_tuning_suggestions(self, analysis_results: Dict[str, Any]) -> List[OptimizationSuggestion]:
        """生成参数调优建议"""
        suggestions = []
        
        # 基于偏差分析的参数调优
        if 'deviation_analysis' in analysis_results:
            deviation = analysis_results['deviation_analysis']
            
            # 和值偏差调优
            if isinstance(deviation, dict) and 'numerical_deviation' in deviation:
                numerical = deviation['numerical_deviation']
                if isinstance(numerical, dict) and 'sum_deviation' in numerical:
                    sum_dev = numerical['sum_deviation']
                    if isinstance(sum_dev, dict) and sum_dev.get('absolute_deviation', 0) > 3:
                        suggestions.append(OptimizationSuggestion(
                            strategy='sum_value_enhancement',
                            description='增强和值预测能力',
                            specific_actions=[
                                '调整和值特征权重参数',
                                '优化和值分布建模参数',
                                '引入和值趋势预测参数',
                                '实施和值范围约束参数'
                            ],
                            expected_improvement='和值预测准确率提升15-25%',
                            implementation_effort=OptimizationComplexity.MEDIUM,
                            priority=OptimizationPriority.HIGH,
                            estimated_impact=0.7,
                            risk_level='medium',
                            prerequisites=['历史和值数据分析', '特征工程完成'],
                            success_metrics=['和值偏差减少50%', '整体准确率提升10%']
                        ))
        
        # 基于弱点识别的参数调优
        if 'weakness_identification' in analysis_results:
            weaknesses = analysis_results['weakness_identification']
            if isinstance(weaknesses, dict) and 'identified_weaknesses' in weaknesses:
                weakness_dict = weaknesses['identified_weaknesses']
                
                # 过拟合问题
                if 'overfitting' in weakness_dict:
                    suggestions.append(OptimizationSuggestion(
                        strategy='overfitting_mitigation',
                        description='缓解过拟合问题',
                        specific_actions=[
                            '增加L1/L2正则化强度',
                            '调整Dropout比率',
                            '减少模型复杂度参数',
                            '实施早停策略参数'
                        ],
                        expected_improvement='泛化能力提升20-30%',
                        implementation_effort=OptimizationComplexity.MEDIUM,
                        priority=OptimizationPriority.HIGH,
                        estimated_impact=0.8,
                        risk_level='low',
                        prerequisites=['交叉验证设置', '验证集准备'],
                        success_metrics=['验证集准确率提升', '训练验证差距缩小']
                    ))
                
                # 置信度校准问题
                if 'confidence_miscalibration' in weakness_dict:
                    suggestions.append(OptimizationSuggestion(
                        strategy='confidence_calibration',
                        description='改善置信度校准',
                        specific_actions=[
                            '实施Platt缩放参数调整',
                            '使用温度缩放技术',
                            '调整置信度计算权重',
                            '优化不确定性量化参数'
                        ],
                        expected_improvement='置信度校准误差减少40-60%',
                        implementation_effort=OptimizationComplexity.LOW,
                        priority=OptimizationPriority.MEDIUM,
                        estimated_impact=0.6,
                        risk_level='low',
                        prerequisites=['置信度历史数据', '校准数据集'],
                        success_metrics=['校准误差<0.1', '置信度-准确率相关性>0.8']
                    ))
        
        return suggestions
    
    def _generate_feature_engineering_suggestions(self, analysis_results: Dict[str, Any]) -> List[OptimizationSuggestion]:
        """生成特征工程建议"""
        suggestions = []
        
        # 基于成功因子的特征工程
        if 'success_factors' in analysis_results:
            factors = analysis_results['success_factors']
            if isinstance(factors, dict) and 'top_factors' in factors:
                top_factors = factors['top_factors']
                if isinstance(top_factors, list) and len(top_factors) > 0:
                    suggestions.append(OptimizationSuggestion(
                        strategy='success_factor_amplification',
                        description='放大成功因子影响',
                        specific_actions=[
                            f'增强 {factor.get("factor_name", "关键因子")} 特征权重' 
                            for factor in top_factors[:3] if isinstance(factor, dict)
                        ] + [
                            '创建成功因子组合特征',
                            '实施因子交互特征工程',
                            '优化因子特征预处理'
                        ],
                        expected_improvement='基于成功模式的准确率提升10-20%',
                        implementation_effort=OptimizationComplexity.MEDIUM,
                        priority=OptimizationPriority.MEDIUM,
                        estimated_impact=0.6,
                        risk_level='medium',
                        prerequisites=['成功因子分析完成', '特征重要性评估'],
                        success_metrics=['成功因子覆盖率>80%', '因子相关准确率提升']
                    ))
        
        # 基于模式识别的特征工程
        suggestions.append(OptimizationSuggestion(
            strategy='pattern_feature_enhancement',
            description='增强模式识别特征',
            specific_actions=[
                '添加数字序列模式特征',
                '创建奇偶分布特征',
                '引入大小比例特征',
                '实施重复模式检测特征',
                '开发跨度分布特征'
            ],
            expected_improvement='模式识别准确率提升15-25%',
            implementation_effort=OptimizationComplexity.HIGH,
            priority=OptimizationPriority.MEDIUM,
            estimated_impact=0.7,
            risk_level='medium',
            prerequisites=['模式分析完成', '历史模式数据'],
            success_metrics=['模式识别准确率>70%', '特征重要性排名提升']
        ))
        
        return suggestions
    
    def _generate_model_architecture_suggestions(self, analysis_results: Dict[str, Any]) -> List[OptimizationSuggestion]:
        """生成模型架构建议"""
        suggestions = []
        
        # 基于弱点的架构优化
        if 'weakness_identification' in analysis_results:
            weaknesses = analysis_results['weakness_identification']
            if isinstance(weaknesses, dict) and 'identified_weaknesses' in weaknesses:
                weakness_dict = weaknesses['identified_weaknesses']
                
                # 模式盲点问题
                if any('pattern_blindness' in w for w in weakness_dict.keys()):
                    suggestions.append(OptimizationSuggestion(
                        strategy='pattern_recognition_enhancement',
                        description='增强模式识别架构',
                        specific_actions=[
                            '引入注意力机制模块',
                            '添加卷积神经网络层',
                            '实施多尺度特征提取',
                            '使用残差连接结构',
                            '集成序列建模组件'
                        ],
                        expected_improvement='模式识别能力提升25-40%',
                        implementation_effort=OptimizationComplexity.HIGH,
                        priority=OptimizationPriority.HIGH,
                        estimated_impact=0.8,
                        risk_level='high',
                        prerequisites=['深度学习框架', '计算资源充足'],
                        success_metrics=['模式识别准确率>80%', '复杂模式处理能力提升']
                    ))
        
        return suggestions
    
    def _generate_ensemble_optimization_suggestions(self, analysis_results: Dict[str, Any]) -> List[OptimizationSuggestion]:
        """生成集成优化建议"""
        suggestions = []
        
        # 多样性集成建议
        suggestions.append(OptimizationSuggestion(
            strategy='diversity_ensemble',
            description='优化模型集成多样性',
            specific_actions=[
                '实施多样性权重调整',
                '引入模型选择机制',
                '优化投票策略',
                '实施动态权重分配',
                '添加模型性能监控'
            ],
            expected_improvement='集成准确率提升10-15%',
            implementation_effort=OptimizationComplexity.MEDIUM,
            priority=OptimizationPriority.MEDIUM,
            estimated_impact=0.5,
            risk_level='low',
            prerequisites=['多个基础模型', '性能评估体系'],
            success_metrics=['集成准确率>单模型', '预测一致性提升']
        ))
        
        return suggestions
    
    def _generate_data_preprocessing_suggestions(self, analysis_results: Dict[str, Any]) -> List[OptimizationSuggestion]:
        """生成数据预处理建议"""
        suggestions = []
        
        # 数据质量优化
        suggestions.append(OptimizationSuggestion(
            strategy='data_quality_enhancement',
            description='提升数据质量',
            specific_actions=[
                '实施数据清洗流程',
                '添加异常值检测',
                '优化特征标准化',
                '实施数据增强技术',
                '建立数据质量监控'
            ],
            expected_improvement='数据质量提升，准确率间接提升5-10%',
            implementation_effort=OptimizationComplexity.LOW,
            priority=OptimizationPriority.LOW,
            estimated_impact=0.3,
            risk_level='low',
            prerequisites=['数据质量评估', '清洗规则定义'],
            success_metrics=['数据质量分数>0.9', '异常数据比例<5%']
        ))
        
        return suggestions
    
    def _calculate_expected_improvements(self, suggestions: List[OptimizationSuggestion]) -> Dict[str, float]:
        """计算预期改进"""
        improvements = {
            'accuracy_improvement': 0.0,
            'confidence_calibration': 0.0,
            'stability_enhancement': 0.0,
            'efficiency_gain': 0.0
        }
        
        for suggestion in suggestions:
            # 根据建议类型分配改进
            if 'accuracy' in suggestion.expected_improvement.lower():
                improvements['accuracy_improvement'] += suggestion.estimated_impact * 0.1
            if 'confidence' in suggestion.expected_improvement.lower() or 'calibration' in suggestion.expected_improvement.lower():
                improvements['confidence_calibration'] += suggestion.estimated_impact * 0.1
            if 'stability' in suggestion.expected_improvement.lower() or 'generalization' in suggestion.expected_improvement.lower():
                improvements['stability_enhancement'] += suggestion.estimated_impact * 0.1
            if 'efficiency' in suggestion.expected_improvement.lower() or 'speed' in suggestion.expected_improvement.lower():
                improvements['efficiency_gain'] += suggestion.estimated_impact * 0.1
        
        # 限制改进幅度
        for key in improvements:
            improvements[key] = min(improvements[key], 0.5)  # 最大50%改进
        
        return improvements
    
    def _generate_implementation_roadmap(self, suggestions: List[OptimizationSuggestion]) -> List[Dict[str, Any]]:
        """生成实施路线图"""
        roadmap = []
        
        # 按优先级和复杂度分组
        high_priority = [s for s in suggestions if s.priority in [OptimizationPriority.CRITICAL, OptimizationPriority.HIGH]]
        medium_priority = [s for s in suggestions if s.priority == OptimizationPriority.MEDIUM]
        low_priority = [s for s in suggestions if s.priority == OptimizationPriority.LOW]
        
        # 第一阶段：高优先级，低复杂度
        phase1 = [s for s in high_priority if s.implementation_effort == OptimizationComplexity.LOW]
        if phase1:
            roadmap.append({
                'phase': 1,
                'name': '快速优化阶段',
                'duration': '1-2周',
                'suggestions': [s.strategy for s in phase1],
                'expected_impact': sum(s.estimated_impact for s in phase1) / len(phase1),
                'risk_level': 'low'
            })
        
        # 第二阶段：高优先级，中等复杂度
        phase2 = [s for s in high_priority if s.implementation_effort == OptimizationComplexity.MEDIUM]
        if phase2:
            roadmap.append({
                'phase': 2,
                'name': '核心优化阶段',
                'duration': '2-4周',
                'suggestions': [s.strategy for s in phase2],
                'expected_impact': sum(s.estimated_impact for s in phase2) / len(phase2),
                'risk_level': 'medium'
            })
        
        # 第三阶段：中等优先级
        if medium_priority:
            roadmap.append({
                'phase': 3,
                'name': '增强优化阶段',
                'duration': '3-6周',
                'suggestions': [s.strategy for s in medium_priority],
                'expected_impact': sum(s.estimated_impact for s in medium_priority) / len(medium_priority),
                'risk_level': 'medium'
            })
        
        return roadmap
    
    def _assess_implementation_risks(self, suggestions: List[OptimizationSuggestion]) -> Dict[str, Any]:
        """评估实施风险"""
        risk_assessment = {
            'overall_risk': 'low',
            'high_risk_suggestions': [],
            'risk_factors': [],
            'mitigation_strategies': []
        }
        
        high_risk_count = sum(1 for s in suggestions if s.risk_level == 'high')
        medium_risk_count = sum(1 for s in suggestions if s.risk_level == 'medium')
        
        # 评估整体风险
        if high_risk_count > 2:
            risk_assessment['overall_risk'] = 'high'
        elif high_risk_count > 0 or medium_risk_count > 3:
            risk_assessment['overall_risk'] = 'medium'
        
        # 识别高风险建议
        risk_assessment['high_risk_suggestions'] = [
            s.strategy for s in suggestions if s.risk_level == 'high'
        ]
        
        # 风险因子
        if high_risk_count > 0:
            risk_assessment['risk_factors'].append('包含高复杂度架构变更')
        if any(s.implementation_effort == OptimizationComplexity.HIGH for s in suggestions):
            risk_assessment['risk_factors'].append('需要大量开发资源')
        
        # 缓解策略
        risk_assessment['mitigation_strategies'] = [
            '分阶段实施，降低单次变更风险',
            '建立完善的测试和回滚机制',
            '实施A/B测试验证效果',
            '保持详细的变更日志'
        ]
        
        return risk_assessment
    
    def _calculate_suggestion_confidence(self, analysis_results: Dict[str, Any], 
                                       suggestions: List[OptimizationSuggestion]) -> float:
        """计算建议置信度"""
        # 基于分析结果的完整性
        analysis_completeness = len([r for r in analysis_results.values() if isinstance(r, dict) and 'error' not in r]) / len(analysis_results)
        
        # 基于建议的一致性
        suggestion_consistency = 1.0 - (len(set(s.priority for s in suggestions)) - 1) * 0.2
        
        # 基于预期影响的合理性
        avg_impact = sum(s.estimated_impact for s in suggestions) / len(suggestions) if suggestions else 0
        impact_reasonableness = 1.0 - abs(avg_impact - 0.5)  # 期望影响在0.5左右最合理
        
        # 综合置信度
        confidence = (analysis_completeness * 0.4 + suggestion_consistency * 0.3 + impact_reasonableness * 0.3)
        return max(0.0, min(1.0, confidence))


if __name__ == "__main__":
    # 测试代码
    advisor = OptimizationAdvisor()
    
    # 模拟分析结果
    test_analysis = {
        'deviation_analysis': {
            'overall_score': 0.4,
            'numerical_deviation': {
                'sum_deviation': {'absolute_deviation': 5}
            }
        },
        'weakness_identification': {
            'identified_weaknesses': {
                'overfitting': {'severity_score': 0.7},
                'confidence_miscalibration': {'severity_score': 0.5}
            }
        },
        'success_factors': {
            'top_factors': [
                {'factor_name': 'high_confidence', 'importance_score': 0.8}
            ],
            'confidence_score': 0.6
        }
    }
    
    # 生成建议
    result = advisor.generate_suggestions("test_model", test_analysis)
    
    print("优化建议生成结果:")
    print(f"优先级: {result.priority_level.value}")
    print(f"建议数量: {len(result.optimization_strategies)}")
    print(f"置信度: {result.confidence_score:.3f}")
    
    for i, suggestion in enumerate(result.optimization_strategies[:3], 1):
        print(f"\n建议 {i}: {suggestion.strategy}")
        print(f"  描述: {suggestion.description}")
        print(f"  优先级: {suggestion.priority.value}")
        print(f"  预期影响: {suggestion.estimated_impact:.2f}")
