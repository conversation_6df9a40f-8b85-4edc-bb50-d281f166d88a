{"timestamp": "2025-07-26T02:21:15.290977", "total_tests": 11, "passed_tests": 5, "success_rate": 45.45454545454545, "detailed_results": {"api_availability": {"/health": {"status": 200, "success": true, "response_time": 0.009306}, "/api/v1/health/websocket": {"status": 404, "success": false, "response_time": 0.001625}, "/api/v1/health/websocket/connections": {"status": 404, "success": false, "response_time": 0.011541}, "/api/v1/health/websocket/performance": {"status": 404, "success": false, "response_time": 0.001531}, "/api/v1/stats/basic": {"status": 200, "success": true, "response_time": 0.014797}, "/api/v1/bug-detection/statistics": {"status": 200, "success": true, "response_time": 0.013602}}, "websocket_endpoints": {"/ws/bug-detection": {"success": false, "status": "❌ 连接失败", "error": "BaseEventLoop.create_connection() got an unexpected keyword argument 'timeout'"}, "/ws/realtime-stats": {"success": false, "status": "❌ 连接失败", "error": "BaseEventLoop.create_connection() got an unexpected keyword argument 'timeout'"}}, "realtime_data": {"bug_stats": {"success": true, "data": {"status": "success", "statistics": {"bugs": {"total": 0, "by_severity": {}, "by_status": {}}, "performance": {"endpoints_monitored": 2, "summary": {"/api/v1/stats/basic": {"avg_time": 0.010896768420934677, "max_time": 0.058533430099487305, "count": 64}, "/health": {"avg_time": 0.004497752701940616, "max_time": 0.03599810600280762, "count": 121}}}}, "timestamp": "2025-07-26T02:21:15.227978"}}}, "bug_detection": {"js_error_reporting": {"success": false, "error": "HTTP 422"}}, "fallback_mechanisms": {"api_polling": {"success": true, "note": "API可以作为WebSocket的降级方案"}}}}