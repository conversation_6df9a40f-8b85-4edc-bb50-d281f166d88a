"""
高级特征工程模块
实现小波变换、分形分析、混沌特征、相位同步等高级数学特征
基于参考文档的成功经验实现
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

try:
    from scipy import signal
    from scipy.fft import fft, fftfreq
    from scipy.stats import entropy
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    print("Warning: scipy not available, some features will be disabled")

try:
    import nolds
    NOLDS_AVAILABLE = True
except ImportError:
    NOLDS_AVAILABLE = False
    print("Warning: nolds not available, fractal analysis will be disabled")


class AdvancedFeatureExtractor:
    """
    高级特征提取器
    基于参考文档的成功经验实现高级数学特征
    """
    
    def __init__(self):
        self.wavelet_types = ['db4', 'db8', 'haar', 'coif2']
        self.window_sizes = [10, 20, 30, 50]
        
    def extract_all_features(self, data: List[str], max_records: int = 1000) -> Dict[str, Any]:
        """
        提取所有高级特征
        
        Args:
            data: 开奖号码列表，格式如['123', '456', ...]
            max_records: 最大处理记录数，避免计算过慢
            
        Returns:
            包含所有高级特征的字典
        """
        if not data:
            return self._get_default_features()
            
        # 限制数据量以提高性能
        if len(data) > max_records:
            data = data[-max_records:]
            
        features = {}
        
        try:
            # 转换为数值序列
            numeric_sequences = self._convert_to_numeric_sequences(data)
            
            # 小波变换特征
            if SCIPY_AVAILABLE:
                features.update(self._extract_wavelet_features(numeric_sequences))
            
            # 分形分析特征
            if NOLDS_AVAILABLE:
                features.update(self._extract_fractal_features(numeric_sequences))
            
            # 混沌特征
            features.update(self._extract_chaos_features(numeric_sequences))
            
            # 相位同步特征
            if SCIPY_AVAILABLE:
                features.update(self._extract_phase_sync_features(numeric_sequences))
            
            # 时间序列高级特征
            features.update(self._extract_time_series_features(numeric_sequences))
            
        except Exception as e:
            print(f"Error extracting advanced features: {e}")
            features = self._get_default_features()
            
        return features

    def _simple_cwt(self, data: np.ndarray, widths: np.ndarray) -> np.ndarray:
        """简化的连续小波变换实现"""
        try:
            # 使用简单的高斯小波近似
            cwt_matrix = []
            for width in widths:
                # 创建高斯小波
                t = np.arange(-width, width + 1)
                wavelet = np.exp(-t**2 / (2 * width**2)) * np.cos(5 * t / width)

                # 卷积
                if len(data) >= len(wavelet):
                    convolved = np.convolve(data, wavelet, mode='same')
                else:
                    convolved = np.zeros_like(data)

                cwt_matrix.append(convolved)

            return np.array(cwt_matrix)
        except:
            # 如果失败，返回零矩阵
            return np.zeros((len(widths), len(data)))

    def _convert_to_numeric_sequences(self, data: List[str]) -> Dict[str, np.ndarray]:
        """
        将开奖号码转换为多种数值序列
        """
        sequences = {}
        
        # 各位数字序列
        hundreds = []
        tens = []
        units = []
        
        # 和值序列
        sums = []
        
        # 跨度序列
        spans = []
        
        for number_str in data:
            if len(number_str) == 3:
                h, t, u = int(number_str[0]), int(number_str[1]), int(number_str[2])
                hundreds.append(h)
                tens.append(t)
                units.append(u)
                sums.append(h + t + u)
                spans.append(max(h, t, u) - min(h, t, u))
        
        sequences['hundreds'] = np.array(hundreds)
        sequences['tens'] = np.array(tens)
        sequences['units'] = np.array(units)
        sequences['sums'] = np.array(sums)
        sequences['spans'] = np.array(spans)
        
        return sequences
    
    def _extract_wavelet_features(self, sequences: Dict[str, np.ndarray]) -> Dict[str, float]:
        """
        提取小波变换特征
        使用scipy.signal实现
        """
        features = {}
        
        if not SCIPY_AVAILABLE:
            return features
            
        try:
            for seq_name, seq_data in sequences.items():
                if len(seq_data) < 10:
                    continue
                    
                # 使用简化的小波变换（兼容性处理）
                widths = np.arange(1, min(31, len(seq_data)//2))
                if len(widths) > 0:
                    # 使用简化的小波变换实现
                    cwt_matrix = self._simple_cwt(seq_data, widths)
                    
                    # 提取小波特征
                    features[f'{seq_name}_wavelet_energy'] = float(np.sum(np.abs(cwt_matrix)**2))
                    features[f'{seq_name}_wavelet_entropy'] = float(self._calculate_entropy(np.abs(cwt_matrix).flatten()))
                    features[f'{seq_name}_wavelet_max_scale'] = float(np.argmax(np.sum(np.abs(cwt_matrix), axis=1)))
                    
                    # 频域特征
                    fft_vals = fft(seq_data)
                    power_spectrum = np.abs(fft_vals)**2
                    features[f'{seq_name}_spectral_centroid'] = float(np.sum(np.arange(len(power_spectrum)) * power_spectrum) / np.sum(power_spectrum))
                    features[f'{seq_name}_spectral_rolloff'] = float(self._calculate_spectral_rolloff(power_spectrum))
                    
        except Exception as e:
            print(f"Error in wavelet feature extraction: {e}")
            
        return features
    
    def _extract_fractal_features(self, sequences: Dict[str, np.ndarray]) -> Dict[str, float]:
        """
        提取分形分析特征
        使用nolds库实现Hurst指数、盒维数等
        """
        features = {}
        
        if not NOLDS_AVAILABLE:
            return features
            
        try:
            for seq_name, seq_data in sequences.items():
                if len(seq_data) < 10:
                    continue
                    
                # Hurst指数
                try:
                    hurst = nolds.hurst_rs(seq_data)
                    features[f'{seq_name}_hurst_exponent'] = float(hurst)
                except:
                    features[f'{seq_name}_hurst_exponent'] = 0.5
                
                # 分形维数
                try:
                    dfa = nolds.dfa(seq_data)
                    features[f'{seq_name}_dfa'] = float(dfa)
                except:
                    features[f'{seq_name}_dfa'] = 1.0
                
                # 样本熵
                try:
                    sampen = nolds.sampen(seq_data)
                    features[f'{seq_name}_sample_entropy'] = float(sampen)
                except:
                    features[f'{seq_name}_sample_entropy'] = 0.0
                    
        except Exception as e:
            print(f"Error in fractal feature extraction: {e}")
            
        return features
    
    def _extract_chaos_features(self, sequences: Dict[str, np.ndarray]) -> Dict[str, float]:
        """
        提取混沌特征
        计算Lyapunov指数等混沌系统特征
        """
        features = {}
        
        try:
            for seq_name, seq_data in sequences.items():
                if len(seq_data) < 20:
                    continue
                    
                # 简化的Lyapunov指数估计
                lyapunov = self._estimate_lyapunov_exponent(seq_data)
                features[f'{seq_name}_lyapunov_exponent'] = float(lyapunov)
                
                # 关联维数估计
                correlation_dim = self._estimate_correlation_dimension(seq_data)
                features[f'{seq_name}_correlation_dimension'] = float(correlation_dim)
                
                # 递归率
                recurrence_rate = self._calculate_recurrence_rate(seq_data)
                features[f'{seq_name}_recurrence_rate'] = float(recurrence_rate)
                
        except Exception as e:
            print(f"Error in chaos feature extraction: {e}")
            
        return features
    
    def _extract_phase_sync_features(self, sequences: Dict[str, np.ndarray]) -> Dict[str, float]:
        """
        提取相位同步特征
        """
        features = {}
        
        if not SCIPY_AVAILABLE or len(sequences) < 2:
            return features
            
        try:
            seq_names = list(sequences.keys())
            
            # 计算序列间的相位同步
            for i in range(len(seq_names)):
                for j in range(i+1, len(seq_names)):
                    seq1 = sequences[seq_names[i]]
                    seq2 = sequences[seq_names[j]]
                    
                    if len(seq1) == len(seq2) and len(seq1) > 10:
                        # 相位锁定值
                        plv = self._calculate_phase_locking_value(seq1, seq2)
                        features[f'phase_sync_{seq_names[i]}_{seq_names[j]}'] = float(plv)
                        
        except Exception as e:
            print(f"Error in phase sync feature extraction: {e}")
            
        return features
    
    def _extract_time_series_features(self, sequences: Dict[str, np.ndarray]) -> Dict[str, float]:
        """
        提取时间序列高级特征
        """
        features = {}
        
        try:
            for seq_name, seq_data in sequences.items():
                if len(seq_data) < 5:
                    continue
                    
                # 自相关特征
                autocorr = self._calculate_autocorrelation(seq_data)
                features[f'{seq_name}_autocorr_lag1'] = float(autocorr[0] if len(autocorr) > 0 else 0)
                features[f'{seq_name}_autocorr_lag5'] = float(autocorr[4] if len(autocorr) > 4 else 0)
                
                # 趋势强度
                trend_strength = self._calculate_trend_strength(seq_data)
                features[f'{seq_name}_trend_strength'] = float(trend_strength)
                
                # 季节性强度（如果数据足够长）
                if len(seq_data) > 20:
                    seasonality = self._calculate_seasonality_strength(seq_data)
                    features[f'{seq_name}_seasonality_strength'] = float(seasonality)
                
        except Exception as e:
            print(f"Error in time series feature extraction: {e}")
            
        return features
    
    def _calculate_entropy(self, data: np.ndarray) -> float:
        """计算熵"""
        if len(data) == 0:
            return 0.0
        hist, _ = np.histogram(data, bins=min(10, len(np.unique(data))))
        hist = hist[hist > 0]
        return entropy(hist)
    
    def _calculate_spectral_rolloff(self, power_spectrum: np.ndarray, rolloff_percent: float = 0.85) -> float:
        """计算频谱滚降点"""
        total_energy = np.sum(power_spectrum)
        cumulative_energy = np.cumsum(power_spectrum)
        rolloff_index = np.where(cumulative_energy >= rolloff_percent * total_energy)[0]
        return rolloff_index[0] if len(rolloff_index) > 0 else len(power_spectrum) - 1
    
    def _estimate_lyapunov_exponent(self, data: np.ndarray, m: int = 3, tau: int = 1) -> float:
        """
        估计Lyapunov指数
        简化实现，用于混沌特征分析
        """
        try:
            if len(data) < 2 * m:
                return 0.0
                
            # 重构相空间
            N = len(data) - (m - 1) * tau
            if N <= 0:
                return 0.0
                
            # 简化的Lyapunov指数计算
            diffs = np.diff(data)
            if len(diffs) == 0:
                return 0.0
                
            # 计算平均发散率
            divergence = np.mean(np.abs(diffs))
            return np.log(max(divergence, 1e-10))
            
        except:
            return 0.0
    
    def _estimate_correlation_dimension(self, data: np.ndarray) -> float:
        """估计关联维数"""
        try:
            if len(data) < 10:
                return 1.0
                
            # 简化的关联维数计算
            distances = []
            for i in range(len(data)-1):
                for j in range(i+1, len(data)):
                    distances.append(abs(data[i] - data[j]))
            
            if not distances:
                return 1.0
                
            # 计算关联积分的斜率作为维数估计
            distances = np.array(distances)
            distances = distances[distances > 0]
            
            if len(distances) == 0:
                return 1.0
                
            return min(2.0, np.std(distances) / np.mean(distances))
            
        except:
            return 1.0
    
    def _calculate_recurrence_rate(self, data: np.ndarray, threshold: float = None) -> float:
        """计算递归率"""
        try:
            if len(data) < 5:
                return 0.0
                
            if threshold is None:
                threshold = np.std(data) * 0.1
                
            recurrence_count = 0
            total_pairs = 0
            
            for i in range(len(data)):
                for j in range(len(data)):
                    if i != j:
                        if abs(data[i] - data[j]) < threshold:
                            recurrence_count += 1
                        total_pairs += 1
            
            return recurrence_count / total_pairs if total_pairs > 0 else 0.0
            
        except:
            return 0.0
    
    def _calculate_phase_locking_value(self, seq1: np.ndarray, seq2: np.ndarray) -> float:
        """计算相位锁定值"""
        try:
            if not SCIPY_AVAILABLE or len(seq1) != len(seq2) or len(seq1) < 5:
                return 0.0
                
            # 使用Hilbert变换获取瞬时相位
            analytic1 = signal.hilbert(seq1)
            analytic2 = signal.hilbert(seq2)
            
            phase1 = np.angle(analytic1)
            phase2 = np.angle(analytic2)
            
            # 计算相位差
            phase_diff = phase1 - phase2
            
            # 相位锁定值
            plv = np.abs(np.mean(np.exp(1j * phase_diff)))
            
            return plv
            
        except:
            return 0.0
    
    def _calculate_autocorrelation(self, data: np.ndarray, max_lags: int = 10) -> np.ndarray:
        """计算自相关函数"""
        try:
            if len(data) < 2:
                return np.array([0.0])
                
            autocorr = []
            data_centered = data - np.mean(data)
            variance = np.var(data)
            
            if variance == 0:
                return np.array([0.0] * max_lags)
            
            for lag in range(1, min(max_lags + 1, len(data))):
                if len(data) > lag:
                    corr = np.corrcoef(data_centered[:-lag], data_centered[lag:])[0, 1]
                    autocorr.append(corr if not np.isnan(corr) else 0.0)
                else:
                    autocorr.append(0.0)
            
            return np.array(autocorr)
            
        except:
            return np.array([0.0])
    
    def _calculate_trend_strength(self, data: np.ndarray) -> float:
        """计算趋势强度"""
        try:
            if len(data) < 3:
                return 0.0
                
            # 使用线性回归斜率作为趋势强度
            x = np.arange(len(data))
            coeffs = np.polyfit(x, data, 1)
            return abs(coeffs[0])  # 斜率的绝对值
            
        except:
            return 0.0
    
    def _calculate_seasonality_strength(self, data: np.ndarray, period: int = 7) -> float:
        """计算季节性强度"""
        try:
            if len(data) < 2 * period:
                return 0.0
                
            # 简化的季节性检测
            seasonal_diffs = []
            for i in range(period, len(data)):
                seasonal_diffs.append(abs(data[i] - data[i - period]))
            
            if not seasonal_diffs:
                return 0.0
                
            # 季节性强度 = 1 - (季节性差异的方差 / 原始数据的方差)
            seasonal_var = np.var(seasonal_diffs)
            original_var = np.var(data)
            
            if original_var == 0:
                return 0.0
                
            seasonality = 1 - (seasonal_var / original_var)
            return max(0.0, min(1.0, seasonality))
            
        except:
            return 0.0
    
    def _get_default_features(self) -> Dict[str, float]:
        """返回默认特征值"""
        default_features = {}
        
        # 为每个序列类型添加默认特征
        seq_types = ['hundreds', 'tens', 'units', 'sums', 'spans']
        
        for seq_type in seq_types:
            # 小波特征
            default_features[f'{seq_type}_wavelet_energy'] = 0.0
            default_features[f'{seq_type}_wavelet_entropy'] = 0.0
            default_features[f'{seq_type}_wavelet_max_scale'] = 0.0
            default_features[f'{seq_type}_spectral_centroid'] = 0.0
            default_features[f'{seq_type}_spectral_rolloff'] = 0.0
            
            # 分形特征
            default_features[f'{seq_type}_hurst_exponent'] = 0.5
            default_features[f'{seq_type}_dfa'] = 1.0
            default_features[f'{seq_type}_sample_entropy'] = 0.0
            
            # 混沌特征
            default_features[f'{seq_type}_lyapunov_exponent'] = 0.0
            default_features[f'{seq_type}_correlation_dimension'] = 1.0
            default_features[f'{seq_type}_recurrence_rate'] = 0.0
            
            # 时间序列特征
            default_features[f'{seq_type}_autocorr_lag1'] = 0.0
            default_features[f'{seq_type}_autocorr_lag5'] = 0.0
            default_features[f'{seq_type}_trend_strength'] = 0.0
            default_features[f'{seq_type}_seasonality_strength'] = 0.0
        
        return default_features


def test_advanced_features():
    """测试高级特征提取功能"""
    print("Testing Advanced Feature Extractor...")
    
    # 创建测试数据
    test_data = ['123', '456', '789', '012', '345', '678', '901', '234', '567', '890']
    
    extractor = AdvancedFeatureExtractor()
    features = extractor.extract_all_features(test_data)
    
    print(f"Extracted {len(features)} advanced features:")
    for key, value in list(features.items())[:10]:  # 显示前10个特征
        print(f"  {key}: {value:.6f}")
    
    print(f"Total features: {len(features)}")
    return features


if __name__ == "__main__":
    test_advanced_features()
