/* 
福彩3D预测系统响应式样式
优化CSS样式和响应式设计，实现美观的预测结果展示效果
*/

/* ============================================================================
   全局样式设置
   ============================================================================ */

:root {
    /* 主色调 */
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    
    /* 功能色彩 */
    --success-color: #4CAF50;
    --warning-color: #FF9800;
    --error-color: #f44336;
    --info-color: #2196F3;
    
    /* 推荐等级颜色 */
    --strong-recommend: #e74c3c;
    --recommend: #f39c12;
    --consider: #3498db;
    --caution: #95a5a6;
    --not-recommend: #bdc3c7;
    
    /* 间距和尺寸 */
    --border-radius: 12px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    
    /* 字体 */
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-large: 1.2rem;
    --font-size-medium: 1rem;
    --font-size-small: 0.9rem;
}

/* ============================================================================
   最佳推荐号码样式
   ============================================================================ */

.best-prediction-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: 20px;
    padding: 30px;
    color: white;
    text-align: center;
    margin: 20px 0;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.best-prediction-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: var(--transition);
    opacity: 0;
}

.best-prediction-card:hover::before {
    opacity: 1;
    animation: shimmer 2s infinite;
}

.best-prediction-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2);
}

.prediction-number {
    font-size: 4rem;
    font-weight: bold;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.3);
    margin: 0;
    letter-spacing: 0.1em;
    animation: pulse 2s infinite;
}

.prediction-subtitle {
    font-size: 1.2rem;
    margin: 15px 0 0 0;
    opacity: 0.9;
    font-weight: 300;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* ============================================================================
   排行榜表格样式
   ============================================================================ */

.ranking-table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    margin: 20px 0;
    background: white;
}

.ranking-table table {
    width: 100%;
    border-collapse: collapse;
    font-family: var(--font-family);
}

.ranking-table th {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 15px 10px;
    text-align: center;
    font-weight: 600;
    font-size: var(--font-size-medium);
    border: none;
}

.ranking-table td {
    padding: 12px 10px;
    text-align: center;
    border-bottom: 1px solid #f0f0f0;
    font-size: var(--font-size-medium);
    transition: var(--transition);
}

.ranking-table tr:hover td {
    background-color: #f8f9fa;
    transform: scale(1.02);
}

.ranking-table tr:nth-child(even) {
    background-color: #fafafa;
}

/* 排名列特殊样式 */
.ranking-table .rank-column {
    font-weight: bold;
    color: var(--primary-color);
    font-size: 1.1rem;
}

/* 号码列特殊样式 */
.ranking-table .number-column {
    font-weight: bold;
    font-size: 1.2rem;
    color: var(--secondary-color);
    font-family: 'Courier New', monospace;
}

/* ============================================================================
   推荐等级样式
   ============================================================================ */

.recommendation-strong {
    color: var(--strong-recommend);
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.recommendation-normal {
    color: var(--recommend);
    font-weight: 600;
}

.recommendation-consider {
    color: var(--consider);
    font-weight: 500;
}

.recommendation-caution {
    color: var(--caution);
    font-weight: 400;
}

.recommendation-not {
    color: var(--not-recommend);
    font-weight: 300;
}

/* 推荐等级徽章样式 */
.recommendation-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.recommendation-badge.strong {
    background-color: var(--strong-recommend);
    color: white;
}

.recommendation-badge.normal {
    background-color: var(--recommend);
    color: white;
}

.recommendation-badge.consider {
    background-color: var(--consider);
    color: white;
}

.recommendation-badge.caution {
    background-color: var(--caution);
    color: white;
}

/* ============================================================================
   卡片和容器样式
   ============================================================================ */

.prediction-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 20px;
    margin: 15px 0;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border: 1px solid #e0e0e0;
}

.prediction-card:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.prediction-card h3 {
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: 15px;
    font-weight: 600;
}

.metric-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    margin: 10px 0;
    transition: var(--transition);
    border: 2px solid transparent;
}

.metric-card:hover {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
    margin: 0;
}

.metric-label {
    font-size: var(--font-size-small);
    color: #666;
    margin: 5px 0 0 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ============================================================================
   按钮样式
   ============================================================================ */

.prediction-button {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 12px 24px;
    font-size: var(--font-size-medium);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: var(--box-shadow);
}

.prediction-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
}

.prediction-button:active {
    transform: translateY(0);
    box-shadow: var(--box-shadow);
}

.prediction-button.secondary {
    background: linear-gradient(135deg, #6c757d, #495057);
}

.prediction-button.success {
    background: linear-gradient(135deg, var(--success-color), #45a049);
}

/* ============================================================================
   进度条和加载动画
   ============================================================================ */

.confidence-bar {
    width: 100%;
    height: 8px;
    background-color: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 4px;
    transition: width 1s ease-in-out;
}

.loading-spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ============================================================================
   响应式布局
   ============================================================================ */

/* 大屏幕 (桌面) */
@media (min-width: 1200px) {
    .prediction-container {
        max-width: 1140px;
        margin: 0 auto;
    }
    
    .prediction-number {
        font-size: 5rem;
    }
}

/* 中等屏幕 (平板) */
@media (max-width: 1199px) and (min-width: 768px) {
    .prediction-container {
        max-width: 960px;
        margin: 0 auto;
        padding: 0 15px;
    }
    
    .prediction-number {
        font-size: 3.5rem;
    }
    
    .ranking-table th,
    .ranking-table td {
        padding: 10px 8px;
        font-size: var(--font-size-small);
    }
}

/* 小屏幕 (手机) */
@media (max-width: 767px) {
    .prediction-container {
        padding: 0 10px;
    }
    
    .best-prediction-card {
        padding: 20px;
        margin: 15px 0;
    }
    
    .prediction-number {
        font-size: 2.5rem;
    }
    
    .prediction-subtitle {
        font-size: 1rem;
    }
    
    .ranking-table {
        font-size: var(--font-size-small);
    }
    
    .ranking-table th,
    .ranking-table td {
        padding: 8px 4px;
    }
    
    .metric-card {
        padding: 15px;
        margin: 8px 0;
    }
    
    .metric-value {
        font-size: 1.5rem;
    }
    
    .prediction-button {
        width: 100%;
        padding: 15px;
        font-size: var(--font-size-large);
    }
}

/* 超小屏幕 */
@media (max-width: 480px) {
    .prediction-number {
        font-size: 2rem;
    }
    
    .ranking-table th,
    .ranking-table td {
        padding: 6px 2px;
        font-size: 0.8rem;
    }
    
    .prediction-card {
        padding: 15px;
        margin: 10px 0;
    }
}

/* ============================================================================
   深色主题支持
   ============================================================================ */

@media (prefers-color-scheme: dark) {
    :root {
        --background-color: #1a1a1a;
        --text-color: #ffffff;
        --card-background: #2d2d2d;
        --border-color: #404040;
    }
    
    body {
        background-color: var(--background-color);
        color: var(--text-color);
    }
    
    .prediction-card {
        background: var(--card-background);
        border-color: var(--border-color);
        color: var(--text-color);
    }
    
    .ranking-table td {
        border-bottom-color: var(--border-color);
    }
    
    .ranking-table tr:nth-child(even) {
        background-color: rgba(255, 255, 255, 0.05);
    }
    
    .ranking-table tr:hover td {
        background-color: rgba(255, 255, 255, 0.1);
    }
}

/* ============================================================================
   打印样式
   ============================================================================ */

@media print {
    .prediction-button,
    .loading-spinner {
        display: none;
    }
    
    .best-prediction-card {
        background: white !important;
        color: black !important;
        border: 2px solid #000;
    }
    
    .prediction-number {
        color: black !important;
        text-shadow: none !important;
    }
    
    .ranking-table {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .ranking-table th {
        background: #f0f0f0 !important;
        color: black !important;
    }
}

/* ============================================================================
   辅助功能和可访问性
   ============================================================================ */

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .best-prediction-card {
        border: 3px solid #000;
    }
    
    .prediction-button {
        border: 2px solid #000;
    }
    
    .ranking-table th,
    .ranking-table td {
        border: 1px solid #000;
    }
}

/* 减少动画 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .prediction-number {
        animation: none;
    }
    
    .best-prediction-card::before {
        animation: none;
    }
}

/* 焦点样式 */
.prediction-button:focus,
.ranking-table tr:focus {
    outline: 3px solid var(--info-color);
    outline-offset: 2px;
}

/* ============================================================================
   工具提示样式
   ============================================================================ */

.tooltip {
    position: relative;
    display: inline-block;
    cursor: help;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: var(--font-size-small);
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}
