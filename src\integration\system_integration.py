"""
系统集成模块
提供与现有福彩3D预测系统的集成接口，数据同步，配置管理
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import requests
import sqlite3
import pandas as pd

# 导入模型库模块
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from model_library.features.feature_ranking import MultiAlgorithmFeatureRanking
    from model_library.data.adaptive_quality_engine import AdaptiveDataQualityEngine
    from model_library.training.bayesian_recommender import BayesianHyperparameterRecommender
    from model_library.optimization.ab_testing import AdaptiveABTestingFramework
    from model_library.meta_learning.meta_optimizer import MetaLearner, Task
    from api.model_library_api import app
except ImportError as e:
    print(f"导入模块失败: {e}")


@dataclass
class IntegrationConfig:
    """集成配置"""
    # 现有系统配置
    existing_api_base_url: str = "http://localhost:8080/api"
    existing_db_path: str = "data/lottery_data.db"
    
    # 模型库配置
    model_library_api_url: str = "http://localhost:8000/api"
    
    # 同步配置
    sync_interval_minutes: int = 30
    auto_sync_enabled: bool = True
    
    # 数据映射配置
    data_field_mapping: Dict[str, str] = None
    
    def __post_init__(self):
        if self.data_field_mapping is None:
            self.data_field_mapping = {
                "period": "期号",
                "date": "开奖日期", 
                "winning_numbers": "开奖号码",
                "sales_amount": "销售额",
                "direct_prize": "直选奖金",
                "group3_prize": "组三奖金",
                "group6_prize": "组六奖金"
            }


class SystemIntegrator:
    """系统集成器"""
    
    def __init__(self, config: IntegrationConfig):
        self.config = config
        self.logger = logging.getLogger("SystemIntegrator")
        self._setup_logger()
        
        # 初始化模型库组件
        self.feature_ranking = MultiAlgorithmFeatureRanking()
        self.quality_engine = AdaptiveDataQualityEngine()
        self.bayesian_recommender = BayesianHyperparameterRecommender()
        self.ab_framework = AdaptiveABTestingFramework()
        self.meta_learner = MetaLearner()
        
        # 同步状态
        self.last_sync_time = None
        self.sync_running = False
    
    def _setup_logger(self):
        """设置日志"""
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    async def initialize_integration(self) -> bool:
        """初始化集成"""
        try:
            self.logger.info("开始初始化系统集成...")
            
            # 检查现有系统连接
            if not await self._check_existing_system():
                self.logger.error("无法连接到现有系统")
                return False
            
            # 检查模型库API
            if not await self._check_model_library_api():
                self.logger.error("无法连接到模型库API")
                return False
            
            # 执行初始数据同步
            await self._initial_data_sync()
            
            # 启动定期同步
            if self.config.auto_sync_enabled:
                asyncio.create_task(self._periodic_sync())
            
            self.logger.info("系统集成初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"系统集成初始化失败: {e}")
            return False
    
    async def _check_existing_system(self) -> bool:
        """检查现有系统连接"""
        try:
            # 检查数据库连接
            if os.path.exists(self.config.existing_db_path):
                conn = sqlite3.connect(self.config.existing_db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM lottery_records LIMIT 1")
                conn.close()
                self.logger.info("现有数据库连接正常")
                return True
            else:
                self.logger.warning("现有数据库不存在，将创建新数据库")
                return True
                
        except Exception as e:
            self.logger.error(f"检查现有系统失败: {e}")
            return False
    
    async def _check_model_library_api(self) -> bool:
        """检查模型库API连接"""
        try:
            response = requests.get(f"{self.config.model_library_api_url}/../health", timeout=5)
            if response.status_code == 200:
                self.logger.info("模型库API连接正常")
                return True
            else:
                self.logger.error(f"模型库API响应异常: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"检查模型库API失败: {e}")
            return False
    
    async def _initial_data_sync(self):
        """初始数据同步"""
        try:
            self.logger.info("开始初始数据同步...")
            
            # 从现有系统获取数据
            existing_data = await self._fetch_existing_data()
            
            if existing_data.empty:
                self.logger.warning("现有系统无数据")
                return
            
            # 数据质量评估
            quality_score = self.quality_engine.calculate_adaptive_quality_score(
                "system_integration", (0, len(existing_data)), existing_data
            )
            
            self.logger.info(f"数据质量评分: {quality_score.overall_score:.3f}")
            
            # 特征重要性分析
            if len(existing_data.columns) > 3:
                feature_names = list(existing_data.columns[3:])  # 跳过基础字段
                X = existing_data[feature_names].fillna(0).values
                y = existing_data['winning_numbers'].apply(lambda x: int(str(x)[:1]) if str(x) else 0).values
                
                rankings = self.feature_ranking.calculate_comprehensive_ranking(
                    X, y, feature_names, "system_integration"
                )
                
                self.logger.info(f"特征重要性排序: {rankings}")
            
            self.last_sync_time = datetime.now()
            self.logger.info("初始数据同步完成")
            
        except Exception as e:
            self.logger.error(f"初始数据同步失败: {e}")
    
    async def _fetch_existing_data(self) -> pd.DataFrame:
        """从现有系统获取数据"""
        try:
            if os.path.exists(self.config.existing_db_path):
                conn = sqlite3.connect(self.config.existing_db_path)
                
                # 查询最新数据
                query = """
                SELECT * FROM lottery_records 
                ORDER BY period DESC 
                LIMIT 1000
                """
                
                df = pd.read_sql_query(query, conn)
                conn.close()
                
                # 数据字段映射
                df = self._map_data_fields(df)
                
                return df
            else:
                # 生成模拟数据用于测试
                return self._generate_mock_data()
                
        except Exception as e:
            self.logger.error(f"获取现有数据失败: {e}")
            return pd.DataFrame()
    
    def _map_data_fields(self, df: pd.DataFrame) -> pd.DataFrame:
        """映射数据字段"""
        try:
            # 重命名列
            reverse_mapping = {v: k for k, v in self.config.data_field_mapping.items()}
            df = df.rename(columns=reverse_mapping)
            
            # 确保必要字段存在
            required_fields = ['period', 'date', 'winning_numbers']
            for field in required_fields:
                if field not in df.columns:
                    df[field] = None
            
            return df
            
        except Exception as e:
            self.logger.error(f"数据字段映射失败: {e}")
            return df
    
    def _generate_mock_data(self) -> pd.DataFrame:
        """生成模拟数据"""
        import numpy as np
        
        data = []
        for i in range(100):
            data.append({
                'period': 2024000 + i,
                'date': pd.Timestamp('2024-01-01') + pd.Timedelta(days=i),
                'winning_numbers': f"{np.random.randint(0,10)}{np.random.randint(0,10)}{np.random.randint(0,10)}",
                'sales_amount': np.random.uniform(1000000, 5000000),
                'direct_prize': 1040,
                'group3_prize': 346,
                'group6_prize': 173
            })
        
        return pd.DataFrame(data)
    
    async def _periodic_sync(self):
        """定期同步"""
        while True:
            try:
                await asyncio.sleep(self.config.sync_interval_minutes * 60)
                
                if not self.sync_running:
                    self.sync_running = True
                    await self._incremental_sync()
                    self.sync_running = False
                    
            except Exception as e:
                self.logger.error(f"定期同步失败: {e}")
                self.sync_running = False
    
    async def _incremental_sync(self):
        """增量同步"""
        try:
            self.logger.info("开始增量同步...")
            
            # 获取新数据
            new_data = await self._fetch_new_data_since_last_sync()
            
            if new_data.empty:
                self.logger.info("无新数据需要同步")
                return
            
            # 处理新数据
            await self._process_new_data(new_data)
            
            self.last_sync_time = datetime.now()
            self.logger.info(f"增量同步完成，处理了 {len(new_data)} 条记录")
            
        except Exception as e:
            self.logger.error(f"增量同步失败: {e}")
    
    async def _fetch_new_data_since_last_sync(self) -> pd.DataFrame:
        """获取上次同步后的新数据"""
        try:
            if not self.last_sync_time:
                return pd.DataFrame()
            
            # 这里应该根据时间戳查询新数据
            # 简化实现，返回空DataFrame
            return pd.DataFrame()
            
        except Exception as e:
            self.logger.error(f"获取新数据失败: {e}")
            return pd.DataFrame()
    
    async def _process_new_data(self, new_data: pd.DataFrame):
        """处理新数据"""
        try:
            # 数据质量评估
            quality_score = self.quality_engine.calculate_adaptive_quality_score(
                "incremental_sync", (0, len(new_data)), new_data
            )
            
            # 如果质量分数过低，发出警告
            if quality_score.overall_score < 0.7:
                self.logger.warning(f"新数据质量较低: {quality_score.overall_score:.3f}")
            
            # 更新元学习任务
            await self._update_meta_learning_with_new_data(new_data)
            
        except Exception as e:
            self.logger.error(f"处理新数据失败: {e}")
    
    async def _update_meta_learning_with_new_data(self, new_data: pd.DataFrame):
        """使用新数据更新元学习"""
        try:
            # 创建新任务
            task = Task(
                task_id=f"sync_task_{int(datetime.now().timestamp())}",
                name="增量同步任务",
                description="基于新数据的增量学习任务",
                task_type="time_series",
                data_size=len(new_data),
                feature_count=len(new_data.columns),
                target_type="categorical",
                complexity_score=0.5
            )
            
            self.meta_learner.add_task(task)
            
        except Exception as e:
            self.logger.error(f"更新元学习失败: {e}")
    
    async def sync_model_configurations(self):
        """同步模型配置"""
        try:
            self.logger.info("开始同步模型配置...")
            
            # 获取现有系统的模型配置
            existing_configs = await self._fetch_existing_model_configs()
            
            # 更新模型库配置
            for model_id, config in existing_configs.items():
                # 更新贝叶斯推荐器的历史
                if 'hyperparameters' in config and 'performance' in config:
                    self.bayesian_recommender.update_optimization_history(
                        model_id, config['hyperparameters'], config['performance']
                    )
            
            self.logger.info("模型配置同步完成")
            
        except Exception as e:
            self.logger.error(f"同步模型配置失败: {e}")
    
    async def _fetch_existing_model_configs(self) -> Dict[str, Any]:
        """获取现有系统的模型配置"""
        try:
            # 这里应该从现有系统API获取配置
            # 简化实现，返回模拟配置
            return {
                "intelligent_fusion": {
                    "hyperparameters": {"learning_rate": 0.001, "batch_size": 64},
                    "performance": {"accuracy": 0.85, "loss": 0.25}
                },
                "deep_learning_cnn_lstm": {
                    "hyperparameters": {"learning_rate": 0.002, "batch_size": 32},
                    "performance": {"accuracy": 0.82, "loss": 0.28}
                }
            }
            
        except Exception as e:
            self.logger.error(f"获取现有模型配置失败: {e}")
            return {}
    
    async def export_integration_report(self) -> Dict[str, Any]:
        """导出集成报告"""
        try:
            report = {
                "integration_status": "active" if self.last_sync_time else "inactive",
                "last_sync_time": self.last_sync_time.isoformat() if self.last_sync_time else None,
                "sync_interval_minutes": self.config.sync_interval_minutes,
                "auto_sync_enabled": self.config.auto_sync_enabled,
                "components_status": {
                    "feature_ranking": "active",
                    "quality_engine": "active", 
                    "bayesian_recommender": "active",
                    "ab_framework": "active",
                    "meta_learner": "active"
                },
                "data_quality": await self._get_current_data_quality(),
                "model_performance": await self._get_model_performance_summary()
            }
            
            return report
            
        except Exception as e:
            self.logger.error(f"导出集成报告失败: {e}")
            return {"error": str(e)}
    
    async def _get_current_data_quality(self) -> Dict[str, float]:
        """获取当前数据质量"""
        try:
            # 获取最新数据
            latest_data = await self._fetch_existing_data()
            
            if latest_data.empty:
                return {"overall_score": 0.0}
            
            # 评估质量
            quality_score = self.quality_engine.calculate_adaptive_quality_score(
                "current_assessment", (0, len(latest_data)), latest_data
            )
            
            return {
                "overall_score": quality_score.overall_score,
                "completeness": quality_score.completeness,
                "consistency": quality_score.consistency,
                "accuracy": quality_score.accuracy,
                "timeliness": quality_score.timeliness,
                "validity": quality_score.validity
            }
            
        except Exception as e:
            self.logger.error(f"获取数据质量失败: {e}")
            return {"overall_score": 0.0}
    
    async def _get_model_performance_summary(self) -> Dict[str, Any]:
        """获取模型性能摘要"""
        try:
            # 获取元学习洞察
            insights = self.meta_learner.get_meta_learning_insights()
            
            # 获取A/B测试结果
            experiments = self.ab_framework.list_experiments()
            
            return {
                "total_tasks": insights.get("total_tasks", 0),
                "average_performance": insights.get("average_task_performance", 0.0),
                "active_experiments": len([exp for exp in experiments if exp.get("status") == "running"]),
                "completed_experiments": len([exp for exp in experiments if exp.get("status") == "completed"])
            }
            
        except Exception as e:
            self.logger.error(f"获取模型性能摘要失败: {e}")
            return {}


async def test_system_integration():
    """测试系统集成"""
    print("🧪 测试系统集成...")
    
    # 创建集成配置
    config = IntegrationConfig(
        existing_db_path="test_lottery.db",
        sync_interval_minutes=1  # 测试用短间隔
    )
    
    # 创建集成器
    integrator = SystemIntegrator(config)
    
    # 初始化集成
    success = await integrator.initialize_integration()
    print(f"✅ 集成初始化: {'成功' if success else '失败'}")
    
    # 同步模型配置
    await integrator.sync_model_configurations()
    print("✅ 模型配置同步完成")
    
    # 导出集成报告
    report = await integrator.export_integration_report()
    print(f"📊 集成报告: {report}")
    
    print("✅ 系统集成测试完成！")


if __name__ == "__main__":
    asyncio.run(test_system_integration())
