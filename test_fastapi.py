#!/usr/bin/env python3
"""
测试FastAPI接口
"""

import sys
sys.path.append('src')

import requests
import json
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

def test_api_endpoints():
    """测试API接口"""
    base_url = "http://localhost:8000"
    
    print("🚀 开始测试FastAPI接口...")
    
    # 测试接口列表
    test_cases = [
        {
            "name": "健康检查",
            "url": f"{base_url}/health",
            "method": "GET"
        },
        {
            "name": "基础统计",
            "url": f"{base_url}/api/v1/stats/basic",
            "method": "GET"
        },
        {
            "name": "频率分析",
            "url": f"{base_url}/api/v1/analysis/frequency?position=all",
            "method": "GET"
        },
        {
            "name": "和值分布",
            "url": f"{base_url}/api/v1/analysis/sum-distribution",
            "method": "GET"
        },
        {
            "name": "销售额分析",
            "url": f"{base_url}/api/v1/analysis/sales",
            "method": "GET"
        },
        {
            "name": "数据查询-日期范围",
            "url": f"{base_url}/api/v1/data/query?start_date=2025-01-01&end_date=2025-07-13&limit=10",
            "method": "GET"
        },
        {
            "name": "数据查询-和值范围",
            "url": f"{base_url}/api/v1/data/query?min_sum=10&max_sum=15&limit=10",
            "method": "GET"
        },
        {
            "name": "趋势分析",
            "url": f"{base_url}/api/v1/analysis/trends?days=30",
            "method": "GET"
        },
        {
            "name": "性能统计",
            "url": f"{base_url}/api/v1/system/performance",
            "method": "GET"
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"\n测试: {test_case['name']}")
        try:
            start_time = time.time()
            response = requests.get(test_case['url'], timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 成功 - 响应时间: {response_time*1000:.2f}ms")
                
                # 显示关键信息
                if 'query_time_ms' in data:
                    print(f"   查询时间: {data['query_time_ms']}ms")
                if 'total_records' in data:
                    print(f"   记录数: {data['total_records']}")
                if 'total_count' in data:
                    print(f"   返回数: {data['total_count']}")
                
                results.append({
                    "test": test_case['name'],
                    "status": "success",
                    "response_time_ms": response_time * 1000,
                    "query_time_ms": data.get('query_time_ms', 0)
                })
            else:
                print(f"❌ 失败 - 状态码: {response.status_code}")
                print(f"   错误: {response.text}")
                results.append({
                    "test": test_case['name'],
                    "status": "failed",
                    "error": response.text
                })
                
        except Exception as e:
            print(f"❌ 异常: {e}")
            results.append({
                "test": test_case['name'],
                "status": "error",
                "error": str(e)
            })
    
    return results

def test_concurrent_requests():
    """测试并发请求"""
    print(f"\n🔄 测试并发性能...")
    
    base_url = "http://localhost:8000"
    endpoint = f"{base_url}/api/v1/stats/basic"
    
    def make_request(i):
        try:
            start_time = time.time()
            response = requests.get(endpoint, timeout=5)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                return {
                    "request_id": i,
                    "status": "success",
                    "response_time_ms": response_time * 1000,
                    "query_time_ms": data.get('query_time_ms', 0)
                }
            else:
                return {
                    "request_id": i,
                    "status": "failed",
                    "error": response.text
                }
        except Exception as e:
            return {
                "request_id": i,
                "status": "error",
                "error": str(e)
            }
    
    # 并发测试
    concurrent_requests = 20
    start_time = time.time()
    
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(make_request, i) for i in range(concurrent_requests)]
        results = [future.result() for future in as_completed(futures)]
    
    total_time = time.time() - start_time
    
    # 统计结果
    success_count = len([r for r in results if r["status"] == "success"])
    failed_count = len([r for r in results if r["status"] != "success"])
    
    if success_count > 0:
        avg_response_time = sum(r["response_time_ms"] for r in results if r["status"] == "success") / success_count
        avg_query_time = sum(r["query_time_ms"] for r in results if r["status"] == "success") / success_count
    else:
        avg_response_time = 0
        avg_query_time = 0
    
    print(f"✅ 并发测试完成:")
    print(f"   总请求数: {concurrent_requests}")
    print(f"   成功数: {success_count}")
    print(f"   失败数: {failed_count}")
    print(f"   总耗时: {total_time:.2f}秒")
    print(f"   平均响应时间: {avg_response_time:.2f}ms")
    print(f"   平均查询时间: {avg_query_time:.2f}ms")
    print(f"   QPS: {concurrent_requests/total_time:.2f}")
    
    return {
        "total_requests": concurrent_requests,
        "success_count": success_count,
        "failed_count": failed_count,
        "total_time_s": total_time,
        "avg_response_time_ms": avg_response_time,
        "avg_query_time_ms": avg_query_time,
        "qps": concurrent_requests/total_time
    }

def test_performance_benchmark():
    """性能基准测试"""
    print(f"\n📊 性能基准测试...")
    
    base_url = "http://localhost:8000"
    
    # 测试不同接口的性能
    performance_tests = [
        {
            "name": "基础统计(缓存)",
            "url": f"{base_url}/api/v1/stats/basic?use_cache=true"
        },
        {
            "name": "基础统计(无缓存)",
            "url": f"{base_url}/api/v1/stats/basic?use_cache=false"
        },
        {
            "name": "频率分析(缓存)",
            "url": f"{base_url}/api/v1/analysis/frequency?use_cache=true"
        },
        {
            "name": "数据查询(小范围)",
            "url": f"{base_url}/api/v1/data/query?min_sum=13&max_sum=14&limit=50"
        },
        {
            "name": "数据查询(大范围)",
            "url": f"{base_url}/api/v1/data/query?start_date=2020-01-01&end_date=2025-12-31&limit=100"
        }
    ]
    
    benchmark_results = []
    
    for test in performance_tests:
        print(f"\n测试: {test['name']}")
        
        # 预热请求
        try:
            requests.get(test['url'], timeout=5)
        except:
            pass
        
        # 性能测试
        times = []
        query_times = []
        
        for i in range(10):
            try:
                start_time = time.time()
                response = requests.get(test['url'], timeout=10)
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    data = response.json()
                    times.append(response_time * 1000)
                    query_times.append(data.get('query_time_ms', 0))
                else:
                    print(f"   请求失败: {response.status_code}")
                    
            except Exception as e:
                print(f"   请求异常: {e}")
        
        if times:
            avg_time = sum(times) / len(times)
            min_time = min(times)
            max_time = max(times)
            avg_query_time = sum(query_times) / len(query_times)
            
            print(f"   平均响应时间: {avg_time:.2f}ms")
            print(f"   最快响应时间: {min_time:.2f}ms")
            print(f"   最慢响应时间: {max_time:.2f}ms")
            print(f"   平均查询时间: {avg_query_time:.2f}ms")
            
            # 性能评估
            if avg_time < 50:
                performance_grade = "优秀"
            elif avg_time < 200:
                performance_grade = "良好"
            elif avg_time < 500:
                performance_grade = "一般"
            else:
                performance_grade = "需要优化"
            
            print(f"   性能评级: {performance_grade}")
            
            benchmark_results.append({
                "test": test['name'],
                "avg_response_time_ms": avg_time,
                "min_response_time_ms": min_time,
                "max_response_time_ms": max_time,
                "avg_query_time_ms": avg_query_time,
                "performance_grade": performance_grade
            })
    
    return benchmark_results

def main():
    """主测试函数"""
    print("🧪 FastAPI接口测试套件")
    print("=" * 50)
    
    try:
        # 基础接口测试
        basic_results = test_api_endpoints()
        
        # 并发性能测试
        concurrent_results = test_concurrent_requests()
        
        # 性能基准测试
        benchmark_results = test_performance_benchmark()
        
        # 生成测试报告
        report = {
            "test_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "basic_tests": basic_results,
            "concurrent_test": concurrent_results,
            "benchmark_tests": benchmark_results
        }
        
        # 保存测试报告
        with open('data/processed/api_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📋 测试总结:")
        success_count = len([r for r in basic_results if r["status"] == "success"])
        total_count = len(basic_results)
        print(f"   基础测试: {success_count}/{total_count} 通过")
        print(f"   并发测试: QPS {concurrent_results['qps']:.2f}")
        print(f"   性能基准: {len(benchmark_results)} 项测试完成")
        print(f"   测试报告: data/processed/api_test_report.json")
        
        if success_count == total_count and concurrent_results['success_count'] > 0:
            print(f"\n🎉 所有测试通过！FastAPI接口准备就绪！")
            return True
        else:
            print(f"\n⚠️ 部分测试失败，请检查API服务状态")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
