#!/usr/bin/env python3
"""
福彩3D预测系统 - 一键启动脚本
支持Windows和Linux/macOS平台
"""

import os
import platform
import subprocess
import sys
import time
from pathlib import Path


def print_banner():
    """打印启动横幅"""
    print("\n" + "=" * 50)
    print("    福彩3D预测分析工具 - 一键启动")
    print("=" * 50)
    print("\n🚀 正在启动系统...\n")


def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major != 3 or python_version.minor < 11:
        print(f"❌ 错误：需要Python 3.11+，当前版本：{python_version.major}.{python_version.minor}")
        return False
    
    # 检查虚拟环境
    venv_path = Path("venv")
    if platform.system() == "Windows":
        activate_script = venv_path / "Scripts" / "activate.bat"
        python_exe = venv_path / "Scripts" / "python.exe"
    else:
        activate_script = venv_path / "bin" / "activate"
        python_exe = venv_path / "bin" / "python"
    
    if not activate_script.exists():
        print("❌ 错误：虚拟环境不存在！")
        print("请先运行以下命令创建虚拟环境：")
        print("python -m venv venv")
        if platform.system() == "Windows":
            print("venv\\Scripts\\activate")
        else:
            print("source venv/bin/activate")
        print("pip install -e .")
        return False
    
    # 检查数据库文件
    db_path = Path("data") / "lottery.db"
    if not db_path.exists():
        print("❌ 错误：数据库文件不存在！")
        print("请确保 data/lottery.db 文件存在")
        return False
    
    print("✅ 环境检查通过")
    return True, python_exe


def start_api_service(python_exe):
    """启动API服务"""
    print("🔧 步骤1: 启动API服务（后台运行）...")

    try:
        if platform.system() == "Windows":
            # Windows下使用start命令在新窗口中启动
            cmd = f'start "API服务" cmd /k "{python_exe} start_production_api.py"'
            subprocess.Popen(cmd, shell=True, text=True, encoding='utf-8')
        else:
            # Linux/macOS下使用nohup在后台启动
            with open("api_service.log", "w", encoding='utf-8') as log_file:
                subprocess.Popen([
                    str(python_exe), "start_production_api.py"
                ], stdout=log_file, stderr=log_file, text=True, encoding='utf-8')

        print("✅ API服务启动命令已执行")
        return True
    except Exception as e:
        print(f"❌ API服务启动失败：{e}")
        return False


def start_streamlit_interface(python_exe):
    """启动Streamlit界面"""
    print("🎨 步骤3: 启动Streamlit界面...")
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = 'src'
        
        # 启动Streamlit
        subprocess.run([
            str(python_exe), "-m", "streamlit", "run", 
            "src/ui/main.py",
            "--server.port=8501",
            "--server.address=127.0.0.1",
            "--browser.gatherUsageStats=false"
        ], env=env)
        
    except KeyboardInterrupt:
        print("\n👋 Streamlit界面已停止")
    except Exception as e:
        print(f"❌ Streamlit启动失败：{e}")


def print_access_info():
    """打印访问信息"""
    print("\n✅ 系统启动完成！\n")
    print("📱 访问地址：")
    print("   - Streamlit界面: http://127.0.0.1:8501")
    print("   - API服务: http://127.0.0.1:8888")
    print("   - API文档: http://127.0.0.1:8888/docs")
    print("\n💡 提示：")
    print("   - 关闭此窗口将停止Streamlit界面")
    print("   - API服务在独立进程中运行")
    if platform.system() != "Windows":
        print("   - API服务日志保存在 api_service.log")
    print("   - 如需停止所有服务，请关闭所有相关窗口/进程\n")


def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    result = check_environment()
    if not result:
        input("\n按回车键退出...")
        return
    
    _, python_exe = result
    
    print("\n📋 启动步骤：")
    print("1. 启动API服务（后台运行）")
    print("2. 等待5秒确保API服务启动完成")
    print("3. 启动Streamlit界面\n")
    
    # 启动API服务
    if not start_api_service(python_exe):
        input("\n按回车键退出...")
        return
    
    # 等待API服务启动
    print("⏳ 步骤2: 等待API服务启动（5秒）...")
    time.sleep(5)
    
    # 打印访问信息
    print_access_info()
    
    # 启动Streamlit界面
    start_streamlit_interface(python_exe)
    
    print("\n👋 系统已停止")
    if platform.system() != "Windows":
        print("💡 API服务可能仍在后台运行，请检查进程")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，系统退出")
    except Exception as e:
        print(f"\n❌ 启动过程中发生错误：{e}")
        input("\n按回车键退出...")
