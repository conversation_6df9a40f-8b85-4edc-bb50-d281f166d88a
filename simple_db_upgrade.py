import sqlite3
import os
from datetime import datetime

# 数据库升级
db_path = "data/bug_detection.db"
os.makedirs("data", exist_ok=True)

conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# 添加缺失的列
columns = [
    "ALTER TABLE bug_reports ADD COLUMN environment TEXT DEFAULT 'production'",
    "ALTER TABLE bug_reports ADD COLUMN category TEXT DEFAULT 'general'", 
    "ALTER TABLE bug_reports ADD COLUMN priority TEXT DEFAULT 'medium'",
    "ALTER TABLE bug_reports ADD COLUMN tags TEXT",
    "ALTER TABLE bug_reports ADD COLUMN source TEXT DEFAULT 'user'"
]

for sql in columns:
    try:
        cursor.execute(sql)
        print(f"✅ 执行成功: {sql}")
    except Exception as e:
        print(f"⏭️ 跳过 (可能已存在): {e}")

conn.commit()
conn.close()
print("🎉 数据库升级完成！")
