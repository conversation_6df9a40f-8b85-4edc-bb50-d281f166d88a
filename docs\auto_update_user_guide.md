# 福彩3D自动更新功能使用指南

## 📋 概述

福彩3D预测系统现已集成完整的自动更新功能，支持在界面中直接控制调度器、设置更新时间、查看运行状态和日志。本指南将详细介绍如何使用这些功能。

## 🚀 快速开始

### 1. 启动自动更新（3步完成）

1. **进入自动更新页面**
   - 启动Streamlit应用
   - 进入"数据更新"页面
   - 点击"⏰ 自动更新"选项卡

2. **启动调度器**
   - 点击"🚀 启动调度器"按钮
   - 等待启动完成（通常需要3-5秒）
   - 确认状态显示为"运行中"

3. **设置更新时间**
   - 选择"21:30 (推荐)"或其他时间
   - 点击"💾 应用时间配置"
   - 确认配置更新成功

### 2. 验证设置

- 进入"📊 调度监控"选项卡
- 查看"运行状态"为"运行中"
- 确认"更新时间"显示正确
- 检查"自动更新"状态为"已启用"

## 📊 功能详解

### 自动更新设置

**位置**: 数据更新 → ⏰ 自动更新

**功能**:
- 🚀 启动/停止/重启调度器
- ⏰ 设置自动更新时间
- 📊 查看实时状态
- 💾 保存配置

**时间选项**:
- `21:30 (推荐)` - 福彩3D开奖后的最佳更新时间
- `21:00` - 开奖时间，可能数据未完全更新
- `22:00` - 较晚的更新时间
- `23:00` - 最晚的更新时间
- `自定义` - 使用Cron表达式自定义

### 调度监控

**位置**: 数据更新 → 📊 调度监控

**功能**:
- 📈 实时状态监控
- 📊 快速统计信息
- ⚡ 快速操作按钮
- 📋 操作历史记录

**监控指标**:
- 运行状态（运行中/已停止）
- 任务数量
- 自动更新状态
- 最后检查时间

### 日志查看

**位置**: 数据更新 → 📋 日志查看

**功能**:
- 📄 查看调度器日志
- 🔍 过滤和搜索
- 📥 下载日志文件
- 📊 日志统计

**过滤选项**:
- 日志级别（DEBUG, INFO, WARNING, ERROR）
- 模块过滤
- 文本搜索
- 时间范围

## ⚙️ 高级配置

### Cron表达式

如果选择"自定义"时间，需要使用Cron表达式：

```
格式: 分 时 日 月 周
示例:
30 21 * * *  # 每天21:30
0 22 * * *   # 每天22:00
*/30 * * * * # 每30分钟
0 21 * * 1-5 # 周一到周五21:00
```

### 配置文件

**调度器配置**: `scheduler_config.json`
```json
{
  "update_schedule": {
    "enabled": true,
    "cron": "30 21 * * *",
    "timezone": "Asia/Shanghai",
    "description": "每天21:30执行数据更新"
  }
}
```

**UI配置**: `data/ui_config.json`
```json
{
  "auto_update": {
    "enabled": true,
    "selected_time": "21:30 (推荐)",
    "custom_cron": "30 21 * * *"
  }
}
```

## 🔧 故障排除

### 常见问题

#### 1. 调度器启动失败

**症状**: 点击启动按钮后显示"启动失败"

**解决方案**:
1. 检查是否有管理员权限
2. 确认端口没有被占用
3. 重启应用程序
4. 查看错误日志

**命令行测试**:
```bash
python scripts/start_scheduler.py --test
```

#### 2. 配置保存失败

**症状**: 点击"应用配置"后显示"配置更新失败"

**解决方案**:
1. 检查文件写入权限
2. 确认磁盘空间充足
3. 检查配置文件格式

#### 3. 自动更新不执行

**症状**: 到了设定时间但没有自动更新

**解决方案**:
1. 确认调度器正在运行
2. 检查时间配置是否正确
3. 查看调度器日志
4. 验证网络连接

#### 4. 数据源访问失败

**症状**: 日志中显示429错误或网络错误

**解决方案**:
1. 等待1-2分钟后重试
2. 检查网络连接
3. 在"数据源管理"中测试连接

### 日志分析

**重要日志级别**:
- `INFO` - 正常操作信息
- `WARNING` - 警告信息，需要关注
- `ERROR` - 错误信息，需要处理

**关键日志内容**:
- "调度器启动成功" - 调度器正常启动
- "数据更新成功" - 自动更新执行成功
- "配置文件加载成功" - 配置正常加载
- "429" - 访问频率限制，需要等待

## 📱 命令行工具

### 调度器管理

```bash
# 启动调度器（后台运行）
python scripts/start_scheduler.py --daemon

# 查看调度器状态
python scripts/start_scheduler.py --status

# 立即执行数据更新
python scripts/start_scheduler.py --run-job data_update

# 测试调度器功能
python scripts/start_scheduler.py --test
```

### 配置管理

```bash
# 查看当前配置
cat scheduler_config.json

# 备份配置
cp scheduler_config.json scheduler_config.backup.json

# 重置为默认配置
rm scheduler_config.json
# 重启应用后会自动创建默认配置
```

## 🎯 最佳实践

### 1. 推荐设置

- **更新时间**: 21:30（开奖后30分钟）
- **监控频率**: 每天检查一次状态
- **日志保留**: 保留最近30天的日志
- **备份策略**: 定期备份配置文件

### 2. 性能优化

- 避免在高峰时段手动更新
- 定期清理旧的日志文件
- 监控磁盘空间使用情况
- 及时处理错误和警告

### 3. 安全建议

- 定期检查调度器运行状态
- 监控异常的网络访问
- 保护配置文件不被误删
- 及时更新系统依赖

## 📞 技术支持

### 获取帮助

1. **界面帮助**: 查看各选项卡底部的帮助文档
2. **日志分析**: 使用日志查看器分析问题
3. **命令行测试**: 使用测试命令验证功能
4. **配置检查**: 验证配置文件格式和内容

### 报告问题

提供以下信息有助于快速解决问题：
- 错误截图或描述
- 相关日志内容
- 操作步骤
- 系统环境信息

---

**版本**: v2.0  
**更新日期**: 2025-07-16  
**适用系统**: Windows 10/11, Python 3.11+
