#!/usr/bin/env python3
"""
测试数据更新API端点
"""

import requests
import json
import time

def test_data_update_api():
    """测试数据更新API功能"""
    base_url = "http://127.0.0.1:8888"
    
    print("🧪 测试数据更新API端点...")
    
    # 1. 测试更新状态检查
    try:
        print("\n1. 测试数据更新状态检查...")
        response = requests.get(f"{base_url}/api/v1/data/update/status", timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功")
            print(f"   最后更新: {data.get('last_update_time', 'N/A')}")
            print(f"   最新期号: {data.get('last_period', 'N/A')}")
            print(f"   记录总数: {data.get('current_record_count', 'N/A')}")
            print(f"   数据源状态: {data.get('data_source_status', 'N/A')}")
            print(f"   有新数据: {data.get('has_new_data', 'N/A')}")
        else:
            print(f"   ❌ 失败 - 状态码: {response.status_code}")
            print(f"   响应: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 2. 测试更新历史记录
    try:
        print("\n2. 测试更新历史记录...")
        response = requests.get(f"{base_url}/api/v1/data/update/history?limit=5", timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功")
            print(f"   历史记录数: {len(data.get('updates', []))}")
            print(f"   总记录数: {data.get('total_count', 'N/A')}")
            print(f"   活跃更新: {data.get('active_updates', 'N/A')}")
        else:
            print(f"   ❌ 失败 - 状态码: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 3. 测试手动触发更新（不强制）
    try:
        print("\n3. 测试手动触发更新...")
        response = requests.post(f"{base_url}/api/v1/data/update/trigger?force_update=false", timeout=30)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功")
            print(f"   更新ID: {data.get('update_id', 'N/A')}")
            print(f"   状态: {data.get('status', 'N/A')}")
            print(f"   消息: {data.get('message', 'N/A')}")
            print(f"   新增记录: {data.get('records_added', 'N/A')}")
            print(f"   耗时: {data.get('duration_ms', 'N/A')}ms")
            
            # 如果有更新ID，测试进度查询
            update_id = data.get('update_id')
            if update_id:
                print(f"\n   测试进度查询...")
                progress_response = requests.get(f"{base_url}/api/v1/data/update/progress/{update_id}", timeout=10)
                if progress_response.status_code == 200:
                    progress_data = progress_response.json()
                    print(f"   进度状态: {progress_data.get('status', 'N/A')}")
                    print(f"   进度: {progress_data.get('progress', 'N/A')}%")
        else:
            print(f"   ❌ 失败 - 状态码: {response.status_code}")
            print(f"   响应: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 4. 再次检查状态（看是否有变化）
    try:
        print("\n4. 再次检查更新状态...")
        response = requests.get(f"{base_url}/api/v1/data/update/status", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 状态检查成功")
            print(f"   记录总数: {data.get('current_record_count', 'N/A')}")
            print(f"   更新次数: {data.get('update_count', 'N/A')}")
        else:
            print(f"   ❌ 状态检查失败")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    print("\n📋 数据更新API测试完成")

if __name__ == "__main__":
    test_data_update_api()
