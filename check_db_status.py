#!/usr/bin/env python3
"""
检查模型库数据库状态
"""

import sqlite3
import sys
from pathlib import Path

def check_model_status():
    """检查模型状态"""
    db_path = "data/model_library.db"
    
    if not Path(db_path).exists():
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='model_states'")
            if not cursor.fetchone():
                print("❌ model_states表不存在")
                return
            
            print("✅ 数据库连接成功")
            print("=" * 50)
            
            # 查询markov_enhanced模型状态
            cursor.execute('''
                SELECT model_id, status, data_ready, features_ready, trained, 
                       training_data_size, last_training_time, last_check_time
                FROM model_states 
                WHERE model_id = ?
            ''', ('markov_enhanced',))
            
            result = cursor.fetchone()
            if result:
                print("🔍 markov_enhanced模型状态:")
                print(f"  模型ID: {result[0]}")
                print(f"  状态: {result[1]}")
                print(f"  数据就绪: {result[2]}")
                print(f"  特征就绪: {result[3]}")
                print(f"  已训练: {result[4]}")
                print(f"  训练数据量: {result[5]}")
                print(f"  最后训练时间: {result[6]}")
                print(f"  最后检查时间: {result[7]}")
            else:
                print("❌ 未找到markov_enhanced模型记录")
            
            print("\n" + "=" * 50)
            
            # 查询所有模型状态
            cursor.execute('''
                SELECT model_id, status, trained, last_training_time
                FROM model_states
                ORDER BY model_id
            ''')
            
            all_results = cursor.fetchall()
            print("📋 所有模型状态:")
            for row in all_results:
                trained_status = "✅" if row[2] else "❌"
                print(f"  {row[0]}: status={row[1]}, trained={trained_status}, last_training={row[3]}")
                
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_model_status()
