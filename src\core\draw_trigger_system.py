#!/usr/bin/env python3
"""
开奖触发系统
Draw Trigger System

监听开奖事件，自动触发验证分析流程
"""

import asyncio
import logging
from datetime import datetime, time
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
import json

from .unified_prediction_storage import UnifiedPredictionStorage, PredictionRecord


@dataclass
class DrawEvent:
    """开奖事件数据模型"""
    period_number: str
    actual_numbers: str
    draw_time: datetime
    source: str = "manual"  # manual, auto, api


@dataclass
class AnalysisResult:
    """分析结果数据模型"""
    period_number: str
    total_predictions: int
    successful_predictions: int
    analysis_details: Dict[str, Any]
    optimization_suggestions: Dict[str, Any]
    timestamp: datetime


class DrawTriggerSystem:
    """开奖触发系统"""
    
    def __init__(self, storage: Optional[UnifiedPredictionStorage] = None):
        """
        初始化开奖触发系统
        
        Args:
            storage: 统一预测存储系统实例
        """
        self.storage = storage or UnifiedPredictionStorage()
        self.logger = logging.getLogger(__name__)
        
        # 事件监听器
        self.event_listeners: List[Callable] = []
        
        # 分析器注册表
        self.analyzers: Dict[str, Callable] = {}
        
        # 优化器注册表
        self.optimizers: Dict[str, Callable] = {}
        
        # 系统状态
        self.is_running = False
        self.last_processed_period = None
        
        # 配置
        self.config = {
            'auto_trigger_enabled': True,
            'analysis_timeout': 300,  # 5分钟
            'max_concurrent_analysis': 3,
            'retry_attempts': 3,
            'notification_enabled': True
        }
    
    def register_analyzer(self, name: str, analyzer_func: Callable):
        """
        注册分析器
        
        Args:
            name: 分析器名称
            analyzer_func: 分析器函数
        """
        self.analyzers[name] = analyzer_func
        self.logger.info(f"注册分析器: {name}")
    
    def register_optimizer(self, name: str, optimizer_func: Callable):
        """
        注册优化器
        
        Args:
            name: 优化器名称
            optimizer_func: 优化器函数
        """
        self.optimizers[name] = optimizer_func
        self.logger.info(f"注册优化器: {name}")
    
    def add_event_listener(self, listener: Callable):
        """
        添加事件监听器
        
        Args:
            listener: 监听器函数
        """
        self.event_listeners.append(listener)
        self.logger.info(f"添加事件监听器: {listener.__name__}")
    
    async def on_draw_announced(self, period_number: str, actual_numbers: str, 
                               source: str = "manual") -> AnalysisResult:
        """
        开奖后自动触发分析流程
        
        Args:
            period_number: 期号
            actual_numbers: 实际开奖号码
            source: 数据源
            
        Returns:
            分析结果
        """
        try:
            self.logger.info(f"🎯 开奖触发：第{period_number}期，实际号码：{actual_numbers}")
            
            # 创建开奖事件
            draw_event = DrawEvent(
                period_number=period_number,
                actual_numbers=actual_numbers,
                draw_time=datetime.now(),
                source=source
            )
            
            # 通知事件监听器
            await self._notify_event_listeners(draw_event)
            
            # 1. 更新预测记录
            self.logger.info("📝 更新预测记录...")
            self.storage.update_prediction_result(period_number, actual_numbers)
            
            # 2. 获取该期所有预测
            self.logger.info("📊 获取预测记录...")
            predictions = self.storage.get_period_predictions(period_number)
            
            if not predictions:
                self.logger.warning(f"期号 {period_number} 没有找到预测记录")
                return AnalysisResult(
                    period_number=period_number,
                    total_predictions=0,
                    successful_predictions=0,
                    analysis_details={},
                    optimization_suggestions={},
                    timestamp=datetime.now()
                )
            
            # 3. 执行深度分析
            self.logger.info("🔍 执行深度分析...")
            analysis_results = await self._execute_analysis(predictions, actual_numbers)
            
            # 4. 生成优化建议
            self.logger.info("💡 生成优化建议...")
            optimization_suggestions = await self._generate_optimization_suggestions(
                analysis_results, predictions
            )
            
            # 5. 保存分析结果
            self.logger.info("💾 保存分析结果...")
            await self._save_analysis_results(period_number, analysis_results, optimization_suggestions)
            
            # 6. 创建分析结果对象
            result = AnalysisResult(
                period_number=period_number,
                total_predictions=len(predictions),
                successful_predictions=sum(1 for p in predictions if p.accuracy_score == 1.0),
                analysis_details=analysis_results,
                optimization_suggestions=optimization_suggestions,
                timestamp=datetime.now()
            )
            
            # 更新最后处理期号
            self.last_processed_period = period_number
            
            self.logger.info(f"✅ 分析完成：{period_number}期")
            return result
            
        except Exception as e:
            self.logger.error(f"开奖分析流程失败: {e}")
            raise
    
    async def start_auto_monitoring(self):
        """启动自动监控"""
        if self.is_running:
            self.logger.warning("自动监控已在运行中")
            return
        
        self.is_running = True
        self.logger.info("🚀 启动自动监控...")
        
        try:
            while self.is_running:
                # 检查是否有新的开奖结果
                await self._check_new_draws()
                
                # 等待一段时间再检查
                await asyncio.sleep(60)  # 每分钟检查一次
                
        except Exception as e:
            self.logger.error(f"自动监控异常: {e}")
        finally:
            self.is_running = False
            self.logger.info("自动监控已停止")
    
    def stop_auto_monitoring(self):
        """停止自动监控"""
        self.is_running = False
        self.logger.info("停止自动监控...")
    
    async def _execute_analysis(self, predictions: List[PredictionRecord], 
                               actual_numbers: str) -> Dict[str, Any]:
        """
        执行深度分析
        
        Args:
            predictions: 预测记录列表
            actual_numbers: 实际开奖号码
            
        Returns:
            分析结果字典
        """
        analysis_results = {}
        
        # 并发执行所有注册的分析器
        analysis_tasks = []
        for analyzer_name, analyzer_func in self.analyzers.items():
            task = asyncio.create_task(
                self._run_analyzer(analyzer_name, analyzer_func, predictions, actual_numbers)
            )
            analysis_tasks.append(task)
        
        # 等待所有分析完成
        if analysis_tasks:
            completed_analyses = await asyncio.gather(*analysis_tasks, return_exceptions=True)
            
            for i, result in enumerate(completed_analyses):
                analyzer_name = list(self.analyzers.keys())[i]
                if isinstance(result, Exception):
                    self.logger.error(f"分析器 {analyzer_name} 执行失败: {result}")
                    analysis_results[analyzer_name] = {"error": str(result)}
                else:
                    analysis_results[analyzer_name] = result
        
        # 如果没有注册的分析器，执行基础分析
        if not self.analyzers:
            analysis_results['basic_analysis'] = self._basic_analysis(predictions, actual_numbers)
        
        return analysis_results
    
    async def _generate_optimization_suggestions(self, analysis_results: Dict[str, Any], 
                                               predictions: List[PredictionRecord]) -> Dict[str, Any]:
        """
        生成优化建议
        
        Args:
            analysis_results: 分析结果
            predictions: 预测记录列表
            
        Returns:
            优化建议字典
        """
        optimization_suggestions = {}
        
        # 并发执行所有注册的优化器
        optimization_tasks = []
        for optimizer_name, optimizer_func in self.optimizers.items():
            task = asyncio.create_task(
                self._run_optimizer(optimizer_name, optimizer_func, analysis_results, predictions)
            )
            optimization_tasks.append(task)
        
        # 等待所有优化器完成
        if optimization_tasks:
            completed_optimizations = await asyncio.gather(*optimization_tasks, return_exceptions=True)
            
            for i, result in enumerate(completed_optimizations):
                optimizer_name = list(self.optimizers.keys())[i]
                if isinstance(result, Exception):
                    self.logger.error(f"优化器 {optimizer_name} 执行失败: {result}")
                    optimization_suggestions[optimizer_name] = {"error": str(result)}
                else:
                    optimization_suggestions[optimizer_name] = result
        
        # 如果没有注册的优化器，生成基础建议
        if not self.optimizers:
            optimization_suggestions['basic_suggestions'] = self._basic_optimization_suggestions(
                analysis_results, predictions
            )
        
        return optimization_suggestions
    
    async def _run_analyzer(self, name: str, analyzer_func: Callable, 
                           predictions: List[PredictionRecord], actual_numbers: str) -> Any:
        """运行单个分析器"""
        try:
            self.logger.debug(f"运行分析器: {name}")
            
            # 如果是异步函数
            if asyncio.iscoroutinefunction(analyzer_func):
                result = await asyncio.wait_for(
                    analyzer_func(predictions, actual_numbers),
                    timeout=self.config['analysis_timeout']
                )
            else:
                # 同步函数在线程池中运行
                result = await asyncio.get_event_loop().run_in_executor(
                    None, analyzer_func, predictions, actual_numbers
                )
            
            self.logger.debug(f"分析器 {name} 完成")
            return result
            
        except asyncio.TimeoutError:
            self.logger.error(f"分析器 {name} 超时")
            raise
        except Exception as e:
            self.logger.error(f"分析器 {name} 执行异常: {e}")
            raise
    
    async def _run_optimizer(self, name: str, optimizer_func: Callable, 
                            analysis_results: Dict[str, Any], 
                            predictions: List[PredictionRecord]) -> Any:
        """运行单个优化器"""
        try:
            self.logger.debug(f"运行优化器: {name}")
            
            # 如果是异步函数
            if asyncio.iscoroutinefunction(optimizer_func):
                result = await asyncio.wait_for(
                    optimizer_func(analysis_results, predictions),
                    timeout=self.config['analysis_timeout']
                )
            else:
                # 同步函数在线程池中运行
                result = await asyncio.get_event_loop().run_in_executor(
                    None, optimizer_func, analysis_results, predictions
                )
            
            self.logger.debug(f"优化器 {name} 完成")
            return result
            
        except asyncio.TimeoutError:
            self.logger.error(f"优化器 {name} 超时")
            raise
        except Exception as e:
            self.logger.error(f"优化器 {name} 执行异常: {e}")
            raise
    
    def _basic_analysis(self, predictions: List[PredictionRecord], actual_numbers: str) -> Dict[str, Any]:
        """基础分析"""
        analysis = {
            'total_predictions': len(predictions),
            'successful_predictions': 0,
            'model_performance': {},
            'accuracy_distribution': {},
            'confidence_analysis': {}
        }
        
        # 统计各模型表现
        for prediction in predictions:
            model_name = prediction.model_name
            if model_name not in analysis['model_performance']:
                analysis['model_performance'][model_name] = {
                    'predictions': 0,
                    'accuracy_score': 0.0,
                    'confidence': 0.0
                }
            
            analysis['model_performance'][model_name]['predictions'] += 1
            analysis['model_performance'][model_name]['accuracy_score'] += prediction.accuracy_score or 0.0
            analysis['model_performance'][model_name]['confidence'] += prediction.confidence
            
            if prediction.accuracy_score == 1.0:
                analysis['successful_predictions'] += 1
        
        # 计算平均值
        for model_name, stats in analysis['model_performance'].items():
            if stats['predictions'] > 0:
                stats['accuracy_score'] /= stats['predictions']
                stats['confidence'] /= stats['predictions']
        
        return analysis
    
    def _basic_optimization_suggestions(self, analysis_results: Dict[str, Any], 
                                      predictions: List[PredictionRecord]) -> Dict[str, Any]:
        """基础优化建议"""
        suggestions = {
            'model_adjustments': [],
            'confidence_improvements': [],
            'general_recommendations': []
        }
        
        # 基于基础分析生成建议
        if 'basic_analysis' in analysis_results:
            basic_analysis = analysis_results['basic_analysis']
            
            # 模型调整建议
            for model_name, performance in basic_analysis.get('model_performance', {}).items():
                if performance['accuracy_score'] < 0.3:
                    suggestions['model_adjustments'].append({
                        'model': model_name,
                        'issue': 'low_accuracy',
                        'suggestion': '考虑调整模型参数或重新训练',
                        'priority': 'high'
                    })
                
                if performance['confidence'] > 0.8 and performance['accuracy_score'] < 0.5:
                    suggestions['confidence_improvements'].append({
                        'model': model_name,
                        'issue': 'overconfidence',
                        'suggestion': '置信度校准需要调整',
                        'priority': 'medium'
                    })
        
        return suggestions
    
    async def _save_analysis_results(self, period_number: str, analysis_results: Dict[str, Any], 
                                   optimization_suggestions: Dict[str, Any]):
        """保存分析结果"""
        try:
            # 获取该期号的预测记录
            predictions = self.storage.get_period_predictions(period_number)
            
            # 为每个预测记录保存分析结果
            for prediction in predictions:
                if prediction.id:
                    # 保存分析结果
                    self.storage.save_analysis_result(
                        prediction.id, 
                        'comprehensive_analysis', 
                        {
                            'analysis_results': analysis_results,
                            'optimization_suggestions': optimization_suggestions,
                            'timestamp': datetime.now().isoformat()
                        }
                    )
            
        except Exception as e:
            self.logger.error(f"保存分析结果失败: {e}")
    
    async def _notify_event_listeners(self, draw_event: DrawEvent):
        """通知事件监听器"""
        for listener in self.event_listeners:
            try:
                if asyncio.iscoroutinefunction(listener):
                    await listener(draw_event)
                else:
                    listener(draw_event)
            except Exception as e:
                self.logger.error(f"事件监听器执行失败: {e}")
    
    async def _check_new_draws(self):
        """检查新的开奖结果"""
        # 这里应该实现检查新开奖结果的逻辑
        # 可以从API、数据库或其他数据源获取
        pass
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'is_running': self.is_running,
            'last_processed_period': self.last_processed_period,
            'registered_analyzers': list(self.analyzers.keys()),
            'registered_optimizers': list(self.optimizers.keys()),
            'event_listeners_count': len(self.event_listeners),
            'config': self.config
        }


# 示例分析器和优化器
async def example_deviation_analyzer(predictions: List[PredictionRecord], actual_numbers: str) -> Dict[str, Any]:
    """示例偏差分析器"""
    return {
        'analyzer_type': 'deviation_analysis',
        'total_predictions': len(predictions),
        'analysis_timestamp': datetime.now().isoformat()
    }


def example_parameter_optimizer(analysis_results: Dict[str, Any], 
                               predictions: List[PredictionRecord]) -> Dict[str, Any]:
    """示例参数优化器"""
    return {
        'optimizer_type': 'parameter_optimization',
        'suggestions_count': 3,
        'optimization_timestamp': datetime.now().isoformat()
    }


if __name__ == "__main__":
    # 测试代码
    async def test_draw_trigger():
        # 创建触发系统
        trigger_system = DrawTriggerSystem()
        
        # 注册示例分析器和优化器
        trigger_system.register_analyzer('deviation_analyzer', example_deviation_analyzer)
        trigger_system.register_optimizer('parameter_optimizer', example_parameter_optimizer)
        
        # 模拟开奖事件
        result = await trigger_system.on_draw_announced("2025194", "123")
        print(f"分析结果: {result}")
        
        # 获取系统状态
        status = trigger_system.get_system_status()
        print(f"系统状态: {status}")
    
    # 运行测试
    asyncio.run(test_draw_trigger())
