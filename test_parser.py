#!/usr/bin/env python3
"""
测试数据解析器
"""

import sys
sys.path.append('src')

from data.parser import DataParser
from data.models import DataValidator

def test_parser():
    """测试数据解析器"""
    print("开始测试数据解析器...")
    
    # 测试单行解析
    test_line = "2025184 2025-07-13 7 9 9 6 3 3 1 1 110880482 0 1040 0 346 0 173"
    print(f"测试行: {test_line}")
    
    result = DataValidator.validate_record_line(test_line)
    print(f"解析结果: {result}")
    
    if result:
        print(f"期号: {result['period']}")
        print(f"日期: {result['date']}")
        print(f"号码: {result['numbers']}")
    
    # 测试完整文件解析
    try:
        with open('data/raw/3d_data_20250714_144231.txt', 'r', encoding='utf-8') as f:
            data = f.read()
        
        print(f"\n读取文件成功，数据长度: {len(data)} 字符")
        
        parser = DataParser()
        records, report = parser.parse_data(data)
        
        print(f"解析完成:")
        print(f"  有效记录: {len(records)}")
        print(f"  质量评分: {report.quality_score}")
        
        if records:
            print(f"  最新3条记录:")
            for record in records[-3:]:
                print(f"    {record.period} {record.date} {record.numbers}")
        
        return True
        
    except Exception as e:
        print(f"解析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_parser()
