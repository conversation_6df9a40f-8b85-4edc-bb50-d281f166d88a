#!/usr/bin/env python3
"""
API数据模型

定义FastAPI接口的请求和响应模型
"""

from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime

# 基础响应模型
class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = True
    message: str = "Success"
    timestamp: datetime = Field(default_factory=datetime.now)

# 健康检查响应
class HealthResponse(BaseResponse):
    """健康检查响应"""
    status: str
    database_records: int
    date_range: str

# 基础统计响应
class BasicStatsResponse(BaseResponse):
    """基础统计响应"""
    total_records: int
    date_range: Dict[str, str]
    sum_value_stats: Dict[str, Any]
    span_value_stats: Dict[str, Any]
    sales_amount_stats: Dict[str, Any]
    query_time_ms: float
    cached: bool

# 频率分析响应
class FrequencyAnalysisResponse(BaseResponse):
    """频率分析响应"""
    position: str
    analysis: Dict[str, Any]
    query_time_ms: float
    cached: bool

# 和值分布响应
class SumDistributionResponse(BaseResponse):
    """和值分布响应"""
    sum_distribution: List[Dict[str, Any]]
    trial_sum_distribution: List[Dict[str, Any]]
    query_time_ms: float
    cached: bool

# 销售额分析响应
class SalesAnalysisResponse(BaseResponse):
    """销售额分析响应"""
    overall: Dict[str, Any]
    yearly: List[Dict[str, Any]]
    query_time_ms: float
    cached: bool

# 数据查询响应
class DataQueryResponse(BaseResponse):
    """数据查询响应"""
    records: List[Dict[str, Any]]
    total_count: int
    query_time_ms: float
    query_params: Dict[str, Any]

# 趋势分析响应
class TrendsAnalysisResponse(BaseResponse):
    """趋势分析响应"""
    period: str
    trends: Dict[str, Any]
    query_time_ms: float
    cached: bool

# 性能统计响应
class PerformanceStatsResponse(BaseResponse):
    """性能统计响应"""
    performance_stats: Dict[str, Any]
    database_info: Dict[str, Any]

# 错误响应
class ErrorResponse(BaseModel):
    """错误响应"""
    success: bool = False
    error: str
    detail: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)

# 请求模型
class DateRangeQuery(BaseModel):
    """日期范围查询"""
    start_date: str = Field(..., description="开始日期 (YYYY-MM-DD)")
    end_date: str = Field(..., description="结束日期 (YYYY-MM-DD)")

class SumRangeQuery(BaseModel):
    """和值范围查询"""
    min_sum: int = Field(..., ge=0, le=27, description="最小和值")
    max_sum: int = Field(..., ge=0, le=27, description="最大和值")

class FrequencyQuery(BaseModel):
    """频率分析查询"""
    position: str = Field("all", description="分析位置: all, hundreds, tens, units")
    use_cache: bool = Field(True, description="是否使用缓存")

class TrendsQuery(BaseModel):
    """趋势分析查询"""
    days: int = Field(30, ge=1, le=365, description="分析天数")
    use_cache: bool = Field(True, description="是否使用缓存")

# 彩票记录模型
class LotteryRecordModel(BaseModel):
    """彩票记录模型"""
    period: str = Field(..., description="期号")
    date: str = Field(..., description="开奖日期")
    numbers: str = Field(..., description="开奖号码")
    trial_numbers: str = Field(..., description="试机号码")
    draw_machine: int = Field(..., description="开奖机器号")
    trial_machine: int = Field(..., description="试机机器号")
    sales_amount: int = Field(..., description="销售额")
    direct_prize: int = Field(..., description="直选奖金")
    group3_prize: int = Field(..., description="组三奖金")
    group6_prize: int = Field(..., description="组六奖金")
    sum_value: int = Field(..., description="和值")
    trial_sum_value: int = Field(..., description="试机号和值")
    span_value: int = Field(..., description="跨度")
    trial_span_value: int = Field(..., description="试机号跨度")

# 统计指标模型
class StatisticsModel(BaseModel):
    """统计指标模型"""
    min_value: float
    max_value: float
    mean_value: float
    median_value: Optional[float] = None
    std_value: Optional[float] = None

# 频率统计模型
class FrequencyStatsModel(BaseModel):
    """频率统计模型"""
    digit: str
    count: int
    percentage: float

# 和值分布模型
class SumDistributionModel(BaseModel):
    """和值分布模型"""
    sum_value: int
    count: int
    percentage: float

# 年度销售模型
class YearlySalesModel(BaseModel):
    """年度销售模型"""
    year: str
    draw_count: int
    total_sales: int
    avg_sales: float

# 趋势数据模型
class TrendDataModel(BaseModel):
    """趋势数据模型"""
    mean: float
    std: Optional[float] = None
    trend_direction: Optional[str] = None  # "up", "down", "stable"

# 性能指标模型
class PerformanceMetricsModel(BaseModel):
    """性能指标模型"""
    queries_count: int
    cache_hits: int
    cache_hit_rate: float
    avg_query_time: float
    total_query_time: float

# 数据库信息模型
class DatabaseInfoModel(BaseModel):
    """数据库信息模型"""
    db_path: str
    db_size_mb: float
    tables: List[str]
    record_counts: Dict[str, int]

# 批量操作响应
class BatchOperationResponse(BaseResponse):
    """批量操作响应"""
    processed_count: int
    success_count: int
    error_count: int
    errors: Optional[List[str]] = None
    operation_time_ms: float

# 导出响应
class ExportResponse(BaseResponse):
    """导出响应"""
    export_files: Dict[str, str]
    export_format: str
    file_size_mb: Optional[float] = None
    export_time_ms: float

# 缓存操作响应
class CacheOperationResponse(BaseResponse):
    """缓存操作响应"""
    operation: str  # "clear", "refresh", "stats"
    affected_items: int
    operation_time_ms: float
    cache_stats: Optional[Dict[str, Any]] = None
