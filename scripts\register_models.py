#!/usr/bin/env python3
"""
注册所有模型到模型库

运行此脚本来注册所有包装的模型
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.model_library.model_registry import ModelRegistry
from src.model_library.wrappers import (
    MarkovModelWrapper,
    FusionModelWrapper,
    TrendModelWrapper,
    DeepLearningModelWrapper
)


def main():
    """主函数"""
    print("🔧 注册模型到模型库...")
    
    try:
        # 创建模型注册中心
        registry = ModelRegistry()
        
        # 创建模型实例
        models = [
            MarkovModelWrapper(),
            FusionModelWrapper(),
            TrendModelWrapper(),
            DeepLearningModelWrapper()
        ]
        
        # 注册所有模型
        registered_count = 0
        for model in models:
            try:
                success = registry.register_model(model)
                if success:
                    print(f"✅ 成功注册模型: {model.name} ({model.model_id})")
                    registered_count += 1
                else:
                    print(f"❌ 注册模型失败: {model.name} ({model.model_id})")
            except Exception as e:
                print(f"❌ 注册模型异常: {model.name} - {e}")
        
        print(f"\n📊 注册统计:")
        print(f"  - 总模型数: {len(models)}")
        print(f"  - 成功注册: {registered_count}")
        print(f"  - 失败数量: {len(models) - registered_count}")
        
        # 验证注册结果
        print(f"\n📋 已注册的模型列表:")
        registered_models = registry.list_models()
        for model_info in registered_models:
            print(f"  - {model_info.name} ({model_info.model_id}) - {model_info.model_type.value}")
        
        print(f"\n🎯 模型注册完成！共注册 {len(registered_models)} 个模型")
        
    except Exception as e:
        print(f"❌ 模型注册失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
