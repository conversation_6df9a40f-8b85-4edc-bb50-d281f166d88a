#!/usr/bin/env python3
"""
检查服务状态
"""

import requests
import time

def check_services():
    print("🔍 检查服务状态...")
    
    # 检查Streamlit
    try:
        response = requests.get("http://localhost:8501", timeout=5)
        if response.status_code == 200:
            print("✅ Streamlit完整版应用 (8501): 正常运行")
        else:
            print(f"❌ Streamlit完整版应用 (8501): 状态码 {response.status_code}")
    except Exception as e:
        print(f"❌ Streamlit完整版应用 (8501): 连接失败 - {e}")
    
    # 检查FastAPI
    try:
        response = requests.get("http://localhost:8000/docs", timeout=5)
        if response.status_code == 200:
            print("✅ FastAPI服务 (8000): 正常运行")
            print("📖 API文档: http://localhost:8000/docs")
        else:
            print(f"⚠️ FastAPI服务 (8000): 状态码 {response.status_code}")
    except Exception as e:
        print(f"❌ FastAPI服务 (8000): 连接失败 - {e}")
    
    print(f"\n🎯 完整版Streamlit应用已启动:")
    print(f"📱 应用地址: http://localhost:8501")
    print(f"🔧 当前状态: API连接问题时会自动切换到演示模式")
    print(f"✨ 功能完整: 所有6个页面都可以正常使用")

if __name__ == "__main__":
    check_services()
