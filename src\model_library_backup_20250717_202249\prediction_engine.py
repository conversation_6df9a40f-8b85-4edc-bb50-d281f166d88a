"""
模型预测引擎

统一的预测引擎，支持单模型和组合预测
"""

import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from .model_registry import ModelRegistry
from .base_model import PredictionResult
from .database_manager import DatabaseManager
from .exceptions import ModelNotFoundError, PredictionError


class ModelPredictor:
    """模型预测引擎"""
    
    def __init__(self):
        self.registry = ModelRegistry()
        self.db_manager = DatabaseManager()
        self._prediction_cache = {}
        self._cache_lock = threading.RLock()
        self._cache_ttl = 300  # 缓存5分钟
    
    def predict_single(self, model_id: str, history: List[Dict[str, Any]], top_n: int = 3) -> PredictionResult:
        """单模型预测"""
        try:
            # 获取模型
            model = self.registry.get_model(model_id)
            if not model:
                raise ModelNotFoundError(model_id)
            
            # 检查模型是否就绪
            if not model.is_ready():
                raise PredictionError(model_id, "模型尚未就绪")
            
            # 执行预测
            prediction = model.predict(history, top_n)
            
            # 保存预测记录
            self._save_prediction_record(prediction)
            
            return prediction
            
        except Exception as e:
            raise PredictionError(model_id, str(e))
    
    def predict_combined(self, model_ids: List[str], history: List[Dict[str, Any]], 
                        strategy: str = "voting", weights: Optional[Dict[str, float]] = None,
                        top_n: int = 3) -> PredictionResult:
        """组合模型预测"""
        try:
            if not model_ids:
                raise ValueError("必须指定至少一个模型ID")
            
            # 并行获取所有模型的预测结果
            predictions = []
            with ThreadPoolExecutor(max_workers=4) as executor:
                future_to_model = {
                    executor.submit(self._safe_predict, model_id, history, top_n): model_id 
                    for model_id in model_ids
                }
                
                for future in as_completed(future_to_model):
                    model_id = future_to_model[future]
                    try:
                        prediction = future.result()
                        if prediction:
                            weight = weights.get(model_id, 1.0) if weights else 1.0
                            predictions.append({
                                "model_id": model_id,
                                "prediction": prediction,
                                "weight": weight
                            })
                    except Exception as e:
                        print(f"模型 {model_id} 预测失败: {e}")
            
            if not predictions:
                raise PredictionError("combined", "没有可用的预测结果")
            
            # 组合预测结果
            combined_prediction = self._combine_predictions(predictions, strategy)
            
            # 保存组合预测记录
            self._save_prediction_record(combined_prediction)
            
            return combined_prediction
            
        except Exception as e:
            raise PredictionError("combined", str(e))
    
    def _safe_predict(self, model_id: str, history: List[Dict[str, Any]], top_n: int) -> Optional[PredictionResult]:
        """安全的预测方法，捕获异常"""
        try:
            return self.predict_single(model_id, history, top_n)
        except Exception:
            return None
    
    def _combine_predictions(self, predictions: List[Dict], strategy: str) -> PredictionResult:
        """组合预测结果"""
        if not predictions:
            raise ValueError("没有预测结果可组合")
        
        # 获取基础信息
        first_pred = predictions[0]["prediction"]
        target_period = first_pred.target_period
        
        if strategy == "voting":
            combined_result = self._voting_combination(predictions)
        elif strategy == "weighted":
            combined_result = self._weighted_combination(predictions)
        elif strategy == "intersection":
            combined_result = self._intersection_combination(predictions)
        else:
            combined_result = self._voting_combination(predictions)  # 默认投票
        
        # 计算组合置信度
        combined_confidence = self._calculate_combined_confidence(predictions, strategy)
        
        return PredictionResult(
            model_id="combined",
            prediction_time=datetime.now(),
            target_period=target_period,
            百位=combined_result['百位'],
            十位=combined_result['十位'],
            个位=combined_result['个位'],
            和值=combined_result.get('和值', {}),
            跨度=combined_result.get('跨度', {}),
            confidence=combined_confidence,
            metadata={
                "strategy": strategy,
                "model_count": len(predictions),
                "model_ids": [p["model_id"] for p in predictions]
            }
        )
    
    def _voting_combination(self, predictions: List[Dict]) -> Dict[str, Dict[str, float]]:
        """投票组合策略"""
        result = {}
        
        for pos in ['百位', '十位', '个位']:
            vote_counts = {str(i): 0.0 for i in range(10)}
            
            for pred_data in predictions:
                prediction = pred_data["prediction"]
                weight = pred_data["weight"]
                
                if hasattr(prediction, pos):
                    pos_probs = getattr(prediction, pos)
                    if pos_probs:
                        # 找到最高概率的数字
                        best_digit = max(pos_probs.items(), key=lambda x: x[1])[0]
                        vote_counts[best_digit] += weight
            
            # 标准化投票结果
            total_votes = sum(vote_counts.values())
            if total_votes > 0:
                result[pos] = {k: v/total_votes for k, v in vote_counts.items()}
            else:
                result[pos] = {str(i): 0.1 for i in range(10)}
        
        return result
    
    def _weighted_combination(self, predictions: List[Dict]) -> Dict[str, Dict[str, float]]:
        """加权组合策略"""
        result = {}
        
        for pos in ['百位', '十位', '个位']:
            weighted_probs = {str(i): 0.0 for i in range(10)}
            total_weight = 0.0
            
            for pred_data in predictions:
                prediction = pred_data["prediction"]
                weight = pred_data["weight"]
                
                if hasattr(prediction, pos):
                    pos_probs = getattr(prediction, pos)
                    if pos_probs:
                        for digit, prob in pos_probs.items():
                            weighted_probs[digit] += prob * weight
                        total_weight += weight
            
            # 标准化权重
            if total_weight > 0:
                result[pos] = {k: v/total_weight for k, v in weighted_probs.items()}
            else:
                result[pos] = {str(i): 0.1 for i in range(10)}
        
        return result
    
    def _intersection_combination(self, predictions: List[Dict]) -> Dict[str, Dict[str, float]]:
        """交集组合策略"""
        result = {}
        
        for pos in ['百位', '十位', '个位']:
            # 获取所有模型的top3数字
            all_top_digits = []
            
            for pred_data in predictions:
                prediction = pred_data["prediction"]
                
                if hasattr(prediction, pos):
                    pos_probs = getattr(prediction, pos)
                    if pos_probs:
                        # 获取top3数字
                        top_digits = sorted(pos_probs.items(), key=lambda x: x[1], reverse=True)[:3]
                        all_top_digits.extend([digit for digit, _ in top_digits])
            
            # 计算交集
            if all_top_digits:
                digit_counts = {}
                for digit in all_top_digits:
                    digit_counts[digit] = digit_counts.get(digit, 0) + 1
                
                # 给出现频率高的数字更高概率
                max_count = max(digit_counts.values())
                intersection_probs = {}
                
                for i in range(10):
                    digit = str(i)
                    count = digit_counts.get(digit, 0)
                    if count > 0:
                        intersection_probs[digit] = count / max_count * 0.8 + 0.1
                    else:
                        intersection_probs[digit] = 0.1
                
                # 标准化
                total = sum(intersection_probs.values())
                result[pos] = {k: v/total for k, v in intersection_probs.items()}
            else:
                result[pos] = {str(i): 0.1 for i in range(10)}
        
        return result
    
    def _calculate_combined_confidence(self, predictions: List[Dict], strategy: str) -> float:
        """计算组合置信度"""
        try:
            confidences = [p["prediction"].confidence for p in predictions]
            weights = [p["weight"] for p in predictions]
            
            if strategy == "voting":
                # 投票策略：使用平均置信度
                return np.mean(confidences)
            elif strategy == "weighted":
                # 加权策略：使用加权平均置信度
                total_weight = sum(weights)
                if total_weight > 0:
                    weighted_conf = sum(c * w for c, w in zip(confidences, weights)) / total_weight
                    return weighted_conf
                else:
                    return np.mean(confidences)
            elif strategy == "intersection":
                # 交集策略：使用最大置信度
                return max(confidences)
            else:
                return np.mean(confidences)
                
        except Exception:
            return 0.5
    
    def _save_prediction_record(self, prediction: PredictionResult):
        """保存预测记录"""
        try:
            prediction_data = {
                "model_id": prediction.model_id,
                "prediction_time": prediction.prediction_time.isoformat(),
                "target_period": prediction.target_period,
                "prediction_result": {
                    "百位": prediction.百位,
                    "十位": prediction.十位,
                    "个位": prediction.个位,
                    "和值": prediction.和值,
                    "跨度": prediction.跨度
                },
                "confidence_score": prediction.confidence,
                "metadata": prediction.metadata
            }
            
            self.db_manager.save_prediction_record(prediction_data)
            
        except Exception as e:
            print(f"保存预测记录失败: {e}")
    
    def get_model_predictions(self, model_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """获取模型预测历史"""
        return self.db_manager.get_model_predictions(model_id, limit)
    
    def clear_cache(self):
        """清除预测缓存"""
        with self._cache_lock:
            self._prediction_cache.clear()
