#!/usr/bin/env python3
"""
简化的AI优化脚本
"""

import os
import sys
from pathlib import Path

def optimize_ai():
    """优化AI配置"""
    print("🤖 开始AI模型优化...")
    
    # 创建模型目录
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    
    cache_dir = models_dir / "cache"
    cache_dir.mkdir(exist_ok=True)
    
    print(f"📁 创建模型目录: {models_dir}")
    
    # 设置离线模式环境变量
    offline_env_vars = {
        'TRANSFORMERS_OFFLINE': '1',
        'HF_DATASETS_OFFLINE': '1',
        'TRANSFORMERS_CACHE': str(cache_dir),
        'HF_HOME': str(cache_dir)
    }
    
    for var, value in offline_env_vars.items():
        os.environ[var] = value
        print(f"✅ 设置环境变量: {var}={value}")
    
    # 创建离线配置文件
    config_content = f"""# AI模型离线配置
import os

# 设置离线模式
os.environ['TRANSFORMERS_OFFLINE'] = '1'
os.environ['HF_DATASETS_OFFLINE'] = '1'
os.environ['TRANSFORMERS_CACHE'] = '{cache_dir}'
os.environ['HF_HOME'] = '{cache_dir}'

# 模型配置
OFFLINE_MODE = True
USE_LIGHTWEIGHT_MODELS = True
"""
    
    config_file = models_dir / "offline_config.py"
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"📝 离线配置文件已创建: {config_file}")
    
    # 测试AI库
    print("🧪 测试AI库...")
    
    try:
        from sklearn.feature_extraction.text import TfidfVectorizer
        vectorizer = TfidfVectorizer()
        test_texts = ["测试文本1", "测试文本2"]
        tfidf_matrix = vectorizer.fit_transform(test_texts)
        print(f"✅ TF-IDF功能正常: {tfidf_matrix.shape}")
    except Exception as e:
        print(f"❌ TF-IDF测试失败: {e}")
    
    try:
        import transformers
        print(f"✅ Transformers库可用: 版本 {transformers.__version__}")
    except Exception as e:
        print(f"❌ Transformers库不可用: {e}")
    
    try:
        from sentence_transformers import SentenceTransformer
        print("✅ Sentence Transformers库可用")
        
        # 尝试加载轻量级模型
        try:
            model = SentenceTransformer('all-MiniLM-L6-v2')
            test_embedding = model.encode(["测试"])
            print(f"✅ 模型加载成功: {test_embedding.shape}")
        except Exception as e:
            print(f"⚠️ 模型加载失败，将使用离线模式: {e}")
            
    except Exception as e:
        print(f"❌ Sentence Transformers库不可用: {e}")
    
    print("🎉 AI模型优化完成！")
    return True

if __name__ == "__main__":
    optimize_ai()
