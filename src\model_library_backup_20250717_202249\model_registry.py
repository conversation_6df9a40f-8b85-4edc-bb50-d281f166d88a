"""
模型注册中心

负责模型的注册、发现、管理和卸载
"""

import sqlite3
import json
import threading
from pathlib import Path
from typing import Dict, List, Optional, Type
from datetime import datetime

from .base_model import BaseModel, ModelInfo, ModelStatusInfo, ModelType
from .exceptions import ModelRegistrationError, ModelNotFoundError


class ModelRegistry:
    """模型注册中心
    
    管理所有模型的注册、发现和生命周期
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, db_path: str = "data/lottery.db"):
        """单例模式"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, db_path: str = "data/lottery.db"):
        if hasattr(self, '_initialized'):
            return
            
        self.db_path = Path(db_path)
        self._models: Dict[str, BaseModel] = {}
        self._model_classes: Dict[str, Type[BaseModel]] = {}
        self._lock = threading.RLock()
        
        # 确保数据库目录存在
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库表
        self._init_database()
        self._initialized = True
    
    def _init_database(self):
        """初始化数据库表"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建模型库表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS model_library (
                    model_id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    model_type TEXT NOT NULL,
                    version TEXT DEFAULT '1.0.0',
                    author TEXT DEFAULT 'Augment Agent',
                    created_at TEXT,
                    updated_at TEXT,
                    data_requirements TEXT,
                    feature_engineering TEXT,
                    parameters TEXT,
                    is_active BOOLEAN DEFAULT TRUE
                )
            ''')
            
            # 创建模型状态表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS model_states (
                    model_id TEXT PRIMARY KEY,
                    status TEXT NOT NULL,
                    data_ready BOOLEAN DEFAULT FALSE,
                    features_ready BOOLEAN DEFAULT FALSE,
                    trained BOOLEAN DEFAULT FALSE,
                    up_to_date BOOLEAN DEFAULT FALSE,
                    training_data_size INTEGER DEFAULT 0,
                    last_training_time TEXT,
                    last_check_time TEXT,
                    error_message TEXT,
                    FOREIGN KEY (model_id) REFERENCES model_library(model_id)
                )
            ''')
            
            conn.commit()
    
    def register_model(self, model: BaseModel) -> bool:
        """注册模型
        
        Args:
            model: 模型实例
            
        Returns:
            bool: 注册是否成功
        """
        with self._lock:
            try:
                model_info = model.get_info()
                
                # 检查模型ID是否已存在
                if model_info.model_id in self._models:
                    raise ModelRegistrationError(
                        model_info.model_id, 
                        "模型ID已存在"
                    )
                
                # 保存到内存
                self._models[model_info.model_id] = model
                self._model_classes[model_info.model_id] = type(model)
                
                # 保存到数据库
                self._save_model_info(model_info)
                self._save_model_status(model.get_status())
                
                return True
                
            except Exception as e:
                raise ModelRegistrationError(
                    model.model_id if hasattr(model, 'model_id') else 'unknown',
                    str(e)
                )
    
    def unregister_model(self, model_id: str) -> bool:
        """注销模型
        
        Args:
            model_id: 模型ID
            
        Returns:
            bool: 注销是否成功
        """
        with self._lock:
            if model_id not in self._models:
                return False
            
            # 从内存中移除
            del self._models[model_id]
            if model_id in self._model_classes:
                del self._model_classes[model_id]
            
            # 从数据库中标记为非活跃
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    'UPDATE model_library SET is_active = FALSE WHERE model_id = ?',
                    (model_id,)
                )
                conn.commit()
            
            return True
    
    def get_model(self, model_id: str) -> Optional[BaseModel]:
        """获取模型实例
        
        Args:
            model_id: 模型ID
            
        Returns:
            BaseModel: 模型实例，如果不存在返回None
        """
        with self._lock:
            return self._models.get(model_id)
    
    def get_model_info(self, model_id: str) -> Optional[ModelInfo]:
        """获取模型信息
        
        Args:
            model_id: 模型ID
            
        Returns:
            ModelInfo: 模型信息，如果不存在返回None
        """
        model = self.get_model(model_id)
        if model:
            return model.get_info()
        
        # 从数据库获取
        return self._load_model_info(model_id)
    
    def get_model_status(self, model_id: str) -> Optional[ModelStatusInfo]:
        """获取模型状态
        
        Args:
            model_id: 模型ID
            
        Returns:
            ModelStatusInfo: 模型状态，如果不存在返回None
        """
        model = self.get_model(model_id)
        if model:
            return model.get_status()
        
        # 从数据库获取
        return self._load_model_status(model_id)
    
    def list_models(self, active_only: bool = True) -> List[ModelInfo]:
        """列出所有模型
        
        Args:
            active_only: 是否只返回活跃模型
            
        Returns:
            List[ModelInfo]: 模型信息列表
        """
        models = []
        
        # 从内存中获取
        with self._lock:
            for model in self._models.values():
                models.append(model.get_info())
        
        # 从数据库中获取其他模型
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            query = 'SELECT * FROM model_library'
            if active_only:
                query += ' WHERE is_active = TRUE'
            
            cursor.execute(query)
            rows = cursor.fetchall()
            
            for row in rows:
                model_id = row[0]
                if model_id not in self._models:
                    model_info = self._row_to_model_info(row)
                    if model_info:
                        models.append(model_info)
        
        return models
    
    def update_model_status(self, model_id: str, status_info: ModelStatusInfo):
        """更新模型状态
        
        Args:
            model_id: 模型ID
            status_info: 状态信息
        """
        self._save_model_status(status_info)
    
    def _save_model_info(self, model_info: ModelInfo):
        """保存模型信息到数据库"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO model_library 
                (model_id, name, description, model_type, version, author,
                 created_at, updated_at, data_requirements, feature_engineering,
                 parameters, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                model_info.model_id,
                model_info.name,
                model_info.description,
                model_info.model_type.value,
                model_info.version,
                model_info.author,
                model_info.created_at.isoformat(),
                model_info.updated_at.isoformat(),
                json.dumps(model_info.data_requirements),
                json.dumps(model_info.feature_engineering),
                json.dumps(model_info.parameters),
                model_info.is_active
            ))
            conn.commit()
    
    def _save_model_status(self, status_info: ModelStatusInfo):
        """保存模型状态到数据库"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO model_states
                (model_id, status, data_ready, features_ready, trained,
                 up_to_date, training_data_size, last_training_time,
                 last_check_time, error_message)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                status_info.model_id,
                status_info.status.value,
                status_info.data_ready,
                status_info.features_ready,
                status_info.trained,
                status_info.up_to_date,
                status_info.training_data_size,
                status_info.last_training_time.isoformat() if status_info.last_training_time else None,
                status_info.last_check_time.isoformat(),
                status_info.error_message
            ))
            conn.commit()
    
    def _load_model_info(self, model_id: str) -> Optional[ModelInfo]:
        """从数据库加载模型信息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM model_library WHERE model_id = ?', (model_id,))
            row = cursor.fetchone()
            
            if row:
                return self._row_to_model_info(row)
        
        return None
    
    def _load_model_status(self, model_id: str) -> Optional[ModelStatusInfo]:
        """从数据库加载模型状态"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM model_states WHERE model_id = ?', (model_id,))
            row = cursor.fetchone()
            
            if row:
                return ModelStatusInfo(
                    model_id=row[0],
                    status=ModelStatus(row[1]),
                    data_ready=bool(row[2]),
                    features_ready=bool(row[3]),
                    trained=bool(row[4]),
                    up_to_date=bool(row[5]),
                    training_data_size=row[6],
                    last_training_time=datetime.fromisoformat(row[7]) if row[7] else None,
                    last_check_time=datetime.fromisoformat(row[8]),
                    error_message=row[9]
                )
        
        return None
    
    def _row_to_model_info(self, row) -> Optional[ModelInfo]:
        """将数据库行转换为ModelInfo对象"""
        try:
            return ModelInfo(
                model_id=row[0],
                name=row[1],
                description=row[2],
                model_type=ModelType(row[3]),
                version=row[4],
                author=row[5],
                created_at=datetime.fromisoformat(row[6]),
                updated_at=datetime.fromisoformat(row[7]),
                data_requirements=json.loads(row[8]) if row[8] else {},
                feature_engineering=json.loads(row[9]) if row[9] else {},
                parameters=json.loads(row[10]) if row[10] else {},
                is_active=bool(row[11])
            )
        except Exception:
            return None
