#!/usr/bin/env python3
"""
检查trend_analyzer模型的数据库状态
"""

import sqlite3
import sys
from pathlib import Path

def check_trend_analyzer_status():
    """检查trend_analyzer模型状态"""
    db_path = "data/model_library.db"
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='model_states'
            """)
            if not cursor.fetchone():
                print("❌ model_states表不存在")
                return
            
            # 查询trend_analyzer状态
            cursor.execute("""
                SELECT * FROM model_states 
                WHERE model_id = ?
            """, ('trend_analyzer',))
            
            result = cursor.fetchone()
            if result:
                print("📊 当前trend_analyzer状态:")
                print(f"  🆔 model_id: {result[0]}")
                print(f"  📈 status: {result[1]}")
                print(f"  💾 data_ready: {result[2]}")
                print(f"  🔧 features_ready: {result[3]}")
                print(f"  🎯 trained: {result[4]}")
                print(f"  🔄 up_to_date: {result[5]}")
                print(f"  📏 training_data_size: {result[6]}")
                print(f"  ⏰ last_training_time: {result[7]}")
                print(f"  🕐 last_check_time: {result[8]}")
                print(f"  ❗ error_message: {result[9]}")
            else:
                print("❌ 数据库中没有找到trend_analyzer的状态记录")
                
            # 查看所有模型状态
            print("\n📋 所有模型状态:")
            cursor.execute("SELECT model_id, status, trained FROM model_states")
            all_models = cursor.fetchall()
            for model in all_models:
                status_icon = "✅" if model[2] else "❌"
                print(f"  {status_icon} {model[0]}: {model[1]} (trained: {model[2]})")
                
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_trend_analyzer_status()
