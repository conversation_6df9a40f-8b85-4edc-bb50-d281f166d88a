#!/usr/bin/env python3
"""
性能基准测试

验证数据库查询响应时间、页面加载时间、WebSocket延迟等性能指标
"""

import asyncio
import json
import logging
import statistics
import sys
import time
from datetime import datetime
from typing import Dict, List, Any, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 尝试导入所需库
try:
    import requests
    HAS_REQUESTS = True
except ImportError:
    HAS_REQUESTS = False
    logger.warning("requests库未安装，将跳过API性能测试")

try:
    import websockets
    HAS_WEBSOCKETS = True
except ImportError:
    HAS_WEBSOCKETS = False
    logger.warning("websockets库未安装，将跳过WebSocket性能测试")

class PerformanceBenchmarkTester:
    """性能基准测试器"""
    
    def __init__(self):
        self.api_base_url = "http://127.0.0.1:8000"
        self.websocket_url = "ws://127.0.0.1:8000"
        self.session = None
        
        # 性能基准阈值（毫秒）
        self.thresholds = {
            "api_response": 2000,      # API响应时间 < 2秒
            "database_query": 1000,    # 数据库查询 < 1秒
            "websocket_latency": 100,  # WebSocket延迟 < 100ms
            "health_check": 500,       # 健康检查 < 500ms
            "concurrent_requests": 5000 # 并发请求 < 5秒
        }
        
        if HAS_REQUESTS:
            self.session = requests.Session()
            self.session.timeout = 30
    
    def measure_time(self, func):
        """测量函数执行时间的装饰器"""
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            execution_time = (end_time - start_time) * 1000  # 转换为毫秒
            return result, execution_time
        return wrapper
    
    async def measure_async_time(self, coro):
        """测量异步函数执行时间"""
        start_time = time.time()
        result = await coro
        end_time = time.time()
        execution_time = (end_time - start_time) * 1000  # 转换为毫秒
        return result, execution_time
    
    async def test_api_response_times(self) -> Dict[str, Any]:
        """测试API响应时间"""
        logger.info("🌐 测试API响应时间...")
        
        test_result = {
            "test_name": "api_response_times",
            "tests": [],
            "summary": {},
            "passed": True
        }
        
        if not HAS_REQUESTS:
            test_result["tests"].append({
                "endpoint": "all",
                "note": "跳过测试 - requests库未安装",
                "passed": True
            })
            return test_result
        
        # 定义测试端点
        endpoints = [
            ("/api/v1/health/", "基本健康检查"),
            ("/api/v1/health/ping", "Ping检查"),
            ("/api/v1/health/summary", "健康摘要"),
            ("/api/v1/data/basic-stats", "基础统计"),
            ("/api/v1/prediction/status", "预测状态")
        ]
        
        all_times = []
        
        for endpoint, description in endpoints:
            times = []
            errors = 0
            
            # 每个端点测试5次
            for i in range(5):
                try:
                    start_time = time.time()
                    response = self.session.get(f"{self.api_base_url}{endpoint}")
                    end_time = time.time()
                    
                    response_time = (end_time - start_time) * 1000
                    times.append(response_time)
                    all_times.append(response_time)
                    
                    # 短暂延迟避免过于频繁的请求
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    errors += 1
                    logger.warning(f"请求失败 {endpoint}: {e}")
            
            if times:
                avg_time = statistics.mean(times)
                min_time = min(times)
                max_time = max(times)
                median_time = statistics.median(times)
                
                passed = avg_time < self.thresholds["api_response"]
                
                test_result["tests"].append({
                    "endpoint": endpoint,
                    "description": description,
                    "avg_time_ms": round(avg_time, 2),
                    "min_time_ms": round(min_time, 2),
                    "max_time_ms": round(max_time, 2),
                    "median_time_ms": round(median_time, 2),
                    "errors": errors,
                    "threshold_ms": self.thresholds["api_response"],
                    "passed": passed
                })
                
                if not passed:
                    test_result["passed"] = False
            else:
                test_result["tests"].append({
                    "endpoint": endpoint,
                    "description": description,
                    "error": "所有请求都失败",
                    "passed": False
                })
                test_result["passed"] = False
        
        # 计算总体统计
        if all_times:
            test_result["summary"] = {
                "total_requests": len(all_times),
                "avg_response_time_ms": round(statistics.mean(all_times), 2),
                "min_response_time_ms": round(min(all_times), 2),
                "max_response_time_ms": round(max(all_times), 2),
                "median_response_time_ms": round(statistics.median(all_times), 2),
                "p95_response_time_ms": round(statistics.quantiles(all_times, n=20)[18], 2) if len(all_times) >= 20 else "N/A"
            }
        
        return test_result
    
    async def test_database_query_performance(self) -> Dict[str, Any]:
        """测试数据库查询性能"""
        logger.info("🗄️  测试数据库查询性能...")
        
        test_result = {
            "test_name": "database_query_performance",
            "tests": [],
            "summary": {},
            "passed": True
        }
        
        if not HAS_REQUESTS:
            test_result["tests"].append({
                "query_type": "all",
                "note": "跳过测试 - requests库未安装",
                "passed": True
            })
            return test_result
        
        # 定义查询测试
        query_tests = [
            ("/api/v1/data/basic-stats", "基础统计查询"),
            ("/api/v1/data/recent?limit=100", "最近数据查询"),
            ("/api/v1/data/by-date-range?start_date=2024-01-01&end_date=2024-12-31&limit=1000", "日期范围查询")
        ]
        
        all_times = []
        
        for endpoint, description in query_tests:
            times = []
            errors = 0
            
            # 每个查询测试3次
            for i in range(3):
                try:
                    start_time = time.time()
                    response = self.session.get(f"{self.api_base_url}{endpoint}")
                    end_time = time.time()
                    
                    if response.status_code == 200:
                        response_time = (end_time - start_time) * 1000
                        times.append(response_time)
                        all_times.append(response_time)
                    else:
                        errors += 1
                    
                    await asyncio.sleep(0.2)
                    
                except Exception as e:
                    errors += 1
                    logger.warning(f"查询失败 {endpoint}: {e}")
            
            if times:
                avg_time = statistics.mean(times)
                passed = avg_time < self.thresholds["database_query"]
                
                test_result["tests"].append({
                    "query_type": description,
                    "endpoint": endpoint,
                    "avg_time_ms": round(avg_time, 2),
                    "min_time_ms": round(min(times), 2),
                    "max_time_ms": round(max(times), 2),
                    "errors": errors,
                    "threshold_ms": self.thresholds["database_query"],
                    "passed": passed
                })
                
                if not passed:
                    test_result["passed"] = False
            else:
                test_result["tests"].append({
                    "query_type": description,
                    "endpoint": endpoint,
                    "error": "所有查询都失败",
                    "passed": False
                })
                test_result["passed"] = False
        
        # 计算总体统计
        if all_times:
            test_result["summary"] = {
                "total_queries": len(all_times),
                "avg_query_time_ms": round(statistics.mean(all_times), 2),
                "min_query_time_ms": round(min(all_times), 2),
                "max_query_time_ms": round(max(all_times), 2)
            }
        
        return test_result
    
    async def test_websocket_latency(self) -> Dict[str, Any]:
        """测试WebSocket延迟"""
        logger.info("🔌 测试WebSocket延迟...")
        
        test_result = {
            "test_name": "websocket_latency",
            "tests": [],
            "summary": {},
            "passed": True
        }
        
        if not HAS_WEBSOCKETS:
            test_result["tests"].append({
                "websocket_type": "all",
                "note": "跳过测试 - websockets库未安装",
                "passed": True
            })
            return test_result
        
        # 测试Bug检测WebSocket
        try:
            uri = f"{self.websocket_url}/ws/bug-detection"
            latencies = []
            
            async with websockets.connect(uri, timeout=10) as websocket:
                # 测试10次ping-pong
                for i in range(10):
                    start_time = time.time()
                    
                    ping_msg = {"type": "ping", "timestamp": start_time}
                    await websocket.send(json.dumps(ping_msg))
                    
                    response = await asyncio.wait_for(websocket.recv(), timeout=5)
                    end_time = time.time()
                    
                    latency = (end_time - start_time) * 1000
                    latencies.append(latency)
                    
                    await asyncio.sleep(0.1)
            
            if latencies:
                avg_latency = statistics.mean(latencies)
                passed = avg_latency < self.thresholds["websocket_latency"]
                
                test_result["tests"].append({
                    "websocket_type": "bug_detection",
                    "avg_latency_ms": round(avg_latency, 2),
                    "min_latency_ms": round(min(latencies), 2),
                    "max_latency_ms": round(max(latencies), 2),
                    "samples": len(latencies),
                    "threshold_ms": self.thresholds["websocket_latency"],
                    "passed": passed
                })
                
                if not passed:
                    test_result["passed"] = False
        
        except Exception as e:
            logger.warning(f"WebSocket延迟测试失败: {e}")
            test_result["tests"].append({
                "websocket_type": "bug_detection",
                "error": str(e),
                "passed": False
            })
            test_result["passed"] = False
        
        return test_result
    
    async def test_concurrent_performance(self) -> Dict[str, Any]:
        """测试并发性能"""
        logger.info("⚡ 测试并发性能...")
        
        test_result = {
            "test_name": "concurrent_performance",
            "tests": [],
            "summary": {},
            "passed": True
        }
        
        if not HAS_REQUESTS:
            test_result["tests"].append({
                "test_type": "concurrent",
                "note": "跳过测试 - requests库未安装",
                "passed": True
            })
            return test_result
        
        async def make_request(session, url):
            """发起单个请求"""
            try:
                start_time = time.time()
                response = session.get(url)
                end_time = time.time()
                return {
                    "success": response.status_code == 200,
                    "response_time": (end_time - start_time) * 1000,
                    "status_code": response.status_code
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": str(e),
                    "response_time": 0
                }
        
        # 测试并发请求
        concurrent_levels = [5, 10, 20]
        
        for concurrent_count in concurrent_levels:
            try:
                start_time = time.time()
                
                # 创建并发任务
                tasks = []
                for i in range(concurrent_count):
                    # 使用不同的端点避免缓存影响
                    url = f"{self.api_base_url}/api/v1/health/ping"
                    task = asyncio.create_task(
                        asyncio.to_thread(make_request, self.session, url)
                    )
                    tasks.append(task)
                
                # 等待所有任务完成
                results = await asyncio.gather(*tasks, return_exceptions=True)
                end_time = time.time()
                
                total_time = (end_time - start_time) * 1000
                successful_requests = sum(1 for r in results if isinstance(r, dict) and r.get("success", False))
                response_times = [r.get("response_time", 0) for r in results if isinstance(r, dict) and r.get("success", False)]
                
                passed = total_time < self.thresholds["concurrent_requests"]
                
                test_result["tests"].append({
                    "concurrent_requests": concurrent_count,
                    "total_time_ms": round(total_time, 2),
                    "successful_requests": successful_requests,
                    "success_rate": successful_requests / concurrent_count,
                    "avg_response_time_ms": round(statistics.mean(response_times), 2) if response_times else 0,
                    "threshold_ms": self.thresholds["concurrent_requests"],
                    "passed": passed
                })
                
                if not passed:
                    test_result["passed"] = False
                
                # 测试间隔
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.warning(f"并发测试失败 (并发数: {concurrent_count}): {e}")
                test_result["tests"].append({
                    "concurrent_requests": concurrent_count,
                    "error": str(e),
                    "passed": False
                })
                test_result["passed"] = False
        
        return test_result
    
    async def test_health_check_performance(self) -> Dict[str, Any]:
        """测试健康检查性能"""
        logger.info("🏥 测试健康检查性能...")
        
        test_result = {
            "test_name": "health_check_performance",
            "tests": [],
            "summary": {},
            "passed": True
        }
        
        if not HAS_REQUESTS:
            test_result["tests"].append({
                "check_type": "all",
                "note": "跳过测试 - requests库未安装",
                "passed": True
            })
            return test_result
        
        # 测试不同类型的健康检查
        health_checks = [
            ("/api/v1/health/", "基本健康检查"),
            ("/api/v1/health/detailed", "详细健康检查"),
            ("/api/v1/health/components/database", "数据库组件检查"),
            ("/api/v1/health/components/system_resources", "系统资源检查")
        ]
        
        for endpoint, description in health_checks:
            times = []
            errors = 0
            
            # 每个检查测试3次
            for i in range(3):
                try:
                    start_time = time.time()
                    response = self.session.get(f"{self.api_base_url}{endpoint}")
                    end_time = time.time()
                    
                    response_time = (end_time - start_time) * 1000
                    times.append(response_time)
                    
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    errors += 1
                    logger.warning(f"健康检查失败 {endpoint}: {e}")
            
            if times:
                avg_time = statistics.mean(times)
                passed = avg_time < self.thresholds["health_check"]
                
                test_result["tests"].append({
                    "check_type": description,
                    "endpoint": endpoint,
                    "avg_time_ms": round(avg_time, 2),
                    "min_time_ms": round(min(times), 2),
                    "max_time_ms": round(max(times), 2),
                    "errors": errors,
                    "threshold_ms": self.thresholds["health_check"],
                    "passed": passed
                })
                
                if not passed:
                    test_result["passed"] = False
            else:
                test_result["tests"].append({
                    "check_type": description,
                    "endpoint": endpoint,
                    "error": "所有检查都失败",
                    "passed": False
                })
                test_result["passed"] = False
        
        return test_result
    
    async def run_performance_tests(self) -> Dict[str, Any]:
        """运行所有性能测试"""
        logger.info("🚀 开始性能基准测试套件")
        
        test_start_time = time.time()
        
        # 定义测试套件
        test_suite = [
            ("API响应时间", self.test_api_response_times),
            ("数据库查询性能", self.test_database_query_performance),
            ("WebSocket延迟", self.test_websocket_latency),
            ("健康检查性能", self.test_health_check_performance),
            ("并发性能", self.test_concurrent_performance)
        ]
        
        results = {}
        overall_passed = True
        
        for test_name, test_func in test_suite:
            try:
                logger.info(f"执行测试: {test_name}")
                result = await test_func()
                results[test_name] = result
                
                if not result.get("passed", False):
                    overall_passed = False
                
                # 测试间隔
                await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(f"测试 '{test_name}' 执行失败: {e}")
                results[test_name] = {
                    "test_name": test_name.lower().replace(" ", "_"),
                    "passed": False,
                    "error": str(e),
                    "tests": []
                }
                overall_passed = False
        
        total_execution_time = int((time.time() - test_start_time) * 1000)
        
        # 生成性能报告
        report = {
            "timestamp": datetime.now().isoformat(),
            "overall_passed": overall_passed,
            "total_execution_time_ms": total_execution_time,
            "thresholds": self.thresholds,
            "test_results": results,
            "summary": self._generate_performance_summary(results)
        }
        
        # 输出测试结果
        self._print_performance_results(report)
        
        return report
    
    def _generate_performance_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成性能摘要"""
        total_tests = 0
        passed_tests = 0
        
        for test_name, result in results.items():
            tests = result.get("tests", [])
            total_tests += len(tests)
            
            for test in tests:
                if test.get("passed", False):
                    passed_tests += 1
        
        return {
            "total_test_categories": len(results),
            "passed_test_categories": sum(1 for r in results.values() if r.get("passed", False)),
            "total_individual_tests": total_tests,
            "passed_individual_tests": passed_tests,
            "performance_score": passed_tests / total_tests if total_tests > 0 else 0
        }
    
    def _print_performance_results(self, report: Dict[str, Any]):
        """打印性能测试结果"""
        logger.info("\n" + "="*60)
        logger.info("⚡ 性能基准测试结果")
        logger.info("="*60)
        
        summary = report["summary"]
        logger.info(f"总体状态: {'✅ 通过' if report['overall_passed'] else '❌ 未达标'}")
        logger.info(f"执行时间: {report['total_execution_time_ms']}ms")
        logger.info(f"测试类别: {summary['passed_test_categories']}/{summary['total_test_categories']} 通过")
        logger.info(f"单项测试: {summary['passed_individual_tests']}/{summary['total_individual_tests']} 通过")
        logger.info(f"性能得分: {summary['performance_score']:.1%}")
        
        logger.info("\n性能阈值:")
        for metric, threshold in report["thresholds"].items():
            logger.info(f"  {metric}: {threshold}ms")
        
        logger.info("\n详细结果:")
        for test_name, result in report["test_results"].items():
            status = "✅" if result.get("passed", False) else "❌"
            logger.info(f"{status} {test_name}")
            
            # 显示关键性能指标
            for test in result.get("tests", []):
                if "avg_time_ms" in test:
                    avg_time = test["avg_time_ms"]
                    threshold = test.get("threshold_ms", 0)
                    status_icon = "✅" if test.get("passed", False) else "❌"
                    logger.info(f"   {status_icon} {test.get('description', test.get('endpoint', 'unknown'))}: {avg_time}ms (阈值: {threshold}ms)")
    
    def close(self):
        """关闭测试器"""
        if self.session:
            self.session.close()

async def main():
    """主函数"""
    tester = PerformanceBenchmarkTester()
    
    try:
        report = await tester.run_performance_tests()
        
        # 保存性能报告
        from pathlib import Path
        report_file = Path("performance_benchmark_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"\n📄 性能报告已保存到: {report_file}")
        
        # 返回退出码
        if report["overall_passed"]:
            return 0
        else:
            return 1
    finally:
        tester.close()

if __name__ == "__main__":
    if not HAS_REQUESTS:
        logger.warning("⚠️  requests库未安装，大部分性能测试将被跳过")
        logger.info("安装命令: pip install requests")
    
    if not HAS_WEBSOCKETS:
        logger.warning("⚠️  websockets库未安装，WebSocket性能测试将被跳过")
        logger.info("安装命令: pip install websockets")
    
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
