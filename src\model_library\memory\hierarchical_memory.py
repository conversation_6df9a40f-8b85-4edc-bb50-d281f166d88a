"""
分层训练记忆数据库
实现多层次存储架构（Redis+SQLite+PostgreSQL），知识提取和图谱存储
"""

import asyncio
import sqlite3
import json
import pickle
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import asdict
from collections import defaultdict
import hashlib
import logging

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

try:
    import psycopg2
    from psycopg2.extras import RealDictCursor
    POSTGRESQL_AVAILABLE = True
except ImportError:
    POSTGRESQL_AVAILABLE = False

from .training_record import TrainingRecord, Knowledge, TrainingStatus, KnowledgeType


class MemoryLayer:
    """内存层基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(f"MemoryLayer.{name}")
    
    async def store(self, record: TrainingRecord) -> bool:
        """存储训练记录"""
        raise NotImplementedError
    
    async def retrieve(self, query: Dict[str, Any]) -> List[TrainingRecord]:
        """检索训练记录"""
        raise NotImplementedError
    
    async def store_knowledge(self, knowledge: Knowledge) -> bool:
        """存储知识"""
        raise NotImplementedError
    
    async def retrieve_knowledge(self, query: Dict[str, Any]) -> List[Knowledge]:
        """检索知识"""
        raise NotImplementedError


class RedisMemoryLayer(MemoryLayer):
    """Redis实时缓存层"""
    
    def __init__(self, host: str = "localhost", port: int = 6379, db: int = 0):
        super().__init__("Redis")
        self.host = host
        self.port = port
        self.db = db
        self.redis_client = None
        self.ttl = 3600  # 1小时过期
        
    async def connect(self):
        """连接Redis"""
        if not REDIS_AVAILABLE:
            self.logger.warning("Redis不可用，使用内存模拟")
            self.redis_client = {}
            return
        
        try:
            self.redis_client = redis.Redis(
                host=self.host, 
                port=self.port, 
                db=self.db,
                decode_responses=False
            )
            # 测试连接
            self.redis_client.ping()
            self.logger.info("Redis连接成功")
        except Exception as e:
            self.logger.error(f"Redis连接失败: {e}")
            self.redis_client = {}
    
    async def store(self, record: TrainingRecord) -> bool:
        """存储到Redis缓存"""
        try:
            if not self.redis_client:
                await self.connect()
            
            key = f"training_record:{record.record_id}"
            data = pickle.dumps(record)
            
            if isinstance(self.redis_client, dict):
                # 内存模拟
                self.redis_client[key] = data
            else:
                self.redis_client.setex(key, self.ttl, data)
            
            self.logger.debug(f"训练记录已存储到Redis: {record.record_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Redis存储失败: {e}")
            return False
    
    async def retrieve(self, query: Dict[str, Any]) -> List[TrainingRecord]:
        """从Redis检索"""
        try:
            if not self.redis_client:
                await self.connect()
            
            records = []
            
            if isinstance(self.redis_client, dict):
                # 内存模拟
                for key, data in self.redis_client.items():
                    if key.startswith("training_record:"):
                        try:
                            record = pickle.loads(data)
                            if self._match_query(record, query):
                                records.append(record)
                        except Exception:
                            continue
            else:
                # 真实Redis
                pattern = "training_record:*"
                for key in self.redis_client.scan_iter(match=pattern):
                    try:
                        data = self.redis_client.get(key)
                        if data:
                            record = pickle.loads(data)
                            if self._match_query(record, query):
                                records.append(record)
                    except Exception:
                        continue
            
            return records
            
        except Exception as e:
            self.logger.error(f"Redis检索失败: {e}")
            return []
    
    def _match_query(self, record: TrainingRecord, query: Dict[str, Any]) -> bool:
        """匹配查询条件"""
        for key, value in query.items():
            if hasattr(record, key):
                if getattr(record, key) != value:
                    return False
        return True
    
    async def store_knowledge(self, knowledge: Knowledge) -> bool:
        """存储知识到Redis"""
        try:
            if not self.redis_client:
                await self.connect()
            
            key = f"knowledge:{knowledge.knowledge_id}"
            data = pickle.dumps(knowledge)
            
            if isinstance(self.redis_client, dict):
                self.redis_client[key] = data
            else:
                self.redis_client.setex(key, self.ttl, data)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Redis知识存储失败: {e}")
            return False
    
    async def retrieve_knowledge(self, query: Dict[str, Any]) -> List[Knowledge]:
        """从Redis检索知识"""
        try:
            if not self.redis_client:
                await self.connect()
            
            knowledge_list = []
            
            if isinstance(self.redis_client, dict):
                for key, data in self.redis_client.items():
                    if key.startswith("knowledge:"):
                        try:
                            knowledge = pickle.loads(data)
                            knowledge_list.append(knowledge)
                        except Exception:
                            continue
            else:
                pattern = "knowledge:*"
                for key in self.redis_client.scan_iter(match=pattern):
                    try:
                        data = self.redis_client.get(key)
                        if data:
                            knowledge = pickle.loads(data)
                            knowledge_list.append(knowledge)
                    except Exception:
                        continue
            
            return knowledge_list
            
        except Exception as e:
            self.logger.error(f"Redis知识检索失败: {e}")
            return []


class SQLiteMemoryLayer(MemoryLayer):
    """SQLite短期存储层"""
    
    def __init__(self, db_path: str = "training_memory.db"):
        super().__init__("SQLite")
        self.db_path = db_path
        self.connection = None
        
    async def connect(self):
        """连接SQLite数据库"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row
            await self._create_tables()
            self.logger.info("SQLite连接成功")
        except Exception as e:
            self.logger.error(f"SQLite连接失败: {e}")
    
    async def _create_tables(self):
        """创建数据表"""
        cursor = self.connection.cursor()
        
        # 训练记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS training_records (
                record_id TEXT PRIMARY KEY,
                model_id TEXT NOT NULL,
                model_version TEXT,
                status TEXT,
                created_at TEXT,
                started_at TEXT,
                completed_at TEXT,
                hyperparameters TEXT,
                data_config TEXT,
                metrics TEXT,
                model_path TEXT,
                tags TEXT,
                notes TEXT,
                experiment_name TEXT,
                error_message TEXT,
                signature TEXT,
                INDEX(model_id),
                INDEX(status),
                INDEX(created_at),
                INDEX(signature)
            )
        """)
        
        # 知识表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS knowledge (
                knowledge_id TEXT PRIMARY KEY,
                knowledge_type TEXT,
                source_record_id TEXT,
                model_id TEXT,
                title TEXT,
                content TEXT,
                confidence REAL,
                applicable_models TEXT,
                applicable_scenarios TEXT,
                created_at TEXT,
                updated_at TEXT,
                tags TEXT,
                validation_count INTEGER,
                success_count INTEGER,
                INDEX(knowledge_type),
                INDEX(model_id),
                INDEX(confidence),
                INDEX(created_at)
            )
        """)
        
        self.connection.commit()
    
    async def store(self, record: TrainingRecord) -> bool:
        """存储到SQLite"""
        try:
            if not self.connection:
                await self.connect()
            
            cursor = self.connection.cursor()
            
            cursor.execute("""
                INSERT OR REPLACE INTO training_records (
                    record_id, model_id, model_version, status, created_at,
                    started_at, completed_at, hyperparameters, data_config,
                    metrics, model_path, tags, notes, experiment_name,
                    error_message, signature
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                record.record_id,
                record.model_id,
                record.model_version,
                record.status.value,
                record.created_at.isoformat(),
                record.started_at.isoformat() if record.started_at else None,
                record.completed_at.isoformat() if record.completed_at else None,
                json.dumps(record.hyperparameters.to_dict()),
                json.dumps(record.data_config.to_dict()),
                json.dumps(record.metrics.to_dict()),
                record.model_path,
                json.dumps(record.tags),
                record.notes,
                record.experiment_name,
                record.error_message,
                record.get_signature()
            ))
            
            self.connection.commit()
            self.logger.debug(f"训练记录已存储到SQLite: {record.record_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"SQLite存储失败: {e}")
            return False
    
    async def retrieve(self, query: Dict[str, Any]) -> List[TrainingRecord]:
        """从SQLite检索"""
        try:
            if not self.connection:
                await self.connect()
            
            cursor = self.connection.cursor()
            
            # 构建查询条件
            where_clauses = []
            params = []
            
            for key, value in query.items():
                if key in ['model_id', 'status', 'experiment_name']:
                    where_clauses.append(f"{key} = ?")
                    params.append(value)
                elif key == 'created_after':
                    where_clauses.append("created_at > ?")
                    params.append(value.isoformat() if isinstance(value, datetime) else value)
            
            where_clause = " AND ".join(where_clauses) if where_clauses else "1=1"
            
            cursor.execute(f"""
                SELECT * FROM training_records 
                WHERE {where_clause}
                ORDER BY created_at DESC
                LIMIT 100
            """, params)
            
            records = []
            for row in cursor.fetchall():
                try:
                    record = self._row_to_training_record(row)
                    records.append(record)
                except Exception as e:
                    self.logger.error(f"记录解析失败: {e}")
                    continue
            
            return records
            
        except Exception as e:
            self.logger.error(f"SQLite检索失败: {e}")
            return []
    
    def _row_to_training_record(self, row) -> TrainingRecord:
        """将数据库行转换为TrainingRecord对象"""
        from .training_record import HyperParameters, DataConfiguration, TrainingMetrics
        
        record = TrainingRecord()
        record.record_id = row['record_id']
        record.model_id = row['model_id']
        record.model_version = row['model_version']
        record.status = TrainingStatus(row['status'])
        record.created_at = datetime.fromisoformat(row['created_at'])
        
        if row['started_at']:
            record.started_at = datetime.fromisoformat(row['started_at'])
        if row['completed_at']:
            record.completed_at = datetime.fromisoformat(row['completed_at'])
        
        if row['hyperparameters']:
            record.hyperparameters = HyperParameters.from_dict(json.loads(row['hyperparameters']))
        if row['data_config']:
            record.data_config = DataConfiguration.from_dict(json.loads(row['data_config']))
        if row['metrics']:
            record.metrics = TrainingMetrics.from_dict(json.loads(row['metrics']))
        
        record.model_path = row['model_path']
        record.tags = json.loads(row['tags']) if row['tags'] else []
        record.notes = row['notes'] or ""
        record.experiment_name = row['experiment_name']
        record.error_message = row['error_message']
        
        return record
    
    async def store_knowledge(self, knowledge: Knowledge) -> bool:
        """存储知识到SQLite"""
        try:
            if not self.connection:
                await self.connect()
            
            cursor = self.connection.cursor()
            
            cursor.execute("""
                INSERT OR REPLACE INTO knowledge (
                    knowledge_id, knowledge_type, source_record_id, model_id,
                    title, content, confidence, applicable_models,
                    applicable_scenarios, created_at, updated_at, tags,
                    validation_count, success_count
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                knowledge.knowledge_id,
                knowledge.knowledge_type.value,
                knowledge.source_record_id,
                knowledge.model_id,
                knowledge.title,
                json.dumps(knowledge.content),
                knowledge.confidence,
                json.dumps(knowledge.applicable_models),
                json.dumps(knowledge.applicable_scenarios),
                knowledge.created_at.isoformat(),
                knowledge.updated_at.isoformat(),
                json.dumps(knowledge.tags),
                knowledge.validation_count,
                knowledge.success_count
            ))
            
            self.connection.commit()
            return True
            
        except Exception as e:
            self.logger.error(f"SQLite知识存储失败: {e}")
            return False
    
    async def retrieve_knowledge(self, query: Dict[str, Any]) -> List[Knowledge]:
        """从SQLite检索知识"""
        try:
            if not self.connection:
                await self.connect()
            
            cursor = self.connection.cursor()
            
            where_clauses = []
            params = []
            
            for key, value in query.items():
                if key in ['knowledge_type', 'model_id']:
                    where_clauses.append(f"{key} = ?")
                    params.append(value)
                elif key == 'min_confidence':
                    where_clauses.append("confidence >= ?")
                    params.append(value)
            
            where_clause = " AND ".join(where_clauses) if where_clauses else "1=1"
            
            cursor.execute(f"""
                SELECT * FROM knowledge 
                WHERE {where_clause}
                ORDER BY confidence DESC, updated_at DESC
                LIMIT 50
            """, params)
            
            knowledge_list = []
            for row in cursor.fetchall():
                try:
                    knowledge = self._row_to_knowledge(row)
                    knowledge_list.append(knowledge)
                except Exception as e:
                    self.logger.error(f"知识解析失败: {e}")
                    continue
            
            return knowledge_list
            
        except Exception as e:
            self.logger.error(f"SQLite知识检索失败: {e}")
            return []
    
    def _row_to_knowledge(self, row) -> Knowledge:
        """将数据库行转换为Knowledge对象"""
        knowledge = Knowledge()
        knowledge.knowledge_id = row['knowledge_id']
        knowledge.knowledge_type = KnowledgeType(row['knowledge_type'])
        knowledge.source_record_id = row['source_record_id']
        knowledge.model_id = row['model_id']
        knowledge.title = row['title']
        knowledge.content = json.loads(row['content']) if row['content'] else {}
        knowledge.confidence = row['confidence']
        knowledge.applicable_models = json.loads(row['applicable_models']) if row['applicable_models'] else []
        knowledge.applicable_scenarios = json.loads(row['applicable_scenarios']) if row['applicable_scenarios'] else []
        knowledge.created_at = datetime.fromisoformat(row['created_at'])
        knowledge.updated_at = datetime.fromisoformat(row['updated_at'])
        knowledge.tags = json.loads(row['tags']) if row['tags'] else []
        knowledge.validation_count = row['validation_count']
        knowledge.success_count = row['success_count']
        
        return knowledge


class HierarchicalTrainingMemoryDB:
    """分层训练记忆数据库"""
    
    def __init__(self):
        self.layers = {
            "immediate": RedisMemoryLayer(),
            "short_term": SQLiteMemoryLayer(),
            "long_term": None,  # PostgreSQL层（可选）
            "knowledge": None   # 知识图谱层（可选）
        }
        
        self.knowledge_extractors = {
            KnowledgeType.PARAMETER_PATTERN: self._extract_parameter_patterns,
            KnowledgeType.ACCURACY_TREND: self._extract_accuracy_trends,
            KnowledgeType.FEATURE_IMPORTANCE: self._extract_feature_importance,
            KnowledgeType.CONVERGENCE_PATTERN: self._extract_convergence_patterns
        }
        
        self.logger = logging.getLogger("HierarchicalMemoryDB")
    
    async def initialize(self):
        """初始化数据库连接"""
        for layer_name, layer in self.layers.items():
            if layer:
                try:
                    await layer.connect()
                    self.logger.info(f"{layer_name}层初始化成功")
                except Exception as e:
                    self.logger.error(f"{layer_name}层初始化失败: {e}")
    
    async def store_training_result(self, record: TrainingRecord):
        """存储训练结果到合适的层次"""
        try:
            # 1. 立即存储到实时缓存
            if self.layers["immediate"]:
                await self.layers["immediate"].store(record)
            
            # 2. 异步存储到短期存储
            if self.layers["short_term"]:
                await self.layers["short_term"].store(record)
            
            # 3. 如果是重要结果，存储到长期存储
            if self._is_significant_result(record) and self.layers["long_term"]:
                await self.layers["long_term"].store(record)
            
            # 4. 提取知识并存储
            await self._extract_and_store_knowledge(record)
            
            self.logger.info(f"训练结果已存储: {record.record_id}")
            
        except Exception as e:
            self.logger.error(f"存储训练结果失败: {e}")
    
    def _is_significant_result(self, record: TrainingRecord) -> bool:
        """判断是否为重要结果"""
        if record.status != TrainingStatus.COMPLETED:
            return False
        
        # 高准确率结果
        if record.metrics.accuracy > 0.85:
            return True
        
        # 快速收敛结果
        if record.metrics.convergence_epoch and record.metrics.convergence_epoch < 20:
            return True
        
        # 实验性结果
        if record.experiment_name and "experiment" in record.experiment_name.lower():
            return True
        
        return False
    
    async def _extract_and_store_knowledge(self, record: TrainingRecord):
        """提取并存储知识"""
        try:
            for knowledge_type, extractor in self.knowledge_extractors.items():
                knowledge = await extractor(record)
                if knowledge:
                    # 存储到各层
                    if self.layers["immediate"]:
                        await self.layers["immediate"].store_knowledge(knowledge)
                    if self.layers["short_term"]:
                        await self.layers["short_term"].store_knowledge(knowledge)
                    
                    self.logger.debug(f"知识已提取并存储: {knowledge.title}")
        
        except Exception as e:
            self.logger.error(f"知识提取失败: {e}")
    
    async def _extract_parameter_patterns(self, record: TrainingRecord) -> Optional[Knowledge]:
        """提取参数模式知识"""
        if record.status != TrainingStatus.COMPLETED or record.metrics.accuracy < 0.7:
            return None
        
        knowledge = Knowledge(
            knowledge_type=KnowledgeType.PARAMETER_PATTERN,
            source_record_id=record.record_id,
            model_id=record.model_id,
            title=f"{record.model_id}最优参数模式",
            content={
                "learning_rate": record.hyperparameters.learning_rate,
                "batch_size": record.hyperparameters.batch_size,
                "optimizer": record.hyperparameters.optimizer,
                "accuracy_achieved": record.metrics.accuracy,
                "training_time": record.metrics.training_time
            },
            confidence=min(record.metrics.accuracy, 0.95),
            applicable_models=[record.model_id]
        )
        
        return knowledge
    
    async def _extract_accuracy_trends(self, record: TrainingRecord) -> Optional[Knowledge]:
        """提取准确率趋势知识"""
        if record.status != TrainingStatus.COMPLETED:
            return None
        
        knowledge = Knowledge(
            knowledge_type=KnowledgeType.ACCURACY_TREND,
            source_record_id=record.record_id,
            model_id=record.model_id,
            title=f"{record.model_id}准确率趋势分析",
            content={
                "final_accuracy": record.metrics.accuracy,
                "val_accuracy": record.metrics.val_accuracy,
                "convergence_epoch": record.metrics.convergence_epoch,
                "data_size": record.data_config.data_range[1] - record.data_config.data_range[0],
                "feature_count": len(record.data_config.feature_set)
            },
            confidence=0.8,
            applicable_models=[record.model_id]
        )
        
        return knowledge
    
    async def _extract_feature_importance(self, record: TrainingRecord) -> Optional[Knowledge]:
        """提取特征重要性知识"""
        if not record.data_config.feature_set:
            return None
        
        knowledge = Knowledge(
            knowledge_type=KnowledgeType.FEATURE_IMPORTANCE,
            source_record_id=record.record_id,
            model_id=record.model_id,
            title=f"{record.model_id}特征重要性分析",
            content={
                "feature_set": record.data_config.feature_set,
                "accuracy_achieved": record.metrics.accuracy,
                "data_quality": record.data_config.data_quality_score,
                "preprocessing_steps": record.data_config.preprocessing_steps
            },
            confidence=0.75,
            applicable_models=[record.model_id]
        )
        
        return knowledge
    
    async def _extract_convergence_patterns(self, record: TrainingRecord) -> Optional[Knowledge]:
        """提取收敛模式知识"""
        if not record.metrics.convergence_epoch:
            return None
        
        knowledge = Knowledge(
            knowledge_type=KnowledgeType.CONVERGENCE_PATTERN,
            source_record_id=record.record_id,
            model_id=record.model_id,
            title=f"{record.model_id}收敛模式分析",
            content={
                "convergence_epoch": record.metrics.convergence_epoch,
                "total_epochs": record.metrics.epochs,
                "final_loss": record.metrics.loss,
                "learning_rate": record.hyperparameters.learning_rate,
                "batch_size": record.hyperparameters.batch_size
            },
            confidence=0.85,
            applicable_models=[record.model_id]
        )
        
        return knowledge
    
    async def retrieve_relevant_memories(self, query: Dict[str, Any]) -> List[TrainingRecord]:
        """检索相关训练记忆"""
        all_results = []
        
        # 从各层检索
        for layer_name, layer in self.layers.items():
            if layer:
                try:
                    results = await layer.retrieve(query)
                    for result in results:
                        result._source_layer = layer_name
                    all_results.extend(results)
                except Exception as e:
                    self.logger.error(f"{layer_name}层检索失败: {e}")
        
        # 去重和排序
        unique_results = {}
        for result in all_results:
            if result.record_id not in unique_results:
                unique_results[result.record_id] = result
        
        # 按相关性排序
        sorted_results = sorted(
            unique_results.values(),
            key=lambda x: (x.metrics.accuracy, x.created_at),
            reverse=True
        )
        
        return sorted_results[:20]  # 返回前20个最相关的结果
    
    async def retrieve_knowledge(self, query: Dict[str, Any]) -> List[Knowledge]:
        """检索知识"""
        all_knowledge = []
        
        for layer_name, layer in self.layers.items():
            if layer:
                try:
                    knowledge_list = await layer.retrieve_knowledge(query)
                    all_knowledge.extend(knowledge_list)
                except Exception as e:
                    self.logger.error(f"{layer_name}层知识检索失败: {e}")
        
        # 去重和排序
        unique_knowledge = {}
        for knowledge in all_knowledge:
            if knowledge.knowledge_id not in unique_knowledge:
                unique_knowledge[knowledge.knowledge_id] = knowledge
        
        # 按有效性评分排序
        sorted_knowledge = sorted(
            unique_knowledge.values(),
            key=lambda x: x.get_effectiveness_score(),
            reverse=True
        )
        
        return sorted_knowledge[:10]  # 返回前10个最有效的知识
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        stats = {
            "layers": {},
            "total_records": 0,
            "total_knowledge": 0
        }
        
        for layer_name, layer in self.layers.items():
            if layer:
                try:
                    records = await layer.retrieve({})
                    knowledge = await layer.retrieve_knowledge({})
                    
                    stats["layers"][layer_name] = {
                        "records": len(records),
                        "knowledge": len(knowledge),
                        "status": "active"
                    }
                    
                    stats["total_records"] += len(records)
                    stats["total_knowledge"] += len(knowledge)
                    
                except Exception as e:
                    stats["layers"][layer_name] = {
                        "records": 0,
                        "knowledge": 0,
                        "status": f"error: {e}"
                    }
        
        return stats


async def test_hierarchical_memory():
    """测试分层训练记忆数据库"""
    print("🧪 测试分层训练记忆数据库...")
    
    # 创建数据库实例
    memory_db = HierarchicalTrainingMemoryDB()
    await memory_db.initialize()
    
    # 创建测试训练记录
    from .training_record import HyperParameters, DataConfiguration, TrainingMetrics
    
    record = TrainingRecord(
        model_id="test_model",
        status=TrainingStatus.COMPLETED,
        hyperparameters=HyperParameters(learning_rate=0.001, batch_size=64),
        data_config=DataConfiguration(
            data_range=(0, 1000),
            feature_set=["feature1", "feature2", "feature3"]
        ),
        metrics=TrainingMetrics(accuracy=0.87, training_time=120.5)
    )
    
    # 存储训练结果
    await memory_db.store_training_result(record)
    print(f"✅ 训练记录已存储: {record.record_id}")
    
    # 检索训练记录
    results = await memory_db.retrieve_relevant_memories({"model_id": "test_model"})
    print(f"📊 检索到 {len(results)} 条训练记录")
    
    # 检索知识
    knowledge = await memory_db.retrieve_knowledge({"model_id": "test_model"})
    print(f"📚 检索到 {len(knowledge)} 条知识")
    
    # 获取统计信息
    stats = await memory_db.get_statistics()
    print(f"📈 数据库统计: {stats}")
    
    print("✅ 分层训练记忆数据库测试完成！")


if __name__ == "__main__":
    asyncio.run(test_hierarchical_memory())
