#!/usr/bin/env python3
"""
WebSocket连接测试脚本
用于验证WebSocket服务是否正常工作
"""

import asyncio
import json
import logging
import time
import sys
from typing import Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

try:
    import websockets
    HAS_WEBSOCKETS = True
except ImportError:
    HAS_WEBSOCKETS = False
    logger.error("websockets库未安装")

class WebSocketTester:
    """WebSocket连接测试器"""
    
    def __init__(self, base_url: str = "ws://127.0.0.1:8888"):
        self.base_url = base_url
        self.test_results = {}
    
    async def test_endpoint(self, endpoint: str, timeout: float = 5.0) -> Dict[str, Any]:
        """测试单个WebSocket端点"""
        uri = f"{self.base_url}{endpoint}"
        result = {
            'endpoint': endpoint,
            'uri': uri,
            'status': 'unknown',
            'error': None,
            'response_time': None
        }
        
        start_time = time.time()
        try:
            async with websockets.connect(uri, timeout=timeout) as websocket:
                # 发送测试消息
                test_message = {"type": "ping", "timestamp": time.time()}
                await websocket.send(json.dumps(test_message))
                
                # 等待响应
                response = await asyncio.wait_for(
                    websocket.recv(), 
                    timeout=timeout
                )
                
                result['status'] = 'success'
                result['response_time'] = time.time() - start_time
                logger.info(f"✅ {endpoint} 连接成功 ({result['response_time']:.2f}s)")
                
        except Exception as e:
            result['status'] = 'failed'
            result['error'] = str(e)
            result['response_time'] = time.time() - start_time
            logger.error(f"❌ {endpoint} 连接失败: {e}")
        
        return result
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有WebSocket端点测试"""
        if not HAS_WEBSOCKETS:
            return {'error': 'websockets库未安装'}
        
        endpoints = [
            '/ws/bug-detection',
            '/ws/realtime-stats'
        ]
        
        logger.info("🚀 开始WebSocket连接测试...")
        
        results = {}
        for endpoint in endpoints:
            results[endpoint] = await self.test_endpoint(endpoint)
        
        # 生成测试报告
        success_count = sum(1 for r in results.values() if r['status'] == 'success')
        total_count = len(results)
        
        report = {
            'timestamp': time.time(),
            'total_tests': total_count,
            'successful_tests': success_count,
            'success_rate': (success_count / total_count) * 100,
            'results': results
        }
        
        logger.info(f"📊 测试完成: {success_count}/{total_count} 成功 ({report['success_rate']:.1f}%)")
        
        return report

async def main():
    """主函数"""
    tester = WebSocketTester()
    report = await tester.run_all_tests()
    
    # 保存测试报告
    with open(f'websocket_test_report_{int(time.time())}.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    # 返回退出码
    if report.get('success_rate', 0) == 100:
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
