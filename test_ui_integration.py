#!/usr/bin/env python3
"""
测试UI集成功能
验证修复后的UI功能是否正常工作
"""

import sys
import os
import requests
import time

def test_api_health():
    """测试API健康状态"""
    print("=== 测试API健康状态 ===")
    
    try:
        response = requests.get("http://127.0.0.1:8888/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API服务正常运行")
            print(f"   数据库记录: {data.get('database_records', 'N/A')}")
            print(f"   数据范围: {data.get('date_range', 'N/A')}")
            return True
        else:
            print(f"❌ API服务异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False

def test_intelligent_fusion_prediction():
    """测试智能融合预测功能"""
    print("\n=== 测试智能融合预测功能 ===")
    
    try:
        # 测试智能融合预测API
        response = requests.get(
            "http://127.0.0.1:8888/api/v1/prediction/intelligent-fusion/predict",
            params={
                "max_candidates": 10,
                "confidence_threshold": 0.5,
                "auto_train": True
            },
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success', False):
                prediction = result.get('prediction', {})
                
                if 'error' not in prediction:
                    print("✅ 智能融合预测成功")
                    print(f"   推荐号码: {prediction.get('numbers', 'N/A')}")
                    print(f"   置信度: {prediction.get('confidence', 0):.1%}")
                    
                    # 检查候选列表
                    candidates = prediction.get('candidates', [])
                    print(f"   候选数量: {len(candidates)}")
                    
                    # 验证候选数据结构
                    if candidates:
                        first_candidate = candidates[0]
                        has_numbers = 'numbers' in first_candidate
                        has_confidence = 'confidence' in first_candidate
                        has_fusion_score = 'fusion_score' in first_candidate
                        
                        print(f"   候选数据结构: numbers={has_numbers}, confidence={has_confidence}, fusion_score={has_fusion_score}")
                        
                        # 这里应该不会出现apply错误了
                        return True
                    else:
                        print("⚠️ 没有候选数据")
                        return True  # 这不是错误，只是没有候选
                else:
                    print(f"❌ 预测失败: {prediction['error']}")
                    return False
            else:
                print(f"❌ API调用失败: {result.get('error', '未知错误')}")
                return False
        else:
            print(f"❌ API请求失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 智能融合预测测试失败: {e}")
        return False

def test_trend_analysis():
    """测试趋势分析功能"""
    print("\n=== 测试趋势分析功能 ===")
    
    try:
        # 这里我们不能直接测试Streamlit UI，但可以测试底层功能
        sys.path.append('src')
        from prediction.trend_analysis import TrendAnalyzer
        
        analyzer = TrendAnalyzer(window_size=30)
        records = analyzer.load_recent_data(limit=200)
        
        if len(records) >= 30:
            trends = analyzer.analyze_digit_frequency_trends(records)
            print(f"✅ 趋势分析功能正常，分析了 {len(trends)} 个数字的趋势")
            return True
        else:
            print(f"⚠️ 数据不足进行趋势分析，只有 {len(records)} 条记录")
            return False
            
    except Exception as e:
        print(f"❌ 趋势分析测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始UI集成功能测试...")
    
    results = []
    
    # 运行所有测试
    results.append(("API健康状态", test_api_health()))
    results.append(("智能融合预测", test_intelligent_fusion_prediction()))
    results.append(("趋势分析功能", test_trend_analysis()))
    
    # 汇总结果
    print("\n" + "="*50)
    print("UI集成测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有UI集成测试通过！")
        print("💡 修复后的系统应该不会再出现'int' object has no attribute 'apply'错误")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
