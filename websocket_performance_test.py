#!/usr/bin/env python3
"""
WebSocket性能和稳定性测试
创建日期: 2025年7月25日
用途: 测试WebSocket连接性能、数据传输延迟、并发连接处理能力，验证连接稳定性和错误恢复机制
"""

import asyncio
import json
import logging
import statistics
import time
from datetime import datetime
from typing import Dict, List

import requests

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebSocketPerformanceTester:
    """WebSocket性能和稳定性测试器"""
    
    def __init__(self):
        self.api_base_url = "http://127.0.0.1:8888"
        self.test_results = {}
        
    async def run_all_performance_tests(self):
        """运行所有性能测试"""
        print("🚀 开始WebSocket性能和稳定性测试")
        print("=" * 60)
        
        # 测试1: API响应性能测试
        await self.test_api_response_performance()
        
        # 测试2: 数据传输延迟测试
        await self.test_data_transmission_latency()
        
        # 测试3: 连接稳定性测试
        await self.test_connection_stability()
        
        # 测试4: 错误恢复机制测试
        await self.test_error_recovery_mechanisms()
        
        # 测试5: 降级机制性能测试
        await self.test_fallback_performance()
        
        # 生成性能测试报告
        self.generate_performance_report()
    
    async def test_api_response_performance(self):
        """测试API响应性能"""
        print("\n⚡ 测试1: API响应性能")
        
        endpoints = [
            "/health",
            "/api/v1/stats/basic",
            "/api/v1/bug-detection/statistics",
            "/api/v1/health/websocket"
        ]
        
        performance_results = {}
        
        for endpoint in endpoints:
            print(f"  测试端点: {endpoint}")
            response_times = []
            success_count = 0
            
            # 进行10次请求测试
            for i in range(10):
                try:
                    start_time = time.time()
                    response = requests.get(f"{self.api_base_url}{endpoint}", timeout=5)
                    end_time = time.time()
                    
                    response_time = (end_time - start_time) * 1000  # 转换为毫秒
                    response_times.append(response_time)
                    
                    if response.status_code == 200:
                        success_count += 1
                        
                except Exception as e:
                    logger.warning(f"请求失败: {e}")
                    response_times.append(5000)  # 超时记为5秒
            
            # 计算统计数据
            if response_times:
                avg_response_time = statistics.mean(response_times)
                min_response_time = min(response_times)
                max_response_time = max(response_times)
                success_rate = (success_count / 10) * 100
                
                performance_results[endpoint] = {
                    'avg_response_time': avg_response_time,
                    'min_response_time': min_response_time,
                    'max_response_time': max_response_time,
                    'success_rate': success_rate,
                    'total_requests': 10,
                    'successful_requests': success_count
                }
                
                print(f"    平均响应时间: {avg_response_time:.2f}ms")
                print(f"    最小响应时间: {min_response_time:.2f}ms")
                print(f"    最大响应时间: {max_response_time:.2f}ms")
                print(f"    成功率: {success_rate:.1f}%")
                
                # 性能评估
                if avg_response_time < 100:
                    print("    性能评级: ✅ 优秀")
                elif avg_response_time < 500:
                    print("    性能评级: ✅ 良好")
                elif avg_response_time < 1000:
                    print("    性能评级: ⚠️ 一般")
                else:
                    print("    性能评级: ❌ 需要优化")
        
        self.test_results['api_performance'] = performance_results
    
    async def test_data_transmission_latency(self):
        """测试数据传输延迟"""
        print("\n📊 测试2: 数据传输延迟")
        
        # 测试不同大小的数据传输
        test_cases = [
            {"name": "基础统计数据", "endpoint": "/api/v1/stats/basic"},
            {"name": "Bug检测统计", "endpoint": "/api/v1/bug-detection/statistics"}
        ]
        
        latency_results = {}
        
        for test_case in test_cases:
            print(f"  测试: {test_case['name']}")
            endpoint = test_case['endpoint']
            
            latencies = []
            data_sizes = []
            
            for i in range(5):
                try:
                    start_time = time.time()
                    response = requests.get(f"{self.api_base_url}{endpoint}", timeout=5)
                    end_time = time.time()
                    
                    if response.status_code == 200:
                        latency = (end_time - start_time) * 1000
                        data_size = len(response.content)
                        
                        latencies.append(latency)
                        data_sizes.append(data_size)
                        
                except Exception as e:
                    logger.warning(f"延迟测试失败: {e}")
            
            if latencies:
                avg_latency = statistics.mean(latencies)
                avg_data_size = statistics.mean(data_sizes)
                throughput = (avg_data_size / 1024) / (avg_latency / 1000)  # KB/s
                
                latency_results[test_case['name']] = {
                    'avg_latency': avg_latency,
                    'avg_data_size': avg_data_size,
                    'throughput': throughput
                }
                
                print(f"    平均延迟: {avg_latency:.2f}ms")
                print(f"    平均数据大小: {avg_data_size:.0f} bytes")
                print(f"    吞吐量: {throughput:.2f} KB/s")
        
        self.test_results['data_transmission'] = latency_results
    
    async def test_connection_stability(self):
        """测试连接稳定性"""
        print("\n🔗 测试3: 连接稳定性")
        
        # 测试长时间连接稳定性
        print("  测试长时间API连接稳定性...")
        
        stability_results = {
            'long_term_stability': {},
            'rapid_requests': {}
        }
        
        # 长时间稳定性测试（30秒内每2秒请求一次）
        start_time = time.time()
        request_count = 0
        success_count = 0
        
        while time.time() - start_time < 30:
            try:
                response = requests.get(f"{self.api_base_url}/health", timeout=3)
                request_count += 1
                if response.status_code == 200:
                    success_count += 1
                await asyncio.sleep(2)
            except Exception as e:
                request_count += 1
                logger.warning(f"稳定性测试请求失败: {e}")
        
        long_term_success_rate = (success_count / request_count * 100) if request_count > 0 else 0
        stability_results['long_term_stability'] = {
            'duration_seconds': 30,
            'total_requests': request_count,
            'successful_requests': success_count,
            'success_rate': long_term_success_rate
        }
        
        print(f"    长时间稳定性测试:")
        print(f"      总请求数: {request_count}")
        print(f"      成功请求数: {success_count}")
        print(f"      成功率: {long_term_success_rate:.1f}%")
        
        # 快速连续请求测试
        print("  测试快速连续请求处理能力...")
        rapid_start_time = time.time()
        rapid_success = 0
        rapid_total = 20
        
        for i in range(rapid_total):
            try:
                response = requests.get(f"{self.api_base_url}/health", timeout=1)
                if response.status_code == 200:
                    rapid_success += 1
            except Exception as e:
                logger.warning(f"快速请求失败: {e}")
        
        rapid_end_time = time.time()
        rapid_duration = rapid_end_time - rapid_start_time
        rapid_success_rate = (rapid_success / rapid_total * 100)
        requests_per_second = rapid_total / rapid_duration
        
        stability_results['rapid_requests'] = {
            'total_requests': rapid_total,
            'successful_requests': rapid_success,
            'success_rate': rapid_success_rate,
            'duration_seconds': rapid_duration,
            'requests_per_second': requests_per_second
        }
        
        print(f"    快速连续请求测试:")
        print(f"      总请求数: {rapid_total}")
        print(f"      成功请求数: {rapid_success}")
        print(f"      成功率: {rapid_success_rate:.1f}%")
        print(f"      请求速率: {requests_per_second:.2f} req/s")
        
        self.test_results['connection_stability'] = stability_results
    
    async def test_error_recovery_mechanisms(self):
        """测试错误恢复机制"""
        print("\n🛠️ 测试4: 错误恢复机制")
        
        recovery_results = {}
        
        # 测试无效端点的错误处理
        print("  测试无效端点错误处理...")
        try:
            response = requests.get(f"{self.api_base_url}/invalid-endpoint", timeout=5)
            recovery_results['invalid_endpoint'] = {
                'status_code': response.status_code,
                'handled_gracefully': response.status_code == 404
            }
            print(f"    无效端点返回状态码: {response.status_code}")
            print(f"    错误处理: {'✅ 正常' if response.status_code == 404 else '❌ 异常'}")
        except Exception as e:
            recovery_results['invalid_endpoint'] = {
                'error': str(e),
                'handled_gracefully': False
            }
            print(f"    无效端点测试异常: {e}")
        
        # 测试超时处理
        print("  测试超时处理机制...")
        timeout_handled = False
        try:
            # 使用极短的超时时间测试超时处理
            response = requests.get(f"{self.api_base_url}/health", timeout=0.001)
        except requests.exceptions.Timeout:
            timeout_handled = True
            print("    ✅ 超时异常正确捕获")
        except Exception as e:
            print(f"    ⚠️ 其他异常: {e}")
        
        recovery_results['timeout_handling'] = {
            'timeout_handled': timeout_handled
        }
        
        self.test_results['error_recovery'] = recovery_results
    
    async def test_fallback_performance(self):
        """测试降级机制性能"""
        print("\n🔄 测试5: 降级机制性能")
        
        fallback_results = {}
        
        # 测试API轮询降级性能
        print("  测试API轮询降级性能...")
        
        # 模拟WebSocket不可用，测试API轮询性能
        polling_times = []
        
        for i in range(5):
            start_time = time.time()
            try:
                # 模拟降级到API轮询
                response = requests.get(f"{self.api_base_url}/api/v1/stats/basic", timeout=5)
                if response.status_code == 200:
                    end_time = time.time()
                    polling_time = (end_time - start_time) * 1000
                    polling_times.append(polling_time)
            except Exception as e:
                logger.warning(f"降级测试失败: {e}")
        
        if polling_times:
            avg_polling_time = statistics.mean(polling_times)
            fallback_results['api_polling'] = {
                'avg_response_time': avg_polling_time,
                'performance_acceptable': avg_polling_time < 1000  # 1秒内可接受
            }
            
            print(f"    API轮询平均响应时间: {avg_polling_time:.2f}ms")
            print(f"    降级性能: {'✅ 可接受' if avg_polling_time < 1000 else '⚠️ 需要优化'}")
        
        self.test_results['fallback_performance'] = fallback_results
    
    def generate_performance_report(self):
        """生成性能测试报告"""
        print("\n" + "=" * 60)
        print("📋 WebSocket性能和稳定性测试报告")
        print("=" * 60)
        
        # API性能评估
        if 'api_performance' in self.test_results:
            print("\n⚡ API性能评估:")
            api_results = self.test_results['api_performance']
            
            total_avg_time = 0
            endpoint_count = 0
            
            for endpoint, metrics in api_results.items():
                avg_time = metrics['avg_response_time']
                success_rate = metrics['success_rate']
                
                total_avg_time += avg_time
                endpoint_count += 1
                
                status = "✅" if avg_time < 500 and success_rate > 90 else "⚠️" if avg_time < 1000 else "❌"
                print(f"  {endpoint}: {status} {avg_time:.2f}ms ({success_rate:.1f}%)")
            
            overall_avg = total_avg_time / endpoint_count if endpoint_count > 0 else 0
            print(f"  整体平均响应时间: {overall_avg:.2f}ms")
        
        # 连接稳定性评估
        if 'connection_stability' in self.test_results:
            print("\n🔗 连接稳定性评估:")
            stability = self.test_results['connection_stability']
            
            if 'long_term_stability' in stability:
                long_term = stability['long_term_stability']
                print(f"  长时间稳定性: {long_term['success_rate']:.1f}% ({long_term['successful_requests']}/{long_term['total_requests']})")
            
            if 'rapid_requests' in stability:
                rapid = stability['rapid_requests']
                print(f"  快速请求处理: {rapid['success_rate']:.1f}% ({rapid['requests_per_second']:.2f} req/s)")
        
        # 错误恢复评估
        if 'error_recovery' in self.test_results:
            print("\n🛠️ 错误恢复机制评估:")
            recovery = self.test_results['error_recovery']
            
            if 'invalid_endpoint' in recovery:
                invalid_handled = recovery['invalid_endpoint'].get('handled_gracefully', False)
                print(f"  无效端点处理: {'✅ 正常' if invalid_handled else '❌ 异常'}")
            
            if 'timeout_handling' in recovery:
                timeout_handled = recovery['timeout_handling'].get('timeout_handled', False)
                print(f"  超时处理: {'✅ 正常' if timeout_handled else '❌ 异常'}")
        
        # 降级机制评估
        if 'fallback_performance' in self.test_results:
            print("\n🔄 降级机制性能评估:")
            fallback = self.test_results['fallback_performance']
            
            if 'api_polling' in fallback:
                polling = fallback['api_polling']
                acceptable = polling.get('performance_acceptable', False)
                avg_time = polling.get('avg_response_time', 0)
                print(f"  API轮询降级: {'✅ 可接受' if acceptable else '⚠️ 需要优化'} ({avg_time:.2f}ms)")
        
        # 总体评估
        print(f"\n📊 总体性能评估:")
        
        # 计算总体得分
        score = 0
        max_score = 0
        
        # API性能得分 (40分)
        if 'api_performance' in self.test_results:
            api_score = 0
            for endpoint, metrics in self.test_results['api_performance'].items():
                if metrics['avg_response_time'] < 500 and metrics['success_rate'] > 90:
                    api_score += 10
                elif metrics['avg_response_time'] < 1000 and metrics['success_rate'] > 80:
                    api_score += 7
                else:
                    api_score += 3
            score += min(api_score, 40)
            max_score += 40
        
        # 稳定性得分 (30分)
        if 'connection_stability' in self.test_results:
            stability_score = 0
            stability = self.test_results['connection_stability']
            
            if 'long_term_stability' in stability:
                long_term_rate = stability['long_term_stability']['success_rate']
                if long_term_rate > 95:
                    stability_score += 15
                elif long_term_rate > 90:
                    stability_score += 12
                else:
                    stability_score += 8
            
            if 'rapid_requests' in stability:
                rapid_rate = stability['rapid_requests']['success_rate']
                if rapid_rate > 95:
                    stability_score += 15
                elif rapid_rate > 90:
                    stability_score += 12
                else:
                    stability_score += 8
            
            score += stability_score
            max_score += 30
        
        # 错误恢复得分 (20分)
        if 'error_recovery' in self.test_results:
            recovery_score = 0
            recovery = self.test_results['error_recovery']
            
            if recovery.get('invalid_endpoint', {}).get('handled_gracefully', False):
                recovery_score += 10
            if recovery.get('timeout_handling', {}).get('timeout_handled', False):
                recovery_score += 10
            
            score += recovery_score
            max_score += 20
        
        # 降级机制得分 (10分)
        if 'fallback_performance' in self.test_results:
            fallback_score = 0
            fallback = self.test_results['fallback_performance']
            
            if fallback.get('api_polling', {}).get('performance_acceptable', False):
                fallback_score += 10
            else:
                fallback_score += 5
            
            score += fallback_score
            max_score += 10
        
        final_score = (score / max_score * 100) if max_score > 0 else 0
        
        print(f"  性能得分: {score}/{max_score} ({final_score:.1f}%)")
        
        if final_score >= 90:
            print("  🎉 性能评级: 优秀")
            overall_rating = "优秀"
        elif final_score >= 80:
            print("  ✅ 性能评级: 良好")
            overall_rating = "良好"
        elif final_score >= 70:
            print("  ⚠️ 性能评级: 一般")
            overall_rating = "一般"
        else:
            print("  ❌ 性能评级: 需要优化")
            overall_rating = "需要优化"
        
        # 保存测试结果
        test_report = {
            'timestamp': datetime.now().isoformat(),
            'overall_score': final_score,
            'overall_rating': overall_rating,
            'detailed_results': self.test_results
        }
        
        with open('websocket_performance_results.json', 'w', encoding='utf-8') as f:
            json.dump(test_report, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 详细性能测试结果已保存到: websocket_performance_results.json")

async def main():
    """主函数"""
    tester = WebSocketPerformanceTester()
    await tester.run_all_performance_tests()

if __name__ == "__main__":
    asyncio.run(main())
