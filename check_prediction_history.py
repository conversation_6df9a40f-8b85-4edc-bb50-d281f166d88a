import os
import sqlite3

# 检查数据库文件
db_files = []
for root, dirs, files in os.walk('.'):
    for file in files:
        if file.endswith('.db') or file.endswith('.sqlite'):
            db_files.append(os.path.join(root, file))

print('找到的数据库文件:')
for db_file in db_files:
    print(f'  {db_file}')

# 检查主数据库的表结构
if os.path.exists('data/lottery_data.db'):
    conn = sqlite3.connect('data/lottery_data.db')
    cursor = conn.cursor()

    # 获取所有表名
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()

    print('\n主数据库表:')
    for table in tables:
        print(f'  {table[0]}')

        # 获取表结构
        cursor.execute(f'PRAGMA table_info({table[0]})')
        columns = cursor.fetchall()
        for col in columns:
            print(f'    {col[1]} ({col[2]})')

    conn.close()
else:
    print('\ndata/lottery_data.db 不存在')

# 检查模型库数据库
if os.path.exists('data/model_library.db'):
    conn = sqlite3.connect('data/model_library.db')
    cursor = conn.cursor()

    # 获取所有表名
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()

    print('\n模型库数据库表:')
    for table in tables:
        print(f'  {table[0]}')

        # 获取表结构
        cursor.execute(f'PRAGMA table_info({table[0]})')
        columns = cursor.fetchall()
        for col in columns:
            print(f'    {col[1]} ({col[2]})')

    # 检查预测记录表
    cursor.execute("SELECT COUNT(*) FROM model_predictions")
    prediction_count = cursor.fetchone()[0]
    print(f'\n预测记录数: {prediction_count}')

    if prediction_count > 0:
        cursor.execute("SELECT * FROM model_predictions ORDER BY prediction_time DESC LIMIT 5")
        recent_predictions = cursor.fetchall()
        print('\n最近5条预测记录:')
        for pred in recent_predictions:
            print(f'  模型: {pred[1]}, 时间: {pred[2]}, 期号: {pred[3]}, 置信度: {pred[5]}')

    conn.close()
else:
    print('\ndata/model_library.db 不存在')
