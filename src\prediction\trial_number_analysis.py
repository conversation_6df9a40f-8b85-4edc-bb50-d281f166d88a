"""
试机号码关联分析模块
实现试机号码与正式号码的关联关系分析和预测
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
from collections import defaultdict, Counter
import sqlite3
import os
from datetime import datetime, timedelta

class TrialNumberAnalyzer:
    """试机号码关联分析器"""
    
    def __init__(self, db_path: str = None):
        """
        初始化分析器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path or os.path.join('data', 'lottery.db')
        self.analysis_results = {}
        
    def load_trial_data(self) -> List[Dict[str, Any]]:
        """
        从数据库加载试机号码数据
        
        Returns:
            包含试机号码和正式号码的记录列表
        """
        if not os.path.exists(self.db_path):
            raise FileNotFoundError(f"数据库文件不存在: {self.db_path}")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 查询试机号码和正式号码数据
        cursor.execute("""
            SELECT period, date, numbers, trial_numbers, 
                   draw_machine, trial_machine, sales_amount
            FROM lottery_records 
            WHERE trial_numbers IS NOT NULL 
            AND trial_numbers != '' 
            AND trial_numbers != '000'
            ORDER BY date ASC
        """)
        
        records = []
        for row in cursor.fetchall():
            record = {
                'period': row[0],
                'date': row[1],
                'numbers': row[2],
                'trial_numbers': row[3],
                'draw_machine': row[4],
                'trial_machine': row[5],
                'sales_amount': row[6]
            }
            records.append(record)
        
        conn.close()
        return records
    
    def analyze_position_differences(self, records: List[Dict]) -> Dict[str, Any]:
        """
        分析试机号码与正式号码的位置差值关系
        
        Args:
            records: 试机号码记录列表
            
        Returns:
            位置差值分析结果
        """
        position_diffs = {
            'hundreds': [],  # 百位差值
            'tens': [],      # 十位差值
            'units': []      # 个位差值
        }
        
        for record in records:
            trial = record['trial_numbers']
            formal = record['numbers']
            
            if len(trial) == 3 and len(formal) == 3:
                # 计算各位差值（考虑循环性，0-9循环）
                for i, pos in enumerate(['hundreds', 'tens', 'units']):
                    trial_digit = int(trial[i])
                    formal_digit = int(formal[i])
                    
                    # 计算最小差值（考虑0-9循环）
                    diff1 = (formal_digit - trial_digit) % 10
                    diff2 = (trial_digit - formal_digit) % 10
                    min_diff = min(diff1, diff2)
                    
                    # 保留方向信息
                    if diff1 <= diff2:
                        signed_diff = diff1
                    else:
                        signed_diff = -diff2
                    
                    position_diffs[pos].append({
                        'trial': trial_digit,
                        'formal': formal_digit,
                        'min_diff': min_diff,
                        'signed_diff': signed_diff,
                        'period': record['period']
                    })
        
        # 统计分析
        analysis = {}
        for pos, diffs in position_diffs.items():
            if diffs:
                min_diffs = [d['min_diff'] for d in diffs]
                signed_diffs = [d['signed_diff'] for d in diffs]
                
                analysis[pos] = {
                    'count': len(diffs),
                    'min_diff_distribution': Counter(min_diffs),
                    'signed_diff_distribution': Counter(signed_diffs),
                    'avg_min_diff': np.mean(min_diffs),
                    'std_min_diff': np.std(min_diffs),
                    'most_common_min_diff': Counter(min_diffs).most_common(3),
                    'most_common_signed_diff': Counter(signed_diffs).most_common(5)
                }
        
        return analysis
    
    def analyze_preheating_effect(self, records: List[Dict]) -> Dict[str, Any]:
        """
        分析试机号码的"预热"效应
        检测试机号码中的数字在后续正式开奖中的出现频率
        
        Args:
            records: 试机号码记录列表
            
        Returns:
            预热效应分析结果
        """
        preheating_stats = {
            'immediate': defaultdict(int),    # 当期预热效应
            'next_1': defaultdict(int),       # 下1期预热效应
            'next_3': defaultdict(int),       # 下3期预热效应
            'next_5': defaultdict(int),       # 下5期预热效应
        }
        
        total_counts = {
            'immediate': 0,
            'next_1': 0,
            'next_3': 0,
            'next_5': 0
        }
        
        for i, record in enumerate(records):
            trial_digits = set(record['trial_numbers'])
            formal_digits = set(record['numbers'])
            
            # 当期预热效应
            immediate_hits = len(trial_digits & formal_digits)
            preheating_stats['immediate'][immediate_hits] += 1
            total_counts['immediate'] += 1
            
            # 后续期数预热效应
            for period_offset, key in [(1, 'next_1'), (3, 'next_3'), (5, 'next_5')]:
                hits = 0
                valid_periods = 0
                
                for j in range(1, period_offset + 1):
                    if i + j < len(records):
                        future_formal = set(records[i + j]['numbers'])
                        if trial_digits & future_formal:
                            hits += 1
                        valid_periods += 1
                
                if valid_periods > 0:
                    hit_rate = hits / valid_periods
                    preheating_stats[key][round(hit_rate, 2)] += 1
                    total_counts[key] += 1
        
        # 计算预热效应统计
        analysis = {}
        for period, stats in preheating_stats.items():
            if total_counts[period] > 0:
                analysis[period] = {
                    'distribution': dict(stats),
                    'total_samples': total_counts[period],
                    'avg_hits': sum(k * v for k, v in stats.items()) / total_counts[period],
                    'hit_probability': sum(v for k, v in stats.items() if k > 0) / total_counts[period]
                }
        
        return analysis
    
    def analyze_machine_correlation(self, records: List[Dict]) -> Dict[str, Any]:
        """
        分析试机机器与开奖机器的关联性
        
        Args:
            records: 试机号码记录列表
            
        Returns:
            机器关联性分析结果
        """
        machine_pairs = []
        machine_accuracy = defaultdict(list)
        
        for record in records:
            trial_machine = record['trial_machine']
            draw_machine = record['draw_machine']
            
            machine_pairs.append((trial_machine, draw_machine))
            
            # 计算试机号码与正式号码的相似度
            trial = record['trial_numbers']
            formal = record['numbers']
            
            if len(trial) == 3 and len(formal) == 3:
                # 计算位置匹配数
                position_matches = sum(1 for i in range(3) if trial[i] == formal[i])
                # 计算数字重叠数
                digit_overlap = len(set(trial) & set(formal))
                
                similarity_score = (position_matches * 2 + digit_overlap) / 5  # 归一化到0-1
                machine_accuracy[(trial_machine, draw_machine)].append(similarity_score)
        
        # 统计机器配对
        machine_pair_counts = Counter(machine_pairs)
        
        # 计算每种机器配对的平均准确率
        machine_performance = {}
        for (trial_m, draw_m), scores in machine_accuracy.items():
            if scores:
                machine_performance[(trial_m, draw_m)] = {
                    'count': len(scores),
                    'avg_similarity': np.mean(scores),
                    'std_similarity': np.std(scores),
                    'max_similarity': max(scores),
                    'min_similarity': min(scores)
                }
        
        return {
            'machine_pair_distribution': dict(machine_pair_counts),
            'machine_performance': machine_performance,
            'total_combinations': len(machine_pair_counts),
            'most_common_pairs': machine_pair_counts.most_common(10)
        }
    
    def build_trial_prediction_model(self, records: List[Dict]) -> Dict[str, Any]:
        """
        构建基于试机号码的预测模型
        
        Args:
            records: 试机号码记录列表
            
        Returns:
            预测模型参数和规则
        """
        # 收集特征和目标
        features = []
        targets = []
        
        for record in records:
            trial = record['trial_numbers']
            formal = record['numbers']
            
            if len(trial) == 3 and len(formal) == 3:
                # 特征：试机号码的各种属性
                trial_digits = [int(d) for d in trial]
                feature_vector = [
                    # 基础特征
                    trial_digits[0], trial_digits[1], trial_digits[2],  # 原始数字
                    sum(trial_digits),  # 和值
                    max(trial_digits) - min(trial_digits),  # 跨度
                    len(set(trial_digits)),  # 不重复数字个数
                    
                    # 奇偶特征
                    sum(1 for d in trial_digits if d % 2 == 1),  # 奇数个数
                    
                    # 大小特征
                    sum(1 for d in trial_digits if d >= 5),  # 大数个数
                    
                    # 机器特征
                    record['trial_machine'],
                    record['draw_machine'],
                ]
                
                features.append(feature_vector)
                targets.append(formal)
        
        # 分析特征与目标的关联性
        if features and targets:
            features_array = np.array(features)
            
            # 计算特征重要性（简单的相关性分析）
            feature_importance = {}
            feature_names = [
                'trial_h', 'trial_t', 'trial_u', 'trial_sum', 'trial_span', 
                'trial_unique', 'trial_odd_count', 'trial_big_count', 
                'trial_machine', 'draw_machine'
            ]
            
            for i, name in enumerate(feature_names):
                if i < features_array.shape[1]:
                    # 计算该特征与正式号码各位的相关性
                    correlations = []
                    for pos in range(3):
                        formal_digits = [int(target[pos]) for target in targets]
                        if len(set(formal_digits)) > 1:  # 避免方差为0
                            corr = np.corrcoef(features_array[:, i], formal_digits)[0, 1]
                            if not np.isnan(corr):
                                correlations.append(abs(corr))
                    
                    if correlations:
                        feature_importance[name] = np.mean(correlations)
            
            # 构建预测规则
            prediction_rules = self._extract_prediction_rules(features, targets)
            
            return {
                'feature_importance': feature_importance,
                'prediction_rules': prediction_rules,
                'sample_count': len(features),
                'feature_names': feature_names
            }
        
        return {}
    
    def _extract_prediction_rules(self, features: List[List], targets: List[str]) -> Dict[str, Any]:
        """
        从数据中提取预测规则
        
        Args:
            features: 特征列表
            targets: 目标列表
            
        Returns:
            预测规则字典
        """
        rules = {
            'sum_value_rules': defaultdict(list),
            'span_value_rules': defaultdict(list),
            'machine_rules': defaultdict(list),
            'position_rules': defaultdict(list)
        }
        
        for feature, target in zip(features, targets):
            trial_sum = feature[3]
            trial_span = feature[4]
            trial_machine = feature[8]
            draw_machine = feature[9]
            
            formal_digits = [int(d) for d in target]
            formal_sum = sum(formal_digits)
            formal_span = max(formal_digits) - min(formal_digits)
            
            # 和值规则
            rules['sum_value_rules'][trial_sum].append(formal_sum)
            
            # 跨度规则
            rules['span_value_rules'][trial_span].append(formal_span)
            
            # 机器规则
            rules['machine_rules'][(trial_machine, draw_machine)].append(target)
            
            # 位置规则
            for i in range(3):
                trial_digit = feature[i]
                formal_digit = formal_digits[i]
                rules['position_rules'][(i, trial_digit)].append(formal_digit)
        
        # 统计规则
        processed_rules = {}
        for rule_type, rule_data in rules.items():
            processed_rules[rule_type] = {}
            for key, values in rule_data.items():
                if values:
                    processed_rules[rule_type][str(key)] = {
                        'count': len(values),
                        'most_common': Counter(values).most_common(5),
                        'average': np.mean(values) if isinstance(values[0], (int, float)) else None
                    }
        
        return processed_rules
    
    def predict_from_trial(self, trial_numbers: str, 
                          trial_machine: int = None, 
                          draw_machine: int = None) -> Dict[str, Any]:
        """
        基于试机号码预测正式号码
        
        Args:
            trial_numbers: 试机号码
            trial_machine: 试机机器号
            draw_machine: 开奖机器号
            
        Returns:
            预测结果
        """
        if not hasattr(self, 'prediction_model') or not self.prediction_model:
            raise ValueError("预测模型未训练，请先调用train_model方法")
        
        if len(trial_numbers) != 3:
            raise ValueError("试机号码必须是3位数字")
        
        # 构建特征向量
        trial_digits = [int(d) for d in trial_numbers]
        features = [
            trial_digits[0], trial_digits[1], trial_digits[2],
            sum(trial_digits),
            max(trial_digits) - min(trial_digits),
            len(set(trial_digits)),
            sum(1 for d in trial_digits if d % 2 == 1),
            sum(1 for d in trial_digits if d >= 5),
            trial_machine or 1,
            draw_machine or 1
        ]
        
        # 应用预测规则
        predictions = []
        confidence_scores = []
        
        rules = self.prediction_model.get('prediction_rules', {})
        
        # 基于和值规则预测
        trial_sum = sum(trial_digits)
        if 'sum_value_rules' in rules and str(trial_sum) in rules['sum_value_rules']:
            sum_rule = rules['sum_value_rules'][str(trial_sum)]
            if sum_rule['most_common']:
                predicted_sum = sum_rule['most_common'][0][0]
                confidence = sum_rule['most_common'][0][1] / sum_rule['count']
                predictions.append(('sum_based', predicted_sum, confidence))
        
        # 基于机器规则预测
        if trial_machine and draw_machine:
            machine_key = str((trial_machine, draw_machine))
            if 'machine_rules' in rules and machine_key in rules['machine_rules']:
                machine_rule = rules['machine_rules'][machine_key]
                if machine_rule['most_common']:
                    predicted_number = machine_rule['most_common'][0][0]
                    confidence = machine_rule['most_common'][0][1] / machine_rule['count']
                    predictions.append(('machine_based', predicted_number, confidence))
        
        # 基于位置规则预测
        position_predictions = []
        for i in range(3):
            pos_key = str((i, trial_digits[i]))
            if 'position_rules' in rules and pos_key in rules['position_rules']:
                pos_rule = rules['position_rules'][pos_key]
                if pos_rule['most_common']:
                    predicted_digit = pos_rule['most_common'][0][0]
                    confidence = pos_rule['most_common'][0][1] / pos_rule['count']
                    position_predictions.append((predicted_digit, confidence))
                else:
                    position_predictions.append((trial_digits[i], 0.1))  # 默认保持不变
            else:
                position_predictions.append((trial_digits[i], 0.1))
        
        if len(position_predictions) == 3:
            predicted_number = ''.join(str(p[0]) for p in position_predictions)
            avg_confidence = np.mean([p[1] for p in position_predictions])
            predictions.append(('position_based', predicted_number, avg_confidence))
        
        return {
            'trial_numbers': trial_numbers,
            'predictions': predictions,
            'feature_vector': features,
            'analysis_summary': {
                'trial_sum': trial_sum,
                'trial_span': max(trial_digits) - min(trial_digits),
                'trial_unique_count': len(set(trial_digits))
            }
        }
    
    def train_model(self) -> Dict[str, Any]:
        """
        训练试机号码预测模型
        
        Returns:
            训练结果和模型性能
        """
        print("开始训练试机号码预测模型...")
        
        # 加载数据
        records = self.load_trial_data()
        print(f"加载了 {len(records)} 条试机号码记录")
        
        if len(records) < 10:
            raise ValueError("试机号码数据不足，无法训练模型")
        
        # 执行各种分析
        print("分析位置差值关系...")
        position_analysis = self.analyze_position_differences(records)
        
        print("分析预热效应...")
        preheating_analysis = self.analyze_preheating_effect(records)
        
        print("分析机器关联性...")
        machine_analysis = self.analyze_machine_correlation(records)
        
        print("构建预测模型...")
        prediction_model = self.build_trial_prediction_model(records)
        
        # 保存分析结果
        self.analysis_results = {
            'position_analysis': position_analysis,
            'preheating_analysis': preheating_analysis,
            'machine_analysis': machine_analysis,
            'data_summary': {
                'total_records': len(records),
                'date_range': (records[0]['date'], records[-1]['date']) if records else None,
                'unique_trial_machines': len(set(r['trial_machine'] for r in records)),
                'unique_draw_machines': len(set(r['draw_machine'] for r in records))
            }
        }
        
        self.prediction_model = prediction_model
        
        print("试机号码预测模型训练完成!")
        return {
            'success': True,
            'model_performance': prediction_model,
            'analysis_results': self.analysis_results
        }


if __name__ == "__main__":
    # 测试代码
    analyzer = TrialNumberAnalyzer()
    
    try:
        # 训练模型
        result = analyzer.train_model()
        print("训练结果:", result['success'])
        
        # 测试预测
        if result['success']:
            test_prediction = analyzer.predict_from_trial("123", 1, 1)
            print("测试预测结果:", test_prediction)
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
