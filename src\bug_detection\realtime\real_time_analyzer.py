#!/usr/bin/env python3
"""
实时分析引擎
实现实时Bug模式识别、性能指标计算、异常检测算法
"""

import asyncio
import json
import logging
import re
import statistics
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple

from .event_bus import (Event, EventPriority, EventType, event_bus,
                        publish_event)
from .stream_processor import add_processing_rule, stream_processor

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AnalysisType(Enum):
    """分析类型"""
    PATTERN_RECOGNITION = "pattern_recognition"
    ANOMALY_DETECTION = "anomaly_detection"
    PERFORMANCE_ANALYSIS = "performance_analysis"
    TREND_ANALYSIS = "trend_analysis"

@dataclass
class BugPattern:
    """Bug模式"""
    id: str
    name: str
    pattern_type: str
    regex_patterns: List[str]
    keywords: List[str]
    severity_score: float
    frequency: int = 0
    last_seen: Optional[float] = None
    confidence: float = 0.0

@dataclass
class PerformanceMetrics:
    """性能指标"""
    endpoint: str
    avg_response_time: float
    min_response_time: float
    max_response_time: float
    request_count: int
    error_rate: float
    throughput: float
    timestamp: float

@dataclass
class AnomalyAlert:
    """异常告警"""
    id: str
    type: str
    severity: str
    message: str
    data: Dict[str, Any]
    timestamp: float
    confidence: float

class RealTimeAnalyzer:
    """实时分析引擎"""
    
    def __init__(self):
        # Bug模式库
        self.bug_patterns: Dict[str, BugPattern] = {}
        
        # 性能基线
        self.performance_baselines: Dict[str, Dict[str, float]] = defaultdict(dict)
        
        # 时间窗口数据
        self.time_windows: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        
        # 异常检测阈值
        self.anomaly_thresholds = {
            'error_rate_threshold': 0.05,  # 5%错误率
            'response_time_multiplier': 3.0,  # 3倍基线响应时间
            'frequency_threshold': 10,  # 10次/分钟
            'confidence_threshold': 0.8  # 80%置信度
        }
        
        # 分析统计
        self.analysis_stats = {
            'patterns_detected': 0,
            'anomalies_detected': 0,
            'performance_alerts': 0,
            'total_events_analyzed': 0
        }
        
        # 运行状态
        self.running = False
        self.analysis_tasks: List[asyncio.Task] = []
        
        # 初始化Bug模式
        self._initialize_bug_patterns()
    
    def _initialize_bug_patterns(self):
        """初始化Bug模式库"""
        patterns = [
            BugPattern(
                id="js_null_reference",
                name="JavaScript空引用错误",
                pattern_type="javascript_error",
                regex_patterns=[
                    r"Cannot read property .* of null",
                    r"Cannot read properties of null",
                    r"null is not an object"
                ],
                keywords=["null", "undefined", "property"],
                severity_score=0.8
            ),
            BugPattern(
                id="api_timeout",
                name="API超时错误",
                pattern_type="api_error",
                regex_patterns=[
                    r"timeout.*exceeded",
                    r"request.*timeout",
                    r"connection.*timeout"
                ],
                keywords=["timeout", "exceeded", "connection"],
                severity_score=0.7
            ),
            BugPattern(
                id="memory_leak",
                name="内存泄漏模式",
                pattern_type="performance_issue",
                regex_patterns=[
                    r"memory.*leak",
                    r"out of memory",
                    r"heap.*overflow"
                ],
                keywords=["memory", "leak", "heap", "overflow"],
                severity_score=0.9
            ),
            BugPattern(
                id="database_connection",
                name="数据库连接错误",
                pattern_type="database_error",
                regex_patterns=[
                    r"database.*connection.*failed",
                    r"connection.*refused",
                    r"database.*timeout"
                ],
                keywords=["database", "connection", "failed", "refused"],
                severity_score=0.8
            ),
            # 基础设施层监控模式
            BugPattern(
                id="websocket_connection_failure",
                name="WebSocket连接失败",
                pattern_type="infrastructure_error",
                regex_patterns=[
                    r"websocket.*connection.*failed",
                    r"websocket.*disconnected",
                    r"websocket.*timeout",
                    r"ws.*connection.*error"
                ],
                keywords=["websocket", "connection", "failed", "disconnected"],
                severity_score=0.7
            ),
            BugPattern(
                id="service_dependency_failure",
                name="服务依赖失败",
                pattern_type="infrastructure_error",
                regex_patterns=[
                    r"service.*unavailable",
                    r"dependency.*failed",
                    r"service.*timeout",
                    r"health.*check.*failed"
                ],
                keywords=["service", "dependency", "unavailable", "health"],
                severity_score=0.8
            ),
            BugPattern(
                id="redis_connection_error",
                name="Redis连接错误",
                pattern_type="infrastructure_error",
                regex_patterns=[
                    r"redis.*connection.*failed",
                    r"redis.*timeout",
                    r"redis.*unavailable"
                ],
                keywords=["redis", "connection", "failed", "timeout"],
                severity_score=0.7
            ),
            BugPattern(
                id="event_bus_failure",
                name="事件总线故障",
                pattern_type="infrastructure_error",
                regex_patterns=[
                    r"event.*bus.*failed",
                    r"event.*publish.*failed",
                    r"event.*subscription.*failed"
                ],
                keywords=["event", "bus", "failed", "publish", "subscription"],
                severity_score=0.8
            )
        ]
        
        for pattern in patterns:
            self.bug_patterns[pattern.id] = pattern
    
    async def analyze_javascript_errors(self, events: List[Event]):
        """分析JavaScript错误事件"""
        try:
            for event in events:
                self.analysis_stats['total_events_analyzed'] += 1
                
                # 提取错误信息
                error_message = event.data.get('message', '')
                stack_trace = event.data.get('stack_trace', '')
                
                # 模式匹配
                matched_patterns = self._match_bug_patterns(error_message, stack_trace)
                
                if matched_patterns:
                    for pattern in matched_patterns:
                        pattern.frequency += 1
                        pattern.last_seen = event.timestamp
                        
                        # 计算置信度
                        pattern.confidence = min(1.0, pattern.frequency / 10.0)
                        
                        # 发布模式检测事件
                        await publish_event(
                            EventType.BUG_DETECTED,
                            {
                                'pattern_id': pattern.id,
                                'pattern_name': pattern.name,
                                'severity_score': pattern.severity_score,
                                'confidence': pattern.confidence,
                                'original_event': event.to_dict()
                            },
                            EventPriority.HIGH if pattern.severity_score > 0.8 else EventPriority.MEDIUM,
                            "real_time_analyzer"
                        )
                        
                        self.analysis_stats['patterns_detected'] += 1
                
                # 异常检测
                await self._detect_error_anomalies(event)
                
        except Exception as e:
            logger.error(f"❌ JavaScript错误分析失败: {e}")
    
    async def analyze_api_performance(self, events: List[Event]):
        """分析API性能事件"""
        try:
            # 按端点分组
            endpoint_events = defaultdict(list)
            for event in events:
                endpoint = event.data.get('endpoint', 'unknown')
                endpoint_events[endpoint].append(event)
            
            # 分析每个端点
            for endpoint, endpoint_event_list in endpoint_events.items():
                await self._analyze_endpoint_performance(endpoint, endpoint_event_list)
                
        except Exception as e:
            logger.error(f"❌ API性能分析失败: {e}")

    async def analyze_infrastructure_events(self, events: List[Event]):
        """分析基础设施事件"""
        try:
            for event in events:
                self.analysis_stats['total_events_analyzed'] += 1

                # 提取基础设施事件信息
                service_name = event.data.get('service_name', 'unknown')
                error_message = event.data.get('message', '')
                component = event.data.get('component', 'unknown')

                # 模式匹配
                matched_patterns = self._match_infrastructure_patterns(error_message, service_name)

                if matched_patterns:
                    for pattern in matched_patterns:
                        pattern.frequency += 1
                        pattern.last_seen = event.timestamp

                        # 计算置信度
                        pattern.confidence = min(1.0, pattern.frequency / 5.0)

                        # 发布基础设施错误事件
                        await publish_event(
                            EventType.SYSTEM_ERROR,
                            {
                                'pattern_id': pattern.id,
                                'pattern_name': pattern.name,
                                'service_name': service_name,
                                'component': component,
                                'severity_score': pattern.severity_score,
                                'confidence': pattern.confidence,
                                'original_event': event.to_dict()
                            },
                            EventPriority.HIGH if pattern.severity_score > 0.8 else EventPriority.MEDIUM,
                            "infrastructure_analyzer"
                        )

                        self.analysis_stats['patterns_detected'] += 1

                # 服务依赖检查
                await self._check_service_dependencies(event)

        except Exception as e:
            logger.error(f"❌ 基础设施事件分析失败: {e}")

    def _match_infrastructure_patterns(self, error_message: str, service_name: str) -> List[BugPattern]:
        """匹配基础设施Bug模式"""
        matched_patterns = []
        combined_text = f"{error_message} {service_name}".lower()

        for pattern in self.bug_patterns.values():
            if pattern.pattern_type == "infrastructure_error":
                # 检查正则表达式匹配
                regex_match = any(
                    re.search(regex, combined_text, re.IGNORECASE)
                    for regex in pattern.regex_patterns
                )

                # 检查关键词匹配
                keyword_match = any(
                    keyword.lower() in combined_text
                    for keyword in pattern.keywords
                )

                if regex_match or keyword_match:
                    matched_patterns.append(pattern)

        return matched_patterns

    async def _check_service_dependencies(self, event: Event):
        """检查服务依赖状态"""
        try:
            service_name = event.data.get('service_name', '')

            # 定义关键服务依赖
            critical_services = {
                'database': ['sqlite', 'database', 'db'],
                'websocket': ['websocket', 'ws', 'socket'],
                'redis': ['redis', 'cache'],
                'api': ['api', 'fastapi', 'endpoint']
            }

            # 检查是否涉及关键服务
            for service_type, keywords in critical_services.items():
                if any(keyword in service_name.lower() for keyword in keywords):
                    # 发布服务依赖告警
                    await publish_event(
                        EventType.SYSTEM_ERROR,
                        {
                            'alert_type': 'service_dependency_failure',
                            'service_type': service_type,
                            'service_name': service_name,
                            'impact_level': 'high' if service_type in ['database', 'api'] else 'medium',
                            'original_event': event.to_dict()
                        },
                        EventPriority.HIGH,
                        "dependency_checker"
                    )
                    break

        except Exception as e:
            logger.error(f"❌ 服务依赖检查失败: {e}")

    async def _analyze_endpoint_performance(self, endpoint: str, events: List[Event]):
        """分析单个端点性能"""
        try:
            if not events:
                return
            
            # 提取性能数据
            response_times = []
            error_count = 0
            
            for event in events:
                response_time = event.data.get('response_time', 0)
                status_code = event.data.get('status_code', 200)
                
                response_times.append(response_time)
                if status_code >= 400:
                    error_count += 1
            
            if not response_times:
                return
            
            # 计算指标
            metrics = PerformanceMetrics(
                endpoint=endpoint,
                avg_response_time=statistics.mean(response_times),
                min_response_time=min(response_times),
                max_response_time=max(response_times),
                request_count=len(events),
                error_rate=error_count / len(events),
                throughput=len(events) / 60.0,  # 每分钟请求数
                timestamp=time.time()
            )
            
            # 更新基线
            await self._update_performance_baseline(endpoint, metrics)
            
            # 异常检测
            await self._detect_performance_anomalies(endpoint, metrics)
            
            # 存储到时间窗口
            self.time_windows[f"performance:{endpoint}"].append(metrics)
            
        except Exception as e:
            logger.error(f"❌ 端点性能分析失败 {endpoint}: {e}")
    
    def _match_bug_patterns(self, error_message: str, stack_trace: str) -> List[BugPattern]:
        """匹配Bug模式"""
        matched_patterns = []
        combined_text = f"{error_message} {stack_trace}".lower()
        
        for pattern in self.bug_patterns.values():
            # 正则匹配
            regex_match = any(
                re.search(regex, combined_text, re.IGNORECASE)
                for regex in pattern.regex_patterns
            )
            
            # 关键词匹配
            keyword_match = any(
                keyword.lower() in combined_text
                for keyword in pattern.keywords
            )
            
            if regex_match or keyword_match:
                matched_patterns.append(pattern)
        
        return matched_patterns
    
    async def _detect_error_anomalies(self, event: Event):
        """检测错误异常"""
        try:
            error_type = event.data.get('type', 'unknown')
            
            # 添加到时间窗口
            window_key = f"errors:{error_type}"
            self.time_windows[window_key].append(event)
            
            # 计算频率
            current_time = time.time()
            recent_events = [
                e for e in self.time_windows[window_key]
                if current_time - e.timestamp <= 60  # 最近1分钟
            ]
            
            frequency = len(recent_events)
            
            # 检查是否超过阈值
            if frequency > self.anomaly_thresholds['frequency_threshold']:
                await self._create_anomaly_alert(
                    "high_error_frequency",
                    "critical",
                    f"错误频率异常: {error_type} 在1分钟内出现{frequency}次",
                    {
                        'error_type': error_type,
                        'frequency': frequency,
                        'threshold': self.anomaly_thresholds['frequency_threshold']
                    },
                    0.9
                )
                
        except Exception as e:
            logger.error(f"❌ 错误异常检测失败: {e}")
    
    async def _update_performance_baseline(self, endpoint: str, metrics: PerformanceMetrics):
        """更新性能基线"""
        try:
            baseline = self.performance_baselines[endpoint]
            
            # 使用指数移动平均更新基线
            alpha = 0.1  # 平滑因子
            
            if 'avg_response_time' in baseline:
                baseline['avg_response_time'] = (
                    alpha * metrics.avg_response_time + 
                    (1 - alpha) * baseline['avg_response_time']
                )
            else:
                baseline['avg_response_time'] = metrics.avg_response_time
            
            if 'error_rate' in baseline:
                baseline['error_rate'] = (
                    alpha * metrics.error_rate + 
                    (1 - alpha) * baseline['error_rate']
                )
            else:
                baseline['error_rate'] = metrics.error_rate
            
            baseline['last_updated'] = time.time()
            
        except Exception as e:
            logger.error(f"❌ 性能基线更新失败: {e}")
    
    async def _detect_performance_anomalies(self, endpoint: str, metrics: PerformanceMetrics):
        """检测性能异常"""
        try:
            baseline = self.performance_baselines.get(endpoint, {})
            
            if not baseline:
                return
            
            # 响应时间异常
            baseline_response_time = baseline.get('avg_response_time', 0)
            if (baseline_response_time > 0 and 
                metrics.avg_response_time > baseline_response_time * self.anomaly_thresholds['response_time_multiplier']):
                
                await self._create_anomaly_alert(
                    "high_response_time",
                    "warning",
                    f"API响应时间异常: {endpoint} 响应时间 {metrics.avg_response_time:.2f}ms 超过基线 {baseline_response_time:.2f}ms",
                    {
                        'endpoint': endpoint,
                        'current_response_time': metrics.avg_response_time,
                        'baseline_response_time': baseline_response_time,
                        'multiplier': self.anomaly_thresholds['response_time_multiplier']
                    },
                    0.8
                )
            
            # 错误率异常
            if metrics.error_rate > self.anomaly_thresholds['error_rate_threshold']:
                await self._create_anomaly_alert(
                    "high_error_rate",
                    "critical",
                    f"API错误率异常: {endpoint} 错误率 {metrics.error_rate:.2%} 超过阈值 {self.anomaly_thresholds['error_rate_threshold']:.2%}",
                    {
                        'endpoint': endpoint,
                        'error_rate': metrics.error_rate,
                        'threshold': self.anomaly_thresholds['error_rate_threshold']
                    },
                    0.9
                )
                
        except Exception as e:
            logger.error(f"❌ 性能异常检测失败: {e}")
    
    async def _create_anomaly_alert(self, alert_type: str, severity: str, 
                                   message: str, data: Dict[str, Any], confidence: float):
        """创建异常告警"""
        try:
            alert = AnomalyAlert(
                id=f"alert_{int(time.time() * 1000)}",
                type=alert_type,
                severity=severity,
                message=message,
                data=data,
                timestamp=time.time(),
                confidence=confidence
            )
            
            # 发布告警事件
            priority = EventPriority.CRITICAL if severity == "critical" else EventPriority.HIGH
            
            await publish_event(
                EventType.ALERT_TRIGGERED,
                {
                    'alert_id': alert.id,
                    'alert_type': alert.type,
                    'severity': alert.severity,
                    'message': alert.message,
                    'data': alert.data,
                    'confidence': alert.confidence
                },
                priority,
                "real_time_analyzer"
            )
            
            self.analysis_stats['anomalies_detected'] += 1
            logger.warning(f"🚨 异常告警: {message}")
            
        except Exception as e:
            logger.error(f"❌ 异常告警创建失败: {e}")
    
    async def start(self):
        """启动实时分析引擎"""
        if self.running:
            return
        
        self.running = True
        
        # 注册处理规则
        add_processing_rule(
            "javascript_error_analysis",
            [EventType.JAVASCRIPT_ERROR],
            self.analyze_javascript_errors,
            batch_size=10,
            rate_limit=100
        )
        
        add_processing_rule(
            "api_performance_analysis",
            [EventType.API_PERFORMANCE],
            self.analyze_api_performance,
            batch_size=20,
            window_size=60
        )

        add_processing_rule(
            "infrastructure_analysis",
            [EventType.SYSTEM_ERROR, EventType.INFRASTRUCTURE_ERROR],
            self.analyze_infrastructure_events,
            batch_size=5,
            rate_limit=50
        )

        logger.info("🚀 实时分析引擎已启动（包含基础设施监控）")

    async def monitor_infrastructure_health(self):
        """监控基础设施健康状态"""
        try:
            # 检查WebSocket服务状态
            await self._check_websocket_health()

            # 检查数据库连接状态
            await self._check_database_health()

            # 检查Redis连接状态
            await self._check_redis_health()

        except Exception as e:
            logger.error(f"❌ 基础设施健康监控失败: {e}")

    async def _check_websocket_health(self):
        """检查WebSocket服务健康状态"""
        try:
            # 这里可以添加实际的WebSocket健康检查逻辑
            # 例如检查连接数、响应时间等
            pass
        except Exception as e:
            # 发布WebSocket错误事件
            await publish_event(
                EventType.INFRASTRUCTURE_ERROR,
                {
                    'service_name': 'websocket',
                    'component': 'websocket_manager',
                    'message': f'WebSocket健康检查失败: {str(e)}',
                    'error_type': 'websocket_connection_failure'
                },
                EventPriority.HIGH,
                "infrastructure_monitor"
            )

    async def _check_database_health(self):
        """检查数据库健康状态"""
        try:
            # 这里可以添加实际的数据库健康检查逻辑
            pass
        except Exception as e:
            # 发布数据库错误事件
            await publish_event(
                EventType.INFRASTRUCTURE_ERROR,
                {
                    'service_name': 'database',
                    'component': 'database_manager',
                    'message': f'数据库健康检查失败: {str(e)}',
                    'error_type': 'database_connection_error'
                },
                EventPriority.HIGH,
                "infrastructure_monitor"
            )

    async def _check_redis_health(self):
        """检查Redis健康状态"""
        try:
            # 这里可以添加实际的Redis健康检查逻辑
            pass
        except Exception as e:
            # 发布Redis错误事件
            await publish_event(
                EventType.INFRASTRUCTURE_ERROR,
                {
                    'service_name': 'redis',
                    'component': 'event_bus',
                    'message': f'Redis健康检查失败: {str(e)}',
                    'error_type': 'redis_connection_error'
                },
                EventPriority.MEDIUM,
                "infrastructure_monitor"
            )

    async def stop(self):
        """停止实时分析引擎"""
        self.running = False
        
        # 取消所有分析任务
        for task in self.analysis_tasks:
            task.cancel()
        
        if self.analysis_tasks:
            await asyncio.gather(*self.analysis_tasks, return_exceptions=True)
        
        logger.info("🛑 实时分析引擎已停止")
    
    def get_analysis_stats(self) -> Dict[str, Any]:
        """获取分析统计"""
        return {
            **self.analysis_stats,
            'bug_patterns': {
                pattern_id: {
                    'name': pattern.name,
                    'frequency': pattern.frequency,
                    'confidence': pattern.confidence,
                    'last_seen': pattern.last_seen
                }
                for pattern_id, pattern in self.bug_patterns.items()
            },
            'performance_baselines': dict(self.performance_baselines),
            'time_window_sizes': {
                key: len(window) for key, window in self.time_windows.items()
            }
        }
    
    def get_recent_patterns(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近检测到的模式"""
        patterns = sorted(
            self.bug_patterns.values(),
            key=lambda p: p.last_seen or 0,
            reverse=True
        )
        
        return [
            {
                'id': pattern.id,
                'name': pattern.name,
                'frequency': pattern.frequency,
                'confidence': pattern.confidence,
                'severity_score': pattern.severity_score,
                'last_seen': pattern.last_seen
            }
            for pattern in patterns[:limit]
        ]

# 全局实时分析引擎实例
real_time_analyzer = RealTimeAnalyzer()

# 初始化函数
async def initialize_real_time_analyzer():
    """初始化实时分析引擎"""
    await real_time_analyzer.start()
    logger.info("✅ 实时分析引擎初始化完成")
    return real_time_analyzer

if __name__ == "__main__":
    # 测试代码
    async def test_analyzer():
        await initialize_real_time_analyzer()
        
        # 等待分析
        await asyncio.sleep(10)
        
        # 获取统计
        stats = real_time_analyzer.get_analysis_stats()
        print(f"分析统计: {json.dumps(stats, indent=2, ensure_ascii=False)}")
        
        # 停止
        await real_time_analyzer.stop()
    
    asyncio.run(test_analyzer())
