import shutil
import os

# 移动优化建议页面（智能融合优化）
source = "src/ui/pages_disabled/optimization_suggestions.py"
dest = "src/ui/pages/optimization_suggestions.py"

if os.path.exists(source):
    shutil.move(source, dest)
    print(f"✅ 移动成功: {source} -> {dest}")
    print("✅ 智能融合优化页面恢复完成!")
else:
    print(f"❌ 源文件不存在: {source}")

# 验证移动结果
if os.path.exists(dest):
    print(f"✅ 目标文件存在: {dest}")
else:
    print(f"❌ 目标文件不存在: {dest}")
