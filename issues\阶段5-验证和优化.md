# 阶段5：验证和优化

**阶段状态**: 未开始  
**预计耗时**: 45分钟  
**依赖关系**: 阶段4完成  

## 📋 阶段目标

验证功能完整性，进行性能优化和稳定性测试，完善监控和文档。

## 🔧 具体任务

### 5.1 功能完整性测试

**操作内容**: 测试实时Bug检测、训练监控等功能

**测试项目**:

#### Bug检测功能测试
```javascript
// 在浏览器控制台执行，触发JavaScript错误
console.error("测试错误：WebSocket Bug检测功能");
throw new Error("WebSocket功能测试错误");

// 检查是否在后端收到错误报告
```

#### 训练监控功能测试
```python
# 创建测试脚本 test_training_monitor.py
import asyncio
import websockets
import json

async def test_training_monitor():
    uri = "ws://127.0.0.1:8888/ws/training/test-model"
    async with websockets.connect(uri) as websocket:
        # 发送训练开始消息
        await websocket.send(json.dumps({
            "type": "training_start",
            "model_id": "test-model",
            "epochs": 10
        }))
        
        # 接收响应
        response = await websocket.recv()
        print(f"训练监控响应: {response}")

asyncio.run(test_training_monitor())
```

#### 实时统计功能测试
```python
# 测试实时统计WebSocket
import asyncio
import websockets

async def test_realtime_stats():
    uri = "ws://127.0.0.1:8888/ws/realtime-stats"
    async with websockets.connect(uri) as websocket:
        # 接收实时统计数据
        for i in range(3):
            data = await websocket.recv()
            print(f"实时统计数据 {i+1}: {data}")
            await asyncio.sleep(5)

asyncio.run(test_realtime_stats())
```

**预期结果**: 
- Bug检测功能正常接收和处理错误
- 训练监控功能正常建立连接和通信
- 实时统计功能正常推送数据
- 所有功能响应时间 <2秒

**依赖库**: 
- websockets
- asyncio
- json

### 5.2 性能和稳定性测试

**操作内容**: 进行连接稳定性和并发测试

#### 连接稳定性测试
**文件路径**: `test_websocket_stability.py` (新建)

```python
#!/usr/bin/env python3
"""
WebSocket连接稳定性测试
"""

import asyncio
import websockets
import time
import logging
from typing import List, Dict

logger = logging.getLogger(__name__)

class StabilityTester:
    def __init__(self):
        self.results = []
    
    async def long_connection_test(self, duration_minutes: int = 30):
        """长连接稳定性测试"""
        uri = "ws://127.0.0.1:8888/ws/bug-detection"
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        
        try:
            async with websockets.connect(uri) as websocket:
                while time.time() < end_time:
                    # 发送心跳
                    await websocket.send('{"type": "ping"}')
                    response = await websocket.recv()
                    
                    # 记录响应时间
                    self.results.append({
                        'timestamp': time.time(),
                        'response_time': time.time() - start_time,
                        'status': 'success'
                    })
                    
                    await asyncio.sleep(10)  # 每10秒一次心跳
                    
        except Exception as e:
            logger.error(f"连接稳定性测试失败: {e}")
            return False
        
        return True
    
    async def concurrent_connection_test(self, num_clients: int = 10):
        """并发连接测试"""
        tasks = []
        for i in range(num_clients):
            task = asyncio.create_task(self.single_client_test(i))
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        success_count = sum(1 for r in results if r is True)
        return success_count, num_clients
    
    async def single_client_test(self, client_id: int):
        """单个客户端测试"""
        try:
            uri = "ws://127.0.0.1:8888/ws/realtime-stats"
            async with websockets.connect(uri) as websocket:
                # 保持连接5分钟
                for _ in range(30):  # 30次，每次10秒
                    data = await asyncio.wait_for(websocket.recv(), timeout=15)
                    await asyncio.sleep(10)
                return True
        except Exception as e:
            logger.error(f"客户端 {client_id} 测试失败: {e}")
            return False

async def main():
    tester = StabilityTester()
    
    print("🚀 开始WebSocket稳定性测试...")
    
    # 并发连接测试
    print("📊 并发连接测试...")
    success, total = await tester.concurrent_connection_test(10)
    print(f"并发测试结果: {success}/{total} 成功")
    
    # 长连接测试（5分钟）
    print("⏱️ 长连接稳定性测试...")
    stable = await tester.long_connection_test(5)
    print(f"稳定性测试: {'通过' if stable else '失败'}")

if __name__ == "__main__":
    asyncio.run(main())
```

**性能基准测试**:
```bash
# 运行稳定性测试
python test_websocket_stability.py

# 监控系统资源使用
top -p $(pgrep -f "production_main.py")
```

**预期结果**: 
- 连接稳定性 >95%
- 并发连接支持 ≥10个客户端
- 平均响应时间 <100ms
- 内存使用增长 <10%
- CPU使用率增长 <5%

**依赖库**: 
- websockets
- asyncio
- psutil (系统监控)

### 5.3 创建监控和文档

**操作内容**: 设置监控指标并更新文档

#### 监控脚本
**文件路径**: `websocket_monitor.py` (新建)

```python
#!/usr/bin/env python3
"""
WebSocket监控脚本
实时监控WebSocket连接状态和性能指标
"""

import time
import json
import requests
import psutil
from datetime import datetime

class WebSocketMonitor:
    def __init__(self):
        self.api_base = "http://127.0.0.1:8888"
    
    def get_websocket_stats(self):
        """获取WebSocket统计信息"""
        try:
            response = requests.get(f"{self.api_base}/api/v1/health/websocket/connections")
            return response.json() if response.status_code == 200 else {}
        except:
            return {}
    
    def get_system_stats(self):
        """获取系统资源统计"""
        return {
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent,
            'timestamp': datetime.now().isoformat()
        }
    
    def generate_report(self):
        """生成监控报告"""
        websocket_stats = self.get_websocket_stats()
        system_stats = self.get_system_stats()
        
        report = {
            'timestamp': time.time(),
            'websocket_stats': websocket_stats,
            'system_stats': system_stats
        }
        
        return report
    
    def save_report(self, report):
        """保存监控报告"""
        filename = f"websocket_monitor_{int(time.time())}.json"
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        return filename

def main():
    monitor = WebSocketMonitor()
    report = monitor.generate_report()
    filename = monitor.save_report(report)
    
    print(f"监控报告已保存: {filename}")
    print(json.dumps(report, indent=2))

if __name__ == "__main__":
    main()
```

#### 更新README文档
**文件路径**: `README.md` (更新)

在README.md中添加WebSocket功能说明：

```markdown
## WebSocket实时功能

### 功能特性
- 🔍 实时Bug检测和错误监控
- 📊 训练进度实时显示
- 📈 系统状态实时推送
- 🔄 自动重连和降级机制

### WebSocket端点
- `/ws/bug-detection` - Bug检测实时通信
- `/ws/realtime-stats` - 实时统计数据推送
- `/ws/training/{model_id}` - 模型训练监控

### 健康检查
- `GET /api/v1/health/websocket` - WebSocket服务健康状态
- `GET /api/v1/health/websocket/connections` - 连接统计信息

### 故障排除
如果WebSocket连接失败，系统会自动启用API轮询降级模式，确保所有功能正常使用。
```

**预期结果**: 
- 监控脚本正常运行
- 生成详细的监控报告
- README文档更新完成
- 用户指南清晰明确

**依赖库**: 
- requests
- psutil
- json

### 5.4 记录修复经验

**操作内容**: 将修复过程和经验记录到知识图谱

**记录内容**:
1. 修复过程总结
2. 遇到的问题和解决方案
3. 性能优化经验
4. 最佳实践建议
5. 未来改进方向

**具体步骤**:
```python
# 将在执行阶段使用知识图谱工具记录
# 包括但不限于：
# - 依赖安装经验
# - 代码修复要点
# - 测试验证方法
# - 性能优化技巧
# - 监控配置经验
```

**预期结果**: 
- 完整的修复经验记录
- 可复用的解决方案
- 知识图谱更新完成
- 团队知识积累

## ✅ 验收标准

- [ ] Bug检测功能测试通过
- [ ] 训练监控功能测试通过
- [ ] 实时统计功能测试通过
- [ ] 连接稳定性 >95%
- [ ] 并发连接支持 ≥10个
- [ ] 平均响应时间 <100ms
- [ ] 监控脚本创建完成
- [ ] README文档更新完成
- [ ] 修复经验记录完成

## 🚨 注意事项

1. **测试环境**: 确保在稳定的网络环境中进行测试
2. **资源监控**: 密切关注系统资源使用情况
3. **数据备份**: 测试前备份重要数据
4. **用户通知**: 如需在生产环境测试，提前通知用户
5. **回滚准备**: 准备好快速回滚方案

## 📝 执行记录

### 执行时间
- 开始时间: ___________
- 结束时间: ___________
- 实际耗时: ___________

### 执行结果
- [ ] 任务5.1完成
- [ ] 任务5.2完成
- [ ] 任务5.3完成
- [ ] 任务5.4完成
- [ ] 阶段验收通过

### 问题记录
- 遇到的问题: ___________
- 解决方案: ___________
- 经验教训: ___________

## 🎉 项目完成

完成本阶段后，WebSocket修复项目全部完成！

### 最终验收
- [ ] 所有阶段任务完成
- [ ] 功能验收通过
- [ ] 性能指标达标
- [ ] 文档更新完成
- [ ] 知识记录完成

### 后续维护
- 定期运行监控脚本
- 关注WebSocket连接状态
- 收集用户反馈
- 持续优化性能
