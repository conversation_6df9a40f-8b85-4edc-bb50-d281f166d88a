#!/usr/bin/env python3
"""
实时通知系统
创建日期: 2025年7月24日
用途: 实现多渠道通知支持、告警规则引擎、通知去重和聚合
"""

import asyncio
import hashlib
import json
import logging
import smtplib
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta

try:
    from email.mime.multipart import MimeMultipart
    from email.mime.text import MimeText
    EMAIL_SUPPORT = True
except ImportError:
    # 如果邮件库不可用，创建占位符类
    class MimeMultipart:
        def __init__(self, *args, **kwargs):
            pass
        def attach(self, *args, **kwargs):
            pass
        def as_string(self):
            return ""

    class MimeText:
        def __init__(self, *args, **kwargs):
            pass

    EMAIL_SUPPORT = False
    # 注意：logger在这里还没有定义，所以先不记录日志
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 现在可以安全地记录邮件库不可用的警告
if not EMAIL_SUPPORT:
    logger.warning("⚠️ 邮件库不可用，邮件通知功能将被禁用")

class NotificationChannel(Enum):
    """通知渠道"""
    EMAIL = "email"
    WEBHOOK = "webhook"
    WEBSOCKET = "websocket"
    LOG = "log"
    CONSOLE = "console"

class NotificationPriority(Enum):
    """通知优先级"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class NotificationRule:
    """通知规则"""
    id: str
    name: str
    event_types: List[str]
    channels: List[NotificationChannel]
    priority_threshold: NotificationPriority
    conditions: Dict[str, Any] = field(default_factory=dict)
    template: str = ""
    enabled: bool = True
    rate_limit: Optional[int] = None  # 每分钟最大通知数
    cooldown: Optional[int] = None    # 冷却时间（秒）

@dataclass
class Notification:
    """通知消息"""
    id: str
    rule_id: str
    title: str
    message: str
    priority: NotificationPriority
    channels: List[NotificationChannel]
    data: Dict[str, Any]
    timestamp: float
    hash: str = ""
    sent_channels: Set[str] = field(default_factory=set)
    retry_count: int = 0
    
    def __post_init__(self):
        if not self.hash:
            self.hash = self._generate_hash()
    
    def _generate_hash(self) -> str:
        """生成通知哈希用于去重"""
        content = f"{self.title}_{self.message}_{self.priority.value}"
        return hashlib.md5(content.encode()).hexdigest()[:16]

class NotificationManager:
    """实时通知管理器"""
    
    def __init__(self, database_manager=None, websocket_manager=None):
        self.db_manager = database_manager
        self.websocket_manager = websocket_manager
        
        # 通知规则
        self.rules: Dict[str, NotificationRule] = {}
        
        # 通知历史
        self.notification_history: deque = deque(maxlen=1000)
        
        # 去重缓存
        self.dedup_cache: Dict[str, float] = {}
        self.cache_ttl = 3600  # 1小时
        
        # 速率限制
        self.rate_limiters: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # 冷却时间跟踪
        self.cooldown_tracker: Dict[str, float] = {}
        
        # 通知渠道配置
        self.channel_configs = {
            NotificationChannel.EMAIL: {
                'smtp_server': 'localhost',
                'smtp_port': 587,
                'username': '',
                'password': '',
                'from_email': 'bug-detection@localhost'
            },
            NotificationChannel.WEBHOOK: {
                'urls': [],
                'timeout': 10,
                'retry_attempts': 3
            }
        }
        
        # 统计信息
        self.stats = {
            'total_notifications': 0,
            'sent_notifications': 0,
            'failed_notifications': 0,
            'deduplicated_notifications': 0,
            'rate_limited_notifications': 0
        }
        
        # 运行状态
        self.running = False
        self.background_tasks: List[asyncio.Task] = []
        
        # 初始化默认规则
        self._initialize_default_rules()
    
    def _initialize_default_rules(self):
        """初始化默认通知规则"""
        default_rules = [
            NotificationRule(
                id="critical_errors",
                name="严重错误通知",
                event_types=["javascript_error", "api_error", "system_error"],
                channels=[NotificationChannel.WEBSOCKET, NotificationChannel.LOG],
                priority_threshold=NotificationPriority.HIGH,
                conditions={"severity": "critical"},
                template="🚨 严重错误: {message}",
                rate_limit=10,
                cooldown=60
            ),
            NotificationRule(
                id="performance_alerts",
                name="性能告警通知",
                event_types=["api_performance", "system_health"],
                channels=[NotificationChannel.WEBSOCKET, NotificationChannel.CONSOLE],
                priority_threshold=NotificationPriority.MEDIUM,
                conditions={"response_time": ">2.0"},
                template="⚡ 性能告警: {message}",
                rate_limit=5,
                cooldown=300
            ),
            NotificationRule(
                id="bug_patterns",
                name="Bug模式检测通知",
                event_types=["bug_detected"],
                channels=[NotificationChannel.WEBSOCKET, NotificationChannel.LOG],
                priority_threshold=NotificationPriority.MEDIUM,
                template="🧩 Bug模式检测: {message}",
                rate_limit=20
            ),
            NotificationRule(
                id="system_alerts",
                name="系统告警通知",
                event_types=["alert_triggered"],
                channels=[NotificationChannel.WEBSOCKET, NotificationChannel.CONSOLE, NotificationChannel.LOG],
                priority_threshold=NotificationPriority.LOW,
                template="🔔 系统告警: {message}"
            )
        ]
        
        for rule in default_rules:
            self.rules[rule.id] = rule
    
    def add_rule(self, rule: NotificationRule):
        """添加通知规则"""
        self.rules[rule.id] = rule
        logger.info(f"添加通知规则: {rule.name}")
    
    def remove_rule(self, rule_id: str):
        """移除通知规则"""
        if rule_id in self.rules:
            del self.rules[rule_id]
            logger.info(f"移除通知规则: {rule_id}")
    
    def update_channel_config(self, channel: NotificationChannel, config: Dict[str, Any]):
        """更新渠道配置"""
        if channel in self.channel_configs:
            self.channel_configs[channel].update(config)
            logger.info(f"更新通知渠道配置: {channel.value}")
    
    async def process_event(self, event_data: Dict[str, Any]):
        """处理事件并生成通知"""
        try:
            event_type = event_data.get('event_type', event_data.get('type', 'unknown'))
            
            # 查找匹配的规则
            matching_rules = self._find_matching_rules(event_type, event_data)
            
            for rule in matching_rules:
                # 检查条件
                if not self._check_conditions(rule, event_data):
                    continue
                
                # 检查速率限制
                if not self._check_rate_limit(rule):
                    self.stats['rate_limited_notifications'] += 1
                    continue
                
                # 检查冷却时间
                if not self._check_cooldown(rule):
                    continue
                
                # 生成通知
                notification = await self._create_notification(rule, event_data)
                
                # 检查去重
                if self._is_duplicate(notification):
                    self.stats['deduplicated_notifications'] += 1
                    continue
                
                # 发送通知
                await self._send_notification(notification)
                
        except Exception as e:
            logger.error(f"处理事件通知失败: {e}")
    
    def _find_matching_rules(self, event_type: str, event_data: Dict[str, Any]) -> List[NotificationRule]:
        """查找匹配的通知规则"""
        matching_rules = []
        
        for rule in self.rules.values():
            if not rule.enabled:
                continue
            
            # 检查事件类型匹配
            if event_type in rule.event_types or '*' in rule.event_types:
                matching_rules.append(rule)
        
        return matching_rules
    
    def _check_conditions(self, rule: NotificationRule, event_data: Dict[str, Any]) -> bool:
        """检查规则条件"""
        if not rule.conditions:
            return True
        
        try:
            for key, condition in rule.conditions.items():
                value = event_data.get(key)
                
                if isinstance(condition, str):
                    if condition.startswith('>'):
                        threshold = float(condition[1:])
                        if not (isinstance(value, (int, float)) and value > threshold):
                            return False
                    elif condition.startswith('<'):
                        threshold = float(condition[1:])
                        if not (isinstance(value, (int, float)) and value < threshold):
                            return False
                    elif condition.startswith('='):
                        if str(value) != condition[1:]:
                            return False
                    else:
                        if str(value) != condition:
                            return False
                else:
                    if value != condition:
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"检查规则条件失败: {e}")
            return False
    
    def _check_rate_limit(self, rule: NotificationRule) -> bool:
        """检查速率限制"""
        if not rule.rate_limit:
            return True
        
        current_time = time.time()
        rate_limiter = self.rate_limiters[rule.id]
        
        # 清理过期记录
        while rate_limiter and current_time - rate_limiter[0] > 60:
            rate_limiter.popleft()
        
        # 检查是否超过限制
        if len(rate_limiter) >= rule.rate_limit:
            return False
        
        # 记录当前时间
        rate_limiter.append(current_time)
        return True
    
    def _check_cooldown(self, rule: NotificationRule) -> bool:
        """检查冷却时间"""
        if not rule.cooldown:
            return True
        
        current_time = time.time()
        last_sent = self.cooldown_tracker.get(rule.id, 0)
        
        if current_time - last_sent < rule.cooldown:
            return False
        
        self.cooldown_tracker[rule.id] = current_time
        return True
    
    async def _create_notification(self, rule: NotificationRule, event_data: Dict[str, Any]) -> Notification:
        """创建通知"""
        # 格式化消息
        message = rule.template.format(**event_data) if rule.template else str(event_data.get('message', ''))
        
        # 确定优先级
        event_priority = event_data.get('priority', 'medium')
        if event_priority == 'critical':
            priority = NotificationPriority.CRITICAL
        elif event_priority == 'high':
            priority = NotificationPriority.HIGH
        elif event_priority == 'low':
            priority = NotificationPriority.LOW
        else:
            priority = NotificationPriority.MEDIUM
        
        # 创建通知
        notification = Notification(
            id=f"notif_{int(time.time() * 1000)}_{rule.id}",
            rule_id=rule.id,
            title=f"Bug检测通知 - {rule.name}",
            message=message,
            priority=priority,
            channels=rule.channels,
            data=event_data,
            timestamp=time.time()
        )
        
        return notification
    
    def _is_duplicate(self, notification: Notification) -> bool:
        """检查是否为重复通知"""
        current_time = time.time()
        
        # 清理过期缓存
        expired_hashes = [
            h for h, t in self.dedup_cache.items()
            if current_time - t > self.cache_ttl
        ]
        for h in expired_hashes:
            del self.dedup_cache[h]
        
        # 检查重复
        if notification.hash in self.dedup_cache:
            return True
        
        # 添加到缓存
        self.dedup_cache[notification.hash] = current_time
        return False
    
    async def _send_notification(self, notification: Notification):
        """发送通知"""
        try:
            self.stats['total_notifications'] += 1
            
            # 添加到历史记录
            self.notification_history.append(notification)
            
            # 并发发送到各个渠道
            send_tasks = []
            for channel in notification.channels:
                task = asyncio.create_task(self._send_to_channel(notification, channel))
                send_tasks.append(task)
            
            # 等待所有发送任务完成
            results = await asyncio.gather(*send_tasks, return_exceptions=True)
            
            # 统计结果
            success_count = sum(1 for result in results if result is True)
            if success_count > 0:
                self.stats['sent_notifications'] += 1
            else:
                self.stats['failed_notifications'] += 1
            
            logger.info(f"通知已发送: {notification.id}, 成功渠道: {success_count}/{len(notification.channels)}")
            
        except Exception as e:
            logger.error(f"发送通知失败: {e}")
            self.stats['failed_notifications'] += 1
    
    async def _send_to_channel(self, notification: Notification, channel: NotificationChannel) -> bool:
        """发送通知到指定渠道"""
        try:
            if channel == NotificationChannel.WEBSOCKET:
                return await self._send_websocket(notification)
            elif channel == NotificationChannel.EMAIL:
                return await self._send_email(notification)
            elif channel == NotificationChannel.WEBHOOK:
                return await self._send_webhook(notification)
            elif channel == NotificationChannel.LOG:
                return self._send_log(notification)
            elif channel == NotificationChannel.CONSOLE:
                return self._send_console(notification)
            else:
                logger.warning(f"未知通知渠道: {channel}")
                return False
                
        except Exception as e:
            logger.error(f"发送到渠道 {channel.value} 失败: {e}")
            return False
    
    async def _send_websocket(self, notification: Notification) -> bool:
        """通过WebSocket发送通知"""
        try:
            if self.websocket_manager:
                message = {
                    'type': 'notification',
                    'notification_id': notification.id,
                    'title': notification.title,
                    'message': notification.message,
                    'priority': notification.priority.value,
                    'timestamp': notification.timestamp,
                    'data': notification.data
                }
                
                # 广播给所有连接的客户端
                sent_count = await self.websocket_manager.broadcast(message)
                return sent_count > 0
            
            return False
            
        except Exception as e:
            logger.error(f"WebSocket通知发送失败: {e}")
            return False
    
    def _send_log(self, notification: Notification) -> bool:
        """记录到日志"""
        try:
            log_message = f"[{notification.priority.name}] {notification.title}: {notification.message}"
            
            if notification.priority == NotificationPriority.CRITICAL:
                logger.critical(log_message)
            elif notification.priority == NotificationPriority.HIGH:
                logger.error(log_message)
            elif notification.priority == NotificationPriority.MEDIUM:
                logger.warning(log_message)
            else:
                logger.info(log_message)
            
            return True
            
        except Exception as e:
            logger.error(f"日志通知失败: {e}")
            return False
    
    def _send_console(self, notification: Notification) -> bool:
        """输出到控制台"""
        try:
            timestamp = datetime.fromtimestamp(notification.timestamp).strftime('%H:%M:%S')
            priority_icon = {
                NotificationPriority.CRITICAL: "🔴",
                NotificationPriority.HIGH: "🟠", 
                NotificationPriority.MEDIUM: "🟡",
                NotificationPriority.LOW: "🟢"
            }.get(notification.priority, "⚪")
            
            print(f"{priority_icon} [{timestamp}] {notification.title}: {notification.message}")
            return True
            
        except Exception as e:
            logger.error(f"控制台通知失败: {e}")
            return False

    async def _send_email(self, notification: Notification) -> bool:
        """通过邮件发送通知"""
        try:
            config = self.channel_configs[NotificationChannel.EMAIL]

            if not config.get('username') or not config.get('password'):
                logger.warning("邮件配置不完整，跳过邮件发送")
                return False

            # 创建邮件
            msg = MimeMultipart()
            msg['From'] = config['from_email']
            msg['To'] = config.get('to_email', 'admin@localhost')
            msg['Subject'] = notification.title

            # 邮件正文
            body = f"""
            {notification.message}

            时间: {datetime.fromtimestamp(notification.timestamp).strftime('%Y-%m-%d %H:%M:%S')}
            优先级: {notification.priority.name}
            规则ID: {notification.rule_id}

            详细信息:
            {json.dumps(notification.data, indent=2, ensure_ascii=False)}
            """

            msg.attach(MimeText(body, 'plain', 'utf-8'))

            # 发送邮件
            server = smtplib.SMTP(config['smtp_server'], config['smtp_port'])
            server.starttls()
            server.login(config['username'], config['password'])
            server.send_message(msg)
            server.quit()

            return True

        except Exception as e:
            logger.error(f"邮件通知发送失败: {e}")
            return False

    async def _send_webhook(self, notification: Notification) -> bool:
        """通过Webhook发送通知"""
        try:
            import aiohttp

            config = self.channel_configs[NotificationChannel.WEBHOOK]
            urls = config.get('urls', [])

            if not urls:
                logger.warning("未配置Webhook URL，跳过Webhook发送")
                return False

            # 准备Webhook数据
            webhook_data = {
                'notification_id': notification.id,
                'title': notification.title,
                'message': notification.message,
                'priority': notification.priority.name,
                'timestamp': notification.timestamp,
                'rule_id': notification.rule_id,
                'data': notification.data
            }

            success_count = 0

            async with aiohttp.ClientSession() as session:
                for url in urls:
                    try:
                        async with session.post(
                            url,
                            json=webhook_data,
                            timeout=aiohttp.ClientTimeout(total=config.get('timeout', 10))
                        ) as response:
                            if response.status == 200:
                                success_count += 1
                            else:
                                logger.warning(f"Webhook响应错误 {url}: {response.status}")

                    except Exception as e:
                        logger.error(f"Webhook发送失败 {url}: {e}")

            return success_count > 0

        except ImportError:
            logger.warning("aiohttp未安装，无法发送Webhook通知")
            return False
        except Exception as e:
            logger.error(f"Webhook通知发送失败: {e}")
            return False

    async def start(self):
        """启动通知管理器"""
        if self.running:
            return

        self.running = True

        # 启动清理任务
        cleanup_task = asyncio.create_task(self._cleanup_loop())
        self.background_tasks.append(cleanup_task)

        logger.info("🚀 实时通知管理器已启动")

    async def stop(self):
        """停止通知管理器"""
        self.running = False

        # 取消所有后台任务
        for task in self.background_tasks:
            task.cancel()

        if self.background_tasks:
            await asyncio.gather(*self.background_tasks, return_exceptions=True)

        logger.info("🛑 实时通知管理器已停止")

    async def _cleanup_loop(self):
        """清理循环"""
        while self.running:
            try:
                await self._cleanup_old_data()
                await asyncio.sleep(3600)  # 每小时清理一次
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"清理任务失败: {e}")
                await asyncio.sleep(3600)

    async def _cleanup_old_data(self):
        """清理过期数据"""
        current_time = time.time()

        # 清理去重缓存
        expired_hashes = [
            h for h, t in self.dedup_cache.items()
            if current_time - t > self.cache_ttl
        ]
        for h in expired_hashes:
            del self.dedup_cache[h]

        # 清理速率限制记录
        for rule_id, rate_limiter in self.rate_limiters.items():
            while rate_limiter and current_time - rate_limiter[0] > 60:
                rate_limiter.popleft()

        # 清理冷却时间记录
        expired_cooldowns = [
            rule_id for rule_id, last_time in self.cooldown_tracker.items()
            if current_time - last_time > 3600  # 1小时后清理
        ]
        for rule_id in expired_cooldowns:
            del self.cooldown_tracker[rule_id]

        logger.debug(f"清理了 {len(expired_hashes)} 个过期缓存记录")

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            'active_rules': len([r for r in self.rules.values() if r.enabled]),
            'total_rules': len(self.rules),
            'dedup_cache_size': len(self.dedup_cache),
            'notification_history_size': len(self.notification_history),
            'rate_limiters': {
                rule_id: len(limiter)
                for rule_id, limiter in self.rate_limiters.items()
            }
        }

    def get_recent_notifications(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的通知"""
        recent = list(self.notification_history)[-limit:]
        return [
            {
                'id': n.id,
                'title': n.title,
                'message': n.message,
                'priority': n.priority.name,
                'timestamp': n.timestamp,
                'channels': [c.value for c in n.channels],
                'sent_channels': list(n.sent_channels)
            }
            for n in recent
        ]

# 全局通知管理器实例
notification_manager = NotificationManager()

# 便捷函数
async def send_notification(title: str, message: str,
                           priority: NotificationPriority = NotificationPriority.MEDIUM,
                           channels: List[NotificationChannel] = None,
                           data: Dict[str, Any] = None):
    """发送通知的便捷函数"""
    if channels is None:
        channels = [NotificationChannel.WEBSOCKET, NotificationChannel.LOG]

    notification = Notification(
        id=f"manual_{int(time.time() * 1000)}",
        rule_id="manual",
        title=title,
        message=message,
        priority=priority,
        channels=channels,
        data=data or {},
        timestamp=time.time()
    )

    await notification_manager._send_notification(notification)

async def initialize_notification_manager(database_manager=None, websocket_manager=None):
    """初始化通知管理器"""
    global notification_manager
    notification_manager = NotificationManager(database_manager, websocket_manager)
    await notification_manager.start()
    logger.info("✅ 实时通知管理器初始化完成")
    return notification_manager

if __name__ == "__main__":
    # 测试代码
    async def test_notification_manager():
        await initialize_notification_manager()

        # 发送测试通知
        await send_notification(
            "测试通知",
            "这是一个测试通知消息",
            NotificationPriority.HIGH,
            [NotificationChannel.CONSOLE, NotificationChannel.LOG]
        )

        # 等待处理
        await asyncio.sleep(2)

        # 获取统计
        stats = notification_manager.get_stats()
        print(f"通知统计: {json.dumps(stats, indent=2, ensure_ascii=False)}")

        # 停止
        await notification_manager.stop()

    asyncio.run(test_notification_manager())
