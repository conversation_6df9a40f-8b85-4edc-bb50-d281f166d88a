{"test_timestamp": "2025-07-21T18:19:17.820411", "test_environment": {"api_base": "http://127.0.0.1:8888", "ui_base": "http://127.0.0.1:8501"}, "test_results": {"api_tests": {"health_check": {"status": "PASS", "status_code": 200, "response_time": 0.008998870849609375, "response_size": 125, "success": true, "has_json_response": true, "response_keys": ["status", "timestamp", "database_records", "date_range"]}, "single_best_prediction": {"status": "FAIL", "status_code": 404, "response_time": 0.01500082015991211, "response_size": 22, "success": false}, "model_performance": {"status": "FAIL", "status_code": 404, "response_time": 0.003000020980834961, "response_size": 22, "success": false}}, "ui_tests": {"homepage_load": {"status": "PASS", "response_time": 0.01500082015991211, "success": true}, "prediction_page_load": {"status": "PASS", "response_time": 0.003000497817993164, "success": true}, "navigation_menu": {"status": "PASS", "response_time": 0.0, "success": true}}, "performance_tests": {"api_response_time": {"average_time": 0.006200027465820312, "max_time": 0.0070002079010009766, "min_time": 0.005999565124511719, "target_time": 1.0, "meets_target": true, "iterations": 5, "success_rate": 1.0}, "prediction_response_time": {"average_time": 0.006184021631876628, "max_time": 0.01355290412902832, "min_time": 0.001999378204345703, "target_time": 5.0, "meets_target": true, "iterations": 3, "success_rate": 1.0}, "ui_load_time": {"average_time": 0.003333886464436849, "max_time": 0.004000186920166016, "min_time": 0.003000497817993164, "target_time": 3.0, "meets_target": true, "iterations": 3, "success_rate": 1.0}}, "data_flow_tests": {"prediction_workflow": {"steps_completed": 4, "total_steps": 4, "success": true, "errors": []}, "model_performance_workflow": {"steps_completed": 4, "total_steps": 4, "success": true, "errors": []}}}, "summary": {"total_tests": 11, "passed_tests": 6, "failed_tests": 5, "success_rate": 0.5454545454545454, "categories_tested": ["api_tests", "ui_tests", "performance_tests", "data_flow_tests"]}}