{"model_params": {"transition_window_size": 500, "probability_window_size": 250, "smoothing_alpha": 1.0}, "validation_params": {"k_folds": 2, "data_limit": 400}, "fold_results": [{"fold_idx": 0, "train_size": 200, "val_size": 200, "predictions_count": 200, "accuracy_metrics": {"exact_match": 0.0, "digit_accuracy": 0.24333333333333335, "position_accuracy": [0.105, 0.125, 0.095], "total_predictions": 200}, "diversity_metrics": {"simpson_diversity": 0.99035, "unique_ratio": 0.675, "entropy": 6.907564284943829, "unique_count": 135, "total_count": 200}, "stability_metrics": {"variance": 100115.52189999999, "std_dev": 316.4103694571339, "coefficient_of_variation": 0.6438826427161309, "mean": 491.41}, "aic_bic": {"aic": 9430.340371976183, "bic": 9793.155282296466, "log_likelihood": -4605.170185988091, "num_params": 110, "num_samples": 200}}], "overall_results": {"accuracy_metrics": {"exact_match": 0.0, "digit_accuracy": 0.24333333333333335, "position_accuracy": [0.105, 0.125, 0.095], "total_predictions": 200}, "diversity_metrics": {"simpson_diversity": 0.99035, "unique_ratio": 0.675, "entropy": 6.907564284943829, "unique_count": 135, "total_count": 200}, "stability_metrics": {"variance": 100115.52189999999, "std_dev": 316.4103694571339, "coefficient_of_variation": 0.6438826427161309, "mean": 491.41}, "aic_bic": {"aic": 9430.340371976183, "bic": 9793.155282296466, "log_likelihood": -4605.170185988091, "num_params": 110, "num_samples": 200}, "total_predictions": 200}, "metadata": {"generated_at": "2025-07-17T16:29:03.285888", "validator_version": "1.0", "database_path": "data\\lottery.db"}}