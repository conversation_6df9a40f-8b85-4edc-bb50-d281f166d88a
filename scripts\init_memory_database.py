#!/usr/bin/env python3
"""
数据库初始化脚本
创建数据库表结构，初始化Redis配置，设置索引约束，数据迁移备份机制
"""

import os
import sys
import asyncio
import sqlite3
import json
import shutil
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

try:
    import psycopg2
    POSTGRESQL_AVAILABLE = True
except ImportError:
    POSTGRESQL_AVAILABLE = False

from src.model_library.memory.hierarchical_memory import HierarchicalTrainingMemoryDB
from src.model_library.memory.training_record import TrainingRecord, Knowledge


class DatabaseInitializer:
    """数据库初始化器"""
    
    def __init__(self, config_path: str = "config/database_config.json"):
        self.config_path = config_path
        self.config = self._load_config()
        self.backup_dir = Path("backups")
        self.backup_dir.mkdir(exist_ok=True)
        
    def _load_config(self) -> dict:
        """加载数据库配置"""
        default_config = {
            "sqlite": {
                "path": "data/training_memory.db",
                "backup_enabled": True,
                "backup_interval_hours": 24
            },
            "redis": {
                "host": "localhost",
                "port": 6379,
                "db": 0,
                "password": None,
                "enabled": True
            },
            "postgresql": {
                "host": "localhost",
                "port": 5432,
                "database": "training_memory",
                "user": "postgres",
                "password": "password",
                "enabled": False
            }
        }
        
        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                    elif isinstance(value, dict):
                        for subkey, subvalue in value.items():
                            if subkey not in config[key]:
                                config[key][subkey] = subvalue
                return config
            except Exception as e:
                print(f"⚠️ 配置文件加载失败，使用默认配置: {e}")
        
        # 保存默认配置
        os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
        with open(self.config_path, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
        
        return default_config
    
    async def initialize_all(self):
        """初始化所有数据库"""
        print("🚀 开始初始化训练记忆数据库系统...")
        
        # 创建数据目录
        data_dir = Path("data")
        data_dir.mkdir(exist_ok=True)
        
        # 初始化SQLite
        await self._initialize_sqlite()
        
        # 初始化Redis
        if self.config["redis"]["enabled"]:
            await self._initialize_redis()
        
        # 初始化PostgreSQL
        if self.config["postgresql"]["enabled"]:
            await self._initialize_postgresql()
        
        # 测试连接
        await self._test_connections()
        
        # 创建备份任务
        await self._setup_backup_tasks()
        
        print("✅ 数据库系统初始化完成！")
    
    async def _initialize_sqlite(self):
        """初始化SQLite数据库"""
        print("📊 初始化SQLite数据库...")
        
        db_path = self.config["sqlite"]["path"]
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # 如果数据库已存在，创建备份
        if os.path.exists(db_path):
            await self._backup_sqlite(db_path)
        
        try:
            connection = sqlite3.connect(db_path)
            cursor = connection.cursor()
            
            # 创建训练记录表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS training_records (
                    record_id TEXT PRIMARY KEY,
                    model_id TEXT NOT NULL,
                    model_version TEXT DEFAULT '1.0',
                    status TEXT NOT NULL,
                    progress REAL DEFAULT 0.0,
                    created_at TEXT NOT NULL,
                    started_at TEXT,
                    completed_at TEXT,
                    hyperparameters TEXT,
                    data_config TEXT,
                    metrics TEXT,
                    model_path TEXT,
                    tags TEXT,
                    notes TEXT,
                    experiment_name TEXT,
                    error_message TEXT,
                    error_traceback TEXT,
                    signature TEXT,
                    created_timestamp INTEGER DEFAULT (strftime('%s', 'now'))
                )
            """)
            
            # 创建索引
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_model_id ON training_records(model_id)",
                "CREATE INDEX IF NOT EXISTS idx_status ON training_records(status)",
                "CREATE INDEX IF NOT EXISTS idx_created_at ON training_records(created_at)",
                "CREATE INDEX IF NOT EXISTS idx_signature ON training_records(signature)",
                "CREATE INDEX IF NOT EXISTS idx_experiment ON training_records(experiment_name)",
                "CREATE INDEX IF NOT EXISTS idx_accuracy ON training_records(json_extract(metrics, '$.accuracy'))",
                "CREATE INDEX IF NOT EXISTS idx_timestamp ON training_records(created_timestamp)"
            ]
            
            for index_sql in indexes:
                cursor.execute(index_sql)
            
            # 创建知识表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS knowledge (
                    knowledge_id TEXT PRIMARY KEY,
                    knowledge_type TEXT NOT NULL,
                    source_record_id TEXT,
                    model_id TEXT,
                    title TEXT NOT NULL,
                    content TEXT,
                    confidence REAL DEFAULT 0.0,
                    applicable_models TEXT,
                    applicable_scenarios TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    tags TEXT,
                    validation_count INTEGER DEFAULT 0,
                    success_count INTEGER DEFAULT 0,
                    effectiveness_score REAL DEFAULT 0.0,
                    FOREIGN KEY (source_record_id) REFERENCES training_records(record_id)
                )
            """)
            
            # 知识表索引
            knowledge_indexes = [
                "CREATE INDEX IF NOT EXISTS idx_knowledge_type ON knowledge(knowledge_type)",
                "CREATE INDEX IF NOT EXISTS idx_knowledge_model ON knowledge(model_id)",
                "CREATE INDEX IF NOT EXISTS idx_confidence ON knowledge(confidence)",
                "CREATE INDEX IF NOT EXISTS idx_effectiveness ON knowledge(effectiveness_score)",
                "CREATE INDEX IF NOT EXISTS idx_knowledge_created ON knowledge(created_at)",
                "CREATE INDEX IF NOT EXISTS idx_source_record ON knowledge(source_record_id)"
            ]
            
            for index_sql in knowledge_indexes:
                cursor.execute(index_sql)
            
            # 创建统计视图
            cursor.execute("""
                CREATE VIEW IF NOT EXISTS training_statistics AS
                SELECT 
                    model_id,
                    COUNT(*) as total_records,
                    AVG(json_extract(metrics, '$.accuracy')) as avg_accuracy,
                    MAX(json_extract(metrics, '$.accuracy')) as max_accuracy,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
                    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
                    AVG(json_extract(metrics, '$.training_time')) as avg_training_time
                FROM training_records
                GROUP BY model_id
            """)
            
            # 创建知识统计视图
            cursor.execute("""
                CREATE VIEW IF NOT EXISTS knowledge_statistics AS
                SELECT 
                    knowledge_type,
                    model_id,
                    COUNT(*) as knowledge_count,
                    AVG(confidence) as avg_confidence,
                    AVG(effectiveness_score) as avg_effectiveness,
                    SUM(validation_count) as total_validations,
                    SUM(success_count) as total_successes
                FROM knowledge
                GROUP BY knowledge_type, model_id
            """)
            
            connection.commit()
            connection.close()
            
            print(f"✅ SQLite数据库初始化完成: {db_path}")
            
        except Exception as e:
            print(f"❌ SQLite初始化失败: {e}")
            raise
    
    async def _initialize_redis(self):
        """初始化Redis配置"""
        print("🔴 初始化Redis配置...")
        
        if not REDIS_AVAILABLE:
            print("⚠️ Redis不可用，跳过Redis初始化")
            return
        
        try:
            redis_config = self.config["redis"]
            client = redis.Redis(
                host=redis_config["host"],
                port=redis_config["port"],
                db=redis_config["db"],
                password=redis_config.get("password"),
                decode_responses=False
            )
            
            # 测试连接
            client.ping()
            
            # 设置Redis配置
            client.config_set("maxmemory-policy", "allkeys-lru")
            client.config_set("save", "900 1 300 10 60 10000")  # 持久化配置
            
            # 创建命名空间
            namespaces = ["training_record:", "knowledge:", "cache:", "session:"]
            for namespace in namespaces:
                client.set(f"namespace:{namespace}initialized", "true", ex=3600)
            
            print(f"✅ Redis初始化完成: {redis_config['host']}:{redis_config['port']}")
            
        except Exception as e:
            print(f"❌ Redis初始化失败: {e}")
            print("💡 提示: 请确保Redis服务正在运行")
    
    async def _initialize_postgresql(self):
        """初始化PostgreSQL数据库"""
        print("🐘 初始化PostgreSQL数据库...")
        
        if not POSTGRESQL_AVAILABLE:
            print("⚠️ PostgreSQL不可用，跳过PostgreSQL初始化")
            return
        
        try:
            pg_config = self.config["postgresql"]
            
            # 连接到PostgreSQL
            connection = psycopg2.connect(
                host=pg_config["host"],
                port=pg_config["port"],
                database=pg_config["database"],
                user=pg_config["user"],
                password=pg_config["password"]
            )
            
            cursor = connection.cursor()
            
            # 创建扩展
            cursor.execute("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"")
            cursor.execute("CREATE EXTENSION IF NOT EXISTS \"pg_trgm\"")
            
            # 创建训练记录表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS training_records_pg (
                    record_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                    model_id VARCHAR(100) NOT NULL,
                    model_version VARCHAR(20) DEFAULT '1.0',
                    status VARCHAR(20) NOT NULL,
                    progress DECIMAL(5,4) DEFAULT 0.0,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    started_at TIMESTAMP WITH TIME ZONE,
                    completed_at TIMESTAMP WITH TIME ZONE,
                    hyperparameters JSONB,
                    data_config JSONB,
                    metrics JSONB,
                    model_path TEXT,
                    tags TEXT[],
                    notes TEXT,
                    experiment_name VARCHAR(200),
                    error_message TEXT,
                    error_traceback TEXT,
                    signature VARCHAR(64)
                )
            """)
            
            # 创建PostgreSQL索引
            pg_indexes = [
                "CREATE INDEX IF NOT EXISTS idx_pg_model_id ON training_records_pg(model_id)",
                "CREATE INDEX IF NOT EXISTS idx_pg_status ON training_records_pg(status)",
                "CREATE INDEX IF NOT EXISTS idx_pg_created_at ON training_records_pg(created_at)",
                "CREATE INDEX IF NOT EXISTS idx_pg_signature ON training_records_pg(signature)",
                "CREATE INDEX IF NOT EXISTS idx_pg_metrics_gin ON training_records_pg USING GIN(metrics)",
                "CREATE INDEX IF NOT EXISTS idx_pg_hyperparams_gin ON training_records_pg USING GIN(hyperparameters)",
                "CREATE INDEX IF NOT EXISTS idx_pg_tags_gin ON training_records_pg USING GIN(tags)"
            ]
            
            for index_sql in pg_indexes:
                cursor.execute(index_sql)
            
            # 创建知识表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS knowledge_pg (
                    knowledge_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                    knowledge_type VARCHAR(50) NOT NULL,
                    source_record_id UUID REFERENCES training_records_pg(record_id),
                    model_id VARCHAR(100),
                    title VARCHAR(500) NOT NULL,
                    content JSONB,
                    confidence DECIMAL(5,4) DEFAULT 0.0,
                    applicable_models TEXT[],
                    applicable_scenarios TEXT[],
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    tags TEXT[],
                    validation_count INTEGER DEFAULT 0,
                    success_count INTEGER DEFAULT 0,
                    effectiveness_score DECIMAL(5,4) DEFAULT 0.0
                )
            """)
            
            connection.commit()
            connection.close()
            
            print(f"✅ PostgreSQL初始化完成: {pg_config['host']}:{pg_config['port']}")
            
        except Exception as e:
            print(f"❌ PostgreSQL初始化失败: {e}")
            print("💡 提示: 请确保PostgreSQL服务正在运行且数据库已创建")
    
    async def _test_connections(self):
        """测试数据库连接"""
        print("🔍 测试数据库连接...")
        
        try:
            memory_db = HierarchicalTrainingMemoryDB()
            await memory_db.initialize()
            
            # 获取统计信息
            stats = await memory_db.get_statistics()
            
            print("📊 连接测试结果:")
            for layer_name, layer_stats in stats["layers"].items():
                status = "✅" if layer_stats["status"] == "active" else "❌"
                print(f"  {status} {layer_name}: {layer_stats['status']}")
            
        except Exception as e:
            print(f"❌ 连接测试失败: {e}")
    
    async def _setup_backup_tasks(self):
        """设置备份任务"""
        print("💾 设置备份任务...")
        
        # 创建备份脚本
        backup_script = """#!/usr/bin/env python3
import os
import sys
import asyncio
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent))
from scripts.init_memory_database import DatabaseInitializer

async def main():
    initializer = DatabaseInitializer()
    await initializer.backup_databases()

if __name__ == "__main__":
    asyncio.run(main())
"""
        
        backup_script_path = Path("scripts/backup_databases.py")
        with open(backup_script_path, 'w', encoding='utf-8') as f:
            f.write(backup_script)
        
        # 使脚本可执行
        os.chmod(backup_script_path, 0o755)
        
        print(f"✅ 备份脚本已创建: {backup_script_path}")
        print("💡 提示: 可以使用cron定时执行备份脚本")
    
    async def _backup_sqlite(self, db_path: str):
        """备份SQLite数据库"""
        if not os.path.exists(db_path):
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = self.backup_dir / f"training_memory_{timestamp}.db"
        
        try:
            shutil.copy2(db_path, backup_path)
            print(f"📦 SQLite备份完成: {backup_path}")
        except Exception as e:
            print(f"❌ SQLite备份失败: {e}")
    
    async def backup_databases(self):
        """备份所有数据库"""
        print("💾 开始备份数据库...")
        
        # 备份SQLite
        sqlite_path = self.config["sqlite"]["path"]
        if os.path.exists(sqlite_path):
            await self._backup_sqlite(sqlite_path)
        
        # 清理旧备份（保留最近7天）
        await self._cleanup_old_backups(days=7)
        
        print("✅ 数据库备份完成！")
    
    async def _cleanup_old_backups(self, days: int = 7):
        """清理旧备份文件"""
        cutoff_time = datetime.now().timestamp() - (days * 24 * 3600)
        
        for backup_file in self.backup_dir.glob("*.db"):
            if backup_file.stat().st_mtime < cutoff_time:
                try:
                    backup_file.unlink()
                    print(f"🗑️ 已删除旧备份: {backup_file.name}")
                except Exception as e:
                    print(f"❌ 删除备份失败: {e}")
    
    async def reset_databases(self):
        """重置数据库（危险操作）"""
        print("⚠️ 警告: 即将重置所有数据库！")
        confirm = input("请输入 'RESET' 确认操作: ")
        
        if confirm != "RESET":
            print("❌ 操作已取消")
            return
        
        # 备份现有数据
        await self.backup_databases()
        
        # 删除SQLite数据库
        sqlite_path = self.config["sqlite"]["path"]
        if os.path.exists(sqlite_path):
            os.remove(sqlite_path)
            print(f"🗑️ 已删除SQLite数据库: {sqlite_path}")
        
        # 清空Redis
        if self.config["redis"]["enabled"] and REDIS_AVAILABLE:
            try:
                redis_config = self.config["redis"]
                client = redis.Redis(
                    host=redis_config["host"],
                    port=redis_config["port"],
                    db=redis_config["db"],
                    password=redis_config.get("password")
                )
                client.flushdb()
                print("🗑️ 已清空Redis数据库")
            except Exception as e:
                print(f"❌ Redis清空失败: {e}")
        
        # 重新初始化
        await self.initialize_all()
        
        print("✅ 数据库重置完成！")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="训练记忆数据库初始化工具")
    parser.add_argument("--init", action="store_true", help="初始化数据库")
    parser.add_argument("--backup", action="store_true", help="备份数据库")
    parser.add_argument("--reset", action="store_true", help="重置数据库（危险操作）")
    parser.add_argument("--config", default="config/database_config.json", help="配置文件路径")
    
    args = parser.parse_args()
    
    initializer = DatabaseInitializer(args.config)
    
    if args.init:
        await initializer.initialize_all()
    elif args.backup:
        await initializer.backup_databases()
    elif args.reset:
        await initializer.reset_databases()
    else:
        print("请指定操作: --init, --backup, 或 --reset")
        parser.print_help()


if __name__ == "__main__":
    asyncio.run(main())
