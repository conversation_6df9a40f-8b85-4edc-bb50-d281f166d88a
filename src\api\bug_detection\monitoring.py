"""
Bug检测系统监控API端点
创建日期: 2025年7月24日
用途: 提供Bug检测和监控相关的API接口
"""

import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

# 导入Bug检测组件
from src.bug_detection.core.database_manager import DatabaseManager
from src.bug_detection.feedback.bug_reporter import IntelligentBugReporter

# from src.bug_detection.monitoring.api_monitor import monitoring_manager  # 暂时禁用

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/v1/bug-detection", tags=["bug-detection"])

# 数据模型
class JSErrorData(BaseModel):
    """JavaScript错误数据模型"""
    type: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    source: Optional[str] = Field(None, description="错误源文件")
    line: Optional[int] = Field(None, description="行号")
    column: Optional[int] = Field(None, description="列号")
    stack: Optional[str] = Field(None, description="堆栈跟踪")
    sessionId: str = Field(..., description="会话ID")
    pageName: str = Field(..., description="页面名称")
    url: str = Field(..., description="页面URL")
    userAgent: str = Field(..., description="用户代理")
    timestamp: str = Field(..., description="时间戳")

class BugReportRequest(BaseModel):
    """Bug报告请求模型"""
    error_data: Dict[str, Any] = Field(..., description="错误数据")
    context: Optional[Dict[str, Any]] = Field(None, description="上下文信息")
    user_actions: Optional[List[str]] = Field(None, description="用户操作序列")

class MonitoringStatusResponse(BaseModel):
    """监控状态响应模型"""
    status: str = Field(..., description="监控状态")
    timestamp: str = Field(..., description="时间戳")
    active_monitors: int = Field(..., description="活跃监控器数量")
    total_requests: int = Field(..., description="总请求数")
    error_count: int = Field(..., description="错误数量")
    error_rate: float = Field(..., description="错误率")

# 依赖注入
def get_database_manager():
    """获取数据库管理器"""
    return DatabaseManager()

def get_bug_reporter():
    """获取Bug报告生成器"""
    return IntelligentBugReporter(get_database_manager())

# API端点
@router.post("/js-error")
async def report_js_error(error_data: JSErrorData, db: DatabaseManager = Depends(get_database_manager)):
    """
    接收JavaScript错误报告
    """
    try:
        # 转换为字典格式
        error_dict = error_data.dict()
        
        # 保存JavaScript错误
        db.save_js_error(
            session_id=error_data.sessionId,
            error_message=error_data.message,
            page_url=error_data.url
        )
        
        # 生成增强Bug报告
        bug_reporter = get_bug_reporter()
        bug_report = bug_reporter.generate_enhanced_report(error_dict)
        
        logger.info(f"JavaScript error reported: {error_data.type} on {error_data.pageName}")
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "message": "Error reported successfully",
                "bug_id": bug_report.get('id'),
                "timestamp": datetime.now().isoformat()
            }
        )
        
    except Exception as e:
        logger.error(f"Error processing JS error report: {e}")
        raise HTTPException(status_code=500, detail="Failed to process error report")

@router.get("/monitoring-status", response_model=MonitoringStatusResponse)
async def get_monitoring_status():
    """
    获取监控系统状态
    """
    try:
        # 暂时返回模拟数据，因为monitoring_manager被禁用
        summaries = {}

        # 计算总体统计
        total_requests = 0  # sum(summary.get('total_requests', 0) for summary in summaries.values())
        total_errors = sum(summary.get('error_count', 0) for summary in summaries.values())
        error_rate = total_errors / total_requests if total_requests > 0 else 0
        
        return MonitoringStatusResponse(
            status="active",
            timestamp=datetime.now().isoformat(),
            active_monitors=len(summaries),
            total_requests=total_requests,
            error_count=total_errors,
            error_rate=error_rate
        )
        
    except Exception as e:
        logger.error(f"Error getting monitoring status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get monitoring status")

@router.get("/performance-metrics")
async def get_performance_metrics(
    time_range: str = "1h",
    endpoint: Optional[str] = None,
    db: DatabaseManager = Depends(get_database_manager)
):
    """
    获取性能指标
    
    Args:
        time_range: 时间范围 (1h, 24h, 7d)
        endpoint: 特定端点过滤
    """
    try:
        # 获取性能摘要
        performance_summary = db.get_performance_summary()
        
        # 过滤特定端点
        if endpoint:
            performance_summary = {
                k: v for k, v in performance_summary.items() 
                if endpoint in k
            }
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "time_range": time_range,
                "endpoint_filter": endpoint,
                "metrics": performance_summary,
                "timestamp": datetime.now().isoformat()
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get performance metrics")

@router.post("/bug-report")
async def create_bug_report(
    request: BugReportRequest,
    bug_reporter: IntelligentBugReporter = Depends(get_bug_reporter)
):
    """
    创建详细的Bug报告
    """
    try:
        # 生成增强的Bug报告
        bug_report = bug_reporter.generate_enhanced_report(
            error_data=request.error_data,
            context=request.context,
            user_actions=request.user_actions
        )
        
        return JSONResponse(
            status_code=201,
            content={
                "status": "success",
                "message": "Bug report created successfully",
                "bug_report": bug_report
            }
        )
        
    except Exception as e:
        logger.error(f"Error creating bug report: {e}")
        raise HTTPException(status_code=500, detail="Failed to create bug report")

@router.get("/bug-reports")
async def get_bug_reports(
    limit: int = 20,
    status: Optional[str] = None,
    severity: Optional[str] = None,
    db: DatabaseManager = Depends(get_database_manager)
):
    """
    获取Bug报告列表
    
    Args:
        limit: 返回数量限制
        status: 状态过滤 (open, in_progress, resolved, closed)
        severity: 严重程度过滤 (low, medium, high, critical)
    """
    try:
        # 获取Bug报告
        bug_reports = db.get_bug_reports(limit=limit)
        
        # 应用过滤器
        if status:
            bug_reports = [bug for bug in bug_reports if bug.get('status') == status]
        
        if severity:
            bug_reports = [bug for bug in bug_reports if bug.get('severity') == severity]
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "count": len(bug_reports),
                "bug_reports": bug_reports,
                "timestamp": datetime.now().isoformat()
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting bug reports: {e}")
        raise HTTPException(status_code=500, detail="Failed to get bug reports")

@router.get("/health")
async def health_check():
    """
    健康检查端点
    """
    try:
        # 检查数据库连接
        db = get_database_manager()
        
        # 简单的数据库查询测试
        bug_reports = db.get_bug_reports(limit=1)
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "database": "connected",
                "version": "1.0.0"
            }
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
        )

@router.get("/statistics")
async def get_statistics(db: DatabaseManager = Depends(get_database_manager)):
    """
    获取Bug检测系统统计信息
    """
    try:
        # 获取Bug报告统计
        bug_reports = db.get_bug_reports(limit=1000)  # 获取更多数据用于统计
        
        # 统计分析
        total_bugs = len(bug_reports)
        severity_stats = {}
        status_stats = {}
        
        for bug in bug_reports:
            severity = bug.get('severity', 'unknown')
            status = bug.get('status', 'unknown')
            
            severity_stats[severity] = severity_stats.get(severity, 0) + 1
            status_stats[status] = status_stats.get(status, 0) + 1
        
        # 获取性能统计
        performance_summary = db.get_performance_summary()
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "statistics": {
                    "bugs": {
                        "total": total_bugs,
                        "by_severity": severity_stats,
                        "by_status": status_stats
                    },
                    "performance": {
                        "endpoints_monitored": len(performance_summary),
                        "summary": performance_summary
                    }
                },
                "timestamp": datetime.now().isoformat()
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting statistics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get statistics")
