#!/usr/bin/env python3
"""
性能基准测试
Performance Benchmark Tests

测试系统各组件的性能指标
"""

import pytest
import time
import asyncio
import statistics
from datetime import datetime
import tempfile
import os
import psutil
import gc

# 导入系统组件
import sys
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from src.core.unified_prediction_storage import UnifiedPredictionStorage, PredictionRecord
from src.core.draw_trigger_system import DrawTriggerSystem
from src.analysis.prediction_deviation_analyzer import PredictionDeviationAnalyzer
from src.analysis.model_weakness_identifier import ModelWeaknessIdentifier
from src.analysis.success_factor_extractor import SuccessFactorExtractor


class TestPerformanceBenchmarks:
    """性能基准测试类"""
    
    @pytest.fixture
    def temp_db_path(self):
        """创建临时数据库路径"""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            temp_path = f.name
        yield temp_path
        if os.path.exists(temp_path):
            os.unlink(temp_path)
    
    @pytest.fixture
    def storage_system(self, temp_db_path):
        """创建存储系统"""
        return UnifiedPredictionStorage(temp_db_path)
    
    @pytest.fixture
    def large_dataset(self):
        """创建大型数据集"""
        predictions = []
        for i in range(1000):
            prediction = PredictionRecord(
                period_number=f"2025{i:03d}",
                model_name=f"model_{i%10}",
                predicted_numbers=f"{i%10}{(i+1)%10}{(i+2)%10}",
                confidence=0.3 + (i % 7) * 0.1,
                prediction_time=datetime.now(),
                actual_numbers=f"{(i+1)%10}{(i+2)%10}{(i+3)%10}",
                is_verified=True,
                accuracy_score=0.2 + (i % 5) * 0.2,
                metadata={'batch': i // 100, 'test_data': True}
            )
            predictions.append(prediction)
        return predictions
    
    def test_storage_insertion_performance(self, storage_system, large_dataset):
        """测试存储插入性能"""
        print("\n=== 存储插入性能测试 ===")
        
        # 单条插入性能测试
        single_insert_times = []
        for i in range(100):
            prediction = large_dataset[i]
            
            start_time = time.perf_counter()
            record_id = storage_system.save_prediction_record(prediction)
            end_time = time.perf_counter()
            
            insert_time = (end_time - start_time) * 1000  # 转换为毫秒
            single_insert_times.append(insert_time)
            
            assert record_id > 0
        
        # 计算统计信息
        avg_insert_time = statistics.mean(single_insert_times)
        max_insert_time = max(single_insert_times)
        min_insert_time = min(single_insert_times)
        
        print(f"单条插入平均时间: {avg_insert_time:.2f}ms")
        print(f"单条插入最大时间: {max_insert_time:.2f}ms")
        print(f"单条插入最小时间: {min_insert_time:.2f}ms")
        
        # 性能要求：平均插入时间应小于10ms
        assert avg_insert_time < 10.0, f"插入性能不达标: {avg_insert_time:.2f}ms > 10ms"
        
        # 批量插入性能测试
        batch_predictions = large_dataset[100:500]
        
        start_time = time.perf_counter()
        for prediction in batch_predictions:
            storage_system.save_prediction_record(prediction)
        end_time = time.perf_counter()
        
        batch_time = (end_time - start_time) * 1000
        batch_avg_time = batch_time / len(batch_predictions)
        
        print(f"批量插入总时间: {batch_time:.2f}ms")
        print(f"批量插入平均时间: {batch_avg_time:.2f}ms")
        
        # 批量插入应该更高效
        assert batch_avg_time < avg_insert_time * 1.2, "批量插入性能未优化"
    
    def test_storage_query_performance(self, storage_system, large_dataset):
        """测试存储查询性能"""
        print("\n=== 存储查询性能测试 ===")
        
        # 先插入测试数据
        for prediction in large_dataset[:500]:
            storage_system.save_prediction_record(prediction)
        
        # 期号查询性能测试
        query_times = []
        for i in range(50):
            period_number = f"2025{i:03d}"
            
            start_time = time.perf_counter()
            predictions = storage_system.get_period_predictions(period_number)
            end_time = time.perf_counter()
            
            query_time = (end_time - start_time) * 1000
            query_times.append(query_time)
        
        avg_query_time = statistics.mean(query_times)
        max_query_time = max(query_times)
        
        print(f"期号查询平均时间: {avg_query_time:.2f}ms")
        print(f"期号查询最大时间: {max_query_time:.2f}ms")
        
        # 性能要求：查询时间应小于5ms
        assert avg_query_time < 5.0, f"查询性能不达标: {avg_query_time:.2f}ms > 5ms"
        
        # 模型查询性能测试
        model_query_times = []
        for i in range(10):
            model_name = f"model_{i}"
            
            start_time = time.perf_counter()
            predictions = storage_system.get_model_predictions(model_name, 100)
            end_time = time.perf_counter()
            
            query_time = (end_time - start_time) * 1000
            model_query_times.append(query_time)
        
        avg_model_query_time = statistics.mean(model_query_times)
        print(f"模型查询平均时间: {avg_model_query_time:.2f}ms")
        
        # 统计查询性能测试
        stats_times = []
        for _ in range(20):
            start_time = time.perf_counter()
            stats = storage_system.get_statistics()
            end_time = time.perf_counter()
            
            stats_time = (end_time - start_time) * 1000
            stats_times.append(stats_time)
        
        avg_stats_time = statistics.mean(stats_times)
        print(f"统计查询平均时间: {avg_stats_time:.2f}ms")
        
        # 统计查询应该在20ms内完成
        assert avg_stats_time < 20.0, f"统计查询性能不达标: {avg_stats_time:.2f}ms > 20ms"
    
    @pytest.mark.asyncio
    async def test_trigger_system_performance(self, storage_system, large_dataset):
        """测试触发系统性能"""
        print("\n=== 触发系统性能测试 ===")
        
        # 插入测试数据
        for prediction in large_dataset[:100]:
            storage_system.save_prediction_record(prediction)
        
        trigger_system = DrawTriggerSystem(storage_system)
        
        # 注册测试分析器
        async def fast_analyzer(predictions, actual_numbers):
            await asyncio.sleep(0.001)  # 1ms处理时间
            return {'fast_analyzer': len(predictions)}
        
        async def slow_analyzer(predictions, actual_numbers):
            await asyncio.sleep(0.01)   # 10ms处理时间
            return {'slow_analyzer': len(predictions)}
        
        trigger_system.register_analyzer('fast_analyzer', fast_analyzer)
        trigger_system.register_analyzer('slow_analyzer', slow_analyzer)
        
        # 单次触发性能测试
        trigger_times = []
        for i in range(20):
            period_number = f"2025{i:03d}"
            actual_numbers = f"{i%10}{(i+1)%10}{(i+2)%10}"
            
            start_time = time.perf_counter()
            result = await trigger_system.on_draw_announced(period_number, actual_numbers)
            end_time = time.perf_counter()
            
            trigger_time = (end_time - start_time) * 1000
            trigger_times.append(trigger_time)
            
            assert result.period_number == period_number
        
        avg_trigger_time = statistics.mean(trigger_times)
        max_trigger_time = max(trigger_times)
        
        print(f"触发分析平均时间: {avg_trigger_time:.2f}ms")
        print(f"触发分析最大时间: {max_trigger_time:.2f}ms")
        
        # 性能要求：触发分析应在100ms内完成
        assert avg_trigger_time < 100.0, f"触发性能不达标: {avg_trigger_time:.2f}ms > 100ms"
        
        # 并发触发性能测试
        concurrent_tasks = []
        start_time = time.perf_counter()
        
        for i in range(10):
            task = asyncio.create_task(
                trigger_system.on_draw_announced(f"2025{i+50:03d}", f"{i}{i+1}{i+2}")
            )
            concurrent_tasks.append(task)
        
        results = await asyncio.gather(*concurrent_tasks)
        end_time = time.perf_counter()
        
        concurrent_time = (end_time - start_time) * 1000
        avg_concurrent_time = concurrent_time / len(concurrent_tasks)
        
        print(f"并发触发总时间: {concurrent_time:.2f}ms")
        print(f"并发触发平均时间: {avg_concurrent_time:.2f}ms")
        
        assert len(results) == 10
        # 并发处理应该比串行处理更高效
        assert concurrent_time < avg_trigger_time * len(concurrent_tasks) * 0.8
    
    def test_analysis_components_performance(self, large_dataset):
        """测试分析组件性能"""
        print("\n=== 分析组件性能测试 ===")
        
        # 准备测试数据
        test_predictions = large_dataset[:100]
        
        # 偏差分析器性能测试
        deviation_analyzer = PredictionDeviationAnalyzer()
        deviation_times = []
        
        for i in range(20):
            prediction = test_predictions[i]
            actual_result = f"{(i+1)%10}{(i+2)%10}{(i+3)%10}"
            
            start_time = time.perf_counter()
            result = deviation_analyzer.analyze_deviation(prediction, actual_result)
            end_time = time.perf_counter()
            
            analysis_time = (end_time - start_time) * 1000
            deviation_times.append(analysis_time)
            
            assert result.overall_score >= 0
        
        avg_deviation_time = statistics.mean(deviation_times)
        print(f"偏差分析平均时间: {avg_deviation_time:.2f}ms")
        
        # 性能要求：偏差分析应在50ms内完成
        assert avg_deviation_time < 50.0, f"偏差分析性能不达标: {avg_deviation_time:.2f}ms > 50ms"
        
        # 弱点识别器性能测试
        weakness_identifier = ModelWeaknessIdentifier()
        
        start_time = time.perf_counter()
        weakness_result = weakness_identifier.identify_weaknesses("test_model", test_predictions[:50])
        end_time = time.perf_counter()
        
        weakness_time = (end_time - start_time) * 1000
        print(f"弱点识别时间: {weakness_time:.2f}ms")
        
        # 性能要求：弱点识别应在200ms内完成
        assert weakness_time < 200.0, f"弱点识别性能不达标: {weakness_time:.2f}ms > 200ms"
        
        # 成功因子提取器性能测试
        success_extractor = SuccessFactorExtractor()
        
        start_time = time.perf_counter()
        success_result = success_extractor.extract_success_factors("test_model", test_predictions[:50])
        end_time = time.perf_counter()
        
        success_time = (end_time - start_time) * 1000
        print(f"成功因子提取时间: {success_time:.2f}ms")
        
        # 性能要求：成功因子提取应在300ms内完成
        assert success_time < 300.0, f"成功因子提取性能不达标: {success_time:.2f}ms > 300ms"
    
    def test_memory_usage_performance(self, storage_system, large_dataset):
        """测试内存使用性能"""
        print("\n=== 内存使用性能测试 ===")
        
        process = psutil.Process()
        
        # 获取初始内存使用
        gc.collect()  # 强制垃圾回收
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        print(f"初始内存使用: {initial_memory:.2f}MB")
        
        # 批量插入数据
        batch_size = 500
        for i in range(0, batch_size):
            prediction = large_dataset[i]
            storage_system.save_prediction_record(prediction)
            
            # 每100条记录检查一次内存
            if (i + 1) % 100 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024
                memory_increase = current_memory - initial_memory
                print(f"插入{i+1}条记录后内存使用: {current_memory:.2f}MB (+{memory_increase:.2f}MB)")
        
        # 执行查询操作
        for i in range(100):
            storage_system.get_period_predictions(f"2025{i:03d}")
            storage_system.get_statistics()
        
        # 获取最终内存使用
        gc.collect()
        final_memory = process.memory_info().rss / 1024 / 1024
        total_increase = final_memory - initial_memory
        
        print(f"最终内存使用: {final_memory:.2f}MB")
        print(f"总内存增长: {total_increase:.2f}MB")
        
        # 性能要求：处理500条记录的内存增长应小于50MB
        assert total_increase < 50.0, f"内存使用过多: {total_increase:.2f}MB > 50MB"
        
        # 内存增长率检查（每条记录<0.1MB）
        memory_per_record = total_increase / batch_size
        print(f"每条记录内存使用: {memory_per_record:.4f}MB")
        
        assert memory_per_record < 0.1, f"单记录内存使用过多: {memory_per_record:.4f}MB > 0.1MB"
    
    def test_concurrent_access_performance(self, storage_system, large_dataset):
        """测试并发访问性能"""
        print("\n=== 并发访问性能测试 ===")
        
        # 先插入一些数据
        for prediction in large_dataset[:200]:
            storage_system.save_prediction_record(prediction)
        
        import threading
        import queue
        
        # 并发读取测试
        read_times = queue.Queue()
        
        def concurrent_reader(thread_id):
            for i in range(20):
                start_time = time.perf_counter()
                predictions = storage_system.get_period_predictions(f"2025{(thread_id * 20 + i):03d}")
                end_time = time.perf_counter()
                
                read_time = (end_time - start_time) * 1000
                read_times.put(read_time)
        
        # 启动多个读取线程
        threads = []
        start_time = time.perf_counter()
        
        for i in range(5):
            thread = threading.Thread(target=concurrent_reader, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        end_time = time.perf_counter()
        total_concurrent_time = (end_time - start_time) * 1000
        
        # 收集所有读取时间
        all_read_times = []
        while not read_times.empty():
            all_read_times.append(read_times.get())
        
        avg_concurrent_read_time = statistics.mean(all_read_times)
        max_concurrent_read_time = max(all_read_times)
        
        print(f"并发读取总时间: {total_concurrent_time:.2f}ms")
        print(f"并发读取平均时间: {avg_concurrent_read_time:.2f}ms")
        print(f"并发读取最大时间: {max_concurrent_read_time:.2f}ms")
        print(f"总读取操作数: {len(all_read_times)}")
        
        # 性能要求：并发读取平均时间应小于10ms
        assert avg_concurrent_read_time < 10.0, f"并发读取性能不达标: {avg_concurrent_read_time:.2f}ms > 10ms"
    
    def test_scalability_performance(self, temp_db_path):
        """测试可扩展性性能"""
        print("\n=== 可扩展性性能测试 ===")
        
        # 测试不同数据量下的性能
        data_sizes = [100, 500, 1000, 2000]
        performance_results = {}
        
        for size in data_sizes:
            print(f"\n测试数据量: {size}")
            
            # 创建新的存储实例
            storage = UnifiedPredictionStorage(f"{temp_db_path}_{size}")
            
            # 生成测试数据
            test_data = []
            for i in range(size):
                prediction = PredictionRecord(
                    period_number=f"2025{i:04d}",
                    model_name=f"model_{i%10}",
                    predicted_numbers=f"{i%10}{(i+1)%10}{(i+2)%10}",
                    confidence=0.5,
                    prediction_time=datetime.now()
                )
                test_data.append(prediction)
            
            # 测试插入性能
            start_time = time.perf_counter()
            for prediction in test_data:
                storage.save_prediction_record(prediction)
            insert_time = (time.perf_counter() - start_time) * 1000
            
            # 测试查询性能
            start_time = time.perf_counter()
            for i in range(min(50, size)):
                storage.get_period_predictions(f"2025{i:04d}")
            query_time = (time.perf_counter() - start_time) * 1000
            
            # 测试统计性能
            start_time = time.perf_counter()
            stats = storage.get_statistics()
            stats_time = (time.perf_counter() - start_time) * 1000
            
            performance_results[size] = {
                'insert_time': insert_time,
                'query_time': query_time,
                'stats_time': stats_time,
                'insert_per_record': insert_time / size,
                'query_per_operation': query_time / min(50, size)
            }
            
            print(f"  插入总时间: {insert_time:.2f}ms")
            print(f"  查询总时间: {query_time:.2f}ms")
            print(f"  统计时间: {stats_time:.2f}ms")
            print(f"  每条记录插入时间: {insert_time/size:.4f}ms")
            
            # 清理
            os.unlink(f"{temp_db_path}_{size}")
        
        # 分析可扩展性
        print(f"\n=== 可扩展性分析 ===")
        for size in data_sizes:
            result = performance_results[size]
            print(f"数据量 {size}: 每记录插入 {result['insert_per_record']:.4f}ms, "
                  f"每次查询 {result['query_per_operation']:.4f}ms")
        
        # 验证线性可扩展性（性能不应该随数据量急剧下降）
        small_size_perf = performance_results[data_sizes[0]]['insert_per_record']
        large_size_perf = performance_results[data_sizes[-1]]['insert_per_record']
        
        # 大数据量下的性能不应该比小数据量差太多（不超过3倍）
        performance_ratio = large_size_perf / small_size_perf
        print(f"性能比率 (大/小): {performance_ratio:.2f}")
        
        assert performance_ratio < 3.0, f"可扩展性不佳: 性能比率 {performance_ratio:.2f} > 3.0"


if __name__ == "__main__":
    # 运行性能基准测试
    pytest.main([__file__, "-v", "-s", "--tb=short"])
