"""
模型性能监控系统

记录和分析模型的预测准确率、置信度、响应时间等指标
"""

import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from collections import defaultdict
import threading

from .database_manager import DatabaseManager
from .model_registry import ModelRegistry


class PerformanceTracker:
    """性能监控器"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.registry = ModelRegistry()
        self._performance_cache = {}
        self._cache_lock = threading.RLock()
        self._cache_ttl = 300  # 缓存5分钟
    
    def update_prediction_result(self, prediction_id: int, actual_result: str) -> bool:
        """更新预测结果"""
        try:
            # 获取预测记录
            predictions = self.db_manager.get_model_predictions("", 1000)  # 获取所有预测
            target_prediction = None
            
            for pred in predictions:
                if pred.get('id') == prediction_id:
                    target_prediction = pred
                    break
            
            if not target_prediction:
                return False
            
            # 计算是否正确
            is_correct = self._check_prediction_accuracy(target_prediction, actual_result)
            
            # 更新数据库
            self.db_manager.update_prediction_result(prediction_id, actual_result, is_correct)
            
            # 更新模型性能统计
            self._update_model_performance(target_prediction['model_id'])
            
            return True
            
        except Exception as e:
            print(f"更新预测结果失败: {e}")
            return False
    
    def _check_prediction_accuracy(self, prediction: Dict[str, Any], actual_result: str) -> bool:
        """检查预测准确性"""
        try:
            if len(actual_result) != 3:
                return False
            
            actual_digits = [int(d) for d in actual_result]
            prediction_result = prediction.get('prediction_result', {})
            
            # 获取预测的最高概率数字
            predicted_digits = []
            for pos in ['百位', '十位', '个位']:
                if pos in prediction_result and prediction_result[pos]:
                    best_digit = max(prediction_result[pos].items(), key=lambda x: x[1])[0]
                    predicted_digits.append(int(best_digit))
                else:
                    predicted_digits.append(0)
            
            # 检查直选是否正确
            return predicted_digits == actual_digits
            
        except Exception:
            return False
    
    def _update_model_performance(self, model_id: str):
        """更新模型性能统计"""
        try:
            # 计算准确率
            accuracy_data = self.db_manager.calculate_model_accuracy(model_id)
            
            # 获取最近的预测记录
            recent_predictions = self.db_manager.get_model_predictions(model_id, 100)
            
            # 计算平均置信度
            confidences = [p.get('confidence_score', 0.0) for p in recent_predictions if p.get('confidence_score') is not None]
            avg_confidence = np.mean(confidences) if confidences else 0.0
            
            # 计算性能趋势
            performance_trend = self._calculate_performance_trend(recent_predictions)
            
            # 更新性能数据
            performance_data = {
                **accuracy_data,
                'average_confidence': avg_confidence,
                'performance_trend': performance_trend,
                'detailed_metrics': {
                    'recent_predictions': len(recent_predictions),
                    'confidence_std': np.std(confidences) if confidences else 0.0,
                    'trend_direction': 'up' if performance_trend and performance_trend[-1] > performance_trend[0] else 'down'
                }
            }
            
            self.db_manager.update_model_performance(model_id, performance_data)
            
            # 清除缓存
            with self._cache_lock:
                if model_id in self._performance_cache:
                    del self._performance_cache[model_id]
            
        except Exception as e:
            print(f"更新模型性能失败: {e}")
    
    def _calculate_performance_trend(self, predictions: List[Dict[str, Any]]) -> List[float]:
        """计算性能趋势"""
        try:
            if len(predictions) < 10:
                return []
            
            # 按时间排序
            sorted_predictions = sorted(predictions, key=lambda x: x.get('prediction_time', ''))
            
            # 计算滑动窗口准确率
            window_size = 10
            trend = []
            
            for i in range(window_size, len(sorted_predictions)):
                window_predictions = sorted_predictions[i-window_size:i]
                correct_count = sum(1 for p in window_predictions if p.get('is_correct'))
                accuracy = correct_count / window_size
                trend.append(accuracy)
            
            return trend
            
        except Exception:
            return []
    
    def get_model_performance(self, model_id: str, use_cache: bool = True) -> Optional[Dict[str, Any]]:
        """获取模型性能统计"""
        # 检查缓存
        if use_cache:
            with self._cache_lock:
                if model_id in self._performance_cache:
                    cache_time, performance = self._performance_cache[model_id]
                    if (datetime.now() - cache_time).seconds < self._cache_ttl:
                        return performance
        
        # 从数据库获取
        performance = self.db_manager.get_model_performance(model_id)
        
        if performance:
            # 更新缓存
            with self._cache_lock:
                self._performance_cache[model_id] = (datetime.now(), performance)
        
        return performance
    
    def get_performance_ranking(self, metric: str = 'direct_accuracy', limit: int = 10) -> List[Dict[str, Any]]:
        """获取性能排行榜"""
        return self.db_manager.get_performance_ranking(metric, limit)
    
    def get_model_statistics(self, model_id: str, days: int = 30) -> Dict[str, Any]:
        """获取模型统计信息"""
        try:
            # 获取最近N天的预测记录
            cutoff_date = datetime.now() - timedelta(days=days)
            all_predictions = self.db_manager.get_model_predictions(model_id, 1000)
            
            recent_predictions = [
                p for p in all_predictions 
                if p.get('prediction_time') and 
                datetime.fromisoformat(p['prediction_time']) >= cutoff_date
            ]
            
            if not recent_predictions:
                return {
                    "model_id": model_id,
                    "period_days": days,
                    "total_predictions": 0,
                    "accuracy_stats": {},
                    "confidence_stats": {},
                    "prediction_frequency": 0.0
                }
            
            # 计算统计信息
            total_predictions = len(recent_predictions)
            correct_predictions = sum(1 for p in recent_predictions if p.get('is_correct'))
            accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0.0
            
            # 置信度统计
            confidences = [p.get('confidence_score', 0.0) for p in recent_predictions if p.get('confidence_score') is not None]
            
            # 预测频率（每天平均预测次数）
            prediction_frequency = total_predictions / days
            
            return {
                "model_id": model_id,
                "period_days": days,
                "total_predictions": total_predictions,
                "accuracy_stats": {
                    "accuracy": accuracy,
                    "correct_predictions": correct_predictions,
                    "total_predictions": total_predictions
                },
                "confidence_stats": {
                    "mean": np.mean(confidences) if confidences else 0.0,
                    "std": np.std(confidences) if confidences else 0.0,
                    "min": min(confidences) if confidences else 0.0,
                    "max": max(confidences) if confidences else 0.0
                },
                "prediction_frequency": prediction_frequency,
                "performance_trend": self._calculate_performance_trend(recent_predictions)
            }
            
        except Exception as e:
            print(f"获取模型统计失败: {e}")
            return {}
    
    def compare_models(self, model_ids: List[str], metric: str = 'direct_accuracy') -> Dict[str, Any]:
        """比较多个模型的性能"""
        try:
            comparison_data = {}
            
            for model_id in model_ids:
                performance = self.get_model_performance(model_id)
                if performance:
                    comparison_data[model_id] = {
                        "metric_value": performance.get(metric, 0.0),
                        "total_predictions": performance.get('total_predictions', 0),
                        "average_confidence": performance.get('average_confidence', 0.0),
                        "last_updated": performance.get('last_updated')
                    }
            
            # 排序
            sorted_models = sorted(
                comparison_data.items(), 
                key=lambda x: x[1]["metric_value"], 
                reverse=True
            )
            
            return {
                "metric": metric,
                "comparison_time": datetime.now().isoformat(),
                "model_count": len(comparison_data),
                "ranking": [
                    {
                        "rank": i + 1,
                        "model_id": model_id,
                        **data
                    }
                    for i, (model_id, data) in enumerate(sorted_models)
                ]
            }
            
        except Exception as e:
            print(f"模型比较失败: {e}")
            return {}
    
    def generate_performance_report(self, model_id: str) -> Dict[str, Any]:
        """生成性能报告"""
        try:
            # 获取模型信息
            model_info = self.registry.get_model_info(model_id)
            if not model_info:
                return {"error": f"模型 {model_id} 未找到"}
            
            # 获取性能数据
            performance = self.get_model_performance(model_id)
            statistics = self.get_model_statistics(model_id, 30)
            
            # 生成报告
            report = {
                "model_info": {
                    "model_id": model_info.model_id,
                    "name": model_info.name,
                    "type": model_info.model_type.value,
                    "version": model_info.version
                },
                "performance_summary": performance or {},
                "recent_statistics": statistics,
                "report_time": datetime.now().isoformat(),
                "recommendations": self._generate_recommendations(performance, statistics)
            }
            
            return report
            
        except Exception as e:
            return {"error": f"生成报告失败: {e}"}
    
    def _generate_recommendations(self, performance: Optional[Dict], statistics: Dict) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        try:
            if performance:
                accuracy = performance.get('direct_accuracy', 0.0)
                confidence = performance.get('average_confidence', 0.0)
                
                if accuracy < 0.1:
                    recommendations.append("模型准确率较低，建议重新训练或调整参数")
                
                if confidence < 0.3:
                    recommendations.append("模型置信度较低，建议增加训练数据或优化算法")
                
                if statistics.get('prediction_frequency', 0) < 0.5:
                    recommendations.append("预测频率较低，建议增加预测次数以获得更多性能数据")
            
            if not recommendations:
                recommendations.append("模型性能良好，继续保持当前配置")
            
        except Exception:
            recommendations.append("无法生成建议，请检查性能数据")
        
        return recommendations
    
    def clear_cache(self):
        """清除性能缓存"""
        with self._cache_lock:
            self._performance_cache.clear()
