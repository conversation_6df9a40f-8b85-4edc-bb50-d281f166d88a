import streamlit as st
import requests
import pandas as pd
from datetime import datetime, timedelta
import time

def show_data_update():
    """数据更新页面 - 连接真实数据源"""
    st.header("🔄 数据更新管理")
    
    # 真实数据源和API配置
    data_source_url = "https://data.17500.cn/3d_asc.txt"
    api_base_url = "http://127.0.0.1:8888"
    
    # 检查API服务状态
    try:
        health_response = requests.get(f"{api_base_url}/health", timeout=5)
        health_data = health_response.json()
        api_status = "✅ 正常" if health_response.status_code == 200 else "❌ 异常"
    except:
        api_status = "❌ 连接失败"
        health_data = {}
    
    # 检查数据源可用性
    try:
        source_response = requests.head(data_source_url, timeout=10)
        source_status = "✅ 可用" if source_response.status_code == 200 else "❌ 不可用"
        source_size = source_response.headers.get('content-length', 'N/A')
        if source_size != 'N/A':
            source_size = f"{int(source_size) / 1024:.1f} KB"
    except:
        source_status = "❌ 连接失败"
        source_size = "N/A"
    
    # 显示系统状态
    st.subheader("📊 系统状态")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("API服务", api_status)
    with col2:
        st.metric("数据源", source_status)
        if source_size != "N/A":
            st.caption(f"文件大小: {source_size}")
    with col3:
        if health_data and 'database' in health_data:
            total_records = health_data['database'].get('total_records', 0)
            st.metric("数据库记录", f"{total_records:,}")
    with col4:
        if health_data and 'database' in health_data:
            db_info = health_data['database']
            date_range = db_info.get('date_range', {})
            start_date = date_range.get('start', 'N/A')
            end_date = date_range.get('end', 'N/A')
            st.metric("数据范围", f"{start_date} 至 {end_date}")
    
    # 数据更新控制
    st.subheader("🔄 数据更新控制")
    
    col1, col2 = st.columns(2)
    
    with col1:
        # 手动更新功能
        st.write("**手动更新**")
        
        # 显示上次更新时间
        try:
            last_update_response = requests.get(f"{api_base_url}/api/v1/data/last-update", timeout=5)
            if last_update_response.status_code == 200:
                last_update_data = last_update_response.json()
                if last_update_data.get('success'):
                    last_update_time = last_update_data.get('last_update_time', 'N/A')
                    st.info(f"上次更新: {last_update_time}")
        except:
            pass
        
        if st.button("🔄 立即更新数据", type="primary"):
            with st.spinner("正在从数据源获取最新数据..."):
                try:
                    # 调用真实的数据更新API
                    update_response = requests.post(
                        f"{api_base_url}/api/v1/data/update",
                        timeout=60  # 数据更新可能需要较长时间
                    )
                    
                    if update_response.status_code == 200:
                        update_result = update_response.json()
                        if update_result.get('success'):
                            st.success("✅ 数据更新成功！")
                            
                            # 显示更新详情
                            col_a, col_b, col_c = st.columns(3)
                            with col_a:
                                st.metric("新增记录", f"{update_result.get('new_records', 0)} 条")
                            with col_b:
                                st.metric("总记录数", f"{update_result.get('total_records', 0):,} 条")
                            with col_c:
                                st.metric("更新时间", update_result.get('timestamp', 'N/A'))
                            
                            # 显示更新摘要
                            if 'summary' in update_result:
                                summary = update_result['summary']
                                st.write("**更新摘要:**")
                                st.write(f"- 处理记录: {summary.get('processed_records', 0)} 条")
                                st.write(f"- 有效记录: {summary.get('valid_records', 0)} 条")
                                st.write(f"- 重复记录: {summary.get('duplicate_records', 0)} 条")
                                st.write(f"- 错误记录: {summary.get('error_records', 0)} 条")
                            
                            time.sleep(2)  # 等待2秒后刷新页面
                            st.rerun()
                        else:
                            st.error(f"数据更新失败: {update_result.get('message', '未知错误')}")
                    else:
                        st.error(f"更新请求失败: HTTP {update_response.status_code}")
                        
                except requests.exceptions.Timeout:
                    st.error("更新请求超时，数据更新可能仍在进行中，请稍后检查结果")
                except Exception as e:
                    st.error(f"更新过程出现错误: {str(e)}")
    
    with col2:
        # 自动更新配置
        st.write("**自动更新配置**")
        
        # 获取当前自动更新配置
        try:
            config_response = requests.get(f"{api_base_url}/api/v1/config/auto-update", timeout=5)
            if config_response.status_code == 200:
                config_data = config_response.json()
                auto_update_enabled = config_data.get('enabled', False)
                update_time = config_data.get('update_time', '21:30')
                update_frequency = config_data.get('frequency', 'daily')
            else:
                auto_update_enabled = False
                update_time = '21:30'
                update_frequency = 'daily'
        except:
            auto_update_enabled = False
            update_time = '21:30'
            update_frequency = 'daily'
        
        # 自动更新开关
        new_auto_update = st.checkbox("启用自动更新", value=auto_update_enabled)
        
        # 更新时间设置
        new_update_time = st.time_input(
            "更新时间", 
            value=datetime.strptime(update_time, '%H:%M').time(),
            help="建议设置在晚上21:30之后，确保当日开奖结果已公布"
        )
        
        # 更新频率设置
        new_frequency = st.selectbox(
            "更新频率",
            options=['daily', 'hourly'],
            index=0 if update_frequency == 'daily' else 1,
            format_func=lambda x: '每日' if x == 'daily' else '每小时'
        )
        
        if st.button("💾 保存配置"):
            try:
                config_payload = {
                    'enabled': new_auto_update,
                    'update_time': new_update_time.strftime('%H:%M'),
                    'frequency': new_frequency
                }
                
                save_response = requests.post(
                    f"{api_base_url}/api/v1/config/auto-update",
                    json=config_payload,
                    timeout=10
                )
                
                if save_response.status_code == 200:
                    save_result = save_response.json()
                    if save_result.get('success'):
                        st.success("配置保存成功！")
                        time.sleep(1)
                        st.rerun()
                    else:
                        st.error(f"配置保存失败: {save_result.get('message', '未知错误')}")
                else:
                    st.error("配置保存失败")
            except Exception as e:
                st.error(f"保存配置时出错: {str(e)}")
    
    # 更新历史记录
    st.subheader("📋 更新历史记录")
    
    try:
        history_response = requests.get(f"{api_base_url}/api/v1/data/update-history", timeout=10)
        if history_response.status_code == 200:
            history_data = history_response.json()
            
            if history_data.get('success') and history_data.get('history'):
                history_list = history_data['history']
                
                # 创建历史记录数据框
                history_df = pd.DataFrame(history_list[:20])  # 显示最近20条记录
                
                # 格式化时间列
                if 'timestamp' in history_df.columns:
                    history_df['更新时间'] = pd.to_datetime(history_df['timestamp']).dt.strftime('%Y-%m-%d %H:%M:%S')
                
                # 重命名列
                column_mapping = {
                    'new_records': '新增记录',
                    'total_records': '总记录数',
                    'status': '状态',
                    'message': '说明',
                    'duration': '耗时(秒)'
                }
                
                display_df = history_df.copy()
                for old_col, new_col in column_mapping.items():
                    if old_col in display_df.columns:
                        display_df[new_col] = display_df[old_col]
                
                # 选择要显示的列
                display_cols = ['更新时间', '新增记录', '总记录数', '状态', '说明', '耗时(秒)']
                available_cols = [col for col in display_cols if col in display_df.columns]
                
                if available_cols:
                    st.dataframe(
                        display_df[available_cols],
                        hide_index=True,
                        use_container_width=True,
                        column_config={
                            "状态": st.column_config.TextColumn(
                                "状态",
                                help="更新操作的执行状态"
                            ),
                            "新增记录": st.column_config.NumberColumn(
                                "新增记录",
                                help="本次更新新增的记录数量"
                            ),
                            "总记录数": st.column_config.NumberColumn(
                                "总记录数",
                                help="更新后的总记录数量"
                            )
                        }
                    )
                else:
                    st.dataframe(display_df, hide_index=True, use_container_width=True)
                
                # 更新统计
                if len(history_list) > 0:
                    st.subheader("📊 更新统计")
                    
                    # 计算统计信息
                    successful_updates = len([h for h in history_list if h.get('status') == 'success'])
                    total_updates = len(history_list)
                    success_rate = (successful_updates / total_updates * 100) if total_updates > 0 else 0
                    
                    # 计算平均新增记录数
                    avg_new_records = sum([h.get('new_records', 0) for h in history_list]) / len(history_list)
                    
                    col1, col2, col3, col4 = st.columns(4)
                    with col1:
                        st.metric("成功率", f"{success_rate:.1f}%")
                    with col2:
                        st.metric("总更新次数", total_updates)
                    with col3:
                        st.metric("平均新增", f"{avg_new_records:.1f} 条")
                    with col4:
                        if history_list:
                            last_update = history_list[0]
                            last_status = last_update.get('status', 'unknown')
                            st.metric("最近状态", last_status)
            else:
                st.info("暂无更新历史记录")
        else:
            st.warning("无法获取更新历史记录")
    except Exception as e:
        st.error(f"获取更新历史时出错: {str(e)}")
    
    # 数据完整性验证
    st.subheader("🔍 数据完整性验证")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("🔍 验证数据完整性"):
            with st.spinner("正在验证数据完整性..."):
                try:
                    verify_response = requests.get(f"{api_base_url}/api/v1/data/verify", timeout=30)
                    if verify_response.status_code == 200:
                        verify_data = verify_response.json()
                        
                        if verify_data.get('success'):
                            st.success("✅ 数据完整性验证完成")
                            
                            # 显示验证结果
                            col_a, col_b, col_c = st.columns(3)
                            with col_a:
                                completeness = verify_data.get('completeness', 0)
                                st.metric("记录完整性", f"{completeness:.1%}")
                            with col_b:
                                consistency = verify_data.get('consistency', 0)
                                st.metric("数据一致性", f"{consistency:.1%}")
                            with col_c:
                                format_validity = verify_data.get('format_validity', 0)
                                st.metric("格式正确性", f"{format_validity:.1%}")
                            
                            # 显示验证详情
                            if 'details' in verify_data:
                                details = verify_data['details']
                                st.write("**验证详情:**")
                                st.write(f"- 总记录数: {details.get('total_records', 0):,}")
                                st.write(f"- 有效记录: {details.get('valid_records', 0):,}")
                                st.write(f"- 重复记录: {details.get('duplicate_records', 0):,}")
                                st.write(f"- 格式错误: {details.get('format_errors', 0):,}")
                                st.write(f"- 数据缺失: {details.get('missing_data', 0):,}")
                            
                            # 显示发现的问题
                            if verify_data.get('issues'):
                                st.warning("发现以下问题:")
                                for issue in verify_data['issues']:
                                    st.write(f"- {issue}")
                        else:
                            st.error("数据完整性验证失败")
                            st.write(verify_data.get('message', '未知错误'))
                    else:
                        st.error("验证请求失败")
                except Exception as e:
                    st.error(f"验证过程出现错误: {str(e)}")
    
    with col2:
        if st.button("🧹 清理重复数据"):
            with st.spinner("正在清理重复数据..."):
                try:
                    cleanup_response = requests.post(f"{api_base_url}/api/v1/data/cleanup", timeout=30)
                    if cleanup_response.status_code == 200:
                        cleanup_data = cleanup_response.json()
                        
                        if cleanup_data.get('success'):
                            st.success("✅ 数据清理完成")
                            
                            # 显示清理结果
                            removed_count = cleanup_data.get('removed_duplicates', 0)
                            remaining_count = cleanup_data.get('remaining_records', 0)
                            
                            st.write(f"**清理结果:**")
                            st.write(f"- 移除重复记录: {removed_count:,} 条")
                            st.write(f"- 剩余有效记录: {remaining_count:,} 条")
                            
                            if removed_count > 0:
                                st.info("建议刷新页面查看最新数据")
                        else:
                            st.error("数据清理失败")
                            st.write(cleanup_data.get('message', '未知错误'))
                    else:
                        st.error("清理请求失败")
                except Exception as e:
                    st.error(f"清理过程出现错误: {str(e)}")

if __name__ == "__main__":
    show_data_update()
