#!/usr/bin/env python3
"""
简化的改进验证脚本
"""

import os
import sqlite3
import requests
from pathlib import Path

def verify_improvements():
    """验证所有改进"""
    print("🔍 开始验证所有改进...")
    print("=" * 60)
    
    results = {}
    
    # 1. 验证数据库修复
    print("🗄️ 验证数据库结构修复...")
    try:
        db_path = "data/bug_detection.db"
        if os.path.exists(db_path):
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("PRAGMA table_info(bug_reports)")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            required_columns = ['environment', 'category', 'priority', 'tags', 'source']
            missing_columns = [col for col in required_columns if col not in column_names]
            
            if not missing_columns:
                print(f"✅ 数据库结构验证通过，包含 {len(columns)} 列")
                
                # 测试Bug分类统计功能
                try:
                    cursor.execute("SELECT environment, COUNT(*) FROM bug_reports GROUP BY environment")
                    stats = cursor.fetchall()
                    print(f"✅ Bug分类统计功能正常，返回 {len(stats)} 条记录")
                    results['database_fixes'] = True
                except Exception as e:
                    print(f"❌ Bug分类统计功能失败: {e}")
                    results['database_fixes'] = False
            else:
                print(f"❌ 仍缺失必需列: {missing_columns}")
                results['database_fixes'] = False
            
            conn.close()
        else:
            print("❌ 数据库文件不存在")
            results['database_fixes'] = False
            
    except Exception as e:
        print(f"❌ 数据库验证失败: {e}")
        results['database_fixes'] = False
    
    # 2. 验证AI优化
    print("\\n🤖 验证AI模型优化...")
    try:
        # 检查模型目录
        models_dir = Path("models")
        if models_dir.exists():
            print("✅ 模型目录存在")
            
            # 检查配置文件
            config_files = ['offline_config.py', 'lightweight_config.json', 'optimized_loader.py']
            config_count = sum(1 for f in config_files if (models_dir / f).exists())
            print(f"✅ 配置文件: {config_count}/{len(config_files)} 存在")
            
            # 测试TF-IDF功能
            try:
                from sklearn.feature_extraction.text import TfidfVectorizer
                vectorizer = TfidfVectorizer()
                test_texts = ["测试错误1", "测试错误2"]
                tfidf_matrix = vectorizer.fit_transform(test_texts)
                print(f"✅ TF-IDF功能正常: {tfidf_matrix.shape}")
                results['ai_optimizations'] = True
            except Exception as e:
                print(f"❌ TF-IDF功能失败: {e}")
                results['ai_optimizations'] = False
        else:
            print("❌ 模型目录不存在")
            results['ai_optimizations'] = False
            
    except Exception as e:
        print(f"❌ AI优化验证失败: {e}")
        results['ai_optimizations'] = False
    
    # 3. 验证性能优化
    print("\\n⚡ 验证性能优化...")
    try:
        opt_dir = Path("optimizations")
        if opt_dir.exists():
            print("✅ 优化目录存在")
            
            # 检查优化文件
            opt_files = ['singleton_pattern.py', 'connection_pool.py', 'performance_monitor.py']
            opt_count = sum(1 for f in opt_files if (opt_dir / f).exists())
            print(f"✅ 优化文件: {opt_count}/{len(opt_files)} 存在")
            
            # 检查性能指标
            metrics_file = opt_dir / "performance_metrics.json"
            if metrics_file.exists():
                print("✅ 性能指标文件存在")
            
            results['performance_optimizations'] = opt_count >= 2
        else:
            print("❌ 优化目录不存在")
            results['performance_optimizations'] = False
            
    except Exception as e:
        print(f"❌ 性能优化验证失败: {e}")
        results['performance_optimizations'] = False
    
    # 4. 验证服务功能
    print("\\n🌐 验证服务功能...")
    try:
        # 测试API服务
        try:
            response = requests.get("http://127.0.0.1:8888/health", timeout=10)
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ API服务正常: {health_data.get('status', 'unknown')}")
                results['service_functionality'] = True
            else:
                print(f"❌ API服务异常: {response.status_code}")
                results['service_functionality'] = False
        except Exception as e:
            print(f"❌ API服务连接失败: {e}")
            results['service_functionality'] = False
            
    except Exception as e:
        print(f"❌ 服务功能验证失败: {e}")
        results['service_functionality'] = False
    
    # 5. 生成验证报告
    print("\\n📊 生成验证报告...")
    total_tests = len(results)
    passed_tests = sum(results.values())
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    report = {
        'total_tests': total_tests,
        'passed_tests': passed_tests,
        'success_rate': success_rate,
        'results': results,
        'overall_status': 'PASS' if success_rate >= 75 else 'FAIL'
    }
    
    # 保存报告
    import json
    with open("verification_report.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print("=" * 60)
    print(f"📊 验证完成: {passed_tests}/{total_tests} 通过")
    print(f"🎯 成功率: {success_rate:.1f}%")
    print(f"🏆 总体状态: {report['overall_status']}")
    
    # 详细结果
    print("\\n📋 详细结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    return report['overall_status'] == 'PASS'

if __name__ == "__main__":
    verify_improvements()
