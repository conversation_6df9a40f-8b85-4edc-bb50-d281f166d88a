import sqlite3
import os

# 确保数据目录存在
os.makedirs("data", exist_ok=True)
db_path = "data/bug_detection.db"

print(f"🗄️ 连接数据库: {db_path}")
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# 首先创建基础表结构
print("📋 创建基础表结构...")

# 创建bug_reports表
cursor.execute("""
    CREATE TABLE IF NOT EXISTS bug_reports (
        id TEXT PRIMARY KEY,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        error_type TEXT NOT NULL,
        severity TEXT DEFAULT 'medium',
        page_name TEXT,
        error_message TEXT,
        stack_trace TEXT,
        status TEXT DEFAULT 'open',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
""")

print("✅ bug_reports表已创建")

# 检查现有列
cursor.execute("PRAGMA table_info(bug_reports)")
columns = cursor.fetchall()
existing_columns = [col[1] for col in columns]
print(f"📊 现有列: {existing_columns}")

# 需要添加的新列
new_columns = [
    ("environment", "TEXT DEFAULT 'production'"),
    ("category", "TEXT DEFAULT 'general'"),
    ("priority", "TEXT DEFAULT 'medium'"),
    ("tags", "TEXT"),
    ("source", "TEXT DEFAULT 'user'"),
    ("component_name", "TEXT"),
    ("reproduction_steps", "TEXT"),
    ("system_context", "TEXT"),
    ("user_journey", "TEXT"),
    ("screenshots", "TEXT")
]

added_count = 0

print("🔧 添加新列...")
for column_name, column_def in new_columns:
    if column_name not in existing_columns:
        try:
            sql = f"ALTER TABLE bug_reports ADD COLUMN {column_name} {column_def}"
            cursor.execute(sql)
            print(f"✅ 添加列: {column_name}")
            added_count += 1
        except Exception as e:
            print(f"❌ 添加列失败 {column_name}: {e}")
    else:
        print(f"⏭️ 列已存在: {column_name}")

# 创建其他必要的表
print("📋 创建其他必要表...")

# realtime_events表
cursor.execute("""
    CREATE TABLE IF NOT EXISTS realtime_events (
        id TEXT PRIMARY KEY,
        event_type TEXT NOT NULL,
        priority INTEGER NOT NULL,
        source TEXT NOT NULL,
        timestamp REAL NOT NULL,
        data TEXT,
        tags TEXT,
        correlation_id TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
""")

# 创建索引
cursor.execute("CREATE INDEX IF NOT EXISTS idx_event_type ON realtime_events (event_type)")
cursor.execute("CREATE INDEX IF NOT EXISTS idx_timestamp ON realtime_events (timestamp)")
cursor.execute("CREATE INDEX IF NOT EXISTS idx_priority ON realtime_events (priority)")

print("✅ realtime_events表已创建")

# anomaly_alerts表
cursor.execute("""
    CREATE TABLE IF NOT EXISTS anomaly_alerts (
        id TEXT PRIMARY KEY,
        alert_type TEXT NOT NULL,
        severity TEXT NOT NULL,
        message TEXT NOT NULL,
        details TEXT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        status TEXT DEFAULT 'active',
        resolved_at DATETIME
    )
""")

print("✅ anomaly_alerts表已创建")

# bug_patterns表
cursor.execute("""
    CREATE TABLE IF NOT EXISTS bug_patterns (
        id TEXT PRIMARY KEY,
        pattern_type TEXT NOT NULL,
        pattern_data TEXT NOT NULL,
        frequency INTEGER DEFAULT 1,
        last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
""")

print("✅ bug_patterns表已创建")

# 提交更改
conn.commit()

# 验证结果
cursor.execute("PRAGMA table_info(bug_reports)")
final_columns = cursor.fetchall()
print(f"📈 最终列数: {len(final_columns)}")
print(f"🆕 新增列数: {added_count}")

# 测试插入数据
print("🧪 测试数据库操作...")
try:
    test_data = (
        'test_001',
        'javascript_error', 
        'medium',
        'test_page',
        '测试错误消息',
        'test',
        'database',
        'medium',
        'test,upgrade',
        'upgrade_script'
    )
    
    cursor.execute("""
        INSERT INTO bug_reports 
        (id, error_type, severity, page_name, error_message, environment, category, priority, tags, source)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, test_data)
    
    # 查询验证
    cursor.execute("SELECT COUNT(*) FROM bug_reports WHERE id = 'test_001'")
    count = cursor.fetchone()[0]
    
    if count > 0:
        print("✅ 数据插入测试成功")
        # 清理测试数据
        cursor.execute("DELETE FROM bug_reports WHERE id = 'test_001'")
        conn.commit()
        print("🧹 测试数据已清理")
    else:
        print("❌ 数据插入测试失败")
        
except Exception as e:
    print(f"❌ 数据库测试失败: {e}")

# 显示所有表
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = cursor.fetchall()
print(f"📋 数据库包含表: {[table[0] for table in tables]}")

conn.close()
print("🎉 数据库创建和升级完成！")
