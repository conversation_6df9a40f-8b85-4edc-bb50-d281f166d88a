#!/usr/bin/env python3
"""
FastAPI主应用

高性能RESTful API服务，提供彩票数据查询和分析接口
"""

import logging
import sys
import time
from datetime import date, datetime
from typing import Any, Dict, List, Optional

from fastapi import Depends, FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

sys.path.append('src')

from api.dependencies import get_data_engine
from api.models import *
from core.data_engine import DataEngine

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="福彩3D数据分析API",
    description="高性能彩票数据查询和分析服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局数据引擎实例
data_engine = DataEngine("data/lottery.db")

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("🚀 FastAPI应用启动中...")
    
    # 初始化数据引擎
    try:
        # 检查数据库是否有数据，如果没有则从文件加载
        if data_engine.db_manager.get_records_count() == 0:
            logger.info("数据库为空，从文件加载数据...")
            from data.parser import DataParser
            
            try:
                with open('data/raw/3d_data_20250714_144231.txt', 'r', encoding='utf-8') as f:
                    raw_data = f.read()
                
                parser = DataParser()
                records, quality_report = parser.parse_data(raw_data)
                data_engine.load_data_from_records(records, save_to_db=True)
                logger.info(f"数据加载完成: {len(records)} 条记录")
            except Exception as e:
                logger.error(f"数据加载失败: {e}")
        else:
            logger.info(f"数据库已有数据: {data_engine.db_manager.get_records_count()} 条记录")
        
        logger.info("✅ FastAPI应用启动完成")
    except Exception as e:
        logger.error(f"❌ 应用启动失败: {e}")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("FastAPI应用关闭")

# 健康检查
@app.get("/health")
async def health_check():
    """健康检查接口"""
    try:
        record_count = data_engine.db_manager.get_records_count()
        date_range = data_engine.db_manager.get_date_range()

        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "database_records": record_count,
            "date_range": f"{date_range[0]} to {date_range[1]}" if date_range and date_range[0] else "No data"
        }
    except Exception as e:
        logger.error(f"Health check error: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

# 基础统计接口
@app.get("/api/v1/stats/basic")
async def get_basic_stats(
    use_cache: bool = Query(True, description="是否使用缓存")
):
    """获取基础统计信息"""
    try:
        start_time = time.time()
        stats = data_engine.get_basic_stats(use_cache=use_cache)
        query_time = time.time() - start_time

        return {
            "total_records": stats["total_records"],
            "date_range": stats["date_range"],
            "sum_value_stats": stats["sum_value_stats"],
            "span_value_stats": stats["span_value_stats"],
            "sales_amount_stats": stats["sales_amount_stats"],
            "query_time_ms": round(query_time * 1000, 2),
            "cached": use_cache
        }
    except Exception as e:
        logger.error(f"Basic stats error: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get basic stats: {str(e)}")

# 频率分析接口
@app.get("/api/v1/analysis/frequency")
async def get_frequency_analysis(
    position: str = Query("all", description="分析位置: all, hundreds, tens, units"),
    use_cache: bool = Query(True, description="是否使用缓存")
):
    """获取号码频率分析"""
    try:
        if position not in ["all", "hundreds", "tens", "units"]:
            raise HTTPException(status_code=400, detail="Invalid position parameter")
        
        start_time = time.time()
        analysis = data_engine.get_frequency_analysis(position, use_cache=use_cache)
        query_time = time.time() - start_time
        
        return {
            "position": position,
            "analysis": analysis,
            "query_time_ms": round(query_time * 1000, 2),
            "cached": use_cache
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get frequency analysis: {str(e)}")

# 和值分布接口
@app.get("/api/v1/analysis/sum-distribution", response_model=SumDistributionResponse)
async def get_sum_distribution(
    use_cache: bool = Query(True, description="是否使用缓存")
):
    """获取和值分布分析"""
    try:
        start_time = time.time()
        distribution = data_engine.get_sum_distribution(use_cache=use_cache)
        query_time = time.time() - start_time
        
        return SumDistributionResponse(
            sum_distribution=distribution["sum_distribution"],
            trial_sum_distribution=distribution["trial_sum_distribution"],
            query_time_ms=round(query_time * 1000, 2),
            cached=use_cache
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get sum distribution: {str(e)}")

# 销售额分析接口
@app.get("/api/v1/analysis/sales", response_model=SalesAnalysisResponse)
async def get_sales_analysis(
    use_cache: bool = Query(True, description="是否使用缓存")
):
    """获取销售额分析"""
    try:
        start_time = time.time()
        analysis = data_engine.get_sales_analysis(use_cache=use_cache)
        query_time = time.time() - start_time
        
        return SalesAnalysisResponse(
            overall=analysis["overall"],
            yearly=analysis["yearly"],
            query_time_ms=round(query_time * 1000, 2),
            cached=use_cache
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get sales analysis: {str(e)}")

# 数据查询接口
@app.get("/api/v1/data/query", response_model=DataQueryResponse)
async def query_data(
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    min_sum: Optional[int] = Query(None, description="最小和值"),
    max_sum: Optional[int] = Query(None, description="最大和值"),
    limit: int = Query(100, description="返回记录数限制", le=10000),
    use_polars: bool = Query(False, description="是否强制使用Polars引擎")
):
    """数据查询接口"""
    try:
        start_time = time.time()
        
        # 参数验证
        if start_date and end_date:
            # 日期范围查询
            df = data_engine.query_by_date_range(start_date, end_date, use_polars=use_polars)
        elif min_sum is not None and max_sum is not None:
            # 和值范围查询
            df = data_engine.query_by_sum_range(min_sum, max_sum, use_polars=use_polars)
        else:
            raise HTTPException(status_code=400, detail="Must provide either date range or sum range")
        
        # 限制返回记录数
        if len(df) > limit:
            df = df.head(limit)
        
        # 转换为字典列表
        records = df.to_dicts()
        
        query_time = time.time() - start_time
        
        return DataQueryResponse(
            records=records,
            total_count=len(records),
            query_time_ms=round(query_time * 1000, 2),
            query_params={
                "start_date": start_date,
                "end_date": end_date,
                "min_sum": min_sum,
                "max_sum": max_sum,
                "limit": limit,
                "use_polars": use_polars
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to query data: {str(e)}")

# 趋势分析接口
@app.get("/api/v1/analysis/trends", response_model=TrendsAnalysisResponse)
async def get_trends_analysis(
    days: int = Query(30, description="分析天数", ge=1, le=365),
    use_cache: bool = Query(True, description="是否使用缓存")
):
    """获取趋势分析"""
    try:
        start_time = time.time()
        trends = data_engine.get_recent_trends(days, use_cache=use_cache)
        query_time = time.time() - start_time
        
        return TrendsAnalysisResponse(
            period=trends.get("period", f"最近{days}期"),
            trends=trends,
            query_time_ms=round(query_time * 1000, 2),
            cached=use_cache
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get trends analysis: {str(e)}")

# 性能统计接口
@app.get("/api/v1/system/performance", response_model=PerformanceStatsResponse)
async def get_performance_stats():
    """获取系统性能统计"""
    try:
        perf_stats = data_engine.get_performance_stats()
        db_info = data_engine.db_manager.get_database_info()
        
        return PerformanceStatsResponse(
            performance_stats=perf_stats,
            database_info=db_info,
            timestamp=datetime.now()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get performance stats: {str(e)}")

# 缓存管理接口
@app.post("/api/v1/system/cache/clear")
async def clear_cache():
    """清理过期缓存"""
    try:
        start_time = time.time()
        result = data_engine.optimize_performance()
        operation_time = time.time() - start_time
        
        return {
            "message": "Cache cleared successfully",
            "expired_cache_cleared": result["expired_cache_cleared"],
            "operation_time_ms": round(operation_time * 1000, 2),
            "timestamp": datetime.now()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to clear cache: {str(e)}")

# 数据导出接口
@app.get("/api/v1/data/export")
async def export_data(
    format: str = Query("json", description="导出格式: json, csv"),
    analysis_type: str = Query("all", description="分析类型: all, basic, frequency, sales")
):
    """数据导出接口"""
    try:
        if format not in ["json", "csv"]:
            raise HTTPException(status_code=400, detail="Invalid format parameter")
        
        start_time = time.time()
        export_results = data_engine.export_analysis_results('data/processed')
        export_time = time.time() - start_time
        
        return {
            "message": "Data exported successfully",
            "export_files": export_results,
            "export_time_ms": round(export_time * 1000, 2),
            "timestamp": datetime.now()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to export data: {str(e)}")

# 注意：此文件已弃用，请使用项目根目录的 start_production_api.py 启动API服务
# 不要直接运行此文件
