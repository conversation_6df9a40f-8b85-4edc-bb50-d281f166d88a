import sqlite3
import os

print("开始数据库检查...")

# 确保目录存在
os.makedirs("data", exist_ok=True)
db_path = "data/bug_detection.db"

# 连接数据库
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# 检查表结构
cursor.execute("PRAGMA table_info(bug_reports)")
columns = cursor.fetchall()
column_names = [col[1] for col in columns]

print(f"当前列数: {len(columns)}")

# 检查必需列
required_columns = ['environment', 'category', 'priority', 'tags', 'source']
missing = [col for col in required_columns if col not in column_names]
existing = [col for col in required_columns if col in column_names]

print(f"已存在: {existing}")
print(f"缺失: {missing}")

# 添加缺失的列
for col in missing:
    try:
        if col == 'environment':
            cursor.execute("ALTER TABLE bug_reports ADD COLUMN environment TEXT DEFAULT 'production'")
        elif col == 'category':
            cursor.execute("ALTER TABLE bug_reports ADD COLUMN category TEXT DEFAULT 'general'")
        elif col == 'priority':
            cursor.execute("ALTER TABLE bug_reports ADD COLUMN priority TEXT DEFAULT 'medium'")
        elif col == 'tags':
            cursor.execute("ALTER TABLE bug_reports ADD COLUMN tags TEXT")
        elif col == 'source':
            cursor.execute("ALTER TABLE bug_reports ADD COLUMN source TEXT DEFAULT 'user'")
        print(f"添加列: {col}")
    except Exception as e:
        print(f"添加列失败 {col}: {e}")

conn.commit()
conn.close()
print("数据库检查完成")
