"""
增强版马尔可夫系统全面性能测试

对整个增强版马尔可夫链系统进行全面测试
"""

import sys
import os
import time
sys.path.append('src')

from prediction.markov_enhanced import EnhancedMarkovChain
from prediction.markov_integration import MarkovIntegrationAdapter


def test_enhanced_markov_initialization():
    """测试增强版马尔可夫链初始化"""
    print("🔧 测试增强版马尔可夫链初始化...")
    
    try:
        # 测试不同阶数初始化
        markov1 = EnhancedMarkovChain(max_order=1)
        markov2 = EnhancedMarkovChain(max_order=2)
        
        # 验证属性
        tests = [
            ("一阶模型初始化", markov1.max_order == 1),
            ("二阶模型初始化", markov2.max_order == 2),
            ("缓存目录创建", os.path.exists(markov1.cache_dir)),
            ("状态空间定义", len(markov1.first_order_states) == 10),
            ("二阶状态空间", len(markov2.second_order_states) == 100)
        ]
        
        passed = 0
        for test_name, result in tests:
            status = "✅" if result else "❌"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        return passed == len(tests)
        
    except Exception as e:
        print(f"   ❌ 初始化测试失败: {e}")
        return False


def test_enhanced_markov_training():
    """测试增强版马尔可夫链训练"""
    print("\n🎓 测试增强版马尔可夫链训练...")
    
    try:
        markov = EnhancedMarkovChain(max_order=2)
        
        # 训练模型
        print("   执行模型训练...")
        start_time = time.time()
        markov.train(data_limit=500)
        training_time = time.time() - start_time
        
        # 验证训练结果
        tests = [
            ("模型已训练", markov.is_trained),
            ("训练数据量", markov.training_data_size > 0),
            ("一阶矩阵构建", len(markov.first_order_matrix) == 3),
            ("二阶矩阵构建", len(markov.second_order_matrix) == 3),
            ("稀疏矩阵构建", len(markov.sparse_matrices['first_order']) == 3),
            ("训练时间合理", training_time < 60)  # 应在1分钟内完成
        ]
        
        passed = 0
        for test_name, result in tests:
            status = "✅" if result else "❌"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"   训练耗时: {training_time:.2f}秒")
        print(f"   训练数据量: {markov.training_data_size}")
        
        return passed == len(tests)
        
    except Exception as e:
        print(f"   ❌ 训练测试失败: {e}")
        return False


def test_prediction_methods():
    """测试各种预测方法"""
    print("\n🎯 测试各种预测方法...")
    
    try:
        markov = EnhancedMarkovChain(max_order=2)
        markov.train(data_limit=300)
        
        test_history = ['123', '456', '789']
        
        # 测试各种预测方法
        prediction_tests = []
        
        # 一阶预测
        try:
            pred1 = markov.predict_first_order('789')
            prediction_tests.append(("一阶预测", 
                                   isinstance(pred1, dict) and 'numbers' in pred1))
        except Exception as e:
            prediction_tests.append(("一阶预测", False))
            print(f"     一阶预测失败: {e}")
        
        # 二阶预测
        try:
            pred2 = markov.predict_second_order('456', '789')
            prediction_tests.append(("二阶预测", 
                                   isinstance(pred2, dict) and 'numbers' in pred2))
        except Exception as e:
            prediction_tests.append(("二阶预测", False))
            print(f"     二阶预测失败: {e}")
        
        # 混合预测
        try:
            pred3 = markov.predict_hybrid(test_history)
            prediction_tests.append(("混合预测", 
                                   isinstance(pred3, dict) and 'numbers' in pred3))
        except Exception as e:
            prediction_tests.append(("混合预测", False))
            print(f"     混合预测失败: {e}")
        
        # 自适应预测
        try:
            pred4 = markov.predict_adaptive(test_history)
            prediction_tests.append(("自适应预测", 
                                   isinstance(pred4, dict) and 'numbers' in pred4))
        except Exception as e:
            prediction_tests.append(("自适应预测", False))
            print(f"     自适应预测失败: {e}")
        
        # 集成预测
        try:
            pred5 = markov.ensemble_predict(test_history, num_predictions=3)
            prediction_tests.append(("集成预测", 
                                   isinstance(pred5, dict) and 'numbers' in pred5))
        except Exception as e:
            prediction_tests.append(("集成预测", False))
            print(f"     集成预测失败: {e}")
        
        # 统计结果
        passed = 0
        for test_name, result in prediction_tests:
            status = "✅" if result else "❌"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        return passed == len(prediction_tests)
        
    except Exception as e:
        print(f"   ❌ 预测方法测试失败: {e}")
        return False


def test_adaptive_order_selection():
    """测试自适应阶数选择"""
    print("\n🔍 测试自适应阶数选择...")
    
    try:
        markov = EnhancedMarkovChain(max_order=2)
        markov.train(data_limit=400)
        
        # 获取训练数据进行阶数选择测试
        data = markov.load_training_data(300)
        
        # 执行阶数选择
        selection_result = markov.select_optimal_order(data)
        
        # 验证结果
        tests = [
            ("选择结果存在", isinstance(selection_result, dict)),
            ("包含最优阶数", 'optimal_order' in selection_result),
            ("包含选择准则", 'criterion' in selection_result),
            ("包含所有结果", 'all_results' in selection_result),
            ("最优阶数合理", selection_result.get('optimal_order', 0) in [1, 2])
        ]
        
        passed = 0
        for test_name, result in tests:
            status = "✅" if result else "❌"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        if 'optimal_order' in selection_result:
            print(f"   推荐阶数: {selection_result['optimal_order']}")
            print(f"   选择准则: {selection_result['criterion']}")
        
        return passed == len(tests)
        
    except Exception as e:
        print(f"   ❌ 阶数选择测试失败: {e}")
        return False


def test_integration_adapter():
    """测试集成适配器"""
    print("\n🔗 测试集成适配器...")
    
    try:
        adapter = MarkovIntegrationAdapter()
        
        # 测试集成
        print("   执行系统集成...")
        integration_success = adapter.integrate_with_fusion_system()
        
        if not integration_success:
            print("   ❌ 系统集成失败")
            return False
        
        # 获取集成状态
        status = adapter.get_integration_status()
        
        # 测试预测对比
        test_history = ['123', '456', '789', '012']
        comparison = adapter.compare_predictions(test_history)
        
        # 验证结果
        tests = [
            ("集成成功", integration_success),
            ("集成状态", status.get('is_integrated', False)),
            ("增强模型已训练", status.get('enhanced_markov_trained', False)),
            ("预测对比", 'predictions' in comparison),
            ("包含推荐", 'recommendations' in comparison)
        ]
        
        passed = 0
        for test_name, result in tests:
            status_icon = "✅" if result else "❌"
            print(f"   {test_name}: {status_icon}")
            if result:
                passed += 1
        
        # 显示预测结果
        if 'predictions' in comparison:
            predictions = comparison['predictions']
            print(f"   预测方法数量: {len(predictions)}")
            
            for method, pred in predictions.items():
                if isinstance(pred, dict) and 'numbers' in pred:
                    conf = pred.get('confidence', 0)
                    print(f"     {method}: {pred['numbers']} (置信度: {conf:.3f})")
        
        return passed == len(tests)
        
    except Exception as e:
        print(f"   ❌ 集成适配器测试失败: {e}")
        return False


def test_performance_optimization():
    """测试性能优化功能"""
    print("\n⚡ 测试性能优化功能...")
    
    try:
        markov = EnhancedMarkovChain(max_order=2)
        
        # 测试缓存功能
        print("   测试缓存功能...")
        data = markov.load_training_data(200)
        data_hash = markov._get_data_hash(data)
        
        # 第一次训练
        start_time = time.time()
        markov.train(data_limit=200)
        first_training_time = time.time() - start_time
        
        # 保存缓存
        markov.save_model_cache(data_hash)
        
        # 创建新实例并尝试加载缓存
        markov2 = EnhancedMarkovChain(max_order=2)
        cache_loaded = markov2.load_model_cache(data_hash)
        
        # 测试内存优化
        print("   测试内存优化...")
        markov.optimize_memory_usage()
        
        # 获取性能统计
        perf_stats = markov.get_performance_stats()
        sparse_stats = markov.get_sparse_matrix_stats()
        
        # 验证结果
        tests = [
            ("缓存保存", os.path.exists(os.path.join(markov.cache_dir, f"model_{data_hash}.pkl"))),
            ("缓存加载", cache_loaded),
            ("性能统计", isinstance(perf_stats, dict)),
            ("稀疏矩阵统计", isinstance(sparse_stats, dict)),
            ("内存使用信息", 'memory_usage_mb' in perf_stats),
            ("训练时间合理", first_training_time < 30)
        ]
        
        passed = 0
        for test_name, result in tests:
            status = "✅" if result else "❌"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"   训练耗时: {first_training_time:.2f}秒")
        if 'memory_usage_mb' in perf_stats:
            print(f"   内存使用: {perf_stats['memory_usage_mb']:.2f}MB")
        
        return passed == len(tests)
        
    except Exception as e:
        print(f"   ❌ 性能优化测试失败: {e}")
        return False


def test_system_stability():
    """测试系统稳定性"""
    print("\n🛡️ 测试系统稳定性...")
    
    try:
        # 测试异常处理
        markov = EnhancedMarkovChain(max_order=2)
        
        stability_tests = []
        
        # 测试未训练状态的预测
        try:
            markov.predict_first_order('123')
            stability_tests.append(("未训练预测处理", False))  # 应该抛出异常
        except ValueError:
            stability_tests.append(("未训练预测处理", True))  # 正确抛出异常
        except Exception:
            stability_tests.append(("未训练预测处理", False))
        
        # 训练模型
        markov.train(data_limit=100)
        
        # 测试边界条件
        try:
            # 空历史数据
            result = markov.predict_adaptive([])
            stability_tests.append(("空历史处理", False))  # 应该抛出异常
        except ValueError:
            stability_tests.append(("空历史处理", True))
        except Exception:
            stability_tests.append(("空历史处理", False))
        
        # 测试单个历史数据
        try:
            result = markov.predict_adaptive(['123'])
            stability_tests.append(("单历史预测", isinstance(result, dict)))
        except Exception:
            stability_tests.append(("单历史预测", False))
        
        # 测试大量历史数据
        try:
            large_history = ['123'] * 1000
            result = markov.predict_adaptive(large_history)
            stability_tests.append(("大量历史处理", isinstance(result, dict)))
        except Exception:
            stability_tests.append(("大量历史处理", False))
        
        # 测试无效输入
        try:
            result = markov.predict_first_order('abc')
            stability_tests.append(("无效输入处理", False))  # 应该处理错误
        except Exception:
            stability_tests.append(("无效输入处理", True))  # 正确处理异常
        
        # 统计结果
        passed = 0
        for test_name, result in stability_tests:
            status = "✅" if result else "❌"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        return passed == len(stability_tests)
        
    except Exception as e:
        print(f"   ❌ 稳定性测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 增强版马尔可夫系统全面性能测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("增强版马尔可夫链初始化", test_enhanced_markov_initialization()))
    test_results.append(("增强版马尔可夫链训练", test_enhanced_markov_training()))
    test_results.append(("各种预测方法", test_prediction_methods()))
    test_results.append(("自适应阶数选择", test_adaptive_order_selection()))
    test_results.append(("集成适配器", test_integration_adapter()))
    test_results.append(("性能优化功能", test_performance_optimization()))
    test_results.append(("系统稳定性", test_system_stability()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📋 测试结果汇总:")
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed_tests += 1
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    # 评估系统质量
    success_rate = passed_tests / total_tests
    
    if success_rate >= 0.9:
        print("🎉 系统质量优秀！增强版马尔可夫链系统功能完整且性能良好！")
        grade = "A"
    elif success_rate >= 0.8:
        print("✅ 系统质量良好！大部分功能正常，建议优化失败项目")
        grade = "B"
    elif success_rate >= 0.7:
        print("⚠️ 系统基本可用，但需要进一步完善")
        grade = "C"
    else:
        print("❌ 系统存在较多问题，需要重点修复")
        grade = "D"
    
    print(f"\n🏆 系统评级: {grade}级")
    print(f"📈 成功率: {success_rate:.1%}")
    
    return success_rate >= 0.7


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
