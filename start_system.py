#!/usr/bin/env python3
"""
福彩3D预测系统一键启动脚本
解决"启动失败: None"等启动问题
"""

import os
import sys
import time
import subprocess
import requests
import psutil
from pathlib import Path

def print_header():
    """打印启动脚本标题"""
    print("=" * 60)
    print("🎯 福彩3D预测系统一键启动脚本")
    print("=" * 60)
    print()

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    version = sys.version_info
    print(f"   Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major != 3 or version.minor < 8:
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print("✅ Python版本检查通过")
    return True

def kill_process_on_port(port):
    """杀死占用指定端口的进程"""
    try:
        for proc in psutil.process_iter(['pid', 'name', 'connections']):
            try:
                connections = proc.info['connections']
                if connections:
                    for conn in connections:
                        if conn.laddr.port == port:
                            print(f"   终止进程 {proc.info['name']} (PID: {proc.info['pid']})")
                            proc.terminate()
                            proc.wait(timeout=3)
                            return True
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
    except Exception as e:
        print(f"   警告: 无法检查端口占用 - {e}")
    return False

def check_and_free_ports():
    """检查并释放端口"""
    print("🔍 检查端口占用情况...")
    
    ports_to_check = [8888, 8501]
    
    for port in ports_to_check:
        try:
            # 检查端口是否被占用
            for proc in psutil.process_iter(['pid', 'name', 'connections']):
                try:
                    connections = proc.info['connections']
                    if connections:
                        for conn in connections:
                            if conn.laddr.port == port:
                                print(f"⚠️ 端口{port}被进程{proc.info['name']}占用，正在释放...")
                                kill_process_on_port(port)
                                time.sleep(2)
                                break
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
        except Exception as e:
            print(f"   警告: 检查端口{port}时出错 - {e}")
    
    print("✅ 端口检查完成")

def start_api_service():
    """启动API服务"""
    print("🚀 启动API服务...")
    
    try:
        # 启动API服务
        api_process = subprocess.Popen(
            [sys.executable, "-m", "uvicorn", "src.api.production_main:app", 
             "--host", "127.0.0.1", "--port", "8888"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print("⏳ 等待API服务启动...")
        time.sleep(8)
        
        # 验证API服务
        for attempt in range(5):
            try:
                response = requests.get("http://127.0.0.1:8888/health", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    print("✅ API服务启动成功")
                    print(f"   状态: {data.get('status', 'N/A')}")
                    print(f"   数据库记录: {data.get('database_records', 'N/A')}")
                    print(f"   数据范围: {data.get('date_range', 'N/A')}")
                    return True
            except Exception as e:
                if attempt < 4:
                    print(f"   尝试 {attempt + 1}/5: 等待API服务响应...")
                    time.sleep(3)
                else:
                    print(f"❌ API服务启动失败: {e}")
                    return False
        
        return False
        
    except Exception as e:
        print(f"❌ 启动API服务时出错: {e}")
        return False

def start_streamlit_service():
    """启动Streamlit服务"""
    print("🖥️ 启动Streamlit界面...")
    
    try:
        # 启动Streamlit服务
        streamlit_process = subprocess.Popen(
            [sys.executable, "-m", "streamlit", "run", "src/ui/main.py", 
             "--server.port", "8501", "--server.address", "127.0.0.1"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print("⏳ 等待Streamlit界面启动...")
        time.sleep(5)
        
        # 验证Streamlit服务
        try:
            response = requests.get("http://127.0.0.1:8501", timeout=10)
            if response.status_code == 200:
                print("✅ Streamlit界面启动成功")
                return True
            else:
                print(f"⚠️ Streamlit界面响应异常: HTTP {response.status_code}")
                return True  # 有时候Streamlit会返回非200状态码但仍然正常工作
        except Exception as e:
            print(f"⚠️ 无法验证Streamlit状态: {e}")
            return True  # 假设启动成功
        
    except Exception as e:
        print(f"❌ 启动Streamlit界面时出错: {e}")
        return False

def print_success_info():
    """打印成功信息"""
    print()
    print("=" * 60)
    print("🎉 系统启动完成！")
    print("=" * 60)
    print()
    print("📊 API服务地址: http://127.0.0.1:8888")
    print("🖥️ 用户界面地址: http://127.0.0.1:8501")
    print()
    print("💡 使用提示:")
    print("- 请在浏览器中访问用户界面地址")
    print("- 如果界面没有自动打开，请手动访问上述地址")
    print("- 要停止服务，请按Ctrl+C或关闭命令行窗口")
    print()

def main():
    """主函数"""
    print_header()
    
    # 检查Python版本
    if not check_python_version():
        input("按Enter键退出...")
        return False
    
    print()
    
    # 检查并释放端口
    check_and_free_ports()
    print()
    
    # 启动API服务
    if not start_api_service():
        print("❌ API服务启动失败，无法继续")
        input("按Enter键退出...")
        return False
    
    print()
    
    # 启动Streamlit服务
    if not start_streamlit_service():
        print("❌ Streamlit界面启动失败")
        input("按Enter键退出...")
        return False
    
    # 打印成功信息
    print_success_info()
    
    try:
        print("🔄 系统正在运行中... (按Ctrl+C停止)")
        while True:
            time.sleep(10)
            # 定期检查服务状态
            try:
                response = requests.get("http://127.0.0.1:8888/health", timeout=5)
                if response.status_code != 200:
                    print("⚠️ API服务状态异常")
            except:
                print("⚠️ API服务连接失败")
                
    except KeyboardInterrupt:
        print("\n👋 正在停止服务...")
        return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ 启动脚本执行失败: {e}")
        input("按Enter键退出...")
        sys.exit(1)
