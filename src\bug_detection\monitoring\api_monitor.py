"""
API性能监控中间件
创建日期: 2025年7月24日
用途: 监控FastAPI接口性能和错误，记录响应时间和异常信息
"""

import asyncio
import json
import logging
import time
from collections import defaultdict, deque
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional

try:
    from fastapi import Request, Response
    from fastapi.middleware.base import BaseHTTPMiddleware
    from starlette.middleware.base import RequestResponseEndpoint
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    # 创建占位符类
    class BaseHTTPMiddleware:
        def __init__(self, app=None, database_manager=None, app_name="unknown"):
            pass
    class Request:
        pass
    class Response:
        pass
    RequestResponseEndpoint = None
import asyncio

logger = logging.getLogger(__name__)

class APIPerformanceMonitor(BaseHTTPMiddleware):
    """增强版API性能监控中间件 - 支持实时监控和告警"""

    def __init__(self, app, database_manager=None, event_bus=None, exclude_paths=None):
        """
        初始化API监控中间件

        Args:
            app: FastAPI应用实例
            database_manager: 数据库管理器
            event_bus: 事件总线
            exclude_paths: 排除监控的路径列表
        """
        super().__init__(app)
        self.db_manager = database_manager
        self.event_bus = event_bus
        self.exclude_paths = exclude_paths or ['/docs', '/redoc', '/openapi.json', '/favicon.ico', '/ws']

        # 性能数据存储
        self.performance_data = []
        self.error_count = 0
        self.request_count = 0

        # 实时监控数据
        self.endpoint_metrics = defaultdict(lambda: {
            'response_times': deque(maxlen=100),
            'error_count': 0,
            'request_count': 0,
            'last_error_time': None,
            'avg_response_time': 0.0
        })

        # 告警阈值配置
        self.alert_thresholds = {
            'response_time_threshold': 2.0,  # 2秒
            'error_rate_threshold': 0.05,    # 5%
            'consecutive_errors_threshold': 5,
            'slow_requests_threshold': 10     # 连续10个慢请求
        }

        # 实时统计
        self.real_time_stats = {
            'total_requests': 0,
            'total_errors': 0,
            'avg_response_time': 0.0,
            'requests_per_second': 0.0,
            'last_minute_requests': deque(maxlen=60),
            'active_requests': 0
        }

        # 异步任务
        self.background_tasks = []
        self.monitoring_enabled = True
    
    async def dispatch(self, request, call_next):
        """增强版请求处理和性能监控"""

        if not FASTAPI_AVAILABLE or not self.monitoring_enabled:
            return await call_next(request)

        # 检查是否需要排除此路径
        path = str(request.url.path) if hasattr(request, 'url') else str(request.get('path', ''))
        if self._should_exclude_path(path):
            return await call_next(request)

        # 增加活跃请求计数
        self.real_time_stats['active_requests'] += 1

        # 记录请求开始时间
        start_time = time.time()
        request_id = f"req_{int(start_time * 1000)}_{id(request)}"

        # 收集请求信息
        method = getattr(request, 'method', 'UNKNOWN')
        url = str(getattr(request, 'url', ''))
        client_ip = self._get_client_ip(request)
        user_agent = ''

        if hasattr(request, 'headers'):
            user_agent = request.headers.get('user-agent', '')

        request_info = {
            'request_id': request_id,
            'method': method,
            'url': url,
            'path': path,
            'client_ip': client_ip,
            'user_agent': user_agent,
            'start_time': start_time,
            'timestamp': start_time
        }

        response = None
        error_info = None
        status_code = 200

        try:
            # 执行请求
            response = await call_next(request)
            status_code = getattr(response, 'status_code', 200)

        except Exception as e:
            # 记录异常信息
            status_code = 500
            error_info = {
                'error_type': type(e).__name__,
                'error_message': str(e),
                'error_traceback': self._get_traceback_string(e)
            }
            logger.error(f"API Error in {path}: {e}")

            # 创建错误响应
            try:
                from fastapi.responses import JSONResponse
                response = JSONResponse(
                    status_code=500,
                    content={"error": "Internal Server Error", "request_id": request_id}
                )
            except ImportError:
                # 降级处理
                response = None

        # 计算响应时间
        end_time = time.time()
        response_time = end_time - start_time

        # 减少活跃请求计数
        self.real_time_stats['active_requests'] = max(0, self.real_time_stats['active_requests'] - 1)

        # 收集响应信息
        response_info = {
            'status_code': status_code,
            'response_time': response_time,
            'end_time': end_time,
            'has_error': error_info is not None
        }

        # 合并监控数据
        monitoring_data = {**request_info, **response_info}
        if error_info:
            monitoring_data.update(error_info)

        # 异步处理监控数据
        asyncio.create_task(self._process_monitoring_data(monitoring_data))
        
        # 异步保存监控数据
        asyncio.create_task(self._save_monitoring_data(monitoring_data))
        
        # 更新统计信息
        self._update_stats(monitoring_data)
        
        # 添加监控头信息
        if response:
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Response-Time"] = f"{response_time:.3f}s"
        
        return response
    
    def _should_exclude_path(self, path: str) -> bool:
        """检查路径是否应该被排除"""
        return any(excluded in path for excluded in self.exclude_paths)
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        forwarded_for = request.headers.get('x-forwarded-for')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()
        
        real_ip = request.headers.get('x-real-ip')
        if real_ip:
            return real_ip
        
        # 返回直接连接的IP
        return request.client.host if request.client else 'unknown'
    
    def _get_traceback_string(self, exception: Exception) -> str:
        """获取异常的traceback字符串"""
        import traceback
        return traceback.format_exc()

    async def _process_monitoring_data(self, data: Dict[str, Any]):
        """异步处理监控数据 - 实时分析和告警"""
        try:
            path = data.get('path', '')
            response_time = data.get('response_time', 0)
            status_code = data.get('status_code', 200)
            has_error = data.get('has_error', False)

            # 更新端点指标
            endpoint_metric = self.endpoint_metrics[path]
            endpoint_metric['response_times'].append(response_time)
            endpoint_metric['request_count'] += 1

            if has_error or status_code >= 400:
                endpoint_metric['error_count'] += 1
                endpoint_metric['last_error_time'] = time.time()

            # 计算平均响应时间
            if endpoint_metric['response_times']:
                endpoint_metric['avg_response_time'] = sum(endpoint_metric['response_times']) / len(endpoint_metric['response_times'])

            # 检查告警条件
            await self._check_alert_conditions(path, endpoint_metric, data)

            # 发布实时事件
            if self.event_bus:
                await self._publish_performance_event(data)

        except Exception as e:
            logger.error(f"处理监控数据失败: {e}")

    async def _check_alert_conditions(self, path: str, metrics: Dict[str, Any], data: Dict[str, Any]):
        """检查告警条件"""
        try:
            response_time = data.get('response_time', 0)
            has_error = data.get('has_error', False)

            # 响应时间告警
            if response_time > self.alert_thresholds['response_time_threshold']:
                await self._trigger_alert(
                    'slow_response',
                    f"API响应时间过慢: {path} 响应时间 {response_time:.2f}s",
                    {
                        'path': path,
                        'response_time': response_time,
                        'threshold': self.alert_thresholds['response_time_threshold']
                    }
                )

            # 错误率告警
            error_rate = metrics['error_count'] / max(metrics['request_count'], 1)
            if error_rate > self.alert_thresholds['error_rate_threshold']:
                await self._trigger_alert(
                    'high_error_rate',
                    f"API错误率过高: {path} 错误率 {error_rate:.2%}",
                    {
                        'path': path,
                        'error_rate': error_rate,
                        'threshold': self.alert_thresholds['error_rate_threshold']
                    }
                )

            # 连续错误告警
            if has_error:
                recent_errors = sum(1 for rt in list(metrics['response_times'])[-10:] if rt == -1)  # -1表示错误
                if recent_errors >= self.alert_thresholds['consecutive_errors_threshold']:
                    await self._trigger_alert(
                        'consecutive_errors',
                        f"API连续错误: {path} 连续 {recent_errors} 个错误",
                        {
                            'path': path,
                            'consecutive_errors': recent_errors,
                            'threshold': self.alert_thresholds['consecutive_errors_threshold']
                        }
                    )

        except Exception as e:
            logger.error(f"检查告警条件失败: {e}")

    async def _trigger_alert(self, alert_type: str, message: str, data: Dict[str, Any]):
        """触发告警"""
        try:
            if self.event_bus:
                # 导入事件相关模块
                from ..realtime.event_bus import (EventPriority, EventType,
                                                  publish_event)

                await publish_event(
                    EventType.ALERT_TRIGGERED,
                    {
                        'alert_type': alert_type,
                        'message': message,
                        'data': data,
                        'source': 'api_monitor'
                    },
                    EventPriority.HIGH,
                    "api_performance_monitor"
                )

            logger.warning(f"🚨 API告警: {message}")

        except Exception as e:
            logger.error(f"触发告警失败: {e}")

    async def _publish_performance_event(self, data: Dict[str, Any]):
        """发布性能事件"""
        try:
            if self.event_bus:
                # 导入事件相关模块
                from ..realtime.event_bus import (EventPriority, EventType,
                                                  publish_event)

                await publish_event(
                    EventType.API_PERFORMANCE,
                    data,
                    EventPriority.MEDIUM,
                    "api_performance_monitor"
                )

        except Exception as e:
            logger.error(f"发布性能事件失败: {e}")

    async def _save_monitoring_data(self, data: Dict[str, Any]):
        """异步保存监控数据"""
        try:
            # 保存到数据库
            if self.db_manager:
                self.db_manager.save_performance_metric(
                    endpoint=data['path'],
                    response_time=data['response_time'],
                    status_code=data['status_code']
                )
            
            # 保存到内存（用于实时统计）
            self.performance_data.append(data)
            
            # 限制内存中的数据量
            if len(self.performance_data) > 1000:
                self.performance_data = self.performance_data[-500:]
            
            # 记录慢请求
            if data['response_time'] > 2.0:  # 超过2秒的请求
                logger.warning(f"Slow API request: {data['path']} took {data['response_time']:.3f}s")
            
            # 记录错误请求
            if data.get('has_error') or data['status_code'] >= 400:
                logger.error(f"API error: {data['path']} returned {data['status_code']}")
                
        except Exception as e:
            logger.error(f"Error saving monitoring data: {e}")
    
    def _update_stats(self, data: Dict[str, Any]):
        """更新统计信息"""
        self.request_count += 1
        
        if data.get('has_error') or data['status_code'] >= 400:
            self.error_count += 1
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.performance_data:
            return {
                'total_requests': self.request_count,
                'error_count': self.error_count,
                'error_rate': 0,
                'avg_response_time': 0,
                'endpoints': {}
            }
        
        # 计算总体统计
        response_times = [d['response_time'] for d in self.performance_data]
        avg_response_time = sum(response_times) / len(response_times)
        
        # 按端点统计
        endpoint_stats = {}
        for data in self.performance_data:
            path = data['path']
            if path not in endpoint_stats:
                endpoint_stats[path] = {
                    'count': 0,
                    'total_time': 0,
                    'errors': 0,
                    'status_codes': {}
                }
            
            stats = endpoint_stats[path]
            stats['count'] += 1
            stats['total_time'] += data['response_time']
            
            if data.get('has_error') or data['status_code'] >= 400:
                stats['errors'] += 1
            
            status_code = str(data['status_code'])
            stats['status_codes'][status_code] = stats['status_codes'].get(status_code, 0) + 1
        
        # 计算每个端点的平均响应时间
        for path, stats in endpoint_stats.items():
            stats['avg_response_time'] = stats['total_time'] / stats['count']
            stats['error_rate'] = stats['errors'] / stats['count']
        
        return {
            'total_requests': self.request_count,
            'error_count': self.error_count,
            'error_rate': self.error_count / self.request_count if self.request_count > 0 else 0,
            'avg_response_time': avg_response_time,
            'endpoints': endpoint_stats,
            'last_updated': datetime.now().isoformat()
        }
    
    def get_recent_errors(self, limit: int = 10) -> list:
        """获取最近的错误"""
        errors = [
            data for data in self.performance_data 
            if data.get('has_error') or data['status_code'] >= 400
        ]
        return sorted(errors, key=lambda x: x['start_time'], reverse=True)[:limit]
    
    def get_slow_requests(self, threshold: float = 1.0, limit: int = 10) -> list:
        """获取慢请求"""
        slow_requests = [
            data for data in self.performance_data 
            if data['response_time'] > threshold
        ]
        return sorted(slow_requests, key=lambda x: x['response_time'], reverse=True)[:limit]

class APIMonitoringManager:
    """API监控管理器"""
    
    def __init__(self):
        self.monitors = {}
    
    def add_monitor(self, app_name: str, monitor: APIPerformanceMonitor):
        """添加监控器"""
        self.monitors[app_name] = monitor
    
    def get_monitor(self, app_name: str) -> Optional[APIPerformanceMonitor]:
        """获取监控器"""
        return self.monitors.get(app_name)
    
    def get_all_summaries(self) -> Dict[str, Any]:
        """获取所有应用的监控摘要"""
        summaries = {}
        for app_name, monitor in self.monitors.items():
            summaries[app_name] = monitor.get_performance_summary()
        return summaries

# 全局监控管理器实例
monitoring_manager = APIMonitoringManager()

def create_api_monitor(database_manager=None, exclude_paths: list = None) -> APIPerformanceMonitor:
    """创建API监控中间件"""
    return APIPerformanceMonitor(
        app=None,  # 将在添加到app时设置
        database_manager=database_manager,
        exclude_paths=exclude_paths
    )

def add_monitoring_to_app(app, database_manager=None, app_name: str = "default"):
    """为FastAPI应用添加监控中间件"""
    try:
        # 确保数据库管理器存在
        if database_manager is None:
            from ..core.database_manager import DatabaseManager
            database_manager = DatabaseManager()

        # 创建监控器实例
        monitor = create_api_monitor(database_manager)

        # 创建中间件实例的工厂函数
        def create_middleware(app):
            return APIPerformanceMonitor(
                app=app,
                database_manager=database_manager,
                exclude_paths=['/docs', '/redoc', '/openapi.json', '/favicon.ico', '/health']
            )

        # 使用工厂函数添加中间件
        app.add_middleware(APIPerformanceMonitor, database_manager=database_manager)

        # 添加到监控管理器
        monitoring_manager.add_monitor(app_name, monitor)

        logger.info(f"API monitoring added to app: {app_name}")
        return True

    except Exception as e:
        logger.error(f"Failed to add monitoring to app: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    logger.info(f"API monitoring added to app: {app_name}")
    return monitor
