#!/usr/bin/env python3
"""
测试Bug分类功能
"""

import sys
import os
sys.path.insert(0, '.')

from src.bug_detection.algorithms.enhanced_detection import EnhancedBugDetector
from src.bug_detection.feedback.bug_reporter import IntelligentBugReporter
from src.bug_detection.core.database_manager import DatabaseManager

def test_enhanced_detection():
    print("=== 测试增强Bug检测算法 ===")
    
    # 初始化增强检测器
    detector = EnhancedBugDetector()
    print("✅ EnhancedBugDetector 初始化完成")
    
    # 测试数据
    test_error_data = {
        'type': 'javascript',
        'message': 'Cannot read properties of null (reading \'property\')',
        'stack_trace': 'TypeError: Cannot read properties of null at test.js:5:10',
        'page_url': 'http://127.0.0.1:8501/bug_detection_status',
        'source': 'test.js',
        'line': 5,
        'column': 10
    }
    
    print(f"测试错误数据: {test_error_data}")
    
    # 执行检测和分类
    try:
        result = detector.detect_and_classify(test_error_data)
        print("\n✅ 检测和分类结果:")
        for key, value in result.items():
            print(f"  {key}: {value}")
        
        return result
        
    except Exception as e:
        print(f"❌ 检测失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_intelligent_bug_reporter():
    print("\n=== 测试智能Bug报告生成器 ===")
    
    # 初始化数据库管理器和Bug报告器
    db_manager = DatabaseManager()
    reporter = IntelligentBugReporter(db_manager)
    print("✅ IntelligentBugReporter 初始化完成")
    
    # 测试数据
    test_error_data = {
        'type': 'javascript',
        'message': 'Cannot read properties of null (reading \'property\')',
        'stack_trace': 'TypeError: Cannot read properties of null at test.js:5:10',
        'pageName': 'bug_detection_status',
        'url': 'http://127.0.0.1:8501/bug_detection_status',
        'source': 'test.js',
        'line': 5,
        'column': 10,
        'sessionId': 'test_session_123',
        'userAgent': 'Mozilla/5.0 Test Browser'
    }
    
    print(f"测试错误数据: {test_error_data}")
    
    # 生成增强报告
    try:
        report = reporter.generate_enhanced_report(test_error_data)
        print("\n✅ 增强报告生成结果:")
        for key, value in report.items():
            if key == 'enhanced_analysis':
                print(f"  {key}:")
                for sub_key, sub_value in value.items():
                    print(f"    {sub_key}: {sub_value}")
            else:
                print(f"  {key}: {value}")
        
        return report
        
    except Exception as e:
        print(f"❌ 报告生成失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_database_save():
    print("\n=== 测试数据库保存功能 ===")
    
    # 生成测试报告
    report = test_intelligent_bug_reporter()
    if not report:
        print("❌ 无法生成测试报告")
        return
    
    # 保存到数据库
    db_manager = DatabaseManager()
    try:
        bug_id = db_manager.save_bug_report(report)
        print(f"✅ Bug报告已保存，ID: {bug_id}")
        
        # 验证保存结果
        saved_bug = db_manager.get_bug_by_id(bug_id)
        if saved_bug:
            print("✅ 保存验证成功:")
            print(f"  ID: {saved_bug.get('id')}")
            print(f"  错误类型: {saved_bug.get('error_type')}")
            print(f"  严重程度: {saved_bug.get('severity')}")
            print(f"  分类: {saved_bug.get('category')}")
            print(f"  环境: {saved_bug.get('environment')}")
            print(f"  优先级: {saved_bug.get('priority')}")
        else:
            print("❌ 保存验证失败")
            
    except Exception as e:
        print(f"❌ 数据库保存失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🔧 Bug分类功能测试")
    print("=" * 50)
    
    # 测试增强检测算法
    detection_result = test_enhanced_detection()
    
    # 测试智能Bug报告生成器
    if detection_result:
        test_intelligent_bug_reporter()
    
    # 测试数据库保存
    test_database_save()
    
    print("\n" + "=" * 50)
    print("🎉 测试完成")

if __name__ == "__main__":
    main()
