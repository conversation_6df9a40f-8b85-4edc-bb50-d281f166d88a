"""
马尔可夫模型优化单元测试

测试数据窗口扩展和拉普拉斯平滑的效果
"""

import unittest
import numpy as np
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.prediction.pattern_prediction import PatternPredictor


class TestMarkovOptimization(unittest.TestCase):
    """马尔可夫模型优化测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.predictor = PatternPredictor(
            transition_window_size=1000,
            probability_window_size=500,
            smoothing_alpha=1.0
        )
        
        # 创建测试数据
        self.test_data = []
        for i in range(1200):  # 创建足够的测试数据
            self.test_data.append({
                'numbers': f"{i%10}{(i+1)%10}{(i+2)%10}",
                'period': f"2024{i:03d}",
                'date': f"2024-01-{(i%30)+1:02d}"
            })
    
    def test_configuration_parameters(self):
        """测试配置参数"""
        # 测试默认参数
        predictor_default = PatternPredictor()
        self.assertEqual(predictor_default.transition_window_size, 1000)
        self.assertEqual(predictor_default.probability_window_size, 500)
        self.assertEqual(predictor_default.smoothing_alpha, 1.0)
        
        # 测试自定义参数
        predictor_custom = PatternPredictor(
            transition_window_size=2000,
            probability_window_size=800,
            smoothing_alpha=0.5
        )
        self.assertEqual(predictor_custom.transition_window_size, 2000)
        self.assertEqual(predictor_custom.probability_window_size, 800)
        self.assertEqual(predictor_custom.smoothing_alpha, 0.5)
    
    def test_build_transition_matrix_window_size(self):
        """测试转移矩阵数据窗口大小"""
        # 测试不同窗口大小
        matrix_100 = self.predictor._build_transition_matrix(self.test_data, window_size=100)
        matrix_1000 = self.predictor._build_transition_matrix(self.test_data, window_size=1000)
        
        # 验证矩阵形状
        self.assertEqual(matrix_100.shape, (10, 10))
        self.assertEqual(matrix_1000.shape, (10, 10))
        
        # 验证概率矩阵性质（每行和为1）
        for i in range(10):
            self.assertAlmostEqual(matrix_100[i].sum(), 1.0, places=5)
            self.assertAlmostEqual(matrix_1000[i].sum(), 1.0, places=5)
        
        # 验证所有概率非负
        self.assertTrue(np.all(matrix_100 >= 0))
        self.assertTrue(np.all(matrix_1000 >= 0))
    
    def test_calculate_digit_probabilities_window_size(self):
        """测试数字概率计算窗口大小"""
        # 测试不同窗口大小
        probs_50 = self.predictor._calculate_digit_probabilities(self.test_data, window_size=50)
        probs_500 = self.predictor._calculate_digit_probabilities(self.test_data, window_size=500)
        
        # 验证概率数组长度
        self.assertEqual(len(probs_50), 10)
        self.assertEqual(len(probs_500), 10)
        
        # 验证概率和为1
        self.assertAlmostEqual(probs_50.sum(), 1.0, places=5)
        self.assertAlmostEqual(probs_500.sum(), 1.0, places=5)
        
        # 验证所有概率非负
        self.assertTrue(np.all(probs_50 >= 0))
        self.assertTrue(np.all(probs_500 >= 0))
    
    def test_laplace_smoothing(self):
        """测试拉普拉斯平滑"""
        # 创建测试矩阵（包含零值）
        test_matrix = np.array([
            [5, 0, 2, 0, 1, 0, 0, 0, 0, 0],
            [0, 3, 0, 4, 0, 1, 0, 0, 0, 0],
            [1, 0, 0, 0, 5, 0, 2, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],  # 全零行
            [2, 1, 1, 1, 1, 1, 1, 1, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],  # 全零行
            [3, 2, 1, 0, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],  # 全零行
            [1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]   # 全零行
        ], dtype=float)
        
        # 应用拉普拉斯平滑
        smoothed_matrix = self.predictor._apply_laplace_smoothing(test_matrix, alpha=1.0)
        
        # 验证矩阵形状
        self.assertEqual(smoothed_matrix.shape, (10, 10))
        
        # 验证每行和为1
        for i in range(10):
            self.assertAlmostEqual(smoothed_matrix[i].sum(), 1.0, places=5)
        
        # 验证所有概率非负
        self.assertTrue(np.all(smoothed_matrix >= 0))
        
        # 验证平滑效果：原来的零值现在应该有小的正概率
        for i in range(10):
            for j in range(10):
                self.assertGreater(smoothed_matrix[i, j], 0)
    
    def test_laplace_smoothing_alpha_parameter(self):
        """测试拉普拉斯平滑的alpha参数效果"""
        test_matrix = np.array([
            [10, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
        ], dtype=float)
        
        # 测试不同alpha值
        smoothed_alpha_1 = self.predictor._apply_laplace_smoothing(test_matrix[:2], alpha=1.0)
        smoothed_alpha_2 = self.predictor._apply_laplace_smoothing(test_matrix[:2], alpha=2.0)
        
        # alpha值越大，平滑效果越强（概率分布越均匀）
        # 第一行：原来有数据的行，alpha越大，原有高概率会被平滑得更均匀
        self.assertLess(smoothed_alpha_2[0, 0], smoothed_alpha_1[0, 0])
        
        # 第二行：原来全零的行，应该变成均匀分布
        for j in range(10):
            self.assertAlmostEqual(smoothed_alpha_1[1, j], 0.1, places=5)
            self.assertAlmostEqual(smoothed_alpha_2[1, j], 0.1, places=5)
    
    def test_data_window_utilization(self):
        """测试数据窗口利用率"""
        # 创建大量测试数据
        large_data = []
        for i in range(2000):
            large_data.append({
                'numbers': f"{i%10}{(i+1)%10}{(i+2)%10}",
                'period': f"2024{i:04d}",
                'date': f"2024-{(i//30)+1:02d}-{(i%30)+1:02d}"
            })
        
        # 测试不同窗口大小的效果
        matrix_small = self.predictor._build_transition_matrix(large_data, window_size=100)
        matrix_large = self.predictor._build_transition_matrix(large_data, window_size=1000)
        
        # 大窗口应该提供更稳定的估计（方差更小）
        variance_small = np.var(matrix_small)
        variance_large = np.var(matrix_large)
        
        # 验证矩阵都是有效的概率矩阵
        self.assertTrue(np.all(matrix_small >= 0))
        self.assertTrue(np.all(matrix_large >= 0))
        
        # 验证每行和为1
        for i in range(10):
            self.assertAlmostEqual(matrix_small[i].sum(), 1.0, places=5)
            self.assertAlmostEqual(matrix_large[i].sum(), 1.0, places=5)


if __name__ == '__main__':
    unittest.main()
