# 阶段3：代码修复和配置

**阶段状态**: 未开始  
**预计耗时**: 45分钟  
**依赖关系**: 阶段2完成  

## 📋 阶段目标

修复前端和后端代码，配置WebSocket连接逻辑，创建测试脚本。

## 🔧 具体任务

### 3.1 修复主界面WebSocket状态

**文件路径**: `src/ui/main.py`  
**涉及方法**: `show_connection_status()` 调用  
**修改行数**: 第902行附近  

**当前代码**:
```python
# 第902行附近
show_connection_status(websocket_connected=False)
```

**修改为**:
```python
# 动态检查WebSocket连接状态
websocket_status = check_websocket_connection()
show_connection_status(websocket_connected=websocket_status)
```

**新增函数**:
```python
def check_websocket_connection():
    """检查WebSocket连接状态"""
    try:
        import asyncio
        import websockets
        
        async def test_connection():
            try:
                uri = "ws://127.0.0.1:8888/ws/bug-detection"
                async with websockets.connect(uri, timeout=2) as websocket:
                    return True
            except:
                return False
        
        # 在Streamlit中运行异步检查
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(test_connection())
        except:
            return asyncio.run(test_connection())
    except Exception as e:
        st.warning(f"WebSocket连接检查失败: {e}")
        return False
```

**预期结果**: 
- 动态检查WebSocket连接状态
- 根据实际连接情况显示状态
- 连接失败时显示警告信息

**依赖库**: 
- websockets
- asyncio
- streamlit

### 3.2 更新连接状态显示

**文件路径**: `src/ui/components/fallback_manager.py`  
**涉及方法**: `show_connection_status()`  
**修改行数**: 第190行附近  

**当前代码**:
```python
# 第190行附近
if websocket_connected:
    st.success("🔗 WebSocket: 已连接")
else:
    st.warning("🔗 WebSocket: 已断开")
```

**修改为**:
```python
if websocket_connected:
    st.success("🔗 WebSocket: 已连接")
    st.info("✨ 实时功能已启用：Bug检测、训练监控、状态推送")
else:
    st.warning("🔗 WebSocket: 已断开")
    st.info("🔄 已启用降级模式：使用API轮询，功能正常")
    
    # 添加重连按钮
    if st.button("🔄 尝试重新连接", key="reconnect_websocket"):
        st.rerun()
```

**预期结果**: 
- 更详细的连接状态信息
- 连接成功时显示可用功能
- 连接失败时显示降级模式说明
- 提供手动重连功能

**依赖库**: 
- streamlit

### 3.3 验证API服务配置

**文件路径**: `src/api/production_main.py`  
**涉及部分**: WebSocket路由注册  
**检查行数**: 第1400-1600行  

**检查内容**:
1. WebSocket路由是否正确注册
2. WebSocket管理器是否正确初始化
3. 事件总线是否正确配置

**验证代码**:
```python
# 检查WebSocket路由注册
@app.websocket("/ws/bug-detection")
async def websocket_bug_detection(websocket: WebSocket):
    # 确认此路由存在且正确实现
    pass

@app.websocket("/ws/realtime-stats") 
async def websocket_realtime_stats(websocket: WebSocket):
    # 确认此路由存在且正确实现
    pass

# 检查WebSocket管理器初始化
if websocket_manager:
    logger.info("✅ WebSocket管理器已初始化")
else:
    logger.warning("⚠️ WebSocket管理器未初始化")
```

**预期结果**: 
- 所有WebSocket路由正确注册
- WebSocket管理器正确初始化
- 事件总线配置正确

**依赖库**: 
- fastapi
- websockets

### 3.4 创建连接测试脚本

**文件路径**: `test_websocket_connection.py` (新建)  
**文件内容**: 完整的WebSocket连接测试脚本

**脚本内容**:
```python
#!/usr/bin/env python3
"""
WebSocket连接测试脚本
用于验证WebSocket服务是否正常工作
"""

import asyncio
import json
import logging
import time
import sys
from typing import Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

try:
    import websockets
    HAS_WEBSOCKETS = True
except ImportError:
    HAS_WEBSOCKETS = False
    logger.error("websockets库未安装")

class WebSocketTester:
    """WebSocket连接测试器"""
    
    def __init__(self, base_url: str = "ws://127.0.0.1:8888"):
        self.base_url = base_url
        self.test_results = {}
    
    async def test_endpoint(self, endpoint: str, timeout: float = 5.0) -> Dict[str, Any]:
        """测试单个WebSocket端点"""
        uri = f"{self.base_url}{endpoint}"
        result = {
            'endpoint': endpoint,
            'uri': uri,
            'status': 'unknown',
            'error': None,
            'response_time': None
        }
        
        start_time = time.time()
        try:
            async with websockets.connect(uri, timeout=timeout) as websocket:
                # 发送测试消息
                test_message = {"type": "ping", "timestamp": time.time()}
                await websocket.send(json.dumps(test_message))
                
                # 等待响应
                response = await asyncio.wait_for(
                    websocket.recv(), 
                    timeout=timeout
                )
                
                result['status'] = 'success'
                result['response_time'] = time.time() - start_time
                logger.info(f"✅ {endpoint} 连接成功 ({result['response_time']:.2f}s)")
                
        except Exception as e:
            result['status'] = 'failed'
            result['error'] = str(e)
            result['response_time'] = time.time() - start_time
            logger.error(f"❌ {endpoint} 连接失败: {e}")
        
        return result
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有WebSocket端点测试"""
        if not HAS_WEBSOCKETS:
            return {'error': 'websockets库未安装'}
        
        endpoints = [
            '/ws/bug-detection',
            '/ws/realtime-stats',
            '/ws/training/test-model'
        ]
        
        logger.info("🚀 开始WebSocket连接测试...")
        
        results = {}
        for endpoint in endpoints:
            results[endpoint] = await self.test_endpoint(endpoint)
        
        # 生成测试报告
        success_count = sum(1 for r in results.values() if r['status'] == 'success')
        total_count = len(results)
        
        report = {
            'timestamp': time.time(),
            'total_tests': total_count,
            'successful_tests': success_count,
            'success_rate': (success_count / total_count) * 100,
            'results': results
        }
        
        logger.info(f"📊 测试完成: {success_count}/{total_count} 成功 ({report['success_rate']:.1f}%)")
        
        return report

async def main():
    """主函数"""
    tester = WebSocketTester()
    report = await tester.run_all_tests()
    
    # 保存测试报告
    with open(f'websocket_test_report_{int(time.time())}.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    # 返回退出码
    if report.get('success_rate', 0) == 100:
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
```

**预期结果**: 
- 创建完整的测试脚本
- 可以测试所有WebSocket端点
- 生成详细的测试报告
- 提供明确的成功/失败状态

**依赖库**: 
- websockets
- asyncio
- json
- logging

## ✅ 验收标准

- [ ] src/ui/main.py WebSocket状态检查已修复
- [ ] src/ui/components/fallback_manager.py 状态显示已更新
- [ ] src/api/production_main.py WebSocket配置已验证
- [ ] test_websocket_connection.py 脚本已创建
- [ ] 所有代码修改语法正确
- [ ] 新增函数逻辑正确
- [ ] 测试脚本可以正常运行

## 🚨 注意事项

1. **异步处理**: 在Streamlit中处理异步WebSocket连接需要特别注意
2. **错误处理**: 确保所有网络操作都有适当的错误处理
3. **超时设置**: 设置合理的连接超时时间
4. **资源清理**: 确保WebSocket连接正确关闭
5. **用户体验**: 提供清晰的状态反馈

## 📝 执行记录

### 执行时间
- 开始时间: ___________
- 结束时间: ___________
- 实际耗时: ___________

### 执行结果
- [ ] 任务3.1完成
- [ ] 任务3.2完成
- [ ] 任务3.3完成
- [ ] 任务3.4完成
- [ ] 阶段验收通过

### 问题记录
- 遇到的问题: ___________
- 解决方案: ___________
- 经验教训: ___________

## 🔄 下一阶段

完成本阶段后，继续执行 [阶段4：服务启动和连接测试](./阶段4-服务启动和连接测试.md)
