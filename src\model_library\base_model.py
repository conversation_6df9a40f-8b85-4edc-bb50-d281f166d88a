"""
模型库基础抽象类和数据结构定义
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from enum import Enum


class ModelType(Enum):
    """模型类型枚举"""
    MARKOV = "markov"
    FUSION = "fusion"
    TREND = "trend"
    DEEP_LEARNING = "deep_learning"
    TIME_SERIES = "time_series"
    MACHINE_LEARNING = "machine_learning"
    ENSEMBLE = "ensemble"


class ModelStatus(Enum):
    """模型状态枚举"""
    NOT_READY = "not_ready"
    READY = "ready"
    TRAINING = "training"
    TRAINED = "trained"
    ERROR = "error"


@dataclass
class ModelInfo:
    """模型基本信息"""
    model_id: str
    name: str
    description: str
    model_type: ModelType
    version: str = "1.0.0"
    author: str = "Augment Agent"
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    data_requirements: Dict[str, Any] = field(default_factory=dict)
    feature_engineering: Dict[str, Any] = field(default_factory=dict)
    parameters: Dict[str, Any] = field(default_factory=dict)
    is_active: bool = True


@dataclass
class ModelStatusInfo:
    """模型状态信息"""
    model_id: str
    status: ModelStatus
    data_ready: bool = False
    features_ready: bool = False
    trained: bool = False
    up_to_date: bool = False
    training_data_size: int = 0
    last_training_time: Optional[datetime] = None
    last_check_time: datetime = field(default_factory=datetime.now)
    error_message: Optional[str] = None


@dataclass
class PredictionResult:
    """预测结果"""
    model_id: str
    prediction_time: datetime
    target_period: int
    百位: Dict[str, float]
    十位: Dict[str, float] 
    个位: Dict[str, float]
    和值: Dict[str, float] = field(default_factory=dict)
    跨度: Dict[str, float] = field(default_factory=dict)
    confidence: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TrainingResult:
    """训练结果"""
    model_id: str
    training_time: datetime
    success: bool
    training_data_size: int
    training_duration: float  # 秒
    metrics: Dict[str, float] = field(default_factory=dict)
    error_message: Optional[str] = None


@dataclass
class ValidationResult:
    """验证结果"""
    model_id: str
    validation_time: datetime
    validation_type: str  # "cross_validation", "train_test_split", etc.
    metrics: Dict[str, float]
    success: bool
    error_message: Optional[str] = None


class BaseModel(ABC):
    """模型基础抽象类
    
    所有模型都必须继承此类并实现抽象方法
    """
    
    def __init__(self, model_id: str, name: str, description: str, model_type: ModelType):
        self.model_id = model_id
        self.name = name
        self.description = description
        self.model_type = model_type
        self._status = ModelStatus.NOT_READY
        self._parameters = {}
        self._training_data = None
        
    @abstractmethod
    def get_info(self) -> ModelInfo:
        """获取模型基本信息"""
        pass
    
    @abstractmethod
    def get_status(self) -> ModelStatusInfo:
        """获取模型状态信息"""
        pass
    
    @abstractmethod
    def train(self, data: List[Dict[str, Any]]) -> TrainingResult:
        """训练模型
        
        Args:
            data: 训练数据列表，每个元素包含期号、开奖号码等信息
            
        Returns:
            TrainingResult: 训练结果
        """
        pass
    
    @abstractmethod
    def predict(self, history: List[Dict[str, Any]], top_n: int = 3) -> PredictionResult:
        """执行预测
        
        Args:
            history: 历史数据列表
            top_n: 返回前N个预测结果
            
        Returns:
            PredictionResult: 预测结果
        """
        pass
    
    @abstractmethod
    def get_parameters(self) -> Dict[str, Any]:
        """获取模型参数"""
        pass
    
    @abstractmethod
    def set_parameters(self, parameters: Dict[str, Any]) -> bool:
        """设置模型参数
        
        Args:
            parameters: 参数字典
            
        Returns:
            bool: 设置是否成功
        """
        pass
    
    @abstractmethod
    def validate(self, test_data: List[Dict[str, Any]]) -> ValidationResult:
        """验证模型
        
        Args:
            test_data: 测试数据
            
        Returns:
            ValidationResult: 验证结果
        """
        pass
    
    @abstractmethod
    def calculate_confidence(self, prediction: PredictionResult) -> float:
        """计算预测置信度
        
        Args:
            prediction: 预测结果
            
        Returns:
            float: 置信度分数 (0-1)
        """
        pass
    
    # 通用方法
    def is_ready(self) -> bool:
        """检查模型是否就绪"""
        status_info = self.get_status()
        return (status_info.data_ready and 
                status_info.features_ready and 
                status_info.trained)
    
    def get_required_data_size(self) -> int:
        """获取模型所需的最小数据量"""
        return 100  # 默认值，子类可以重写
    
    def preprocess_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """数据预处理（可选重写）"""
        return data
    
    def postprocess_prediction(self, prediction: PredictionResult) -> PredictionResult:
        """预测结果后处理（可选重写）"""
        return prediction
