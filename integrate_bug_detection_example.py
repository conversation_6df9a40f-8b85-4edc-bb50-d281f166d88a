#!/usr/bin/env python3
"""
Bug检测系统集成示例
创建日期: 2025年7月24日
用途: 演示如何在现有福彩3D预测系统中集成Bug检测功能
"""

import streamlit as st
import sys
import os
import pandas as pd
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '.')

# 导入Bug检测组件
try:
    from src.bug_detection.monitoring.js_monitor import inject_js_monitor, show_js_monitor_debug
    from src.bug_detection.core.database_manager import DatabaseManager
    from src.bug_detection.feedback.bug_reporter import IntelligentBugReporter
except ImportError as e:
    st.error(f"Bug检测组件导入失败: {e}")
    st.info("请确保Bug检测系统已正确安装")

def main():
    """主函数 - 演示集成后的效果"""
    st.set_page_config(
        page_title="福彩3D预测系统 - 集成Bug检测",
        page_icon="🎲",
        layout="wide"
    )
    
    # 🔍 集成Bug检测监控 - 这是关键的一行代码！
    inject_js_monitor("lottery_prediction_system")
    
    st.title("🎲 福彩3D预测系统 - Bug检测集成演示")
    st.markdown("---")
    
    # 侧边栏 - 页面选择
    page = st.sidebar.selectbox(
        "选择功能页面",
        [
            "🏠 数据概览",
            "📊 频率分析", 
            "🔮 预测分析",
            "🧠 智能融合",
            "📈 趋势分析",
            "🔧 系统监控",
            "🐛 Bug检测状态"
        ]
    )
    
    # Bug检测状态指示器
    with st.sidebar:
        st.markdown("### 🔍 Bug检测状态")
        st.success("✅ JavaScript监控: 活跃")
        st.success("✅ 错误捕获: 就绪")
        st.success("✅ 自动报告: 启用")
        
        if st.button("🧪 触发测试错误"):
            trigger_test_error()
    
    # 根据选择显示不同页面
    if page == "🏠 数据概览":
        show_data_overview()
    elif page == "📊 频率分析":
        show_frequency_analysis()
    elif page == "🔮 预测分析":
        show_prediction_analysis()
    elif page == "🧠 智能融合":
        show_intelligent_fusion()
    elif page == "📈 趋势分析":
        show_trend_analysis()
    elif page == "🔧 系统监控":
        show_system_monitoring()
    elif page == "🐛 Bug检测状态":
        show_bug_detection_status()

def show_data_overview():
    """数据概览页面 - 集成了Bug检测"""
    st.header("🏠 数据概览")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("总期数", "8,343", "↑ 1")
    
    with col2:
        st.metric("最新期号", "2025186", "")
    
    with col3:
        st.metric("数据完整性", "100%", "")
    
    with col4:
        st.metric("系统状态", "正常", "🟢")
    
    # 模拟数据表格
    st.subheader("📋 最新开奖数据")
    
    sample_data = pd.DataFrame({
        '期号': ['2025186', '2025185', '2025184', '2025183', '2025182'],
        '开奖号码': ['1 2 3', '4 5 6', '7 8 9', '0 1 2', '3 4 5'],
        '和值': [6, 15, 24, 3, 12],
        '销售额': ['1,234,567', '1,345,678', '1,456,789', '1,567,890', '1,678,901']
    })
    
    st.dataframe(sample_data, use_container_width=True)
    
    # 故意的Bug测试按钮
    if st.button("🧪 测试数据加载错误", key="data_load_error"):
        # 这会触发一个JavaScript错误
        error_script = """
        <script>
        setTimeout(() => {
            // 模拟数据加载错误
            const data = null;
            console.log(data.length); // 这会触发TypeError
        }, 500);
        </script>
        """
        st.components.v1.html(error_script, height=0)
        st.error("💥 数据加载错误已触发！Bug检测系统会自动捕获此错误。")

def show_frequency_analysis():
    """频率分析页面"""
    st.header("📊 频率分析")
    
    # 模拟频率数据
    freq_data = pd.DataFrame({
        '号码': list(range(10)),
        '出现次数': [834, 823, 845, 812, 856, 867, 798, 889, 876, 801],
        '频率(%)': [10.0, 9.9, 10.1, 9.7, 10.3, 10.4, 9.6, 10.7, 10.5, 9.6]
    })
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📈 号码频率统计")
        st.dataframe(freq_data, use_container_width=True)
    
    with col2:
        st.subheader("📊 频率分布图")
        st.bar_chart(freq_data.set_index('号码')['出现次数'])
    
    # 测试API错误
    if st.button("🧪 测试API调用错误", key="api_error"):
        try:
            # 模拟API调用失败
            import requests
            response = requests.get("http://nonexistent-api.com/frequency", timeout=1)
        except Exception as e:
            st.error(f"💥 API调用失败: {str(e)}")
            
            # 自动生成Bug报告
            try:
                db_manager = DatabaseManager()
                bug_reporter = IntelligentBugReporter(db_manager)
                
                error_data = {
                    'type': 'api_error',
                    'message': str(e),
                    'severity': 'high',
                    'page_name': 'frequency_analysis',
                    'source': 'api_call'
                }
                
                bug_report = bug_reporter.generate_bug_report(error_data)
                st.success(f"✅ Bug报告已自动生成: {bug_report['id']}")
                
            except Exception as report_error:
                st.warning(f"Bug报告生成失败: {report_error}")

def show_prediction_analysis():
    """预测分析页面"""
    st.header("🔮 预测分析")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("🎯 预测配置")
        
        model_type = st.selectbox(
            "选择预测模型",
            ["统计学模型", "机器学习模型", "深度学习模型", "混合模型"]
        )
        
        confidence_level = st.slider("置信度", 0.5, 0.99, 0.85)
        
        if st.button("🚀 执行预测", key="run_prediction"):
            with st.spinner("正在执行预测分析..."):
                # 模拟预测过程
                import time
                time.sleep(2)
                
                # 模拟预测结果
                prediction_result = {
                    "预测号码": "1 2 3",
                    "置信度": f"{confidence_level:.1%}",
                    "预测模型": model_type,
                    "计算时间": "1.23秒"
                }
                
                st.session_state.prediction_result = prediction_result
                st.success("✅ 预测完成！")
    
    with col2:
        st.subheader("📊 预测结果")
        
        if 'prediction_result' in st.session_state:
            result = st.session_state.prediction_result
            
            for key, value in result.items():
                st.metric(key, value)
            
            # 预测准确率历史
            st.subheader("📈 历史准确率")
            accuracy_data = pd.DataFrame({
                '日期': pd.date_range('2025-07-01', periods=7),
                '准确率': [0.82, 0.85, 0.78, 0.88, 0.83, 0.86, 0.84]
            })
            st.line_chart(accuracy_data.set_index('日期'))
        else:
            st.info("👆 请执行预测来查看结果")
    
    # 测试内存错误
    if st.button("🧪 测试内存使用错误", key="memory_error"):
        memory_error_script = """
        <script>
        setTimeout(() => {
            // 模拟内存使用问题
            let bigArray = [];
            for (let i = 0; i < 100000; i++) {
                bigArray.push(new Array(1000).fill('memory test'));
            }
            console.warn('内存使用测试：创建了大量数组');
        }, 500);
        </script>
        """
        st.components.v1.html(memory_error_script, height=0)
        st.warning("⚠️ 内存使用测试已执行！")

def show_intelligent_fusion():
    """智能融合页面"""
    st.header("🧠 智能融合")
    
    st.info("💡 智能融合功能将多个预测模型的结果进行融合，提供更准确的预测。")
    
    # 模型权重配置
    st.subheader("⚖️ 模型权重配置")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        weight1 = st.slider("统计学模型权重", 0.0, 1.0, 0.3)
    
    with col2:
        weight2 = st.slider("机器学习模型权重", 0.0, 1.0, 0.4)
    
    with col3:
        weight3 = st.slider("深度学习模型权重", 0.0, 1.0, 0.3)
    
    total_weight = weight1 + weight2 + weight3
    
    if abs(total_weight - 1.0) > 0.01:
        st.warning(f"⚠️ 权重总和应为1.0，当前为{total_weight:.2f}")
    else:
        st.success("✅ 权重配置正确")
    
    # 融合预测
    if st.button("🔮 执行融合预测", key="fusion_prediction"):
        if abs(total_weight - 1.0) <= 0.01:
            with st.spinner("正在执行智能融合预测..."):
                import time
                time.sleep(3)
                
                st.success("🎯 融合预测完成！")
                
                # 显示融合结果
                fusion_result = pd.DataFrame({
                    '模型': ['统计学模型', '机器学习模型', '深度学习模型', '融合结果'],
                    '预测号码': ['1 2 3', '2 3 4', '1 3 4', '1 2 3'],
                    '置信度': ['0.75', '0.82', '0.78', '0.85'],
                    '权重': [f'{weight1:.1f}', f'{weight2:.1f}', f'{weight3:.1f}', '1.0']
                })
                
                st.dataframe(fusion_result, use_container_width=True)
        else:
            st.error("❌ 请先调整权重配置")

def show_trend_analysis():
    """趋势分析页面"""
    st.header("📈 趋势分析")
    
    # 趋势图表
    trend_data = pd.DataFrame({
        '期号': [f'2025{180+i}' for i in range(7)],
        '和值': [12, 15, 8, 22, 18, 11, 16],
        '奇偶比': [2.0, 1.5, 0.5, 3.0, 2.5, 1.0, 2.0]
    })
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📊 和值趋势")
        st.line_chart(trend_data.set_index('期号')['和值'])
    
    with col2:
        st.subheader("📊 奇偶比趋势")
        st.line_chart(trend_data.set_index('期号')['奇偶比'])
    
    # 趋势预测
    st.subheader("🔮 趋势预测")
    
    if st.button("📈 分析趋势", key="analyze_trend"):
        with st.spinner("正在分析趋势..."):
            import time
            time.sleep(2)
            
            st.success("✅ 趋势分析完成！")
            
            trend_insights = [
                "📈 和值呈现周期性波动，当前处于上升阶段",
                "🔄 奇偶比显示偶数号码出现频率增加",
                "📊 建议关注中等和值范围(12-18)",
                "🎯 下期预测倾向于奇偶平衡组合"
            ]
            
            for insight in trend_insights:
                st.info(insight)

def show_system_monitoring():
    """系统监控页面"""
    st.header("🔧 系统监控")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("系统状态", "正常", "🟢")
        st.metric("CPU使用率", "15%", "↓ 2%")
    
    with col2:
        st.metric("内存使用率", "32%", "↑ 1%")
        st.metric("数据库连接", "正常", "🟢")
    
    with col3:
        st.metric("API响应时间", "45ms", "↓ 5ms")
        st.metric("错误率", "0.05%", "↓ 0.02%")
    
    # 系统日志
    st.subheader("📋 系统日志")
    
    log_data = pd.DataFrame({
        '时间': [
            '2025-07-24 12:45:30',
            '2025-07-24 12:44:15',
            '2025-07-24 12:43:02',
            '2025-07-24 12:42:18',
            '2025-07-24 12:41:45'
        ],
        '级别': ['INFO', 'INFO', 'WARN', 'INFO', 'ERROR'],
        '消息': [
            '预测分析完成',
            '数据更新成功',
            'API响应时间较长',
            '用户登录成功',
            'JavaScript错误已捕获'
        ]
    })
    
    st.dataframe(log_data, use_container_width=True)

def show_bug_detection_status():
    """Bug检测状态页面"""
    st.header("🐛 Bug检测系统状态")
    
    # 显示JavaScript监控调试信息
    show_js_monitor_debug()
    
    # Bug统计
    st.subheader("📊 Bug统计")
    
    try:
        db_manager = DatabaseManager()
        bug_reports = db_manager.get_bug_reports(limit=10)
        
        if bug_reports:
            st.success(f"✅ 发现 {len(bug_reports)} 个Bug报告")
            
            # 显示Bug列表
            bug_df = pd.DataFrame([
                {
                    'Bug ID': bug.get('id', 'N/A')[:12] + '...',
                    '类型': bug.get('error_type', 'N/A'),
                    '严重程度': bug.get('severity', 'N/A'),
                    '状态': bug.get('status', 'N/A'),
                    '创建时间': bug.get('created_at', 'N/A')[:19]
                }
                for bug in bug_reports[:5]
            ])
            
            st.dataframe(bug_df, use_container_width=True)
        else:
            st.info("📋 暂无Bug报告")
            
    except Exception as e:
        st.error(f"❌ 获取Bug报告失败: {e}")
    
    # 性能监控
    st.subheader("📈 性能监控")
    
    try:
        performance_summary = db_manager.get_performance_summary()
        
        if performance_summary:
            perf_df = pd.DataFrame([
                {
                    'API端点': endpoint,
                    '平均响应时间(s)': f"{data['avg_time']:.3f}",
                    '最大响应时间(s)': f"{data['max_time']:.3f}",
                    '请求次数': data['count']
                }
                for endpoint, data in performance_summary.items()
            ])
            
            st.dataframe(perf_df, use_container_width=True)
        else:
            st.info("📊 暂无性能数据")
            
    except Exception as e:
        st.error(f"❌ 获取性能数据失败: {e}")

def trigger_test_error():
    """触发测试错误"""
    test_error_script = """
    <script>
    setTimeout(() => {
        throw new Error('这是一个测试错误 - Bug检测系统演示');
    }, 500);
    </script>
    """
    st.components.v1.html(test_error_script, height=0)
    st.success("✅ 测试错误已触发！")

if __name__ == "__main__":
    main()
