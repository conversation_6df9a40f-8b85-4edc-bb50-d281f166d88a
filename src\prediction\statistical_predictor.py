#!/usr/bin/env python3
"""
统计学预测器

基于统计分析的福彩3D预测方法
"""

from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import logging
from collections import Counter, defaultdict
import random

from .base_predictor import BasePredictor

logger = logging.getLogger(__name__)

class StatisticalPredictor(BasePredictor):
    """统计学预测器"""
    
    def __init__(self):
        super().__init__(
            name="统计学预测器",
            description="基于历史数据的统计分析进行预测，包括频率分析、热号冷号、遗漏分析等方法"
        )
        
        # 统计数据
        self.number_frequency = {}
        self.position_frequency = [{}, {}, {}]  # 三个位置的数字频率
        self.sum_frequency = {}
        self.pattern_stats = {}
        self.missing_stats = {}
        
        # 预测参数
        self.hot_threshold = 0.15  # 热号阈值
        self.cold_threshold = 0.05  # 冷号阈值
        self.prediction_count = 10  # 预测号码数量
    
    def train(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        训练统计模型
        
        Args:
            data: 历史数据
            
        Returns:
            训练结果
        """
        logger.info(f"开始训练统计学预测器，数据量: {len(data)}")
        
        if not self.validate_data(data):
            return {"success": False, "error": "数据验证失败"}
        
        try:
            # 重置统计数据
            self._reset_stats()
            
            # 计算各种统计指标
            self._calculate_frequency_stats(data)
            self._calculate_position_stats(data)
            self._calculate_sum_stats(data)
            self._calculate_pattern_stats(data)
            self._calculate_missing_stats(data)
            
            # 标记为已训练
            self.is_trained = True
            self.last_training_time = datetime.now()
            
            training_result = {
                "success": True,
                "training_time": self.last_training_time.isoformat(),
                "data_count": len(data),
                "statistics": {
                    "number_frequency": len(self.number_frequency),
                    "hot_numbers": self._get_hot_numbers(),
                    "cold_numbers": self._get_cold_numbers(),
                    "most_common_sum": self._get_most_common_sum(),
                    "pattern_count": len(self.pattern_stats)
                }
            }
            
            logger.info("统计学预测器训练完成")
            return training_result
            
        except Exception as e:
            logger.error(f"训练过程中发生错误: {e}")
            return {"success": False, "error": str(e)}
    
    def predict(self, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        执行统计学预测
        
        Args:
            context: 预测上下文
            
        Returns:
            预测结果
        """
        if not self.is_trained:
            return {
                "success": False,
                "error": "模型尚未训练",
                "predictions": [],
                "confidence": 0.0
            }
        
        try:
            # 生成多种预测方法的结果
            predictions = []
            
            # 方法1：频率分析预测
            freq_predictions = self._predict_by_frequency()
            predictions.extend(freq_predictions)
            
            # 方法2：热号预测
            hot_predictions = self._predict_by_hot_numbers()
            predictions.extend(hot_predictions)
            
            # 方法3：冷号预测
            cold_predictions = self._predict_by_cold_numbers()
            predictions.extend(cold_predictions)
            
            # 方法4：和值预测
            sum_predictions = self._predict_by_sum_analysis()
            predictions.extend(sum_predictions)
            
            # 方法5：遗漏分析预测
            missing_predictions = self._predict_by_missing_analysis()
            predictions.extend(missing_predictions)
            
            # 去重并限制数量
            unique_predictions = list(set(predictions))
            final_predictions = unique_predictions[:self.prediction_count]
            
            # 计算置信度
            confidence = self._calculate_confidence(final_predictions)
            
            result = {
                "success": True,
                "predictions": final_predictions,
                "confidence": confidence,
                "method": "统计学分析",
                "details": {
                    "frequency_based": len(freq_predictions),
                    "hot_number_based": len(hot_predictions),
                    "cold_number_based": len(cold_predictions),
                    "sum_based": len(sum_predictions),
                    "missing_based": len(missing_predictions),
                    "total_candidates": len(predictions),
                    "final_count": len(final_predictions)
                },
                "analysis": {
                    "hot_numbers": self._get_hot_numbers(),
                    "cold_numbers": self._get_cold_numbers(),
                    "recommended_sums": self._get_recommended_sums(),
                    "pattern_suggestions": self._get_pattern_suggestions()
                }
            }
            
            logger.info(f"统计学预测完成，生成 {len(final_predictions)} 个预测号码")
            return result
            
        except Exception as e:
            logger.error(f"预测过程中发生错误: {e}")
            return {
                "success": False,
                "error": str(e),
                "predictions": [],
                "confidence": 0.0
            }
    
    def _reset_stats(self):
        """重置统计数据"""
        self.number_frequency = {}
        self.position_frequency = [{}, {}, {}]
        self.sum_frequency = {}
        self.pattern_stats = {}
        self.missing_stats = {}
    
    def _calculate_frequency_stats(self, data: List[Dict[str, Any]]):
        """计算数字频率统计"""
        for record in data:
            numbers = record.get('numbers', '')
            if isinstance(numbers, str) and len(numbers) == 3 and numbers.isdigit():
                for digit in numbers:
                    self.number_frequency[digit] = self.number_frequency.get(digit, 0) + 1
    
    def _calculate_position_stats(self, data: List[Dict[str, Any]]):
        """计算位置统计"""
        for record in data:
            numbers = record.get('numbers', '')
            if isinstance(numbers, str) and len(numbers) == 3 and numbers.isdigit():
                for i, digit in enumerate(numbers):
                    self.position_frequency[i][digit] = self.position_frequency[i].get(digit, 0) + 1
    
    def _calculate_sum_stats(self, data: List[Dict[str, Any]]):
        """计算和值统计"""
        for record in data:
            numbers = record.get('numbers', '')
            if isinstance(numbers, str) and len(numbers) == 3 and numbers.isdigit():
                total = sum(int(digit) for digit in numbers)
                self.sum_frequency[total] = self.sum_frequency.get(total, 0) + 1
    
    def _calculate_pattern_stats(self, data: List[Dict[str, Any]]):
        """计算模式统计"""
        patterns = defaultdict(int)
        
        for record in data:
            numbers = record.get('numbers', '')
            if isinstance(numbers, str) and len(numbers) == 3 and numbers.isdigit():
                digits = [int(d) for d in numbers]
                
                # 分析各种模式
                if len(set(digits)) == 1:
                    patterns['豹子'] += 1
                elif len(set(digits)) == 2:
                    patterns['对子'] += 1
                else:
                    patterns['单选'] += 1
                
                # 奇偶模式
                odd_count = sum(1 for d in digits if d % 2 == 1)
                patterns[f'奇偶{odd_count}'] += 1
                
                # 大小模式
                big_count = sum(1 for d in digits if d >= 5)
                patterns[f'大小{big_count}'] += 1
        
        self.pattern_stats = dict(patterns)
    
    def _calculate_missing_stats(self, data: List[Dict[str, Any]]):
        """计算遗漏统计"""
        last_appearance = {}
        missing_counts = defaultdict(list)
        
        for i, record in enumerate(data):
            numbers = record.get('numbers', '')
            if isinstance(numbers, str) and len(numbers) == 3 and numbers.isdigit():
                appeared_digits = set(numbers)
                
                # 更新遗漏计数
                for digit in '0123456789':
                    if digit in appeared_digits:
                        if digit in last_appearance:
                            missing_count = i - last_appearance[digit] - 1
                            missing_counts[digit].append(missing_count)
                        last_appearance[digit] = i
        
        # 计算平均遗漏
        self.missing_stats = {}
        for digit, counts in missing_counts.items():
            if counts:
                self.missing_stats[digit] = {
                    'avg_missing': sum(counts) / len(counts),
                    'max_missing': max(counts),
                    'current_missing': len(data) - last_appearance.get(digit, 0) - 1
                }
    
    def _predict_by_frequency(self) -> List[str]:
        """基于频率分析预测"""
        if not self.number_frequency:
            return []
        
        # 获取频率最高的数字
        sorted_numbers = sorted(self.number_frequency.items(), key=lambda x: x[1], reverse=True)
        top_numbers = [num for num, _ in sorted_numbers[:6]]
        
        # 生成组合
        predictions = []
        for i in range(min(3, len(top_numbers))):
            for j in range(i+1, min(4, len(top_numbers))):
                for k in range(j+1, min(5, len(top_numbers))):
                    combo = ''.join(sorted([top_numbers[i], top_numbers[j], top_numbers[k]]))
                    predictions.append(combo)
        
        return predictions[:3]
    
    def _predict_by_hot_numbers(self) -> List[str]:
        """基于热号预测"""
        hot_numbers = self._get_hot_numbers()
        if len(hot_numbers) < 3:
            return []
        
        predictions = []
        # 生成热号组合
        for i in range(min(2, len(hot_numbers))):
            for j in range(i+1, min(3, len(hot_numbers))):
                for k in range(j+1, min(4, len(hot_numbers))):
                    combo = ''.join(sorted([hot_numbers[i], hot_numbers[j], hot_numbers[k]]))
                    predictions.append(combo)
        
        return predictions[:2]
    
    def _predict_by_cold_numbers(self) -> List[str]:
        """基于冷号预测"""
        cold_numbers = self._get_cold_numbers()
        if len(cold_numbers) < 3:
            return []
        
        predictions = []
        # 冷号回补预测
        for i in range(min(2, len(cold_numbers))):
            for j in range(i+1, min(3, len(cold_numbers))):
                for k in range(j+1, min(4, len(cold_numbers))):
                    combo = ''.join(sorted([cold_numbers[i], cold_numbers[j], cold_numbers[k]]))
                    predictions.append(combo)
        
        return predictions[:2]
    
    def _predict_by_sum_analysis(self) -> List[str]:
        """基于和值分析预测"""
        if not self.sum_frequency:
            return []
        
        # 获取最常见的和值
        common_sums = sorted(self.sum_frequency.items(), key=lambda x: x[1], reverse=True)[:3]
        
        predictions = []
        for target_sum, _ in common_sums:
            # 生成符合目标和值的组合
            combos = self._generate_combinations_for_sum(target_sum)
            predictions.extend(combos[:2])
        
        return predictions
    
    def _predict_by_missing_analysis(self) -> List[str]:
        """基于遗漏分析预测"""
        if not self.missing_stats:
            return []
        
        # 找出遗漏较久的数字
        overdue_numbers = []
        for digit, stats in self.missing_stats.items():
            if stats['current_missing'] > stats['avg_missing'] * 1.5:
                overdue_numbers.append(digit)
        
        if len(overdue_numbers) < 3:
            return []
        
        predictions = []
        # 生成遗漏号码组合
        for i in range(min(2, len(overdue_numbers))):
            for j in range(i+1, min(3, len(overdue_numbers))):
                for k in range(j+1, min(4, len(overdue_numbers))):
                    combo = ''.join(sorted([overdue_numbers[i], overdue_numbers[j], overdue_numbers[k]]))
                    predictions.append(combo)
        
        return predictions[:2]
    
    def _generate_combinations_for_sum(self, target_sum: int) -> List[str]:
        """生成指定和值的组合"""
        combinations = []
        
        for a in range(10):
            for b in range(10):
                for c in range(10):
                    if a + b + c == target_sum:
                        combo = ''.join(map(str, sorted([a, b, c])))
                        combinations.append(combo)
        
        return list(set(combinations))[:3]
    
    def _get_hot_numbers(self) -> List[str]:
        """获取热号"""
        if not self.number_frequency:
            return []
        
        total_count = sum(self.number_frequency.values())
        hot_numbers = []
        
        for digit, count in self.number_frequency.items():
            if count / total_count > self.hot_threshold:
                hot_numbers.append(digit)
        
        return sorted(hot_numbers, key=lambda x: self.number_frequency[x], reverse=True)
    
    def _get_cold_numbers(self) -> List[str]:
        """获取冷号"""
        if not self.number_frequency:
            return []
        
        total_count = sum(self.number_frequency.values())
        cold_numbers = []
        
        for digit, count in self.number_frequency.items():
            if count / total_count < self.cold_threshold:
                cold_numbers.append(digit)
        
        return sorted(cold_numbers, key=lambda x: self.number_frequency[x])
    
    def _get_most_common_sum(self) -> int:
        """获取最常见的和值"""
        if not self.sum_frequency:
            return 13  # 默认值
        
        return max(self.sum_frequency.items(), key=lambda x: x[1])[0]
    
    def _get_recommended_sums(self) -> List[int]:
        """获取推荐的和值"""
        if not self.sum_frequency:
            return [10, 11, 12, 13, 14, 15, 16]
        
        sorted_sums = sorted(self.sum_frequency.items(), key=lambda x: x[1], reverse=True)
        return [s for s, _ in sorted_sums[:5]]
    
    def _get_pattern_suggestions(self) -> Dict[str, Any]:
        """获取模式建议"""
        if not self.pattern_stats:
            return {}
        
        return {
            "most_common_pattern": max(self.pattern_stats.items(), key=lambda x: x[1])[0],
            "pattern_distribution": self.pattern_stats
        }
    
    def _calculate_confidence(self, predictions: List[str]) -> float:
        """计算预测置信度"""
        if not predictions:
            return 0.0
        
        # 基于统计数据的置信度计算
        base_confidence = 0.3  # 基础置信度
        
        # 根据数据量调整
        data_factor = min(len(self.number_frequency) / 1000, 0.3)
        
        # 根据预测数量调整
        count_factor = min(len(predictions) / 10, 0.2)
        
        total_confidence = base_confidence + data_factor + count_factor
        return min(total_confidence, 0.8)  # 最大不超过80%
