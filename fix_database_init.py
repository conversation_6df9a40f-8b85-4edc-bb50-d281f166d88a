#!/usr/bin/env python3
"""修复数据库初始化问题"""

import os
import sys
import sqlite3

# 添加src目录到路径
sys.path.append('src')

def check_and_fix_database():
    """检查并修复数据库问题"""
    print("🔍 检查数据库状态...")
    
    # 检查数据库文件
    db_paths = [
        "data/lottery.db",
        "data/model_library.db"
    ]
    
    for db_path in db_paths:
        abs_path = os.path.abspath(db_path)
        exists = os.path.exists(abs_path)
        print(f"📊 数据库: {db_path}")
        print(f"   绝对路径: {abs_path}")
        print(f"   存在: {exists}")
        
        if exists:
            try:
                with sqlite3.connect(abs_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = cursor.fetchall()
                    table_names = [t[0] for t in tables]
                    print(f"   表: {table_names}")
                    
                    if 'model_states' in table_names:
                        cursor.execute("SELECT COUNT(*) FROM model_states")
                        count = cursor.fetchone()[0]
                        print(f"   model_states记录数: {count}")
                        
                        cursor.execute("SELECT model_id, trained FROM model_states")
                        records = cursor.fetchall()
                        for model_id, trained in records:
                            print(f"     {model_id}: trained={trained}")
                    else:
                        print(f"   ⚠️ 缺少model_states表")
            except Exception as e:
                print(f"   数据库错误: {e}")
        print()

def force_init_model_library():
    """强制初始化模型库数据库"""
    print("🔧 强制初始化模型库数据库...")
    
    try:
        from model_library.model_registry import ModelRegistry
        
        # 强制创建新的ModelRegistry实例
        ModelRegistry._instance = None
        registry = ModelRegistry()
        
        print("✅ 模型库数据库初始化成功")
        
        # 检查表是否创建成功
        with sqlite3.connect("data/model_library.db") as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            table_names = [t[0] for t in tables]
            print(f"📊 创建的表: {table_names}")
            
            if 'model_states' in table_names:
                print("✅ model_states表创建成功")
            else:
                print("❌ model_states表创建失败")
                
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        import traceback
        traceback.print_exc()

def sync_model_states():
    """同步模型状态到数据库"""
    print("🔄 同步模型状态...")
    
    try:
        from model_library.model_registry import ModelRegistry
        
        registry = ModelRegistry()
        
        # 手动添加intelligent_fusion的状态
        with sqlite3.connect("data/model_library.db") as conn:
            cursor = conn.cursor()
            
            # 插入或更新intelligent_fusion状态
            cursor.execute('''
                INSERT OR REPLACE INTO model_states
                (model_id, status, data_ready, features_ready, trained,
                 up_to_date, training_data_size, last_training_time, last_check_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                'intelligent_fusion',
                'ready',
                True,
                True,
                True,  # 设置为已训练
                True,
                8346,
                '2025-07-19T10:00:00',
                '2025-07-19T10:00:00'
            ))
            
            conn.commit()
            print("✅ intelligent_fusion状态已同步")
            
            # 验证状态
            cursor.execute("SELECT model_id, trained FROM model_states WHERE model_id = 'intelligent_fusion'")
            result = cursor.fetchone()
            if result:
                print(f"📊 验证结果: {result[0]} trained={result[1]}")
            
    except Exception as e:
        print(f"❌ 同步失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("=" * 60)
    print("🔧 数据库修复工具")
    print("=" * 60)
    
    # 1. 检查当前状态
    check_and_fix_database()
    
    print("=" * 60)
    
    # 2. 强制初始化模型库
    force_init_model_library()
    
    print("=" * 60)
    
    # 3. 同步模型状态
    sync_model_states()
    
    print("=" * 60)
    
    # 4. 最终验证
    print("🔍 最终验证...")
    check_and_fix_database()
    
    print("✅ 数据库修复完成！")
