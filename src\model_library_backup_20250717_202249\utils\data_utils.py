"""
数据处理工具函数
"""

import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd


class LotteryDataLoader:
    """福彩3D数据加载器"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = Path(db_path)
    
    def load_all_records(self) -> List[Dict[str, Any]]:
        """加载所有彩票记录"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT period, date, number, trial_number, machine_number,
                       trial_machine_number, sales_amount, direct_bonus,
                       group3_bonus, group6_bonus
                FROM lottery_records
                ORDER BY period ASC
            ''')
            
            columns = ['period', 'date', 'number', 'trial_number', 'machine_number',
                      'trial_machine_number', 'sales_amount', 'direct_bonus',
                      'group3_bonus', 'group6_bonus']
            
            records = []
            for row in cursor.fetchall():
                record = dict(zip(columns, row))
                # 解析开奖号码
                if record['number']:
                    digits = [int(d) for d in record['number']]
                    record['百位'] = digits[0] if len(digits) > 0 else 0
                    record['十位'] = digits[1] if len(digits) > 1 else 0
                    record['个位'] = digits[2] if len(digits) > 2 else 0
                    record['和值'] = sum(digits)
                    record['跨度'] = max(digits) - min(digits) if digits else 0
                
                records.append(record)
            
            return records
    
    def load_recent_records(self, days: int = 30) -> List[Dict[str, Any]]:
        """加载最近N天的记录"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT period, date, number, trial_number, machine_number,
                       trial_machine_number, sales_amount, direct_bonus,
                       group3_bonus, group6_bonus
                FROM lottery_records
                WHERE date >= ?
                ORDER BY period ASC
            ''', (cutoff_date.strftime('%Y-%m-%d'),))
            
            columns = ['period', 'date', 'number', 'trial_number', 'machine_number',
                      'trial_machine_number', 'sales_amount', 'direct_bonus',
                      'group3_bonus', 'group6_bonus']
            
            records = []
            for row in cursor.fetchall():
                record = dict(zip(columns, row))
                # 解析开奖号码
                if record['number']:
                    digits = [int(d) for d in record['number']]
                    record['百位'] = digits[0] if len(digits) > 0 else 0
                    record['十位'] = digits[1] if len(digits) > 1 else 0
                    record['个位'] = digits[2] if len(digits) > 2 else 0
                    record['和值'] = sum(digits)
                    record['跨度'] = max(digits) - min(digits) if digits else 0
                
                records.append(record)
            
            return records
    
    def load_records_by_period_range(self, start_period: int, end_period: int) -> List[Dict[str, Any]]:
        """根据期号范围加载记录"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT period, date, number, trial_number, machine_number,
                       trial_machine_number, sales_amount, direct_bonus,
                       group3_bonus, group6_bonus
                FROM lottery_records
                WHERE period >= ? AND period <= ?
                ORDER BY period ASC
            ''', (start_period, end_period))
            
            columns = ['period', 'date', 'number', 'trial_number', 'machine_number',
                      'trial_machine_number', 'sales_amount', 'direct_bonus',
                      'group3_bonus', 'group6_bonus']
            
            records = []
            for row in cursor.fetchall():
                record = dict(zip(columns, row))
                # 解析开奖号码
                if record['number']:
                    digits = [int(d) for d in record['number']]
                    record['百位'] = digits[0] if len(digits) > 0 else 0
                    record['十位'] = digits[1] if len(digits) > 1 else 0
                    record['个位'] = digits[2] if len(digits) > 2 else 0
                    record['和值'] = sum(digits)
                    record['跨度'] = max(digits) - min(digits) if digits else 0
                
                records.append(record)
            
            return records
    
    def get_latest_period(self) -> Optional[int]:
        """获取最新期号"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT MAX(period) FROM lottery_records')
            result = cursor.fetchone()
            return result[0] if result and result[0] else None


class DataProcessor:
    """数据处理器"""
    
    @staticmethod
    def split_train_test(data: List[Dict[str, Any]], test_ratio: float = 0.2) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """分割训练集和测试集"""
        if not data:
            return [], []
        
        split_index = int(len(data) * (1 - test_ratio))
        train_data = data[:split_index]
        test_data = data[split_index:]
        
        return train_data, test_data
    
    @staticmethod
    def create_sliding_windows(data: List[Dict[str, Any]], window_size: int, step: int = 1) -> List[Tuple[List[Dict[str, Any]], Dict[str, Any]]]:
        """创建滑动窗口数据"""
        windows = []
        
        for i in range(0, len(data) - window_size, step):
            window_data = data[i:i + window_size]
            target = data[i + window_size]
            windows.append((window_data, target))
        
        return windows
    
    @staticmethod
    def normalize_data(data: List[Dict[str, Any]], fields: List[str]) -> List[Dict[str, Any]]:
        """数据标准化"""
        if not data or not fields:
            return data
        
        # 计算每个字段的均值和标准差
        stats = {}
        for field in fields:
            values = [record.get(field, 0) for record in data if field in record]
            if values:
                stats[field] = {
                    'mean': np.mean(values),
                    'std': np.std(values)
                }
        
        # 标准化数据
        normalized_data = []
        for record in data:
            normalized_record = record.copy()
            for field in fields:
                if field in record and field in stats:
                    mean = stats[field]['mean']
                    std = stats[field]['std']
                    if std > 0:
                        normalized_record[field] = (record[field] - mean) / std
            normalized_data.append(normalized_record)
        
        return normalized_data
    
    @staticmethod
    def filter_by_date_range(data: List[Dict[str, Any]], start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """按日期范围过滤数据"""
        filtered_data = []
        
        for record in data:
            record_date = record.get('date', '')
            if start_date <= record_date <= end_date:
                filtered_data.append(record)
        
        return filtered_data
    
    @staticmethod
    def remove_outliers(data: List[Dict[str, Any]], field: str, threshold: float = 3.0) -> List[Dict[str, Any]]:
        """移除异常值"""
        if not data or field not in data[0]:
            return data
        
        values = [record.get(field, 0) for record in data]
        mean = np.mean(values)
        std = np.std(values)
        
        filtered_data = []
        for record in data:
            value = record.get(field, 0)
            z_score = abs((value - mean) / std) if std > 0 else 0
            if z_score <= threshold:
                filtered_data.append(record)
        
        return filtered_data
    
    @staticmethod
    def calculate_statistics(data: List[Dict[str, Any]], field: str) -> Dict[str, float]:
        """计算字段统计信息"""
        if not data or field not in data[0]:
            return {}
        
        values = [record.get(field, 0) for record in data if field in record]
        
        if not values:
            return {}
        
        return {
            'count': len(values),
            'mean': np.mean(values),
            'std': np.std(values),
            'min': np.min(values),
            'max': np.max(values),
            'median': np.median(values),
            'q25': np.percentile(values, 25),
            'q75': np.percentile(values, 75)
        }
    
    @staticmethod
    def encode_categorical(data: List[Dict[str, Any]], field: str) -> Tuple[List[Dict[str, Any]], Dict[Any, int]]:
        """分类变量编码"""
        if not data or field not in data[0]:
            return data, {}
        
        # 获取所有唯一值
        unique_values = list(set(record.get(field) for record in data if field in record))
        unique_values.sort()
        
        # 创建编码映射
        encoding_map = {value: idx for idx, value in enumerate(unique_values)}
        
        # 编码数据
        encoded_data = []
        for record in data:
            encoded_record = record.copy()
            if field in record:
                encoded_record[f"{field}_encoded"] = encoding_map.get(record[field], -1)
            encoded_data.append(encoded_record)
        
        return encoded_data, encoding_map
    
    @staticmethod
    def create_lag_features(data: List[Dict[str, Any]], field: str, lags: List[int]) -> List[Dict[str, Any]]:
        """创建滞后特征"""
        if not data or field not in data[0]:
            return data
        
        enhanced_data = []
        
        for i, record in enumerate(data):
            enhanced_record = record.copy()
            
            for lag in lags:
                if i >= lag:
                    lag_value = data[i - lag].get(field, 0)
                    enhanced_record[f"{field}_lag_{lag}"] = lag_value
                else:
                    enhanced_record[f"{field}_lag_{lag}"] = 0
            
            enhanced_data.append(enhanced_record)
        
        return enhanced_data

    @staticmethod
    def validate_data_quality(data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证数据质量"""
        if not data:
            return {"valid": False, "error": "数据为空"}

        # 检查必要字段
        required_fields = ['period', 'date', 'number']
        missing_fields = []

        for field in required_fields:
            if field not in data[0]:
                missing_fields.append(field)

        if missing_fields:
            return {
                "valid": False,
                "error": f"缺少必要字段: {missing_fields}"
            }

        # 检查数据完整性
        incomplete_records = 0
        for record in data:
            if not all(record.get(field) for field in required_fields):
                incomplete_records += 1

        quality_score = 1.0 - (incomplete_records / len(data))

        return {
            "valid": quality_score >= 0.9,
            "quality_score": quality_score,
            "total_records": len(data),
            "incomplete_records": incomplete_records,
            "completeness": f"{quality_score:.2%}"
        }
