#!/usr/bin/env python3
"""
数据迁移工具
Data Migration Tool

将现有分散的预测记录迁移到统一系统
"""

import sqlite3
import json
import logging
import os
import glob
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path
import shutil

from ..core.unified_prediction_storage import UnifiedPredictionStorage, PredictionRecord


class DataMigrationTool:
    """数据迁移工具"""
    
    def __init__(self, target_storage: Optional[UnifiedPredictionStorage] = None):
        """
        初始化数据迁移工具
        
        Args:
            target_storage: 目标统一存储系统
        """
        self.target_storage = target_storage or UnifiedPredictionStorage()
        self.logger = logging.getLogger(__name__)
        
        # 迁移统计
        self.migration_stats = {
            'model_library_records': 0,
            'cache_records': 0,
            'validation_records': 0,
            'total_migrated': 0,
            'errors': 0,
            'duplicates_skipped': 0
        }
        
        # 数据源路径
        self.data_sources = {
            'model_library_db': 'data/model_library.db',
            'lottery_db': 'data/lottery.db',
            'cache_dir': 'data/cache',
            'validation_reports_dir': 'data/validation_reports'
        }
        
        # 备份目录
        self.backup_dir = f"data/migration_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    def migrate_all_data(self) -> Dict[str, Any]:
        """
        迁移所有数据源的数据
        
        Returns:
            迁移结果统计
        """
        self.logger.info("🚀 开始数据迁移...")
        
        try:
            # 创建备份
            self._create_backup()
            
            # 1. 从model_library.db迁移
            self.logger.info("📊 迁移model_library.db数据...")
            self._migrate_from_model_library()
            
            # 2. 从lottery.db迁移
            self.logger.info("🎯 迁移lottery.db数据...")
            self._migrate_from_lottery_db()
            
            # 3. 从cache文件迁移
            self.logger.info("💾 迁移cache文件数据...")
            self._migrate_from_cache_files()
            
            # 4. 从validation_reports迁移
            self.logger.info("📋 迁移validation_reports数据...")
            self._migrate_from_validation_reports()
            
            # 5. 数据一致性检查
            self.logger.info("🔍 执行数据一致性检查...")
            consistency_result = self._verify_migration_integrity()
            
            # 6. 生成迁移报告
            migration_report = self._generate_migration_report(consistency_result)
            
            self.logger.info("✅ 数据迁移完成！")
            return migration_report
            
        except Exception as e:
            self.logger.error(f"数据迁移失败: {e}")
            # 尝试回滚
            self._rollback_migration()
            raise
    
    def _create_backup(self):
        """创建数据备份"""
        try:
            os.makedirs(self.backup_dir, exist_ok=True)
            
            # 备份现有数据库文件
            for db_name, db_path in [
                ('model_library.db', self.data_sources['model_library_db']),
                ('lottery.db', self.data_sources['lottery_db'])
            ]:
                if os.path.exists(db_path):
                    backup_path = os.path.join(self.backup_dir, db_name)
                    shutil.copy2(db_path, backup_path)
                    self.logger.info(f"备份 {db_name} 到 {backup_path}")
            
            # 备份cache目录
            cache_dir = self.data_sources['cache_dir']
            if os.path.exists(cache_dir):
                backup_cache_dir = os.path.join(self.backup_dir, 'cache')
                shutil.copytree(cache_dir, backup_cache_dir)
                self.logger.info(f"备份cache目录到 {backup_cache_dir}")
            
            self.logger.info(f"数据备份完成: {self.backup_dir}")
            
        except Exception as e:
            self.logger.error(f"创建备份失败: {e}")
            raise
    
    def _migrate_from_model_library(self):
        """从model_library.db迁移数据"""
        db_path = self.data_sources['model_library_db']
        if not os.path.exists(db_path):
            self.logger.warning(f"model_library.db不存在: {db_path}")
            return
        
        try:
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                
                # 检查是否存在model_predictions表
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name='model_predictions'
                """)
                
                if not cursor.fetchone():
                    self.logger.warning("model_library.db中未找到model_predictions表")
                    return
                
                # 获取所有预测记录
                cursor.execute("""
                    SELECT model_id, prediction_time, target_period, 
                           prediction_result, confidence_score, actual_result, 
                           is_correct, metadata
                    FROM model_predictions
                    ORDER BY prediction_time
                """)
                
                records = cursor.fetchall()
                self.logger.info(f"从model_library.db获取到 {len(records)} 条记录")
                
                # 转换并保存记录
                for record in records:
                    try:
                        # 解析预测结果
                        prediction_result = json.loads(record[3]) if record[3] else {}
                        predicted_numbers = self._extract_predicted_numbers(prediction_result)
                        
                        # 创建预测记录
                        prediction_record = PredictionRecord(
                            period_number=str(record[2]),
                            model_name=record[0],
                            predicted_numbers=predicted_numbers,
                            confidence=record[4] or 0.0,
                            prediction_time=datetime.fromisoformat(record[1]) if record[1] else datetime.now(),
                            actual_numbers=record[5],
                            is_verified=record[6] is not None,
                            accuracy_score=1.0 if record[6] else 0.0,
                            metadata=json.loads(record[7]) if record[7] else None
                        )
                        
                        # 保存到统一存储
                        self.target_storage.save_prediction_record(prediction_record)
                        self.migration_stats['model_library_records'] += 1
                        
                    except Exception as e:
                        self.logger.error(f"迁移model_library记录失败: {e}")
                        self.migration_stats['errors'] += 1
                
        except Exception as e:
            self.logger.error(f"从model_library.db迁移失败: {e}")
            raise
    
    def _migrate_from_lottery_db(self):
        """从lottery.db迁移数据"""
        db_path = self.data_sources['lottery_db']
        if not os.path.exists(db_path):
            self.logger.warning(f"lottery.db不存在: {db_path}")
            return
        
        try:
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                
                # 查找预测相关的表
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name LIKE '%prediction%'
                """)
                
                prediction_tables = [row[0] for row in cursor.fetchall()]
                self.logger.info(f"在lottery.db中找到预测表: {prediction_tables}")
                
                # 迁移每个预测表的数据
                for table_name in prediction_tables:
                    self._migrate_table_data(conn, table_name)
                
        except Exception as e:
            self.logger.error(f"从lottery.db迁移失败: {e}")
    
    def _migrate_from_cache_files(self):
        """从cache文件迁移数据"""
        cache_dir = self.data_sources['cache_dir']
        if not os.path.exists(cache_dir):
            self.logger.warning(f"cache目录不存在: {cache_dir}")
            return
        
        try:
            # 迁移last_prediction.json
            last_prediction_file = os.path.join(cache_dir, 'last_prediction.json')
            if os.path.exists(last_prediction_file):
                self._migrate_last_prediction_cache(last_prediction_file)
            
            # 迁移其他cache文件
            cache_files = glob.glob(os.path.join(cache_dir, '*.json'))
            for cache_file in cache_files:
                if 'last_prediction' not in cache_file:
                    self._migrate_cache_file(cache_file)
                    
        except Exception as e:
            self.logger.error(f"从cache文件迁移失败: {e}")
    
    def _migrate_from_validation_reports(self):
        """从validation_reports迁移数据"""
        reports_dir = self.data_sources['validation_reports_dir']
        if not os.path.exists(reports_dir):
            self.logger.warning(f"validation_reports目录不存在: {reports_dir}")
            return
        
        try:
            # 查找所有验证报告文件
            report_files = glob.glob(os.path.join(reports_dir, '*.json'))
            self.logger.info(f"找到 {len(report_files)} 个验证报告文件")
            
            for report_file in report_files:
                self._migrate_validation_report(report_file)
                
        except Exception as e:
            self.logger.error(f"从validation_reports迁移失败: {e}")
    
    def _migrate_last_prediction_cache(self, cache_file: str):
        """迁移最后预测缓存"""
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            # 提取预测信息
            predicted_numbers = cache_data.get('numbers', '')
            confidence = cache_data.get('confidence', 0.0)
            timestamp_str = cache_data.get('timestamp', '')
            
            if predicted_numbers and timestamp_str:
                # 估算期号（基于时间戳）
                timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                period_number = self._estimate_period_from_timestamp(timestamp)
                
                # 创建预测记录
                prediction_record = PredictionRecord(
                    period_number=period_number,
                    model_name='intelligent_fusion',
                    predicted_numbers=predicted_numbers,
                    confidence=confidence,
                    prediction_time=timestamp,
                    metadata={'source': 'cache', 'cache_file': os.path.basename(cache_file)}
                )
                
                self.target_storage.save_prediction_record(prediction_record)
                self.migration_stats['cache_records'] += 1
                
        except Exception as e:
            self.logger.error(f"迁移cache文件失败 {cache_file}: {e}")
            self.migration_stats['errors'] += 1
    
    def _migrate_cache_file(self, cache_file: str):
        """迁移其他cache文件"""
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            # 根据文件名和内容判断数据类型
            filename = os.path.basename(cache_file)
            
            if 'fusion_state' in filename:
                # 融合状态文件，提取训练信息
                self._extract_fusion_state_info(cache_data, filename)
            elif 'quality_history' in filename:
                # 质量历史文件
                self._extract_quality_history_info(cache_data, filename)
                
        except Exception as e:
            self.logger.error(f"迁移cache文件失败 {cache_file}: {e}")
            self.migration_stats['errors'] += 1
    
    def _migrate_validation_report(self, report_file: str):
        """迁移验证报告"""
        try:
            with open(report_file, 'r', encoding='utf-8') as f:
                report_data = json.load(f)
            
            # 提取报告中的预测信息
            filename = os.path.basename(report_file)
            
            if 'diversity_report_fusion_prediction' in filename:
                self._extract_diversity_report_predictions(report_data, filename)
            elif 'markov_cross_validation' in filename:
                self._extract_markov_validation_predictions(report_data, filename)
                
        except Exception as e:
            self.logger.error(f"迁移验证报告失败 {report_file}: {e}")
            self.migration_stats['errors'] += 1
    
    def _extract_predicted_numbers(self, prediction_result: Dict) -> str:
        """从预测结果中提取预测号码"""
        if isinstance(prediction_result, dict):
            # 尝试不同的字段名
            for field in ['predicted_number', 'numbers', 'result', '百位', '十位', '个位']:
                if field in prediction_result:
                    value = prediction_result[field]
                    if isinstance(value, str) and len(value) == 3:
                        return value
            
            # 尝试组合百十个位
            if all(pos in prediction_result for pos in ['百位', '十位', '个位']):
                return f"{prediction_result['百位']}{prediction_result['十位']}{prediction_result['个位']}"
        
        return "000"  # 默认值
    
    def _estimate_period_from_timestamp(self, timestamp: datetime) -> str:
        """根据时间戳估算期号"""
        # 简单的期号估算逻辑
        # 假设每天一期，从2025001开始
        base_date = datetime(2025, 1, 1)
        days_diff = (timestamp.date() - base_date.date()).days
        period_number = 2025001 + days_diff
        return str(period_number)
    
    def _extract_diversity_report_predictions(self, report_data: Dict, filename: str):
        """从多样性报告中提取预测信息"""
        if 'detailed_analysis' in report_data and 'number_frequency' in report_data['detailed_analysis']:
            frequency_data = report_data['detailed_analysis']['number_frequency']
            timestamp_str = report_data.get('timestamp', '')
            
            if timestamp_str:
                timestamp = datetime.fromisoformat(timestamp_str)
                period_number = self._estimate_period_from_timestamp(timestamp)
                
                # 取频率最高的号码作为预测
                if frequency_data:
                    most_frequent = max(frequency_data.items(), key=lambda x: x[1])
                    predicted_numbers = most_frequent[0]
                    
                    if predicted_numbers and len(predicted_numbers) == 3:
                        prediction_record = PredictionRecord(
                            period_number=period_number,
                            model_name='fusion_prediction',
                            predicted_numbers=predicted_numbers,
                            confidence=0.5,  # 默认置信度
                            prediction_time=timestamp,
                            metadata={'source': 'validation_report', 'report_file': filename}
                        )
                        
                        self.target_storage.save_prediction_record(prediction_record)
                        self.migration_stats['validation_records'] += 1
    
    def _extract_markov_validation_predictions(self, report_data: Dict, filename: str):
        """从马尔可夫验证报告中提取预测信息"""
        # 实现马尔可夫验证报告的预测提取逻辑
        pass
    
    def _extract_fusion_state_info(self, state_data: Dict, filename: str):
        """从融合状态中提取信息"""
        # 可以提取训练时间、数据指纹等信息用于元数据
        pass
    
    def _extract_quality_history_info(self, quality_data: Dict, filename: str):
        """从质量历史中提取信息"""
        # 可以提取质量指标历史用于分析
        pass
    
    def _migrate_table_data(self, conn: sqlite3.Connection, table_name: str):
        """迁移指定表的数据"""
        try:
            cursor = conn.cursor()
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [row[1] for row in cursor.fetchall()]
            
            # 获取所有数据
            cursor.execute(f"SELECT * FROM {table_name}")
            rows = cursor.fetchall()
            
            self.logger.info(f"从表 {table_name} 获取到 {len(rows)} 条记录")
            
            # 根据表结构转换数据
            for row in rows:
                try:
                    # 这里需要根据具体的表结构来转换数据
                    # 暂时跳过，因为不知道具体的表结构
                    pass
                except Exception as e:
                    self.logger.error(f"转换表 {table_name} 记录失败: {e}")
                    self.migration_stats['errors'] += 1
                    
        except Exception as e:
            self.logger.error(f"迁移表 {table_name} 失败: {e}")
    
    def _verify_migration_integrity(self) -> Dict[str, Any]:
        """验证迁移数据的完整性"""
        try:
            # 获取统计信息
            stats = self.target_storage.get_statistics()
            
            # 检查数据一致性
            consistency_checks = {
                'total_records_check': stats['total_predictions'] > 0,
                'model_distribution_check': len(stats['model_counts']) > 0,
                'verified_records_check': stats['verified_predictions'] >= 0,
                'accuracy_calculation_check': stats['average_accuracy'] >= 0.0
            }
            
            # 检查重复记录
            duplicate_check = self._check_duplicate_records()
            
            return {
                'statistics': stats,
                'consistency_checks': consistency_checks,
                'duplicate_check': duplicate_check,
                'all_checks_passed': all(consistency_checks.values()) and duplicate_check['duplicates_count'] == 0
            }
            
        except Exception as e:
            self.logger.error(f"数据完整性验证失败: {e}")
            return {'error': str(e)}
    
    def _check_duplicate_records(self) -> Dict[str, Any]:
        """检查重复记录"""
        try:
            with sqlite3.connect(self.target_storage.db_path) as conn:
                cursor = conn.cursor()
                
                # 检查重复的期号-模型组合
                cursor.execute("""
                    SELECT period_number, model_name, COUNT(*) as count
                    FROM unified_predictions
                    GROUP BY period_number, model_name
                    HAVING COUNT(*) > 1
                """)
                
                duplicates = cursor.fetchall()
                
                return {
                    'duplicates_count': len(duplicates),
                    'duplicate_details': duplicates
                }
                
        except Exception as e:
            self.logger.error(f"检查重复记录失败: {e}")
            return {'error': str(e)}
    
    def _generate_migration_report(self, consistency_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成迁移报告"""
        # 更新总迁移数
        self.migration_stats['total_migrated'] = (
            self.migration_stats['model_library_records'] +
            self.migration_stats['cache_records'] +
            self.migration_stats['validation_records']
        )
        
        return {
            'migration_timestamp': datetime.now().isoformat(),
            'migration_stats': self.migration_stats,
            'consistency_result': consistency_result,
            'backup_location': self.backup_dir,
            'success': consistency_result.get('all_checks_passed', False)
        }
    
    def _rollback_migration(self):
        """回滚迁移"""
        try:
            self.logger.warning("开始回滚迁移...")
            
            # 删除统一数据库
            if os.path.exists(self.target_storage.db_path):
                os.remove(self.target_storage.db_path)
                self.logger.info("删除统一数据库")
            
            # 恢复备份文件
            if os.path.exists(self.backup_dir):
                for backup_file in os.listdir(self.backup_dir):
                    if backup_file.endswith('.db'):
                        backup_path = os.path.join(self.backup_dir, backup_file)
                        restore_path = os.path.join('data', backup_file)
                        shutil.copy2(backup_path, restore_path)
                        self.logger.info(f"恢复 {backup_file}")
            
            self.logger.info("迁移回滚完成")
            
        except Exception as e:
            self.logger.error(f"回滚失败: {e}")


if __name__ == "__main__":
    # 测试代码
    migration_tool = DataMigrationTool()
    
    try:
        result = migration_tool.migrate_all_data()
        print("迁移结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"迁移失败: {e}")
