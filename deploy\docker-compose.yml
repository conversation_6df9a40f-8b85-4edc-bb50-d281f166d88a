version: '3.8'

services:
  # 主应用服务
  lottery-app:
    build:
      context: ..
      dockerfile: deploy/Dockerfile
    container_name: lottery-3d-app
    ports:
      - "8501:8501"  # Streamlit主界面
    environment:
      - PYTHONPATH=/app/src
      - STREAMLIT_SERVER_PORT=8501
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0
    volumes:
      - ../data:/app/data
      - ../logs:/app/logs
    depends_on:
      - redis
      - postgres
    networks:
      - lottery-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API服务
  lottery-api:
    build:
      context: ..
      dockerfile: deploy/Dockerfile.api
    container_name: lottery-3d-api
    ports:
      - "8000:8000"  # FastAPI服务
    environment:
      - PYTHONPATH=/app/src
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - REDIS_URL=redis://redis:6379
      - POSTGRES_URL=*********************************************/lottery_db
    volumes:
      - ../data:/app/data
      - ../logs:/app/logs
    depends_on:
      - redis
      - postgres
    networks:
      - lottery-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: lottery-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - lottery-network
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL数据库服务
  postgres:
    image: postgres:15-alpine
    container_name: lottery-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=lottery_db
      - POSTGRES_USER=lottery
      - POSTGRES_PASSWORD=lottery123
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ../scripts/init_postgres.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - lottery-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U lottery -d lottery_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: lottery-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - lottery-app
      - lottery-api
    networks:
      - lottery-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 监控服务（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: lottery-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    networks:
      - lottery-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  # Grafana仪表板（可选）
  grafana:
    image: grafana/grafana:latest
    container_name: lottery-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - lottery-network
    restart: unless-stopped

volumes:
  redis-data:
    driver: local
  postgres-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

networks:
  lottery-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
