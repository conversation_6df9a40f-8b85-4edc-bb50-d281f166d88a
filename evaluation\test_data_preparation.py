#!/usr/bin/env python3
"""
测试数据准备脚本
为福彩3D预测系统模式评审准备测试数据和测试场景
"""

import json
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
import random

class TestDataPreparation:
    """测试数据准备器"""
    
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root) if project_root else Path(__file__).parent.parent
        self.test_data = {}
        
    def prepare_historical_data(self):
        """准备历史数据样本"""
        print("📊 准备历史数据样本...")
        
        # 从数据库获取最近100期数据作为测试样本
        db_path = self.project_root / 'data' / 'lottery.db'
        
        try:
            with sqlite3.connect(str(db_path)) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT period_number, draw_date, winning_number 
                    FROM lottery_records 
                    ORDER BY period_number DESC 
                    LIMIT 100
                """)
                
                historical_data = []
                for row in cursor.fetchall():
                    historical_data.append({
                        'period_number': row[0],
                        'draw_date': row[1],
                        'winning_number': row[2]
                    })
                
                self.test_data['historical_data'] = historical_data
                print(f"✅ 准备了 {len(historical_data)} 期历史数据")
                
        except Exception as e:
            print(f"❌ 历史数据准备失败: {e}")
            # 使用模拟数据
            self.test_data['historical_data'] = self._generate_mock_historical_data()
    
    def _generate_mock_historical_data(self):
        """生成模拟历史数据"""
        mock_data = []
        base_period = 2025000
        
        for i in range(100):
            period = base_period + i + 1
            date = (datetime.now() - timedelta(days=100-i)).strftime('%Y-%m-%d')
            number = f"{random.randint(0,9)}{random.randint(0,9)}{random.randint(0,9)}"
            
            mock_data.append({
                'period_number': str(period),
                'draw_date': date,
                'winning_number': number
            })
        
        return mock_data
    
    def prepare_prediction_scenarios(self):
        """准备模拟预测场景"""
        print("🎯 准备模拟预测场景...")
        
        scenarios = [
            {
                'name': '标准预测场景',
                'description': '使用默认参数进行预测',
                'parameters': {
                    'candidate_count': 10,
                    'confidence_threshold': 0.3,
                    'window_size': 50
                }
            },
            {
                'name': '高置信度场景',
                'description': '使用高置信度阈值进行预测',
                'parameters': {
                    'candidate_count': 5,
                    'confidence_threshold': 0.6,
                    'window_size': 30
                }
            },
            {
                'name': '大候选集场景',
                'description': '生成更多候选号码',
                'parameters': {
                    'candidate_count': 20,
                    'confidence_threshold': 0.2,
                    'window_size': 100
                }
            },
            {
                'name': '快速预测场景',
                'description': '使用较小窗口快速预测',
                'parameters': {
                    'candidate_count': 8,
                    'confidence_threshold': 0.4,
                    'window_size': 20
                }
            }
        ]
        
        self.test_data['prediction_scenarios'] = scenarios
        print(f"✅ 准备了 {len(scenarios)} 个预测场景")
    
    def prepare_performance_benchmarks(self):
        """准备性能测试基准数据"""
        print("⚡ 准备性能测试基准数据...")
        
        benchmarks = {
            'response_time_targets': {
                'api_health_check': 0.5,  # 秒
                'single_best_prediction': 5.0,  # 秒
                'prediction_ranking': 3.0,  # 秒
                'model_performance': 1.0,  # 秒
                'page_load_time': 3.0  # 秒
            },
            'accuracy_targets': {
                'single_prediction_accuracy': 0.05,  # 5%
                'top_3_accuracy': 0.12,  # 12%
                'top_5_accuracy': 0.18,  # 18%
                'prediction_consistency': 0.90  # 90%
            },
            'user_experience_targets': {
                'interface_friendliness': 9.0,  # /10
                'operation_convenience': 9.0,  # /10
                'information_clarity': 9.0,  # /10
                'overall_satisfaction': 8.8  # /10
            },
            'system_stability_targets': {
                'uptime': 0.999,  # 99.9%
                'error_rate': 0.001,  # 0.1%
                'memory_usage_mb': 512,  # MB
                'cpu_usage_percent': 80  # %
            }
        }
        
        self.test_data['performance_benchmarks'] = benchmarks
        print("✅ 性能基准数据准备完成")
    
    def prepare_user_behavior_scripts(self):
        """准备用户行为测试脚本"""
        print("👤 准备用户行为测试脚本...")
        
        user_behaviors = [
            {
                'name': '新用户首次使用流程',
                'steps': [
                    '访问主页',
                    '查看功能介绍',
                    '点击预测分析',
                    '查看预测结果',
                    '查看排行榜',
                    '查看技术详情'
                ],
                'expected_time': 180  # 秒
            },
            {
                'name': '常规用户预测流程',
                'steps': [
                    '直接访问预测页面',
                    '调整预测参数',
                    '执行预测',
                    '查看单一最优推荐',
                    '查看候选排行榜',
                    '查看模型权重'
                ],
                'expected_time': 60  # 秒
            },
            {
                'name': '高级用户深度分析流程',
                'steps': [
                    '访问预测页面',
                    '设置高级参数',
                    '执行多次预测',
                    '对比预测结果',
                    '查看历史表现',
                    '分析模型性能'
                ],
                'expected_time': 300  # 秒
            },
            {
                'name': '移动端用户流程',
                'steps': [
                    '移动端访问',
                    '检查响应式布局',
                    '触摸操作测试',
                    '滑动查看排行榜',
                    '缩放查看详情'
                ],
                'expected_time': 120  # 秒
            }
        ]
        
        self.test_data['user_behavior_scripts'] = user_behaviors
        print(f"✅ 准备了 {len(user_behaviors)} 个用户行为脚本")
    
    def prepare_api_test_cases(self):
        """准备API测试用例"""
        print("🔌 准备API测试用例...")
        
        api_test_cases = [
            {
                'name': '健康检查API测试',
                'method': 'GET',
                'url': '/health',
                'expected_status': 200,
                'expected_fields': ['status', 'timestamp', 'database_records']
            },
            {
                'name': '单一最优预测API测试',
                'method': 'POST',
                'url': '/api/v1/prediction/single-best',
                'payload': {
                    'candidate_count': 10,
                    'confidence_threshold': 0.3,
                    'window_size': 50
                },
                'expected_status': 200,
                'expected_fields': ['best_prediction', 'ranking_list', 'model_performance']
            },
            {
                'name': '预测排行榜API测试',
                'method': 'GET',
                'url': '/api/v1/prediction/ranking/2025999',
                'expected_status': 200,
                'expected_fields': ['ranking_list', 'metadata']
            },
            {
                'name': '模型性能API测试',
                'method': 'GET',
                'url': '/api/v1/models/performance',
                'expected_status': 200,
                'expected_fields': ['models', 'overall_stats', 'last_updated']
            },
            {
                'name': '预测结果跟踪API测试',
                'method': 'POST',
                'url': '/api/v1/prediction/track-result',
                'payload': {
                    'period_number': '2025999',
                    'predicted_number': '123',
                    'actual_number': '123',
                    'model_name': 'test_model'
                },
                'expected_status': 200,
                'expected_fields': ['success', 'message']
            }
        ]
        
        self.test_data['api_test_cases'] = api_test_cases
        print(f"✅ 准备了 {len(api_test_cases)} 个API测试用例")
    
    def save_test_data(self):
        """保存测试数据到文件"""
        print("💾 保存测试数据...")
        
        # 创建评审目录
        eval_dir = self.project_root / 'evaluation'
        eval_dir.mkdir(exist_ok=True)
        
        # 保存测试数据
        test_data_file = eval_dir / 'test_data.json'
        with open(test_data_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"✅ 测试数据已保存到: {test_data_file}")
        
        # 生成测试数据摘要
        summary = {
            'preparation_time': datetime.now().isoformat(),
            'data_categories': list(self.test_data.keys()),
            'historical_data_count': len(self.test_data.get('historical_data', [])),
            'prediction_scenarios_count': len(self.test_data.get('prediction_scenarios', [])),
            'user_behavior_scripts_count': len(self.test_data.get('user_behavior_scripts', [])),
            'api_test_cases_count': len(self.test_data.get('api_test_cases', [])),
            'total_test_items': sum([
                len(self.test_data.get('historical_data', [])),
                len(self.test_data.get('prediction_scenarios', [])),
                len(self.test_data.get('user_behavior_scripts', [])),
                len(self.test_data.get('api_test_cases', []))
            ])
        }
        
        summary_file = eval_dir / 'test_data_summary.json'
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 测试数据摘要已保存到: {summary_file}")
        return summary
    
    def run_preparation(self):
        """执行完整的测试数据准备流程"""
        print("🚀 开始测试数据准备...")
        
        self.prepare_historical_data()
        self.prepare_prediction_scenarios()
        self.prepare_performance_benchmarks()
        self.prepare_user_behavior_scripts()
        self.prepare_api_test_cases()
        
        summary = self.save_test_data()
        
        print("\n📋 测试数据准备完成摘要:")
        print(f"  - 历史数据: {summary['historical_data_count']} 期")
        print(f"  - 预测场景: {summary['prediction_scenarios_count']} 个")
        print(f"  - 用户行为脚本: {summary['user_behavior_scripts_count']} 个")
        print(f"  - API测试用例: {summary['api_test_cases_count']} 个")
        print(f"  - 总测试项目: {summary['total_test_items']} 个")
        print("✅ 测试数据准备就绪！")
        
        return summary

def main():
    """主函数"""
    preparation = TestDataPreparation()
    summary = preparation.run_preparation()
    return summary

if __name__ == "__main__":
    main()
