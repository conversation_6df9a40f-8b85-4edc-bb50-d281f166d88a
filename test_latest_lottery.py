#!/usr/bin/env python3
"""
测试最新开奖信息获取功能
"""

import sys
import os
sys.path.append('src')

import sqlite3
from datetime import datetime

def test_database_query():
    """测试数据库查询"""
    print("测试数据库查询...")
    
    try:
        db_path = "data/lottery.db"
        
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 直接获取最新记录
            cursor.execute("""
                SELECT period, date, numbers 
                FROM lottery_records 
                ORDER BY period DESC 
                LIMIT 1
            """)
            
            latest_record = cursor.fetchone()
            
            if latest_record:
                period, date_str, numbers = latest_record
                
                print(f"最新记录:")
                print(f"  期号: {period}")
                print(f"  日期: {date_str}")
                print(f"  号码: {numbers}")
                
                # 格式化日期
                try:
                    if date_str:
                        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                        formatted_date = date_obj.strftime("%Y年%m月%d日")
                    else:
                        formatted_date = "未知日期"
                except:
                    formatted_date = date_str or "未知日期"
                
                print(f"  格式化日期: {formatted_date}")
                
                # 处理号码显示
                display_numbers = []
                if numbers and len(numbers) >= 3:
                    display_numbers = [numbers[i] for i in range(min(3, len(numbers)))]
                else:
                    display_numbers = ["?", "?", "?"]
                
                print(f"  显示号码: {display_numbers}")
                
                result = {
                    "success": True,
                    "data": {
                        "period": str(period),
                        "date": date_str,
                        "numbers": numbers or "---",
                        "formatted_date": formatted_date,
                        "display_numbers": display_numbers
                    }
                }
                
                print(f"返回结果: {result}")
                return result
            else:
                print("没有找到记录")
                return None
                
    except Exception as e:
        print(f"数据库查询失败: {e}")
        return None

def test_get_latest_lottery_result():
    """测试get_latest_lottery_result函数"""
    print("\n测试get_latest_lottery_result函数...")
    
    # 导入函数
    from src.ui.main import get_latest_lottery_result
    
    result = get_latest_lottery_result()
    print(f"函数返回结果: {result}")
    
    if result and result.get("success"):
        data = result.get("data", {})
        print(f"期号: {data.get('period')}")
        print(f"日期: {data.get('formatted_date')}")
        print(f"号码: {data.get('display_numbers')}")
    else:
        print("函数执行失败")

if __name__ == "__main__":
    test_database_query()
    test_get_latest_lottery_result()
