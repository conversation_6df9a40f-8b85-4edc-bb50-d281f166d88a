#!/usr/bin/env python3
"""
更新现有Bug的分类信息
"""

import sys
import os
sys.path.insert(0, '.')

from src.bug_detection.core.database_manager import DatabaseManager
from src.bug_detection.algorithms.enhanced_detection import EnhancedBugDetector
import sqlite3

def update_existing_bugs():
    print("=== 更新现有Bug分类信息 ===")
    
    # 初始化组件
    db_manager = DatabaseManager()
    detector = EnhancedBugDetector()
    
    # 获取所有未正确分类的Bug (error_type = 'unknown')
    try:
        conn = sqlite3.connect(db_manager.db_path)
        cursor = conn.cursor()
        
        # 查找需要更新的Bug
        cursor.execute("""
            SELECT id, error_message, stack_trace, page_name, created_at 
            FROM bug_reports 
            WHERE error_type = 'unknown' OR error_type = ''
            ORDER BY created_at DESC
        """)
        
        bugs_to_update = cursor.fetchall()
        print(f"找到 {len(bugs_to_update)} 个需要重新分类的Bug")
        
        updated_count = 0
        
        for bug in bugs_to_update:
            bug_id, error_message, stack_trace, page_name, created_at = bug
            print(f"\n处理Bug: {bug_id}")
            
            # 构造错误数据用于重新分析
            error_data = {
                'type': 'javascript',
                'message': error_message or 'Unknown error',
                'stack_trace': stack_trace or '',
                'page_url': f'http://127.0.0.1:8501/{page_name}' if page_name else 'http://127.0.0.1:8501',
                'source': 'unknown',
                'line': 0,
                'column': 0
            }
            
            try:
                # 重新分析Bug
                analysis_result = detector.detect_and_classify(error_data)
                
                print(f"  原分类: unknown")
                print(f"  新分类: {analysis_result.get('category', 'general')}")
                print(f"  严重程度: {analysis_result.get('severity', 'medium')}")
                print(f"  优先级: {analysis_result.get('priority', 'medium')}")
                
                # 更新数据库
                cursor.execute("""
                    UPDATE bug_reports 
                    SET error_type = ?, 
                        severity = ?, 
                        category = ?, 
                        priority = ?, 
                        environment = ?,
                        tags = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (
                    analysis_result.get('category', 'general'),
                    analysis_result.get('severity', 'medium'),
                    analysis_result.get('category', 'general'),
                    analysis_result.get('priority', 'medium'),
                    analysis_result.get('environment', 'production'),
                    ','.join(analysis_result.get('tags', [])),
                    bug_id
                ))
                
                updated_count += 1
                print(f"  ✅ 更新成功")
                
            except Exception as e:
                print(f"  ❌ 更新失败: {e}")
        
        # 提交更改
        conn.commit()
        conn.close()
        
        print(f"\n=== 更新完成 ===")
        print(f"总计处理: {len(bugs_to_update)} 个Bug")
        print(f"成功更新: {updated_count} 个Bug")
        
        # 验证更新结果
        print("\n=== 验证更新结果 ===")
        bugs = db_manager.get_bug_reports(limit=10)
        for bug in bugs:
            print(f"Bug {bug.get('id')}: {bug.get('error_type')} | {bug.get('severity')} | {bug.get('status')}")
        
    except Exception as e:
        print(f"❌ 更新过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    update_existing_bugs()
