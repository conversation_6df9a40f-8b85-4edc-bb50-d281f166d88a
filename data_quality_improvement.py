#!/usr/bin/env python3
"""
数据质量提升工具
清理历史数据异常记录，标准化Bug数据格式，建立数据验证机制
"""

import sys
import os
sys.path.insert(0, '.')

from src.bug_detection.core.database_manager import DatabaseManager
from src.bug_detection.algorithms.enhanced_detection import EnhancedBugDetector
import sqlite3
import json
import re
from datetime import datetime, timedelta

class DataQualityImprover:
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.detector = EnhancedBugDetector(self.db_manager)
        
    def analyze_data_quality(self):
        """分析当前数据质量状况"""
        print("=== 数据质量分析 ===")
        
        try:
            conn = sqlite3.connect(self.db_manager.db_path)
            cursor = conn.cursor()
            
            # 1. 统计总体数据
            cursor.execute("SELECT COUNT(*) FROM bug_reports")
            total_bugs = cursor.fetchone()[0]
            print(f"📊 总Bug数量: {total_bugs}")
            
            # 2. 检查数据完整性
            print("\n🔍 数据完整性检查:")
            
            # 检查空值字段
            empty_fields = {}
            fields_to_check = ['error_message', 'error_type', 'severity', 'status', 'created_at']
            
            for field in fields_to_check:
                cursor.execute(f"SELECT COUNT(*) FROM bug_reports WHERE {field} IS NULL OR {field} = ''")
                empty_count = cursor.fetchone()[0]
                if empty_count > 0:
                    empty_fields[field] = empty_count
                    print(f"  ❌ {field}: {empty_count} 个空值")
                else:
                    print(f"  ✅ {field}: 无空值")
            
            # 3. 检查数据一致性
            print("\n🔧 数据一致性检查:")
            
            # 检查错误类型分布
            cursor.execute("SELECT error_type, COUNT(*) FROM bug_reports GROUP BY error_type")
            error_types = cursor.fetchall()
            print("  错误类型分布:")
            for error_type, count in error_types:
                print(f"    {error_type}: {count} 个")
            
            # 检查严重程度分布
            cursor.execute("SELECT severity, COUNT(*) FROM bug_reports GROUP BY severity")
            severities = cursor.fetchall()
            print("  严重程度分布:")
            for severity, count in severities:
                print(f"    {severity}: {count} 个")
            
            # 4. 检查异常数据
            print("\n⚠️ 异常数据检查:")
            
            # 检查异常长的错误消息
            cursor.execute("SELECT COUNT(*) FROM bug_reports WHERE LENGTH(error_message) > 1000")
            long_messages = cursor.fetchone()[0]
            if long_messages > 0:
                print(f"  ❌ 异常长的错误消息: {long_messages} 个")
            else:
                print(f"  ✅ 错误消息长度正常")
            
            # 检查重复的Bug
            cursor.execute("""
                SELECT error_message, COUNT(*) as count 
                FROM bug_reports 
                GROUP BY error_message 
                HAVING count > 1
                ORDER BY count DESC
                LIMIT 5
            """)
            duplicates = cursor.fetchall()
            if duplicates:
                print(f"  ❌ 发现重复Bug:")
                for message, count in duplicates:
                    print(f"    '{message[:50]}...': {count} 次")
            else:
                print(f"  ✅ 无明显重复Bug")
            
            conn.close()
            
            return {
                'total_bugs': total_bugs,
                'empty_fields': empty_fields,
                'error_types': dict(error_types),
                'severities': dict(severities),
                'long_messages': long_messages,
                'duplicates': len(duplicates)
            }
            
        except Exception as e:
            print(f"❌ 数据质量分析失败: {e}")
            return None
    
    def clean_historical_data(self):
        """清理历史数据异常记录"""
        print("\n=== 清理历史数据 ===")
        
        try:
            conn = sqlite3.connect(self.db_manager.db_path)
            cursor = conn.cursor()
            
            cleaned_count = 0
            
            # 1. 清理空的错误消息
            cursor.execute("SELECT COUNT(*) FROM bug_reports WHERE error_message IS NULL OR error_message = ''")
            empty_messages = cursor.fetchone()[0]
            
            if empty_messages > 0:
                cursor.execute("UPDATE bug_reports SET error_message = 'Unknown error' WHERE error_message IS NULL OR error_message = ''")
                print(f"  ✅ 修复了 {empty_messages} 个空错误消息")
                cleaned_count += empty_messages
            
            # 2. 标准化错误类型
            cursor.execute("UPDATE bug_reports SET error_type = 'general' WHERE error_type IS NULL OR error_type = '' OR error_type = 'unknown'")
            standardized_types = cursor.rowcount
            if standardized_types > 0:
                print(f"  ✅ 标准化了 {standardized_types} 个错误类型")
                cleaned_count += standardized_types
            
            # 3. 标准化严重程度
            severity_mapping = {
                'low': 'low',
                'medium': 'medium', 
                'high': 'high',
                'critical': 'critical'
            }
            
            cursor.execute("SELECT DISTINCT severity FROM bug_reports WHERE severity NOT IN ('low', 'medium', 'high', 'critical')")
            invalid_severities = cursor.fetchall()
            
            for (invalid_severity,) in invalid_severities:
                if invalid_severity:
                    # 尝试映射到标准严重程度
                    normalized = invalid_severity.lower().strip()
                    if 'crit' in normalized:
                        cursor.execute("UPDATE bug_reports SET severity = 'critical' WHERE severity = ?", (invalid_severity,))
                    elif 'high' in normalized:
                        cursor.execute("UPDATE bug_reports SET severity = 'high' WHERE severity = ?", (invalid_severity,))
                    elif 'low' in normalized:
                        cursor.execute("UPDATE bug_reports SET severity = 'low' WHERE severity = ?", (invalid_severity,))
                    else:
                        cursor.execute("UPDATE bug_reports SET severity = 'medium' WHERE severity = ?", (invalid_severity,))
                    
                    fixed_count = cursor.rowcount
                    if fixed_count > 0:
                        print(f"  ✅ 修复了 {fixed_count} 个无效严重程度 '{invalid_severity}'")
                        cleaned_count += fixed_count
            
            # 4. 清理异常长的错误消息
            cursor.execute("SELECT id, error_message FROM bug_reports WHERE LENGTH(error_message) > 1000")
            long_messages = cursor.fetchall()
            
            for bug_id, message in long_messages:
                # 截断到合理长度并添加省略号
                truncated_message = message[:500] + "... (truncated)"
                cursor.execute("UPDATE bug_reports SET error_message = ? WHERE id = ?", (truncated_message, bug_id))
                cleaned_count += 1
            
            if long_messages:
                print(f"  ✅ 截断了 {len(long_messages)} 个过长的错误消息")
            
            # 5. 添加缺失的时间戳
            cursor.execute("SELECT COUNT(*) FROM bug_reports WHERE created_at IS NULL")
            missing_timestamps = cursor.fetchone()[0]
            
            if missing_timestamps > 0:
                current_time = datetime.now().isoformat()
                cursor.execute("UPDATE bug_reports SET created_at = ? WHERE created_at IS NULL", (current_time,))
                print(f"  ✅ 添加了 {missing_timestamps} 个缺失的时间戳")
                cleaned_count += missing_timestamps
            
            conn.commit()
            conn.close()
            
            print(f"\n📊 数据清理完成，总计修复 {cleaned_count} 个问题")
            return cleaned_count
            
        except Exception as e:
            print(f"❌ 数据清理失败: {e}")
            return 0
    
    def standardize_data_format(self):
        """标准化Bug数据格式"""
        print("\n=== 标准化数据格式 ===")
        
        try:
            conn = sqlite3.connect(self.db_manager.db_path)
            cursor = conn.cursor()
            
            standardized_count = 0
            
            # 1. 标准化状态字段
            status_mapping = {
                'open': 'open',
                'opened': 'open',
                'new': 'open',
                'in_progress': 'in_progress',
                'in-progress': 'in_progress',
                'working': 'in_progress',
                'resolved': 'resolved',
                'fixed': 'resolved',
                'closed': 'resolved',
                'done': 'resolved'
            }
            
            for old_status, new_status in status_mapping.items():
                cursor.execute("UPDATE bug_reports SET status = ? WHERE LOWER(status) = ?", (new_status, old_status.lower()))
                updated = cursor.rowcount
                if updated > 0:
                    standardized_count += updated
            
            # 2. 标准化优先级字段
            cursor.execute("SELECT DISTINCT priority FROM bug_reports WHERE priority IS NOT NULL")
            priorities = cursor.fetchall()
            
            for (priority,) in priorities:
                if priority and not re.match(r'^P[1-4]$', priority):
                    # 尝试映射到标准优先级格式
                    normalized = priority.lower().strip()
                    if 'critical' in normalized or 'urgent' in normalized or '1' in normalized:
                        cursor.execute("UPDATE bug_reports SET priority = 'P1' WHERE priority = ?", (priority,))
                    elif 'high' in normalized or '2' in normalized:
                        cursor.execute("UPDATE bug_reports SET priority = 'P2' WHERE priority = ?", (priority,))
                    elif 'medium' in normalized or 'normal' in normalized or '3' in normalized:
                        cursor.execute("UPDATE bug_reports SET priority = 'P3' WHERE priority = ?", (priority,))
                    else:
                        cursor.execute("UPDATE bug_reports SET priority = 'P4' WHERE priority = ?", (priority,))
                    
                    updated = cursor.rowcount
                    if updated > 0:
                        standardized_count += updated
            
            # 3. 标准化环境字段
            cursor.execute("UPDATE bug_reports SET environment = 'development' WHERE environment IS NULL OR environment = ''")
            env_updated = cursor.rowcount
            if env_updated > 0:
                standardized_count += env_updated
            
            # 4. 确保所有Bug都有分类
            cursor.execute("UPDATE bug_reports SET category = error_type WHERE category IS NULL OR category = ''")
            category_updated = cursor.rowcount
            if category_updated > 0:
                standardized_count += category_updated
            
            conn.commit()
            conn.close()
            
            print(f"📊 数据格式标准化完成，总计更新 {standardized_count} 个字段")
            return standardized_count
            
        except Exception as e:
            print(f"❌ 数据格式标准化失败: {e}")
            return 0
    
    def establish_data_validation(self):
        """建立数据验证机制"""
        print("\n=== 建立数据验证机制 ===")
        
        validation_rules = {
            'error_message': {
                'required': True,
                'max_length': 1000,
                'min_length': 5
            },
            'error_type': {
                'required': True,
                'allowed_values': ['ui', 'api', 'database', 'network', 'performance', 'security', 'data', 'integration', 'general']
            },
            'severity': {
                'required': True,
                'allowed_values': ['low', 'medium', 'high', 'critical']
            },
            'status': {
                'required': True,
                'allowed_values': ['open', 'in_progress', 'resolved', 'closed']
            },
            'priority': {
                'required': False,
                'pattern': r'^P[1-4]$'
            },
            'environment': {
                'required': True,
                'allowed_values': ['development', 'testing', 'staging', 'production']
            }
        }
        
        # 保存验证规则到文件
        validation_file = 'data/bug_validation_rules.json'
        os.makedirs(os.path.dirname(validation_file), exist_ok=True)
        
        with open(validation_file, 'w', encoding='utf-8') as f:
            json.dump(validation_rules, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 数据验证规则已保存到: {validation_file}")
        
        # 创建验证函数
        validation_code = '''
def validate_bug_data(bug_data, validation_rules):
    """验证Bug数据是否符合规则"""
    errors = []
    
    for field, rules in validation_rules.items():
        value = bug_data.get(field)
        
        # 检查必填字段
        if rules.get('required', False) and (value is None or value == ''):
            errors.append(f"Field '{field}' is required")
            continue
        
        if value is not None and value != '':
            # 检查长度限制
            if 'max_length' in rules and len(str(value)) > rules['max_length']:
                errors.append(f"Field '{field}' exceeds maximum length of {rules['max_length']}")
            
            if 'min_length' in rules and len(str(value)) < rules['min_length']:
                errors.append(f"Field '{field}' is below minimum length of {rules['min_length']}")
            
            # 检查允许的值
            if 'allowed_values' in rules and value not in rules['allowed_values']:
                errors.append(f"Field '{field}' has invalid value '{value}'. Allowed: {rules['allowed_values']}")
            
            # 检查正则模式
            if 'pattern' in rules:
                import re
                if not re.match(rules['pattern'], str(value)):
                    errors.append(f"Field '{field}' does not match required pattern '{rules['pattern']}'")
    
    return errors
'''
        
        # 保存验证函数到文件
        validation_func_file = 'src/bug_detection/utils/data_validator.py'
        os.makedirs(os.path.dirname(validation_func_file), exist_ok=True)
        
        with open(validation_func_file, 'w', encoding='utf-8') as f:
            f.write('#!/usr/bin/env python3\n')
            f.write('"""\nBug数据验证工具\n"""\n\n')
            f.write('import json\n')
            f.write('import re\n\n')
            f.write(validation_code)
        
        print(f"✅ 数据验证函数已保存到: {validation_func_file}")
        
        return True

def main():
    print("🔧 数据质量提升工具")
    print("=" * 50)
    
    improver = DataQualityImprover()
    
    # 1. 分析数据质量
    quality_report = improver.analyze_data_quality()
    
    if quality_report:
        # 2. 清理历史数据
        cleaned_count = improver.clean_historical_data()
        
        # 3. 标准化数据格式
        standardized_count = improver.standardize_data_format()
        
        # 4. 建立数据验证机制
        validation_established = improver.establish_data_validation()
        
        # 5. 生成改进报告
        print("\n" + "=" * 50)
        print("📊 数据质量提升报告")
        print("=" * 50)
        
        print(f"原始Bug数量: {quality_report['total_bugs']}")
        print(f"清理的问题数量: {cleaned_count}")
        print(f"标准化的字段数量: {standardized_count}")
        print(f"验证机制建立: {'✅ 成功' if validation_established else '❌ 失败'}")
        
        improvement_rate = ((cleaned_count + standardized_count) / quality_report['total_bugs']) * 100 if quality_report['total_bugs'] > 0 else 0
        print(f"数据改进率: {improvement_rate:.1f}%")
        
        if improvement_rate > 0 and validation_established:
            print("\n🎉 数据质量提升完成！")
            return True
        else:
            print("\n⚠️ 数据质量提升部分完成")
            return False
    else:
        print("❌ 数据质量分析失败")
        return False

if __name__ == "__main__":
    main()
