#!/usr/bin/env python3
"""
单例模式完整实现
确保每个类只有一个实例
"""

import threading
from typing import Any, Dict

class SingletonMeta(type):
    """单例元类"""
    _instances: Dict[Any, Any] = {}
    _lock: threading.Lock = threading.Lock()
    
    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            with cls._lock:
                if cls not in cls._instances:
                    instance = super().__call__(*args, **kwargs)
                    cls._instances[cls] = instance
        return cls._instances[cls]

class Singleton(metaclass=SingletonMeta):
    """单例基类"""
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        self._initialized = True
        self._setup()
    
    def _setup(self):
        """子类重写此方法进行初始化"""
        pass

class DatabaseManagerSingleton(Singleton):
    """数据库管理器单例"""
    
    def _setup(self):
        """初始化数据库管理器"""
        self.connection_pool = {}
        self.max_connections = 10
        self.current_connections = 0
        
    def get_connection(self, db_path: str):
        """获取数据库连接"""
        if db_path not in self.connection_pool:
            import sqlite3
            self.connection_pool[db_path] = sqlite3.connect(
                db_path, 
                check_same_thread=False,
                timeout=30.0
            )
            self.current_connections += 1
        
        return self.connection_pool[db_path]
    
    def close_connections(self):
        """关闭所有连接"""
        for conn in self.connection_pool.values():
            conn.close()
        self.connection_pool.clear()
        self.current_connections = 0

class AIManagerSingleton(Singleton):
    """AI管理器单例"""
    
    def _setup(self):
        """初始化AI管理器"""
        self.model_cache = {}
        self.analysis_cache = {}
        self.cache_size_limit = 1000
        
    def get_cached_analysis(self, key: str):
        """获取缓存的分析结果"""
        return self.analysis_cache.get(key)
    
    def cache_analysis(self, key: str, result: dict):
        """缓存分析结果"""
        if len(self.analysis_cache) >= self.cache_size_limit:
            # 清理最旧的缓存
            oldest_key = next(iter(self.analysis_cache))
            del self.analysis_cache[oldest_key]
        
        self.analysis_cache[key] = result
    
    def clear_cache(self):
        """清理缓存"""
        self.analysis_cache.clear()
        self.model_cache.clear()

# 全局单例实例
_db_manager = None
_ai_manager = None

def get_database_manager():
    """获取数据库管理器单例"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManagerSingleton()
    return _db_manager

def get_ai_manager():
    """获取AI管理器单例"""
    global _ai_manager
    if _ai_manager is None:
        _ai_manager = AIManagerSingleton()
    return _ai_manager
