#!/usr/bin/env python3
"""
系统健康检查器

监控数据库、数据源、WebSocket状态等系统组件的健康状况
"""

import asyncio
import logging
import time
import sys
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from pathlib import Path

sys.path.append('src')

logger = logging.getLogger(__name__)

class SystemHealthChecker:
    """系统健康检查器"""
    
    def __init__(self):
        """初始化健康检查器"""
        self.logger = logger
        self.last_check_time = None
        self.health_history = []
        self.max_history_size = 100
        
        # 健康检查配置
        self.check_interval = 60  # 检查间隔（秒）
        self.timeout = 30  # 检查超时时间（秒）
        
        # 组件引用
        self.database_manager = None
        self.data_source_manager = None
        self.websocket_manager = None
        
    def set_components(self, database_manager=None, data_source_manager=None, websocket_manager=None):
        """设置要监控的组件"""
        self.database_manager = database_manager
        self.data_source_manager = data_source_manager
        self.websocket_manager = websocket_manager
        
        self.logger.info("系统组件已设置完成")
    
    async def check_database_health(self) -> Dict[str, Any]:
        """检查数据库健康状态"""
        check_result = {
            "component": "database",
            "healthy": False,
            "checks": [],
            "response_time_ms": 0,
            "error": None
        }
        
        start_time = time.time()
        
        try:
            if not self.database_manager:
                check_result["error"] = "数据库管理器未设置"
                return check_result
            
            # 检查数据库连接
            if hasattr(self.database_manager, 'health_check'):
                health_status = self.database_manager.health_check()
                check_result["checks"] = health_status.get("checks", [])
                check_result["healthy"] = health_status.get("healthy", False)
            else:
                # 基本连接测试
                try:
                    record_count = self.database_manager.get_records_count()
                    check_result["checks"].append({
                        "name": "basic_connection",
                        "status": "ok",
                        "record_count": record_count
                    })
                    check_result["healthy"] = True
                except Exception as e:
                    check_result["checks"].append({
                        "name": "basic_connection",
                        "status": "error",
                        "error": str(e)
                    })
                    check_result["healthy"] = False
            
        except Exception as e:
            check_result["error"] = str(e)
            self.logger.error(f"数据库健康检查失败: {e}")
        
        check_result["response_time_ms"] = int((time.time() - start_time) * 1000)
        return check_result
    
    async def check_data_source_health(self) -> Dict[str, Any]:
        """检查数据源健康状态"""
        check_result = {
            "component": "data_source",
            "healthy": False,
            "checks": [],
            "response_time_ms": 0,
            "error": None
        }
        
        start_time = time.time()
        
        try:
            if not self.data_source_manager:
                check_result["error"] = "数据源管理器未设置"
                return check_result
            
            # 检查数据源状态
            if hasattr(self.data_source_manager, 'health_check'):
                health_status = self.data_source_manager.health_check()
                check_result["checks"] = health_status.get("checks", [])
                check_result["healthy"] = health_status.get("healthy", False)
            else:
                # 基本缓存检查
                try:
                    cache_status = self.data_source_manager.get_cache_status()
                    check_result["checks"].append({
                        "name": "cache_status",
                        "status": "ok",
                        "cache_files": cache_status.get("cache_files", 0),
                        "hit_rate": cache_status.get("cache_hit_rate", 0)
                    })
                    check_result["healthy"] = True
                except Exception as e:
                    check_result["checks"].append({
                        "name": "cache_status",
                        "status": "error",
                        "error": str(e)
                    })
                    check_result["healthy"] = False
            
        except Exception as e:
            check_result["error"] = str(e)
            self.logger.error(f"数据源健康检查失败: {e}")
        
        check_result["response_time_ms"] = int((time.time() - start_time) * 1000)
        return check_result
    
    async def check_websocket_health(self) -> Dict[str, Any]:
        """检查WebSocket健康状态"""
        check_result = {
            "component": "websocket",
            "healthy": False,
            "checks": [],
            "response_time_ms": 0,
            "error": None
        }
        
        start_time = time.time()
        
        try:
            if not self.websocket_manager:
                check_result["error"] = "WebSocket管理器未设置"
                return check_result
            
            # 检查WebSocket管理器状态
            if hasattr(self.websocket_manager, 'get_stats'):
                stats = self.websocket_manager.get_stats()
                check_result["checks"].append({
                    "name": "connection_stats",
                    "status": "ok",
                    "current_connections": stats.get("current_connections", 0),
                    "total_connections": stats.get("total_connections", 0),
                    "messages_sent": stats.get("messages_sent", 0),
                    "errors": stats.get("errors", 0)
                })
                check_result["healthy"] = True
            else:
                # 基本状态检查
                try:
                    active_connections = len(getattr(self.websocket_manager, 'active_connections', {}))
                    check_result["checks"].append({
                        "name": "basic_status",
                        "status": "ok",
                        "active_connections": active_connections
                    })
                    check_result["healthy"] = True
                except Exception as e:
                    check_result["checks"].append({
                        "name": "basic_status",
                        "status": "error",
                        "error": str(e)
                    })
                    check_result["healthy"] = False
            
        except Exception as e:
            check_result["error"] = str(e)
            self.logger.error(f"WebSocket健康检查失败: {e}")
        
        check_result["response_time_ms"] = int((time.time() - start_time) * 1000)
        return check_result
    
    async def check_system_resources(self) -> Dict[str, Any]:
        """检查系统资源"""
        check_result = {
            "component": "system_resources",
            "healthy": True,
            "checks": [],
            "response_time_ms": 0,
            "error": None
        }
        
        start_time = time.time()
        
        try:
            # 检查磁盘空间
            try:
                import shutil
                total, used, free = shutil.disk_usage(".")
                free_gb = free // (1024**3)
                
                if free_gb < 1:  # 少于1GB
                    check_result["checks"].append({
                        "name": "disk_space",
                        "status": "error",
                        "free_gb": free_gb,
                        "message": "磁盘空间不足"
                    })
                    check_result["healthy"] = False
                elif free_gb < 5:  # 少于5GB
                    check_result["checks"].append({
                        "name": "disk_space",
                        "status": "warning",
                        "free_gb": free_gb,
                        "message": "磁盘空间较少"
                    })
                else:
                    check_result["checks"].append({
                        "name": "disk_space",
                        "status": "ok",
                        "free_gb": free_gb
                    })
            except Exception as e:
                check_result["checks"].append({
                    "name": "disk_space",
                    "status": "error",
                    "error": str(e)
                })
            
            # 检查内存使用（如果psutil可用）
            try:
                import psutil
                memory = psutil.virtual_memory()
                memory_usage_percent = memory.percent
                
                if memory_usage_percent > 90:
                    check_result["checks"].append({
                        "name": "memory_usage",
                        "status": "error",
                        "usage_percent": memory_usage_percent,
                        "message": "内存使用率过高"
                    })
                    check_result["healthy"] = False
                elif memory_usage_percent > 80:
                    check_result["checks"].append({
                        "name": "memory_usage",
                        "status": "warning",
                        "usage_percent": memory_usage_percent,
                        "message": "内存使用率较高"
                    })
                else:
                    check_result["checks"].append({
                        "name": "memory_usage",
                        "status": "ok",
                        "usage_percent": memory_usage_percent
                    })
            except ImportError:
                check_result["checks"].append({
                    "name": "memory_usage",
                    "status": "warning",
                    "message": "psutil未安装，无法检查内存使用"
                })
            except Exception as e:
                check_result["checks"].append({
                    "name": "memory_usage",
                    "status": "error",
                    "error": str(e)
                })
            
        except Exception as e:
            check_result["error"] = str(e)
            self.logger.error(f"系统资源检查失败: {e}")
        
        check_result["response_time_ms"] = int((time.time() - start_time) * 1000)
        return check_result
    
    async def perform_full_health_check(self) -> Dict[str, Any]:
        """执行完整的健康检查"""
        check_start_time = time.time()
        
        self.logger.info("开始执行系统健康检查...")
        
        # 并发执行所有健康检查
        check_tasks = [
            self.check_database_health(),
            self.check_data_source_health(),
            self.check_websocket_health(),
            self.check_system_resources()
        ]
        
        try:
            results = await asyncio.gather(*check_tasks, return_exceptions=True)
        except Exception as e:
            self.logger.error(f"健康检查执行失败: {e}")
            return {
                "timestamp": datetime.now().isoformat(),
                "overall_healthy": False,
                "error": str(e),
                "components": []
            }
        
        # 处理结果
        components = []
        overall_healthy = True
        
        for result in results:
            if isinstance(result, Exception):
                self.logger.error(f"健康检查组件失败: {result}")
                components.append({
                    "component": "unknown",
                    "healthy": False,
                    "error": str(result)
                })
                overall_healthy = False
            else:
                components.append(result)
                if not result.get("healthy", False):
                    overall_healthy = False
        
        # 构建完整结果
        full_result = {
            "timestamp": datetime.now().isoformat(),
            "overall_healthy": overall_healthy,
            "total_response_time_ms": int((time.time() - check_start_time) * 1000),
            "components": components,
            "summary": {
                "total_components": len(components),
                "healthy_components": sum(1 for c in components if c.get("healthy", False)),
                "unhealthy_components": sum(1 for c in components if not c.get("healthy", False))
            }
        }
        
        # 记录到历史
        self.health_history.append(full_result)
        if len(self.health_history) > self.max_history_size:
            self.health_history.pop(0)
        
        self.last_check_time = datetime.now()
        
        # 记录结果
        if overall_healthy:
            self.logger.info(f"✅ 系统健康检查完成: 所有组件正常 ({full_result['total_response_time_ms']}ms)")
        else:
            unhealthy_count = full_result['summary']['unhealthy_components']
            self.logger.warning(f"⚠️  系统健康检查完成: {unhealthy_count} 个组件异常")
        
        return full_result
    
    def get_health_summary(self) -> Dict[str, Any]:
        """获取健康状态摘要"""
        if not self.health_history:
            return {
                "status": "no_data",
                "message": "尚未执行健康检查"
            }
        
        latest_check = self.health_history[-1]
        
        return {
            "status": "healthy" if latest_check["overall_healthy"] else "unhealthy",
            "last_check": latest_check["timestamp"],
            "summary": latest_check["summary"],
            "components_status": {
                comp["component"]: comp["healthy"] 
                for comp in latest_check["components"]
            }
        }
    
    def get_health_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取健康检查历史"""
        return self.health_history[-limit:] if self.health_history else []
