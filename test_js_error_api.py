#!/usr/bin/env python3
"""
测试JavaScript错误API端点
"""

import requests
import json
from datetime import datetime

def test_js_error_api():
    """测试JavaScript错误API端点"""
    
    # API端点
    url = "http://127.0.0.1:8888/api/v1/bug-detection/js-error"
    
    # 测试数据
    test_data = {
        "type": "test",
        "message": "测试JavaScript错误",
        "sessionId": "test-session-123",
        "pageName": "test-page",
        "url": "http://127.0.0.1:8501",
        "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "timestamp": datetime.now().isoformat(),
        "source": "test.js",
        "line": 10,
        "column": 5,
        "stack": "Error: 测试错误\n    at test.js:10:5"
    }
    
    try:
        print("🧪 测试JavaScript错误API端点...")
        print(f"📡 URL: {url}")
        print(f"📋 数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
        
        # 发送POST请求
        response = requests.post(
            url,
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"\n📊 响应状态码: {response.status_code}")
        print(f"📋 响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ API端点正常工作！")
            return True
        else:
            print("❌ API端点返回错误状态码")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务，请确保API服务正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def check_database_after_test():
    """测试后检查数据库"""
    try:
        from src.bug_detection.core.database_manager import DatabaseManager
        import sqlite3
        
        db_manager = DatabaseManager()
        conn = sqlite3.connect(db_manager.db_path)
        cursor = conn.cursor()
        
        # 检查js_errors表记录数
        cursor.execute("SELECT COUNT(*) FROM js_errors")
        count = cursor.fetchone()[0]
        print(f"\n📊 测试后js_errors记录数: {count}")
        
        if count > 0:
            # 显示最新记录
            cursor.execute("SELECT error_message, page_url, timestamp FROM js_errors ORDER BY timestamp DESC LIMIT 1")
            record = cursor.fetchone()
            print(f"📋 最新记录: {record[0]} | {record[1]} | {record[2]}")
        
        conn.close()
        return count > 0
        
    except Exception as e:
        print(f"❌ 检查数据库时出错: {e}")
        return False

if __name__ == "__main__":
    print("🔍 JavaScript错误API测试")
    print("=" * 50)
    
    # 测试API
    api_success = test_js_error_api()
    
    if api_success:
        # 检查数据库
        db_success = check_database_after_test()
        
        if db_success:
            print("\n✅ 测试完全成功！JavaScript错误已正确记录到数据库")
        else:
            print("\n⚠️ API响应成功，但数据库中没有找到记录")
    else:
        print("\n❌ API测试失败")
