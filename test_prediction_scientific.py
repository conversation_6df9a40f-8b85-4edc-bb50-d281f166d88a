#!/usr/bin/env python3
"""
福彩3D预测系统科学性验证脚本

验证预测系统的科学性和严谨性：
1. 预测动态性测试 - 验证预测结果基于最新数据动态变化
2. 参数响应性测试 - 验证用户参数正确影响预测结果
3. 数据驱动性验证 - 确保预测基于真实历史数据而非硬编码值
4. 候选数量控制验证 - 验证候选数量参数正确生效
"""

import json
import requests
import time
from typing import Dict, List, Any

# API基础URL
API_BASE_URL = "http://127.0.0.1:8888"

class PredictionScientificValidator:
    """预测系统科学性验证器"""
    
    def __init__(self):
        self.test_results = []
        self.api_base_url = API_BASE_URL
    
    def log_test(self, test_name: str, passed: bool, details: str):
        """记录测试结果"""
        result = {
            'test_name': test_name,
            'passed': passed,
            'details': details,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        self.test_results.append(result)
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}: {details}")
    
    def test_api_connectivity(self) -> bool:
        """测试API连接性"""
        try:
            response = requests.get(f"{self.api_base_url}/health", timeout=10)
            if response.status_code == 200:
                health_data = response.json()
                self.log_test(
                    "API连接性测试", 
                    True, 
                    f"API正常运行，数据库记录: {health_data.get('database_records', 'N/A')}"
                )
                return True
            else:
                self.log_test("API连接性测试", False, f"API返回状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("API连接性测试", False, f"连接失败: {str(e)}")
            return False
    
    def test_prediction_dynamics(self) -> bool:
        """测试预测动态性 - 多次调用应有不同结果或相同的科学依据"""
        try:
            predictions = []
            
            # 进行3次预测调用
            for i in range(3):
                response = requests.get(
                    f"{self.api_base_url}/api/v1/prediction/intelligent-fusion/predict",
                    params={
                        "prediction_mode": "智能融合",
                        "max_candidates": 10,
                        "confidence_threshold": 0.5,
                        "auto_train": True
                    },
                    timeout=60
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success', False):
                        prediction = result.get('prediction', {})
                        predictions.append({
                            'numbers': prediction.get('numbers', ''),
                            'confidence': prediction.get('confidence', 0),
                            'candidates_count': len(prediction.get('candidates', []))
                        })
                    else:
                        self.log_test("预测动态性测试", False, f"第{i+1}次预测失败: {result.get('error', '未知错误')}")
                        return False
                else:
                    self.log_test("预测动态性测试", False, f"第{i+1}次API调用失败: HTTP {response.status_code}")
                    return False
                
                time.sleep(2)  # 间隔2秒
            
            # 分析预测结果
            unique_numbers = set(p['numbers'] for p in predictions)
            confidence_range = max(p['confidence'] for p in predictions) - min(p['confidence'] for p in predictions)
            
            # 验证标准：要么有不同的预测号码，要么置信度有合理变化
            is_dynamic = len(unique_numbers) > 1 or confidence_range > 0.01
            
            details = f"3次预测: 号码种类={len(unique_numbers)}, 置信度范围={confidence_range:.3f}"
            self.log_test("预测动态性测试", is_dynamic, details)
            return is_dynamic
            
        except Exception as e:
            self.log_test("预测动态性测试", False, f"测试异常: {str(e)}")
            return False
    
    def test_parameter_responsiveness(self) -> bool:
        """测试参数响应性 - 不同参数应产生不同结果"""
        try:
            # 测试不同候选数量
            test_cases = [
                {"max_candidates": 5, "confidence_threshold": 0.3},
                {"max_candidates": 20, "confidence_threshold": 0.3},
                {"max_candidates": 10, "confidence_threshold": 0.7}
            ]
            
            results = []
            
            for i, params in enumerate(test_cases):
                response = requests.get(
                    f"{self.api_base_url}/api/v1/prediction/intelligent-fusion/predict",
                    params={
                        "prediction_mode": "智能融合",
                        "auto_train": True,
                        **params
                    },
                    timeout=60
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success', False):
                        prediction = result.get('prediction', {})
                        candidates_count = len(prediction.get('candidates', []))
                        results.append({
                            'params': params,
                            'candidates_count': candidates_count,
                            'confidence': prediction.get('confidence', 0)
                        })
                    else:
                        self.log_test("参数响应性测试", False, f"测试用例{i+1}预测失败")
                        return False
                else:
                    self.log_test("参数响应性测试", False, f"测试用例{i+1}API调用失败")
                    return False
            
            # 验证候选数量响应性
            candidates_5 = results[0]['candidates_count']
            candidates_20 = results[1]['candidates_count']
            candidates_filtered = results[2]['candidates_count']
            
            # 期望：20个候选 > 5个候选，高置信度阈值会过滤掉一些候选
            quantity_responsive = candidates_20 >= candidates_5
            threshold_responsive = candidates_filtered <= candidates_20
            
            details = f"候选数量: 5个参数→{candidates_5}个, 20个参数→{candidates_20}个, 高阈值→{candidates_filtered}个"
            is_responsive = quantity_responsive and threshold_responsive
            
            self.log_test("参数响应性测试", is_responsive, details)
            return is_responsive
            
        except Exception as e:
            self.log_test("参数响应性测试", False, f"测试异常: {str(e)}")
            return False
    
    def test_data_driven_prediction(self) -> bool:
        """测试数据驱动性 - 确保预测基于真实数据"""
        try:
            response = requests.get(
                f"{self.api_base_url}/api/v1/prediction/intelligent-fusion/predict",
                params={
                    "prediction_mode": "智能融合",
                    "max_candidates": 15,
                    "confidence_threshold": 0.4,
                    "auto_train": True
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success', False):
                    prediction = result.get('prediction', {})
                    
                    # 检查是否包含融合信息
                    has_fusion_info = 'fusion_info' in prediction
                    
                    # 检查预测号码是否为硬编码的测试值
                    predicted_number = prediction.get('numbers', '')
                    is_not_hardcoded = predicted_number not in ['000', '123', '456', '789', '016']
                    
                    # 检查候选数量是否合理
                    candidates = prediction.get('candidates', [])
                    has_reasonable_candidates = len(candidates) > 0
                    
                    # 检查置信度是否在合理范围
                    confidence = prediction.get('confidence', 0)
                    has_reasonable_confidence = 0.1 <= confidence <= 1.0
                    
                    is_data_driven = (has_fusion_info and is_not_hardcoded and 
                                    has_reasonable_candidates and has_reasonable_confidence)
                    
                    details = (f"融合信息: {has_fusion_info}, 非硬编码: {is_not_hardcoded}, "
                             f"候选数量: {len(candidates)}, 置信度: {confidence:.3f}")
                    
                    self.log_test("数据驱动性验证", is_data_driven, details)
                    return is_data_driven
                else:
                    self.log_test("数据驱动性验证", False, f"预测失败: {result.get('error', '未知错误')}")
                    return False
            else:
                self.log_test("数据驱动性验证", False, f"API调用失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("数据驱动性验证", False, f"测试异常: {str(e)}")
            return False
    
    def test_candidate_quantity_control(self) -> bool:
        """测试候选数量控制 - 验证用户设置的候选数量参数生效"""
        try:
            test_quantities = [5, 10, 20, 30]
            results = []
            
            for quantity in test_quantities:
                response = requests.get(
                    f"{self.api_base_url}/api/v1/prediction/intelligent-fusion/predict",
                    params={
                        "prediction_mode": "智能融合",
                        "max_candidates": quantity,
                        "confidence_threshold": 0.1,  # 低阈值确保不被过滤
                        "auto_train": True
                    },
                    timeout=60
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success', False):
                        prediction = result.get('prediction', {})
                        actual_count = len(prediction.get('candidates', []))
                        results.append({
                            'requested': quantity,
                            'actual': actual_count
                        })
                    else:
                        self.log_test("候选数量控制测试", False, f"数量{quantity}预测失败")
                        return False
                else:
                    self.log_test("候选数量控制测试", False, f"数量{quantity}API调用失败")
                    return False
            
            # 验证候选数量控制
            all_correct = True
            details_parts = []
            
            for result in results:
                requested = result['requested']
                actual = result['actual']
                # 允许一定的误差，但应该接近请求的数量
                is_correct = abs(actual - requested) <= 2  # 允许±2的误差
                all_correct = all_correct and is_correct
                details_parts.append(f"{requested}→{actual}")
            
            details = f"候选数量控制: {', '.join(details_parts)}"
            self.log_test("候选数量控制测试", all_correct, details)
            return all_correct
            
        except Exception as e:
            self.log_test("候选数量控制测试", False, f"测试异常: {str(e)}")
            return False
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有科学性验证测试"""
        print("🧪 开始福彩3D预测系统科学性验证...")
        print("=" * 60)
        
        # 检查API连接
        if not self.test_api_connectivity():
            print("❌ API连接失败，无法继续测试")
            return self.generate_report()
        
        # 运行所有测试
        tests = [
            ("预测动态性", self.test_prediction_dynamics),
            ("参数响应性", self.test_parameter_responsiveness),
            ("数据驱动性", self.test_data_driven_prediction),
            ("候选数量控制", self.test_candidate_quantity_control)
        ]
        
        for test_name, test_func in tests:
            print(f"\n🔬 执行 {test_name} 测试...")
            test_func()
        
        return self.generate_report()
    
    def generate_report(self) -> Dict[str, Any]:
        """生成验证报告"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r['passed'])
        
        report = {
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': total_tests - passed_tests,
                'success_rate': passed_tests / total_tests if total_tests > 0 else 0
            },
            'test_results': self.test_results,
            'scientific_validation': passed_tests >= total_tests * 0.8,  # 80%通过率
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        print("\n" + "=" * 60)
        print("📊 科学性验证报告")
        print("=" * 60)
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {total_tests - passed_tests}")
        print(f"成功率: {report['summary']['success_rate']:.1%}")
        
        if report['scientific_validation']:
            print("✅ 预测系统科学性验证通过")
        else:
            print("❌ 预测系统科学性验证失败")
        
        return report

def main():
    """主函数"""
    validator = PredictionScientificValidator()
    report = validator.run_all_tests()
    
    # 保存报告
    with open('prediction_scientific_validation_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细报告已保存到: prediction_scientific_validation_report.json")
    
    return report['scientific_validation']

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
