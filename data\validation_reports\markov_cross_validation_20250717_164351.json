{"model_params": {"transition_window_size": 1000, "probability_window_size": 500, "smoothing_alpha": 1.0}, "validation_params": {"k_folds": 2, "data_limit": 200}, "fold_results": [{"fold_idx": 0, "train_size": 100, "val_size": 100, "predictions_count": 100, "accuracy_metrics": {"exact_match": 0.0, "digit_accuracy": 0.22, "position_accuracy": [0.12, 0.1, 0.13], "total_predictions": 100}, "diversity_metrics": {"simpson_diversity": 0.9878, "unique_ratio": 0.9, "entropy": 6.436307314753103, "unique_count": 90, "total_count": 100}, "stability_metrics": {"variance": 92305.7475, "std_dev": 303.81860953536074, "coefficient_of_variation": 0.5873728555541049, "mean": 517.25}, "aic_bic": {"aic": 4825.170185988091, "bic": 5111.738906446782, "log_likelihood": -2302.5850929940457, "num_params": 110, "num_samples": 100}}], "overall_results": {"accuracy_metrics": {"exact_match": 0.0, "digit_accuracy": 0.22, "position_accuracy": [0.12, 0.1, 0.13], "total_predictions": 100}, "diversity_metrics": {"simpson_diversity": 0.9878, "unique_ratio": 0.9, "entropy": 6.436307314753103, "unique_count": 90, "total_count": 100}, "stability_metrics": {"variance": 92305.7475, "std_dev": 303.81860953536074, "coefficient_of_variation": 0.5873728555541049, "mean": 517.25}, "aic_bic": {"aic": 4825.170185988091, "bic": 5111.738906446782, "log_likelihood": -2302.5850929940457, "num_params": 110, "num_samples": 100}, "total_predictions": 100}, "metadata": {"generated_at": "2025-07-17T16:43:51.100558", "validator_version": "1.0", "database_path": "D:\\github\\3dyuce\\data\\lottery.db"}}