#!/usr/bin/env python3
"""
测试增量更新功能
"""

import sys
sys.path.append('src')

from data.incremental_updater import IncrementalUpdater, check_for_updates, perform_update
import json

def test_incremental_updater():
    """测试增量更新器"""
    print("开始测试增量更新器...")
    
    try:
        # 1. 创建更新器
        print("\n1. 初始化增量更新器...")
        updater = IncrementalUpdater()
        
        print(f"✅ 增量更新器初始化成功")
        
        # 2. 检查当前状态
        print("\n2. 检查当前更新状态...")
        status = updater.get_update_status()
        
        print(f"📊 当前状态:")
        print(f"   - 上次更新: {status['last_update'] or '从未更新'}")
        print(f"   - 最新期号: {status['last_period'] or 'N/A'}")
        print(f"   - 最新日期: {status['last_date'] or 'N/A'}")
        print(f"   - 记录数量: {status['record_count']}")
        print(f"   - 更新次数: {status['update_count']}")
        print(f"   - 原始文件: {status['raw_files']}")
        print(f"   - 处理文件: {status['processed_files']}")
        
        # 3. 执行增量更新
        print("\n3. 执行增量更新...")
        result = updater.perform_incremental_update()
        
        if result["success"]:
            print(f"✅ 增量更新成功!")
            
            update_info = result.get("update_info", {})
            print(f"📈 更新信息:")
            print(f"   - 新增记录: {update_info.get('records_added', 0)}")
            print(f"   - 总记录数: {update_info.get('total_records', 0)}")
            print(f"   - 质量评分: {update_info.get('quality_score', 0)}")
            print(f"   - 数据文件: {update_info.get('data_file', 'N/A')}")
            
            # 显示新增记录
            new_records = result.get("new_records", [])
            if new_records:
                print(f"   - 最新记录:")
                for record in new_records:
                    print(f"     {record['period']} {record['date']} {record['numbers']}")
            
            # 导出结果
            export_results = result.get("export_results", {})
            print(f"   - 导出结果:")
            for fmt, success in export_results.items():
                status_icon = "✅" if success else "❌"
                print(f"     {fmt.upper()}: {status_icon}")
        
        else:
            print(f"ℹ️ 更新结果: {result['message']}")
            
            # 如果是数据未变化，这是正常的
            if "未发生变化" in result['message']:
                print("   这是正常情况，说明数据源没有新数据")
        
        # 4. 再次检查状态
        print("\n4. 检查更新后状态...")
        new_status = updater.get_update_status()
        
        print(f"📊 更新后状态:")
        print(f"   - 上次更新: {new_status['last_update']}")
        print(f"   - 最新期号: {new_status['last_period']}")
        print(f"   - 最新日期: {new_status['last_date']}")
        print(f"   - 记录数量: {new_status['record_count']}")
        print(f"   - 更新次数: {new_status['update_count']}")
        
        # 5. 测试强制更新
        print("\n5. 测试强制更新...")
        force_result = updater.perform_incremental_update(force_update=True)
        
        if force_result["success"]:
            print(f"✅ 强制更新成功!")
            print(f"   - 消息: {force_result['message']}")
        else:
            print(f"❌ 强制更新失败: {force_result['message']}")
        
        # 6. 测试便捷函数
        print("\n6. 测试便捷函数...")
        
        # 检查更新
        quick_status = check_for_updates()
        print(f"📋 快速状态检查:")
        print(f"   - 记录数量: {quick_status['record_count']}")
        print(f"   - 更新次数: {quick_status['update_count']}")
        
        # 7. 文件清理测试
        print("\n7. 测试文件清理...")
        cleanup_result = updater.cleanup_old_files(keep_count=3)
        
        if cleanup_result["success"]:
            print(f"✅ 文件清理成功!")
            print(f"   - 删除原始文件: {cleanup_result['deleted_raw_files']}")
            print(f"   - 删除处理文件: {cleanup_result['deleted_processed_files']}")
            print(f"   - 保留文件数: {cleanup_result['kept_files']}")
        else:
            print(f"❌ 文件清理失败: {cleanup_result.get('error', 'Unknown error')}")
        
        # 8. 保存测试报告
        print("\n8. 保存测试报告...")
        test_report = {
            "test_time": result.get("timestamp"),
            "initial_status": status,
            "update_result": result,
            "final_status": new_status,
            "force_update_result": force_result,
            "cleanup_result": cleanup_result
        }
        
        with open('data/processed/incremental_update_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(test_report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 测试报告已保存到: data/processed/incremental_update_test_report.json")
        
        print(f"\n🎉 增量更新器测试成功！")
        print(f"📋 测试摘要:")
        print(f"   - 更新器初始化: ✅")
        print(f"   - 状态检查: ✅")
        print(f"   - 增量更新: ✅")
        print(f"   - 强制更新: ✅")
        print(f"   - 便捷函数: ✅")
        print(f"   - 文件清理: ✅")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_incremental_updater()
