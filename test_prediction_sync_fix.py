#!/usr/bin/env python3
"""
测试预测模型数据同步修复效果
"""

import requests
import json
import time
from datetime import datetime

API_BASE_URL = "http://127.0.0.1:8888"

def test_database_record_count():
    """测试数据库记录数获取"""
    print("🔍 测试1: 数据库记录数获取")
    try:
        response = requests.get(f"{API_BASE_URL}/api/v1/data/stats", timeout=10)
        if response.status_code == 200:
            stats = response.json()
            total_records = stats.get('total_records', 0)
            print(f"✅ 数据库记录数: {total_records}")
            return total_records
        else:
            print(f"❌ 获取数据库状态失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 数据库状态检查异常: {e}")
        return None

def test_intelligent_fusion_system():
    """测试智能融合系统动态数据计数"""
    print("\n🔍 测试2: 智能融合系统动态数据计数")
    try:
        from prediction.intelligent_fusion import IntelligentFusionSystem
        system = IntelligentFusionSystem()
        
        # 测试动态获取数据计数
        current_count = system._get_current_data_count()
        print(f"✅ 动态获取数据计数: {current_count}")
        
        # 测试数据变化检查
        data_changed = system._check_data_changed()
        print(f"✅ 数据变化检查: {'有变化' if data_changed else '无变化'}")
        
        return current_count
    except Exception as e:
        print(f"❌ 智能融合系统测试异常: {e}")
        return None

def test_manual_retrain():
    """测试手动重训练功能"""
    print("\n🔍 测试3: 手动重训练功能")
    try:
        response = requests.post(
            f"{API_BASE_URL}/api/v1/prediction/intelligent-fusion/train",
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 重训练API调用成功")
            print(f"   训练结果: {result.get('success', False)}")
            if 'training_data_count' in result:
                print(f"   训练数据量: {result['training_data_count']}")
            return result
        else:
            print(f"❌ 重训练API调用失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 重训练测试异常: {e}")
        return None

def test_prediction_with_latest_data():
    """测试使用最新数据的预测"""
    print("\n🔍 测试4: 使用最新数据的预测")
    try:
        response = requests.get(
            f"{API_BASE_URL}/api/v1/prediction/intelligent-fusion/predict",
            params={
                "prediction_mode": "智能融合",
                "max_candidates": 10,
                "confidence_threshold": 0.5,
                "auto_train": True
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success', False):
                prediction = result.get('prediction', {})
                predicted_number = prediction.get('predicted_number', 'N/A')
                confidence = prediction.get('confidence', 0)
                data_count = result.get('data_count', 0)
                
                print(f"✅ 预测成功")
                print(f"   预测号码: {predicted_number}")
                print(f"   置信度: {confidence:.1%}")
                print(f"   使用数据量: {data_count}")
                
                # 检查是否还是固定的056
                if predicted_number == "056":
                    print("⚠️  警告: 预测结果仍为056，可能存在问题")
                else:
                    print("✅ 预测结果已更新，不再固定为056")
                
                return result
            else:
                print(f"❌ 预测失败: {result.get('error', '未知错误')}")
                return None
        else:
            print(f"❌ 预测API调用失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 预测测试异常: {e}")
        return None

def test_auto_retrain_mechanism():
    """测试自动重训练机制"""
    print("\n🔍 测试5: 自动重训练机制")
    try:
        # 触发数据更新
        response = requests.post(
            f"{API_BASE_URL}/api/v1/data/update/trigger",
            params={"force_update": False},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 数据更新触发成功")
            print(f"   更新状态: {result.get('status', 'unknown')}")
            if 'records_added' in result:
                print(f"   新增记录: {result['records_added']}")
            return result
        else:
            print(f"❌ 数据更新触发失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 自动重训练测试异常: {e}")
        return None

def main():
    """主测试函数"""
    print("🚀 开始测试预测模型数据同步修复效果")
    print("=" * 60)
    
    # 测试1: 数据库记录数
    db_count = test_database_record_count()
    
    # 测试2: 智能融合系统
    system_count = test_intelligent_fusion_system()
    
    # 测试3: 手动重训练
    retrain_result = test_manual_retrain()
    
    # 测试4: 预测功能
    prediction_result = test_prediction_with_latest_data()
    
    # 测试5: 自动重训练机制
    auto_retrain_result = test_auto_retrain_mechanism()
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    if db_count and system_count:
        if db_count == system_count:
            print("✅ 数据同步正常: 数据库和系统记录数一致")
        else:
            print(f"⚠️  数据同步异常: 数据库({db_count}) vs 系统({system_count})")
    
    if retrain_result and retrain_result.get('success'):
        print("✅ 手动重训练功能正常")
    else:
        print("❌ 手动重训练功能异常")
    
    if prediction_result and prediction_result.get('success'):
        predicted = prediction_result.get('prediction', {}).get('predicted_number', '')
        if predicted != "056":
            print("✅ 预测结果已更新，修复成功")
        else:
            print("❌ 预测结果仍为056，修复可能未完全生效")
    else:
        print("❌ 预测功能异常")
    
    if auto_retrain_result:
        print("✅ 自动重训练机制可用")
    else:
        print("❌ 自动重训练机制异常")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
