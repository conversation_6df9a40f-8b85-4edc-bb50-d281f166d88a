#!/usr/bin/env python3
"""
降级机制管理器
创建日期: 2025年7月25日
用途: 统一管理WebSocket不可用时的降级机制，提供API轮询、缓存和友好提示
"""

import asyncio
import logging
import time
from typing import Any, Callable, Dict, List, Optional

import requests
import streamlit as st

logger = logging.getLogger(__name__)

class FallbackManager:
    """降级机制管理器"""
    
    def __init__(self):
        self.api_base_url = "http://127.0.0.1:8888"
        self.polling_intervals = {}
        self.cache_storage = {}
        self.cache_ttl = 300  # 5分钟缓存
        self.max_retries = 3
        self.retry_delay = 2  # 秒
        
    def enable_api_polling(self, endpoint: str, interval: int = 10, callback: Optional[Callable] = None):
        """启用API轮询降级机制"""
        try:
            if endpoint in self.polling_intervals:
                return  # 已经启用
            
            logger.info(f"🔄 启用API轮询降级: {endpoint}")
            
            # 创建轮询任务
            async def polling_task():
                while endpoint in self.polling_intervals:
                    try:
                        data = await self._fetch_api_data(endpoint)
                        if data and callback:
                            callback(data)
                        await asyncio.sleep(interval)
                    except Exception as e:
                        logger.warning(f"API轮询失败 {endpoint}: {e}")
                        await asyncio.sleep(interval * 2)  # 失败时延长间隔
            
            # 启动轮询任务
            self.polling_intervals[endpoint] = asyncio.create_task(polling_task())
            
        except Exception as e:
            logger.error(f"启用API轮询失败: {e}")
    
    def disable_api_polling(self, endpoint: str):
        """禁用API轮询"""
        if endpoint in self.polling_intervals:
            task = self.polling_intervals.pop(endpoint)
            task.cancel()
            logger.info(f"⏹️ 禁用API轮询: {endpoint}")
    
    async def _fetch_api_data(self, endpoint: str) -> Optional[Dict[str, Any]]:
        """获取API数据"""
        for attempt in range(self.max_retries):
            try:
                response = requests.get(
                    f"{self.api_base_url}{endpoint}",
                    timeout=10
                )
                if response.status_code == 200:
                    data = response.json()
                    # 更新缓存
                    self._update_cache(endpoint, data)
                    return data
                else:
                    logger.warning(f"API请求失败 {endpoint}: {response.status_code}")
                    
            except Exception as e:
                logger.warning(f"API请求异常 {endpoint} (尝试 {attempt + 1}): {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.retry_delay)
        
        return None
    
    def _update_cache(self, key: str, data: Any):
        """更新缓存"""
        self.cache_storage[key] = {
            'data': data,
            'timestamp': time.time()
        }
    
    def get_cached_data(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        if key in self.cache_storage:
            cache_entry = self.cache_storage[key]
            if time.time() - cache_entry['timestamp'] < self.cache_ttl:
                return cache_entry['data']
            else:
                # 缓存过期，删除
                del self.cache_storage[key]
        return None
    
    def get_data_with_fallback(self, primary_func: Callable, fallback_func: Optional[Callable] = None, 
                              cache_key: Optional[str] = None) -> Any:
        """带降级机制的数据获取"""
        try:
            # 尝试主要数据源
            data = primary_func()
            if data is not None:
                if cache_key:
                    self._update_cache(cache_key, data)
                return data
        except Exception as e:
            logger.warning(f"主要数据源失败: {e}")
        
        # 尝试降级数据源
        if fallback_func:
            try:
                data = fallback_func()
                if data is not None:
                    st.info("⚠️ 使用降级数据源")
                    return data
            except Exception as e:
                logger.warning(f"降级数据源失败: {e}")
        
        # 尝试缓存数据
        if cache_key:
            cached_data = self.get_cached_data(cache_key)
            if cached_data is not None:
                st.warning("⚠️ 使用缓存数据（可能不是最新）")
                return cached_data
        
        return None
    
    def show_friendly_error(self, error_type: str, message: str = None, 
                           recovery_actions: List[str] = None):
        """显示友好的错误提示"""
        error_configs = {
            'websocket_disconnected': {
                'icon': '🔌',
                'title': 'WebSocket连接中断',
                'default_message': '实时连接已中断，正在使用API轮询模式',
                'actions': ['刷新页面', '检查网络连接', '联系技术支持']
            },
            'api_timeout': {
                'icon': '⏱️',
                'title': 'API请求超时',
                'default_message': '服务响应较慢，请稍后重试',
                'actions': ['重新加载', '检查服务状态', '稍后重试']
            },
            'data_unavailable': {
                'icon': '📊',
                'title': '数据暂时不可用',
                'default_message': '数据服务暂时不可用，正在尝试恢复',
                'actions': ['刷新数据', '检查数据源', '使用缓存数据']
            },
            'service_error': {
                'icon': '⚠️',
                'title': '服务异常',
                'default_message': '服务出现异常，正在自动恢复',
                'actions': ['重试操作', '检查系统状态', '联系管理员']
            }
        }
        
        config = error_configs.get(error_type, error_configs['service_error'])
        display_message = message or config['default_message']
        actions = recovery_actions or config['actions']
        
        st.error(f"{config['icon']} **{config['title']}**")
        st.write(display_message)
        
        if actions:
            st.write("**建议操作：**")
            for i, action in enumerate(actions, 1):
                st.write(f"{i}. {action}")
    
    def create_retry_button(self, label: str = "重试", key: str = None) -> bool:
        """创建重试按钮"""
        return st.button(f"🔄 {label}", key=key)
    
    def show_fallback_status(self, websocket_connected: bool = False,
                           api_available: bool = True, cache_used: bool = False):
        """显示降级状态信息"""
        col1, col2, col3 = st.columns(3)

        with col1:
            if websocket_connected:
                st.success("🔗 WebSocket: 已连接")
                st.info("✨ 实时功能已启用：Bug检测、训练监控、状态推送")
            else:
                st.warning("🔗 WebSocket: 已断开")
                st.info("🔄 已启用降级模式：使用API轮询，功能正常")

                # 添加重连按钮
                if st.button("🔄 尝试重新连接", key="reconnect_websocket"):
                    st.rerun()

        with col2:
            if api_available:
                st.success("🌐 API: 可用")
            else:
                st.error("🌐 API: 不可用")

        with col3:
            if cache_used:
                st.info("💾 数据: 缓存")
            else:
                st.success("💾 数据: 实时")
    
    def cleanup(self):
        """清理资源"""
        # 停止所有轮询任务
        for endpoint in list(self.polling_intervals.keys()):
            self.disable_api_polling(endpoint)
        
        # 清理缓存
        self.cache_storage.clear()
        
        logger.info("🧹 降级机制管理器已清理")

# 全局降级管理器实例
fallback_manager = FallbackManager()

def get_fallback_manager() -> FallbackManager:
    """获取降级管理器实例"""
    return fallback_manager

def enable_websocket_fallback(endpoint: str, callback: Optional[Callable] = None):
    """启用WebSocket降级机制的便捷函数"""
    fallback_manager.enable_api_polling(endpoint, interval=10, callback=callback)

def show_connection_status(websocket_connected: bool = False):
    """显示连接状态的便捷函数"""
    fallback_manager.show_fallback_status(websocket_connected=websocket_connected)

if __name__ == "__main__":
    # 测试代码
    st.title("降级机制管理器测试")
    
    manager = get_fallback_manager()
    
    # 显示状态
    manager.show_fallback_status(
        websocket_connected=False,
        api_available=True,
        cache_used=True
    )
    
    # 显示错误提示
    manager.show_friendly_error('websocket_disconnected')
    
    # 创建重试按钮
    if manager.create_retry_button("重新连接"):
        st.success("正在重新连接...")
