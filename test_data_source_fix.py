#!/usr/bin/env python3
"""
测试修复后的数据源检查功能
"""

import requests

def test_data_source_check():
    """测试修复后的数据源检查逻辑"""
    data_source_url = 'https://data.17500.cn/3d_asc.txt'
    
    print('测试修复后的数据源检查逻辑...')
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://data.17500.cn/'
        }
        
        response = requests.get(data_source_url, headers=headers, timeout=15, stream=True)
        
        print(f'状态码: {response.status_code}')
        print(f'响应时间: {response.elapsed.total_seconds():.2f}秒')
        print(f'内容类型: {response.headers.get("content-type", "unknown")}')
        print(f'文件大小: {response.headers.get("content-length", "unknown")} 字节')
        
        if response.status_code == 200:
            # 只读取前几行来验证数据格式
            content_preview = ''
            line_count = 0
            for line in response.iter_lines(decode_unicode=True):
                if line_count < 3:
                    content_preview += line + '\n'
                    line_count += 1
                else:
                    break
            
            print('数据预览（前3行）:')
            print(content_preview.strip())
            print('✅ 数据源检查成功！')
            return True
        elif response.status_code == 429:
            print('⚠️ 数据源请求频率限制')
            print('💡 建议：等待1-2分钟后重试')
            return False
        else:
            print(f'❌ 状态码异常: {response.status_code}')
            return False
            
    except requests.exceptions.Timeout:
        print('❌ 请求超时，数据源响应缓慢')
        return False
    except requests.exceptions.ConnectionError:
        print('❌ 网络连接错误，无法访问数据源')
        return False
    except Exception as e:
        error_msg = str(e)
        if '429' in error_msg or 'Too Many Requests' in error_msg:
            print('⚠️ 数据源请求频率限制')
            print('💡 建议：等待1-2分钟后重试')
        else:
            print(f'❌ 检查失败: {error_msg}')
        return False

def test_head_vs_get():
    """对比HEAD和GET请求的差异"""
    data_source_url = 'https://data.17500.cn/3d_asc.txt'
    
    print('\n=== 对比HEAD和GET请求 ===')
    
    # 测试HEAD请求
    print('1. 测试HEAD请求:')
    try:
        response = requests.head(data_source_url, timeout=10)
        print(f'   HEAD状态码: {response.status_code}')
        if response.status_code != 200:
            print(f'   HEAD响应头: {dict(response.headers)}')
    except Exception as e:
        print(f'   HEAD请求失败: {e}')
    
    # 测试GET请求
    print('2. 测试GET请求:')
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://data.17500.cn/'
        }
        response = requests.get(data_source_url, headers=headers, timeout=10)
        print(f'   GET状态码: {response.status_code}')
        if response.status_code == 200:
            lines = response.text.strip().split('\n')
            print(f'   数据行数: {len(lines)}')
    except Exception as e:
        print(f'   GET请求失败: {e}')

def test_anti_crawler_strategies():
    """测试反爬虫策略"""
    data_source_url = 'https://data.17500.cn/3d_asc.txt'
    
    print('\n=== 测试反爬虫策略 ===')
    
    # 测试1：无User-Agent
    print('1. 测试无User-Agent:')
    try:
        response = requests.get(data_source_url, timeout=10)
        print(f'   状态码: {response.status_code}')
    except Exception as e:
        print(f'   失败: {e}')
    
    # 测试2：简单User-Agent
    print('2. 测试简单User-Agent:')
    try:
        headers = {'User-Agent': 'Python/requests'}
        response = requests.get(data_source_url, headers=headers, timeout=10)
        print(f'   状态码: {response.status_code}')
    except Exception as e:
        print(f'   失败: {e}')
    
    # 测试3：完整浏览器User-Agent
    print('3. 测试完整浏览器User-Agent:')
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://data.17500.cn/'
        }
        response = requests.get(data_source_url, headers=headers, timeout=10)
        print(f'   状态码: {response.status_code}')
        if response.status_code == 200:
            print(f'   ✅ 成功访问')
    except Exception as e:
        print(f'   失败: {e}')

if __name__ == "__main__":
    print("开始测试数据源访问修复...")
    print("=" * 50)
    
    # 主要测试
    success = test_data_source_check()
    
    # 详细分析
    test_head_vs_get()
    test_anti_crawler_strategies()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 数据源检查修复成功！")
        print("💡 关键发现：")
        print("   - HEAD请求被服务器限制（返回429）")
        print("   - GET请求正常工作（返回200）")
        print("   - 需要完整的浏览器请求头避免反爬虫")
    else:
        print("⚠️ 数据源检查仍有问题，需要进一步调试")
