#!/usr/bin/env python3
"""
测试数据库修复效果
"""

import os
import sys

sys.path.append('.')

import sqlite3

from src.bug_detection.core.database_manager import DatabaseManager


def test_database_fix():
    """测试数据库修复效果"""
    print("🔧 测试数据库修复效果...")
    
    try:
        # 删除旧的数据库文件（如果存在）
        db_paths = ["data/bug_detection.db", "lottery_data.db"]
        for db_path in db_paths:
            if os.path.exists(db_path):
                os.remove(db_path)
                print(f"🗑️ 删除旧数据库文件: {db_path}")
        
        # 创建新的数据库管理器
        print("📊 创建新的数据库管理器...")
        db_manager = DatabaseManager()
        
        print(f"✅ 数据库路径: {db_manager.db_path}")
        
        # 检查数据库表是否正确创建
        conn = sqlite3.connect(db_manager.db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        table_names = [table[0] for table in tables]
        
        print(f"📋 创建的表: {table_names}")
        
        # 检查关键表是否存在
        required_tables = ['bug_reports', 'realtime_events', 'anomaly_alerts', 'bug_patterns']
        missing_tables = []
        
        for table in required_tables:
            if table not in table_names:
                missing_tables.append(table)
        
        if missing_tables:
            print(f"❌ 缺失的表: {missing_tables}")
            return False
        else:
            print("✅ 所有必需的表都已创建")
        
        # 检查索引是否正确创建
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index'")
        indexes = cursor.fetchall()
        index_names = [index[0] for index in indexes]
        
        print(f"📇 创建的索引: {index_names}")
        
        # 测试插入数据
        print("🧪 测试数据插入...")
        
        # 测试realtime_events表
        test_event = {
            'id': 'test_event_001',
            'type': 'javascript_error',  # 注意：这里应该是'type'而不是'event_type'
            'priority': 2,
            'source': 'test_script',
            'timestamp': 1753381000.0,
            'data': {"message": "测试错误"},  # 直接传递字典，不需要JSON字符串
            'tags': ["test"],  # 直接传递列表，不需要JSON字符串
            'correlation_id': 'test_correlation'
        }
        
        success = db_manager.save_realtime_event(test_event)
        if success:
            print("✅ realtime_events表插入测试成功")
        else:
            print("❌ realtime_events表插入测试失败")
            return False
        
        # 测试查询数据
        events = db_manager.get_realtime_events(limit=5)
        print(f"📊 查询到 {len(events)} 个事件")
        
        if events:
            print("✅ 数据查询测试成功")
            for event in events:
                print(f"  - 事件ID: {event.get('id')}, 类型: {event.get('event_type')}")
        
        conn.close()
        
        print("🎉 数据库修复测试完成，所有功能正常！")
        return True
        
    except Exception as e:
        print(f"❌ 数据库修复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_database_fix()
    if success:
        print("\n✅ 数据库修复成功，可以重启服务了！")
    else:
        print("\n❌ 数据库修复失败，需要进一步检查！")
