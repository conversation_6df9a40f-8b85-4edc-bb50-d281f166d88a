"""
模型训练器
实现完整的训练循环、验证流程、早停机制等
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from typing import Dict, List, Tuple, Optional, Callable
import time
import os
from datetime import datetime
import json

from .model_utils import ModelUtils, EarlyStopping


class ModelTrainer:
    """模型训练器"""
    
    def __init__(self, model: nn.Module, config: Dict):
        self.model = model
        self.config = config
        self.device = config.get('device', 'cuda' if torch.cuda.is_available() else 'cpu')
        
        # 移动模型到设备
        self.model.to(self.device)
        
        # 训练历史
        self.train_losses = []
        self.val_losses = []
        self.train_accuracies = []
        self.val_accuracies = []
        
        # 最佳模型状态
        self.best_val_loss = float('inf')
        self.best_val_accuracy = 0.0
        self.best_model_state = None
        
        # 设置优化器
        self.optimizer = self._setup_optimizer()
        
        # 设置损失函数
        self.criterion = nn.CrossEntropyLoss()
        
        # 设置学习率调度器
        self.scheduler = self._setup_scheduler()
        
        # 早停机制
        self.early_stopping = EarlyStopping(
            patience=config.get('early_stopping_patience', 10),
            min_delta=config.get('min_delta', 0.001),
            restore_best_weights=True
        )
        
        print(f"训练器初始化完成，使用设备: {self.device}")
        print(f"模型参数数量: {sum(p.numel() for p in self.model.parameters()):,}")
    
    def _setup_optimizer(self) -> optim.Optimizer:
        """设置优化器"""
        optimizer_type = self.config.get('optimizer', 'adam')
        learning_rate = self.config.get('learning_rate', 0.001)
        weight_decay = self.config.get('weight_decay', 1e-4)
        
        if optimizer_type.lower() == 'adam':
            return optim.Adam(self.model.parameters(), lr=learning_rate, weight_decay=weight_decay)
        elif optimizer_type.lower() == 'sgd':
            momentum = self.config.get('momentum', 0.9)
            return optim.SGD(self.model.parameters(), lr=learning_rate, 
                           momentum=momentum, weight_decay=weight_decay)
        elif optimizer_type.lower() == 'adamw':
            return optim.AdamW(self.model.parameters(), lr=learning_rate, weight_decay=weight_decay)
        else:
            raise ValueError(f"不支持的优化器类型: {optimizer_type}")
    
    def _setup_scheduler(self) -> Optional[optim.lr_scheduler._LRScheduler]:
        """设置学习率调度器"""
        scheduler_type = self.config.get('scheduler', 'step')
        
        if scheduler_type == 'step':
            step_size = self.config.get('step_size', 30)
            gamma = self.config.get('gamma', 0.1)
            return optim.lr_scheduler.StepLR(self.optimizer, step_size=step_size, gamma=gamma)
        elif scheduler_type == 'cosine':
            T_max = self.config.get('num_epochs', 100)
            return optim.lr_scheduler.CosineAnnealingLR(self.optimizer, T_max=T_max)
        elif scheduler_type == 'plateau':
            return optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer, mode='min', factor=0.5, patience=5, verbose=True
            )
        elif scheduler_type is None or scheduler_type == 'none':
            return None
        else:
            raise ValueError(f"不支持的调度器类型: {scheduler_type}")
    
    def train_epoch(self, train_loader: DataLoader) -> Tuple[float, float]:
        """训练一个epoch"""
        self.model.train()
        
        total_loss = 0.0
        correct_predictions = 0
        total_samples = 0
        
        for batch_idx, (data, targets) in enumerate(train_loader):
            data, targets = data.to(self.device), targets.to(self.device)
            
            # 前向传播
            self.optimizer.zero_grad()
            outputs = self.model(data)
            loss = self.criterion(outputs, targets)
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            max_grad_norm = self.config.get('max_grad_norm', 1.0)
            if max_grad_norm > 0:
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_grad_norm)
            
            self.optimizer.step()
            
            # 统计
            total_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total_samples += targets.size(0)
            correct_predictions += (predicted == targets).sum().item()
            
            # 打印进度
            if batch_idx % 10 == 0:
                print(f'  批次 {batch_idx}/{len(train_loader)}, '
                      f'损失: {loss.item():.4f}, '
                      f'准确率: {100. * correct_predictions / total_samples:.2f}%')
        
        avg_loss = total_loss / len(train_loader)
        accuracy = correct_predictions / total_samples
        
        return avg_loss, accuracy
    
    def validate_epoch(self, val_loader: DataLoader) -> Tuple[float, float, Dict[str, float]]:
        """验证一个epoch"""
        self.model.eval()
        
        total_loss = 0.0
        correct_predictions = 0
        total_samples = 0
        top5_correct = 0
        top10_correct = 0
        
        with torch.no_grad():
            for data, targets in val_loader:
                data, targets = data.to(self.device), targets.to(self.device)
                
                outputs = self.model(data)
                loss = self.criterion(outputs, targets)
                
                total_loss += loss.item()
                total_samples += targets.size(0)
                
                # Top-1准确率
                _, predicted = torch.max(outputs.data, 1)
                correct_predictions += (predicted == targets).sum().item()
                
                # Top-5准确率
                k5 = min(5, outputs.size(1))
                if k5 > 0:
                    _, top5_pred = torch.topk(outputs, k5, dim=1)
                    for i in range(targets.size(0)):
                        if targets[i] in top5_pred[i]:
                            top5_correct += 1

                # Top-10准确率
                k10 = min(10, outputs.size(1))
                if k10 > 0:
                    _, top10_pred = torch.topk(outputs, k10, dim=1)
                    for i in range(targets.size(0)):
                        if targets[i] in top10_pred[i]:
                            top10_correct += 1
        
        avg_loss = total_loss / len(val_loader)
        accuracy = correct_predictions / total_samples
        
        metrics = {
            'top1_accuracy': accuracy,
            'top5_accuracy': top5_correct / total_samples,
            'top10_accuracy': top10_correct / total_samples,
            'loss': avg_loss
        }
        
        return avg_loss, accuracy, metrics
    
    def train(self, train_loader: DataLoader, val_loader: DataLoader, 
              num_epochs: int = None) -> Dict[str, List[float]]:
        """
        完整训练流程
        
        Args:
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            num_epochs: 训练轮数
            
        Returns:
            训练历史
        """
        if num_epochs is None:
            num_epochs = self.config.get('num_epochs', 100)
        
        print(f"开始训练，共 {num_epochs} 个epoch")
        print(f"训练集大小: {len(train_loader.dataset)}")
        print(f"验证集大小: {len(val_loader.dataset)}")
        
        start_time = time.time()
        
        for epoch in range(num_epochs):
            epoch_start_time = time.time()
            
            print(f"\nEpoch {epoch+1}/{num_epochs}")
            print("-" * 50)
            
            # 训练
            train_loss, train_acc = self.train_epoch(train_loader)
            
            # 验证
            val_loss, val_acc, val_metrics = self.validate_epoch(val_loader)
            
            # 记录历史
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.train_accuracies.append(train_acc)
            self.val_accuracies.append(val_acc)
            
            # 学习率调度
            if self.scheduler:
                if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                    self.scheduler.step(val_loss)
                else:
                    self.scheduler.step()
            
            # 保存最佳模型
            if val_acc > self.best_val_accuracy:
                self.best_val_accuracy = val_acc
                self.best_val_loss = val_loss
                self.best_model_state = self.model.state_dict().copy()
            
            # 打印epoch结果
            epoch_time = time.time() - epoch_start_time
            current_lr = self.optimizer.param_groups[0]['lr']
            
            print(f"训练损失: {train_loss:.4f}, 训练准确率: {train_acc:.4f}")
            print(f"验证损失: {val_loss:.4f}, 验证准确率: {val_acc:.4f}")
            print(f"Top-5准确率: {val_metrics['top5_accuracy']:.4f}, "
                  f"Top-10准确率: {val_metrics['top10_accuracy']:.4f}")
            print(f"学习率: {current_lr:.6f}, 用时: {epoch_time:.2f}s")
            
            # 早停检查
            if self.early_stopping(val_loss, self.model):
                print(f"\n早停触发，在第 {epoch+1} 个epoch停止训练")
                break
        
        # 恢复最佳模型
        if self.best_model_state:
            self.model.load_state_dict(self.best_model_state)
            print(f"\n已恢复最佳模型 (验证准确率: {self.best_val_accuracy:.4f})")
        
        total_time = time.time() - start_time
        print(f"\n训练完成，总用时: {total_time:.2f}s")
        
        return {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'train_accuracies': self.train_accuracies,
            'val_accuracies': self.val_accuracies
        }
    
    def save_model(self, filepath: str, include_history: bool = True):
        """保存模型"""
        config = self.config.copy()
        
        metrics = {
            'best_val_accuracy': self.best_val_accuracy,
            'best_val_loss': self.best_val_loss,
            'final_train_accuracy': self.train_accuracies[-1] if self.train_accuracies else 0,
            'final_val_accuracy': self.val_accuracies[-1] if self.val_accuracies else 0
        }
        
        if include_history:
            metrics.update({
                'train_losses': self.train_losses,
                'val_losses': self.val_losses,
                'train_accuracies': self.train_accuracies,
                'val_accuracies': self.val_accuracies
            })
        
        ModelUtils.save_model(self.model, filepath, config, metrics)
    
    def load_model(self, filepath: str):
        """加载模型"""
        model, metadata = ModelUtils.load_model(self.model, filepath)
        self.model = model
        
        # 恢复训练历史（如果存在）
        if metadata.get('metrics'):
            metrics = metadata['metrics']
            self.train_losses = metrics.get('train_losses', [])
            self.val_losses = metrics.get('val_losses', [])
            self.train_accuracies = metrics.get('train_accuracies', [])
            self.val_accuracies = metrics.get('val_accuracies', [])
            self.best_val_accuracy = metrics.get('best_val_accuracy', 0)
            self.best_val_loss = metrics.get('best_val_loss', float('inf'))
        
        return metadata


def test_trainer():
    """测试训练器"""
    print("=== 测试训练器 ===")
    
    # 创建简单的测试模型
    class SimpleModel(nn.Module):
        def __init__(self):
            super().__init__()
            self.linear = nn.Linear(10, 5)
        
        def forward(self, x):
            return self.linear(x.mean(dim=1))  # 简化处理
    
    model = SimpleModel()
    
    # 配置
    config = {
        'learning_rate': 0.001,
        'num_epochs': 5,
        'optimizer': 'adam',
        'scheduler': 'step',
        'early_stopping_patience': 3,
        'device': 'cpu'
    }
    
    # 创建训练器
    trainer = ModelTrainer(model, config)
    
    # 创建模拟数据
    from torch.utils.data import TensorDataset, DataLoader
    
    # 训练数据
    train_data = torch.randn(100, 20, 10)  # (samples, seq_len, features)
    train_targets = torch.randint(0, 5, (100,))
    train_dataset = TensorDataset(train_data, train_targets)
    train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True)
    
    # 验证数据
    val_data = torch.randn(30, 20, 10)
    val_targets = torch.randint(0, 5, (30,))
    val_dataset = TensorDataset(val_data, val_targets)
    val_loader = DataLoader(val_dataset, batch_size=16, shuffle=False)
    
    # 训练
    history = trainer.train(train_loader, val_loader, num_epochs=3)
    
    print(f"训练历史: {len(history['train_losses'])} 个epoch")
    print(f"最终训练准确率: {history['train_accuracies'][-1]:.4f}")
    print(f"最终验证准确率: {history['val_accuracies'][-1]:.4f}")
    
    print("✓ 训练器测试成功!")


if __name__ == "__main__":
    test_trainer()
