"""
错误处理中间件
提供全局错误捕获和处理功能
"""

import functools
import time
from typing import Any, Callable, Optional
import streamlit as st
import requests

from .error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorRec<PERSON>y
from .error_config import (
    ErrorType, ErrorSeverity, 
    get_error_config, get_severity_config, get_http_error_info
)


class ErrorMiddleware:
    """错误处理中间件"""
    
    def __init__(self):
        self.retry_count = {}
        self.max_retries = 3
        self.retry_delay = 1.0
    
    def handle_page_error(self, page_name: str):
        """页面错误处理装饰器"""
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    self._handle_page_exception(page_name, e, func.__name__)
                    return None
            return wrapper
        return decorator
    
    def handle_api_call(self, endpoint: str, retry_on_failure: bool = True):
        """API调用错误处理装饰器"""
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                retry_key = f"{endpoint}_{func.__name__}"
                
                for attempt in range(self.max_retries if retry_on_failure else 1):
                    try:
                        result = func(*args, **kwargs)
                        # 重置重试计数
                        if retry_key in self.retry_count:
                            del self.retry_count[retry_key]
                        return result
                        
                    except requests.exceptions.ConnectionError as e:
                        if attempt < self.max_retries - 1 and retry_on_failure:
                            self._show_retry_message(endpoint, attempt + 1)
                            time.sleep(self.retry_delay * (attempt + 1))
                            continue
                        else:
                            self._handle_connection_error(endpoint, e)
                            return None
                            
                    except requests.exceptions.HTTPError as e:
                        self._handle_http_error(endpoint, e)
                        return None
                        
                    except requests.exceptions.Timeout as e:
                        if attempt < self.max_retries - 1 and retry_on_failure:
                            self._show_retry_message(endpoint, attempt + 1, "超时")
                            time.sleep(self.retry_delay * (attempt + 1))
                            continue
                        else:
                            self._handle_timeout_error(endpoint, e)
                            return None
                            
                    except Exception as e:
                        self._handle_api_exception(endpoint, e, func.__name__)
                        return None
                        
                return None
            return wrapper
        return decorator
    
    def handle_data_operation(self, operation: str):
        """数据操作错误处理装饰器"""
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                try:
                    return func(*args, **kwargs)
                except FileNotFoundError as e:
                    self._handle_file_error(operation, e)
                    return None
                except PermissionError as e:
                    self._handle_permission_error(operation, e)
                    return None
                except ValueError as e:
                    self._handle_validation_error(operation, e)
                    return None
                except Exception as e:
                    self._handle_data_exception(operation, e, func.__name__)
                    return None
            return wrapper
        return decorator
    
    def _handle_page_exception(self, page_name: str, error: Exception, func_name: str):
        """处理页面异常"""
        error_config = get_error_config(ErrorType.SYSTEM_ERROR)
        
        ErrorHandler.show_error(
            title=f"页面加载失败: {page_name}",
            message=f"在执行 {func_name} 时发生错误: {str(error)}",
            error_type="error",
            show_details=True,
            exception=error,
            suggestions=error_config["suggestions"]
        )
        
        # 显示恢复选项
        self._show_page_recovery_options(page_name)
        
        # 记录错误日志
        ErrorLogger.log_error(error, {
            "page_name": page_name,
            "function": func_name,
            "error_type": "page_error"
        })
    
    def _handle_connection_error(self, endpoint: str, error: Exception):
        """处理连接错误"""
        error_config = get_error_config(ErrorType.NETWORK_ERROR)
        
        ErrorHandler.show_error(
            title=error_config["title"],
            message=f"无法连接到服务: {endpoint}\n错误: {str(error)}",
            error_type="error",
            suggestions=error_config["suggestions"]
        )
        
        ErrorRecovery.show_network_error_recovery()
    
    def _handle_http_error(self, endpoint: str, error: requests.exceptions.HTTPError):
        """处理HTTP错误"""
        status_code = error.response.status_code
        http_error_info = get_http_error_info(status_code)
        
        ErrorHandler.show_error(
            title=http_error_info["title"],
            message=f"端点: {endpoint}\n状态码: {status_code}\n{http_error_info['message']}",
            error_type="error",
            suggestions=http_error_info["suggestions"]
        )
    
    def _handle_timeout_error(self, endpoint: str, error: Exception):
        """处理超时错误"""
        ErrorHandler.show_error(
            title="请求超时",
            message=f"请求超时: {endpoint}\n错误: {str(error)}",
            error_type="warning",
            suggestions=[
                "检查网络连接速度",
                "增加请求超时时间",
                "重试请求",
                "检查服务器负载"
            ]
        )
    
    def _handle_api_exception(self, endpoint: str, error: Exception, func_name: str):
        """处理API异常"""
        ErrorHandler.show_error(
            title="API调用失败",
            message=f"调用API时发生错误\n端点: {endpoint}\n函数: {func_name}\n错误: {str(error)}",
            error_type="error",
            show_details=True,
            exception=error,
            suggestions=[
                "检查API服务状态",
                "验证请求参数",
                "查看API文档",
                "重试请求"
            ]
        )
        
        ErrorLogger.log_error(error, {
            "endpoint": endpoint,
            "function": func_name,
            "error_type": "api_error"
        })
    
    def _handle_file_error(self, operation: str, error: Exception):
        """处理文件错误"""
        error_config = get_error_config(ErrorType.FILE_ERROR)
        
        ErrorHandler.show_error(
            title=error_config["title"],
            message=f"文件操作失败: {operation}\n错误: {str(error)}",
            error_type="error",
            suggestions=error_config["suggestions"]
        )
    
    def _handle_permission_error(self, operation: str, error: Exception):
        """处理权限错误"""
        ErrorHandler.show_error(
            title="权限错误",
            message=f"没有权限执行操作: {operation}\n错误: {str(error)}",
            error_type="error",
            suggestions=[
                "检查文件权限设置",
                "确认用户访问权限",
                "以管理员身份运行",
                "检查文件是否被占用"
            ]
        )
    
    def _handle_validation_error(self, operation: str, error: Exception):
        """处理验证错误"""
        error_config = get_error_config(ErrorType.VALIDATION_ERROR)
        
        ErrorHandler.show_error(
            title=error_config["title"],
            message=f"数据验证失败: {operation}\n错误: {str(error)}",
            error_type="warning",
            suggestions=error_config["suggestions"]
        )
    
    def _handle_data_exception(self, operation: str, error: Exception, func_name: str):
        """处理数据异常"""
        error_config = get_error_config(ErrorType.DATA_ERROR)
        
        ErrorHandler.show_error(
            title=error_config["title"],
            message=f"数据操作失败: {operation}\n函数: {func_name}\n错误: {str(error)}",
            error_type="error",
            show_details=True,
            exception=error,
            suggestions=error_config["suggestions"]
        )
        
        ErrorRecovery.show_data_error_recovery(operation)
        
        ErrorLogger.log_error(error, {
            "operation": operation,
            "function": func_name,
            "error_type": "data_error"
        })
    
    def _show_retry_message(self, endpoint: str, attempt: int, reason: str = "连接失败"):
        """显示重试消息"""
        st.warning(f"🔄 {reason}，正在重试... (第{attempt}次尝试)")
        st.info(f"端点: {endpoint}")
    
    def _show_page_recovery_options(self, page_name: str):
        """显示页面恢复选项"""
        st.subheader("⚡ 恢复选项")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("🔄 重新加载页面"):
                st.rerun()
        
        with col2:
            if st.button("🏠 返回首页"):
                st.session_state.selected_page = "📈 数据概览"
                st.rerun()
        
        with col3:
            if st.button("🔍 系统诊断"):
                self._show_system_health_check()
    
    def _show_system_health_check(self):
        """显示系统健康检查"""
        st.subheader("🔍 系统健康检查")
        
        # 检查核心服务
        services = [
            ("API服务", "http://127.0.0.1:8888/health"),
            ("数据状态", "http://127.0.0.1:8888/api/v1/data/status"),
        ]
        
        for service_name, url in services:
            try:
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    st.success(f"✅ {service_name}: 正常")
                else:
                    st.error(f"❌ {service_name}: HTTP {response.status_code}")
            except Exception:
                st.error(f"❌ {service_name}: 无法连接")
        
        # 检查外部数据源
        try:
            response = requests.head("https://data.17500.cn/3d_asc.txt", timeout=10)
            if response.status_code == 200:
                st.success("✅ 外部数据源: 正常")
            else:
                st.warning(f"⚠️ 外部数据源: HTTP {response.status_code}")
        except Exception:
            st.error("❌ 外部数据源: 无法访问")


# 全局错误中间件实例
error_middleware = ErrorMiddleware()

# 便捷装饰器
def handle_page_errors(page_name: str):
    """页面错误处理装饰器"""
    return error_middleware.handle_page_error(page_name)

def handle_api_errors(endpoint: str, retry_on_failure: bool = True):
    """API错误处理装饰器"""
    return error_middleware.handle_api_call(endpoint, retry_on_failure)

def handle_data_errors(operation: str):
    """数据操作错误处理装饰器"""
    return error_middleware.handle_data_operation(operation)
