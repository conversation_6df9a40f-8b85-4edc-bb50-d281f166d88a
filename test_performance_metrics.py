"""
性能评估指标测试

验证各种性能评估指标的计算正确性
"""

import sys
import os
sys.path.append('src')

from prediction.model_validation import MarkovCrossValidator
import numpy as np
import math
from collections import Counter


def test_prediction_accuracy():
    """测试预测准确率指标"""
    print("🎯 测试预测准确率指标...")
    
    validator = MarkovCrossValidator()
    
    # 测试用例1：完全匹配
    predictions1 = ["123", "456", "789"]
    actuals1 = ["123", "456", "789"]
    
    # 测试用例2：部分匹配
    predictions2 = ["123", "456", "789", "012"]
    actuals2 = ["123", "654", "987", "210"]
    
    # 测试用例3：无匹配
    predictions3 = ["123", "456", "789"]
    actuals3 = ["321", "654", "987"]
    
    # 计算准确率
    accuracy1 = validator.calculate_prediction_accuracy(predictions1, actuals1)
    accuracy2 = validator.calculate_prediction_accuracy(predictions2, actuals2)
    accuracy3 = validator.calculate_prediction_accuracy(predictions3, actuals3)
    
    # 验证结果
    print("\n📊 测试用例1（完全匹配）:")
    print(f"   完全匹配率: {accuracy1['exact_match']:.4f} (期望: 1.0000)")
    print(f"   数字准确率: {accuracy1['digit_accuracy']:.4f} (期望: 1.0000)")
    print(f"   位置准确率: {[f'{x:.4f}' for x in accuracy1['position_accuracy']]}")
    
    print("\n📊 测试用例2（部分匹配）:")
    print(f"   完全匹配率: {accuracy2['exact_match']:.4f} (期望: 0.2500)")
    print(f"   数字准确率: {accuracy2['digit_accuracy']:.4f}")
    print(f"   位置准确率: {[f'{x:.4f}' for x in accuracy2['position_accuracy']]}")
    
    print("\n📊 测试用例3（无匹配）:")
    print(f"   完全匹配率: {accuracy3['exact_match']:.4f} (期望: 0.0000)")
    print(f"   数字准确率: {accuracy3['digit_accuracy']:.4f}")
    print(f"   位置准确率: {[f'{x:.4f}' for x in accuracy3['position_accuracy']]}")
    
    # 验证计算正确性
    is_correct = (
        abs(accuracy1['exact_match'] - 1.0) < 1e-6 and
        abs(accuracy2['exact_match'] - 0.25) < 1e-6 and
        abs(accuracy3['exact_match'] - 0.0) < 1e-6
    )
    
    if is_correct:
        print("\n✅ 预测准确率指标计算正确")
        return True
    else:
        print("\n❌ 预测准确率指标计算错误")
        return False


def test_diversity_metrics():
    """测试多样性指标"""
    print("\n🔄 测试多样性指标...")
    
    validator = MarkovCrossValidator()
    
    # 测试用例1：完全多样
    predictions1 = ["123", "456", "789", "012", "345"]
    
    # 测试用例2：部分多样
    predictions2 = ["123", "123", "456", "456", "789"]
    
    # 测试用例3：无多样性
    predictions3 = ["123", "123", "123", "123", "123"]
    
    # 计算多样性指标
    diversity1 = validator.calculate_diversity_metrics(predictions1)
    diversity2 = validator.calculate_diversity_metrics(predictions2)
    diversity3 = validator.calculate_diversity_metrics(predictions3)
    
    # 验证结果
    print("\n📊 测试用例1（完全多样）:")
    print(f"   辛普森多样性指数: {diversity1['simpson_diversity']:.4f} (期望: 0.8000)")
    print(f"   唯一预测比率: {diversity1['unique_ratio']:.4f} (期望: 1.0000)")
    print(f"   信息熵: {diversity1['entropy']:.4f}")
    
    print("\n📊 测试用例2（部分多样）:")
    print(f"   辛普森多样性指数: {diversity2['simpson_diversity']:.4f} (期望: 0.6400)")
    print(f"   唯一预测比率: {diversity2['unique_ratio']:.4f} (期望: 0.6000)")
    print(f"   信息熵: {diversity2['entropy']:.4f}")
    
    print("\n📊 测试用例3（无多样性）:")
    print(f"   辛普森多样性指数: {diversity3['simpson_diversity']:.4f} (期望: 0.0000)")
    print(f"   唯一预测比率: {diversity3['unique_ratio']:.4f} (期望: 0.2000)")
    print(f"   信息熵: {diversity3['entropy']:.4f}")
    
    # 手动计算辛普森多样性指数进行验证
    def manual_simpson(predictions):
        counts = Counter(predictions)
        total = len(predictions)
        return 1 - sum((count/total)**2 for count in counts.values())
    
    simpson1 = manual_simpson(predictions1)
    simpson2 = manual_simpson(predictions2)
    simpson3 = manual_simpson(predictions3)
    
    print("\n🔍 手动验证辛普森多样性指数:")
    print(f"   测试用例1: {simpson1:.4f} vs {diversity1['simpson_diversity']:.4f}")
    print(f"   测试用例2: {simpson2:.4f} vs {diversity2['simpson_diversity']:.4f}")
    print(f"   测试用例3: {simpson3:.4f} vs {diversity3['simpson_diversity']:.4f}")
    
    # 验证计算正确性
    is_correct = (
        abs(diversity1['simpson_diversity'] - simpson1) < 1e-6 and
        abs(diversity2['simpson_diversity'] - simpson2) < 1e-6 and
        abs(diversity3['simpson_diversity'] - simpson3) < 1e-6
    )
    
    if is_correct:
        print("\n✅ 多样性指标计算正确")
        return True
    else:
        print("\n❌ 多样性指标计算错误")
        return False


def test_stability_metrics():
    """测试稳定性指标"""
    print("\n📊 测试稳定性指标...")
    
    validator = MarkovCrossValidator()
    
    # 测试用例1：高稳定性
    predictions1 = ["123", "124", "125", "126", "127"]
    
    # 测试用例2：中等稳定性
    predictions2 = ["123", "234", "345", "456", "567"]
    
    # 测试用例3：低稳定性
    predictions3 = ["123", "456", "789", "012", "345"]
    
    # 计算稳定性指标
    stability1 = validator.calculate_stability_metrics(predictions1)
    stability2 = validator.calculate_stability_metrics(predictions2)
    stability3 = validator.calculate_stability_metrics(predictions3)
    
    # 验证结果
    print("\n📊 测试用例1（高稳定性）:")
    print(f"   方差: {stability1['variance']:.4f}")
    print(f"   标准差: {stability1['std_dev']:.4f}")
    print(f"   变异系数: {stability1['coefficient_of_variation']:.4f}")
    
    print("\n📊 测试用例2（中等稳定性）:")
    print(f"   方差: {stability2['variance']:.4f}")
    print(f"   标准差: {stability2['std_dev']:.4f}")
    print(f"   变异系数: {stability2['coefficient_of_variation']:.4f}")
    
    print("\n📊 测试用例3（低稳定性）:")
    print(f"   方差: {stability3['variance']:.4f}")
    print(f"   标准差: {stability3['std_dev']:.4f}")
    print(f"   变异系数: {stability3['coefficient_of_variation']:.4f}")
    
    # 手动计算方差进行验证
    def manual_variance(predictions):
        numeric_predictions = [int(p) for p in predictions if p.isdigit()]
        if not numeric_predictions:
            return 0.0
        mean = sum(numeric_predictions) / len(numeric_predictions)
        return sum((x - mean) ** 2 for x in numeric_predictions) / len(numeric_predictions)
    
    # 将预测转换为数值
    numeric_predictions1 = [int(p) for p in predictions1]
    numeric_predictions2 = [int(p) for p in predictions2]
    numeric_predictions3 = [int(p) for p in predictions3]
    
    # 手动计算方差
    var1 = np.var(numeric_predictions1)
    var2 = np.var(numeric_predictions2)
    var3 = np.var(numeric_predictions3)
    
    print("\n🔍 手动验证方差:")
    print(f"   测试用例1: {var1:.4f} vs {stability1['variance']:.4f}")
    print(f"   测试用例2: {var2:.4f} vs {stability2['variance']:.4f}")
    print(f"   测试用例3: {var3:.4f} vs {stability3['variance']:.4f}")
    
    # 验证计算正确性
    is_correct = (
        abs(stability1['variance'] - var1) < 1e-6 and
        abs(stability2['variance'] - var2) < 1e-6 and
        abs(stability3['variance'] - var3) < 1e-6
    )
    
    if is_correct:
        print("\n✅ 稳定性指标计算正确")
        return True
    else:
        print("\n❌ 稳定性指标计算错误")
        return False


def test_aic_bic_calculation():
    """测试AIC和BIC准则计算"""
    print("\n📈 测试AIC和BIC准则计算...")
    
    validator = MarkovCrossValidator()
    
    # 测试用例
    log_likelihood1 = -100.0
    num_params1 = 10
    num_samples1 = 100
    
    log_likelihood2 = -200.0
    num_params2 = 20
    num_samples2 = 200
    
    # 计算AIC和BIC
    aic_bic1 = validator.calculate_aic_bic(log_likelihood1, num_params1, num_samples1)
    aic_bic2 = validator.calculate_aic_bic(log_likelihood2, num_params2, num_samples2)
    
    # 验证结果
    print("\n📊 测试用例1:")
    print(f"   AIC: {aic_bic1['aic']:.4f}")
    print(f"   BIC: {aic_bic1['bic']:.4f}")
    
    print("\n📊 测试用例2:")
    print(f"   AIC: {aic_bic2['aic']:.4f}")
    print(f"   BIC: {aic_bic2['bic']:.4f}")
    
    # 手动计算AIC和BIC
    # AIC = 2k - 2ln(L)
    manual_aic1 = 2 * num_params1 - 2 * log_likelihood1
    # BIC = ln(n)k - 2ln(L)
    manual_bic1 = math.log(num_samples1) * num_params1 - 2 * log_likelihood1
    
    manual_aic2 = 2 * num_params2 - 2 * log_likelihood2
    manual_bic2 = math.log(num_samples2) * num_params2 - 2 * log_likelihood2
    
    print("\n🔍 手动验证AIC和BIC:")
    print(f"   测试用例1 AIC: {manual_aic1:.4f} vs {aic_bic1['aic']:.4f}")
    print(f"   测试用例1 BIC: {manual_bic1:.4f} vs {aic_bic1['bic']:.4f}")
    print(f"   测试用例2 AIC: {manual_aic2:.4f} vs {aic_bic2['aic']:.4f}")
    print(f"   测试用例2 BIC: {manual_bic2:.4f} vs {aic_bic2['bic']:.4f}")
    
    # 验证计算正确性
    is_correct = (
        abs(aic_bic1['aic'] - manual_aic1) < 1e-6 and
        abs(aic_bic1['bic'] - manual_bic1) < 1e-6 and
        abs(aic_bic2['aic'] - manual_aic2) < 1e-6 and
        abs(aic_bic2['bic'] - manual_bic2) < 1e-6
    )
    
    if is_correct:
        print("\n✅ AIC和BIC准则计算正确")
        return True
    else:
        print("\n❌ AIC和BIC准则计算错误")
        return False


def main():
    """主测试函数"""
    print("🚀 性能评估指标测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("预测准确率指标", test_prediction_accuracy()))
    test_results.append(("多样性指标", test_diversity_metrics()))
    test_results.append(("稳定性指标", test_stability_metrics()))
    test_results.append(("AIC和BIC准则", test_aic_bic_calculation()))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总:")
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed_tests += 1
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！性能评估指标计算正确！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步调试")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
