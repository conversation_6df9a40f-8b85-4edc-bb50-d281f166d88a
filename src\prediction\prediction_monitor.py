#!/usr/bin/env python3
"""
预测质量监控器
实现预测质量的持续监控和自动重训练触发机制
"""

import json
import os
from collections import Counter, deque
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

try:
    from .prediction_validator import PredictionValidator
except ImportError:
    from prediction_validator import PredictionValidator


class PredictionQualityMonitor:
    """预测质量监控器"""
    
    def __init__(self, cache_dir: str = "data/cache"):
        """
        初始化监控器
        
        Args:
            cache_dir: 缓存目录
        """
        self.cache_dir = cache_dir
        self.validator = PredictionValidator()
        
        # 监控配置
        self.alert_thresholds = {
            'diversity_min': 0.6,           # 最小多样性阈值
            'exploration_min': 0.05,        # 最小探索率阈值
            'consecutive_failures': 5,      # 连续失败次数阈值
            'stability_variance_max': 0.01  # 最大稳定性方差
        }
        
        # 质量历史记录
        self.quality_history = deque(maxlen=100)  # 保留最近100次记录
        
        # 确保缓存目录存在
        os.makedirs(cache_dir, exist_ok=True)
        
        # 加载历史记录
        self._load_quality_history()
    
    def monitor_prediction_quality(self, predictions: List[str], 
                                 test_name: str = "quality_monitor") -> Dict[str, Any]:
        """
        监控预测质量
        
        Args:
            predictions: 预测号码列表
            test_name: 测试名称
            
        Returns:
            监控结果
        """
        if not predictions:
            return {'error': '没有预测结果可监控'}
        
        # 1. 计算质量指标
        diversity_metrics = self.validator.validate_prediction_diversity(predictions, test_name)
        
        # 2. 检查是否需要重训练
        need_retrain = self._check_retrain_conditions(diversity_metrics)
        
        # 3. 生成告警信息
        alerts = self._generate_alerts(diversity_metrics)
        
        # 4. 记录质量历史
        quality_record = {
            'timestamp': datetime.now().isoformat(),
            'test_name': test_name,
            'metrics': diversity_metrics,
            'need_retrain': need_retrain,
            'alerts': alerts,
            'prediction_count': len(predictions),
            'unique_count': len(set(predictions))
        }
        
        self.quality_history.append(quality_record)
        
        # 5. 保存历史记录
        self._save_quality_history()
        
        # 6. 生成监控报告
        monitor_result = {
            'current_quality': diversity_metrics,
            'need_retrain': need_retrain,
            'alerts': alerts,
            'recommendations': self._generate_recommendations(diversity_metrics),
            'quality_trend': self._analyze_quality_trend(),
            'monitor_summary': {
                'total_predictions': len(predictions),
                'unique_predictions': len(set(predictions)),
                'diversity_score': diversity_metrics.get('diversity_score', 0),
                'quality_level': diversity_metrics.get('quality_level', 'unknown')
            }
        }
        
        return monitor_result
    
    def _check_retrain_conditions(self, metrics: Dict[str, Any]) -> bool:
        """
        检查是否需要重训练
        
        Args:
            metrics: 质量指标
            
        Returns:
            是否需要重训练
        """
        # 条件1：当前质量过低
        diversity_score = metrics.get('diversity_score', 0)
        if diversity_score < self.alert_thresholds['diversity_min']:
            return True
        
        # 条件2：探索率过低
        exploration_rate = metrics.get('exploration_rate', 0)
        if exploration_rate < self.alert_thresholds['exploration_min']:
            return True
        
        # 条件3：检测到固定模式
        if metrics.get('fixed_pattern_detected', False):
            return True
        
        # 条件4：连续多次质量不达标
        if len(self.quality_history) >= self.alert_thresholds['consecutive_failures']:
            recent_quality = list(self.quality_history)[-self.alert_thresholds['consecutive_failures']:]
            
            low_quality_count = sum(
                1 for record in recent_quality 
                if record['metrics'].get('diversity_score', 0) < self.alert_thresholds['diversity_min']
            )
            
            if low_quality_count >= self.alert_thresholds['consecutive_failures']:
                return True
        
        return False
    
    def _generate_alerts(self, metrics: Dict[str, Any]) -> List[str]:
        """
        生成告警信息
        
        Args:
            metrics: 质量指标
            
        Returns:
            告警信息列表
        """
        alerts = []
        
        # 多样性告警
        diversity_score = metrics.get('diversity_score', 0)
        if diversity_score < self.alert_thresholds['diversity_min']:
            alerts.append(f"多样性告警：当前评分{diversity_score:.3f}低于阈值{self.alert_thresholds['diversity_min']}")
        
        # 探索率告警
        exploration_rate = metrics.get('exploration_rate', 0)
        if exploration_rate < self.alert_thresholds['exploration_min']:
            alerts.append(f"探索率告警：当前探索率{exploration_rate:.3f}低于阈值{self.alert_thresholds['exploration_min']}")
        
        # 固定模式告警
        if metrics.get('fixed_pattern_detected', False):
            alerts.append("固定模式告警：检测到预测结果存在固定模式")
        
        # 质量级别告警
        quality_level = metrics.get('quality_level', 'unknown')
        if quality_level in ['poor', 'fair']:
            alerts.append(f"质量级别告警：当前质量级别为{quality_level}")
        
        return alerts
    
    def _generate_recommendations(self, metrics: Dict[str, Any]) -> List[str]:
        """
        生成改进建议
        
        Args:
            metrics: 质量指标
            
        Returns:
            建议列表
        """
        recommendations = []
        
        # 基于质量指标生成建议
        diversity_score = metrics.get('diversity_score', 0)
        exploration_rate = metrics.get('exploration_rate', 0)
        
        if diversity_score < 0.5:
            recommendations.append("建议增加预测算法的随机性，考虑引入温度参数")
        
        if exploration_rate < 0.1:
            recommendations.append("建议增加候选号码的多样性，可以添加随机候选")
        
        if metrics.get('fixed_pattern_detected', False):
            recommendations.append("建议重构核心预测算法，使用概率性方法")
        
        # 基于历史趋势生成建议
        trend = self._analyze_quality_trend()
        if trend.get('trend_direction') == 'declining':
            recommendations.append("质量呈下降趋势，建议立即检查预测算法")
        
        if not recommendations:
            recommendations.append("当前质量表现良好，继续保持")
        
        return recommendations
    
    def _analyze_quality_trend(self) -> Dict[str, Any]:
        """
        分析质量趋势
        
        Returns:
            趋势分析结果
        """
        if len(self.quality_history) < 3:
            return {'trend_direction': 'insufficient_data', 'trend_strength': 0}
        
        # 获取最近的质量分数
        recent_scores = [
            record['metrics'].get('diversity_score', 0) 
            for record in list(self.quality_history)[-10:]  # 最近10次
        ]
        
        if len(recent_scores) < 3:
            return {'trend_direction': 'insufficient_data', 'trend_strength': 0}
        
        # 简单的趋势分析
        first_half = recent_scores[:len(recent_scores)//2]
        second_half = recent_scores[len(recent_scores)//2:]
        
        first_avg = sum(first_half) / len(first_half)
        second_avg = sum(second_half) / len(second_half)
        
        trend_change = second_avg - first_avg
        
        if abs(trend_change) < 0.05:
            trend_direction = 'stable'
        elif trend_change > 0:
            trend_direction = 'improving'
        else:
            trend_direction = 'declining'
        
        return {
            'trend_direction': trend_direction,
            'trend_strength': abs(trend_change),
            'recent_average': second_avg,
            'previous_average': first_avg,
            'change': trend_change
        }
    
    def _load_quality_history(self) -> None:
        """加载质量历史记录"""
        try:
            history_file = os.path.join(self.cache_dir, 'quality_history.json')
            
            if os.path.exists(history_file):
                with open(history_file, 'r', encoding='utf-8') as f:
                    history_data = json.load(f)
                    
                # 转换为deque
                self.quality_history = deque(history_data.get('history', []), maxlen=100)
                
        except Exception as e:
            print(f"加载质量历史失败: {e}")
            self.quality_history = deque(maxlen=100)
    
    def _save_quality_history(self) -> None:
        """保存质量历史记录"""
        try:
            history_file = os.path.join(self.cache_dir, 'quality_history.json')
            
            history_data = {
                'last_updated': datetime.now().isoformat(),
                'total_records': len(self.quality_history),
                'history': list(self.quality_history)
            }
            
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(history_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存质量历史失败: {e}")
    
    def get_quality_summary(self) -> Dict[str, Any]:
        """
        获取质量摘要
        
        Returns:
            质量摘要
        """
        if not self.quality_history:
            return {'error': '没有质量历史记录'}
        
        recent_records = list(self.quality_history)[-10:]  # 最近10次
        
        # 计算平均质量指标
        avg_diversity = sum(
            record['metrics'].get('diversity_score', 0) 
            for record in recent_records
        ) / len(recent_records)
        
        avg_exploration = sum(
            record['metrics'].get('exploration_rate', 0) 
            for record in recent_records
        ) / len(recent_records)
        
        # 统计告警次数
        alert_count = sum(
            len(record.get('alerts', [])) 
            for record in recent_records
        )
        
        # 统计重训练建议次数
        retrain_count = sum(
            1 for record in recent_records 
            if record.get('need_retrain', False)
        )
        
        return {
            'total_records': len(self.quality_history),
            'recent_records': len(recent_records),
            'average_diversity_score': avg_diversity,
            'average_exploration_rate': avg_exploration,
            'alert_count': alert_count,
            'retrain_recommendations': retrain_count,
            'quality_trend': self._analyze_quality_trend(),
            'last_update': recent_records[-1]['timestamp'] if recent_records else None
        }
