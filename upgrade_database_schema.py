#!/usr/bin/env python3
"""
Bug检测系统数据库升级脚本
添加缺失的environment列和其他AI相关字段
"""

import sqlite3
import os
import logging
from datetime import datetime
import shutil

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseUpgrader:
    """数据库升级器"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or self._find_database()
        self.backup_path = None
    
    def _find_database(self) -> str:
        """查找数据库文件"""
        possible_paths = [
            "data/bug_detection.db",
            "bug_detection.db",
            "lottery_data.db"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"📁 找到数据库文件: {path}")
                return path
        
        # 如果没找到，使用默认路径
        default_path = "data/bug_detection.db"
        os.makedirs(os.path.dirname(default_path), exist_ok=True)
        logger.info(f"📁 使用默认数据库路径: {default_path}")
        return default_path
    
    def backup_database(self) -> bool:
        """备份数据库"""
        try:
            if not os.path.exists(self.db_path):
                logger.info("📄 数据库文件不存在，无需备份")
                return True
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.backup_path = f"{self.db_path}.backup_{timestamp}"
            
            shutil.copy2(self.db_path, self.backup_path)
            logger.info(f"💾 数据库已备份到: {self.backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库备份失败: {e}")
            return False
    
    def check_column_exists(self, table_name: str, column_name: str) -> bool:
        """检查列是否存在"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [row[1] for row in cursor.fetchall()]
            
            conn.close()
            return column_name in columns
            
        except Exception as e:
            logger.error(f"❌ 检查列失败: {e}")
            return False
    
    def add_missing_columns(self) -> bool:
        """添加缺失的列"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 需要添加的列定义
            columns_to_add = [
                ("bug_reports", "environment", "TEXT DEFAULT 'production'"),
                ("bug_reports", "category", "TEXT DEFAULT 'general'"),
                ("bug_reports", "priority", "TEXT DEFAULT 'medium'"),
                ("bug_reports", "tags", "TEXT"),
                ("bug_reports", "source", "TEXT DEFAULT 'user'"),
                ("bug_reports", "component_name", "TEXT"),
                ("bug_reports", "reproduction_steps", "TEXT"),
                ("bug_reports", "system_context", "TEXT"),
                ("bug_reports", "user_journey", "TEXT"),
                ("bug_reports", "screenshots", "TEXT"),
            ]
            
            added_count = 0
            
            for table_name, column_name, column_def in columns_to_add:
                if not self.check_column_exists(table_name, column_name):
                    try:
                        sql = f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_def}"
                        cursor.execute(sql)
                        logger.info(f"✅ 添加列: {table_name}.{column_name}")
                        added_count += 1
                    except Exception as e:
                        logger.error(f"❌ 添加列失败 {table_name}.{column_name}: {e}")
                else:
                    logger.info(f"⏭️ 列已存在: {table_name}.{column_name}")
            
            conn.commit()
            conn.close()
            
            logger.info(f"📊 总共添加了 {added_count} 个列")
            return True
            
        except Exception as e:
            logger.error(f"❌ 添加列操作失败: {e}")
            return False
    
    def verify_schema(self) -> bool:
        """验证数据库结构"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查bug_reports表结构
            cursor.execute("PRAGMA table_info(bug_reports)")
            columns = cursor.fetchall()
            
            logger.info("📋 bug_reports表结构:")
            for col in columns:
                logger.info(f"   {col[1]} {col[2]} (默认值: {col[4]})")
            
            # 检查必需的列
            required_columns = ['environment', 'category', 'priority', 'tags', 'source']
            column_names = [col[1] for col in columns]
            
            missing_columns = [col for col in required_columns if col not in column_names]
            
            if missing_columns:
                logger.error(f"❌ 缺失必需列: {missing_columns}")
                return False
            
            logger.info("✅ 数据库结构验证通过")
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库结构验证失败: {e}")
            return False
    
    def test_database_operations(self) -> bool:
        """测试数据库操作"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 测试插入数据
            test_data = {
                'id': 'test_upgrade_001',
                'error_type': 'test_error',
                'severity': 'low',
                'page_name': 'test_page',
                'error_message': '数据库升级测试',
                'environment': 'test',
                'category': 'database',
                'priority': 'low',
                'tags': 'upgrade,test',
                'source': 'upgrade_script'
            }
            
            # 插入测试数据
            cursor.execute("""
                INSERT INTO bug_reports 
                (id, error_type, severity, page_name, error_message, environment, category, priority, tags, source)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                test_data['id'], test_data['error_type'], test_data['severity'],
                test_data['page_name'], test_data['error_message'], test_data['environment'],
                test_data['category'], test_data['priority'], test_data['tags'], test_data['source']
            ))
            
            # 查询测试数据
            cursor.execute("SELECT * FROM bug_reports WHERE id = ?", (test_data['id'],))
            result = cursor.fetchone()
            
            if result:
                logger.info("✅ 数据库操作测试成功")
                
                # 清理测试数据
                cursor.execute("DELETE FROM bug_reports WHERE id = ?", (test_data['id'],))
                conn.commit()
                logger.info("🧹 测试数据已清理")
            else:
                logger.error("❌ 数据库操作测试失败")
                return False
            
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库操作测试失败: {e}")
            return False
    
    def upgrade(self) -> bool:
        """执行数据库升级"""
        logger.info("🚀 开始数据库升级...")
        logger.info("=" * 60)
        
        # 1. 备份数据库
        if not self.backup_database():
            return False
        
        # 2. 添加缺失的列
        if not self.add_missing_columns():
            logger.error("❌ 添加列失败，升级中止")
            return False
        
        # 3. 验证数据库结构
        if not self.verify_schema():
            logger.error("❌ 数据库结构验证失败")
            return False
        
        # 4. 测试数据库操作
        if not self.test_database_operations():
            logger.error("❌ 数据库操作测试失败")
            return False
        
        logger.info("=" * 60)
        logger.info("🎉 数据库升级完成！")
        
        if self.backup_path:
            logger.info(f"💾 备份文件: {self.backup_path}")
        
        return True

def main():
    """主函数"""
    logger.info("🗄️ Bug检测系统数据库升级工具")
    logger.info("=" * 60)
    
    upgrader = DatabaseUpgrader()
    
    success = upgrader.upgrade()
    
    if success:
        print("\n🚀 下一步:")
        print("1. 重启API和Streamlit服务")
        print("2. 检查Bug检测功能是否正常")
        print("3. 验证environment列相关功能")
    else:
        print("\n❌ 数据库升级失败")
        if upgrader.backup_path:
            print(f"💾 可以从备份恢复: {upgrader.backup_path}")
    
    return success

if __name__ == "__main__":
    main()
