#!/usr/bin/env python3
"""
查询预测记录的脚本
"""

import sqlite3
import json
from datetime import datetime

def check_model_library_db():
    """检查模型库数据库中的预测记录"""
    print("=== 检查 model_library.db ===")
    try:
        with sqlite3.connect("data/model_library.db") as conn:
            cursor = conn.cursor()
            
            # 查看所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            print(f"数据库表: {[table[0] for table in tables]}")
            
            # 检查model_predictions表
            if any('model_predictions' in table for table in tables):
                cursor.execute("SELECT COUNT(*) FROM model_predictions")
                count = cursor.fetchone()[0]
                print(f"model_predictions表记录数: {count}")
                
                if count > 0:
                    cursor.execute("SELECT * FROM model_predictions ORDER BY prediction_time DESC LIMIT 5")
                    records = cursor.fetchall()
                    print("最近5条预测记录:")
                    for record in records:
                        print(f"  {record}")
            else:
                print("未找到model_predictions表")
                
    except Exception as e:
        print(f"查询model_library.db失败: {e}")

def check_lottery_db():
    """检查彩票数据库中的预测记录"""
    print("\n=== 检查 lottery.db ===")
    try:
        with sqlite3.connect("data/lottery.db") as conn:
            cursor = conn.cursor()
            
            # 查看所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            print(f"数据库表: {[table[0] for table in tables]}")
            
            # 检查是否有预测相关的表
            prediction_tables = [table[0] for table in tables if 'prediction' in table[0].lower()]
            if prediction_tables:
                print(f"预测相关表: {prediction_tables}")
                for table in prediction_tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    print(f"{table}表记录数: {count}")
            else:
                print("未找到预测相关表")
                
    except Exception as e:
        print(f"查询lottery.db失败: {e}")

def check_cache_files():
    """检查缓存文件中的预测记录"""
    print("\n=== 检查缓存文件 ===")
    
    # 检查last_prediction.json
    try:
        with open("data/cache/last_prediction.json", "r", encoding="utf-8") as f:
            data = json.load(f)
        print(f"最新预测缓存: {data.get('numbers', 'N/A')}, 置信度: {data.get('confidence', 'N/A')}")
        print(f"预测时间: {data.get('timestamp', 'N/A')}")
    except Exception as e:
        print(f"读取last_prediction.json失败: {e}")
    
    # 检查智能融合状态
    try:
        with open("data/cache/intelligent_fusion_state.json", "r", encoding="utf-8") as f:
            data = json.load(f)
        print(f"模型训练状态: {data.get('models_trained', False)}")
        print(f"最后训练时间: {data.get('last_training_time', 'N/A')}")
    except Exception as e:
        print(f"读取intelligent_fusion_state.json失败: {e}")

def check_validation_reports():
    """检查验证报告中的预测记录"""
    print("\n=== 检查验证报告 ===")
    import os
    import glob
    
    # 查找最新的验证报告
    pattern = "data/validation_reports/diversity_report_fusion_prediction_*.json"
    files = glob.glob(pattern)
    if files:
        latest_file = max(files, key=os.path.getctime)
        print(f"最新验证报告: {latest_file}")
        
        try:
            with open(latest_file, "r", encoding="utf-8") as f:
                data = json.load(f)
            print(f"预测总数: {data.get('total_predictions', 'N/A')}")
            print(f"唯一预测数: {data.get('unique_predictions', 'N/A')}")
            print(f"多样性评分: {data.get('diversity_score', 'N/A')}")
            
            # 显示预测号码频率
            if 'detailed_analysis' in data and 'number_frequency' in data['detailed_analysis']:
                freq = data['detailed_analysis']['number_frequency']
                print("预测号码频率:")
                for num, count in list(freq.items())[:10]:  # 显示前10个
                    if num:  # 过滤空字符串
                        print(f"  {num}: {count}次")
        except Exception as e:
            print(f"读取验证报告失败: {e}")
    else:
        print("未找到验证报告文件")

if __name__ == "__main__":
    print("🔍 福彩3D预测记录检查工具")
    print("=" * 50)
    
    check_model_library_db()
    check_lottery_db()
    check_cache_files()
    check_validation_reports()
    
    print("\n✅ 检查完成")
