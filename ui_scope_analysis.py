#!/usr/bin/env python3
"""
UI模块变量作用域问题分析脚本
检查整个UI模块中的类似变量作用域问题
"""

import os
import re
import sys
from typing import List, Dict, <PERSON><PERSON>

def analyze_file_for_scope_issues(file_path: str) -> Dict[str, List[str]]:
    """分析单个文件中的变量作用域问题"""
    issues = {
        'conditional_assignments': [],
        'uninitialized_variables': [],
        'return_statements_in_branches': [],
        'try_except_scope_issues': []
    }
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
        
        # 检查条件分支中的变量赋值
        in_function = False
        function_name = ""
        indent_level = 0
        
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            
            # 检测函数定义
            if stripped.startswith('def '):
                in_function = True
                function_name = stripped.split('(')[0].replace('def ', '')
                indent_level = len(line) - len(line.lstrip())
            
            # 检测函数结束
            elif in_function and line and len(line) - len(line.lstrip()) <= indent_level and not line.startswith(' '):
                in_function = False
                function_name = ""
            
            if in_function:
                # 检查条件分支中的变量赋值
                if re.match(r'\s*(if|elif)\s+.*:', stripped):
                    # 查找该分支中的变量赋值
                    branch_indent = len(line) - len(line.lstrip())
                    j = i
                    while j < len(lines):
                        next_line = lines[j]
                        if not next_line.strip():
                            j += 1
                            continue
                        
                        next_indent = len(next_line) - len(next_line.lstrip())
                        if next_indent <= branch_indent:
                            break
                        
                        # 检查变量赋值
                        if '=' in next_line and not next_line.strip().startswith('#'):
                            var_match = re.match(r'\s*(\w+)\s*=', next_line)
                            if var_match:
                                var_name = var_match.group(1)
                                issues['conditional_assignments'].append(
                                    f"{function_name}:{i+1} - 变量 '{var_name}' 在条件分支中赋值"
                                )
                        j += 1
                
                # 检查return语句在条件分支中
                if 'return' in stripped and ('if ' in stripped or 'elif ' in stripped or 'else:' in stripped):
                    issues['return_statements_in_branches'].append(
                        f"{function_name}:{i} - return语句在条件分支中"
                    )
                
                # 检查try-except中的变量作用域问题
                if stripped.startswith('try:'):
                    try_indent = len(line) - len(line.lstrip())
                    j = i
                    variables_in_try = []
                    
                    while j < len(lines):
                        next_line = lines[j]
                        if not next_line.strip():
                            j += 1
                            continue
                        
                        next_indent = len(next_line) - len(next_line.lstrip())
                        if next_indent <= try_indent and next_line.strip().startswith('except'):
                            break
                        
                        # 检查try块中的变量赋值
                        if '=' in next_line and not next_line.strip().startswith('#'):
                            var_match = re.match(r'\s*(\w+)\s*=', next_line)
                            if var_match:
                                variables_in_try.append(var_match.group(1))
                        j += 1
                    
                    if variables_in_try:
                        issues['try_except_scope_issues'].append(
                            f"{function_name}:{i} - try块中定义变量: {', '.join(variables_in_try)}"
                        )
        
    except Exception as e:
        print(f"分析文件 {file_path} 时出错: {e}")
    
    return issues

def scan_ui_directory() -> Dict[str, Dict[str, List[str]]]:
    """扫描UI目录中的所有Python文件"""
    ui_dir = "src/ui"
    results = {}
    
    if not os.path.exists(ui_dir):
        print(f"UI目录不存在: {ui_dir}")
        return results
    
    for filename in os.listdir(ui_dir):
        if filename.endswith('.py') and not filename.startswith('__'):
            file_path = os.path.join(ui_dir, filename)
            print(f"分析文件: {file_path}")
            
            issues = analyze_file_for_scope_issues(file_path)
            if any(issues.values()):  # 只记录有问题的文件
                results[filename] = issues
    
    return results

def check_specific_patterns():
    """检查特定的问题模式"""
    print("\n=== 检查特定问题模式 ===")
    
    patterns_to_check = [
        (r'temp_system\s*=\s*IntelligentFusionSystem\(\)', "temp_system变量赋值"),
        (r'intelligentFusionSystem', "intelligentFusionSystem变量引用"),
        (r'if\s+.*:\s*\n\s*.*=.*\n.*else:', "条件分支变量赋值"),
        (r'try:\s*\n\s*.*=.*\n.*except', "try块变量赋值"),
    ]
    
    ui_dir = "src/ui"
    
    for filename in os.listdir(ui_dir):
        if filename.endswith('.py'):
            file_path = os.path.join(ui_dir, filename)
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                print(f"\n--- 检查文件: {filename} ---")
                
                for pattern, description in patterns_to_check:
                    matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
                    if matches:
                        print(f"  ✓ 发现 {description}: {len(matches)} 处")
                    else:
                        print(f"  - 未发现 {description}")
                        
            except Exception as e:
                print(f"  ✗ 读取文件失败: {e}")

def generate_recommendations(results: Dict[str, Dict[str, List[str]]]):
    """生成修复建议"""
    print("\n" + "="*60)
    print("🔧 修复建议")
    print("="*60)
    
    if not results:
        print("✅ 未发现明显的变量作用域问题")
        return
    
    for filename, issues in results.items():
        print(f"\n📁 文件: {filename}")
        
        for issue_type, issue_list in issues.items():
            if issue_list:
                print(f"  🔸 {issue_type}:")
                for issue in issue_list:
                    print(f"    - {issue}")
        
        # 针对性建议
        if issues['conditional_assignments']:
            print("  💡 建议: 在函数开始处初始化变量")
        
        if issues['return_statements_in_branches']:
            print("  💡 建议: 避免在条件分支中使用return，使用统一的错误处理")
        
        if issues['try_except_scope_issues']:
            print("  💡 建议: 在try块外初始化变量，或使用安全的初始化函数")

def main():
    """主分析函数"""
    print("🔍 开始UI模块变量作用域问题分析...")
    print("="*70)
    
    # 扫描UI目录
    results = scan_ui_directory()
    
    # 检查特定模式
    check_specific_patterns()
    
    # 生成建议
    generate_recommendations(results)
    
    # 总结
    print(f"\n📊 分析总结:")
    total_files = len([f for f in os.listdir("src/ui") if f.endswith('.py')])
    problem_files = len(results)
    
    print(f"  总文件数: {total_files}")
    print(f"  有问题的文件: {problem_files}")
    print(f"  问题文件比例: {problem_files/total_files*100:.1f}%")
    
    if problem_files == 0:
        print("\n🎉 所有UI文件都没有明显的变量作用域问题！")
    else:
        print(f"\n⚠️ 发现 {problem_files} 个文件存在潜在的变量作用域问题")
        print("建议按照上述建议进行修复")

if __name__ == "__main__":
    main()
