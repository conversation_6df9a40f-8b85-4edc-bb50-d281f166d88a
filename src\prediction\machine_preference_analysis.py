"""
机器设备偏好识别模块
分析开奖机器和试机机器的号码偏好和规律
"""

import os
import sqlite3
from collections import Counter, defaultdict
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd


class MachinePreferenceAnalyzer:
    """机器设备偏好分析器"""
    
    def __init__(self, db_path: str = None):
        """
        初始化分析器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path or os.path.join('data', 'lottery.db')
        self.analysis_results = {}
        
    def load_machine_data(self) -> List[Dict[str, Any]]:
        """
        从数据库加载机器设备数据
        
        Returns:
            包含机器信息和开奖数据的记录列表
        """
        if not os.path.exists(self.db_path):
            raise FileNotFoundError(f"数据库文件不存在: {self.db_path}")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 查询机器和开奖数据
        cursor.execute("""
            SELECT period, date, numbers, trial_numbers,
                   draw_machine, trial_machine, sales_amount
            FROM lottery_records
            WHERE draw_machine IS NOT NULL
            AND trial_machine IS NOT NULL
            ORDER BY date DESC, period DESC
        """)
        
        records = []
        for row in cursor.fetchall():
            record = {
                'period': row[0],
                'date': row[1],
                'numbers': row[2],
                'trial_numbers': row[3],
                'draw_machine': row[4],
                'trial_machine': row[5],
                'sales_amount': row[6] or 0
            }
            records.append(record)
        
        conn.close()
        return records
    
    def analyze_machine_number_preferences(self, records: List[Dict]) -> Dict[str, Any]:
        """
        分析各机器的号码偏好
        
        Args:
            records: 机器记录列表
            
        Returns:
            机器号码偏好分析结果
        """
        # 分别分析开奖机器和试机机器
        draw_machine_analysis = defaultdict(lambda: {
            'numbers': [], 'digits': [], 'sums': [], 'spans': [], 'count': 0
        })
        
        trial_machine_analysis = defaultdict(lambda: {
            'numbers': [], 'digits': [], 'sums': [], 'spans': [], 'count': 0
        })
        
        for record in records:
            draw_machine = record['draw_machine']
            trial_machine = record['trial_machine']
            numbers = record['numbers']
            trial_numbers = record['trial_numbers']
            
            # 分析开奖机器偏好
            if len(numbers) == 3:
                digits = [int(d) for d in numbers]
                draw_machine_analysis[draw_machine]['numbers'].append(numbers)
                draw_machine_analysis[draw_machine]['digits'].extend(digits)
                draw_machine_analysis[draw_machine]['sums'].append(sum(digits))
                draw_machine_analysis[draw_machine]['spans'].append(max(digits) - min(digits))
                draw_machine_analysis[draw_machine]['count'] += 1
            
            # 分析试机机器偏好
            if len(trial_numbers) == 3:
                trial_digits = [int(d) for d in trial_numbers]
                trial_machine_analysis[trial_machine]['numbers'].append(trial_numbers)
                trial_machine_analysis[trial_machine]['digits'].extend(trial_digits)
                trial_machine_analysis[trial_machine]['sums'].append(sum(trial_digits))
                trial_machine_analysis[trial_machine]['spans'].append(max(trial_digits) - min(trial_digits))
                trial_machine_analysis[trial_machine]['count'] += 1
        
        # 统计分析
        def analyze_machine_stats(machine_data):
            results = {}
            for machine_id, data in machine_data.items():
                if data['count'] > 0:
                    results[machine_id] = {
                        'usage_count': data['count'],
                        'digit_distribution': dict(Counter(data['digits'])),
                        'sum_distribution': dict(Counter(data['sums'])),
                        'span_distribution': dict(Counter(data['spans'])),
                        'most_common_numbers': Counter(data['numbers']).most_common(10),
                        'avg_sum': np.mean(data['sums']) if data['sums'] else 0,
                        'avg_span': np.mean(data['spans']) if data['spans'] else 0,
                        'digit_preferences': self._calculate_digit_preferences(data['digits'])
                    }
            return results
        
        return {
            'draw_machines': analyze_machine_stats(draw_machine_analysis),
            'trial_machines': analyze_machine_stats(trial_machine_analysis),
            'machine_summary': {
                'total_draw_machines': len(draw_machine_analysis),
                'total_trial_machines': len(trial_machine_analysis),
                'total_records': len(records)
            }
        }
    
    def _calculate_digit_preferences(self, digits: List[int]) -> Dict[str, Any]:
        """
        计算数字偏好指标
        
        Args:
            digits: 数字列表
            
        Returns:
            数字偏好分析结果
        """
        if not digits:
            return {}
        
        digit_counts = Counter(digits)
        total_digits = len(digits)
        
        # 计算偏好指数（相对于均匀分布的偏离程度）
        expected_count = total_digits / 10  # 期望每个数字出现的次数
        preferences = {}
        
        for digit in range(10):
            actual_count = digit_counts.get(digit, 0)
            if expected_count > 0:
                preference_index = actual_count / expected_count
                preferences[digit] = {
                    'count': actual_count,
                    'frequency': actual_count / total_digits,
                    'preference_index': preference_index,
                    'deviation': actual_count - expected_count
                }
        
        # 识别偏好和回避的数字
        sorted_prefs = sorted(preferences.items(), key=lambda x: x[1]['preference_index'], reverse=True)
        
        return {
            'digit_preferences': preferences,
            'most_preferred': sorted_prefs[:3],
            'least_preferred': sorted_prefs[-3:],
            'preference_variance': np.var([p['preference_index'] for p in preferences.values()]),
            'total_digits': total_digits
        }
    
    def analyze_machine_combinations(self, records: List[Dict]) -> Dict[str, Any]:
        """
        分析试机机器和开奖机器的组合效应
        
        Args:
            records: 机器记录列表
            
        Returns:
            机器组合分析结果
        """
        combination_stats = defaultdict(lambda: {
            'count': 0,
            'numbers': [],
            'trial_numbers': [],
            'accuracy_scores': []
        })
        
        for record in records:
            trial_machine = record['trial_machine']
            draw_machine = record['draw_machine']
            numbers = record['numbers']
            trial_numbers = record['trial_numbers']
            
            combination = (trial_machine, draw_machine)
            combination_stats[combination]['count'] += 1
            combination_stats[combination]['numbers'].append(numbers)
            combination_stats[combination]['trial_numbers'].append(trial_numbers)
            
            # 计算试机号码与正式号码的匹配度
            if len(numbers) == 3 and len(trial_numbers) == 3:
                accuracy = self._calculate_trial_accuracy(trial_numbers, numbers)
                combination_stats[combination]['accuracy_scores'].append(accuracy)
        
        # 分析组合效果
        combination_analysis = {}
        for (trial_m, draw_m), stats in combination_stats.items():
            if stats['count'] > 0:
                combination_analysis[(trial_m, draw_m)] = {
                    'usage_count': stats['count'],
                    'avg_accuracy': np.mean(stats['accuracy_scores']) if stats['accuracy_scores'] else 0,
                    'accuracy_std': np.std(stats['accuracy_scores']) if stats['accuracy_scores'] else 0,
                    'most_common_numbers': Counter(stats['numbers']).most_common(5),
                    'most_common_trials': Counter(stats['trial_numbers']).most_common(5)
                }
        
        # 找出最佳和最差组合
        if combination_analysis:
            sorted_combinations = sorted(
                combination_analysis.items(), 
                key=lambda x: x[1]['avg_accuracy'], 
                reverse=True
            )
            
            best_combinations = sorted_combinations[:5]
            worst_combinations = sorted_combinations[-5:]
        else:
            best_combinations = []
            worst_combinations = []
        
        return {
            'combination_analysis': combination_analysis,
            'best_combinations': best_combinations,
            'worst_combinations': worst_combinations,
            'total_combinations': len(combination_analysis)
        }
    
    def _calculate_trial_accuracy(self, trial_numbers: str, formal_numbers: str) -> float:
        """
        计算试机号码的准确性分数
        
        Args:
            trial_numbers: 试机号码
            formal_numbers: 正式号码
            
        Returns:
            准确性分数 (0-1)
        """
        if len(trial_numbers) != 3 or len(formal_numbers) != 3:
            return 0
        
        # 位置匹配分数
        position_score = sum(1 for i in range(3) if trial_numbers[i] == formal_numbers[i]) / 3
        
        # 数字包含分数
        trial_set = set(trial_numbers)
        formal_set = set(formal_numbers)
        inclusion_score = len(trial_set & formal_set) / 3
        
        # 综合分数
        return (position_score * 0.7 + inclusion_score * 0.3)
    
    def analyze_machine_temporal_patterns(self, records: List[Dict]) -> Dict[str, Any]:
        """
        分析机器使用的时间模式
        
        Args:
            records: 机器记录列表
            
        Returns:
            机器时间模式分析结果
        """
        temporal_patterns = {
            'draw_machine_timeline': defaultdict(list),
            'trial_machine_timeline': defaultdict(list),
            'machine_usage_frequency': defaultdict(int),
            'machine_performance_over_time': defaultdict(list)
        }
        
        for record in records:
            try:
                date_obj = datetime.strptime(record['date'], '%Y-%m-%d')
                year_month = f"{date_obj.year}-{date_obj.month:02d}"
                
                draw_machine = record['draw_machine']
                trial_machine = record['trial_machine']
                
                # 记录机器使用时间线
                temporal_patterns['draw_machine_timeline'][draw_machine].append(date_obj)
                temporal_patterns['trial_machine_timeline'][trial_machine].append(date_obj)
                
                # 统计使用频率
                temporal_patterns['machine_usage_frequency'][(year_month, draw_machine)] += 1
                
                # 计算性能指标
                if len(record['numbers']) == 3 and len(record['trial_numbers']) == 3:
                    accuracy = self._calculate_trial_accuracy(record['trial_numbers'], record['numbers'])
                    temporal_patterns['machine_performance_over_time'][draw_machine].append({
                        'date': date_obj,
                        'accuracy': accuracy,
                        'numbers': record['numbers']
                    })
                    
            except (ValueError, TypeError):
                continue
        
        # 分析时间模式
        pattern_analysis = {}
        
        # 机器使用寿命分析
        for machine_type in ['draw_machine_timeline', 'trial_machine_timeline']:
            pattern_analysis[machine_type] = {}
            for machine_id, dates in temporal_patterns[machine_type].items():
                if dates:
                    dates.sort()
                    pattern_analysis[machine_type][machine_id] = {
                        'first_use': dates[0].strftime('%Y-%m-%d'),
                        'last_use': dates[-1].strftime('%Y-%m-%d'),
                        'usage_span_days': (dates[-1] - dates[0]).days,
                        'total_uses': len(dates),
                        'avg_uses_per_month': len(dates) / max(1, (dates[-1] - dates[0]).days / 30)
                    }
        
        # 性能趋势分析
        performance_trends = {}
        for machine_id, performance_data in temporal_patterns['machine_performance_over_time'].items():
            if len(performance_data) > 1:
                # 按时间排序
                performance_data.sort(key=lambda x: x['date'])
                
                # 计算趋势
                accuracies = [p['accuracy'] for p in performance_data]
                dates_numeric = [(p['date'] - performance_data[0]['date']).days for p in performance_data]
                
                if len(accuracies) > 1:
                    # 简单线性趋势
                    correlation = np.corrcoef(dates_numeric, accuracies)[0, 1] if len(set(accuracies)) > 1 else 0
                    
                    performance_trends[machine_id] = {
                        'total_records': len(performance_data),
                        'avg_accuracy': np.mean(accuracies),
                        'accuracy_trend': correlation,
                        'performance_stability': 1 - np.std(accuracies),  # 稳定性指标
                        'recent_performance': np.mean(accuracies[-5:]) if len(accuracies) >= 5 else np.mean(accuracies)
                    }
        
        return {
            'usage_patterns': pattern_analysis,
            'performance_trends': performance_trends,
            'usage_frequency': dict(temporal_patterns['machine_usage_frequency'])
        }
    
    def extract_machine_features(self, records: List[Dict]) -> Dict[str, List]:
        """
        提取机器相关特征用于机器学习
        
        Args:
            records: 机器记录列表
            
        Returns:
            机器特征字典
        """
        features = {
            'draw_machine_id': [],
            'trial_machine_id': [],
            'machine_combination': [],
            'draw_machine_usage_rank': [],
            'trial_machine_usage_rank': [],
            'machine_accuracy_history': []
        }
        
        # 统计机器使用频率
        draw_machine_counts = Counter(r['draw_machine'] for r in records)
        trial_machine_counts = Counter(r['trial_machine'] for r in records)
        
        # 计算机器使用排名
        draw_machine_ranks = {machine: rank for rank, (machine, _) in 
                            enumerate(draw_machine_counts.most_common(), 1)}
        trial_machine_ranks = {machine: rank for rank, (machine, _) in 
                             enumerate(trial_machine_counts.most_common(), 1)}
        
        # 计算机器历史准确性
        machine_accuracy_history = defaultdict(list)
        for record in records:
            if len(record['numbers']) == 3 and len(record['trial_numbers']) == 3:
                accuracy = self._calculate_trial_accuracy(record['trial_numbers'], record['numbers'])
                machine_accuracy_history[record['draw_machine']].append(accuracy)
        
        # 提取特征
        for record in records:
            draw_machine = record['draw_machine']
            trial_machine = record['trial_machine']
            
            features['draw_machine_id'].append(draw_machine)
            features['trial_machine_id'].append(trial_machine)
            features['machine_combination'].append(draw_machine * 10 + trial_machine)
            features['draw_machine_usage_rank'].append(draw_machine_ranks.get(draw_machine, 0))
            features['trial_machine_usage_rank'].append(trial_machine_ranks.get(trial_machine, 0))
            
            # 机器历史准确性
            if draw_machine in machine_accuracy_history:
                avg_accuracy = np.mean(machine_accuracy_history[draw_machine])
            else:
                avg_accuracy = 0.5  # 默认值
            features['machine_accuracy_history'].append(avg_accuracy)
        
        return features
    
    def train_model(self) -> Dict[str, Any]:
        """
        训练机器设备偏好识别模型
        
        Returns:
            训练结果和模型性能
        """
        print("开始训练机器设备偏好识别模型...")
        
        # 加载数据
        records = self.load_machine_data()
        print(f"加载了 {len(records)} 条机器记录")
        
        if len(records) < 10:
            raise ValueError("机器数据不足，无法训练模型")
        
        # 执行各种分析
        print("分析机器号码偏好...")
        preference_analysis = self.analyze_machine_number_preferences(records)
        
        print("分析机器组合效应...")
        combination_analysis = self.analyze_machine_combinations(records)
        
        print("分析机器时间模式...")
        temporal_analysis = self.analyze_machine_temporal_patterns(records)
        
        print("提取机器特征...")
        machine_features = self.extract_machine_features(records)
        
        # 保存分析结果
        self.analysis_results = {
            'preference_analysis': preference_analysis,
            'combination_analysis': combination_analysis,
            'temporal_analysis': temporal_analysis,
            'machine_features': machine_features,
            'data_summary': {
                'total_records': len(records),
                'date_range': (records[0]['date'], records[-1]['date']) if records else None,
                'unique_draw_machines': len(set(r['draw_machine'] for r in records)),
                'unique_trial_machines': len(set(r['trial_machine'] for r in records))
            }
        }
        
        print("机器设备偏好识别模型训练完成!")
        return {
            'success': True,
            'analysis_results': self.analysis_results
        }


if __name__ == "__main__":
    # 测试代码
    analyzer = MachinePreferenceAnalyzer()
    
    try:
        # 训练模型
        result = analyzer.train_model()
        print("训练结果:", result['success'])
        
        if result['success']:
            data_summary = result['analysis_results']['data_summary']
            print("数据摘要:", data_summary)
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
