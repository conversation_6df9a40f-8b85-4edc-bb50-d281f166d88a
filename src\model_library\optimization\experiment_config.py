"""
实验配置管理
实现ExperimentConfig、ExperimentState数据结构，实验生命周期管理、配置验证序列化
"""

import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field, asdict
from enum import Enum
import hashlib


class ExperimentType(Enum):
    """实验类型"""
    AB_TEST = "ab_test"
    MULTIVARIATE = "multivariate"
    BANDIT = "bandit"
    SEQUENTIAL = "sequential"


class ExperimentPhase(Enum):
    """实验阶段"""
    DESIGN = "design"
    SETUP = "setup"
    RUNNING = "running"
    ANALYSIS = "analysis"
    COMPLETED = "completed"


@dataclass
class ExperimentMetrics:
    """实验指标配置"""
    primary_metric: str
    secondary_metrics: List[str] = field(default_factory=list)
    success_criteria: Dict[str, float] = field(default_factory=dict)
    minimum_detectable_effect: float = 0.05
    statistical_power: float = 0.8
    significance_level: float = 0.05


@dataclass
class ExperimentConstraints:
    """实验约束"""
    min_sample_size: int = 100
    max_sample_size: Optional[int] = None
    min_duration_hours: int = 24
    max_duration_hours: int = 720  # 30天
    traffic_allocation: float = 1.0  # 分配给实验的流量比例
    exclusion_rules: List[str] = field(default_factory=list)


@dataclass
class ExperimentArm:
    """实验分支配置"""
    arm_id: str
    name: str
    description: str
    configuration: Dict[str, Any]
    traffic_weight: float = 1.0
    is_control: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExperimentArm':
        return cls(**data)


@dataclass
class ExperimentConfig:
    """实验配置"""
    experiment_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    experiment_type: ExperimentType = ExperimentType.AB_TEST
    
    # 实验设计
    arms: List[ExperimentArm] = field(default_factory=list)
    metrics: ExperimentMetrics = field(default_factory=ExperimentMetrics)
    constraints: ExperimentConstraints = field(default_factory=ExperimentConstraints)
    
    # 分配策略
    allocation_strategy: str = "equal"
    allocation_parameters: Dict[str, Any] = field(default_factory=dict)
    
    # 元数据
    created_by: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    tags: List[str] = field(default_factory=list)
    
    # 审批和版本控制
    version: int = 1
    approved: bool = False
    approved_by: Optional[str] = None
    approved_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['experiment_type'] = self.experiment_type.value
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        if self.approved_at:
            data['approved_at'] = self.approved_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExperimentConfig':
        """从字典创建"""
        # 处理枚举
        if 'experiment_type' in data:
            data['experiment_type'] = ExperimentType(data['experiment_type'])
        
        # 处理日期时间
        if 'created_at' in data and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if 'updated_at' in data and isinstance(data['updated_at'], str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        if 'approved_at' in data and data['approved_at']:
            data['approved_at'] = datetime.fromisoformat(data['approved_at'])
        
        # 处理嵌套对象
        if 'arms' in data:
            data['arms'] = [ExperimentArm.from_dict(arm) if isinstance(arm, dict) else arm 
                           for arm in data['arms']]
        if 'metrics' in data and isinstance(data['metrics'], dict):
            data['metrics'] = ExperimentMetrics(**data['metrics'])
        if 'constraints' in data and isinstance(data['constraints'], dict):
            data['constraints'] = ExperimentConstraints(**data['constraints'])
        
        return cls(**data)
    
    def to_json(self) -> str:
        """转换为JSON"""
        return json.dumps(self.to_dict(), indent=2, ensure_ascii=False)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'ExperimentConfig':
        """从JSON创建"""
        data = json.loads(json_str)
        return cls.from_dict(data)
    
    def get_config_hash(self) -> str:
        """获取配置哈希"""
        config_str = json.dumps(self.to_dict(), sort_keys=True)
        return hashlib.sha256(config_str.encode()).hexdigest()
    
    def validate(self) -> List[str]:
        """验证配置"""
        errors = []
        
        # 基本验证
        if not self.name:
            errors.append("实验名称不能为空")
        
        if not self.arms:
            errors.append("至少需要一个实验分支")
        elif len(self.arms) < 2 and self.experiment_type == ExperimentType.AB_TEST:
            errors.append("A/B测试至少需要两个分支")
        
        # 分支验证
        arm_ids = set()
        total_weight = 0
        control_count = 0
        
        for arm in self.arms:
            if not arm.arm_id:
                errors.append("分支ID不能为空")
            elif arm.arm_id in arm_ids:
                errors.append(f"分支ID重复: {arm.arm_id}")
            else:
                arm_ids.add(arm.arm_id)
            
            if arm.traffic_weight <= 0:
                errors.append(f"分支 {arm.arm_id} 的流量权重必须大于0")
            
            total_weight += arm.traffic_weight
            
            if arm.is_control:
                control_count += 1
        
        if control_count == 0:
            errors.append("至少需要一个控制组")
        elif control_count > 1:
            errors.append("只能有一个控制组")
        
        # 指标验证
        if not self.metrics.primary_metric:
            errors.append("必须指定主要指标")
        
        if self.metrics.significance_level <= 0 or self.metrics.significance_level >= 1:
            errors.append("显著性水平必须在0和1之间")
        
        if self.metrics.statistical_power <= 0 or self.metrics.statistical_power >= 1:
            errors.append("统计功效必须在0和1之间")
        
        # 约束验证
        if self.constraints.min_sample_size <= 0:
            errors.append("最小样本量必须大于0")
        
        if (self.constraints.max_sample_size and 
            self.constraints.max_sample_size < self.constraints.min_sample_size):
            errors.append("最大样本量不能小于最小样本量")
        
        if self.constraints.min_duration_hours <= 0:
            errors.append("最小持续时间必须大于0")
        
        if self.constraints.max_duration_hours < self.constraints.min_duration_hours:
            errors.append("最大持续时间不能小于最小持续时间")
        
        if self.constraints.traffic_allocation <= 0 or self.constraints.traffic_allocation > 1:
            errors.append("流量分配比例必须在0和1之间")
        
        return errors
    
    def clone(self, new_name: Optional[str] = None) -> 'ExperimentConfig':
        """克隆配置"""
        cloned = ExperimentConfig.from_dict(self.to_dict())
        cloned.experiment_id = str(uuid.uuid4())
        cloned.name = new_name or f"{self.name}_copy"
        cloned.version = 1
        cloned.approved = False
        cloned.approved_by = None
        cloned.approved_at = None
        cloned.created_at = datetime.now()
        cloned.updated_at = datetime.now()
        
        return cloned


@dataclass
class ExperimentState:
    """实验状态"""
    experiment_id: str
    phase: ExperimentPhase = ExperimentPhase.DESIGN
    
    # 执行状态
    started_at: Optional[datetime] = None
    paused_at: Optional[datetime] = None
    resumed_at: Optional[datetime] = None
    stopped_at: Optional[datetime] = None
    
    # 统计数据
    total_participants: int = 0
    arm_participants: Dict[str, int] = field(default_factory=dict)
    
    # 实时指标
    current_metrics: Dict[str, float] = field(default_factory=dict)
    arm_metrics: Dict[str, Dict[str, float]] = field(default_factory=dict)
    
    # 决策状态
    early_stopping_triggered: bool = False
    early_stopping_reason: Optional[str] = None
    winner_declared: Optional[str] = None
    confidence_level: float = 0.0
    
    # 错误和警告
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['phase'] = self.phase.value
        
        # 处理日期时间
        for field_name in ['started_at', 'paused_at', 'resumed_at', 'stopped_at']:
            if data[field_name]:
                data[field_name] = data[field_name].isoformat()
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExperimentState':
        """从字典创建"""
        # 处理枚举
        if 'phase' in data:
            data['phase'] = ExperimentPhase(data['phase'])
        
        # 处理日期时间
        for field_name in ['started_at', 'paused_at', 'resumed_at', 'stopped_at']:
            if data.get(field_name):
                data[field_name] = datetime.fromisoformat(data[field_name])
        
        return cls(**data)
    
    def get_duration(self) -> Optional[timedelta]:
        """获取运行时长"""
        if not self.started_at:
            return None
        
        end_time = self.stopped_at or datetime.now()
        
        # 计算暂停时间
        paused_duration = timedelta()
        if self.paused_at and self.resumed_at:
            paused_duration = self.resumed_at - self.paused_at
        elif self.paused_at and not self.resumed_at:
            paused_duration = end_time - self.paused_at
        
        total_duration = end_time - self.started_at - paused_duration
        return total_duration
    
    def is_running(self) -> bool:
        """是否正在运行"""
        return (self.phase == ExperimentPhase.RUNNING and 
                self.started_at is not None and 
                self.stopped_at is None and
                (self.paused_at is None or self.resumed_at is not None))
    
    def add_participant(self, arm_id: str):
        """添加参与者"""
        self.total_participants += 1
        if arm_id not in self.arm_participants:
            self.arm_participants[arm_id] = 0
        self.arm_participants[arm_id] += 1
    
    def update_metrics(self, arm_id: str, metrics: Dict[str, float]):
        """更新指标"""
        if arm_id not in self.arm_metrics:
            self.arm_metrics[arm_id] = {}
        
        self.arm_metrics[arm_id].update(metrics)
        
        # 更新总体指标
        for metric_name, value in metrics.items():
            if metric_name not in self.current_metrics:
                self.current_metrics[metric_name] = 0
            
            # 简单平均（实际应该根据参与者数量加权）
            total_arms = len(self.arm_metrics)
            self.current_metrics[metric_name] = (
                (self.current_metrics[metric_name] * (total_arms - 1) + value) / total_arms
            )


class ExperimentConfigManager:
    """实验配置管理器"""
    
    def __init__(self):
        self.configs: Dict[str, ExperimentConfig] = {}
        self.states: Dict[str, ExperimentState] = {}
        self.config_history: Dict[str, List[ExperimentConfig]] = {}
    
    def create_config(self, config: ExperimentConfig) -> bool:
        """创建配置"""
        # 验证配置
        errors = config.validate()
        if errors:
            raise ValueError(f"配置验证失败: {errors}")
        
        # 检查ID冲突
        if config.experiment_id in self.configs:
            raise ValueError(f"实验ID已存在: {config.experiment_id}")
        
        # 保存配置
        self.configs[config.experiment_id] = config
        
        # 创建初始状态
        state = ExperimentState(experiment_id=config.experiment_id)
        self.states[config.experiment_id] = state
        
        # 初始化历史
        self.config_history[config.experiment_id] = [config]
        
        return True
    
    def update_config(self, experiment_id: str, updated_config: ExperimentConfig) -> bool:
        """更新配置"""
        if experiment_id not in self.configs:
            raise ValueError(f"实验不存在: {experiment_id}")
        
        # 验证更新的配置
        errors = updated_config.validate()
        if errors:
            raise ValueError(f"配置验证失败: {errors}")
        
        # 检查是否可以更新（只有在设计阶段才能更新）
        state = self.states[experiment_id]
        if state.phase != ExperimentPhase.DESIGN:
            raise ValueError(f"实验阶段 {state.phase.value} 不允许更新配置")
        
        # 更新版本号
        old_config = self.configs[experiment_id]
        updated_config.version = old_config.version + 1
        updated_config.updated_at = datetime.now()
        
        # 保存配置
        self.configs[experiment_id] = updated_config
        
        # 添加到历史
        self.config_history[experiment_id].append(updated_config)
        
        return True
    
    def get_config(self, experiment_id: str) -> Optional[ExperimentConfig]:
        """获取配置"""
        return self.configs.get(experiment_id)
    
    def get_state(self, experiment_id: str) -> Optional[ExperimentState]:
        """获取状态"""
        return self.states.get(experiment_id)
    
    def approve_config(self, experiment_id: str, approved_by: str) -> bool:
        """审批配置"""
        if experiment_id not in self.configs:
            return False
        
        config = self.configs[experiment_id]
        config.approved = True
        config.approved_by = approved_by
        config.approved_at = datetime.now()
        
        # 更新状态到设置阶段
        state = self.states[experiment_id]
        state.phase = ExperimentPhase.SETUP
        
        return True
    
    def start_experiment(self, experiment_id: str) -> bool:
        """启动实验"""
        if experiment_id not in self.configs:
            return False
        
        config = self.configs[experiment_id]
        state = self.states[experiment_id]
        
        # 检查是否已审批
        if not config.approved:
            raise ValueError("实验未审批，无法启动")
        
        # 检查状态
        if state.phase != ExperimentPhase.SETUP:
            raise ValueError(f"实验阶段 {state.phase.value} 不允许启动")
        
        # 启动实验
        state.phase = ExperimentPhase.RUNNING
        state.started_at = datetime.now()
        
        return True
    
    def stop_experiment(self, experiment_id: str, reason: Optional[str] = None) -> bool:
        """停止实验"""
        if experiment_id not in self.states:
            return False
        
        state = self.states[experiment_id]
        
        if state.phase != ExperimentPhase.RUNNING:
            return False
        
        state.phase = ExperimentPhase.ANALYSIS
        state.stopped_at = datetime.now()
        
        if reason:
            state.early_stopping_triggered = True
            state.early_stopping_reason = reason
        
        return True
    
    def export_config(self, experiment_id: str) -> Optional[str]:
        """导出配置"""
        config = self.get_config(experiment_id)
        if config:
            return config.to_json()
        return None
    
    def import_config(self, json_str: str) -> str:
        """导入配置"""
        config = ExperimentConfig.from_json(json_str)
        
        # 生成新ID避免冲突
        config.experiment_id = str(uuid.uuid4())
        config.approved = False
        config.approved_by = None
        config.approved_at = None
        
        self.create_config(config)
        return config.experiment_id


def test_experiment_config():
    """测试实验配置管理"""
    print("🧪 测试实验配置管理...")
    
    # 创建实验配置
    config = ExperimentConfig(
        name="测试实验",
        description="测试实验配置管理功能",
        experiment_type=ExperimentType.AB_TEST
    )
    
    # 添加分支
    config.arms = [
        ExperimentArm(
            arm_id="control",
            name="控制组",
            description="当前配置",
            configuration={"param1": "value1"},
            is_control=True
        ),
        ExperimentArm(
            arm_id="treatment",
            name="实验组",
            description="新配置",
            configuration={"param1": "value2"}
        )
    ]
    
    # 设置指标
    config.metrics = ExperimentMetrics(
        primary_metric="conversion_rate",
        secondary_metrics=["click_rate", "engagement"]
    )
    
    # 验证配置
    errors = config.validate()
    print(f"✅ 配置验证: {len(errors)} 个错误")
    
    # 测试序列化
    json_str = config.to_json()
    restored_config = ExperimentConfig.from_json(json_str)
    print(f"✅ 序列化测试: {restored_config.name}")
    
    # 测试配置管理器
    manager = ExperimentConfigManager()
    manager.create_config(config)
    
    # 审批配置
    manager.approve_config(config.experiment_id, "test_user")
    
    # 启动实验
    manager.start_experiment(config.experiment_id)
    
    state = manager.get_state(config.experiment_id)
    print(f"✅ 实验状态: {state.phase.value}")
    print(f"✅ 运行状态: {state.is_running()}")
    
    print("✅ 实验配置管理测试完成！")


if __name__ == "__main__":
    test_experiment_config()
