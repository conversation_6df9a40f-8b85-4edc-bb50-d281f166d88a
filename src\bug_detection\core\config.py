"""
Bug检测系统配置
创建日期: 2025年7月24日
用途: Bug检测系统的配置管理
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional

class BugDetectionConfig:
    """Bug检测系统配置类"""
    
    def __init__(self):
        """初始化配置"""
        self.base_dir = Path(__file__).parent.parent.parent.parent
        self.data_dir = self.base_dir / "data"
        self.logs_dir = self.base_dir / "logs"
        
        # 确保目录存在
        self.data_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)
        
        # 数据库配置
        self.database = {
            'path': str(self.data_dir / 'bug_detection.db'),
            'timeout': 30,
            'check_same_thread': False
        }
        
        # JavaScript监控配置
        self.js_monitoring = {
            'enabled': True,
            'capture_console_errors': True,
            'capture_unhandled_rejections': True,
            'capture_resource_errors': True,
            'session_timeout': 3600  # 1小时
        }
        
        # API监控配置
        self.api_monitoring = {
            'enabled': True,
            'exclude_paths': ['/docs', '/redoc', '/openapi.json', '/favicon.ico'],
            'slow_request_threshold': 1.0,  # 秒
            'error_rate_threshold': 0.05,   # 5%
            'max_request_size': 10 * 1024 * 1024  # 10MB
        }
        
        # Bug报告配置
        self.bug_reporting = {
            'auto_generate': True,
            'include_stack_trace': True,
            'include_user_agent': True,
            'include_session_info': True,
            'max_similar_bugs': 5
        }
        
        # 性能监控配置
        self.performance = {
            'enabled': True,
            'metrics_retention_days': 30,
            'alert_thresholds': {
                'response_time': 2.0,  # 秒
                'error_rate': 0.1,     # 10%
                'memory_usage': 0.8    # 80%
            }
        }
        
        # 日志配置
        self.logging = {
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'file_path': str(self.logs_dir / 'bug_detection.log'),
            'max_file_size': 10 * 1024 * 1024,  # 10MB
            'backup_count': 5
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self.__dict__
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """设置配置值"""
        keys = key.split('.')
        config = self.__dict__
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'database': self.database,
            'js_monitoring': self.js_monitoring,
            'api_monitoring': self.api_monitoring,
            'bug_reporting': self.bug_reporting,
            'performance': self.performance,
            'logging': self.logging
        }

# 全局配置实例
config = BugDetectionConfig()
