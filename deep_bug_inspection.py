#!/usr/bin/env python3
"""
深度Bug检查工具
创建日期: 2025年7月24日
用途: 演示如何使用Bug检测系统进行深度检查和分析
"""

import sys
import os
import json
import time
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any

# 添加项目路径
sys.path.insert(0, '.')

def print_header(title: str):
    """打印标题"""
    print("\n" + "="*60)
    print(f"🔍 {title}")
    print("="*60)

def print_section(title: str):
    """打印章节"""
    print(f"\n📋 {title}")
    print("-"*40)

async def demonstrate_deep_inspection():
    """演示深度检查功能"""
    
    print_header("全自动Bug检测与反馈系统 - 深度检查演示")
    
    # 1. 系统初始化检查
    print_section("系统初始化检查")
    
    try:
        from src.bug_detection.core.database_manager import DatabaseManager
        from src.bug_detection.monitoring.js_monitor import JavaScriptMonitor
        from src.bug_detection.feedback.bug_reporter import IntelligentBugReporter
        from src.bug_detection.monitoring.api_monitor import APIPerformanceMonitor
        
        print("✅ 所有核心组件导入成功")
        
        # 初始化组件
        db_manager = DatabaseManager()
        js_monitor = JavaScriptMonitor(db_manager)
        bug_reporter = IntelligentBugReporter(db_manager)
        
        print("✅ 组件初始化完成")
        
    except Exception as e:
        print(f"❌ 组件初始化失败: {e}")
        return
    
    # 2. 数据库深度检查
    print_section("数据库深度检查")
    
    try:
        # 检查数据库连接
        import sqlite3
        conn = sqlite3.connect(db_manager.db_path)
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        expected_tables = ['bug_reports', 'user_behaviors', 'performance_metrics', 'js_errors']
        
        print(f"📊 数据库路径: {db_manager.db_path}")
        print(f"📊 发现表数量: {len(tables)}")
        
        for table in expected_tables:
            if table in tables:
                # 检查表记录数
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"✅ {table}: {count} 条记录")
            else:
                print(f"❌ {table}: 表不存在")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
    
    # 3. Bug报告生成深度测试
    print_section("Bug报告生成深度测试")
    
    test_errors = [
        {
            'type': 'javascript',
            'message': 'TypeError: Cannot read property "data" of undefined',
            'source': 'prediction_analysis.js',
            'line_number': 42,
            'page_url': 'http://127.0.0.1:8501/?page=prediction_analysis',
            'session_id': 'test_session_001'
        },
        {
            'type': 'api_error',
            'message': 'HTTP 500: Internal Server Error',
            'source': '/api/v1/prediction',
            'page_url': 'http://127.0.0.1:8501/?page=intelligent_fusion',
            'session_id': 'test_session_002'
        },
        {
            'type': 'network_error',
            'message': 'Failed to fetch data from external API',
            'source': 'data_update.py',
            'page_url': 'http://127.0.0.1:8501/?page=data_update',
            'session_id': 'test_session_003'
        }
    ]
    
    generated_reports = []
    
    for i, error_data in enumerate(test_errors, 1):
        print(f"\n🧪 测试 {i}: {error_data['type']} 错误")
        
        try:
            # 生成Bug报告
            bug_report = bug_reporter.generate_enhanced_report(error_data)
            generated_reports.append(bug_report)
            
            print(f"✅ Bug ID: {bug_report['id']}")
            print(f"📊 严重程度: {bug_report['error']['severity']}")
            print(f"🏷️ 分类: {bug_report['category']}")
            print(f"🎯 优先级: {bug_report['priority']}")
            print(f"🔧 修复建议数: {len(bug_report['suggested_fixes'])}")
            
            # 显示修复建议
            print("💡 修复建议:")
            for j, suggestion in enumerate(bug_report['suggested_fixes'][:3], 1):
                print(f"   {j}. {suggestion}")
            
            # 显示影响分析
            if 'impact_analysis' in bug_report:
                impact = bug_report['impact_analysis']
                print(f"📈 用户影响: {impact['user_impact']}")
                print(f"🖥️ 系统影响: {impact['system_impact']}")
            
        except Exception as e:
            print(f"❌ Bug报告生成失败: {e}")
    
    # 4. 智能分析功能测试
    print_section("智能分析功能测试")
    
    if generated_reports:
        print("🧠 分析生成的Bug报告...")
        
        # 分析错误类型分布
        error_types = {}
        severity_dist = {}
        
        for report in generated_reports:
            error_type = report['error']['type']
            severity = report['error']['severity']
            
            error_types[error_type] = error_types.get(error_type, 0) + 1
            severity_dist[severity] = severity_dist.get(severity, 0) + 1
        
        print("\n📊 错误类型分布:")
        for error_type, count in error_types.items():
            print(f"   {error_type}: {count} 个")
        
        print("\n📊 严重程度分布:")
        for severity, count in severity_dist.items():
            print(f"   {severity}: {count} 个")
        
        # 相似性分析
        print("\n🔍 相似性分析:")
        for i, report1 in enumerate(generated_reports):
            for j, report2 in enumerate(generated_reports[i+1:], i+1):
                similarity = bug_reporter._calculate_similarity(
                    report1['error'], 
                    {'error_type': report2['error']['type'], 
                     'error_message': report2['error']['message']}
                )
                if similarity > 0.3:
                    print(f"   Bug {report1['id'][:8]} 与 Bug {report2['id'][:8]} 相似度: {similarity:.2%}")
    
    # 5. 性能监控测试
    print_section("性能监控测试")
    
    try:
        # 模拟API调用性能数据
        test_endpoints = [
            ('/api/v1/prediction', 0.15, 200),
            ('/api/v1/data/update', 0.05, 200),
            ('/api/v1/analysis', 0.25, 200),
            ('/api/v1/prediction', 0.8, 500),  # 慢请求
            ('/api/v1/monitoring', 0.03, 200)
        ]
        
        print("📈 模拟API性能数据...")
        
        for endpoint, response_time, status_code in test_endpoints:
            db_manager.save_performance_metric(endpoint, response_time, status_code)
            print(f"   {endpoint}: {response_time}s, HTTP {status_code}")
        
        # 获取性能摘要
        performance_summary = db_manager.get_performance_summary()
        
        print("\n📊 性能摘要:")
        for endpoint, stats in performance_summary.items():
            print(f"   {endpoint}:")
            print(f"     平均响应时间: {stats['avg_time']:.3f}s")
            print(f"     最大响应时间: {stats['max_time']:.3f}s")
            print(f"     请求次数: {stats['count']}")
        
        # 性能告警检查
        print("\n⚠️ 性能告警:")
        alerts = []
        for endpoint, stats in performance_summary.items():
            if stats['avg_time'] > 0.2:
                alerts.append(f"🟡 {endpoint}: 平均响应时间过长 ({stats['avg_time']:.3f}s)")
            if stats['max_time'] > 0.5:
                alerts.append(f"🔴 {endpoint}: 最大响应时间过长 ({stats['max_time']:.3f}s)")
        
        if alerts:
            for alert in alerts:
                print(f"   {alert}")
        else:
            print("   ✅ 所有API性能正常")
            
    except Exception as e:
        print(f"❌ 性能监控测试失败: {e}")
    
    # 6. JavaScript错误监控测试
    print_section("JavaScript错误监控测试")
    
    try:
        # 模拟JavaScript错误
        js_errors = [
            ('test_session_001', 'TypeError: Cannot read property of undefined', 'http://127.0.0.1:8501/?page=prediction'),
            ('test_session_002', 'ReferenceError: variable is not defined', 'http://127.0.0.1:8501/?page=analysis'),
            ('test_session_001', 'NetworkError: Failed to fetch', 'http://127.0.0.1:8501/?page=data_update')
        ]
        
        print("🔧 模拟JavaScript错误...")
        
        for session_id, error_message, page_url in js_errors:
            db_manager.save_js_error(session_id, error_message, page_url)
            print(f"   会话 {session_id}: {error_message}")
        
        print("✅ JavaScript错误记录完成")
        
    except Exception as e:
        print(f"❌ JavaScript错误监控测试失败: {e}")
    
    # 7. 综合分析报告
    print_section("综合分析报告")
    
    try:
        # 获取所有Bug报告
        all_bug_reports = db_manager.get_bug_reports(limit=100)
        
        print(f"📊 总Bug报告数: {len(all_bug_reports)}")
        
        if all_bug_reports:
            # 按严重程度统计
            severity_stats = {}
            for bug in all_bug_reports:
                severity = bug.get('severity', 'unknown')
                severity_stats[severity] = severity_stats.get(severity, 0) + 1
            
            print("\n📈 严重程度统计:")
            for severity, count in sorted(severity_stats.items()):
                print(f"   {severity}: {count} 个")
            
            # 最近的Bug
            print(f"\n🕒 最新Bug报告:")
            latest_bug = all_bug_reports[0]
            print(f"   ID: {latest_bug.get('id', 'N/A')}")
            print(f"   类型: {latest_bug.get('error_type', 'N/A')}")
            print(f"   严重程度: {latest_bug.get('severity', 'N/A')}")
            print(f"   时间: {latest_bug.get('created_at', 'N/A')}")
        
        # 系统健康度评估
        print("\n🏥 系统健康度评估:")
        
        total_errors = len(all_bug_reports)
        critical_errors = len([bug for bug in all_bug_reports if bug.get('severity') == 'critical'])
        
        if total_errors == 0:
            health_score = 100
        elif critical_errors > 0:
            health_score = max(0, 100 - critical_errors * 20 - (total_errors - critical_errors) * 5)
        else:
            health_score = max(0, 100 - total_errors * 5)
        
        print(f"   健康评分: {health_score}/100")
        
        if health_score >= 90:
            print("   状态: 🟢 优秀")
        elif health_score >= 70:
            print("   状态: 🟡 良好")
        elif health_score >= 50:
            print("   状态: 🟠 需要关注")
        else:
            print("   状态: 🔴 需要紧急处理")
        
    except Exception as e:
        print(f"❌ 综合分析失败: {e}")
    
    # 8. 优化建议
    print_section("智能优化建议")
    
    suggestions = [
        "🚀 为高频API添加缓存机制以提升性能",
        "🛡️ 在前端添加更多的错误边界处理",
        "📊 增加用户行为分析以发现潜在问题",
        "🔧 为慢查询添加数据库索引优化",
        "📈 建立性能基线监控和告警机制",
        "🧪 增加自动化回归测试覆盖率",
        "🔍 实施代码质量检查和静态分析",
        "📱 添加移动端兼容性测试"
    ]
    
    print("💡 基于当前分析结果的优化建议:")
    for i, suggestion in enumerate(suggestions[:5], 1):
        print(f"   {i}. {suggestion}")
    
    print_header("深度检查完成")
    print("🎉 Bug检测系统深度检查已完成！")
    print("📋 请根据上述分析结果和建议进行系统优化。")
    print("🔄 建议定期执行深度检查以保持系统健康。")

def run_quick_health_check():
    """快速健康检查"""
    print_header("快速健康检查")
    
    checks = [
        ("数据库连接", check_database),
        ("组件导入", check_imports),
        ("文件结构", check_file_structure),
        ("API端点", check_api_endpoints)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        print(f"\n🔍 检查 {check_name}...")
        try:
            result = check_func()
            if result:
                print(f"✅ {check_name}: 正常")
                results.append(True)
            else:
                print(f"❌ {check_name}: 异常")
                results.append(False)
        except Exception as e:
            print(f"❌ {check_name}: 错误 - {e}")
            results.append(False)
    
    success_rate = sum(results) / len(results) * 100
    print(f"\n📊 健康检查结果: {success_rate:.1f}% ({sum(results)}/{len(results)})")
    
    if success_rate == 100:
        print("🎉 系统完全健康！")
    elif success_rate >= 75:
        print("🟡 系统基本健康，有少量问题需要关注")
    else:
        print("🔴 系统存在较多问题，需要立即处理")

def check_database():
    """检查数据库"""
    try:
        from src.bug_detection.core.database_manager import DatabaseManager
        db_manager = DatabaseManager()
        
        import sqlite3
        conn = sqlite3.connect(db_manager.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        conn.close()
        return True
    except:
        return False

def check_imports():
    """检查组件导入"""
    try:
        from src.bug_detection.core.database_manager import DatabaseManager
        from src.bug_detection.monitoring.js_monitor import JavaScriptMonitor
        from src.bug_detection.feedback.bug_reporter import IntelligentBugReporter
        return True
    except:
        return False

def check_file_structure():
    """检查文件结构"""
    required_files = [
        'src/bug_detection/core/database_manager.py',
        'src/bug_detection/monitoring/js_monitor.py',
        'src/bug_detection/feedback/bug_reporter.py',
        'src/api/bug_detection/monitoring.py'
    ]
    
    return all(os.path.exists(file) for file in required_files)

def check_api_endpoints():
    """检查API端点"""
    try:
        from src.api.bug_detection.monitoring import router
        return router is not None
    except:
        return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Bug检测系统深度检查工具")
    parser.add_argument("--mode", choices=["deep", "quick"], default="deep", 
                       help="检查模式: deep(深度检查) 或 quick(快速检查)")
    
    args = parser.parse_args()
    
    if args.mode == "deep":
        asyncio.run(demonstrate_deep_inspection())
    else:
        run_quick_health_check()
