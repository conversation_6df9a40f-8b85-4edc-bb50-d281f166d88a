#!/usr/bin/env python3
"""
调试数据格式问题
"""

import requests
import sys

def fetch_and_analyze_data():
    """获取并分析原始数据格式"""
    try:
        print("正在获取原始数据...")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        }
        
        response = requests.get("https://data.17500.cn/3d_asc.txt", headers=headers, timeout=30)
        response.raise_for_status()
        
        lines = response.text.strip().split('\n')
        print(f"获取到 {len(lines)} 行数据")
        
        # 分析最新的几行数据
        print("\n=== 最新5行数据分析 ===")
        for i, line in enumerate(lines[-5:], 1):
            parts = line.split()
            print(f"\n第{i}行 (总第{len(lines)-5+i}行):")
            print(f"原始数据: {line}")
            print(f"字段数量: {len(parts)}")
            
            if len(parts) >= 13:
                print("字段解析:")
                print(f"  [0] 期号: {parts[0]}")
                print(f"  [1] 日期: {parts[1]}")
                print(f"  [2-4] 开奖号码: {parts[2]} {parts[3]} {parts[4]} -> {parts[2]+parts[3]+parts[4]}")
                print(f"  [5-7] 试机号码: {parts[5]} {parts[6]} {parts[7]} -> {parts[5]+parts[6]+parts[7]}")
                print(f"  [8] 开奖机器: {parts[8]}")
                print(f"  [9] 试机机器: {parts[9]}")
                print(f"  [10] 销售额: {parts[10]}")
                print(f"  [11] 直选奖金: {parts[11]}")
                print(f"  [12] 组三奖金: {parts[12]}")
                if len(parts) > 13:
                    print(f"  [13] 组六奖金: {parts[13]}")
                if len(parts) > 14:
                    print(f"  [14+] 其他字段: {parts[14:]}")
        
        # 分析历史数据中的奖金字段
        print("\n=== 历史数据奖金字段分析 ===")
        direct_prizes = set()
        group3_prizes = set()
        group6_prizes = set()
        
        for line in lines[-100:]:  # 分析最近100条记录
            parts = line.split()
            if len(parts) >= 14:
                try:
                    direct_prize = int(parts[11]) if parts[11].isdigit() else None
                    group3_prize = int(parts[12]) if parts[12].isdigit() else None
                    group6_prize = int(parts[13]) if parts[13].isdigit() else None
                    
                    if direct_prize is not None:
                        direct_prizes.add(direct_prize)
                    if group3_prize is not None:
                        group3_prizes.add(group3_prize)
                    if group6_prize is not None:
                        group6_prizes.add(group6_prize)
                except:
                    pass
        
        print(f"直选奖金值: {sorted(direct_prizes)}")
        print(f"组三奖金值: {sorted(group3_prizes)}")
        print(f"组六奖金值: {sorted(group6_prizes)}")
        
        # 检查是否有0值
        if 0 in direct_prizes or 0 in group3_prizes or 0 in group6_prizes:
            print("\n⚠️ 发现奖金字段中有0值，这可能表明数据源中确实存在0值")
        else:
            print("\n✅ 历史数据中奖金字段没有0值")
            
        return True
        
    except Exception as e:
        print(f"❌ 获取数据失败: {e}")
        return False

def analyze_specific_records():
    """分析特定记录"""
    print("\n=== 分析特定记录 ===")
    
    # 模拟一些测试数据来理解格式
    test_lines = [
        "2025186 2025-07-15 2 2 7 1 3 7 1 1 113957536 0 1040 0 346 0 173",
        "2025185 2025-07-14 1 6 4 0 5 5 1 1 109890010 0 1040 0 346 0 173"
    ]
    
    for line in test_lines:
        parts = line.split()
        print(f"\n测试数据: {line}")
        print(f"字段数量: {len(parts)}")
        
        if len(parts) >= 16:
            print("可能的字段映射:")
            print(f"  期号: {parts[0]}")
            print(f"  日期: {parts[1]}")
            print(f"  开奖号码: {parts[2]+parts[3]+parts[4]}")
            print(f"  试机号码: {parts[5]+parts[6]+parts[7]}")
            print(f"  开奖机器: {parts[8]}")
            print(f"  试机机器: {parts[9]}")
            print(f"  销售额: {parts[10]}")
            print(f"  字段11: {parts[11]} (可能是空字段)")
            print(f"  直选奖金: {parts[12]} (应该是1040)")
            print(f"  字段13: {parts[13]} (可能是空字段)")
            print(f"  组三奖金: {parts[14]} (应该是346)")
            print(f"  字段15: {parts[15]} (可能是空字段)")
            if len(parts) > 16:
                print(f"  组六奖金: {parts[16]} (应该是173)")

if __name__ == "__main__":
    print("开始调试数据格式问题...")
    
    if fetch_and_analyze_data():
        analyze_specific_records()
    
    print("\n调试完成。")
