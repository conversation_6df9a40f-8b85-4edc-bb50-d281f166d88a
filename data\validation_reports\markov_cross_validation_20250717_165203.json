{"model_params": {"transition_window_size": 1000, "probability_window_size": 500, "smoothing_alpha": 1.0}, "validation_params": {"k_folds": 3, "data_limit": 600}, "fold_results": [{"fold_idx": 0, "train_size": 200, "val_size": 200, "predictions_count": 200, "accuracy_metrics": {"exact_match": 0.0, "digit_accuracy": 0.20666666666666667, "position_accuracy": [0.12, 0.1, 0.045], "total_predictions": 200}, "diversity_metrics": {"simpson_diversity": 0.9914, "unique_ratio": 0.72, "entropy": 7.027435127199017, "unique_count": 144, "total_count": 200}, "stability_metrics": {"variance": 84720.87640000001, "std_dev": 291.0685080870138, "coefficient_of_variation": 0.554479574974309, "mean": 524.94}, "aic_bic": {"aic": 9430.340371976183, "bic": 9793.155282296466, "log_likelihood": -4605.170185988091, "num_params": 110, "num_samples": 200}}, {"fold_idx": 1, "train_size": 400, "val_size": 200, "predictions_count": 200, "accuracy_metrics": {"exact_match": 0.0, "digit_accuracy": 0.25833333333333336, "position_accuracy": [0.12, 0.135, 0.105], "total_predictions": 200}, "diversity_metrics": {"simpson_diversity": 0.992, "unique_ratio": 0.755, "entropy": 7.112337377155746, "unique_count": 151, "total_count": 200}, "stability_metrics": {"variance": 90929.3504, "std_dev": 301.5449392710811, "coefficient_of_variation": 0.5834170554329626, "mean": 516.86}, "aic_bic": {"aic": 9430.340371976183, "bic": 9793.155282296466, "log_likelihood": -4605.170185988091, "num_params": 110, "num_samples": 200}}], "overall_results": {"accuracy_metrics": {"exact_match": 0.0, "digit_accuracy": 0.2325, "position_accuracy": [0.12, 0.1175, 0.075], "total_predictions": 400}, "diversity_metrics": {"simpson_diversity": 0.994225, "unique_ratio": 0.57, "entropy": 7.633908589894145, "unique_count": 228, "total_count": 400}, "stability_metrics": {"variance": 87841.435, "std_dev": 296.3805577294165, "coefficient_of_variation": 0.5689778416767451, "mean": 520.9}, "aic_bic": {"aic": 18640.680743952365, "bic": 19079.741844134245, "log_likelihood": -9210.340371976183, "num_params": 110, "num_samples": 400}, "total_predictions": 400}, "metadata": {"generated_at": "2025-07-17T16:52:03.213188", "validator_version": "1.0", "database_path": "data\\lottery.db"}}