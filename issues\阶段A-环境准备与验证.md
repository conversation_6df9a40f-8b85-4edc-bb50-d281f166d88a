# 阶段A：环境准备与验证

## 📋 任务概述

**任务ID**: av7djyRR455EUfUTN4iRWD  
**阶段**: A  
**优先级**: 高  
**状态**: 未开始  
**预计工期**: 30分钟  
**前置任务**: 无  
**后续任务**: 阶段B - 数据库结构升级  

## 🎯 任务目标

为AI智能Bug检测系统升级做好环境准备，确保系统状态稳定，数据安全备份，为后续升级操作创造安全的执行环境。

## 📊 子任务详情

### A1：系统环境检查

**任务ID**: 7Y9DcpzKHExdHpBe8GGr53  
**预计时间**: 5分钟  

#### 🔧 执行步骤

1. **检查Python版本**
   ```bash
   python --version
   # 要求：Python 3.9+
   ```

2. **检查当前运行进程**
   ```bash
   # Windows
   tasklist | findstr python
   # 或使用工具
   python -c "import psutil; [print(p.info) for p in psutil.process_iter(['pid', 'name', 'cmdline']) if 'python' in p.info['name']]"
   ```

3. **检查磁盘空间**
   ```bash
   # 要求：至少5GB可用空间（用于AI模型）
   python -c "import shutil; print(f'可用空间: {shutil.disk_usage(\".\")[2]/1024**3:.1f}GB')"
   ```

4. **检查网络连接**
   ```bash
   # 测试PyPI连接
   python -c "import urllib.request; urllib.request.urlopen('https://pypi.org', timeout=10); print('网络连接正常')"
   ```

#### 📁 涉及文件
- 无特定文件
- 系统环境检查

#### ✅ 成功标准
- Python版本 ≥ 3.9
- 可用磁盘空间 ≥ 5GB
- 网络连接正常
- 无阻塞进程

---

### A2：停止当前服务

**任务ID**: bedHBzCe12Mcs7VUe2QUoT  
**预计时间**: 5分钟  

#### 🔧 执行步骤

1. **识别运行中的服务**
   ```bash
   # 查找API服务进程
   netstat -ano | findstr :8888
   # 查找Streamlit进程
   netstat -ano | findstr :8501
   ```

2. **优雅停止服务**
   ```bash
   # 方法1：使用Ctrl+C停止
   # 方法2：使用taskkill
   taskkill /f /im python.exe
   ```

3. **验证服务已停止**
   ```bash
   # 确认端口已释放
   netstat -ano | findstr :8888
   netstat -ano | findstr :8501
   ```

#### 📁 涉及文件
- `start_production_api.py`
- `src/ui/main.py`

#### ✅ 成功标准
- API服务（端口8888）已停止
- Streamlit服务（端口8501）已停止
- 无相关Python进程运行
- 端口已释放

---

### A3：数据库备份

**任务ID**: uuV26im8Jkqy9DTqMNWnsi  
**预计时间**: 10分钟  

#### 🔧 执行步骤

1. **定位数据库文件**
   ```python
   import os
   db_paths = [
       "data/bug_detection.db",
       "bug_detection.db", 
       "lottery_data.db"
   ]
   for path in db_paths:
       if os.path.exists(path):
           print(f"找到数据库: {path}")
   ```

2. **创建备份**
   ```python
   import shutil
   from datetime import datetime
   
   timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
   backup_path = f"{db_path}.backup_{timestamp}"
   shutil.copy2(db_path, backup_path)
   ```

3. **验证备份完整性**
   ```python
   import sqlite3
   # 测试备份文件可读性
   conn = sqlite3.connect(backup_path)
   cursor = conn.cursor()
   cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
   table_count = cursor.fetchone()[0]
   print(f"备份包含 {table_count} 个表")
   conn.close()
   ```

#### 📁 涉及文件
- `data/bug_detection.db` (主数据库)
- `lottery_data.db` (福彩数据库)
- 备份文件：`*.backup_YYYYMMDD_HHMMSS`

#### ✅ 成功标准
- 数据库文件成功备份
- 备份文件完整性验证通过
- 备份文件大小合理
- 备份路径记录清楚

---

### A4：代码备份与版本控制

**任务ID**: ooznjvv9k36XovT4ijA3pT  
**预计时间**: 10分钟  

#### 🔧 执行步骤

1. **检查Git状态**
   ```bash
   git status
   git log --oneline -5
   ```

2. **提交当前更改**
   ```bash
   git add .
   git commit -m "feat: 准备AI智能Bug检测系统升级 - 升级前备份"
   ```

3. **创建升级分支**
   ```bash
   git checkout -b feature/ai-bug-detection-upgrade
   git push -u origin feature/ai-bug-detection-upgrade
   ```

4. **标记升级点**
   ```bash
   git tag -a v1.0-pre-ai-upgrade -m "AI升级前版本"
   git push origin v1.0-pre-ai-upgrade
   ```

#### 📁 涉及文件
- 所有项目文件
- `.git/` 版本控制目录
- 新分支：`feature/ai-bug-detection-upgrade`

#### ✅ 成功标准
- 所有更改已提交
- 升级分支已创建
- 版本标签已打上
- 远程仓库已同步

## 🔄 依赖关系

```mermaid
graph TD
    A1[A1: 系统环境检查] --> A2[A2: 停止当前服务]
    A2 --> A3[A3: 数据库备份]
    A2 --> A4[A4: 代码备份与版本控制]
    A3 --> B[阶段B: 数据库结构升级]
    A4 --> B
```

## ⚠️ 风险点与缓解措施

### 🚨 高风险

1. **数据库备份失败**
   - **风险**: 升级过程中数据丢失
   - **缓解**: 多重备份，验证备份完整性
   - **回滚**: 从备份恢复

2. **服务停止失败**
   - **风险**: 数据不一致，端口占用
   - **缓解**: 强制停止进程，验证端口释放
   - **回滚**: 重启服务

### ⚠️ 中风险

1. **磁盘空间不足**
   - **风险**: 备份失败，模型下载失败
   - **缓解**: 清理临时文件，扩展存储
   - **回滚**: 删除不必要文件

2. **网络连接问题**
   - **风险**: 依赖库下载失败
   - **缓解**: 使用镜像源，离线安装包
   - **回滚**: 稍后重试

## 📋 验证清单

### ✅ 阶段A完成标准

- [ ] **A1**: Python版本≥3.9，磁盘空间≥5GB，网络连接正常
- [ ] **A2**: API和Streamlit服务已停止，端口已释放
- [ ] **A3**: 数据库备份完成，完整性验证通过
- [ ] **A4**: 代码已提交，升级分支已创建

### 🔍 质量检查

- [ ] 备份文件存在且可读
- [ ] 服务进程完全停止
- [ ] Git仓库状态干净
- [ ] 系统资源充足

## 📝 执行日志模板

```
=== 阶段A执行日志 ===
开始时间: YYYY-MM-DD HH:MM:SS
执行人员: [姓名]

A1 系统环境检查:
- Python版本: [版本号]
- 磁盘空间: [可用空间]GB
- 网络状态: [正常/异常]
- 状态: [完成/失败]

A2 停止当前服务:
- API服务: [已停止/运行中]
- Streamlit服务: [已停止/运行中]
- 端口状态: [已释放/占用中]
- 状态: [完成/失败]

A3 数据库备份:
- 原始文件: [路径]
- 备份文件: [路径]
- 文件大小: [大小]MB
- 完整性: [验证通过/失败]
- 状态: [完成/失败]

A4 代码备份与版本控制:
- Git状态: [干净/有未提交更改]
- 升级分支: [已创建/失败]
- 版本标签: [已创建/失败]
- 状态: [完成/失败]

结束时间: YYYY-MM-DD HH:MM:SS
总耗时: [分钟]
整体状态: [成功/失败]
备注: [其他说明]
```

---

**创建时间**: 2025-07-25  
**最后更新**: 2025-07-25  
**版本**: v1.0
