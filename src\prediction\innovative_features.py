"""
创新特征集成模块
整合试机号码关联、销售额影响因子、机器设备偏好等创新特征
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
import os
import sys

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from trial_number_analysis import TrialNumberAnalyzer
from sales_impact_analysis import SalesImpactAnalyzer
from machine_preference_analysis import MachinePreferenceAnalyzer

class InnovativeFeatureExtractor:
    """创新特征提取器"""
    
    def __init__(self, db_path: str = None):
        """
        初始化创新特征提取器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path or os.path.join('data', 'lottery.db')
        
        # 初始化各个分析器
        self.trial_analyzer = TrialNumberAnalyzer(self.db_path)
        self.sales_analyzer = SalesImpactAnalyzer(self.db_path)
        self.machine_analyzer = MachinePreferenceAnalyzer(self.db_path)
        
        # 存储训练好的模型
        self.models_trained = False
        
    def train_all_models(self) -> Dict[str, Any]:
        """
        训练所有创新特征模型
        
        Returns:
            训练结果汇总
        """
        print("开始训练所有创新特征模型...")
        
        results = {}
        
        try:
            # 训练试机号码分析模型
            print("\n1. 训练试机号码关联分析模型...")
            trial_result = self.trial_analyzer.train_model()
            results['trial_analysis'] = trial_result
            print("✓ 试机号码分析模型训练完成")
            
        except Exception as e:
            print(f"✗ 试机号码分析模型训练失败: {e}")
            results['trial_analysis'] = {'success': False, 'error': str(e)}
        
        try:
            # 训练销售额影响因子模型
            print("\n2. 训练销售额影响因子模型...")
            sales_result = self.sales_analyzer.train_model()
            results['sales_analysis'] = sales_result
            print("✓ 销售额影响因子模型训练完成")
            
        except Exception as e:
            print(f"✗ 销售额影响因子模型训练失败: {e}")
            results['sales_analysis'] = {'success': False, 'error': str(e)}
        
        try:
            # 训练机器设备偏好模型
            print("\n3. 训练机器设备偏好识别模型...")
            machine_result = self.machine_analyzer.train_model()
            results['machine_analysis'] = machine_result
            print("✓ 机器设备偏好模型训练完成")
            
        except Exception as e:
            print(f"✗ 机器设备偏好模型训练失败: {e}")
            results['machine_analysis'] = {'success': False, 'error': str(e)}
        
        # 检查训练状态
        successful_models = sum(1 for result in results.values() if result.get('success', False))
        total_models = len(results)
        
        self.models_trained = successful_models > 0
        
        print(f"\n创新特征模型训练完成: {successful_models}/{total_models} 个模型成功")
        
        return {
            'success': self.models_trained,
            'successful_models': successful_models,
            'total_models': total_models,
            'results': results
        }
    
    def extract_innovative_features(self, data: List[str], 
                                  context_data: Dict[str, Any] = None) -> Dict[str, float]:
        """
        提取创新特征
        
        Args:
            data: 历史号码数据列表
            context_data: 上下文数据（试机号码、销售额、机器信息等）
            
        Returns:
            创新特征字典
        """
        if not self.models_trained:
            raise ValueError("模型未训练，请先调用train_all_models方法")
        
        features = {}
        context_data = context_data or {}
        
        # 1. 试机号码关联特征
        trial_features = self._extract_trial_features(data, context_data)
        features.update(trial_features)
        
        # 2. 销售额影响特征
        sales_features = self._extract_sales_features(data, context_data)
        features.update(sales_features)
        
        # 3. 机器设备偏好特征
        machine_features = self._extract_machine_features(data, context_data)
        features.update(machine_features)
        
        # 4. 综合创新特征
        combined_features = self._extract_combined_features(features, data)
        features.update(combined_features)
        
        return features
    
    def _extract_trial_features(self, data: List[str], 
                               context_data: Dict[str, Any]) -> Dict[str, float]:
        """
        提取试机号码关联特征
        
        Args:
            data: 历史号码数据
            context_data: 上下文数据
            
        Returns:
            试机号码特征字典
        """
        features = {}
        
        try:
            if hasattr(self.trial_analyzer, 'analysis_results'):
                analysis = self.trial_analyzer.analysis_results
                
                # 位置差值特征
                if 'position_analysis' in analysis:
                    pos_analysis = analysis['position_analysis']
                    for pos in ['hundreds', 'tens', 'units']:
                        if pos in pos_analysis:
                            features[f'trial_{pos}_avg_diff'] = pos_analysis[pos].get('avg_min_diff', 0)
                            features[f'trial_{pos}_std_diff'] = pos_analysis[pos].get('std_min_diff', 0)
                
                # 预热效应特征
                if 'preheating_analysis' in analysis:
                    preheat = analysis['preheating_analysis']
                    for period in ['immediate', 'next_1', 'next_3']:
                        if period in preheat:
                            features[f'trial_preheat_{period}_prob'] = preheat[period].get('hit_probability', 0)
                            features[f'trial_preheat_{period}_avg'] = preheat[period].get('avg_hits', 0)
                
                # 机器关联特征
                if 'machine_analysis' in analysis:
                    machine_analysis = analysis['machine_analysis']
                    features['trial_machine_combinations'] = machine_analysis.get('total_combinations', 0)
                    
                    # 最佳机器配对准确率
                    if 'machine_performance' in machine_analysis:
                        performances = machine_analysis['machine_performance']
                        if performances:
                            avg_similarities = [p.get('avg_similarity', 0) for p in performances.values()]
                            features['trial_best_machine_accuracy'] = max(avg_similarities) if avg_similarities else 0
                            features['trial_avg_machine_accuracy'] = np.mean(avg_similarities) if avg_similarities else 0
            
            # 当前试机号码特征（如果提供）
            if 'trial_numbers' in context_data:
                trial_numbers = context_data['trial_numbers']
                if len(trial_numbers) == 3:
                    trial_digits = [int(d) for d in trial_numbers]
                    features['current_trial_sum'] = sum(trial_digits)
                    features['current_trial_span'] = max(trial_digits) - min(trial_digits)
                    features['current_trial_unique'] = len(set(trial_digits))
                    features['current_trial_odd_count'] = sum(1 for d in trial_digits if d % 2 == 1)
                    features['current_trial_big_count'] = sum(1 for d in trial_digits if d >= 5)
        
        except Exception as e:
            print(f"提取试机号码特征时出错: {e}")
            # 返回默认特征
            for i in range(10):
                features[f'trial_feature_{i}'] = 0.0
        
        return features
    
    def _extract_sales_features(self, data: List[str], 
                               context_data: Dict[str, Any]) -> Dict[str, float]:
        """
        提取销售额影响特征
        
        Args:
            data: 历史号码数据
            context_data: 上下文数据
            
        Returns:
            销售额特征字典
        """
        features = {}
        
        try:
            if hasattr(self.sales_analyzer, 'analysis_results'):
                analysis = self.sales_analyzer.analysis_results
                
                # 销售额分布特征
                if 'distribution_analysis' in analysis:
                    dist = analysis['distribution_analysis']
                    if 'basic_stats' in dist:
                        stats = dist['basic_stats']
                        features['sales_mean'] = stats.get('mean', 0)
                        features['sales_std'] = stats.get('std', 0)
                        features['sales_median'] = stats.get('median', 0)
                        features['sales_q25'] = stats.get('q25', 0)
                        features['sales_q75'] = stats.get('q75', 0)
                
                # 销售额与号码关联特征
                if 'correlation_analysis' in analysis:
                    corr = analysis['correlation_analysis']
                    if 'feature_significance' in corr:
                        significance = corr['feature_significance']
                        for feature_name, sig_value in significance.items():
                            features[f'sales_sig_{feature_name}'] = sig_value
                
                # 销售额预测价值特征
                if 'prediction_analysis' in analysis:
                    pred = analysis['prediction_analysis']
                    if 'prediction_accuracy' in pred:
                        accuracy = pred['prediction_accuracy']
                        for acc_name, acc_value in accuracy.items():
                            features[f'sales_{acc_name}'] = acc_value
            
            # 当前销售额特征（如果提供）
            if 'sales_amount' in context_data:
                sales_amount = context_data['sales_amount']
                if sales_amount > 0:
                    # 归一化销售额（基于历史数据）
                    if hasattr(self.sales_analyzer, 'analysis_results'):
                        dist = self.sales_analyzer.analysis_results.get('distribution_analysis', {})
                        stats = dist.get('basic_stats', {})
                        mean_sales = stats.get('mean', sales_amount)
                        std_sales = stats.get('std', 1)
                        
                        if std_sales > 0:
                            features['current_sales_normalized'] = (sales_amount - mean_sales) / std_sales
                        else:
                            features['current_sales_normalized'] = 0
                        
                        # 销售额级别
                        q25 = stats.get('q25', 0)
                        q75 = stats.get('q75', 0)
                        if sales_amount < q25:
                            features['current_sales_level'] = 0  # 低
                        elif sales_amount < q75:
                            features['current_sales_level'] = 1  # 中
                        else:
                            features['current_sales_level'] = 2  # 高
        
        except Exception as e:
            print(f"提取销售额特征时出错: {e}")
            # 返回默认特征
            for i in range(8):
                features[f'sales_feature_{i}'] = 0.0
        
        return features
    
    def _extract_machine_features(self, data: List[str], 
                                 context_data: Dict[str, Any]) -> Dict[str, float]:
        """
        提取机器设备偏好特征
        
        Args:
            data: 历史号码数据
            context_data: 上下文数据
            
        Returns:
            机器特征字典
        """
        features = {}
        
        try:
            if hasattr(self.machine_analyzer, 'analysis_results'):
                analysis = self.machine_analyzer.analysis_results
                
                # 机器偏好特征
                if 'preference_analysis' in analysis:
                    pref = analysis['preference_analysis']
                    
                    # 开奖机器特征
                    if 'draw_machines' in pref:
                        draw_machines = pref['draw_machines']
                        if draw_machines:
                            # 计算机器偏好方差（多样性指标）
                            preference_variances = []
                            for machine_data in draw_machines.values():
                                if 'digit_preferences' in machine_data:
                                    digit_prefs = machine_data['digit_preferences']
                                    if 'preference_variance' in digit_prefs:
                                        preference_variances.append(digit_prefs['preference_variance'])
                            
                            if preference_variances:
                                features['machine_preference_diversity'] = np.mean(preference_variances)
                                features['machine_preference_max_var'] = max(preference_variances)
                
                # 机器组合效应特征
                if 'combination_analysis' in analysis:
                    comb = analysis['combination_analysis']
                    features['machine_total_combinations'] = comb.get('total_combinations', 0)
                    
                    # 最佳组合准确率
                    if 'best_combinations' in comb and comb['best_combinations']:
                        best_accuracy = comb['best_combinations'][0][1].get('avg_accuracy', 0)
                        features['machine_best_combination_accuracy'] = best_accuracy
                    
                    # 组合准确率分布
                    if 'combination_analysis' in comb:
                        accuracies = [data.get('avg_accuracy', 0) for data in comb['combination_analysis'].values()]
                        if accuracies:
                            features['machine_avg_combination_accuracy'] = np.mean(accuracies)
                            features['machine_combination_accuracy_std'] = np.std(accuracies)
                
                # 机器时间模式特征
                if 'temporal_analysis' in analysis:
                    temporal = analysis['temporal_analysis']
                    
                    # 性能趋势特征
                    if 'performance_trends' in temporal:
                        trends = temporal['performance_trends']
                        if trends:
                            trend_correlations = [t.get('accuracy_trend', 0) for t in trends.values()]
                            stabilities = [t.get('performance_stability', 0) for t in trends.values()]
                            
                            if trend_correlations:
                                features['machine_avg_trend'] = np.mean(trend_correlations)
                                features['machine_avg_stability'] = np.mean(stabilities)
            
            # 当前机器特征（如果提供）
            if 'draw_machine' in context_data and 'trial_machine' in context_data:
                draw_machine = context_data['draw_machine']
                trial_machine = context_data['trial_machine']
                
                features['current_draw_machine'] = draw_machine
                features['current_trial_machine'] = trial_machine
                features['current_machine_combination'] = draw_machine * 10 + trial_machine
                
                # 机器使用频率特征（基于历史数据）
                if hasattr(self.machine_analyzer, 'analysis_results'):
                    machine_features_data = self.machine_analyzer.analysis_results.get('machine_features', {})
                    if 'draw_machine_usage_rank' in machine_features_data:
                        # 简化的排名特征
                        features['current_draw_machine_rank'] = draw_machine  # 简化处理
                        features['current_trial_machine_rank'] = trial_machine
        
        except Exception as e:
            print(f"提取机器特征时出错: {e}")
            # 返回默认特征
            for i in range(8):
                features[f'machine_feature_{i}'] = 0.0
        
        return features
    
    def _extract_combined_features(self, features: Dict[str, float], 
                                  data: List[str]) -> Dict[str, float]:
        """
        提取综合创新特征
        
        Args:
            features: 已提取的特征
            data: 历史号码数据
            
        Returns:
            综合特征字典
        """
        combined = {}
        
        try:
            # 特征交互项
            trial_sum = features.get('current_trial_sum', 0)
            sales_level = features.get('current_sales_level', 1)
            machine_combo = features.get('current_machine_combination', 11)
            
            # 试机号码与销售额交互
            combined['trial_sales_interaction'] = trial_sum * sales_level
            
            # 试机号码与机器交互
            combined['trial_machine_interaction'] = trial_sum * (machine_combo % 10)
            
            # 销售额与机器交互
            combined['sales_machine_interaction'] = sales_level * machine_combo
            
            # 三元交互
            combined['triple_interaction'] = trial_sum * sales_level * (machine_combo % 10)
            
            # 创新特征置信度
            trial_confidence = features.get('trial_best_machine_accuracy', 0)
            sales_confidence = features.get('sales_high_sales_sum_accuracy', 0)
            machine_confidence = features.get('machine_best_combination_accuracy', 0)
            
            combined['innovation_confidence'] = (trial_confidence + sales_confidence + machine_confidence) / 3
            
            # 特征稳定性指标
            feature_values = [v for k, v in features.items() if isinstance(v, (int, float)) and not np.isnan(v)]
            if feature_values:
                combined['feature_stability'] = 1 / (1 + np.std(feature_values))
                combined['feature_complexity'] = len(feature_values)
            else:
                combined['feature_stability'] = 0.5
                combined['feature_complexity'] = 0
        
        except Exception as e:
            print(f"提取综合特征时出错: {e}")
            # 返回默认特征
            for i in range(6):
                combined[f'combined_feature_{i}'] = 0.0
        
        return combined
    
    def get_feature_summary(self) -> Dict[str, Any]:
        """
        获取特征提取器的摘要信息
        
        Returns:
            特征摘要信息
        """
        summary = {
            'models_trained': self.models_trained,
            'available_analyzers': {
                'trial_analyzer': hasattr(self.trial_analyzer, 'analysis_results'),
                'sales_analyzer': hasattr(self.sales_analyzer, 'analysis_results'),
                'machine_analyzer': hasattr(self.machine_analyzer, 'analysis_results')
            }
        }
        
        if self.models_trained:
            # 统计各分析器的数据量
            data_counts = {}
            
            if hasattr(self.trial_analyzer, 'analysis_results'):
                trial_summary = self.trial_analyzer.analysis_results.get('data_summary', {})
                data_counts['trial_records'] = trial_summary.get('total_records', 0)
            
            if hasattr(self.sales_analyzer, 'analysis_results'):
                sales_summary = self.sales_analyzer.analysis_results.get('data_summary', {})
                data_counts['sales_records'] = sales_summary.get('total_records', 0)
            
            if hasattr(self.machine_analyzer, 'analysis_results'):
                machine_summary = self.machine_analyzer.analysis_results.get('data_summary', {})
                data_counts['machine_records'] = machine_summary.get('total_records', 0)
            
            summary['data_counts'] = data_counts
        
        return summary


if __name__ == "__main__":
    # 测试代码
    extractor = InnovativeFeatureExtractor()
    
    try:
        # 训练所有模型
        training_result = extractor.train_all_models()
        print("训练结果:", training_result)
        
        if training_result['success']:
            # 测试特征提取
            test_data = ['123', '456', '789', '012', '345']
            test_context = {
                'trial_numbers': '234',
                'sales_amount': 50000000,
                'draw_machine': 1,
                'trial_machine': 2
            }
            
            features = extractor.extract_innovative_features(test_data, test_context)
            print(f"提取了 {len(features)} 个创新特征")
            
            # 显示部分特征
            feature_items = list(features.items())[:10]
            for name, value in feature_items:
                print(f"  {name}: {value:.4f}")
            
            # 获取摘要
            summary = extractor.get_feature_summary()
            print("特征提取器摘要:", summary)
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
