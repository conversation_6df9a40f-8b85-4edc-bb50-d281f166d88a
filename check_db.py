import os
import sqlite3

# 初始化数据库
db_path = "data/bug_detection.db"
os.makedirs("data", exist_ok=True)

print("初始化Bug检测数据库...")

schema_sql = """
CREATE TABLE IF NOT EXISTS bug_reports (
    id TEXT PRIMARY KEY,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    error_type TEXT NOT NULL,
    severity TEXT DEFAULT 'medium',
    page_name TEXT,
    error_message TEXT,
    stack_trace TEXT,
    status TEXT DEFAULT 'open',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS realtime_events (
    id TEXT PRIMARY KEY,
    event_type TEXT NOT NULL,
    priority INTEGER NOT NULL,
    source TEXT NOT NULL,
    timestamp REAL NOT NULL,
    data TEXT,
    tags TEXT,
    correlation_id TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS performance_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    endpoint TEXT,
    response_time REAL,
    status_code INTEGER,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
);
"""

try:
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    cursor.executescript(schema_sql)
    conn.commit()

    # 检查创建的表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    print(f"创建的表: {[table[0] for table in tables]}")

    # 检查每个表的数据
    for table_name in ['bug_reports', 'realtime_events', 'performance_metrics']:
        if table_name in [table[0] for table in tables]:
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"{table_name} 表记录数: {count}")

            if count > 0:
                cursor.execute(f"SELECT * FROM {table_name} ORDER BY id DESC LIMIT 3")
                rows = cursor.fetchall()
                print(f"最近3条记录:")
                for row in rows:
                    print(f"  {row}")

    conn.close()
    print("✅ 数据库检查完成")

except Exception as e:
    print(f"❌ 检查失败: {e}")
    import traceback
    traceback.print_exc()
