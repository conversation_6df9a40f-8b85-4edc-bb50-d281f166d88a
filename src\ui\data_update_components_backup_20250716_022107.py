"""
实时数据更新组件
提供自动数据更新、手动刷新、增量更新等功能

修复历史：
- 2025-07-16: 修复数据不一致问题
  * 问题：数据库约束违反错误 "NOT NULL constraint failed: lottery_records.sum_value"
  * 原因：表结构不完整，缺少计算字段
  * 解决：统一表结构，添加数据验证和计算逻辑
  * 结果：数据库记录数从8341成功增加到8343
"""

import asyncio
import os
import sqlite3
import threading
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import pandas as pd
import requests
import streamlit as st


class DataUpdateManager:
    """数据更新管理器"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or os.path.join('data', 'lottery.db')
        self.data_source_url = "https://data.17500.cn/3d_asc.txt"
        self.last_update_time = None
        self.update_status = "未更新"
        
    def check_data_freshness(self) -> Dict[str, Any]:
        """检查数据新鲜度"""
        try:
            if not os.path.exists(self.db_path):
                return {
                    'status': 'no_database',
                    'message': '数据库文件不存在',
                    'last_update': None,
                    'record_count': 0,
                    'freshness': 'unknown'
                }
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取最新记录
            cursor.execute("""
                SELECT MAX(date), COUNT(*) 
                FROM lottery_records 
                WHERE numbers IS NOT NULL AND numbers != ''
            """)
            
            result = cursor.fetchone()
            latest_date = result[0] if result[0] else None
            record_count = result[1] if result[1] else 0
            
            conn.close()
            
            # 计算数据新鲜度
            if latest_date:
                latest_datetime = datetime.strptime(latest_date, '%Y-%m-%d')
                days_old = (datetime.now() - latest_datetime).days
                
                if days_old == 0:
                    freshness = 'very_fresh'
                elif days_old <= 1:
                    freshness = 'fresh'
                elif days_old <= 3:
                    freshness = 'moderate'
                elif days_old <= 7:
                    freshness = 'stale'
                else:
                    freshness = 'very_stale'
            else:
                freshness = 'no_data'
            
            return {
                'status': 'success',
                'message': '数据检查完成',
                'last_update': latest_date,
                'record_count': record_count,
                'freshness': freshness,
                'days_old': days_old if latest_date else None
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'数据检查失败: {str(e)}',
                'last_update': None,
                'record_count': 0,
                'freshness': 'error'
            }
    
    def fetch_latest_data(self) -> Dict[str, Any]:
        """获取最新数据"""
        try:
            st.info("正在从数据源获取最新数据...")
            
            # 发送请求获取数据，添加更完整的headers避免被限制
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Referer': 'https://data.17500.cn/'
            }

            # 添加延迟避免请求过于频繁
            import time
            time.sleep(2)

            response = requests.get(self.data_source_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            # 解析数据
            lines = response.text.strip().split('\n')
            
            if not lines:
                return {
                    'status': 'error',
                    'message': '数据源返回空数据',
                    'new_records': 0
                }
            
            # 解析每行数据
            new_records = []
            for line in lines:
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 13:  # 确保有足够的字段
                        # 组合开奖号码和试机号码（原始数据是分开的数字）
                        numbers = parts[2] + parts[3] + parts[4]  # 百位+十位+个位
                        trial_numbers = parts[5] + parts[6] + parts[7]  # 试机号百位+十位+个位

                        record = {
                            'period': parts[0],
                            'date': parts[1],
                            'numbers': numbers,
                            'trial_numbers': trial_numbers,
                            'draw_machine': int(parts[8]) if parts[8].isdigit() else 1,
                            'trial_machine': int(parts[9]) if parts[9].isdigit() else 1,
                            'sales_amount': int(parts[10]) if parts[10].isdigit() else 0,
                            'direct_prize': int(parts[11]) if parts[11].isdigit() else 1040,
                            'group3_prize': int(parts[12]) if parts[12].isdigit() else 346,
                            'group6_prize': int(parts[13]) if len(parts) > 13 and parts[13].isdigit() else 173
                        }
                        new_records.append(record)
            
            return {
                'status': 'success',
                'message': f'成功获取 {len(new_records)} 条记录',
                'new_records': len(new_records),
                'data': new_records
            }
            
        except requests.RequestException as e:
            error_msg = str(e)
            if '429' in error_msg or 'Too Many Requests' in error_msg:
                return {
                    'status': 'error',
                    'message': '数据源请求过于频繁，请稍后再试。建议等待1-2分钟后重新尝试。',
                    'new_records': 0
                }
            elif '403' in error_msg or 'Forbidden' in error_msg:
                return {
                    'status': 'error',
                    'message': '数据源访问被拒绝，可能需要更换数据源或稍后重试。',
                    'new_records': 0
                }
            else:
                return {
                    'status': 'error',
                    'message': f'网络请求失败: {error_msg}',
                    'new_records': 0
                }
        except Exception as e:
            return {
                'status': 'error',
                'message': f'数据获取失败: {str(e)}',
                'new_records': 0
            }
    
    def validate_record(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证单条记录的数据完整性

        修复说明：
        - 添加了完整的字段验证逻辑
        - 检查必需字段的存在性和格式
        - 验证数值字段的合理性
        - 返回详细的验证结果和错误信息

        Args:
            record: 待验证的记录字典

        Returns:
            包含验证结果的字典：
            - is_valid: 是否通过验证
            - errors: 错误信息列表
            - record: 原始记录
        """
        errors = []

        # 检查必需字段
        required_fields = ['period', 'date', 'numbers', 'trial_numbers',
                          'draw_machine', 'trial_machine', 'sales_amount',
                          'direct_prize', 'group3_prize', 'group6_prize']

        for field in required_fields:
            if field not in record or record[field] is None:
                errors.append(f"缺少必需字段: {field}")

        # 验证期号格式
        period = record.get('period', '')
        if not isinstance(period, str) or len(period) != 7 or not period.isdigit():
            errors.append(f"期号格式错误: {period}")

        # 验证号码格式
        numbers = record.get('numbers', '')
        if not isinstance(numbers, str) or len(numbers) != 3 or not numbers.isdigit():
            errors.append(f"开奖号码格式错误: {numbers}")

        trial_numbers = record.get('trial_numbers', '')
        if not isinstance(trial_numbers, str) or len(trial_numbers) != 3 or not trial_numbers.isdigit():
            errors.append(f"试机号码格式错误: {trial_numbers}")

        # 验证数值字段
        numeric_fields = ['draw_machine', 'trial_machine', 'sales_amount',
                         'direct_prize', 'group3_prize', 'group6_prize']

        for field in numeric_fields:
            value = record.get(field)
            if value is not None:
                try:
                    int_value = int(value)
                    if int_value < 0:
                        errors.append(f"{field}不能为负数: {int_value}")
                except (ValueError, TypeError):
                    errors.append(f"{field}必须是数字: {value}")

        return {
            'is_valid': len(errors) == 0,
            'errors': errors,
            'record': record
        }

    def calculate_derived_fields(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """
        计算衍生字段（和值、跨度等）

        修复说明：
        - 新增方法，解决数据库约束违反问题
        - 自动计算sum_value、trial_sum_value、span_value、trial_span_value
        - 为unknown_field字段提供默认值
        - 包含错误处理，确保计算失败时有默认值

        Args:
            record: 原始记录字典

        Returns:
            包含计算字段的增强记录字典
        """
        try:
            # 计算正式开奖号码的和值和跨度
            numbers = record.get('numbers', '000')
            if len(numbers) == 3 and numbers.isdigit():
                number_list = [int(d) for d in numbers]
                sum_value = sum(number_list)
                span_value = max(number_list) - min(number_list)
            else:
                sum_value = 0
                span_value = 0

            # 计算试机号码的和值和跨度
            trial_numbers = record.get('trial_numbers', '000')
            if len(trial_numbers) == 3 and trial_numbers.isdigit():
                trial_number_list = [int(d) for d in trial_numbers]
                trial_sum_value = sum(trial_number_list)
                trial_span_value = max(trial_number_list) - min(trial_number_list)
            else:
                trial_sum_value = 0
                trial_span_value = 0

            # 返回包含计算字段的记录
            enhanced_record = record.copy()
            enhanced_record.update({
                'sum_value': sum_value,
                'trial_sum_value': trial_sum_value,
                'span_value': span_value,
                'trial_span_value': trial_span_value,
                'unknown_field1': record.get('unknown_field1', 0),
                'unknown_field2': record.get('unknown_field2', 0),
                'unknown_field3': record.get('unknown_field3', 0)
            })

            return enhanced_record

        except Exception as e:
            # 如果计算失败，返回默认值
            enhanced_record = record.copy()
            enhanced_record.update({
                'sum_value': 0,
                'trial_sum_value': 0,
                'span_value': 0,
                'trial_span_value': 0,
                'unknown_field1': 0,
                'unknown_field2': 0,
                'unknown_field3': 0
            })
            return enhanced_record

    def update_database(self, new_data: List[Dict[str, Any]],
                       update_mode: str = 'incremental') -> Dict[str, Any]:
        """更新数据库"""
        try:
            if not new_data:
                return {
                    'status': 'error',
                    'message': '没有新数据需要更新',
                    'updated_records': 0
                }

            # 数据验证
            valid_records = []
            validation_errors = []

            for i, record in enumerate(new_data):
                validation_result = self.validate_record(record)
                if validation_result['is_valid']:
                    valid_records.append(record)
                else:
                    validation_errors.extend([f"记录{i+1}: {error}" for error in validation_result['errors']])

            if not valid_records:
                return {
                    'status': 'error',
                    'message': f'所有记录验证失败: {"; ".join(validation_errors[:5])}',
                    'updated_records': 0,
                    'validation_errors': validation_errors
                }

            if validation_errors:
                print(f"警告: {len(validation_errors)}个验证错误，继续处理有效记录")

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建表（如果不存在）- 使用与core/database.py一致的完整表结构
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS lottery_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    period TEXT UNIQUE NOT NULL,
                    date DATE NOT NULL,
                    numbers TEXT NOT NULL,
                    trial_numbers TEXT NOT NULL,
                    draw_machine INTEGER NOT NULL,
                    trial_machine INTEGER NOT NULL,
                    sales_amount INTEGER NOT NULL,
                    direct_prize INTEGER NOT NULL,
                    group3_prize INTEGER NOT NULL,
                    group6_prize INTEGER NOT NULL,
                    unknown_field1 INTEGER DEFAULT 0,
                    unknown_field2 INTEGER DEFAULT 0,
                    unknown_field3 INTEGER DEFAULT 0,
                    sum_value INTEGER NOT NULL,
                    trial_sum_value INTEGER NOT NULL,
                    span_value INTEGER NOT NULL,
                    trial_span_value INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            updated_count = 0
            
            if update_mode == 'full_replace':
                # 全量替换模式
                cursor.execute('DELETE FROM lottery_records')

                for record in valid_records:
                    # 计算衍生字段
                    enhanced_record = self.calculate_derived_fields(record)

                    cursor.execute('''
                        INSERT INTO lottery_records
                        (period, date, numbers, trial_numbers, draw_machine, trial_machine,
                         sales_amount, direct_prize, group3_prize, group6_prize,
                         unknown_field1, unknown_field2, unknown_field3,
                         sum_value, trial_sum_value, span_value, trial_span_value)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        enhanced_record['period'], enhanced_record['date'], enhanced_record['numbers'],
                        enhanced_record['trial_numbers'], enhanced_record['draw_machine'], enhanced_record['trial_machine'],
                        enhanced_record['sales_amount'], enhanced_record['direct_prize'],
                        enhanced_record['group3_prize'], enhanced_record['group6_prize'],
                        enhanced_record['unknown_field1'], enhanced_record['unknown_field2'], enhanced_record['unknown_field3'],
                        enhanced_record['sum_value'], enhanced_record['trial_sum_value'],
                        enhanced_record['span_value'], enhanced_record['trial_span_value']
                    ))
                    updated_count += 1
            
            else:
                # 增量更新模式
                for record in valid_records:
                    # 计算衍生字段
                    enhanced_record = self.calculate_derived_fields(record)

                    cursor.execute('''
                        INSERT OR REPLACE INTO lottery_records
                        (period, date, numbers, trial_numbers, draw_machine, trial_machine,
                         sales_amount, direct_prize, group3_prize, group6_prize,
                         unknown_field1, unknown_field2, unknown_field3,
                         sum_value, trial_sum_value, span_value, trial_span_value, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                    ''', (
                        enhanced_record['period'], enhanced_record['date'], enhanced_record['numbers'],
                        enhanced_record['trial_numbers'], enhanced_record['draw_machine'], enhanced_record['trial_machine'],
                        enhanced_record['sales_amount'], enhanced_record['direct_prize'],
                        enhanced_record['group3_prize'], enhanced_record['group6_prize'],
                        enhanced_record['unknown_field1'], enhanced_record['unknown_field2'], enhanced_record['unknown_field3'],
                        enhanced_record['sum_value'], enhanced_record['trial_sum_value'],
                        enhanced_record['span_value'], enhanced_record['trial_span_value']
                    ))
                    updated_count += 1
            
            conn.commit()
            conn.close()
            
            self.last_update_time = datetime.now()
            self.update_status = "更新成功"
            
            return {
                'status': 'success',
                'message': f'成功更新 {updated_count} 条记录',
                'updated_records': updated_count,
                'update_time': self.last_update_time.strftime('%Y-%m-%d %H:%M:%S'),
                'validation_warnings': len(validation_errors) if validation_errors else 0
            }

        except sqlite3.IntegrityError as e:
            error_msg = str(e)
            if 'NOT NULL constraint failed' in error_msg:
                field_name = error_msg.split('.')[-1] if '.' in error_msg else '未知字段'
                return {
                    'status': 'error',
                    'message': f'数据完整性错误: {field_name} 字段不能为空',
                    'updated_records': 0,
                    'error_type': 'constraint_violation',
                    'technical_details': error_msg
                }
            elif 'UNIQUE constraint failed' in error_msg:
                return {
                    'status': 'error',
                    'message': '数据重复错误: 期号已存在',
                    'updated_records': 0,
                    'error_type': 'duplicate_key',
                    'technical_details': error_msg
                }
            else:
                return {
                    'status': 'error',
                    'message': f'数据库约束错误: {error_msg}',
                    'updated_records': 0,
                    'error_type': 'integrity_error',
                    'technical_details': error_msg
                }
        except sqlite3.OperationalError as e:
            return {
                'status': 'error',
                'message': f'数据库操作错误: {str(e)}',
                'updated_records': 0,
                'error_type': 'operational_error',
                'technical_details': str(e)
            }
        except Exception as e:
            return {
                'status': 'error',
                'message': f'未知错误: {str(e)}',
                'updated_records': 0,
                'error_type': 'unknown_error',
                'technical_details': str(e)
            }
        finally:
            # 确保数据库连接关闭
            try:
                if 'conn' in locals():
                    conn.close()
            except:
                pass

def show_data_update_interface():
    """显示数据更新界面"""
    st.markdown("## 🔄 数据更新管理")

    # 初始化数据更新管理器
    if 'data_manager' not in st.session_state:
        st.session_state.data_manager = DataUpdateManager()

    data_manager = st.session_state.data_manager

    # 数据状态检查
    show_data_status(data_manager)

    # 注意：更新操作界面已移至tab2，避免重复调用

def show_data_status(data_manager: DataUpdateManager):
    """显示数据状态"""
    st.markdown("### 📊 数据状态")
    
    # 检查数据新鲜度
    freshness_info = data_manager.check_data_freshness()
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        record_count = freshness_info.get('record_count', 0)
        st.metric("数据记录数", f"{record_count:,}")
    
    with col2:
        last_update = freshness_info.get('last_update', '未知')
        st.metric("最新数据日期", last_update or '无数据')
    
    with col3:
        days_old = freshness_info.get('days_old', 0)
        if days_old is not None:
            st.metric("数据滞后天数", f"{days_old} 天")
        else:
            st.metric("数据滞后天数", "未知")
    
    with col4:
        freshness = freshness_info.get('freshness', 'unknown')
        freshness_labels = {
            'very_fresh': '🟢 非常新鲜',
            'fresh': '🟡 新鲜',
            'moderate': '🟠 一般',
            'stale': '🔴 过时',
            'very_stale': '⚫ 非常过时',
            'no_data': '❌ 无数据',
            'error': '❌ 错误'
        }
        st.metric("数据新鲜度", freshness_labels.get(freshness, '未知'))
    
    # 数据质量指示器
    if freshness in ['very_fresh', 'fresh']:
        st.success("✅ 数据状态良好，可以进行预测分析")
    elif freshness in ['moderate']:
        st.warning("⚠️ 数据稍有滞后，建议更新后再进行分析")
    elif freshness in ['stale', 'very_stale']:
        st.error("❌ 数据过时，强烈建议立即更新")
    else:
        st.error("❌ 数据状态异常，请检查数据库")

def show_update_operations(data_manager: DataUpdateManager):
    """显示更新操作"""
    st.markdown("### 🔄 数据更新操作")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🔄 手动更新", type="primary", key="manual_update_btn", help="立即从数据源获取最新数据"):
            perform_manual_update(data_manager)
    
    with col2:
        if st.button("📥 增量更新", help="只更新新增的数据记录", key="incremental_update_btn"):
            perform_incremental_update(data_manager)
    
    with col3:
        if st.button("🔄 全量更新", help="完全替换所有数据", key="full_update_btn"):
            if st.session_state.get('confirm_full_update', False):
                perform_full_update(data_manager)
                st.session_state.confirm_full_update = False
            else:
                st.session_state.confirm_full_update = True
                st.warning("⚠️ 全量更新将删除所有现有数据，请再次点击确认")
    
    # 更新模式选择
    st.markdown("#### ⚙️ 更新设置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        update_mode = st.selectbox(
            "更新模式",
            ["增量更新", "全量替换"],
            help="选择数据更新的方式"
        )
        st.session_state.update_mode = update_mode
    
    with col2:
        batch_size = st.number_input(
            "批处理大小",
            min_value=100,
            max_value=10000,
            value=1000,
            help="每批处理的记录数量"
        )
        st.session_state.batch_size = batch_size

def show_auto_update_settings(data_manager: DataUpdateManager, key_prefix: str = ""):
    """显示自动更新设置"""
    st.markdown("### ⏰ 自动更新设置")

    col1, col2 = st.columns(2)

    with col1:
        auto_update_enabled = st.checkbox(
            "启用自动更新",
            value=st.session_state.get('auto_update_enabled', False),
            help="启用后将定期自动检查和更新数据",
            key=f"{key_prefix}auto_update_checkbox" if key_prefix else None
        )
        st.session_state.auto_update_enabled = auto_update_enabled

    with col2:
        if auto_update_enabled:
            update_interval = st.selectbox(
                "更新间隔",
                ["每小时", "每6小时", "每12小时", "每天"],
                index=3,
                help="自动更新的时间间隔",
                key=f"{key_prefix}update_interval_select" if key_prefix else None
            )
            st.session_state.update_interval = update_interval
    
    # 自动更新状态
    if auto_update_enabled:
        st.info(f"🤖 自动更新已启用，间隔: {st.session_state.get('update_interval', '每天')}")
        
        # 下次更新时间（模拟）
        next_update = datetime.now() + timedelta(hours=24)
        st.write(f"📅 下次自动更新时间: {next_update.strftime('%Y-%m-%d %H:%M:%S')}")
    else:
        st.info("⏸️ 自动更新已禁用")

def perform_manual_update(data_manager: DataUpdateManager):
    """执行手动更新"""
    with st.spinner("正在获取最新数据..."):
        # 获取最新数据
        fetch_result = data_manager.fetch_latest_data()
        
        if fetch_result['status'] == 'success':
            st.success(f"✅ {fetch_result['message']}")
            
            # 更新数据库
            update_mode = 'incremental' if st.session_state.get('update_mode') == '增量更新' else 'full_replace'
            
            with st.spinner("正在更新数据库..."):
                update_result = data_manager.update_database(
                    fetch_result['data'], 
                    update_mode
                )
                
                if update_result['status'] == 'success':
                    # 详细的成功反馈
                    st.success(f"✅ {update_result['message']}")

                    # 显示详细信息
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("更新记录数", update_result.get('updated_records', 0))
                    with col2:
                        st.metric("更新时间", update_result.get('update_time', '未知'))
                    with col3:
                        warnings = update_result.get('validation_warnings', 0)
                        if warnings > 0:
                            st.metric("验证警告", warnings, delta="需要注意")
                        else:
                            st.metric("数据质量", "优秀", delta="无警告")

                    st.balloons()

                    # 刷新页面状态
                    st.rerun()
                else:
                    # 详细的错误反馈
                    st.error(f"❌ {update_result['message']}")

                    # 显示错误详情
                    error_type = update_result.get('error_type', 'unknown_error')
                    if error_type == 'constraint_violation':
                        st.error("💡 **解决建议**: 检查数据格式，确保所有必需字段都有有效值")
                    elif error_type == 'duplicate_key':
                        st.warning("💡 **解决建议**: 数据可能已存在，尝试使用增量更新模式")
                    elif error_type == 'operational_error':
                        st.error("💡 **解决建议**: 检查数据库文件权限和磁盘空间")

                    # 显示技术详情（可展开）
                    if 'technical_details' in update_result:
                        with st.expander("🔧 技术详情"):
                            st.code(update_result['technical_details'])
        else:
            st.error(f"❌ {fetch_result['message']}")

def perform_incremental_update(data_manager: DataUpdateManager):
    """执行增量更新"""
    with st.spinner("正在执行增量更新..."):
        # 获取最新数据
        fetch_result = data_manager.fetch_latest_data()
        
        if fetch_result['status'] == 'success':
            # 执行增量更新
            update_result = data_manager.update_database(
                fetch_result['data'], 
                'incremental'
            )
            
            if update_result['status'] == 'success':
                st.success(f"✅ 增量更新完成: {update_result['message']}")

                # 显示增量更新详情
                updated_count = update_result.get('updated_records', 0)
                if updated_count > 0:
                    st.info(f"📊 本次增量更新添加了 {updated_count} 条新记录")

                    # 显示更新统计
                    col1, col2 = st.columns(2)
                    with col1:
                        st.metric("新增记录", updated_count)
                    with col2:
                        warnings = update_result.get('validation_warnings', 0)
                        st.metric("数据质量", "良好" if warnings == 0 else f"{warnings}个警告")
                else:
                    st.info("ℹ️ 没有发现新数据，数据库已是最新状态")

                st.rerun()
            else:
                st.error(f"❌ 增量更新失败: {update_result['message']}")

                # 显示错误处理建议
                if 'error_type' in update_result:
                    error_type = update_result['error_type']
                    if error_type == 'constraint_violation':
                        st.warning("💡 **建议**: 数据格式可能有问题，请检查数据源")
                    elif error_type == 'duplicate_key':
                        st.info("💡 **建议**: 数据可能已存在，这通常是正常的")

                # 技术详情
                if 'technical_details' in update_result:
                    with st.expander("🔧 查看技术详情"):
                        st.code(update_result['technical_details'])
        else:
            st.error(f"❌ 数据获取失败: {fetch_result['message']}")

def perform_full_update(data_manager: DataUpdateManager):
    """执行全量更新"""
    with st.spinner("正在执行全量更新..."):
        # 获取最新数据
        fetch_result = data_manager.fetch_latest_data()
        
        if fetch_result['status'] == 'success':
            # 执行全量更新
            update_result = data_manager.update_database(
                fetch_result['data'], 
                'full_replace'
            )
            
            if update_result['status'] == 'success':
                st.success(f"✅ 全量更新完成: {update_result['message']}")

                # 显示全量更新详情
                updated_count = update_result.get('updated_records', 0)
                st.info(f"🔄 全量更新完成，数据库现有 {updated_count} 条记录")

                # 显示更新统计
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("总记录数", updated_count)
                with col2:
                    st.metric("更新时间", update_result.get('update_time', '未知'))
                with col3:
                    warnings = update_result.get('validation_warnings', 0)
                    st.metric("数据质量", "优秀" if warnings == 0 else f"{warnings}个警告")

                st.balloons()
                st.rerun()
            else:
                st.error(f"❌ 全量更新失败: {update_result['message']}")

                # 显示错误处理建议
                st.warning("⚠️ **重要**: 全量更新失败可能导致数据不完整")
                st.info("💡 **建议**: 尝试使用增量更新模式，或检查网络连接")

                # 技术详情
                if 'technical_details' in update_result:
                    with st.expander("🔧 查看技术详情"):
                        st.code(update_result['technical_details'])
        else:
            st.error(f"❌ 数据获取失败: {fetch_result['message']}")

def show_operation_summary(operation_type: str, result: Dict[str, Any]):
    """显示操作结果摘要"""
    if result['status'] == 'success':
        st.success(f"🎉 {operation_type}操作成功完成！")

        # 创建摘要信息
        summary_data = {
            "操作类型": operation_type,
            "执行状态": "✅ 成功",
            "更新记录数": result.get('updated_records', 0),
            "执行时间": result.get('update_time', '未知'),
            "数据质量": "优秀" if result.get('validation_warnings', 0) == 0 else f"{result.get('validation_warnings', 0)}个警告"
        }

        # 显示摘要表格
        st.table(summary_data)

        # 如果有警告，显示详情
        if result.get('validation_warnings', 0) > 0:
            st.warning(f"⚠️ 发现 {result['validation_warnings']} 个数据验证警告，但操作已成功完成")

    else:
        st.error(f"❌ {operation_type}操作失败")

        # 错误摘要
        error_summary = {
            "操作类型": operation_type,
            "执行状态": "❌ 失败",
            "错误类型": result.get('error_type', '未知错误'),
            "错误信息": result.get('message', '无详细信息')
        }

        st.table(error_summary)

        # 显示解决建议
        error_type = result.get('error_type', '')
        if error_type == 'constraint_violation':
            st.info("💡 **解决方案**: 检查数据格式，确保所有必需字段都有有效值")
        elif error_type == 'duplicate_key':
            st.info("💡 **解决方案**: 数据可能已存在，尝试使用增量更新模式")
        elif error_type == 'operational_error':
            st.info("💡 **解决方案**: 检查数据库文件权限和磁盘空间")
        else:
            st.info("💡 **解决方案**: 检查网络连接，稍后重试，或联系技术支持")

def show_update_history():
    """显示更新历史"""
    st.markdown("### 📋 更新历史")

    # 从API获取真实的更新历史数据
    try:
        response = requests.get("http://127.0.0.1:8888/api/v1/data/update/history?limit=10", timeout=10)

        if response.status_code == 200:
            api_data = response.json()
            updates = api_data.get('updates', [])

            if updates:
                # 转换API数据为显示格式
                update_history = []
                for update in updates:
                    update_history.append({
                        '时间': update.get('timestamp', 'N/A'),
                        '类型': '手动更新' if update.get('status') == 'completed' else '自动更新',
                        '状态': '成功' if update.get('status') == 'completed' else update.get('status', 'N/A'),
                        '更新记录数': update.get('records_added', 0),
                        '耗时': f"{update.get('duration_ms', 0)/1000:.1f}秒",
                        '质量评分': f"{update.get('quality_score', 0):.1f}",
                        '总记录数': update.get('total_records', 0)
                    })

                df_history = pd.DataFrame(update_history)
                st.dataframe(df_history, use_container_width=True)
            else:
                st.info("📝 暂无更新历史记录")
        else:
            st.error(f"❌ 获取更新历史失败: HTTP {response.status_code}")
            # 显示模拟数据作为备用
            show_fallback_update_history()
    except Exception as e:
        st.error(f"❌ 连接API失败: {str(e)}")
        # 显示模拟数据作为备用
        show_fallback_update_history()

def show_fallback_update_history():
    """显示备用的模拟更新历史数据"""
    st.warning("⚠️ 显示模拟数据")
    update_history = [
        {
            '时间': '2025-01-14 10:30:00',
            '类型': '手动更新',
            '状态': '成功',
            '更新记录数': 1250,
            '耗时': '2.3秒'
        },
        {
            '时间': '2025-01-13 18:00:00',
            '类型': '自动更新',
            '状态': '成功',
            '更新记录数': 1,
            '耗时': '1.1秒'
        }
    ]
    df_history = pd.DataFrame(update_history)
    st.dataframe(df_history, use_container_width=True)

def show_data_source_status():
    """显示数据源状态"""
    st.markdown("### 🌐 数据源状态")
    
    data_source_url = "https://data.17500.cn/3d_asc.txt"
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**数据源地址:**")
        st.code(data_source_url)
    
    with col2:
        if st.button("🔍 检查数据源", key="check_data_source_btn"):
            with st.spinner("正在检查数据源状态..."):
                try:
                    response = requests.head(data_source_url, timeout=10)
                    if response.status_code == 200:
                        st.success("✅ 数据源可访问")
                        st.write(f"响应时间: {response.elapsed.total_seconds():.2f}秒")
                    else:
                        st.error(f"❌ 数据源返回状态码: {response.status_code}")
                except Exception as e:
                    st.error(f"❌ 数据源不可访问: {str(e)}")

# 主要的数据管理页面函数
def show_enhanced_data_management_page():
    """显示增强的数据管理页面"""
    st.markdown("## 🔄 数据更新")
    
    # 创建选项卡
    tab1, tab2, tab3, tab4 = st.tabs([
        "📊 数据状态", "🔄 数据更新", "📋 更新历史", "🌐 数据源管理"
    ])
    
    with tab1:
        show_data_update_interface()
    
    with tab2:
        # 数据更新操作的详细界面
        data_manager = DataUpdateManager()

        # 显示更新操作（使用不同的key前缀避免冲突）
        st.markdown("### 🔄 数据更新操作")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("🔄 手动更新", type="primary", key="tab2_manual_update_btn", help="立即从数据源获取最新数据"):
                perform_manual_update(data_manager)

        with col2:
            if st.button("📥 增量更新", help="只更新新增的数据记录", key="tab2_incremental_update_btn"):
                perform_incremental_update(data_manager)

        with col3:
            if st.button("🔄 全量更新", help="完全替换所有数据", key="tab2_full_update_btn"):
                if st.session_state.get('confirm_full_update_tab2', False):
                    perform_full_update(data_manager)
                    st.session_state.confirm_full_update_tab2 = False
                else:
                    st.session_state.confirm_full_update_tab2 = True
                    st.warning("⚠️ 全量更新将删除所有现有数据，请再次点击确认")

        # 更新模式选择
        st.markdown("#### ⚙️ 更新设置")

        col1, col2 = st.columns(2)

        with col1:
            update_mode = st.selectbox(
                "更新模式",
                ["增量更新", "全量替换"],
                help="选择数据更新的方式",
                key="tab2_update_mode_select"
            )
            st.session_state.update_mode = update_mode

        with col2:
            batch_size = st.number_input(
                "批处理大小",
                min_value=100,
                max_value=10000,
                value=1000,
                help="每批处理的记录数量",
                key="tab2_batch_size_input"
            )
            st.session_state.batch_size = batch_size

        show_auto_update_settings(data_manager, "tab2_")
    
    with tab3:
        show_update_history()
    
    with tab4:
        show_data_source_status()
