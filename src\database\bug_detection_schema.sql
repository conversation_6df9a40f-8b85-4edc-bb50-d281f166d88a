-- Bug检测系统数据库扩展Schema
-- 创建日期: 2025年7月24日
-- 用途: 为福彩3D预测系统添加Bug检测和监控功能

-- Bug报告表
CREATE TABLE IF NOT EXISTS bug_reports (
    id TEXT PRIMARY KEY,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    error_type TEXT NOT NULL,
    severity TEXT CHECK(severity IN ('low', 'medium', 'high', 'critical')) DEFAULT 'medium',
    page_name TEXT,
    component_name TEXT,
    error_message TEXT,
    stack_trace TEXT,
    reproduction_steps TEXT,
    system_context TEXT,
    user_journey TEXT,
    screenshots TEXT,  -- JSON array of screenshot paths
    status TEXT DEFAULT 'open' CHECK(status IN ('open', 'in_progress', 'resolved', 'closed')),
    assigned_to TEXT,
    resolution_notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 用户行为跟踪表
CREATE TABLE IF NOT EXISTS user_behaviors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT NOT NULL,
    user_id TEXT,
    page_name TEXT,
    action_type TEXT,  -- click, input, navigate, error
    element_id TEXT,
    element_type TEXT,
    action_data TEXT,  -- JSON data
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    ip_address TEXT,
    user_agent TEXT
);

-- 性能监控表
CREATE TABLE IF NOT EXISTS performance_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    metric_type TEXT,  -- api_response, page_load, database_query
    endpoint TEXT,
    method TEXT,
    response_time REAL,
    status_code INTEGER,
    error_message TEXT,
    request_size INTEGER,
    response_size INTEGER,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 测试执行记录表
CREATE TABLE IF NOT EXISTS test_executions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    test_suite TEXT,
    test_name TEXT,
    test_type TEXT,  -- unit, integration, e2e, visual
    status TEXT CHECK(status IN ('passed', 'failed', 'skipped')),
    execution_time REAL,
    error_message TEXT,
    screenshots TEXT,  -- JSON array for visual tests
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- JavaScript错误表
CREATE TABLE IF NOT EXISTS js_errors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT,
    page_url TEXT,
    error_message TEXT,
    error_source TEXT,
    line_number INTEGER,
    column_number INTEGER,
    stack_trace TEXT,
    user_agent TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_bug_reports_timestamp ON bug_reports(timestamp);
CREATE INDEX IF NOT EXISTS idx_bug_reports_status ON bug_reports(status);
CREATE INDEX IF NOT EXISTS idx_bug_reports_severity ON bug_reports(severity);
CREATE INDEX IF NOT EXISTS idx_user_behaviors_session ON user_behaviors(session_id);
CREATE INDEX IF NOT EXISTS idx_user_behaviors_timestamp ON user_behaviors(timestamp);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_timestamp ON performance_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_endpoint ON performance_metrics(endpoint);
CREATE INDEX IF NOT EXISTS idx_test_executions_timestamp ON test_executions(timestamp);
CREATE INDEX IF NOT EXISTS idx_test_executions_status ON test_executions(status);
CREATE INDEX IF NOT EXISTS idx_js_errors_timestamp ON js_errors(timestamp);
CREATE INDEX IF NOT EXISTS idx_js_errors_session ON js_errors(session_id);

-- 创建视图以便于查询
CREATE VIEW IF NOT EXISTS bug_summary AS
SELECT 
    severity,
    status,
    COUNT(*) as count,
    DATE(created_at) as date
FROM bug_reports 
GROUP BY severity, status, DATE(created_at);

CREATE VIEW IF NOT EXISTS performance_summary AS
SELECT 
    endpoint,
    AVG(response_time) as avg_response_time,
    MAX(response_time) as max_response_time,
    MIN(response_time) as min_response_time,
    COUNT(*) as request_count,
    DATE(timestamp) as date
FROM performance_metrics 
GROUP BY endpoint, DATE(timestamp);

CREATE VIEW IF NOT EXISTS test_summary AS
SELECT 
    test_suite,
    test_type,
    status,
    COUNT(*) as count,
    AVG(execution_time) as avg_execution_time,
    DATE(timestamp) as date
FROM test_executions 
GROUP BY test_suite, test_type, status, DATE(timestamp);
