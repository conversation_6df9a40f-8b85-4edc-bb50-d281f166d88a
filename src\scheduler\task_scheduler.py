"""
福彩3D定时任务调度器

使用APScheduler实现自动定时数据更新功能
"""

import json
import logging
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root / 'src'))

try:
    from apscheduler.events import EVENT_JOB_ERROR, EVENT_JOB_EXECUTED
    from apscheduler.executors.pool import ThreadPoolExecutor
    from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
    from apscheduler.schedulers.background import BackgroundScheduler
    from apscheduler.schedulers.blocking import BlockingScheduler
    from apscheduler.triggers.cron import CronTrigger
    from apscheduler.triggers.interval import IntervalTrigger
    APSCHEDULER_AVAILABLE = True
    print("[成功] APScheduler模块加载成功")
except ImportError as e:
    APSCHEDULER_AVAILABLE = False
    print(f"[失败] APScheduler导入失败: {e}")
    print("[建议] 解决方案: pip install apscheduler")

    # 尝试安装APScheduler
    try:
        import subprocess
        import sys
        print("[安装] 尝试自动安装APScheduler...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", "apscheduler"],
                              capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            print("[成功] APScheduler安装成功，请重启应用")
            # 重新尝试导入
            try:
                from apscheduler.events import (EVENT_JOB_ERROR,
                                                EVENT_JOB_EXECUTED)
                from apscheduler.executors.pool import ThreadPoolExecutor
                from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
                from apscheduler.schedulers.background import \
                    BackgroundScheduler
                from apscheduler.schedulers.blocking import BlockingScheduler
                from apscheduler.triggers.cron import CronTrigger
                from apscheduler.triggers.interval import IntervalTrigger
                APSCHEDULER_AVAILABLE = True
                print("[成功] APScheduler重新导入成功")
            except ImportError:
                print("[警告] APScheduler安装后仍无法导入，请重启应用")
        else:
            print(f"[失败] APScheduler自动安装失败: {result.stderr}")
    except Exception as install_error:
        print(f"[错误] 自动安装过程出错: {install_error}")

from data.incremental_updater import IncrementalUpdater

logger = logging.getLogger(__name__)

# 全局任务函数（避免序列化问题）
def data_update_task():
    """独立的数据更新任务函数"""
    try:
        updater = IncrementalUpdater()
        result = updater.perform_incremental_update()

        if result["success"]:
            if "update_info" in result:
                update_info = result["update_info"]
                logger.info(f"数据更新成功: 新增 {update_info.get('records_added', 0)} 条记录")
            else:
                logger.info(f"数据更新完成: {result['message']}")
        else:
            logger.error(f"数据更新失败: {result['message']}")

    except Exception as e:
        logger.error(f"数据更新任务执行异常: {e}")
        raise

def cleanup_task():
    """独立的文件清理任务函数"""
    try:
        data_dir = Path("data")
        temp_dir = data_dir / "temp"

        if temp_dir.exists():
            deleted_count = 0
            for file_path in temp_dir.glob("*"):
                if file_path.is_file():
                    file_path.unlink()
                    deleted_count += 1

            logger.info(f"文件清理完成: 删除 {deleted_count} 个临时文件")
        else:
            logger.info("临时目录不存在，跳过文件清理")

    except Exception as e:
        logger.error(f"文件清理任务执行异常: {e}")

def log_cleanup_task():
    """独立的日志清理任务函数"""
    try:
        data_dir = Path("data")
        logs_dir = data_dir / "logs"

        if logs_dir.exists():
            cutoff_date = datetime.now() - timedelta(days=30)
            deleted_count = 0

            for log_file in logs_dir.glob("*.log"):
                if log_file.stat().st_mtime < cutoff_date.timestamp():
                    log_file.unlink()
                    deleted_count += 1

            logger.info(f"日志清理完成: 删除 {deleted_count} 个旧日志文件")
        else:
            logger.info("日志目录不存在，跳过日志清理")

    except Exception as e:
        logger.error(f"日志清理任务执行异常: {e}")


class TaskScheduler:
    """定时任务调度器"""
    
    def __init__(self, data_dir: str = "data", config_file: str = "scheduler_config.json"):
        """
        初始化调度器
        
        Args:
            data_dir: 数据目录
            config_file: 配置文件路径
        """
        self.data_dir = Path(data_dir)
        self.config_file = Path(config_file)
        self.log_dir = self.data_dir / "logs"
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化组件
        self.updater = IncrementalUpdater(str(self.data_dir))
        self.scheduler = None
        self.config = self.load_config()
        
        # 设置日志
        self.setup_logging()
        
        if APSCHEDULER_AVAILABLE:
            self.init_scheduler()
            logger.info("定时任务调度器初始化完成")
        else:
            logger.error("APScheduler未安装，无法使用定时任务功能")
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        default_config = {
            "update_schedule": {
                "enabled": True,
                "cron": "0 21 * * *",  # 每天21:00执行
                "timezone": "Asia/Shanghai"
            },
            "cleanup_schedule": {
                "enabled": True,
                "cron": "0 2 * * 0",  # 每周日02:00执行
                "keep_files": 10
            },
            "monitoring": {
                "enabled": True,
                "max_failures": 3,
                "notification_email": None
            },
            "logging": {
                "level": "INFO",
                "max_log_files": 30,
                "log_rotation": "daily"
            }
        }
        
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                
                # 合并配置
                default_config.update(user_config)
                logger.info(f"配置文件加载成功: {self.config_file}")
                
            except Exception as e:
                logger.warning(f"配置文件加载失败，使用默认配置: {e}")
        
        # 保存配置文件
        self.save_config(default_config)
        return default_config
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """
        保存配置文件
        
        Args:
            config: 配置字典
            
        Returns:
            保存是否成功
        """
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            return False
    
    def setup_logging(self):
        """设置日志系统"""
        log_level = getattr(logging, self.config["logging"]["level"].upper(), logging.INFO)
        
        # 创建日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 文件日志处理器
        log_file = self.log_dir / f"scheduler_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(log_level)
        
        # 控制台日志处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(log_level)
        
        # 配置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)
        
        logger.info(f"日志系统初始化完成，日志文件: {log_file}")
    
    def init_scheduler(self):
        """初始化APScheduler调度器"""
        if not APSCHEDULER_AVAILABLE:
            return
        
        # 配置调度器
        jobstores = {
            'default': SQLAlchemyJobStore(url=f'sqlite:///{self.data_dir}/scheduler.db')
        }
        
        executors = {
            'default': ThreadPoolExecutor(20)
        }
        
        job_defaults = {
            'coalesce': False,
            'max_instances': 1
        }
        
        self.scheduler = BackgroundScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone='Asia/Shanghai'
        )
        
        # 添加事件监听器
        self.scheduler.add_listener(self.job_executed_listener, EVENT_JOB_EXECUTED)
        self.scheduler.add_listener(self.job_error_listener, EVENT_JOB_ERROR)
        
        logger.info("APScheduler调度器初始化完成")
    
    def job_executed_listener(self, event):
        """任务执行成功监听器"""
        logger.info(f"任务执行成功: {event.job_id}")
    
    def job_error_listener(self, event):
        """任务执行失败监听器"""
        logger.error(f"任务执行失败: {event.job_id}, 异常: {event.exception}")
        
        # 记录失败信息
        self.record_job_failure(event.job_id, str(event.exception))
    
    def record_job_failure(self, job_id: str, error_message: str):
        """记录任务失败信息"""
        failure_log = self.data_dir / "job_failures.json"
        
        try:
            failures = []
            if failure_log.exists():
                with open(failure_log, 'r', encoding='utf-8') as f:
                    failures = json.load(f)
            
            failures.append({
                "job_id": job_id,
                "timestamp": datetime.now().isoformat(),
                "error": error_message
            })
            
            # 只保留最近100条失败记录
            failures = failures[-100:]
            
            with open(failure_log, 'w', encoding='utf-8') as f:
                json.dump(failures, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"记录任务失败信息时出错: {e}")
    
    def data_update_job(self):
        """数据更新任务（增强版）"""
        logger.info("开始执行定时数据更新任务")

        # 记录开始时间
        start_time = datetime.now()

        try:
            # 执行更新前的状态检查
            logger.info("执行更新前状态检查...")
            pre_status = self._get_database_status()
            logger.info(f"更新前状态: 记录数={pre_status.get('count', 0)}, 最新期号={pre_status.get('latest_period', 'N/A')}")

            # 执行增量更新
            result = self.updater.perform_incremental_update()

            if result["success"]:
                # 执行更新后的状态检查
                logger.info("执行更新后状态检查...")
                post_status = self._get_database_status()
                logger.info(f"更新后状态: 记录数={post_status.get('count', 0)}, 最新期号={post_status.get('latest_period', 'N/A')}")

                # 验证更新结果
                records_added = post_status.get('count', 0) - pre_status.get('count', 0)

                if "update_info" in result:
                    update_info = result["update_info"]
                    expected_added = update_info.get('records_added', 0)

                    if records_added == expected_added:
                        logger.info(f"[成功] 数据更新成功: 新增 {records_added} 条记录")
                    else:
                        logger.warning(f"[警告] 数据更新异常: 预期新增 {expected_added} 条，实际新增 {records_added} 条")
                else:
                    logger.info(f"[成功] 数据更新完成: {result['message']}")

                # 检查一致性
                if "consistency_check" in result:
                    consistency = result["consistency_check"]
                    if consistency.get("is_consistent", False):
                        logger.info("[成功] 数据一致性检查通过")
                    else:
                        logger.warning("[警告] 数据一致性检查发现问题")

            else:
                logger.error(f"[失败] 数据更新失败: {result['message']}")
                # 记录失败信息
                self._record_failure("data_update", result['message'], start_time)

        except Exception as e:
            logger.error(f"[异常] 数据更新任务执行异常: {e}")
            # 记录异常信息
            self._record_failure("data_update", str(e), start_time)
            raise

        # 记录执行时间
        duration = (datetime.now() - start_time).total_seconds()
        logger.info(f"数据更新任务完成，耗时: {duration:.2f}秒")
    
    def cleanup_job(self):
        """文件清理任务"""
        logger.info("开始执行定时文件清理任务")
        
        try:
            keep_files = self.config["cleanup_schedule"]["keep_files"]
            result = self.updater.cleanup_old_files(keep_files)
            
            if result["success"]:
                logger.info(f"文件清理成功: 删除原始文件 {result['deleted_raw_files']} 个, "
                          f"处理文件 {result['deleted_processed_files']} 个")
            else:
                logger.error(f"文件清理失败: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            logger.error(f"文件清理任务执行异常: {e}")
            raise
    
    def log_cleanup_job(self):
        """日志清理任务"""
        logger.info("开始执行日志清理任务")
        
        try:
            max_log_files = self.config["logging"]["max_log_files"]
            log_files = sorted(
                self.log_dir.glob("scheduler_*.log"),
                key=lambda f: f.stat().st_mtime,
                reverse=True
            )
            
            deleted_count = 0
            for log_file in log_files[max_log_files:]:
                log_file.unlink()
                deleted_count += 1
            
            logger.info(f"日志清理完成: 删除 {deleted_count} 个旧日志文件")
            
        except Exception as e:
            logger.error(f"日志清理任务执行异常: {e}")
    
    def add_jobs(self):
        """添加定时任务"""
        if not APSCHEDULER_AVAILABLE or not self.scheduler:
            logger.warning("调度器不可用，无法添加任务")
            return
        
        # 数据更新任务
        if self.config["update_schedule"]["enabled"]:
            self.scheduler.add_job(
                func=data_update_task,
                trigger=CronTrigger.from_crontab(self.config["update_schedule"]["cron"]),
                id='data_update',
                name='数据更新任务',
                replace_existing=True
            )
            logger.info(f"数据更新任务已添加: {self.config['update_schedule']['cron']}")

        # 文件清理任务
        if self.config["cleanup_schedule"]["enabled"]:
            self.scheduler.add_job(
                func=cleanup_task,
                trigger=CronTrigger.from_crontab(self.config["cleanup_schedule"]["cron"]),
                id='file_cleanup',
                name='文件清理任务',
                replace_existing=True
            )
            logger.info(f"文件清理任务已添加: {self.config['cleanup_schedule']['cron']}")

        # 日志清理任务（每天凌晨3点）
        self.scheduler.add_job(
            func=log_cleanup_task,
            trigger=CronTrigger(hour=3, minute=0),
            id='log_cleanup',
            name='日志清理任务',
            replace_existing=True
        )
        logger.info("日志清理任务已添加: 每天03:00")
    
    def start(self):
        """启动调度器"""
        if not APSCHEDULER_AVAILABLE:
            logger.error("APScheduler不可用，无法启动调度器")
            return False
        
        try:
            self.add_jobs()
            self.scheduler.start()
            logger.info("定时任务调度器启动成功")
            return True
        except Exception as e:
            logger.error(f"启动调度器失败: {e}")
            return False
    
    def stop(self):
        """停止调度器"""
        if self.scheduler and self.scheduler.running:
            self.scheduler.shutdown()
            logger.info("定时任务调度器已停止")
    
    def get_job_status(self) -> Dict[str, Any]:
        """获取任务状态"""
        if not self.scheduler:
            return {"error": "调度器未初始化"}
        
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "next_run": job.next_run_time.isoformat() if job.next_run_time else None,
                "trigger": str(job.trigger)
            })
        
        return {
            "running": self.scheduler.running if self.scheduler else False,
            "jobs": jobs,
            "job_count": len(jobs)
        }
    
    def run_job_now(self, job_id: str) -> bool:
        """立即执行指定任务"""
        if not self.scheduler:
            return False
        
        try:
            job = self.scheduler.get_job(job_id)
            if job:
                job.func()
                logger.info(f"手动执行任务成功: {job_id}")
                return True
            else:
                logger.error(f"任务不存在: {job_id}")
                return False
        except Exception as e:
            logger.error(f"手动执行任务失败: {job_id}, 错误: {e}")
            return False

    def _get_database_status(self) -> Dict[str, Any]:
        """获取数据库状态"""
        try:
            import sqlite3
            db_path = self.data_dir / "lottery.db"

            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()

                # 获取记录数
                cursor.execute("SELECT COUNT(*) FROM lottery_records")
                count = cursor.fetchone()[0]

                # 获取最新记录
                cursor.execute("SELECT period, date FROM lottery_records ORDER BY period DESC LIMIT 1")
                latest = cursor.fetchone()

                if latest:
                    return {
                        "count": count,
                        "latest_period": latest[0],
                        "latest_date": latest[1]
                    }
                else:
                    return {"count": 0, "latest_period": None, "latest_date": None}

        except Exception as e:
            logger.error(f"获取数据库状态失败: {e}")
            return {"count": 0, "latest_period": None, "latest_date": None}

    def _record_failure(self, task_name: str, error_message: str, start_time: datetime):
        """记录任务失败信息"""
        try:
            failure_info = {
                "task_name": task_name,
                "error_message": error_message,
                "start_time": start_time.isoformat(),
                "failure_time": datetime.now().isoformat(),
                "duration": (datetime.now() - start_time).total_seconds()
            }

            # 保存到失败记录文件
            failure_file = self.log_dir / f"task_failures_{datetime.now().strftime('%Y%m%d')}.json"

            failures = []
            if failure_file.exists():
                with open(failure_file, 'r', encoding='utf-8') as f:
                    failures = json.load(f)

            failures.append(failure_info)

            # 只保留最近100条失败记录
            failures = failures[-100:]

            with open(failure_file, 'w', encoding='utf-8') as f:
                json.dump(failures, f, ensure_ascii=False, indent=2)

            logger.info(f"任务失败信息已记录: {failure_file}")

        except Exception as e:
            logger.error(f"记录任务失败信息时出错: {e}")


# 便捷函数
def start_scheduler(data_dir: str = "data", config_file: str = "scheduler_config.json") -> TaskScheduler:
    """
    启动调度器的便捷函数
    
    Args:
        data_dir: 数据目录
        config_file: 配置文件
        
    Returns:
        调度器实例
    """
    scheduler = TaskScheduler(data_dir, config_file)
    scheduler.start()
    return scheduler


if __name__ == "__main__":
    # 测试代码
    print("测试定时任务调度器...")
    
    scheduler = TaskScheduler()
    
    # 显示任务状态
    status = scheduler.get_job_status()
    print(f"调度器状态: {status}")
    
    # 手动执行数据更新任务
    print("手动执行数据更新任务...")
    scheduler.data_update_job()
    
    print("调度器测试完成！")
