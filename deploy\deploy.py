#!/usr/bin/env python3
"""
生产环境部署脚本

自动化部署福彩3D预测系统到生产环境
"""

import os
import sys
import subprocess
import shutil
import time
import logging
from pathlib import Path
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ProductionDeployer:
    """生产环境部署器"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.deploy_dir = self.project_root / "deploy"
        self.backup_dir = self.project_root / "backup"
        self.logs_dir = self.project_root / "logs"
        
        # 部署配置
        self.python_version = "3.11"
        self.required_packages = [
            "fastapi", "uvicorn", "streamlit", "pandas", "numpy",
            "scikit-learn", "plotly", "requests", "websockets"
        ]
        
        # 服务配置
        self.services = {
            "api": {
                "script": "src/api/production_main.py",
                "port": 8000,
                "process_name": "lottery_api"
            },
            "ui": {
                "script": "src/ui/main.py",
                "port": 8501,
                "process_name": "lottery_ui"
            }
        }
    
    def check_prerequisites(self):
        """检查部署前提条件"""
        logger.info("🔍 检查部署前提条件...")
        
        issues = []
        
        # 检查Python版本
        python_version = sys.version_info
        if python_version.major != 3 or python_version.minor < 11:
            issues.append(f"Python版本不符合要求，需要3.11+，当前: {python_version.major}.{python_version.minor}")
        
        # 检查必需文件
        required_files = [
            "src/api/production_main.py",
            "src/ui/main.py",
            "lottery_data.db",
            "requirements.txt"
        ]
        
        for file_path in required_files:
            if not (self.project_root / file_path).exists():
                issues.append(f"缺少必需文件: {file_path}")
        
        # 检查目录结构
        required_dirs = ["src", "data", "logs"]
        for dir_path in required_dirs:
            if not (self.project_root / dir_path).exists():
                issues.append(f"缺少必需目录: {dir_path}")
        
        # 检查端口可用性
        for service_name, config in self.services.items():
            if self._is_port_in_use(config["port"]):
                issues.append(f"{service_name}服务端口{config['port']}已被占用")
        
        if issues:
            logger.error("❌ 前提条件检查失败:")
            for issue in issues:
                logger.error(f"  - {issue}")
            return False
        
        logger.info("✅ 前提条件检查通过")
        return True
    
    def _is_port_in_use(self, port):
        """检查端口是否被占用"""
        try:
            import socket
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                return s.connect_ex(('localhost', port)) == 0
        except:
            return False
    
    def install_dependencies(self):
        """安装依赖包"""
        logger.info("📦 安装依赖包...")
        
        try:
            # 升级pip
            subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                         check=True, capture_output=True)
            
            # 安装requirements.txt中的依赖
            if (self.project_root / "requirements.txt").exists():
                subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                             check=True, capture_output=True)
            else:
                # 安装基本依赖
                subprocess.run([sys.executable, "-m", "pip", "install"] + self.required_packages, 
                             check=True, capture_output=True)
            
            logger.info("✅ 依赖包安装完成")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ 依赖包安装失败: {e}")
            return False
    
    def create_directories(self):
        """创建必需目录"""
        logger.info("📁 创建必需目录...")
        
        directories = [
            self.backup_dir,
            self.logs_dir,
            self.project_root / "data" / "cache",
            self.project_root / "data" / "models",
            self.project_root / "data" / "exports"
        ]
        
        for directory in directories:
            try:
                directory.mkdir(parents=True, exist_ok=True)
                logger.info(f"✅ 目录已创建: {directory}")
            except Exception as e:
                logger.error(f"❌ 创建目录失败 {directory}: {e}")
                return False
        
        return True
    
    def backup_existing_data(self):
        """备份现有数据"""
        logger.info("💾 备份现有数据...")
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_subdir = self.backup_dir / f"deploy_backup_{timestamp}"
            backup_subdir.mkdir(parents=True, exist_ok=True)
            
            # 备份数据库
            if (self.project_root / "lottery_data.db").exists():
                shutil.copy2(
                    self.project_root / "lottery_data.db",
                    backup_subdir / "lottery_data.db"
                )
                logger.info("✅ 数据库已备份")
            
            # 备份配置文件
            config_files = ["config.json", ".env", ".env.production"]
            for config_file in config_files:
                config_path = self.project_root / config_file
                if config_path.exists():
                    shutil.copy2(config_path, backup_subdir / config_file)
                    logger.info(f"✅ 配置文件已备份: {config_file}")
            
            # 备份日志文件
            if self.logs_dir.exists():
                shutil.copytree(
                    self.logs_dir,
                    backup_subdir / "logs",
                    dirs_exist_ok=True
                )
                logger.info("✅ 日志文件已备份")
            
            logger.info(f"✅ 数据备份完成: {backup_subdir}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据备份失败: {e}")
            return False
    
    def setup_environment(self):
        """设置环境配置"""
        logger.info("⚙️ 设置环境配置...")
        
        try:
            # 创建生产环境配置
            from deploy.production_config import create_env_file
            create_env_file()
            
            # 设置环境变量
            os.environ["ENVIRONMENT"] = "production"
            os.environ["PYTHONPATH"] = str(self.project_root)
            
            logger.info("✅ 环境配置完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 环境配置失败: {e}")
            return False
    
    def validate_deployment(self):
        """验证部署"""
        logger.info("🔍 验证部署...")
        
        try:
            # 验证Python模块导入
            sys.path.insert(0, str(self.project_root))
            
            # 测试核心模块导入
            test_imports = [
                "src.core.database_manager",
                "src.api.production_main",
                "src.ui.main"
            ]
            
            for module_name in test_imports:
                try:
                    __import__(module_name)
                    logger.info(f"✅ 模块导入成功: {module_name}")
                except ImportError as e:
                    logger.error(f"❌ 模块导入失败 {module_name}: {e}")
                    return False
            
            # 验证数据库连接
            try:
                from src.core.database_manager import EnhancedDatabaseManager
                db_manager = EnhancedDatabaseManager()
                health_status = db_manager.health_check()
                if health_status.get("healthy", False):
                    logger.info("✅ 数据库连接验证成功")
                else:
                    logger.error("❌ 数据库连接验证失败")
                    return False
            except Exception as e:
                logger.error(f"❌ 数据库验证失败: {e}")
                return False
            
            logger.info("✅ 部署验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 部署验证失败: {e}")
            return False
    
    def create_service_scripts(self):
        """创建服务启动脚本"""
        logger.info("📝 创建服务启动脚本...")
        
        try:
            # API服务启动脚本
            api_script = self.deploy_dir / "start_api_production.py"
            api_content = f'''#!/usr/bin/env python3
"""
生产环境API服务启动脚本
"""

import os
import sys
from pathlib import Path

# 设置项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ["ENVIRONMENT"] = "production"
os.environ["PYTHONPATH"] = str(project_root)

if __name__ == "__main__":
    import uvicorn
    from src.api.production_main import app
    
    uvicorn.run(
        app,
        host="127.0.0.1",
        port=8000,
        workers=1,
        log_level="info",
        access_log=True
    )
'''
            
            with open(api_script, 'w', encoding='utf-8') as f:
                f.write(api_content)
            api_script.chmod(0o755)
            
            # Streamlit服务启动脚本
            ui_script = self.deploy_dir / "start_ui_production.py"
            ui_content = f'''#!/usr/bin/env python3
"""
生产环境UI服务启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

# 设置项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ["ENVIRONMENT"] = "production"
os.environ["PYTHONPATH"] = str(project_root)

if __name__ == "__main__":
    # 启动Streamlit
    cmd = [
        sys.executable, "-m", "streamlit", "run",
        str(project_root / "src" / "ui" / "main.py"),
        "--server.port", "8501",
        "--server.address", "127.0.0.1",
        "--server.headless", "true",
        "--server.enableCORS", "false"
    ]
    
    subprocess.run(cmd)
'''
            
            with open(ui_script, 'w', encoding='utf-8') as f:
                f.write(ui_content)
            ui_script.chmod(0o755)
            
            # 一键启动脚本
            start_all_script = self.deploy_dir / "start_all_production.py"
            start_all_content = '''#!/usr/bin/env python3
"""
生产环境一键启动脚本
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def start_services():
    """启动所有服务"""
    project_root = Path(__file__).parent.parent
    deploy_dir = project_root / "deploy"
    
    print("🚀 启动生产环境服务...")
    
    # 启动API服务
    print("启动API服务...")
    api_process = subprocess.Popen([
        sys.executable, str(deploy_dir / "start_api_production.py")
    ])
    
    # 等待API服务启动
    time.sleep(5)
    
    # 启动UI服务
    print("启动UI服务...")
    ui_process = subprocess.Popen([
        sys.executable, str(deploy_dir / "start_ui_production.py")
    ])
    
    print("✅ 服务启动完成")
    print("API服务: http://127.0.0.1:8000")
    print("UI服务: http://127.0.0.1:8501")
    
    try:
        # 等待进程结束
        api_process.wait()
        ui_process.wait()
    except KeyboardInterrupt:
        print("\\n停止服务...")
        api_process.terminate()
        ui_process.terminate()

if __name__ == "__main__":
    start_services()
'''
            
            with open(start_all_script, 'w', encoding='utf-8') as f:
                f.write(start_all_content)
            start_all_script.chmod(0o755)
            
            logger.info("✅ 服务启动脚本创建完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建服务脚本失败: {e}")
            return False
    
    def deploy(self):
        """执行完整部署流程"""
        logger.info("🚀 开始生产环境部署...")
        
        deployment_steps = [
            ("检查前提条件", self.check_prerequisites),
            ("创建目录结构", self.create_directories),
            ("备份现有数据", self.backup_existing_data),
            ("安装依赖包", self.install_dependencies),
            ("设置环境配置", self.setup_environment),
            ("创建服务脚本", self.create_service_scripts),
            ("验证部署", self.validate_deployment)
        ]
        
        for step_name, step_func in deployment_steps:
            logger.info(f"执行步骤: {step_name}")
            
            try:
                if not step_func():
                    logger.error(f"❌ 步骤失败: {step_name}")
                    return False
                
                logger.info(f"✅ 步骤完成: {step_name}")
                
            except Exception as e:
                logger.error(f"❌ 步骤异常 {step_name}: {e}")
                return False
        
        logger.info("🎉 生产环境部署完成！")
        logger.info("使用以下命令启动服务:")
        logger.info("  python deploy/start_all_production.py")
        
        return True

def main():
    """主函数"""
    deployer = ProductionDeployer()
    
    try:
        success = deployer.deploy()
        return 0 if success else 1
        
    except KeyboardInterrupt:
        logger.info("部署被用户中断")
        return 1
    except Exception as e:
        logger.error(f"部署失败: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
