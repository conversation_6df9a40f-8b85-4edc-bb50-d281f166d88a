"""
模型选择模块

使用AIC/BIC准则选择最优的马尔可夫模型阶数
"""

import numpy as np
import os
import json
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime
import matplotlib.pyplot as plt
from collections import defaultdict

from .model_validation import MarkovCrossValidator
from .markov_validator import MarkovModelValidator


class MarkovModelSelector:
    """马尔可夫模型选择器"""
    
    def __init__(self, db_path: str = None):
        """
        初始化选择器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path or os.path.join('data', 'lottery.db')
        self.validator = MarkovModelValidator(db_path)
        self.cross_validator = self.validator.cross_validator
        self.selection_results = {}
        
    def select_optimal_order(self, 
                           max_order: int = 3,
                           window_sizes: List[int] = None,
                           alphas: List[float] = None,
                           k_folds: int = 3,
                           data_limit: int = 1000) -> Dict[str, Any]:
        """
        选择最优的马尔可夫模型阶数
        
        Args:
            max_order: 最大阶数
            window_sizes: 窗口大小列表
            alphas: 平滑参数列表
            k_folds: 交叉验证折数
            data_limit: 数据限制
            
        Returns:
            选择结果
        """
        print(f"🔍 开始选择最优马尔可夫模型阶数 (最大阶数: {max_order})...")
        
        # 默认参数
        if window_sizes is None:
            window_sizes = [500, 1000, 2000]
        
        if alphas is None:
            alphas = [0.5, 1.0, 2.0]
        
        # 加载验证数据
        data = self.cross_validator.load_validation_data(limit=data_limit)
        if not data:
            return {'error': '无法加载验证数据'}
        
        print(f"✅ 加载了 {len(data)} 条验证数据")
        
        # 创建训练-测试分割
        train_data, test_data = self.cross_validator.time_series_split(data, test_ratio=0.2)
        
        print(f"📊 数据分割:")
        print(f"   训练集: {len(train_data)} 条")
        print(f"   测试集: {len(test_data)} 条")
        
        # 存储不同阶数、窗口大小和平滑参数的模型结果
        model_results = {}
        
        # 评估不同阶数的模型
        for order in range(1, max_order + 1):
            print(f"\n📈 评估 {order} 阶马尔可夫模型...")
            
            order_results = {}
            
            # 评估不同窗口大小
            for window_size in window_sizes:
                # 评估不同平滑参数
                for alpha in alphas:
                    print(f"   测试参数: 窗口大小={window_size}, 平滑参数={alpha}")
                    
                    # 模型参数键
                    model_key = f"order_{order}_window_{window_size}_alpha_{alpha}"
                    
                    try:
                        # 使用交叉验证评估模型
                        validation_result = self.validator.validate_markov_model(
                            transition_window_size=window_size,
                            probability_window_size=window_size // 2,  # 概率窗口为转移窗口的一半
                            smoothing_alpha=alpha,
                            k_folds=k_folds,
                            data_limit=data_limit
                        )
                        
                        # 提取性能指标
                        if 'overall_results' in validation_result:
                            overall_results = validation_result['overall_results']
                            
                            # 提取AIC、BIC和准确率
                            aic = overall_results.get('aic_bic', {}).get('aic', float('inf'))
                            bic = overall_results.get('aic_bic', {}).get('bic', float('inf'))
                            accuracy = overall_results.get('accuracy_metrics', {}).get('exact_match', 0.0)
                            diversity = overall_results.get('diversity_metrics', {}).get('simpson_diversity', 0.0)
                            
                            # 存储结果
                            order_results[model_key] = {
                                'order': order,
                                'window_size': window_size,
                                'alpha': alpha,
                                'aic': aic,
                                'bic': bic,
                                'accuracy': accuracy,
                                'diversity': diversity,
                                'validation_result': validation_result
                            }
                            
                            print(f"     AIC: {aic:.2f}, BIC: {bic:.2f}")
                            print(f"     准确率: {accuracy:.4f}, 多样性: {diversity:.4f}")
                        else:
                            print(f"     ❌ 无有效验证结果")
                    
                    except Exception as e:
                        print(f"     ❌ 模型评估失败: {e}")
            
            # 存储当前阶数的所有结果
            model_results[f"order_{order}"] = order_results
        
        # 找到AIC和BIC最小的模型
        best_aic_model = None
        best_bic_model = None
        best_accuracy_model = None
        
        min_aic = float('inf')
        min_bic = float('inf')
        max_accuracy = 0.0
        
        for order_key, order_results in model_results.items():
            for model_key, model_result in order_results.items():
                # 更新最佳AIC模型
                if model_result['aic'] < min_aic:
                    min_aic = model_result['aic']
                    best_aic_model = model_result
                
                # 更新最佳BIC模型
                if model_result['bic'] < min_bic:
                    min_bic = model_result['bic']
                    best_bic_model = model_result
                
                # 更新最佳准确率模型
                if model_result['accuracy'] > max_accuracy:
                    max_accuracy = model_result['accuracy']
                    best_accuracy_model = model_result
        
        # 汇总选择结果
        selection_results = {
            'best_aic_model': best_aic_model,
            'best_bic_model': best_bic_model,
            'best_accuracy_model': best_accuracy_model,
            'model_results': model_results,
            'parameters': {
                'max_order': max_order,
                'window_sizes': window_sizes,
                'alphas': alphas,
                'k_folds': k_folds,
                'data_limit': data_limit
            }
        }
        
        # 保存选择结果
        self.selection_results = selection_results
        
        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"markov_model_selection_{timestamp}.json"
        
        report_dir = os.path.join('data', 'model_selection')
        os.makedirs(report_dir, exist_ok=True)
        
        report_path = os.path.join(report_dir, report_filename)
        
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                # 创建可序列化的结果
                serializable_results = {
                    'best_aic_model': best_aic_model,
                    'best_bic_model': best_bic_model,
                    'best_accuracy_model': best_accuracy_model,
                    'parameters': selection_results['parameters'],
                    'timestamp': timestamp
                }
                json.dump(serializable_results, f, ensure_ascii=False, indent=2)
            
            print(f"\n✅ 模型选择报告已保存: {report_path}")
        except Exception as e:
            print(f"\n❌ 保存模型选择报告失败: {e}")
        
        # 打印选择结果
        print("\n📋 模型选择结果:")
        
        if best_aic_model:
            print(f"\n🏆 最佳AIC模型:")
            print(f"   阶数: {best_aic_model['order']}")
            print(f"   窗口大小: {best_aic_model['window_size']}")
            print(f"   平滑参数: {best_aic_model['alpha']}")
            print(f"   AIC: {best_aic_model['aic']:.2f}")
            print(f"   准确率: {best_aic_model['accuracy']:.4f}")
            print(f"   多样性: {best_aic_model['diversity']:.4f}")
        
        if best_bic_model:
            print(f"\n🏆 最佳BIC模型:")
            print(f"   阶数: {best_bic_model['order']}")
            print(f"   窗口大小: {best_bic_model['window_size']}")
            print(f"   平滑参数: {best_bic_model['alpha']}")
            print(f"   BIC: {best_bic_model['bic']:.2f}")
            print(f"   准确率: {best_bic_model['accuracy']:.4f}")
            print(f"   多样性: {best_bic_model['diversity']:.4f}")
        
        if best_accuracy_model:
            print(f"\n🏆 最佳准确率模型:")
            print(f"   阶数: {best_accuracy_model['order']}")
            print(f"   窗口大小: {best_accuracy_model['window_size']}")
            print(f"   平滑参数: {best_accuracy_model['alpha']}")
            print(f"   准确率: {best_accuracy_model['accuracy']:.4f}")
            print(f"   多样性: {best_accuracy_model['diversity']:.4f}")
        
        # 返回选择结果
        return selection_results
    
    def plot_model_comparison(self, save_path: str = None) -> str:
        """
        绘制模型比较图
        
        Args:
            save_path: 保存路径
            
        Returns:
            保存的文件路径
        """
        if not self.selection_results:
            print("❌ 没有可用的模型选择结果")
            return ""
        
        # 提取模型结果
        model_results = self.selection_results.get('model_results', {})
        if not model_results:
            print("❌ 没有可用的模型结果")
            return ""
        
        # 创建图表
        fig, axs = plt.subplots(2, 2, figsize=(15, 10))
        
        # 提取数据
        orders = []
        aics = []
        bics = []
        accuracies = []
        diversities = []
        
        # 对于每个阶数，计算平均指标
        for order_key, order_results in model_results.items():
            if not order_results:
                continue
            
            order = int(order_key.split('_')[1])
            
            # 计算平均值
            avg_aic = np.mean([result['aic'] for result in order_results.values() if 'aic' in result])
            avg_bic = np.mean([result['bic'] for result in order_results.values() if 'bic' in result])
            avg_accuracy = np.mean([result['accuracy'] for result in order_results.values() if 'accuracy' in result])
            avg_diversity = np.mean([result['diversity'] for result in order_results.values() if 'diversity' in result])
            
            orders.append(order)
            aics.append(avg_aic)
            bics.append(avg_bic)
            accuracies.append(avg_accuracy)
            diversities.append(avg_diversity)
        
        # 绘制AIC图表
        axs[0, 0].plot(orders, aics, 'o-', color='blue')
        axs[0, 0].set_title('平均AIC vs 模型阶数')
        axs[0, 0].set_xlabel('模型阶数')
        axs[0, 0].set_ylabel('AIC')
        axs[0, 0].grid(True)
        
        # 绘制BIC图表
        axs[0, 1].plot(orders, bics, 'o-', color='red')
        axs[0, 1].set_title('平均BIC vs 模型阶数')
        axs[0, 1].set_xlabel('模型阶数')
        axs[0, 1].set_ylabel('BIC')
        axs[0, 1].grid(True)
        
        # 绘制准确率图表
        axs[1, 0].plot(orders, accuracies, 'o-', color='green')
        axs[1, 0].set_title('平均准确率 vs 模型阶数')
        axs[1, 0].set_xlabel('模型阶数')
        axs[1, 0].set_ylabel('准确率')
        axs[1, 0].grid(True)
        
        # 绘制多样性图表
        axs[1, 1].plot(orders, diversities, 'o-', color='purple')
        axs[1, 1].set_title('平均多样性 vs 模型阶数')
        axs[1, 1].set_xlabel('模型阶数')
        axs[1, 1].set_ylabel('多样性')
        axs[1, 1].grid(True)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        if save_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_dir = os.path.join('data', 'model_selection')
            os.makedirs(save_dir, exist_ok=True)
            save_path = os.path.join(save_dir, f"model_comparison_{timestamp}.png")
        
        try:
            plt.savefig(save_path)
            print(f"✅ 模型比较图已保存: {save_path}")
            return save_path
        except Exception as e:
            print(f"❌ 保存模型比较图失败: {e}")
            return ""


if __name__ == "__main__":
    # 测试代码
    selector = MarkovModelSelector()
    
    # 选择最优阶数（简化参数，加快测试速度）
    result = selector.select_optimal_order(
        max_order=2,
        window_sizes=[500, 1000],
        alphas=[1.0],
        k_folds=2,
        data_limit=500
    )
    
    # 绘制比较图
    selector.plot_model_comparison()
