#!/usr/bin/env python3
"""测试MarkovEnhanced和PatternPredictor的导入"""

import sys
import traceback

# 添加src目录到路径
sys.path.append('src')

def test_markov_enhanced_import():
    """测试MarkovEnhanced导入"""
    print("🔍 测试 MarkovEnhanced 导入...")
    try:
        from src.prediction.markov_enhanced import MarkovEnhanced
        print("✅ MarkovEnhanced 导入成功")
        
        # 尝试创建实例
        markov = MarkovEnhanced()
        print("✅ MarkovEnhanced 实例创建成功")
        return True
        
    except Exception as e:
        print(f"❌ MarkovEnhanced 导入失败: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        return False

def test_pattern_predictor_import():
    """测试PatternPredictor导入"""
    print("\n🔍 测试 PatternPredictor 导入...")
    try:
        from src.prediction.pattern_prediction import PatternPredictor
        print("✅ PatternPredictor 导入成功")
        
        # 尝试创建实例
        predictor = PatternPredictor()
        print("✅ PatternPredictor 实例创建成功")
        return True
        
    except Exception as e:
        print(f"❌ PatternPredictor 导入失败: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        return False

def test_markov_wrapper_import():
    """测试MarkovWrapper中的导入逻辑"""
    print("\n🔍 测试 MarkovWrapper 中的导入逻辑...")
    try:
        # 模拟MarkovWrapper中的导入
        try:
            from src.prediction.markov_enhanced import MarkovEnhanced
            from src.prediction.pattern_prediction import PatternPredictor
            print("✅ 在MarkovWrapper上下文中导入成功")
            print(f"MarkovEnhanced: {MarkovEnhanced}")
            print(f"PatternPredictor: {PatternPredictor}")
            return True
        except ImportError as e:
            print(f"❌ 在MarkovWrapper上下文中导入失败: {e}")
            PatternPredictor = None
            MarkovEnhanced = None
            print(f"设置为None后 - PatternPredictor: {PatternPredictor}")
            print(f"设置为None后 - MarkovEnhanced: {MarkovEnhanced}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🔧 MarkovEnhanced 和 PatternPredictor 导入测试")
    print("=" * 60)
    
    # 测试各个导入
    markov_ok = test_markov_enhanced_import()
    pattern_ok = test_pattern_predictor_import()
    wrapper_ok = test_markov_wrapper_import()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"MarkovEnhanced 导入: {'✅' if markov_ok else '❌'}")
    print(f"PatternPredictor 导入: {'✅' if pattern_ok else '❌'}")
    print(f"MarkovWrapper 导入逻辑: {'✅' if wrapper_ok else '❌'}")
    
    if markov_ok and pattern_ok:
        print("\n🎉 所有导入测试通过！")
    else:
        print("\n⚠️ 存在导入问题，需要修复")
