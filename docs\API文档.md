# 福彩3D预测系统深度交互版 - API文档

## 📖 API概述

福彩3D预测系统深度交互版提供了完整的RESTful API接口，支持特征工程、数据管理、训练监控、A/B测试、元学习等核心功能的API访问。

### 🔗 基础信息
- **Base URL**: `http://localhost:8888`
- **API版本**: v1.0
- **认证方式**: 暂无（开发环境）
- **数据格式**: JSON
- **字符编码**: UTF-8

---

## 🚀 快速开始

### 启动API服务
```bash
python src/api/model_library_api.py
```

### 访问API文档
- **Swagger UI**: http://localhost:8888/docs
- **ReDoc**: http://localhost:8888/redoc

### 健康检查
```bash
curl -X GET "http://localhost:8888/health"
```

---

## 📋 API端点列表

### 🏠 系统管理

#### GET /
获取API根信息
```json
{
  "message": "福彩3D预测系统模型库API",
  "version": "1.0.0",
  "status": "running",
  "timestamp": "2025-07-19T10:30:00"
}
```

#### GET /health
健康检查
```json
{
  "status": "healthy",
  "timestamp": "2025-07-19T10:30:00",
  "services": {
    "feature_ranking": "active",
    "quality_engine": "active",
    "websocket_monitor": "active",
    "ab_testing": "active",
    "meta_learning": "active"
  }
}
```

---

### 📊 数据分析API

#### GET /api/v1/analysis/sum-distribution
获取和值分布分析

**响应**:
```json
{
  "sum_distribution": {
    "0": 1, "1": 3, "2": 6, "3": 10,
    "27": 1
  },
  "statistics": {
    "mean": 13.5,
    "std": 6.24,
    "min": 0,
    "max": 27
  }
}
```

#### GET /api/v1/analysis/sales
获取销售额分析

**响应**:
```json
{
  "sales_analysis": {
    "total_sales": **********,
    "average_sales": 147890,
    "trend": "increasing"
  }
}
```

#### GET /api/v1/analysis/trends
获取趋势分析

**响应**:
```json
{
  "trends": {
    "hot_numbers": ["1", "2", "3"],
    "cold_numbers": ["7", "8", "9"],
    "trend_direction": "stable"
  }
}
```

---

### 🔧 特征工程API

#### POST /api/features/ranking
计算特征重要性排序

**请求体**:
```json
{
  "model_id": "intelligent_fusion",
  "feature_names": ["feature_1", "feature_2", "feature_3"],
  "data_sample": [[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]]
}
```

**响应**:
```json
{
  "rankings": {
    "feature_1": 0.85,
    "feature_2": 0.72,
    "feature_3": 0.63
  },
  "algorithm_weights": {
    "mutual_info": 0.25,
    "random_forest": 0.30,
    "correlation": 0.20,
    "lstm_attention": 0.15,
    "lottery_specific": 0.10
  },
  "confidence": 0.87
}
```

#### GET /api/v1/analysis/frequency
获取频率分析

**响应**:
```json
{
  "number_frequency": {
    "0": 834, "1": 845, "2": 832, "3": 841,
    "4": 838, "5": 829, "6": 847, "7": 835,
    "8": 842, "9": 840
  },
  "position_frequency": {
    "百位": {"0": 278, "1": 282, "2": 277},
    "十位": {"0": 278, "1": 283, "2": 278},
    "个位": {"0": 278, "1": 280, "2": 277}
  }
}
```

---

### 📊 数据管理API

#### GET /api/v1/data/query
查询数据

**参数**:
- `limit`: 限制返回记录数
- `offset`: 偏移量
- `start_date`: 开始日期
- `end_date`: 结束日期

**响应**:
```json
{
  "data": [
    {
      "period": "2025186",
      "date": "2025-07-19",
      "numbers": "123",
      "sales": 147890
    }
  ],
  "total": 8343,
  "page": 1
}
```

#### GET /api/v1/data/status
获取数据状态

**响应**:
```json
{
  "status": "healthy",
  "total_records": 8343,
  "latest_update": "2025-07-19T21:30:00",
  "data_quality": "good"
}
```

#### POST /api/v1/data/refresh
刷新数据

**响应**:
```json
{
  "status": "success",
  "message": "数据刷新已启动",
  "task_id": "refresh_20250719_213000"
}
```

---

### 🎯 预测API

#### GET /api/v1/prediction/predict
获取预测结果

**响应**:
```json
{
  "predictions": {
    "next_period": "2025187",
    "recommended_numbers": ["123", "456", "789"],
    "confidence": 0.75,
    "model_used": "intelligent_fusion"
  }
}
```

#### POST /api/v1/prediction/train
训练预测模型

**请求体**:
```json
{
  "model_type": "intelligent_fusion",
  "training_params": {
    "epochs": 100,
    "batch_size": 32
  }
}
```

**响应**:
```json
{
  "status": "success",
  "message": "模型训练已启动",
  "task_id": "train_20250719_213000"
}
```

#### GET /api/v1/prediction/info
获取预测器信息

**响应**:
```json
{
  "models": {
    "intelligent_fusion": {
      "status": "ready",
      "last_trained": "2025-07-19T20:00:00",
      "accuracy": 0.82
    }
  }
}
```

---

### 📊 数据质量API

#### POST /api/data/quality
数据质量评估

**请求体**:
```json
{
  "model_id": "intelligent_fusion",
  "data_range": [0, 1000],
  "data_sample": [
    {"period": "2025001", "numbers": "123", "date": "2025-01-01"},
    {"period": "2025002", "numbers": "456", "date": "2025-01-02"}
  ]
}
```

**响应**:
```json
{
  "overall_score": 0.85,
  "completeness": 0.95,
  "consistency": 0.88,
  "accuracy": 0.82,
  "timeliness": 0.90,
  "validity": 0.87
}
```

---

### 🎯 超参数推荐API

#### POST /api/hyperparameters/recommend
获取超参数推荐

**请求体**:
```json
{
  "model_id": "intelligent_fusion",
  "optimization_target": "accuracy"
}
```

**响应**:
```json
{
  "recommended_parameters": {
    "learning_rate": 0.001,
    "batch_size": 64,
    "epochs": 100,
    "dropout_rate": 0.2
  },
  "confidence": 0.78,
  "expected_improvement": 0.05,
  "reasoning": "基于历史优化数据，推荐使用较小的学习率以获得更稳定的收敛"
}
```

#### POST /api/hyperparameters/update
更新优化历史

**请求体**:
```json
{
  "model_id": "intelligent_fusion",
  "parameters": {
    "learning_rate": 0.001,
    "batch_size": 64
  },
  "results": {
    "accuracy": 0.85,
    "loss": 0.25
  }
}
```

**响应**:
```json
{
  "status": "success",
  "message": "优化历史已更新"
}
```

---

### 🧪 A/B测试API

#### POST /api/ab-test/create
创建A/B测试

**请求体**:
```json
{
  "experiment_name": "超参数优化实验",
  "description": "测试不同学习率的效果",
  "arms": [
    {
      "arm_id": "control",
      "name": "控制组",
      "description": "基线配置",
      "configuration": {"learning_rate": 0.001}
    },
    {
      "arm_id": "treatment",
      "name": "实验组", 
      "description": "新配置",
      "configuration": {"learning_rate": 0.005}
    }
  ],
  "allocation_strategy": "thompson_sampling",
  "target_metric": "accuracy"
}
```

**响应**:
```json
{
  "experiment_id": "exp_1642678800",
  "status": "created",
  "message": "A/B测试创建成功"
}
```

#### POST /api/ab-test/{experiment_id}/start
启动A/B测试

**响应**:
```json
{
  "status": "success",
  "message": "A/B测试已启动"
}
```

#### GET /api/ab-test/{experiment_id}/status
获取A/B测试状态

**响应**:
```json
{
  "experiment_id": "exp_1642678800",
  "name": "超参数优化实验",
  "status": "running",
  "total_trials": 150,
  "arms_count": 2,
  "allocation_strategy": "thompson_sampling",
  "created_at": "2025-07-19T10:00:00",
  "started_at": "2025-07-19T10:05:00",
  "arms": {
    "control": {
      "name": "控制组",
      "total_trials": 75,
      "conversion_rate": 0.82,
      "confidence_interval": [0.78, 0.86]
    },
    "treatment": {
      "name": "实验组",
      "total_trials": 75,
      "conversion_rate": 0.85,
      "confidence_interval": [0.81, 0.89]
    }
  }
}
```

#### GET /api/ab-test/list
列出所有A/B测试

**响应**:
```json
{
  "experiments": [
    {
      "experiment_id": "exp_1642678800",
      "name": "超参数优化实验",
      "status": "running",
      "total_trials": 150,
      "arms_count": 2
    }
  ]
}
```

---

### 📈 训练监控API

#### POST /api/training/session
创建训练会话

**请求体**:
```json
{
  "model_id": "intelligent_fusion",
  "training_config": {
    "epochs": 100,
    "batch_size": 32,
    "learning_rate": 0.001
  }
}
```

**响应**:
```json
{
  "session_id": "session_1642678800",
  "status": "created",
  "message": "训练会话创建成功"
}
```

#### GET /api/training/sessions
列出训练会话

**响应**:
```json
{
  "sessions": [
    {
      "session_id": "session_1642678800",
      "model_id": "intelligent_fusion",
      "status": "running",
      "created_at": "2025-07-19T10:00:00"
    }
  ]
}
```

#### WebSocket /ws/training/{model_id}
训练监控WebSocket端点

**连接示例**:
```javascript
const ws = new WebSocket('ws://localhost:8000/ws/training/intelligent_fusion');

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('训练数据:', data);
};

// 发送心跳
ws.send(JSON.stringify({type: 'ping'}));
```

---

### 🤖 元学习API

#### POST /api/meta-learning/task
添加元学习任务

**请求体**:
```json
{
  "task_id": "task_001",
  "name": "福彩3D预测任务",
  "description": "基于历史数据的预测任务",
  "task_type": "time_series",
  "data_size": 8000,
  "feature_count": 15,
  "target_type": "categorical",
  "complexity_score": 0.7
}
```

**响应**:
```json
{
  "status": "success",
  "message": "元学习任务已添加"
}
```

#### POST /api/meta-learning/recommend
获取元学习推荐

**请求体**:
```json
{
  "task_id": "task_002",
  "name": "新预测任务",
  "task_type": "time_series",
  "data_size": 5000,
  "feature_count": 12,
  "target_type": "categorical",
  "complexity_score": 0.6
}
```

**响应**:
```json
{
  "recommendations": {
    "learning_rate": 0.002,
    "batch_size": 64,
    "epochs": 120,
    "optimizer": "adam"
  }
}
```

#### GET /api/meta-learning/insights
获取元学习洞察

**响应**:
```json
{
  "total_tasks": 5,
  "total_meta_knowledge": 3,
  "average_task_performance": 0.83,
  "task_type_distribution": {
    "time_series": 4,
    "classification": 1
  },
  "knowledge_type_distribution": {
    "hyperparameter_pattern": 2,
    "performance_pattern": 1
  }
}
```

---

### 📊 系统统计API

#### GET /api/v1/stats/basic
获取基础统计信息

**响应**:
```json
{
  "timestamp": "2025-07-19T10:30:00",
  "total_records": 8343,
  "latest_period": "2025186",
  "data_range": {
    "start_date": "2003-02-18",
    "end_date": "2025-07-19"
  },
  "system_status": "healthy"
}
```

---

## 🔧 错误处理

### 错误响应格式
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": {
      "field": "model_id",
      "issue": "字段不能为空"
    }
  }
}
```

### 常见错误码
- `400` - 请求参数错误
- `404` - 资源不存在
- `500` - 服务器内部错误
- `503` - 服务不可用

---

## 📚 使用示例

### Python客户端示例
```python
import requests
import json

# 基础配置
BASE_URL = "http://localhost:8888"
headers = {"Content-Type": "application/json"}

# 特征重要性排序
def get_feature_ranking(model_id, feature_names):
    url = f"{BASE_URL}/api/features/ranking"
    data = {
        "model_id": model_id,
        "feature_names": feature_names
    }
    
    response = requests.post(url, json=data, headers=headers)
    return response.json()

# 创建A/B测试
def create_ab_test(experiment_name, arms):
    url = f"{BASE_URL}/api/ab-test/create"
    data = {
        "experiment_name": experiment_name,
        "description": "API测试实验",
        "arms": arms,
        "allocation_strategy": "thompson_sampling"
    }
    
    response = requests.post(url, json=data, headers=headers)
    return response.json()

# 使用示例
if __name__ == "__main__":
    # 获取特征排序
    ranking = get_feature_ranking("test_model", ["feature_1", "feature_2"])
    print("特征排序:", ranking)
    
    # 创建A/B测试
    arms = [
        {"arm_id": "control", "name": "控制组", "configuration": {"lr": 0.001}},
        {"arm_id": "treatment", "name": "实验组", "configuration": {"lr": 0.005}}
    ]
    experiment = create_ab_test("API测试实验", arms)
    print("实验创建:", experiment)
```

### JavaScript客户端示例
```javascript
// 基础配置
const BASE_URL = 'http://localhost:8888';

// 获取系统健康状态
async function getHealth() {
    try {
        const response = await fetch(`${BASE_URL}/health`);
        const data = await response.json();
        console.log('系统状态:', data);
        return data;
    } catch (error) {
        console.error('请求失败:', error);
    }
}

// 获取特征分类
async function getFeatureCategories() {
    try {
        const response = await fetch(`${BASE_URL}/api/features/categories`);
        const data = await response.json();
        console.log('特征分类:', data);
        return data;
    } catch (error) {
        console.error('请求失败:', error);
    }
}

// WebSocket连接示例
function connectTrainingMonitor(modelId) {
    const ws = new WebSocket(`ws://localhost:8888/ws/training/${modelId}`);
    
    ws.onopen = function(event) {
        console.log('WebSocket连接已建立');
    };
    
    ws.onmessage = function(event) {
        const data = JSON.parse(event.data);
        console.log('训练数据更新:', data);
    };
    
    ws.onclose = function(event) {
        console.log('WebSocket连接已关闭');
    };
    
    return ws;
}
```

---

## 🔒 安全说明

### 开发环境
- 当前API为开发环境配置，未启用认证
- 允许跨域访问（CORS）
- 建议仅在本地开发环境使用

### 生产环境建议
- 启用API认证（JWT Token）
- 配置HTTPS加密传输
- 限制跨域访问
- 添加请求频率限制
- 启用API访问日志

---

*最后更新：2025年7月19日*  
*API版本：v1.0*  
*深度交互版：v2.0*
