#!/usr/bin/env python3
"""
测试Plotly API修复
"""

import pandas as pd
import plotly.express as px

def test_plotly_fix():
    """测试修复后的Plotly代码"""
    print("🧪 测试Plotly API修复...")
    
    try:
        # 创建测试数据
        test_data = [
            {'API端点': '/api/v1/test1', '平均响应时间(s)': 0.15},
            {'API端点': '/api/v1/test2', '平均响应时间(s)': 0.25},
            {'API端点': '/api/v1/test3', '平均响应时间(s)': 0.35}
        ]
        
        df = pd.DataFrame(test_data)
        
        # 创建图表（使用修复后的代码）
        fig = px.bar(
            df,
            x='API端点',
            y='平均响应时间(s)',
            title="API平均响应时间",
            color='平均响应时间(s)',
            color_continuous_scale='RdYlGn_r'
        )
        
        # 使用修复后的方法
        fig.update_layout(xaxis=dict(tickangle=45))
        
        print("✅ 修复后的代码运行正常")
        print("✅ fig.update_layout(xaxis=dict(tickangle=45)) 工作正常")
        
        # 测试错误的方法（应该会失败）
        try:
            fig.update_xaxis(tickangle=45)
            print("❌ 错误：update_xaxis方法不应该存在")
        except AttributeError as e:
            print(f"✅ 确认错误：{e}")
            print("✅ 这证明了我们的修复是正确的")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == '__main__':
    test_plotly_fix()
