#!/usr/bin/env python3
"""
intelligentFusionSystem变量作用域错误诊断脚本
重现和分析'cannot access local variable intelligentFusionSystem'错误
"""

import sys
import os
import traceback
from typing import Dict, Any, Optional

# 添加src路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_import_availability():
    """测试模块导入可用性"""
    print("=== 测试模块导入可用性 ===")
    
    import_results = {}
    
    # 测试基础模块导入
    try:
        import streamlit as st
        import_results['streamlit'] = True
        print("✓ Streamlit导入成功")
    except ImportError as e:
        import_results['streamlit'] = False
        print(f"✗ Streamlit导入失败: {e}")
    
    # 测试智能融合模块导入
    try:
        from prediction.adaptive_fusion import AdaptiveFusionSystem
        import_results['adaptive_fusion'] = True
        print("✓ AdaptiveFusionSystem导入成功")
    except ImportError as e:
        import_results['adaptive_fusion'] = False
        print(f"✗ AdaptiveFusionSystem导入失败: {e}")
    
    try:
        from prediction.intelligent_fusion import IntelligentFusionSystem
        import_results['intelligent_fusion'] = True
        print("✓ IntelligentFusionSystem导入成功")
    except ImportError as e:
        import_results['intelligent_fusion'] = False
        print(f"✗ IntelligentFusionSystem导入失败: {e}")
    
    try:
        from prediction.pattern_prediction import PatternPredictor
        import_results['pattern_prediction'] = True
        print("✓ PatternPredictor导入成功")
    except ImportError as e:
        import_results['pattern_prediction'] = False
        print(f"✗ PatternPredictor导入失败: {e}")
    
    try:
        from prediction.trend_analysis import TrendAnalyzer
        import_results['trend_analysis'] = True
        print("✓ TrendAnalyzer导入成功")
    except ImportError as e:
        import_results['trend_analysis'] = False
        print(f"✗ TrendAnalyzer导入失败: {e}")
    
    # 测试UI组件导入
    try:
        from ui.intelligent_fusion_components import show_adaptive_fusion_tab
        import_results['ui_components'] = True
        print("✓ UI组件导入成功")
    except ImportError as e:
        import_results['ui_components'] = False
        print(f"✗ UI组件导入失败: {e}")
    
    return import_results

def test_intelligent_fusion_system_initialization():
    """测试IntelligentFusionSystem初始化"""
    print("\n=== 测试IntelligentFusionSystem初始化 ===")
    
    try:
        from prediction.intelligent_fusion import IntelligentFusionSystem
        
        # 测试基本初始化
        system = IntelligentFusionSystem()
        print("✓ IntelligentFusionSystem基本初始化成功")
        
        # 测试属性访问
        if hasattr(system, 'db_path'):
            print(f"✓ db_path属性存在: {system.db_path}")
        else:
            print("✗ db_path属性不存在")
        
        if hasattr(system, 'fusion_ready'):
            print(f"✓ fusion_ready属性存在: {system.fusion_ready}")
        else:
            print("✗ fusion_ready属性不存在")
        
        return True, system
        
    except Exception as e:
        print(f"✗ IntelligentFusionSystem初始化失败: {e}")
        traceback.print_exc()
        return False, None

def simulate_variable_scope_error():
    """模拟变量作用域错误场景"""
    print("\n=== 模拟变量作用域错误场景 ===")
    
    # 场景1: 条件分支中定义变量，分支外使用
    def test_scenario_1():
        print("\n--- 场景1: 条件分支变量定义问题 ---")
        
        try:
            condition = False  # 模拟条件不满足
            
            if condition:
                # 这里定义变量，但条件不满足时不会执行
                intelligentFusionSystem = "defined"
            
            # 尝试在条件分支外使用变量
            print(f"尝试访问变量: {intelligentFusionSystem}")
            
        except UnboundLocalError as e:
            print(f"✓ 成功重现UnboundLocalError: {e}")
            return True
        except NameError as e:
            print(f"✓ 成功重现NameError: {e}")
            return True
        except Exception as e:
            print(f"✗ 意外错误: {e}")
            return False
    
    # 场景2: 导入失败导致变量未定义
    def test_scenario_2():
        print("\n--- 场景2: 导入失败导致变量未定义 ---")
        
        try:
            # 模拟导入失败的情况
            INTELLIGENT_FUSION_AVAILABLE = False
            
            if INTELLIGENT_FUSION_AVAILABLE:
                from prediction.intelligent_fusion import IntelligentFusionSystem
                temp_system = IntelligentFusionSystem()
            
            # 尝试使用可能未定义的变量
            db_path = temp_system.db_path
            print(f"数据库路径: {db_path}")
            
        except UnboundLocalError as e:
            print(f"✓ 成功重现UnboundLocalError: {e}")
            return True
        except NameError as e:
            print(f"✓ 成功重现NameError: {e}")
            return True
        except Exception as e:
            print(f"✗ 意外错误: {e}")
            return False
    
    # 场景3: 异常处理中的变量作用域问题
    def test_scenario_3():
        print("\n--- 场景3: 异常处理中的变量作用域问题 ---")
        
        try:
            try:
                # 模拟可能失败的操作
                raise ImportError("模拟导入失败")
                intelligentFusionSystem = "should_not_reach"
            except ImportError:
                print("捕获到导入错误，但变量未定义")
            
            # 尝试在异常处理后使用变量
            result = intelligentFusionSystem.some_method()
            
        except UnboundLocalError as e:
            print(f"✓ 成功重现UnboundLocalError: {e}")
            return True
        except NameError as e:
            print(f"✓ 成功重现NameError: {e}")
            return True
        except Exception as e:
            print(f"✗ 意外错误: {e}")
            return False
    
    # 执行所有场景测试
    results = []
    results.append(test_scenario_1())
    results.append(test_scenario_2())
    results.append(test_scenario_3())
    
    return results

def analyze_ui_components_code():
    """分析UI组件代码中的潜在问题"""
    print("\n=== 分析UI组件代码中的潜在问题 ===")
    
    ui_file_path = "src/ui/intelligent_fusion_components.py"
    
    if not os.path.exists(ui_file_path):
        print(f"✗ UI组件文件不存在: {ui_file_path}")
        return False
    
    try:
        with open(ui_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 搜索可能的问题模式
        issues = []
        
        # 检查变量定义模式
        if 'temp_system = IntelligentFusionSystem()' in content:
            print("✓ 发现temp_system变量定义")
        
        if 'intelligentFusionSystem' in content:
            print("✓ 发现intelligentFusionSystem变量引用")
            issues.append("发现intelligentFusionSystem变量引用")
        
        # 检查条件分支模式
        if_count = content.count('if ')
        elif_count = content.count('elif ')
        else_count = content.count('else:')
        
        print(f"✓ 条件分支统计: if({if_count}), elif({elif_count}), else({else_count})")
        
        # 检查try-except模式
        try_count = content.count('try:')
        except_count = content.count('except ')
        
        print(f"✓ 异常处理统计: try({try_count}), except({except_count})")
        
        return True, issues
        
    except Exception as e:
        print(f"✗ 分析UI组件代码失败: {e}")
        return False, []

def main():
    """主诊断函数"""
    print("🔍 开始intelligentFusionSystem变量作用域错误诊断...")
    print("="*70)
    
    # 1. 测试导入可用性
    import_results = test_import_availability()
    
    # 2. 测试IntelligentFusionSystem初始化
    init_success, system = test_intelligent_fusion_system_initialization()
    
    # 3. 模拟变量作用域错误
    scope_error_results = simulate_variable_scope_error()
    
    # 4. 分析UI组件代码
    ui_analysis_success, ui_issues = analyze_ui_components_code()
    
    # 汇总诊断结果
    print("\n" + "="*70)
    print("🎯 诊断结果汇总:")
    print("="*70)
    
    print(f"模块导入状态:")
    for module, status in import_results.items():
        status_icon = "✓" if status else "✗"
        print(f"  {status_icon} {module}: {'可用' if status else '不可用'}")
    
    print(f"\nIntelligentFusionSystem初始化: {'✓ 成功' if init_success else '✗ 失败'}")
    
    print(f"\n变量作用域错误重现:")
    for i, result in enumerate(scope_error_results, 1):
        status_icon = "✓" if result else "✗"
        print(f"  {status_icon} 场景{i}: {'成功重现' if result else '未能重现'}")
    
    print(f"\nUI组件代码分析: {'✓ 成功' if ui_analysis_success else '✗ 失败'}")
    if ui_issues:
        print("  发现的问题:")
        for issue in ui_issues:
            print(f"    • {issue}")
    
    # 诊断结论
    print(f"\n📊 诊断结论:")
    if any(scope_error_results):
        print("✓ 成功重现了变量作用域错误，确认问题存在")
    else:
        print("⚠ 未能重现变量作用域错误，可能需要特定条件")
    
    if not import_results.get('intelligent_fusion', False):
        print("⚠ IntelligentFusionSystem导入失败，这可能是错误的根本原因")
    
    print("\n🔧 建议的修复方向:")
    print("1. 在函数开始处初始化所有关键变量")
    print("2. 添加变量存在性检查")
    print("3. 改进导入失败的处理机制")
    print("4. 统一异常处理模式")

if __name__ == "__main__":
    main()
