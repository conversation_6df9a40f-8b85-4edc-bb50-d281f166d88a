"""
形态转换预测系统
实现组三/组六转换、奇偶比、大小比等形态转换预测
"""

import itertools
import os
import sqlite3
from collections import Counter, defaultdict, deque
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd


class PatternPredictor:
    """形态转换预测器"""
    
    def __init__(self, db_path: str = None, pattern_window: int = 50):
        """
        初始化形态预测器
        
        Args:
            db_path: 数据库路径
            pattern_window: 模式分析窗口大小
        """
        self.db_path = db_path or os.path.join('data', 'lottery.db')
        self.pattern_window = pattern_window
        self.pattern_models = {}
        
    def load_pattern_data(self, limit: int = 1000) -> List[Dict[str, Any]]:
        """
        加载用于模式分析的数据
        
        Args:
            limit: 加载的记录数量
            
        Returns:
            开奖记录列表
        """
        if not os.path.exists(self.db_path):
            raise FileNotFoundError(f"数据库文件不存在: {self.db_path}")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT period, date, numbers
            FROM lottery_records
            WHERE numbers IS NOT NULL
            AND numbers != ''
            AND LENGTH(numbers) = 3
            ORDER BY date DESC, period DESC
            LIMIT ?
        """, (limit,))
        
        records = []
        for row in cursor.fetchall():
            record = {
                'period': row[0],
                'date': row[1],
                'numbers': row[2]
            }
            records.append(record)
        
        conn.close()
        return records
    
    def extract_pattern_features(self, numbers: str) -> Dict[str, Any]:
        """
        提取号码的形态特征
        
        Args:
            numbers: 三位数号码
            
        Returns:
            形态特征字典
        """
        if len(numbers) != 3:
            return {}
        
        digits = [int(d) for d in numbers]
        
        features = {
            # 基础特征
            'sum_value': sum(digits),
            'span_value': max(digits) - min(digits),
            'unique_count': len(set(digits)),
            
            # 形态特征
            'form_type': self._get_form_type(digits),
            'parity_pattern': self._get_parity_pattern(digits),
            'size_pattern': self._get_size_pattern(digits),
            'position_pattern': self._get_position_pattern(digits),
            
            # 数字分布特征
            'digit_distribution': self._get_digit_distribution(digits),
            'consecutive_count': self._get_consecutive_count(digits),
            'repeat_pattern': self._get_repeat_pattern(digits),
            
            # 高级特征
            'symmetry_score': self._calculate_symmetry(digits),
            'complexity_score': self._calculate_complexity(digits),
            'balance_score': self._calculate_balance(digits)
        }
        
        return features
    
    def _get_form_type(self, digits: List[int]) -> str:
        """获取形态类型"""
        unique_count = len(set(digits))
        if unique_count == 1:
            return 'triple'  # 豹子
        elif unique_count == 2:
            return 'group3'  # 组三
        else:
            return 'group6'  # 组六
    
    def _get_parity_pattern(self, digits: List[int]) -> str:
        """获取奇偶模式"""
        odd_count = sum(1 for d in digits if d % 2 == 1)
        patterns = {3: '3odd', 2: '2odd1even', 1: '1odd2even', 0: '3even'}
        return patterns[odd_count]
    
    def _get_size_pattern(self, digits: List[int]) -> str:
        """获取大小模式"""
        big_count = sum(1 for d in digits if d >= 5)
        patterns = {3: '3big', 2: '2big1small', 1: '1big2small', 0: '3small'}
        return patterns[big_count]
    
    def _get_position_pattern(self, digits: List[int]) -> str:
        """获取位置关系模式"""
        h, t, u = digits
        if h > t > u:
            return 'descending'
        elif h < t < u:
            return 'ascending'
        elif h == t == u:
            return 'equal'
        elif h > t and t < u:
            return 'valley'
        elif h < t and t > u:
            return 'peak'
        else:
            return 'irregular'
    
    def _get_digit_distribution(self, digits: List[int]) -> Dict[str, int]:
        """获取数字分布"""
        ranges = {
            'range_0_2': sum(1 for d in digits if 0 <= d <= 2),
            'range_3_4': sum(1 for d in digits if 3 <= d <= 4),
            'range_5_6': sum(1 for d in digits if 5 <= d <= 6),
            'range_7_9': sum(1 for d in digits if 7 <= d <= 9)
        }
        return ranges
    
    def _get_consecutive_count(self, digits: List[int]) -> int:
        """获取连续数字个数"""
        sorted_digits = sorted(digits)
        consecutive = 0
        for i in range(len(sorted_digits) - 1):
            if sorted_digits[i+1] - sorted_digits[i] == 1:
                consecutive += 1
        return consecutive
    
    def _get_repeat_pattern(self, digits: List[int]) -> str:
        """获取重复模式"""
        counts = Counter(digits)
        max_count = max(counts.values())
        if max_count == 3:
            return 'triple'
        elif max_count == 2:
            return 'pair'
        else:
            return 'unique'
    
    def _calculate_symmetry(self, digits: List[int]) -> float:
        """计算对称性分数"""
        h, t, u = digits
        # 检查是否关于中位对称
        if h == u:
            return 1.0
        else:
            return 1.0 - abs(h - u) / 9.0
    
    def _calculate_complexity(self, digits: List[int]) -> float:
        """计算复杂度分数"""
        # 基于数字的分散程度
        mean_val = np.mean(digits)
        variance = np.var(digits)
        return min(variance / 10.0, 1.0)
    
    def _calculate_balance(self, digits: List[int]) -> float:
        """计算平衡性分数"""
        # 基于奇偶和大小的平衡
        odd_count = sum(1 for d in digits if d % 2 == 1)
        big_count = sum(1 for d in digits if d >= 5)
        
        odd_balance = 1.0 - abs(odd_count - 1.5) / 1.5
        size_balance = 1.0 - abs(big_count - 1.5) / 1.5
        
        return (odd_balance + size_balance) / 2.0
    
    def analyze_pattern_transitions(self, records: List[Dict]) -> Dict[str, Any]:
        """
        分析形态转换规律
        
        Args:
            records: 开奖记录列表
            
        Returns:
            形态转换分析结果
        """
        if len(records) < 2:
            return {}
        
        transitions = {
            'form_transitions': defaultdict(Counter),
            'parity_transitions': defaultdict(Counter),
            'size_transitions': defaultdict(Counter),
            'sum_transitions': defaultdict(list),
            'span_transitions': defaultdict(list),
            'transition_probabilities': {}
        }
        
        # 提取所有记录的特征
        features_sequence = []
        for record in records:
            features = self.extract_pattern_features(record['numbers'])
            features_sequence.append(features)
        
        # 分析转换模式
        for i in range(len(features_sequence) - 1):
            current = features_sequence[i]
            next_features = features_sequence[i + 1]
            
            # 形态转换
            current_form = current['form_type']
            next_form = next_features['form_type']
            transitions['form_transitions'][current_form][next_form] += 1
            
            # 奇偶转换
            current_parity = current['parity_pattern']
            next_parity = next_features['parity_pattern']
            transitions['parity_transitions'][current_parity][next_parity] += 1
            
            # 大小转换
            current_size = current['size_pattern']
            next_size = next_features['size_pattern']
            transitions['size_transitions'][current_size][next_size] += 1
            
            # 和值转换
            current_sum = current['sum_value']
            next_sum = next_features['sum_value']
            transitions['sum_transitions'][current_sum].append(next_sum)
            
            # 跨度转换
            current_span = current['span_value']
            next_span = next_features['span_value']
            transitions['span_transitions'][current_span].append(next_span)
        
        # 计算转换概率
        for transition_type in ['form_transitions', 'parity_transitions', 'size_transitions']:
            transitions['transition_probabilities'][transition_type] = {}
            
            for current_state, next_states in transitions[transition_type].items():
                total = sum(next_states.values())
                if total > 0:
                    probabilities = {next_state: count / total 
                                   for next_state, count in next_states.items()}
                    transitions['transition_probabilities'][transition_type][current_state] = probabilities
        
        return transitions
    
    def detect_pattern_cycles(self, records: List[Dict]) -> Dict[str, Any]:
        """
        检测形态周期性
        
        Args:
            records: 开奖记录列表
            
        Returns:
            周期性检测结果
        """
        cycles = {
            'form_cycles': {},
            'parity_cycles': {},
            'size_cycles': {},
            'sum_cycles': {},
            'detected_periods': []
        }
        
        if len(records) < 20:
            return cycles
        
        # 提取特征序列
        form_sequence = []
        parity_sequence = []
        size_sequence = []
        sum_sequence = []
        
        for record in records:
            features = self.extract_pattern_features(record['numbers'])
            form_sequence.append(features['form_type'])
            parity_sequence.append(features['parity_pattern'])
            size_sequence.append(features['size_pattern'])
            sum_sequence.append(features['sum_value'])
        
        # 检测周期性
        sequences = {
            'form_cycles': form_sequence,
            'parity_cycles': parity_sequence,
            'size_cycles': size_sequence,
            'sum_cycles': sum_sequence
        }
        
        for seq_name, sequence in sequences.items():
            cycles[seq_name] = self._detect_sequence_cycles(sequence)
        
        return cycles
    
    def _detect_sequence_cycles(self, sequence: List) -> Dict[str, Any]:
        """
        检测序列的周期性
        
        Args:
            sequence: 序列数据
            
        Returns:
            周期性检测结果
        """
        cycle_info = {
            'detected_cycles': [],
            'cycle_strength': 0,
            'dominant_period': None
        }
        
        if len(sequence) < 10:
            return cycle_info
        
        # 检测可能的周期长度（2到序列长度的一半）
        max_period = min(20, len(sequence) // 2)
        cycle_scores = {}
        
        for period in range(2, max_period + 1):
            matches = 0
            total_comparisons = 0
            
            for i in range(len(sequence) - period):
                if sequence[i] == sequence[i + period]:
                    matches += 1
                total_comparisons += 1
            
            if total_comparisons > 0:
                cycle_score = matches / total_comparisons
                cycle_scores[period] = cycle_score
                
                if cycle_score > 0.3:  # 阈值
                    cycle_info['detected_cycles'].append({
                        'period': period,
                        'strength': cycle_score,
                        'matches': matches,
                        'total': total_comparisons
                    })
        
        if cycle_scores:
            best_period = max(cycle_scores.items(), key=lambda x: x[1])
            cycle_info['dominant_period'] = best_period[0]
            cycle_info['cycle_strength'] = best_period[1]
        
        return cycle_info

    def _calculate_pattern_data_sensitivity(self, records: List[Dict]) -> float:
        """
        计算形态数据敏感性因子

        Args:
            records: 开奖记录列表

        Returns:
            数据敏感性因子 (0.8-1.2)
        """
        import random
        import time

        if len(records) < 5:
            return 1.0

        # 分析最近5期的形态变化
        recent_forms = []
        for record in records[:5]:
            if 'numbers' in record and record['numbers']:
                features = self.extract_pattern_features(record['numbers'])
                recent_forms.append(features['form_type'])

        # 计算形态多样性
        form_diversity = len(set(recent_forms)) / len(recent_forms) if recent_forms else 0.5

        # 基于时间和形态多样性计算敏感性
        time_factor = (int(time.time()) % 100) / 100.0
        random_factor = random.random() * 0.2

        # 组合敏感性因子
        sensitivity = 0.8 + (form_diversity * 0.2) + (time_factor * 0.1) + random_factor
        return min(max(sensitivity, 0.8), 1.2)

    def predict_next_patterns(self, records: List[Dict]) -> Dict[str, Any]:
        """
        预测下期形态
        
        Args:
            records: 开奖记录列表
            
        Returns:
            形态预测结果
        """
        if len(records) < 10:
            return {}
        
        # 获取转换分析和周期检测结果
        transitions = self.analyze_pattern_transitions(records)
        cycles = self.detect_pattern_cycles(records)
        
        # 获取最近一期的特征
        latest_features = self.extract_pattern_features(records[-1]['numbers'])

        # 计算数据敏感性因子
        data_sensitivity = self._calculate_pattern_data_sensitivity(records)

        predictions = {
            'form_prediction': {},
            'parity_prediction': {},
            'size_prediction': {},
            'sum_prediction': {},
            'span_prediction': {},
            'confidence_scores': {},
            'data_sensitivity_factor': data_sensitivity
        }
        
        # 基于转换概率预测形态
        if 'form_transitions' in transitions['transition_probabilities']:
            current_form = latest_features['form_type']
            if current_form in transitions['transition_probabilities']['form_transitions']:
                form_probs = transitions['transition_probabilities']['form_transitions'][current_form]
                predictions['form_prediction'] = {
                    'probabilities': form_probs,
                    'most_likely': max(form_probs.items(), key=lambda x: x[1]) if form_probs else None
                }
        
        # 基于转换概率预测奇偶
        if 'parity_transitions' in transitions['transition_probabilities']:
            current_parity = latest_features['parity_pattern']
            if current_parity in transitions['transition_probabilities']['parity_transitions']:
                parity_probs = transitions['transition_probabilities']['parity_transitions'][current_parity]
                predictions['parity_prediction'] = {
                    'probabilities': parity_probs,
                    'most_likely': max(parity_probs.items(), key=lambda x: x[1]) if parity_probs else None
                }
        
        # 基于转换概率预测大小
        if 'size_transitions' in transitions['transition_probabilities']:
            current_size = latest_features['size_pattern']
            if current_size in transitions['transition_probabilities']['size_transitions']:
                size_probs = transitions['transition_probabilities']['size_transitions'][current_size]
                predictions['size_prediction'] = {
                    'probabilities': size_probs,
                    'most_likely': max(size_probs.items(), key=lambda x: x[1]) if size_probs else None
                }
        
        # 预测和值范围
        current_sum = latest_features['sum_value']
        if current_sum in transitions['sum_transitions']:
            next_sums = transitions['sum_transitions'][current_sum]
            if next_sums:
                predictions['sum_prediction'] = {
                    'mean': np.mean(next_sums),
                    'std': np.std(next_sums),
                    'range': (min(next_sums), max(next_sums)),
                    'most_common': Counter(next_sums).most_common(3)
                }
        
        # 预测跨度范围
        current_span = latest_features['span_value']
        if current_span in transitions['span_transitions']:
            next_spans = transitions['span_transitions'][current_span]
            if next_spans:
                predictions['span_prediction'] = {
                    'mean': np.mean(next_spans),
                    'std': np.std(next_spans),
                    'range': (min(next_spans), max(next_spans)),
                    'most_common': Counter(next_spans).most_common(3)
                }
        
        # 计算置信度
        predictions['confidence_scores'] = {
            'form_confidence': len(predictions['form_prediction'].get('probabilities', {})) / 3,
            'parity_confidence': len(predictions['parity_prediction'].get('probabilities', {})) / 4,
            'size_confidence': len(predictions['size_prediction'].get('probabilities', {})) / 4,
            'cycle_confidence': max([c['cycle_strength'] for c in cycles.values() if isinstance(c, dict) and 'cycle_strength' in c] + [0])
        }
        
        return predictions
    
    def generate_candidate_numbers(self, predictions: Dict[str, Any], top_k: int = 20) -> List[Dict[str, Any]]:
        """
        基于形态预测生成候选号码
        
        Args:
            predictions: 形态预测结果
            top_k: 返回的候选号码数量
            
        Returns:
            候选号码列表
        """
        candidates = []
        
        # 获取预测的形态约束
        target_form = predictions.get('form_prediction', {}).get('most_likely', [None, 0])[0]
        target_parity = predictions.get('parity_prediction', {}).get('most_likely', [None, 0])[0]
        target_size = predictions.get('size_prediction', {}).get('most_likely', [None, 0])[0]
        
        sum_range = predictions.get('sum_prediction', {}).get('range', (0, 27))
        span_range = predictions.get('span_prediction', {}).get('range', (0, 9))
        
        # 生成所有可能的三位数组合
        for h in range(10):
            for t in range(10):
                for u in range(10):
                    numbers = f"{h}{t}{u}"
                    features = self.extract_pattern_features(numbers)
                    
                    # 检查是否符合预测的形态约束
                    score = 0
                    match_count = 0
                    
                    if target_form and features['form_type'] == target_form:
                        score += 0.3
                        match_count += 1
                    
                    if target_parity and features['parity_pattern'] == target_parity:
                        score += 0.25
                        match_count += 1
                    
                    if target_size and features['size_pattern'] == target_size:
                        score += 0.25
                        match_count += 1
                    
                    if sum_range[0] <= features['sum_value'] <= sum_range[1]:
                        score += 0.1
                        match_count += 1
                    
                    if span_range[0] <= features['span_value'] <= span_range[1]:
                        score += 0.1
                        match_count += 1
                    
                    # 只保留至少匹配2个条件的候选
                    if match_count >= 2:
                        candidates.append({
                            'numbers': numbers,
                            'score': score,
                            'match_count': match_count,
                            'features': features
                        })
        
        # 按分数排序并返回前top_k个
        candidates.sort(key=lambda x: (x['score'], x['match_count']), reverse=True)
        return candidates[:top_k]
    
    def train_model(self) -> Dict[str, Any]:
        """
        训练形态转换预测模型
        
        Returns:
            训练结果和模型性能
        """
        print("开始训练形态转换预测模型...")
        
        # 加载数据
        records = self.load_pattern_data(limit=1000)
        print(f"加载了 {len(records)} 条开奖记录")
        
        if len(records) < self.pattern_window:
            raise ValueError(f"数据不足，至少需要 {self.pattern_window} 条记录")
        
        # 执行形态分析
        print("分析形态转换规律...")
        transitions = self.analyze_pattern_transitions(records)
        
        print("检测形态周期性...")
        cycles = self.detect_pattern_cycles(records)
        
        print("生成形态预测...")
        predictions = self.predict_next_patterns(records)
        
        # 保存模型
        self.pattern_models = {
            'transitions': transitions,
            'cycles': cycles,
            'predictions': predictions,
            'data_summary': {
                'total_records': len(records),
                'pattern_window': self.pattern_window,
                'date_range': (records[0]['date'], records[-1]['date']) if records else None
            }
        }
        
        print("形态转换预测模型训练完成!")
        return {
            'success': True,
            'pattern_models': self.pattern_models
        }


if __name__ == "__main__":
    # 测试代码
    predictor = PatternPredictor(pattern_window=50)
    
    try:
        # 训练模型
        result = predictor.train_model()
        print("训练结果:", result['success'])
        
        if result['success']:
            # 显示一些预测信息
            models = result['pattern_models']
            
            print("\n形态转换概率摘要:")
            if 'form_transitions' in models['transitions']['transition_probabilities']:
                for current, probs in models['transitions']['transition_probabilities']['form_transitions'].items():
                    print(f"  {current} -> {max(probs.items(), key=lambda x: x[1])}")
            
            print("\n周期性检测摘要:")
            for cycle_type, cycle_info in models['cycles'].items():
                if isinstance(cycle_info, dict) and 'dominant_period' in cycle_info:
                    if cycle_info['dominant_period']:
                        print(f"  {cycle_type}: 周期{cycle_info['dominant_period']} (强度{cycle_info['cycle_strength']:.3f})")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
