#!/usr/bin/env python3
"""
增强的状态显示组件
"""

import streamlit as st
import requests
import sqlite3
import json
from datetime import datetime
from pathlib import Path

def get_database_status():
    """直接从数据库获取状态"""
    try:
        db_path = "data/lottery.db"
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 获取记录数
            cursor.execute("SELECT COUNT(*) FROM lottery_records")
            count = cursor.fetchone()[0]
            
            # 获取最新记录
            cursor.execute("SELECT period, date, numbers FROM lottery_records ORDER BY period DESC LIMIT 3")
            latest_records = cursor.fetchall()
            
            # 获取日期范围
            cursor.execute("SELECT MIN(date), MAX(date) FROM lottery_records")
            date_range = cursor.fetchone()
            
            return {
                "success": True,
                "count": count,
                "latest_records": latest_records,
                "date_range": date_range
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

def get_metadata_status():
    """获取元数据状态"""
    try:
        metadata_path = "data/processed/update_metadata.json"
        if not Path(metadata_path).exists():
            return {
                "success": False,
                "error": "元数据文件不存在"
            }
        
        with open(metadata_path, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
        
        return {
            "success": True,
            "last_update_time": metadata.get("last_update_time"),
            "last_record_count": metadata.get("last_record_count"),
            "last_period": metadata.get("last_period"),
            "last_date": metadata.get("last_date"),
            "update_history_count": len(metadata.get("update_history", []))
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

def check_data_consistency():
    """检查数据一致性"""
    db_status = get_database_status()
    metadata_status = get_metadata_status()
    
    if not db_status["success"] or not metadata_status["success"]:
        return {
            "success": False,
            "error": "无法获取状态信息"
        }
    
    # 比较关键指标
    inconsistencies = []
    
    # 比较记录数
    if db_status["count"] != metadata_status["last_record_count"]:
        inconsistencies.append({
            "field": "记录数",
            "database": db_status["count"],
            "metadata": metadata_status["last_record_count"]
        })
    
    # 比较最新期号
    if db_status["latest_records"]:
        db_latest_period = db_status["latest_records"][0][0]
        if db_latest_period != metadata_status["last_period"]:
            inconsistencies.append({
                "field": "最新期号",
                "database": db_latest_period,
                "metadata": metadata_status["last_period"]
            })
    
    return {
        "success": True,
        "is_consistent": len(inconsistencies) == 0,
        "inconsistencies": inconsistencies
    }

def show_enhanced_status():
    """显示增强的状态信息"""
    st.markdown("### 📊 系统状态监控")
    
    # 创建三列布局
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("#### 🗄️ 数据库状态")
        
        db_status = get_database_status()
        if db_status["success"]:
            st.success("✅ 数据库连接正常")
            st.metric("总记录数", f"{db_status['count']:,}")
            
            if db_status["latest_records"]:
                latest = db_status["latest_records"][0]
                st.metric("最新期号", latest[0])
                st.metric("最新日期", latest[1])
                st.metric("最新号码", latest[2])
            
            if db_status["date_range"]:
                st.text(f"数据范围: {db_status['date_range'][0]} 到 {db_status['date_range'][1]}")
        else:
            st.error(f"❌ 数据库连接失败: {db_status['error']}")
    
    with col2:
        st.markdown("#### 📄 元数据状态")
        
        metadata_status = get_metadata_status()
        if metadata_status["success"]:
            st.success("✅ 元数据正常")
            st.metric("元数据记录数", f"{metadata_status['last_record_count']:,}")
            st.metric("元数据最新期号", metadata_status["last_period"])
            st.metric("元数据最新日期", metadata_status["last_date"])
            
            if metadata_status["last_update_time"]:
                update_time = datetime.fromisoformat(metadata_status["last_update_time"])
                st.text(f"最后更新: {update_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            st.text(f"更新历史: {metadata_status['update_history_count']} 条记录")
        else:
            st.error(f"❌ 元数据读取失败: {metadata_status['error']}")
    
    with col3:
        st.markdown("#### 🔍 一致性检查")
        
        consistency = check_data_consistency()
        if consistency["success"]:
            if consistency["is_consistent"]:
                st.success("✅ 数据一致")
                st.info("数据库与元数据状态一致")
            else:
                st.warning("⚠️ 发现不一致")
                st.error(f"发现 {len(consistency['inconsistencies'])} 个不一致项")
                
                # 显示不一致详情
                for item in consistency["inconsistencies"]:
                    st.text(f"• {item['field']}:")
                    st.text(f"  数据库: {item['database']}")
                    st.text(f"  元数据: {item['metadata']}")
        else:
            st.error(f"❌ 一致性检查失败: {consistency['error']}")
    
    # 添加刷新按钮
    st.markdown("---")
    col1, col2, col3 = st.columns([1, 1, 1])
    
    with col1:
        if st.button("🔄 刷新状态", use_container_width=True):
            st.rerun()
    
    with col2:
        if st.button("🔧 修复不一致", use_container_width=True):
            fix_inconsistency()
    
    with col3:
        if st.button("📋 详细报告", use_container_width=True):
            show_detailed_report()

def fix_inconsistency():
    """修复数据不一致"""
    with st.spinner("正在修复数据不一致..."):
        try:
            # 调用一致性检查器的自动修复功能
            import sys
            from pathlib import Path
            sys.path.append(str(Path(__file__).parent.parent))
            
            from data.consistency_checker import ConsistencyChecker
            
            checker = ConsistencyChecker()
            result = checker.auto_repair()
            
            if result["success"]:
                st.success("✅ 数据不一致修复成功！")
                st.info("元数据已更新以匹配数据库状态")
                st.rerun()
            else:
                st.error(f"❌ 修复失败: {result['message']}")
                
        except Exception as e:
            st.error(f"❌ 修复过程中发生异常: {str(e)}")

def show_detailed_report():
    """显示详细报告"""
    st.markdown("### 📋 详细状态报告")
    
    # 获取所有状态信息
    db_status = get_database_status()
    metadata_status = get_metadata_status()
    consistency = check_data_consistency()
    
    # 显示详细信息
    with st.expander("🗄️ 数据库详细信息", expanded=True):
        if db_status["success"]:
            st.json({
                "记录总数": db_status["count"],
                "最新记录": db_status["latest_records"][:3] if db_status["latest_records"] else [],
                "数据范围": db_status["date_range"]
            })
        else:
            st.error(db_status["error"])
    
    with st.expander("📄 元数据详细信息", expanded=True):
        if metadata_status["success"]:
            st.json({
                "最后更新时间": metadata_status["last_update_time"],
                "记录数": metadata_status["last_record_count"],
                "最新期号": metadata_status["last_period"],
                "最新日期": metadata_status["last_date"],
                "更新历史数量": metadata_status["update_history_count"]
            })
        else:
            st.error(metadata_status["error"])
    
    with st.expander("🔍 一致性检查详情", expanded=True):
        if consistency["success"]:
            st.json({
                "是否一致": consistency["is_consistent"],
                "不一致项": consistency["inconsistencies"]
            })
        else:
            st.error(consistency["error"])
