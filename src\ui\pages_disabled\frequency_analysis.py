import streamlit as st
import requests
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np

def show_frequency_analysis():
    """频率分析页面 - 基于真实历史数据"""
    st.header("🔢 频率分析")
    
    # 获取真实的频率分析数据
    try:
        api_url = "http://127.0.0.1:8888/api/v1/analysis/frequency"
        
        with st.spinner("正在分析频率数据..."):
            response = requests.get(api_url, timeout=15)
            response.raise_for_status()
            frequency_data = response.json()
        
        if frequency_data.get('success'):
            # 显示数据概览
            st.subheader("📊 数据概览")
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                total_records = frequency_data.get('total_records', 0)
                st.metric("总记录数", f"{total_records:,}")
            with col2:
                date_range = frequency_data.get('date_range', 'N/A')
                st.metric("分析期间", date_range)
            with col3:
                latest_period = frequency_data.get('latest_period', 'N/A')
                st.metric("最新期号", latest_period)
            with col4:
                analysis_time = frequency_data.get('analysis_time', 'N/A')
                st.metric("分析时间", analysis_time)
            
            # 数字频率统计
            st.subheader("🔢 各数字出现频率")
            
            digit_freq = frequency_data.get('digit_frequency', {})
            
            if digit_freq:
                # 创建频率分布图
                digits = list(range(10))
                frequencies = [digit_freq.get(str(i), 0) for i in digits]
                
                # 计算期望频率（理论上每个数字应该出现的次数）
                expected_freq = total_records * 3 / 10  # 每期3个数字，10个可能的数字
                
                fig_bar = px.bar(
                    x=digits,
                    y=frequencies,
                    title=f"各数字出现频率分布（基于{total_records:,}条真实历史数据）",
                    labels={'x': '数字', 'y': '出现次数'},
                    color=frequencies,
                    color_continuous_scale='viridis'
                )
                
                # 添加期望频率线
                fig_bar.add_hline(
                    y=expected_freq, 
                    line_dash="dash", 
                    line_color="red",
                    annotation_text=f"期望频率: {expected_freq:.0f}"
                )
                
                fig_bar.update_layout(showlegend=False, height=500)
                st.plotly_chart(fig_bar, use_container_width=True)
                
                # 频率统计表
                freq_df = pd.DataFrame({
                    '数字': digits,
                    '出现次数': frequencies,
                    '出现频率': [f"{(freq/total_records/3*100):.2f}%" for freq in frequencies],
                    '期望次数': [f"{expected_freq:.0f}"] * 10,
                    '偏差': [f"{freq - expected_freq:+.0f}" for freq in frequencies]
                })
                
                st.dataframe(
                    freq_df, 
                    hide_index=True, 
                    use_container_width=True,
                    column_config={
                        "数字": st.column_config.NumberColumn("数字", width="small"),
                        "出现次数": st.column_config.NumberColumn("出现次数", width="medium"),
                        "出现频率": st.column_config.TextColumn("出现频率", width="medium"),
                        "期望次数": st.column_config.TextColumn("期望次数", width="medium"),
                        "偏差": st.column_config.TextColumn("偏差", width="medium")
                    }
                )
            
            # 热号冷号分析
            st.subheader("🔥❄️ 热号冷号分析")
            
            hot_numbers = frequency_data.get('hot_numbers', [])
            cold_numbers = frequency_data.get('cold_numbers', [])
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.write("**🔥 热号 (出现频率最高)**")
                if hot_numbers:
                    for i, (num, freq) in enumerate(hot_numbers[:5], 1):
                        percentage = (freq / total_records / 3) * 100
                        st.write(f"{i}. 数字 **{num}**: {freq} 次 ({percentage:.2f}%)")
                else:
                    st.info("暂无热号数据")
            
            with col2:
                st.write("**❄️ 冷号 (出现频率最低)**")
                if cold_numbers:
                    for i, (num, freq) in enumerate(cold_numbers[:5], 1):
                        percentage = (freq / total_records / 3) * 100
                        st.write(f"{i}. 数字 **{num}**: {freq} 次 ({percentage:.2f}%)")
                else:
                    st.info("暂无冷号数据")
            
            # 位置频率分析
            if 'position_frequency' in frequency_data:
                st.subheader("📍 位置频率分析")
                
                position_freq = frequency_data['position_frequency']
                
                # 创建位置频率热力图数据
                position_data = []
                positions = ['百位', '十位', '个位']
                
                for pos in positions:
                    if pos in position_freq:
                        for digit in range(10):
                            freq = position_freq[pos].get(str(digit), 0)
                            position_data.append({
                                '位置': pos,
                                '数字': str(digit),
                                '频率': freq
                            })
                
                if position_data:
                    position_df = pd.DataFrame(position_data)
                    pivot_df = position_df.pivot(index='数字', columns='位置', values='频率')
                    
                    fig_heatmap = px.imshow(
                        pivot_df.T,
                        title="各位置数字出现频率热力图",
                        labels={'x': '数字', 'y': '位置', 'color': '出现次数'},
                        color_continuous_scale='RdYlBu_r',
                        aspect="auto"
                    )
                    fig_heatmap.update_layout(height=400)
                    st.plotly_chart(fig_heatmap, use_container_width=True)
                    
                    # 位置频率统计表
                    st.write("**位置频率详细统计**")
                    st.dataframe(pivot_df, use_container_width=True)
            
            # 频率趋势分析
            if 'frequency_trend' in frequency_data:
                st.subheader("📈 频率趋势分析")
                
                trend_data = frequency_data['frequency_trend']
                
                # 选择要分析的数字
                selected_digits = st.multiselect(
                    "选择要分析趋势的数字",
                    options=list(range(10)),
                    default=[0, 1, 2, 3, 4],
                    max_selections=5,
                    help="最多可选择5个数字进行趋势分析"
                )
                
                if selected_digits and trend_data:
                    fig_trend = go.Figure()
                    
                    for digit in selected_digits:
                        if str(digit) in trend_data:
                            periods = trend_data[str(digit)].get('periods', [])
                            frequencies = trend_data[str(digit)].get('frequencies', [])
                            
                            if periods and frequencies:
                                fig_trend.add_trace(go.Scatter(
                                    x=periods,
                                    y=frequencies,
                                    mode='lines+markers',
                                    name=f'数字 {digit}',
                                    line=dict(width=2),
                                    marker=dict(size=4)
                                ))
                    
                    fig_trend.update_layout(
                        title="数字出现频率趋势（滑动窗口统计）",
                        xaxis_title="期号",
                        yaxis_title="累计频率",
                        hovermode='x unified',
                        height=500
                    )
                    
                    st.plotly_chart(fig_trend, use_container_width=True)
            
            # 连号分析
            if 'consecutive_analysis' in frequency_data:
                st.subheader("🔗 连号分析")
                
                consecutive_data = frequency_data['consecutive_analysis']
                
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write("**连续出现统计**")
                    consecutive_stats = consecutive_data.get('consecutive_stats', {})
                    for digit, stats in consecutive_stats.items():
                        max_consecutive = stats.get('max_consecutive', 0)
                        avg_consecutive = stats.get('avg_consecutive', 0)
                        st.write(f"数字 {digit}: 最长连续 {max_consecutive} 期, 平均 {avg_consecutive:.1f} 期")
                
                with col2:
                    st.write("**间隔出现统计**")
                    interval_stats = consecutive_data.get('interval_stats', {})
                    for digit, stats in interval_stats.items():
                        max_interval = stats.get('max_interval', 0)
                        avg_interval = stats.get('avg_interval', 0)
                        st.write(f"数字 {digit}: 最长间隔 {max_interval} 期, 平均 {avg_interval:.1f} 期")
            
            # 统计学分析
            if 'statistical_analysis' in frequency_data:
                st.subheader("📊 统计学分析")
                
                stats = frequency_data['statistical_analysis']
                
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    chi_square = stats.get('chi_square_statistic', 0)
                    st.metric("卡方检验统计量", f"{chi_square:.4f}")
                with col2:
                    p_value = stats.get('p_value', 0)
                    st.metric("P值", f"{p_value:.6f}")
                with col3:
                    degrees_freedom = stats.get('degrees_of_freedom', 9)
                    st.metric("自由度", degrees_freedom)
                with col4:
                    significance = "显著" if p_value < 0.05 else "不显著"
                    st.metric("显著性(α=0.05)", significance)
                
                # 统计学解释
                st.write("**统计学解释:**")
                if p_value < 0.05:
                    st.warning("⚠️ 频率分布存在显著性差异，可能存在非随机模式")
                    st.write("- 某些数字的出现频率明显偏离期望值")
                    st.write("- 建议进一步分析具体的偏差模式")
                else:
                    st.success("✅ 频率分布符合随机性假设")
                    st.write("- 各数字的出现频率接近期望值")
                    st.write("- 数据符合随机分布的统计特征")
                
                # 置信区间
                if 'confidence_intervals' in stats:
                    st.write("**95%置信区间:**")
                    ci_data = stats['confidence_intervals']
                    ci_df = pd.DataFrame([
                        {
                            '数字': digit,
                            '下限': f"{ci['lower']:.0f}",
                            '上限': f"{ci['upper']:.0f}",
                            '实际值': frequencies[int(digit)],
                            '在区间内': '✅' if ci['lower'] <= frequencies[int(digit)] <= ci['upper'] else '❌'
                        }
                        for digit, ci in ci_data.items()
                    ])
                    st.dataframe(ci_df, hide_index=True, use_container_width=True)
            
            # 预测建议
            if 'prediction_suggestions' in frequency_data:
                st.subheader("💡 基于频率的预测建议")
                
                suggestions = frequency_data['prediction_suggestions']
                
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write("**推荐关注数字:**")
                    recommended = suggestions.get('recommended_numbers', [])
                    for num in recommended:
                        st.write(f"- 数字 {num}")
                
                with col2:
                    st.write("**建议回避数字:**")
                    avoid = suggestions.get('avoid_numbers', [])
                    for num in avoid:
                        st.write(f"- 数字 {num}")
                
                if 'reasoning' in suggestions:
                    st.write("**分析依据:**")
                    for reason in suggestions['reasoning']:
                        st.write(f"- {reason}")
            
        else:
            st.error("频率分析API返回错误")
            if 'message' in frequency_data:
                st.error(f"错误信息: {frequency_data['message']}")
            
    except requests.exceptions.RequestException as e:
        st.error(f"无法连接到频率分析API: {str(e)}")
        st.info("请确保API服务(127.0.0.1:8888)正在运行")
        
        # 显示连接诊断信息
        with st.expander("🔍 连接诊断信息"):
            st.write("**API端点**: http://127.0.0.1:8888/api/v1/analysis/frequency")
            st.write("**错误类型**: 连接错误")
            st.write("**可能原因**:")
            st.write("- API服务未启动")
            st.write("- 频率分析模块未加载")
            st.write("- 数据库连接问题")
            st.write("**解决建议**:")
            st.write("- 检查API服务状态")
            st.write("- 确认数据库包含足够的历史数据")
            st.write("- 重启API服务")
            
    except Exception as e:
        st.error(f"频率分析出现错误: {str(e)}")
        
        # 显示详细错误信息
        with st.expander("🔍 详细错误信息"):
            st.code(str(e))

if __name__ == "__main__":
    show_frequency_analysis()
