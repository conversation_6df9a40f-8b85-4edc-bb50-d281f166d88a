#!/usr/bin/env python3
"""
调试预测功能问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_data_loader():
    """测试数据加载器"""
    print("=== 测试数据加载器 ===")
    try:
        from src.model_library.utils.data_utils import LotteryDataLoader
        
        loader = LotteryDataLoader()
        print("✅ 数据加载器创建成功")
        
        # 测试加载数据
        records = loader.load_recent_records(10)
        print(f"✅ 成功加载 {len(records)} 条记录")
        
        if records:
            print("📊 第一条记录示例:")
            first_record = records[0]
            for key, value in first_record.items():
                print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据加载器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_registry():
    """测试模型注册中心"""
    print("\n=== 测试模型注册中心 ===")
    try:
        from src.model_library.model_registry import ModelRegistry
        
        registry = ModelRegistry()
        print("✅ 模型注册中心创建成功")
        
        # 获取模型列表
        models = registry.list_models()
        print(f"✅ 发现 {len(models)} 个已注册模型")
        
        for model in models:
            print(f"📋 模型: {model.name} ({model.model_id})")
            print(f"   类型: {model.model_type.value}")
            print(f"   状态: {'活跃' if model.is_active else '非活跃'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型注册中心测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_prediction():
    """测试模型预测"""
    print("\n=== 测试模型预测 ===")
    try:
        from src.model_library.model_registry import ModelRegistry
        from src.model_library.utils.data_utils import LotteryDataLoader
        
        registry = ModelRegistry()
        loader = LotteryDataLoader()
        
        # 获取第一个模型
        models = registry.list_models()
        if not models:
            print("❌ 没有可用的模型")
            return False
        
        model = models[0]
        print(f"🤖 测试模型: {model.name}")
        
        # 获取历史数据
        history = loader.load_recent_records(100)
        print(f"📊 加载历史数据: {len(history)} 条")
        
        # 获取模型实例
        model_instance = registry.get_model(model.model_id)
        if not model_instance:
            print(f"❌ 无法获取模型实例: {model.model_id}")
            return False
        
        print("✅ 模型实例获取成功")
        
        # 检查模型是否就绪
        try:
            is_ready = model_instance.is_ready()
            print(f"🔍 模型就绪状态: {is_ready}")
        except Exception as e:
            print(f"⚠️ 检查模型就绪状态失败: {e}")
        
        # 尝试预测
        try:
            prediction = model_instance.predict(history, 3)
            print("✅ 预测成功!")
            print(f"📈 预测结果: {prediction}")
            return True
        except Exception as e:
            print(f"❌ 预测失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 模型预测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 开始诊断预测功能问题...\n")
    
    # 测试各个组件
    data_ok = test_data_loader()
    registry_ok = test_model_registry()
    prediction_ok = test_model_prediction()
    
    print("\n" + "="*50)
    print("📊 诊断结果汇总:")
    print(f"  数据加载器: {'✅ 正常' if data_ok else '❌ 异常'}")
    print(f"  模型注册中心: {'✅ 正常' if registry_ok else '❌ 异常'}")
    print(f"  模型预测: {'✅ 正常' if prediction_ok else '❌ 异常'}")
    
    if all([data_ok, registry_ok, prediction_ok]):
        print("\n🎉 所有组件测试通过!")
    else:
        print("\n⚠️ 发现问题，需要修复")

if __name__ == "__main__":
    main()
