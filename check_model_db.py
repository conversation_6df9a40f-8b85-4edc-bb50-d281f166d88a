#!/usr/bin/env python3
"""
检查模型库数据库表结构
"""

import sqlite3
import os

def check_model_db():
    """检查模型库数据库"""
    db_path = "data/model_library.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    print(f"✅ 数据库文件存在: {db_path}")
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 查看所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"📋 数据库表: {[table[0] for table in tables]}")
            
            # 检查model_states表是否存在
            if ('model_states',) in tables:
                print("✅ model_states表存在")
                
                # 查看表结构
                cursor.execute("PRAGMA table_info(model_states)")
                columns = cursor.fetchall()
                print("📊 model_states表结构:")
                for col in columns:
                    print(f"  - {col[1]} ({col[2]})")
                
                # 查看记录数
                cursor.execute("SELECT COUNT(*) FROM model_states")
                count = cursor.fetchone()[0]
                print(f"📈 记录数: {count}")
                
                # 查看所有记录
                if count > 0:
                    cursor.execute("SELECT model_id, trained FROM model_states")
                    records = cursor.fetchall()
                    print("📝 现有记录:")
                    for record in records:
                        print(f"  - {record[0]}: trained={record[1]}")
                
            else:
                print("❌ model_states表不存在")
                
                # 尝试创建表
                print("🔧 尝试创建model_states表...")
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS model_states (
                        model_id TEXT PRIMARY KEY,
                        status TEXT NOT NULL,
                        data_ready BOOLEAN DEFAULT FALSE,
                        features_ready BOOLEAN DEFAULT FALSE,
                        trained BOOLEAN DEFAULT FALSE,
                        up_to_date BOOLEAN DEFAULT FALSE,
                        training_data_size INTEGER DEFAULT 0,
                        last_training_time TEXT,
                        last_check_time TEXT,
                        error_message TEXT
                    )
                ''')
                conn.commit()
                print("✅ model_states表创建成功")
                
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")

if __name__ == "__main__":
    check_model_db()
