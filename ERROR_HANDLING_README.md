# 🔧 福彩3D预测系统错误处理机制

## 📋 概述

本系统实现了完善的错误处理机制，提供统一的错误提示样式、智能错误恢复和用户友好的错误信息展示。

## 🏗️ 架构组件

### 1. 核心组件

#### ErrorHandler (错误处理器)
- **位置**: `src/ui/components/error_handler.py`
- **功能**: 提供统一的错误显示样式和处理逻辑
- **特性**:
  - 统一的错误样式 (error, warning, info)
  - 专用错误处理方法 (API错误、数据错误、模型错误)
  - 详细错误信息展示
  - 解决建议提供

#### ErrorRecovery (错误恢复)
- **功能**: 提供错误恢复选项和系统诊断
- **特性**:
  - 网络错误恢复
  - 数据错误恢复
  - 模型错误恢复
  - 系统健康检查

#### SmartErrorHandler (智能错误处理)
- **功能**: 提供装饰器和智能API调用
- **特性**:
  - 异常处理装饰器
  - 安全API调用方法
  - 自动错误分类和处理

#### ErrorLogger (错误日志)
- **功能**: 错误日志记录和管理
- **特性**:
  - 文件日志记录
  - 结构化错误信息
  - 上下文信息保存

### 2. 配置组件

#### ErrorConfig (错误配置)
- **位置**: `src/ui/components/error_config.py`
- **功能**: 错误类型定义和配置管理
- **内容**:
  - 错误类型枚举
  - 错误严重程度定义
  - 错误消息模板
  - 恢复策略配置
  - HTTP错误码映射

#### ErrorMiddleware (错误中间件)
- **位置**: `src/ui/components/error_middleware.py`
- **功能**: 全局错误捕获和处理
- **特性**:
  - 页面错误处理装饰器
  - API调用错误处理装饰器
  - 数据操作错误处理装饰器
  - 自动重试机制

## 🎯 使用方法

### 1. 基础错误显示

```python
from src.ui.components.error_handler import ErrorHandler

# 显示普通错误
ErrorHandler.show_error(
    title="操作失败",
    message="无法完成请求的操作",
    error_type="error",
    suggestions=["检查网络连接", "重试操作"]
)

# 显示API错误
ErrorHandler.show_api_error(
    endpoint="/api/v1/data",
    status_code=500,
    message="服务器内部错误"
)

# 显示数据错误
ErrorHandler.show_data_error(
    operation="数据加载",
    details="数据格式不正确"
)
```

### 2. 使用装饰器

```python
from src.ui.components.error_middleware import handle_page_errors, handle_api_errors

# 页面错误处理
@handle_page_errors("数据分析")
def show_data_analysis():
    # 页面逻辑
    pass

# API错误处理
@handle_api_errors("http://127.0.0.1:8888/api/v1/data")
def fetch_data():
    response = requests.get("http://127.0.0.1:8888/api/v1/data")
    return response.json()
```

### 3. 智能错误处理

```python
from src.ui.components.error_handler import SmartErrorHandler

# 使用装饰器
@SmartErrorHandler.handle_exception
def risky_operation():
    # 可能出错的操作
    pass

# 安全API调用
result = SmartErrorHandler.safe_api_call(
    "http://127.0.0.1:8888/health",
    timeout=5
)
```

### 4. 错误恢复

```python
from src.ui.components.error_handler import ErrorRecovery

# 显示网络错误恢复选项
ErrorRecovery.show_network_error_recovery()

# 显示数据错误恢复选项
ErrorRecovery.show_data_error_recovery("数据加载")

# 显示模型错误恢复选项
ErrorRecovery.show_model_error_recovery("prediction_model")
```

## 🎨 错误样式

### 错误类型样式
- **Error (错误)**: 红色边框，红色背景
- **Warning (警告)**: 橙色边框，橙色背景
- **Info (信息)**: 蓝色边框，蓝色背景

### 错误严重程度
- **Low (低)**: 自动恢复，不显示详细信息
- **Medium (中)**: 需要用户干预，显示详细信息
- **High (高)**: 严重错误，显示完整信息和恢复选项
- **Critical (严重)**: 系统级错误，显示所有信息和紧急恢复选项

## 🔄 错误恢复策略

### 网络错误恢复
1. **重试连接**: 自动重新尝试网络连接
2. **检查服务状态**: 验证相关服务运行状态
3. **网络诊断**: 执行网络连接诊断

### 数据错误恢复
1. **重新加载数据**: 重新从数据源加载数据
2. **数据验证**: 执行数据完整性验证
3. **数据修复**: 尝试修复数据问题

### 模型错误恢复
1. **重新加载模型**: 重新加载预测模型
2. **模型诊断**: 执行模型状态诊断
3. **使用备用模型**: 切换到备用预测模型

## 📊 错误日志

### 日志格式
```json
{
  "timestamp": "2025-07-23 23:50:00",
  "error_type": "ConnectionError",
  "error_message": "Connection refused",
  "traceback": "...",
  "context": {
    "page_name": "预测分析",
    "function": "fetch_prediction_data",
    "endpoint": "/api/v1/prediction"
  }
}
```

### 日志存储
- **位置**: `logs/error_YYYYMMDD.log`
- **格式**: JSON格式，每行一个错误记录
- **轮转**: 按日期自动轮转日志文件

## 🧪 测试功能

### 错误处理测试页面
- **位置**: `src/ui/pages_disabled/error_handling_test.py`
- **功能**: 测试和演示各种错误处理功能
- **内容**:
  - 错误样式测试
  - 网络错误测试
  - 数据错误测试
  - 恢复功能测试

### 测试用例
1. **连接超时测试**
2. **HTTP错误码测试**
3. **文件操作错误测试**
4. **数据验证错误测试**
5. **智能错误处理测试**

## ⚙️ 配置选项

### 错误处理配置
```python
ERROR_HANDLER_CONFIG = {
    "log_level": "INFO",
    "log_file_max_size": 10 * 1024 * 1024,  # 10MB
    "log_file_backup_count": 5,
    "auto_recovery_enabled": True,
    "show_technical_details": False,
    "error_reporting_enabled": True,
    "max_retry_attempts": 3,
    "retry_delay": 1.0,
    "timeout_default": 10.0
}
```

### 健康检查配置
```python
HEALTH_CHECK_CONFIG = {
    "endpoints": [
        {
            "name": "API服务",
            "url": "http://127.0.0.1:8888/health",
            "timeout": 5,
            "critical": True
        }
    ]
}
```

## 🔧 最佳实践

### 1. 错误处理原则
- **用户友好**: 提供清晰、易懂的错误信息
- **可操作**: 提供具体的解决建议和恢复选项
- **一致性**: 使用统一的错误显示样式
- **可追踪**: 记录详细的错误日志用于调试

### 2. 装饰器使用
- **页面函数**: 使用 `@handle_page_errors`
- **API调用**: 使用 `@handle_api_errors`
- **数据操作**: 使用 `@handle_data_errors`
- **通用异常**: 使用 `@SmartErrorHandler.handle_exception`

### 3. 错误分类
- **网络错误**: 连接失败、超时、HTTP错误
- **数据错误**: 文件不存在、格式错误、验证失败
- **模型错误**: 模型加载失败、预测错误
- **系统错误**: 内存不足、权限错误、未知异常

## 📈 性能影响

### 错误处理开销
- **装饰器开销**: 微秒级，可忽略
- **日志记录开销**: 毫秒级，异步处理
- **错误显示开销**: 毫秒级，仅在错误时触发

### 优化建议
- **合理使用装饰器**: 仅在必要的函数上使用
- **控制日志级别**: 生产环境使用INFO级别
- **异步日志记录**: 避免阻塞主线程

## 🎉 总结

本错误处理机制提供了：
- ✅ **完善的错误分类和处理**
- ✅ **用户友好的错误显示**
- ✅ **智能的错误恢复机制**
- ✅ **详细的错误日志记录**
- ✅ **便捷的装饰器使用**
- ✅ **全面的测试功能**

通过这套错误处理机制，系统能够优雅地处理各种异常情况，提供良好的用户体验，并为开发和维护提供有价值的调试信息。
