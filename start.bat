@echo off
title 3D Prediction System - Quick Start

echo.
echo ========================================
echo    3D Prediction System - Quick Start
echo ========================================
echo.
echo Starting system...
echo.

echo Steps:
echo 1. Start API service (background)
echo 2. Wait 5 seconds
echo 3. Start Streamlit interface
echo.

:: Step 1: Start API service
echo Step 1: Starting API service...
start cmd /k ".\venv\Scripts\activate && python start_production_api.py"

:: Step 2: Wait
echo Step 2: Waiting for API service (5 seconds)...
timeout /t 5 /nobreak >nul

:: Step 3: Start Streamlit
echo Step 3: Starting Streamlit interface...
echo.
echo System started!
echo.
echo Access URLs:
echo    - Streamlit: http://127.0.0.1:8501
echo    - API: http://127.0.0.1:8888
echo    - API docs: http://127.0.0.1:8888/docs
echo.
echo Tips:
echo    - Closing this window will stop Streamlit
echo    - API service runs in a separate window
echo    - To stop all services, close all related windows
echo.

:: Activate venv and start Streamlit
call .\venv\Scripts\activate
python start_streamlit.py

echo.
echo Streamlit interface stopped
echo API service may still be running in background
pause
