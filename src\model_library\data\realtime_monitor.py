"""
实时数据质量监控系统
提供异步监控循环、质量阈值检查告警、趋势分析功能
"""

import asyncio
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from collections import deque
import pandas as pd
import numpy as np
from enum import Enum

from .adaptive_quality_engine import AdaptiveDataQualityEngine, DataQualityMetrics


class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class QualityAlert:
    """质量告警"""
    timestamp: datetime
    level: AlertLevel
    metric_name: str
    current_value: float
    threshold_value: float
    message: str
    model_id: str


@dataclass
class QualityTrend:
    """质量趋势"""
    metric_name: str
    trend_direction: str  # "improving", "declining", "stable"
    trend_strength: float  # 0-1, 趋势强度
    prediction: float  # 预测下一个值
    confidence: float  # 预测置信度


@dataclass
class MonitoringConfig:
    """监控配置"""
    check_interval: int = 300  # 检查间隔（秒）
    trend_window_size: int = 20  # 趋势分析窗口大小
    alert_thresholds: Dict[str, Dict[str, float]] = field(default_factory=lambda: {
        "completeness": {"warning": 0.8, "error": 0.6, "critical": 0.4},
        "consistency": {"warning": 0.75, "error": 0.5, "critical": 0.3},
        "accuracy": {"warning": 0.8, "error": 0.6, "critical": 0.4},
        "timeliness": {"warning": 0.7, "error": 0.5, "critical": 0.3},
        "validity": {"warning": 0.8, "error": 0.6, "critical": 0.4},
        "overall_score": {"warning": 0.75, "error": 0.6, "critical": 0.4}
    })


class RealTimeDataQualityMonitor:
    """实时数据质量监控系统"""
    
    def __init__(self, config: Optional[MonitoringConfig] = None):
        self.config = config or MonitoringConfig()
        self.quality_engine = AdaptiveDataQualityEngine()
        
        # 监控状态
        self.is_monitoring = False
        self.monitoring_tasks: Dict[str, asyncio.Task] = {}
        
        # 历史数据存储
        self.quality_history: Dict[str, deque] = {}
        self.alert_history: List[QualityAlert] = []
        
        # 回调函数
        self.alert_callbacks: List[Callable[[QualityAlert], None]] = []
        self.trend_callbacks: List[Callable[[str, QualityTrend], None]] = []
        
        # 日志配置
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger("DataQualityMonitor")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    async def start_monitoring(self, model_id: str, data_source: Callable[[], pd.DataFrame]):
        """
        启动监控
        
        Args:
            model_id: 模型ID
            data_source: 数据源函数，返回最新的数据DataFrame
        """
        if model_id in self.monitoring_tasks:
            self.logger.warning(f"模型 {model_id} 已在监控中")
            return
        
        self.logger.info(f"启动模型 {model_id} 的数据质量监控")
        
        # 初始化历史记录
        if model_id not in self.quality_history:
            self.quality_history[model_id] = deque(maxlen=self.config.trend_window_size)
        
        # 创建监控任务
        task = asyncio.create_task(self._monitoring_loop(model_id, data_source))
        self.monitoring_tasks[model_id] = task
        
        self.is_monitoring = True
    
    async def stop_monitoring(self, model_id: str):
        """停止监控"""
        if model_id not in self.monitoring_tasks:
            self.logger.warning(f"模型 {model_id} 未在监控中")
            return
        
        self.logger.info(f"停止模型 {model_id} 的数据质量监控")
        
        # 取消监控任务
        task = self.monitoring_tasks[model_id]
        task.cancel()
        
        try:
            await task
        except asyncio.CancelledError:
            pass
        
        del self.monitoring_tasks[model_id]
        
        if not self.monitoring_tasks:
            self.is_monitoring = False
    
    async def stop_all_monitoring(self):
        """停止所有监控"""
        model_ids = list(self.monitoring_tasks.keys())
        for model_id in model_ids:
            await self.stop_monitoring(model_id)
    
    async def _monitoring_loop(self, model_id: str, data_source: Callable[[], pd.DataFrame]):
        """监控循环"""
        self.logger.info(f"开始监控循环 - 模型: {model_id}")
        
        try:
            while True:
                try:
                    # 获取最新数据
                    current_data = data_source()
                    
                    if current_data.empty:
                        self.logger.warning(f"模型 {model_id} 获取到空数据")
                        await asyncio.sleep(self.config.check_interval)
                        continue
                    
                    # 评估数据质量
                    data_range = (0, len(current_data) - 1)
                    quality_metrics = self.quality_engine.calculate_adaptive_quality_score(
                        model_id, data_range, current_data
                    )
                    
                    # 记录质量历史
                    quality_record = {
                        'timestamp': datetime.now(),
                        'metrics': quality_metrics
                    }
                    self.quality_history[model_id].append(quality_record)
                    
                    # 检查告警
                    await self._check_alerts(model_id, quality_metrics)
                    
                    # 分析趋势
                    await self._analyze_trends(model_id)
                    
                    self.logger.debug(f"模型 {model_id} 质量检查完成，综合评分: {quality_metrics.overall_score:.3f}")
                    
                except Exception as e:
                    self.logger.error(f"监控循环出错 - 模型 {model_id}: {e}")
                
                # 等待下次检查
                await asyncio.sleep(self.config.check_interval)
                
        except asyncio.CancelledError:
            self.logger.info(f"监控循环已取消 - 模型: {model_id}")
            raise
        except Exception as e:
            self.logger.error(f"监控循环异常退出 - 模型 {model_id}: {e}")
    
    async def _check_alerts(self, model_id: str, quality_metrics: DataQualityMetrics):
        """检查告警条件"""
        metrics_dict = {
            'completeness': quality_metrics.completeness,
            'consistency': quality_metrics.consistency,
            'accuracy': quality_metrics.accuracy,
            'timeliness': quality_metrics.timeliness,
            'validity': quality_metrics.validity,
            'overall_score': quality_metrics.overall_score
        }
        
        for metric_name, current_value in metrics_dict.items():
            thresholds = self.config.alert_thresholds.get(metric_name, {})
            
            # 检查各级别告警
            alert_level = None
            threshold_value = None
            
            if current_value < thresholds.get('critical', 0):
                alert_level = AlertLevel.CRITICAL
                threshold_value = thresholds['critical']
            elif current_value < thresholds.get('error', 0):
                alert_level = AlertLevel.ERROR
                threshold_value = thresholds['error']
            elif current_value < thresholds.get('warning', 0):
                alert_level = AlertLevel.WARNING
                threshold_value = thresholds['warning']
            
            if alert_level:
                alert = QualityAlert(
                    timestamp=datetime.now(),
                    level=alert_level,
                    metric_name=metric_name,
                    current_value=current_value,
                    threshold_value=threshold_value,
                    message=f"模型 {model_id} 的 {metric_name} 指标 ({current_value:.3f}) 低于 {alert_level.value} 阈值 ({threshold_value:.3f})",
                    model_id=model_id
                )
                
                await self._trigger_alert(alert)
    
    async def _trigger_alert(self, alert: QualityAlert):
        """触发告警"""
        # 记录告警历史
        self.alert_history.append(alert)
        
        # 日志记录
        log_method = {
            AlertLevel.INFO: self.logger.info,
            AlertLevel.WARNING: self.logger.warning,
            AlertLevel.ERROR: self.logger.error,
            AlertLevel.CRITICAL: self.logger.critical
        }.get(alert.level, self.logger.info)
        
        log_method(f"数据质量告警: {alert.message}")
        
        # 调用回调函数
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                self.logger.error(f"告警回调函数执行失败: {e}")
    
    async def _analyze_trends(self, model_id: str):
        """分析质量趋势"""
        if model_id not in self.quality_history:
            return
        
        history = list(self.quality_history[model_id])
        if len(history) < 3:
            return
        
        # 分析各指标的趋势
        metrics_names = ['completeness', 'consistency', 'accuracy', 'timeliness', 'validity', 'overall_score']
        
        for metric_name in metrics_names:
            values = [getattr(record['metrics'], metric_name) for record in history]
            trend = self._calculate_trend(metric_name, values)
            
            if trend:
                # 调用趋势回调
                for callback in self.trend_callbacks:
                    try:
                        callback(model_id, trend)
                    except Exception as e:
                        self.logger.error(f"趋势回调函数执行失败: {e}")
    
    def _calculate_trend(self, metric_name: str, values: List[float]) -> Optional[QualityTrend]:
        """计算趋势"""
        try:
            if len(values) < 3:
                return None
            
            # 使用线性回归计算趋势
            x = np.arange(len(values))
            y = np.array(values)
            
            # 计算斜率
            slope = np.polyfit(x, y, 1)[0]
            
            # 确定趋势方向
            if abs(slope) < 0.001:
                trend_direction = "stable"
                trend_strength = 0.0
            elif slope > 0:
                trend_direction = "improving"
                trend_strength = min(abs(slope) * 100, 1.0)
            else:
                trend_direction = "declining"
                trend_strength = min(abs(slope) * 100, 1.0)
            
            # 预测下一个值
            next_x = len(values)
            prediction = np.polyval([slope, np.mean(y) - slope * np.mean(x)], next_x)
            prediction = max(0, min(1, prediction))  # 限制在[0,1]范围内
            
            # 计算预测置信度
            residuals = y - np.polyval([slope, np.mean(y) - slope * np.mean(x)], x)
            mse = np.mean(residuals ** 2)
            confidence = max(0, 1 - mse * 10)  # 简化的置信度计算
            
            return QualityTrend(
                metric_name=metric_name,
                trend_direction=trend_direction,
                trend_strength=trend_strength,
                prediction=prediction,
                confidence=confidence
            )
            
        except Exception as e:
            self.logger.error(f"趋势计算失败 - {metric_name}: {e}")
            return None
    
    def add_alert_callback(self, callback: Callable[[QualityAlert], None]):
        """添加告警回调函数"""
        self.alert_callbacks.append(callback)
    
    def add_trend_callback(self, callback: Callable[[str, QualityTrend], None]):
        """添加趋势回调函数"""
        self.trend_callbacks.append(callback)
    
    def get_quality_history(self, model_id: str, hours: int = 24) -> List[Dict[str, Any]]:
        """获取质量历史记录"""
        if model_id not in self.quality_history:
            return []
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        history = []
        
        for record in self.quality_history[model_id]:
            if record['timestamp'] >= cutoff_time:
                history.append({
                    'timestamp': record['timestamp'],
                    'completeness': record['metrics'].completeness,
                    'consistency': record['metrics'].consistency,
                    'accuracy': record['metrics'].accuracy,
                    'timeliness': record['metrics'].timeliness,
                    'validity': record['metrics'].validity,
                    'overall_score': record['metrics'].overall_score
                })
        
        return history
    
    def get_recent_alerts(self, model_id: Optional[str] = None, hours: int = 24) -> List[QualityAlert]:
        """获取最近的告警记录"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        alerts = [
            alert for alert in self.alert_history
            if alert.timestamp >= cutoff_time and (model_id is None or alert.model_id == model_id)
        ]
        
        return sorted(alerts, key=lambda x: x.timestamp, reverse=True)
    
    def get_quality_trend_analysis(self, model_id: str, days: int = 7) -> Dict[str, Any]:
        """获取质量趋势分析"""
        if model_id not in self.quality_history:
            return {}
        
        cutoff_time = datetime.now() - timedelta(days=days)
        recent_records = [
            record for record in self.quality_history[model_id]
            if record['timestamp'] >= cutoff_time
        ]
        
        if len(recent_records) < 2:
            return {}
        
        # 计算各指标的趋势
        trends = {}
        metrics_names = ['completeness', 'consistency', 'accuracy', 'timeliness', 'validity', 'overall_score']
        
        for metric_name in metrics_names:
            values = [getattr(record['metrics'], metric_name) for record in recent_records]
            trend = self._calculate_trend(metric_name, values)
            
            if trend:
                trends[metric_name] = {
                    'direction': trend.trend_direction,
                    'strength': trend.trend_strength,
                    'prediction': trend.prediction,
                    'confidence': trend.confidence
                }
        
        # 计算整体趋势摘要
        overall_trends = [trends[metric]['direction'] for metric in trends if metric != 'overall_score']
        improving_count = overall_trends.count('improving')
        declining_count = overall_trends.count('declining')
        
        if improving_count > declining_count:
            overall_direction = 'improving'
        elif declining_count > improving_count:
            overall_direction = 'declining'
        else:
            overall_direction = 'stable'
        
        return {
            'overall_direction': overall_direction,
            'metric_trends': trends,
            'analysis_period_days': days,
            'data_points': len(recent_records)
        }
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        return {
            'is_monitoring': self.is_monitoring,
            'monitored_models': list(self.monitoring_tasks.keys()),
            'total_alerts': len(self.alert_history),
            'recent_alerts_24h': len(self.get_recent_alerts(hours=24)),
            'config': {
                'check_interval': self.config.check_interval,
                'trend_window_size': self.config.trend_window_size
            }
        }


def test_realtime_monitor():
    """测试实时数据质量监控"""
    print("🧪 测试实时数据质量监控系统...")
    
    async def run_test():
        # 创建监控器
        monitor = RealTimeDataQualityMonitor()
        
        # 模拟数据源
        def mock_data_source():
            return pd.DataFrame({
                'period': range(2024001, 2024101),
                'date': pd.date_range('2024-01-01', periods=100),
                'winning_numbers': [f"{np.random.randint(0,10)}{np.random.randint(0,10)}{np.random.randint(0,10)}" 
                                   for _ in range(100)]
            })
        
        # 添加告警回调
        def alert_callback(alert: QualityAlert):
            print(f"🚨 告警: {alert.level.value} - {alert.message}")
        
        monitor.add_alert_callback(alert_callback)
        
        # 启动监控
        await monitor.start_monitoring("test_model", mock_data_source)
        
        # 运行一段时间
        print("⏳ 监控运行中...")
        await asyncio.sleep(2)
        
        # 获取状态
        status = monitor.get_monitoring_status()
        print(f"📊 监控状态: {status}")
        
        # 停止监控
        await monitor.stop_monitoring("test_model")
        print("✅ 监控已停止")
    
    # 运行测试
    asyncio.run(run_test())
    print("✅ 实时数据质量监控系统测试完成！")


if __name__ == "__main__":
    test_realtime_monitor()
