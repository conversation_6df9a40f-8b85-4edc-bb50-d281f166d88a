"""
特征工程深度管理页面

提供特征选择、重要性分析和参数配置的深度交互界面
"""

import asyncio
import os
import sys
from typing import Any, Dict, List, Optional

import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import streamlit as st
from plotly.subplots import make_subplots

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from model_library.features.feature_ranking import MultiAlgorithmFeatureRanking
from model_library.features.feature_selector import (
    FeatureConfig, InteractiveFeatureSelector)


def initialize_feature_state():
    """初始化特征选择状态"""
    if 'selected_features' not in st.session_state:
        st.session_state.selected_features = set()
    if 'feature_processing' not in st.session_state:
        st.session_state.feature_processing = False
    if 'feature_selection_state' not in st.session_state:
        st.session_state.feature_selection_state = {}


def handle_feature_selection_with_state():
    """处理特征选择逻辑，使用状态管理"""
    initialize_feature_state()

    # 特征分类
    feature_categories = {
        "基础统计特征": ["数字频率统计", "和值分析", "跨度分析", "奇偶分析", "大小分析"],
        "时间序列特征": ["周期性分析", "趋势分析", "季节性分析", "滞后特征", "移动平均"],
        "高级数学特征": ["熵值计算", "相关性分析", "回归分析", "方差分析", "协方差分析"],
        "创新特征": ["机器学习特征", "深度学习特征", "神经网络特征", "集成特征"],
        "组合特征": ["多维度组合分析", "交叉特征", "多项式特征", "特征交互"]
    }

    return feature_categories


def apply_feature_selection_with_error_handling():
    """应用特征选择，包含错误处理"""
    try:
        if not st.session_state.selected_features:
            st.warning("⚠️ 请至少选择一个特征")
            return False

        st.session_state.feature_processing = True

        with st.spinner("正在处理特征选择..."):
            # 模拟特征处理
            import time
            time.sleep(1)

            selected_count = len(st.session_state.selected_features)
            st.success(f"✅ 已选择 {selected_count} 个特征")

            # 显示选择的特征
            with st.expander("📋 已选择的特征", expanded=False):
                for feature in sorted(st.session_state.selected_features):
                    st.write(f"• {feature}")

            return True

    except Exception as e:
        st.error(f"❌ 特征处理失败: {str(e)}")
        return False
    finally:
        st.session_state.feature_processing = False


def show_feature_engineering_deep_page():
    """显示特征工程深度管理页面"""
    st.title("🔧 特征工程深度管理")
    st.markdown("---")
    
    # 初始化组件
    if 'feature_selector' not in st.session_state:
        st.session_state.feature_selector = InteractiveFeatureSelector()

    if 'feature_ranking' not in st.session_state:
        st.session_state.feature_ranking = MultiAlgorithmFeatureRanking()

    # 确保session_state中有必要的键
    if 'selected_features' not in st.session_state:
        st.session_state.selected_features = set()
    
    # 获取当前选择的模型
    model_id = st.selectbox(
        "选择模型",
        ["intelligent_fusion", "trend_analyzer", "deep_learning_cnn_lstm", "markov_enhanced"],
        key="feature_model_selector"
    )
    
    # 主要内容区域
    tab1, tab2, tab3, tab4 = st.tabs(["🎯 特征选择", "📊 重要性分析", "⚙️ 参数配置", "👁️ 实时预览"])
    
    with tab1:
        show_feature_selection_tab(model_id)
    
    with tab2:
        show_feature_importance_tab(model_id)
    
    with tab3:
        show_parameter_configuration_tab()
    
    with tab4:
        show_feature_preview_tab()


def show_feature_selection_tab(model_id: str):
    """显示特征选择标签页"""
    st.subheader("🎯 智能特征选择")
    
    selector = st.session_state.feature_selector
    categories = selector.get_available_feature_categories()
    
    # 特征选择区域
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown("#### 📂 按类别选择特征")

        # 简化的特征选择逻辑
        selected_features = {}

        # 基础统计特征
        with st.expander("📂 基础统计特征", expanded=False):
            basic_features = ["数字频率统计", "和值分析", "跨度分析", "奇偶分析", "大小分析"]
            selected_features["基础统计特征"] = []
            for feature in basic_features:
                if st.checkbox(feature, key=f"basic_{feature}"):
                    selected_features["基础统计特征"].append(feature)

        # 时间序列特征
        with st.expander("📂 时间序列特征", expanded=False):
            time_features = ["周期性分析", "趋势分析", "季节性分析", "滞后特征", "移动平均"]
            selected_features["时间序列特征"] = []
            for feature in time_features:
                if st.checkbox(feature, key=f"time_{feature}"):
                    selected_features["时间序列特征"].append(feature)

        # 高级数学特征
        with st.expander("📂 高级数学特征", expanded=False):
            math_features = ["熵值计算", "相关性分析", "回归分析", "方差分析", "协方差分析"]
            selected_features["高级数学特征"] = []
            for feature in math_features:
                if st.checkbox(feature, key=f"math_{feature}"):
                    selected_features["高级数学特征"].append(feature)

        # 统计选择的特征总数
        total_selected = sum(len(features) for features in selected_features.values())
        st.markdown(f"**已选择特征数**: {total_selected}")

        # 应用选择按钮
        if st.button("🔄 应用特征选择", type="primary"):
            if total_selected > 0:
                st.success(f"✅ 已选择 {total_selected} 个特征")
                with st.expander("📋 已选择的特征", expanded=False):
                    for category, features in selected_features.items():
                        if features:
                            st.write(f"**{category}**: {', '.join(features)}")
                st.balloons()
            else:
                st.warning("⚠️ 请至少选择一个特征")
    
    with col2:
        st.markdown("#### 📊 选择摘要")
        
        summary = selector.get_selected_features_summary()
        
        # 显示统计信息
        st.metric("已选择特征数", summary["total_count"])
        st.metric("预计提取时间", f"{summary['estimated_extraction_time']:.1f}秒")
        
        # 类别分布饼图
        if summary["category_distribution"]:
            fig = px.pie(
                values=list(summary["category_distribution"].values()),
                names=list(summary["category_distribution"].keys()),
                title="特征类别分布"
            )
            fig.update_layout(height=300)
            st.plotly_chart(fig, use_container_width=True)
        
        # 已选择特征列表
        if summary["selected_features"]:
            st.markdown("#### 📋 已选择特征")
            for i, feature in enumerate(summary["selected_features"], 1):
                st.write(f"{i}. {feature}")


def show_feature_importance_tab(model_id: str):
    """显示特征重要性分析标签页"""
    st.subheader("📊 特征重要性分析")
    
    ranking_engine = st.session_state.feature_ranking
    selector = st.session_state.feature_selector
    
    # 重要性计算控制
    col1, col2 = st.columns([3, 1])
    
    with col1:
        st.markdown("#### 🎛️ 重要性计算配置")
        
        # 算法权重配置
        st.markdown("**算法权重配置**")
        weights = ranking_engine._get_algorithm_weights(model_id)
        
        updated_weights = {}
        for algo_name, weight in weights.items():
            algo_display_names = {
                "mutual_information": "互信息",
                "random_forest": "随机森林",
                "correlation_analysis": "相关性分析",
                "lstm_attention": "LSTM注意力",
                "custom_lottery": "福彩3D特定"
            }
            display_name = algo_display_names.get(algo_name, algo_name)
            updated_weights[algo_name] = st.slider(
                display_name,
                min_value=0.0,
                max_value=1.0,
                value=weight,
                step=0.05,
                key=f"weight_{algo_name}"
            )
    
    with col2:
        st.markdown("#### 🚀 执行分析")
        
        if st.button("🔍 计算特征重要性", type="primary"):
            if selector.selected_features:
                with st.spinner("正在计算特征重要性..."):
                    # 模拟特征数据和目标数据
                    sample_size = 1000
                    features_dict = {}
                    
                    for feature_name in selector.selected_features:
                        # 生成模拟特征数据
                        features_dict[feature_name] = np.random.normal(0, 1, sample_size)
                    
                    # 生成模拟目标数据
                    targets = np.random.randint(0, 1000, sample_size)
                    
                    # 计算重要性
                    importance_scores = ranking_engine.calculate_ensemble_feature_importance(
                        model_id, features_dict, targets
                    )
                    
                    # 存储结果
                    st.session_state.importance_scores = importance_scores
                    
                st.success("✅ 特征重要性计算完成！")
            else:
                st.warning("⚠️ 请先选择特征")
    
    # 显示重要性结果
    if 'importance_scores' in st.session_state and st.session_state.importance_scores:
        st.markdown("---")
        st.markdown("#### 📈 重要性分析结果")
        
        importance_scores = st.session_state.importance_scores
        
        # 创建重要性排序图表
        col1, col2 = st.columns(2)
        
        with col1:
            # Top 10 特征重要性条形图
            top_features = ranking_engine.get_top_features(importance_scores, 10)
            
            if top_features:
                df_top = pd.DataFrame(top_features, columns=["特征名称", "重要性分数"])
                
                fig = px.bar(
                    df_top,
                    x="重要性分数",
                    y="特征名称",
                    orientation="h",
                    title="Top 10 特征重要性",
                    color="重要性分数",
                    color_continuous_scale="viridis"
                )
                fig.update_layout(height=400)
                st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # 特征类别重要性分析
            category_stats = ranking_engine.analyze_feature_categories(importance_scores)
            
            if category_stats:
                categories = []
                avg_importance = []
                max_importance = []
                feature_counts = []
                
                for category, stats in category_stats.items():
                    if stats["count"] > 0:
                        categories.append(category)
                        avg_importance.append(stats["avg_importance"])
                        max_importance.append(stats["max_importance"])
                        feature_counts.append(stats["count"])
                
                fig = go.Figure()
                
                fig.add_trace(go.Bar(
                    name='平均重要性',
                    x=categories,
                    y=avg_importance,
                    marker_color='lightblue'
                ))
                
                fig.add_trace(go.Bar(
                    name='最大重要性',
                    x=categories,
                    y=max_importance,
                    marker_color='darkblue'
                ))
                
                fig.update_layout(
                    title="特征类别重要性对比",
                    xaxis_title="特征类别",
                    yaxis_title="重要性分数",
                    barmode='group',
                    height=400
                )
                
                st.plotly_chart(fig, use_container_width=True)
        
        # 详细重要性表格
        st.markdown("#### 📋 详细重要性分数")
        
        df_importance = pd.DataFrame([
            {"特征名称": name, "重要性分数": f"{score:.4f}", "排名": i+1}
            for i, (name, score) in enumerate(sorted(importance_scores.items(), key=lambda x: x[1], reverse=True))
        ])
        
        st.dataframe(df_importance, use_container_width=True)


def show_parameter_configuration_tab():
    """显示参数配置标签页"""
    st.subheader("⚙️ 特征参数配置")
    
    selector = st.session_state.feature_selector
    
    if not selector.selected_features:
        st.info("💡 请先在特征选择标签页中选择特征")
        return
    
    st.markdown("#### 🎛️ 参数调节面板")
    
    # 为每个选中的特征显示参数配置
    for feature_name in selector.selected_features:
        if feature_name in selector.feature_configs:
            config = selector.feature_configs[feature_name]
            
            with st.expander(f"⚙️ {feature_name} - 参数配置", expanded=False):
                st.markdown(f"**类别**: {config.category}")
                st.markdown(f"**描述**: {config.description}")
                
                # 参数配置界面
                updated_params = {}
                for param_name, param_value in config.parameters.items():
                    if isinstance(param_value, bool):
                        updated_params[param_name] = st.checkbox(
                            param_name,
                            value=param_value,
                            key=f"param_{feature_name}_{param_name}"
                        )
                    elif isinstance(param_value, int):
                        updated_params[param_name] = st.number_input(
                            param_name,
                            value=param_value,
                            min_value=1,
                            max_value=1000,
                            key=f"param_{feature_name}_{param_name}"
                        )
                    elif isinstance(param_value, float):
                        updated_params[param_name] = st.slider(
                            param_name,
                            min_value=0.0,
                            max_value=2.0,
                            value=param_value,
                            step=0.01,
                            key=f"param_{feature_name}_{param_name}"
                        )
                    elif isinstance(param_value, str):
                        updated_params[param_name] = st.text_input(
                            param_name,
                            value=param_value,
                            key=f"param_{feature_name}_{param_name}"
                        )
                    elif isinstance(param_value, list):
                        updated_params[param_name] = st.multiselect(
                            param_name,
                            options=param_value + [5, 10, 15, 20, 30, 50],
                            default=param_value,
                            key=f"param_{feature_name}_{param_name}"
                        )
                
                # 应用参数按钮
                if st.button(f"应用参数", key=f"apply_params_{feature_name}"):
                    success = selector.update_feature_parameters(feature_name, updated_params)
                    if success:
                        st.success(f"✅ {feature_name} 参数已更新")
                    else:
                        st.error(f"❌ {feature_name} 参数更新失败")


def show_feature_preview_tab():
    """显示特征预览标签页"""
    st.subheader("👁️ 实时特征预览")
    
    selector = st.session_state.feature_selector
    
    if not selector.selected_features:
        st.info("💡 请先在特征选择标签页中选择特征")
        return
    
    # 预览控制
    col1, col2 = st.columns([3, 1])
    
    with col1:
        st.markdown("#### 🎯 预览配置")
        
        sample_size = st.slider(
            "样本数量",
            min_value=10,
            max_value=1000,
            value=100,
            step=10
        )
        
        preview_features = st.multiselect(
            "选择要预览的特征",
            options=list(selector.selected_features),
            default=list(selector.selected_features)[:5]  # 默认选择前5个
        )
    
    with col2:
        st.markdown("#### 🚀 生成预览")
        
        if st.button("🔍 生成特征预览", type="primary"):
            if preview_features:
                with st.spinner("正在生成特征预览..."):
                    # 生成模拟样本数据
                    sample_data = []
                    for i in range(sample_size):
                        sample_data.append({
                            "period": f"2025{i:03d}",
                            "numbers": [
                                np.random.randint(0, 10),
                                np.random.randint(0, 10),
                                np.random.randint(0, 10)
                            ]
                        })
                    
                    # 异步生成预览（这里简化为同步）
                    preview_results = {}
                    for feature_name in preview_features:
                        # 模拟特征提取结果
                        values = np.random.normal(0, 1, sample_size)
                        preview_results[feature_name] = {
                            "values": values,
                            "statistics": {
                                "mean": float(np.mean(values)),
                                "std": float(np.std(values)),
                                "min": float(np.min(values)),
                                "max": float(np.max(values))
                            },
                            "quality_score": np.random.uniform(0.5, 1.0)
                        }
                    
                    st.session_state.preview_results = preview_results
                
                st.success("✅ 特征预览生成完成！")
            else:
                st.warning("⚠️ 请选择要预览的特征")
    
    # 显示预览结果
    if 'preview_results' in st.session_state and st.session_state.preview_results:
        st.markdown("---")
        st.markdown("#### 📊 特征预览结果")
        
        preview_results = st.session_state.preview_results
        
        # 特征质量概览
        quality_data = []
        for feature_name, result in preview_results.items():
            quality_data.append({
                "特征名称": feature_name,
                "质量评分": result["quality_score"],
                "均值": result["statistics"]["mean"],
                "标准差": result["statistics"]["std"]
            })
        
        df_quality = pd.DataFrame(quality_data)
        
        # 质量评分条形图
        fig = px.bar(
            df_quality,
            x="特征名称",
            y="质量评分",
            title="特征质量评分",
            color="质量评分",
            color_continuous_scale="RdYlGn"
        )
        fig.update_layout(height=400)
        st.plotly_chart(fig, use_container_width=True)
        
        # 特征分布可视化
        st.markdown("#### 📈 特征分布可视化")
        
        selected_feature_for_dist = st.selectbox(
            "选择特征查看分布",
            options=list(preview_results.keys())
        )
        
        if selected_feature_for_dist:
            values = preview_results[selected_feature_for_dist]["values"]
            
            col1, col2 = st.columns(2)
            
            with col1:
                # 直方图
                fig_hist = px.histogram(
                    x=values,
                    title=f"{selected_feature_for_dist} - 分布直方图",
                    nbins=30
                )
                st.plotly_chart(fig_hist, use_container_width=True)
            
            with col2:
                # 时间序列图
                fig_line = px.line(
                    y=values,
                    title=f"{selected_feature_for_dist} - 时间序列",
                    labels={"index": "样本索引", "y": "特征值"}
                )
                st.plotly_chart(fig_line, use_container_width=True)
        
        # 统计摘要表格
        st.markdown("#### 📋 统计摘要")
        st.dataframe(df_quality, use_container_width=True)


if __name__ == "__main__":
    show_feature_engineering_deep_page()
