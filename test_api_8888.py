#!/usr/bin/env python3
"""
测试8888端口的API服务
"""

import json

import requests


def test_api():
    base_url = "http://127.0.0.1:8888"

    print("🔍 测试API服务...")

    # 健康检查
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"✅ 健康检查状态: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"📊 响应数据:")
            print(json.dumps(data, indent=2, ensure_ascii=False))

            # 验证关键字段
            print(f"\n🔍 字段验证:")
            print(f"  status: {data.get('status')}")
            print(f"  database_records: {data.get('database_records')}")
            print(f"  timestamp: {data.get('timestamp')}")

            return True
        else:
            print(f"❌ API返回错误状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False

if __name__ == "__main__":
    test_api()
