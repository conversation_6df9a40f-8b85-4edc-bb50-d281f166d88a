#!/usr/bin/env python3
"""
预测系统启动脚本
Prediction System Startup Script

一键启动预测-验证-优化闭环智能系统
"""

import logging
import os
import subprocess
import sys
import time
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """主启动函数"""
    logger.info("🚀 启动预测-验证-优化闭环智能系统")
    
    project_root = Path(__file__).parent
    processes = []
    
    try:
        # 1. 检查Python版本
        python_version = sys.version_info
        if python_version.major < 3 or python_version.minor < 8:
            logger.error("❌ 需要Python 3.8或更高版本")
            return
        
        logger.info(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # 2. 创建必要目录
        directories = ['data', 'config/models', 'backups/parameters', 'logs']
        for directory in directories:
            (project_root / directory).mkdir(parents=True, exist_ok=True)
        
        logger.info("✅ 目录结构创建完成")
        
        # 3. 启动API服务器
        logger.info("🔌 启动API服务器...")
        
        api_cmd = [
            sys.executable, "-c",
            """
import sys
import os
sys.path.append('.')

try:
    from src.api.analysis_api import app
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8888, log_level="info")
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装所需依赖: pip install fastapi uvicorn")
except Exception as e:
    print(f"API服务器启动失败: {e}")
"""
        ]
        
        api_process = subprocess.Popen(
            api_cmd,
            cwd=project_root,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        processes.append(('API服务器', api_process))
        
        # 等待API服务器启动
        time.sleep(5)
        
        # 4. 启动Streamlit仪表板
        logger.info("📊 启动Streamlit仪表板...")
        
        dashboard_cmd = [
            sys.executable, "-c",
            """
import sys
import os
sys.path.append('.')

try:
    import streamlit as st
    from src.ui.pages.prediction_analysis_dashboard import show_prediction_analysis_dashboard
    
    # 配置Streamlit
    st.set_page_config(
        page_title="预测分析仪表板",
        page_icon="🔍",
        layout="wide"
    )
    
    # 显示仪表板
    show_prediction_analysis_dashboard()
    
except ImportError as e:
    st.error(f"导入错误: {e}")
    st.error("请确保已安装所需依赖: pip install streamlit plotly")
except Exception as e:
    st.error(f"仪表板启动失败: {e}")
"""
        ]
        
        # 使用streamlit run命令启动
        streamlit_cmd = [
            sys.executable, "-m", "streamlit", "run",
            "--server.port", "8501",
            "--server.address", "127.0.0.1"
        ]
        
        # 创建临时启动文件
        temp_app_file = project_root / "temp_dashboard.py"
        with open(temp_app_file, 'w', encoding='utf-8') as f:
            f.write("""
import sys
import os
sys.path.append('.')

import streamlit as st

# 配置页面
st.set_page_config(
    page_title="预测分析仪表板",
    page_icon="🔍",
    layout="wide"
)

try:
    from src.ui.pages.prediction_analysis_dashboard import show_prediction_analysis_dashboard
    show_prediction_analysis_dashboard()
except ImportError as e:
    st.error(f"导入错误: {e}")
    st.error("请确保项目结构正确，并安装所需依赖")
    st.code("pip install streamlit plotly pandas numpy")
except Exception as e:
    st.error(f"系统错误: {e}")
    st.info("系统正在初始化中，请稍后刷新页面")
""")
        
        streamlit_process = subprocess.Popen(
            [sys.executable, "-m", "streamlit", "run", str(temp_app_file)] + streamlit_cmd[2:],
            cwd=project_root
        )
        processes.append(('Streamlit仪表板', streamlit_process))
        
        # 等待仪表板启动
        time.sleep(3)
        
        # 5. 显示启动信息
        logger.info("=" * 60)
        logger.info("🎯 预测-验证-优化闭环智能系统已启动")
        logger.info("=" * 60)
        logger.info("📊 Streamlit仪表板: http://127.0.0.1:8501")
        logger.info("🔌 API服务器: http://127.0.0.1:8888")
        logger.info("📖 API文档: http://127.0.0.1:8888/docs")
        logger.info("=" * 60)
        logger.info("🎛️ 主要功能:")
        logger.info("  • 统一预测记录管理")
        logger.info("  • 自动开奖触发分析")
        logger.info("  • 多维度偏差分析")
        logger.info("  • 智能弱点识别")
        logger.info("  • 成功因子提取")
        logger.info("  • 优化建议生成")
        logger.info("  • 参数自动回测")
        logger.info("  • 参数自动应用")
        logger.info("=" * 60)
        logger.info("✅ 系统启动完成！按 Ctrl+C 停止系统")
        
        # 6. 等待用户中断
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("🛑 收到停止信号...")
    
    except Exception as e:
        logger.error(f"❌ 系统启动失败: {e}")
    
    finally:
        # 清理进程
        logger.info("🧹 清理系统进程...")
        for name, process in processes:
            try:
                process.terminate()
                process.wait(timeout=5)
                logger.info(f"✅ {name} 已停止")
            except subprocess.TimeoutExpired:
                process.kill()
                logger.info(f"🔪 {name} 已强制停止")
            except Exception as e:
                logger.error(f"❌ 停止 {name} 失败: {e}")
        
        # 清理临时文件
        temp_file = project_root / "temp_dashboard.py"
        if temp_file.exists():
            temp_file.unlink()
        
        logger.info("✅ 系统已完全停止")


if __name__ == "__main__":
    main()
