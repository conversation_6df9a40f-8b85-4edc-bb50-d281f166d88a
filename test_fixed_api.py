#!/usr/bin/env python3
"""
测试修复后的API
"""

import requests
import json

def test_fixed_api():
    base_url = "http://127.0.0.1:8888"
    
    print("🧪 测试修复后的API...")
    
    # 测试基础统计
    try:
        print("\n1. 测试基础统计接口...")
        response = requests.get(f"{base_url}/api/v1/stats/basic", timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ 成功!")
            print(f"   总记录数: {data.get('total_records', 'N/A')}")
            print(f"   查询时间: {data.get('query_time_ms', 'N/A')}ms")
            print(f"   数据范围: {data.get('date_range', {})}")
        else:
            print(f"   ❌ 失败 - 状态码: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 测试其他API
    apis_to_test = [
        ("/health", "健康检查"),
        ("/api/v1/analysis/frequency", "频率分析"),
        ("/api/v1/analysis/sum-distribution", "和值分布"),
        ("/api/v1/analysis/sales", "销售分析")
    ]
    
    for endpoint, name in apis_to_test:
        try:
            print(f"\n2. 测试{name}...")
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            if response.status_code == 200:
                print(f"   ✅ {name}: 正常")
            else:
                print(f"   ❌ {name}: 失败 ({response.status_code})")
        except Exception as e:
            print(f"   ❌ {name}: 异常 - {e}")
    
    print("\n📋 API测试完成")

if __name__ == "__main__":
    test_fixed_api()
