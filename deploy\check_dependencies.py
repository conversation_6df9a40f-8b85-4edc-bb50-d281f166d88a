#!/usr/bin/env python3
"""
依赖检查脚本

检查生产环境部署所需的所有依赖和配置
"""

import sys
import subprocess
import importlib
import platform
import logging
from pathlib import Path
from typing import List, Dict, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DependencyChecker:
    """依赖检查器"""
    
    def __init__(self):
        self.python_min_version = (3, 11)
        self.required_packages = {
            # 核心依赖
            "fastapi": "0.68.0",
            "uvicorn": "0.15.0",
            "streamlit": "1.28.0",
            "pandas": "1.5.0",
            "numpy": "1.21.0",
            
            # 机器学习
            "scikit-learn": "1.0.0",
            "xgboost": "1.5.0",
            "lightgbm": "3.3.0",
            
            # 可视化
            "plotly": "5.0.0",
            "matplotlib": "3.5.0",
            
            # 网络和异步
            "requests": "2.25.0",
            "websockets": "10.0",
            "aiofiles": "0.7.0",
            
            # 数据处理
            "python-dateutil": "2.8.0",
            "pytz": "2021.1"
        }
        
        self.optional_packages = {
            "psutil": "5.8.0",  # 系统监控
            "schedule": "1.1.0",  # 任务调度
            "python-dotenv": "0.19.0"  # 环境变量
        }
        
        self.system_requirements = {
            "min_memory_gb": 2,
            "min_disk_gb": 5,
            "recommended_memory_gb": 4,
            "recommended_disk_gb": 10
        }
    
    def check_python_version(self) -> Tuple[bool, str]:
        """检查Python版本"""
        current_version = sys.version_info[:2]
        
        if current_version >= self.python_min_version:
            return True, f"Python版本: {platform.python_version()} ✅"
        else:
            return False, f"Python版本过低: {platform.python_version()}, 需要 {'.'.join(map(str, self.python_min_version))}+ ❌"
    
    def check_package_version(self, package_name: str, min_version: str) -> Tuple[bool, str]:
        """检查单个包的版本"""
        try:
            module = importlib.import_module(package_name)
            
            # 获取版本号
            version = None
            for attr in ['__version__', 'version', 'VERSION']:
                if hasattr(module, attr):
                    version = getattr(module, attr)
                    break
            
            if version is None:
                return True, f"{package_name}: 已安装 (版本未知) ⚠️"
            
            # 简单版本比较
            try:
                from packaging import version as pkg_version
                if pkg_version.parse(str(version)) >= pkg_version.parse(min_version):
                    return True, f"{package_name}: {version} ✅"
                else:
                    return False, f"{package_name}: {version} < {min_version} ❌"
            except ImportError:
                # 如果packaging不可用，只检查是否安装
                return True, f"{package_name}: {version} (未验证版本) ⚠️"
                
        except ImportError:
            return False, f"{package_name}: 未安装 ❌"
        except Exception as e:
            return False, f"{package_name}: 检查失败 - {e} ❌"
    
    def check_required_packages(self) -> Tuple[bool, List[str]]:
        """检查必需包"""
        results = []
        all_passed = True
        
        logger.info("检查必需包...")
        
        for package, min_version in self.required_packages.items():
            passed, message = self.check_package_version(package, min_version)
            results.append(message)
            
            if not passed:
                all_passed = False
        
        return all_passed, results
    
    def check_optional_packages(self) -> Tuple[bool, List[str]]:
        """检查可选包"""
        results = []
        
        logger.info("检查可选包...")
        
        for package, min_version in self.optional_packages.items():
            passed, message = self.check_package_version(package, min_version)
            results.append(message)
        
        return True, results  # 可选包不影响整体结果
    
    def check_system_resources(self) -> Tuple[bool, List[str]]:
        """检查系统资源"""
        results = []
        all_passed = True
        
        logger.info("检查系统资源...")
        
        try:
            import psutil
            
            # 检查内存
            memory = psutil.virtual_memory()
            memory_gb = memory.total / (1024**3)
            
            if memory_gb >= self.system_requirements["recommended_memory_gb"]:
                results.append(f"内存: {memory_gb:.1f}GB (推荐) ✅")
            elif memory_gb >= self.system_requirements["min_memory_gb"]:
                results.append(f"内存: {memory_gb:.1f}GB (最低要求) ⚠️")
            else:
                results.append(f"内存: {memory_gb:.1f}GB (不足) ❌")
                all_passed = False
            
            # 检查磁盘空间
            disk = psutil.disk_usage('.')
            disk_free_gb = disk.free / (1024**3)
            
            if disk_free_gb >= self.system_requirements["recommended_disk_gb"]:
                results.append(f"磁盘空间: {disk_free_gb:.1f}GB (推荐) ✅")
            elif disk_free_gb >= self.system_requirements["min_disk_gb"]:
                results.append(f"磁盘空间: {disk_free_gb:.1f}GB (最低要求) ⚠️")
            else:
                results.append(f"磁盘空间: {disk_free_gb:.1f}GB (不足) ❌")
                all_passed = False
            
            # 检查CPU
            cpu_count = psutil.cpu_count()
            results.append(f"CPU核心数: {cpu_count} ✅")
            
        except ImportError:
            results.append("psutil未安装，无法检查系统资源 ⚠️")
        except Exception as e:
            results.append(f"系统资源检查失败: {e} ❌")
            all_passed = False
        
        return all_passed, results
    
    def check_file_structure(self) -> Tuple[bool, List[str]]:
        """检查文件结构"""
        results = []
        all_passed = True
        
        logger.info("检查文件结构...")
        
        required_files = [
            "src/api/production_main.py",
            "src/ui/main.py",
            "src/core/database_manager.py",
            "lottery_data.db",
            "requirements.txt"
        ]
        
        required_dirs = [
            "src",
            "src/api",
            "src/ui",
            "src/core",
            "data",
            "logs"
        ]
        
        # 检查文件
        for file_path in required_files:
            if Path(file_path).exists():
                results.append(f"文件: {file_path} ✅")
            else:
                results.append(f"文件: {file_path} (缺失) ❌")
                all_passed = False
        
        # 检查目录
        for dir_path in required_dirs:
            if Path(dir_path).exists():
                results.append(f"目录: {dir_path} ✅")
            else:
                results.append(f"目录: {dir_path} (缺失) ❌")
                all_passed = False
        
        return all_passed, results
    
    def check_network_connectivity(self) -> Tuple[bool, List[str]]:
        """检查网络连接"""
        results = []
        all_passed = True
        
        logger.info("检查网络连接...")
        
        test_urls = [
            "https://data.17500.cn/3d_asc.txt",
            "https://pypi.org/simple/",
            "https://www.python.org/"
        ]
        
        try:
            import requests
            
            for url in test_urls:
                try:
                    response = requests.head(url, timeout=10)
                    if response.status_code < 400:
                        results.append(f"网络连接: {url} ✅")
                    else:
                        results.append(f"网络连接: {url} (状态码: {response.status_code}) ⚠️")
                except Exception as e:
                    results.append(f"网络连接: {url} (失败: {e}) ❌")
                    all_passed = False
                    
        except ImportError:
            results.append("requests未安装，无法检查网络连接 ⚠️")
        
        return all_passed, results
    
    def generate_installation_commands(self) -> List[str]:
        """生成安装命令"""
        commands = []
        
        # 检查缺失的包
        missing_packages = []
        
        for package, min_version in self.required_packages.items():
            passed, _ = self.check_package_version(package, min_version)
            if not passed:
                missing_packages.append(f"{package}>={min_version}")
        
        if missing_packages:
            commands.append("# 安装缺失的必需包:")
            commands.append(f"pip install {' '.join(missing_packages)}")
            commands.append("")
        
        # 可选包安装命令
        optional_missing = []
        for package, min_version in self.optional_packages.items():
            passed, _ = self.check_package_version(package, min_version)
            if not passed:
                optional_missing.append(f"{package}>={min_version}")
        
        if optional_missing:
            commands.append("# 安装可选包 (推荐):")
            commands.append(f"pip install {' '.join(optional_missing)}")
            commands.append("")
        
        # 通用安装命令
        commands.append("# 或者使用requirements.txt安装所有依赖:")
        commands.append("pip install -r requirements.txt")
        
        return commands
    
    def run_full_check(self) -> bool:
        """运行完整检查"""
        logger.info("🔍 开始依赖检查...")
        
        all_checks_passed = True
        
        # 检查项目列表
        checks = [
            ("Python版本", self.check_python_version),
            ("必需包", self.check_required_packages),
            ("可选包", self.check_optional_packages),
            ("系统资源", self.check_system_resources),
            ("文件结构", self.check_file_structure),
            ("网络连接", self.check_network_connectivity)
        ]
        
        # 执行所有检查
        for check_name, check_func in checks:
            logger.info(f"\n--- {check_name} ---")
            
            try:
                if check_name == "Python版本":
                    passed, message = check_func()
                    print(message)
                    if not passed:
                        all_checks_passed = False
                else:
                    passed, messages = check_func()
                    for message in messages:
                        print(message)
                    if not passed:
                        all_checks_passed = False
                        
            except Exception as e:
                logger.error(f"检查失败 {check_name}: {e}")
                all_checks_passed = False
        
        # 生成安装建议
        if not all_checks_passed:
            logger.info("\n--- 安装建议 ---")
            commands = self.generate_installation_commands()
            for command in commands:
                print(command)
        
        # 输出总结
        logger.info("\n" + "="*50)
        if all_checks_passed:
            logger.info("✅ 所有依赖检查通过！系统已准备好部署。")
        else:
            logger.info("❌ 部分检查未通过，请根据上述建议进行修复。")
        logger.info("="*50)
        
        return all_checks_passed

def main():
    """主函数"""
    checker = DependencyChecker()
    
    try:
        success = checker.run_full_check()
        return 0 if success else 1
        
    except KeyboardInterrupt:
        logger.info("检查被用户中断")
        return 1
    except Exception as e:
        logger.error(f"检查失败: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
