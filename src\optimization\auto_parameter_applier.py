#!/usr/bin/env python3
"""
自动参数应用系统
Auto Parameter Applier

自动应用优化后的参数到模型中
"""

import json
import logging
import os
import shutil
import sys
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from optimization.parameter_backtesting_engine import BacktestingResult


@dataclass
class ParameterApplicationResult:
    """参数应用结果"""
    model_name: str
    success: bool
    old_parameters: Dict[str, Any]
    new_parameters: Dict[str, Any]
    validation_result: Dict[str, Any]
    rollback_available: bool
    error_message: Optional[str] = None
    application_timestamp: datetime = None


class ModelConfigManager:
    """模型配置管理器"""
    
    def __init__(self, config_dir: str = "config/models"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        self.logger = logging.getLogger(__name__)
    
    async def get_model_parameters(self, model_name: str) -> Dict[str, Any]:
        """获取模型参数"""
        config_file = self.config_dir / f"{model_name}.json"
        
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # 返回默认参数
            return self._get_default_parameters(model_name)
    
    async def update_model_parameters(self, model_name: str, parameters: Dict[str, Any]) -> bool:
        """更新模型参数"""
        try:
            config_file = self.config_dir / f"{model_name}.json"
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(parameters, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"模型 {model_name} 参数更新成功")
            return True
            
        except Exception as e:
            self.logger.error(f"更新模型参数失败: {e}")
            return False
    
    def _get_default_parameters(self, model_name: str) -> Dict[str, Any]:
        """获取默认参数"""
        default_params = {
            'markov_enhanced': {
                'order': 2,
                'smoothing_factor': 0.1,
                'window_size': 30,
                'weight_decay': 0.5
            },
            'deep_learning_cnn_lstm': {
                'learning_rate': 0.001,
                'batch_size': 32,
                'hidden_units': 128,
                'dropout_rate': 0.2,
                'sequence_length': 20
            },
            'trend_analyzer': {
                'trend_window': 15,
                'trend_weight': 0.4,
                'momentum_factor': 0.6,
                'volatility_threshold': 0.3
            },
            'intelligent_fusion': {
                'fusion_method': 'weighted_average',
                'confidence_threshold': 0.5,
                'diversity_weight': 0.3,
                'performance_weight': 0.6
            }
        }
        
        return default_params.get(model_name, {})


class ParameterBackupManager:
    """参数备份管理器"""
    
    def __init__(self, backup_dir: str = "backups/parameters"):
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        self.logger = logging.getLogger(__name__)
    
    async def backup_current_parameters(self, model_name: str) -> Dict[str, Any]:
        """备份当前参数"""
        try:
            config_manager = ModelConfigManager()
            current_params = await config_manager.get_model_parameters(model_name)
            
            # 创建备份文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.backup_dir / f"{model_name}_{timestamp}.json"
            
            backup_data = {
                'model_name': model_name,
                'parameters': current_params,
                'backup_time': datetime.now().isoformat(),
                'backup_reason': 'parameter_optimization'
            }
            
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"参数备份成功: {backup_file}")
            return current_params
            
        except Exception as e:
            self.logger.error(f"参数备份失败: {e}")
            return {}
    
    async def rollback_parameters(self, model_name: str, backup_timestamp: Optional[str] = None) -> bool:
        """回滚参数"""
        try:
            if backup_timestamp:
                backup_file = self.backup_dir / f"{model_name}_{backup_timestamp}.json"
            else:
                # 找到最新的备份文件
                backup_files = list(self.backup_dir.glob(f"{model_name}_*.json"))
                if not backup_files:
                    self.logger.error(f"未找到模型 {model_name} 的备份文件")
                    return False
                
                backup_file = max(backup_files, key=lambda f: f.stat().st_mtime)
            
            if not backup_file.exists():
                self.logger.error(f"备份文件不存在: {backup_file}")
                return False
            
            # 读取备份参数
            with open(backup_file, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            
            # 恢复参数
            config_manager = ModelConfigManager()
            success = await config_manager.update_model_parameters(
                model_name, backup_data['parameters']
            )
            
            if success:
                self.logger.info(f"参数回滚成功: {model_name}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"参数回滚失败: {e}")
            return False


class AutoParameterApplier:
    """自动参数应用系统"""
    
    def __init__(self):
        """初始化自动参数应用系统"""
        self.logger = logging.getLogger(__name__)
        self.config_manager = ModelConfigManager()
        self.backup_manager = ParameterBackupManager()
    
    async def apply_optimizations(self, optimization_results: Dict[str, BacktestingResult]) -> Dict[str, ParameterApplicationResult]:
        """
        应用优化结果到模型
        
        Args:
            optimization_results: 优化结果字典
            
        Returns:
            应用结果字典
        """
        application_results = {}
        
        for model_name, optimization in optimization_results.items():
            try:
                self.logger.info(f"开始应用优化参数: {model_name}")
                
                # 1. 备份当前参数
                current_params = await self.backup_manager.backup_current_parameters(model_name)
                
                # 2. 验证新参数
                new_params = optimization.optimal_parameters
                validation_result = await self._validate_parameter_application(model_name, new_params)
                
                if not validation_result['is_valid']:
                    application_results[model_name] = ParameterApplicationResult(
                        model_name=model_name,
                        success=False,
                        old_parameters=current_params,
                        new_parameters=new_params,
                        validation_result=validation_result,
                        rollback_available=True,
                        error_message=validation_result.get('error_message', '参数验证失败'),
                        application_timestamp=datetime.now()
                    )
                    continue
                
                # 3. 应用新参数
                success = await self.config_manager.update_model_parameters(model_name, new_params)
                
                if success:
                    # 4. 验证应用结果
                    post_validation = await self._post_application_validation(model_name, new_params)
                    
                    application_results[model_name] = ParameterApplicationResult(
                        model_name=model_name,
                        success=post_validation['success'],
                        old_parameters=current_params,
                        new_parameters=new_params,
                        validation_result=post_validation,
                        rollback_available=True,
                        application_timestamp=datetime.now()
                    )
                    
                    if not post_validation['success']:
                        # 应用后验证失败，自动回滚
                        await self.backup_manager.rollback_parameters(model_name)
                        self.logger.warning(f"参数应用后验证失败，已自动回滚: {model_name}")
                else:
                    application_results[model_name] = ParameterApplicationResult(
                        model_name=model_name,
                        success=False,
                        old_parameters=current_params,
                        new_parameters=new_params,
                        validation_result={'success': False, 'error': '参数更新失败'},
                        rollback_available=True,
                        error_message='参数文件更新失败',
                        application_timestamp=datetime.now()
                    )
                
            except Exception as e:
                self.logger.error(f"应用优化参数失败 {model_name}: {e}")
                
                # 尝试回滚
                try:
                    await self.backup_manager.rollback_parameters(model_name)
                    rollback_success = True
                except:
                    rollback_success = False
                
                application_results[model_name] = ParameterApplicationResult(
                    model_name=model_name,
                    success=False,
                    old_parameters={},
                    new_parameters={},
                    validation_result={'success': False, 'error': str(e)},
                    rollback_available=rollback_success,
                    error_message=str(e),
                    application_timestamp=datetime.now()
                )
        
        return application_results
    
    async def _validate_parameter_application(self, model_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """验证参数应用前的有效性"""
        validation_result = {
            'is_valid': True,
            'validation_checks': [],
            'warnings': [],
            'error_message': None
        }
        
        try:
            # 1. 参数类型检查
            type_check = self._validate_parameter_types(model_name, parameters)
            validation_result['validation_checks'].append(type_check)
            
            if not type_check['passed']:
                validation_result['is_valid'] = False
                validation_result['error_message'] = type_check['message']
                return validation_result
            
            # 2. 参数范围检查
            range_check = self._validate_parameter_ranges(model_name, parameters)
            validation_result['validation_checks'].append(range_check)
            
            if not range_check['passed']:
                validation_result['is_valid'] = False
                validation_result['error_message'] = range_check['message']
                return validation_result
            
            # 3. 参数兼容性检查
            compatibility_check = self._validate_parameter_compatibility(model_name, parameters)
            validation_result['validation_checks'].append(compatibility_check)
            
            if not compatibility_check['passed']:
                validation_result['warnings'].append(compatibility_check['message'])
            
            self.logger.info(f"参数验证通过: {model_name}")
            
        except Exception as e:
            validation_result['is_valid'] = False
            validation_result['error_message'] = f"参数验证异常: {e}"
            self.logger.error(f"参数验证异常: {e}")
        
        return validation_result
    
    def _validate_parameter_types(self, model_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """验证参数类型"""
        expected_types = {
            'markov_enhanced': {
                'order': int,
                'smoothing_factor': (int, float),
                'window_size': int,
                'weight_decay': (int, float)
            },
            'deep_learning_cnn_lstm': {
                'learning_rate': (int, float),
                'batch_size': int,
                'hidden_units': int,
                'dropout_rate': (int, float),
                'sequence_length': int
            }
        }
        
        model_types = expected_types.get(model_name, {})
        
        for param_name, param_value in parameters.items():
            if param_name in model_types:
                expected_type = model_types[param_name]
                if not isinstance(param_value, expected_type):
                    return {
                        'passed': False,
                        'message': f"参数 {param_name} 类型错误，期望 {expected_type}，实际 {type(param_value)}"
                    }
        
        return {'passed': True, 'message': '参数类型检查通过'}
    
    def _validate_parameter_ranges(self, model_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """验证参数范围"""
        parameter_ranges = {
            'markov_enhanced': {
                'order': (1, 10),
                'smoothing_factor': (0.001, 1.0),
                'window_size': (5, 200),
                'weight_decay': (0.0, 1.0)
            },
            'deep_learning_cnn_lstm': {
                'learning_rate': (0.0001, 0.1),
                'batch_size': (8, 256),
                'hidden_units': (16, 512),
                'dropout_rate': (0.0, 0.8),
                'sequence_length': (5, 100)
            }
        }
        
        model_ranges = parameter_ranges.get(model_name, {})
        
        for param_name, param_value in parameters.items():
            if param_name in model_ranges:
                min_val, max_val = model_ranges[param_name]
                if not (min_val <= param_value <= max_val):
                    return {
                        'passed': False,
                        'message': f"参数 {param_name} 超出范围，期望 [{min_val}, {max_val}]，实际 {param_value}"
                    }
        
        return {'passed': True, 'message': '参数范围检查通过'}
    
    def _validate_parameter_compatibility(self, model_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """验证参数兼容性"""
        # 检查参数间的兼容性
        if model_name == 'deep_learning_cnn_lstm':
            batch_size = parameters.get('batch_size', 32)
            sequence_length = parameters.get('sequence_length', 20)
            
            if batch_size * sequence_length > 10000:
                return {
                    'passed': False,
                    'message': f"batch_size * sequence_length 过大，可能导致内存不足"
                }
        
        return {'passed': True, 'message': '参数兼容性检查通过'}
    
    async def _post_application_validation(self, model_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """应用后验证"""
        validation_result = {
            'success': True,
            'checks': [],
            'performance_impact': {}
        }
        
        try:
            # 1. 验证参数文件是否正确写入
            current_params = await self.config_manager.get_model_parameters(model_name)
            
            if current_params != parameters:
                validation_result['success'] = False
                validation_result['checks'].append('参数文件写入验证失败')
                return validation_result
            
            # 2. 模拟性能影响评估
            validation_result['performance_impact'] = {
                'estimated_accuracy_change': 0.05,  # 模拟准确率变化
                'estimated_speed_change': -0.02,    # 模拟速度变化
                'memory_usage_change': 0.1          # 模拟内存使用变化
            }
            
            validation_result['checks'].append('参数应用验证通过')
            
        except Exception as e:
            validation_result['success'] = False
            validation_result['checks'].append(f'应用后验证异常: {e}')
        
        return validation_result


if __name__ == "__main__":
    # 测试代码
    import asyncio
    
    async def test_parameter_application():
        applier = AutoParameterApplier()
        
        # 模拟优化结果
        from .parameter_backtesting_engine import BacktestingResult
        
        mock_result = BacktestingResult(
            model_name="test_model",
            target_period="2025194",
            actual_result="123",
            optimal_parameters={
                'order': 3,
                'smoothing_factor': 0.2,
                'window_size': 25,
                'weight_decay': 0.6
            },
            optimization_process={},
            validation_results={},
            improvement_metrics={},
            parameter_sensitivity={},
            confidence_score=0.8,
            analysis_timestamp=datetime.now()
        )
        
        optimization_results = {"test_model": mock_result}
        
        # 应用优化
        results = await applier.apply_optimizations(optimization_results)
        
        print("参数应用结果:")
        for model_name, result in results.items():
            print(f"模型: {model_name}")
            print(f"  成功: {result.success}")
            print(f"  新参数: {result.new_parameters}")
            if result.error_message:
                print(f"  错误: {result.error_message}")
    
    # 运行测试
    asyncio.run(test_parameter_application())
