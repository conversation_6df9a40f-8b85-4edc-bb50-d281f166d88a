#!/usr/bin/env python3
"""
调度器启动包装脚本
提供更好的错误处理和日志记录
"""

import sys
import os
import subprocess
import time
from pathlib import Path

def main():
    """主函数"""
    print("🚀 启动福彩3D调度器...")
    
    # 设置工作目录
    script_dir = Path(__file__).parent
    project_dir = script_dir.parent
    os.chdir(project_dir)
    
    try:
        # 启动调度器
        process = subprocess.Popen(
            [sys.executable, "scripts/start_scheduler.py", "--daemon"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=project_dir
        )
        
        # 等待启动
        time.sleep(5)
        
        # 检查进程状态
        if process.poll() is None:
            print("✅ 调度器启动成功")
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ 调度器启动失败:")
            if stdout:
                print(f"标准输出: {stdout}")
            if stderr:
                print(f"错误输出: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 启动异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
