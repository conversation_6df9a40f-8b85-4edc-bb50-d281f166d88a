"""
增强版马尔可夫链模块

实现二阶马尔可夫链和自适应阶数选择
"""

import hashlib
import json
import multiprocessing
import os
import pickle
import sqlite3
from collections import Counter, defaultdict
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
from scipy import sparse

try:
    from .model_validation import MarkovCrossValidator
except ImportError:
    from model_validation import MarkovCrossValidator


class EnhancedMarkovChain:
    """增强版马尔可夫链类，支持一阶和二阶马尔可夫链"""
    
    def __init__(self, db_path: str = None, max_order: int = 2):
        """
        初始化增强版马尔可夫链
        
        Args:
            db_path: 数据库路径
            max_order: 最大阶数（1或2）
        """
        self.db_path = db_path or os.path.join('data', 'lottery.db')
        self.max_order = min(max_order, 2)  # 限制最大阶数为2
        
        # 转移矩阵
        self.first_order_matrix = {}  # 一阶转移矩阵 10x10
        self.second_order_matrix = {}  # 二阶转移矩阵 100x10

        # 稀疏矩阵表示
        self.sparse_matrices = {
            'first_order': {},  # 位置 -> 稀疏矩阵 (10x10)
            'second_order': {}  # 位置 -> 稀疏矩阵 (100x10)
        }

        # 状态计数
        self.first_order_counts = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))
        self.second_order_counts = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))
        
        # 模型参数
        self.smoothing_alpha = 1.0
        self.window_size = 1000
        
        # 训练状态
        self.is_trained = False
        self.training_data_size = 0
        self.last_training_time = None
        
        # 性能缓存
        self.cache_dir = os.path.join('data', 'markov_cache')
        os.makedirs(self.cache_dir, exist_ok=True)

        # 缓存设置
        self.use_cache = True
        self.cache_version = "v1.0"
        self.prediction_cache = {}

        # 并行处理设置
        self.use_parallel = True
        self.max_workers = min(multiprocessing.cpu_count(), 4)  # 最多使用4个核心

        # 状态空间定义
        self.first_order_states = list(range(10))  # 0-9
        self.second_order_states = [(i, j) for i in range(10) for j in range(10)]  # (0,0)-(9,9)

    def get_state_space_info(self) -> Dict[str, Any]:
        """
        获取状态空间信息

        Returns:
            状态空间信息
        """
        return {
            'first_order': {
                'size': len(self.first_order_states),
                'states': self.first_order_states,
                'description': '单数字状态 (0-9)'
            },
            'second_order': {
                'size': len(self.second_order_states),
                'states': self.second_order_states[:10],  # 只显示前10个作为示例
                'description': '数字对状态 (digit_t-1, digit_t)',
                'total_states': len(self.second_order_states)
            }
        }

    def encode_second_order_state(self, prev_digit: int, current_digit: int) -> int:
        """
        将二阶状态编码为单一索引

        Args:
            prev_digit: 前一个数字
            current_digit: 当前数字

        Returns:
            状态索引 (0-99)
        """
        return prev_digit * 10 + current_digit

    def decode_second_order_state(self, state_index: int) -> Tuple[int, int]:
        """
        将状态索引解码为数字对

        Args:
            state_index: 状态索引

        Returns:
            (prev_digit, current_digit)
        """
        prev_digit = state_index // 10
        current_digit = state_index % 10
        return prev_digit, current_digit
    
    def load_training_data(self, limit: int = None) -> List[str]:
        """
        从数据库加载训练数据
        
        Args:
            limit: 数据限制
            
        Returns:
            开奖号码列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 按期号排序获取历史数据
            if limit:
                query = """
                SELECT numbers FROM lottery_records 
                ORDER BY period_number 
                LIMIT ?
                """
                cursor.execute(query, (limit,))
            else:
                query = """
                SELECT numbers FROM lottery_records 
                ORDER BY period_number
                """
                cursor.execute(query)
            
            records = cursor.fetchall()
            conn.close()
            
            # 提取号码
            numbers_list = []
            for record in records:
                numbers = record[0]
                if len(numbers) == 3 and numbers.isdigit():
                    numbers_list.append(numbers)
            
            print(f"加载了 {len(numbers_list)} 条训练数据")
            return numbers_list
            
        except Exception as e:
            print(f"加载训练数据失败: {e}")
            return []
    
    def build_first_order_matrix(self, data: List[str]):
        """
        构建一阶转移矩阵
        
        Args:
            data: 训练数据
        """
        print("构建一阶转移矩阵...")
        
        # 重置计数
        self.first_order_counts = defaultdict(lambda: defaultdict(int))
        
        # 统计转移
        for i in range(len(data) - 1):
            current_numbers = data[i]
            next_numbers = data[i + 1]
            
            # 对每个位置进行统计
            for pos in range(3):
                current_digit = int(current_numbers[pos])
                next_digit = int(next_numbers[pos])
                
                self.first_order_counts[pos][current_digit][next_digit] += 1
        
        # 构建概率矩阵
        self.first_order_matrix = {}
        for pos in range(3):
            self.first_order_matrix[pos] = {}
            
            for current_digit in range(10):
                total_count = sum(self.first_order_counts[pos][current_digit].values())
                
                if total_count == 0:
                    # 使用拉普拉斯平滑
                    prob = 1.0 / 10
                    self.first_order_matrix[pos][current_digit] = [prob] * 10
                else:
                    # 应用拉普拉斯平滑
                    probs = []
                    for next_digit in range(10):
                        count = self.first_order_counts[pos][current_digit][next_digit]
                        prob = (count + self.smoothing_alpha) / (total_count + self.smoothing_alpha * 10)
                        probs.append(prob)
                    
                    self.first_order_matrix[pos][current_digit] = probs

        # 构建稀疏矩阵表示
        self._build_sparse_first_order_matrices()

    def _build_sparse_first_order_matrices(self):
        """构建一阶转移的稀疏矩阵"""
        for pos in range(3):
            # 创建10x10的稀疏矩阵
            rows, cols, data = [], [], []

            for current_digit in range(10):
                for next_digit in range(10):
                    prob = self.first_order_matrix[pos][current_digit][next_digit]
                    if prob > 1e-10:  # 只存储非零概率
                        rows.append(current_digit)
                        cols.append(next_digit)
                        data.append(prob)

            # 创建稀疏矩阵
            self.sparse_matrices['first_order'][pos] = sparse.csr_matrix(
                (data, (rows, cols)), shape=(10, 10)
            )
    
    def build_second_order_matrix(self, data: List[str]):
        """
        构建二阶转移矩阵
        
        Args:
            data: 训练数据
        """
        print("构建二阶转移矩阵...")
        
        # 重置计数
        self.second_order_counts = defaultdict(lambda: defaultdict(int))
        
        # 统计二阶转移
        for i in range(len(data) - 2):
            prev_numbers = data[i]
            current_numbers = data[i + 1]
            next_numbers = data[i + 2]
            
            # 对每个位置进行统计
            for pos in range(3):
                prev_digit = int(prev_numbers[pos])
                current_digit = int(current_numbers[pos])
                next_digit = int(next_numbers[pos])
                
                # 二阶状态：(prev_digit, current_digit)
                state = (prev_digit, current_digit)
                self.second_order_counts[pos][state][next_digit] += 1
        
        # 构建概率矩阵
        self.second_order_matrix = {}
        for pos in range(3):
            self.second_order_matrix[pos] = {}
            
            # 遍历所有可能的二阶状态
            for prev_digit in range(10):
                for current_digit in range(10):
                    state = (prev_digit, current_digit)
                    total_count = sum(self.second_order_counts[pos][state].values())
                    
                    if total_count == 0:
                        # 使用拉普拉斯平滑
                        prob = 1.0 / 10
                        self.second_order_matrix[pos][state] = [prob] * 10
                    else:
                        # 应用拉普拉斯平滑
                        probs = []
                        for next_digit in range(10):
                            count = self.second_order_counts[pos][state][next_digit]
                            prob = (count + self.smoothing_alpha) / (total_count + self.smoothing_alpha * 10)
                            probs.append(prob)
                        
                        self.second_order_matrix[pos][state] = probs

        # 构建稀疏矩阵表示
        self._build_sparse_second_order_matrices()

    def _build_sparse_second_order_matrices(self):
        """构建二阶转移的稀疏矩阵"""
        for pos in range(3):
            # 创建100x10的稀疏矩阵
            rows, cols, data = [], [], []

            for prev_digit in range(10):
                for current_digit in range(10):
                    state = (prev_digit, current_digit)
                    state_index = self.encode_second_order_state(prev_digit, current_digit)

                    if state in self.second_order_matrix[pos]:
                        for next_digit in range(10):
                            prob = self.second_order_matrix[pos][state][next_digit]
                            if prob > 1e-10:  # 只存储非零概率
                                rows.append(state_index)
                                cols.append(next_digit)
                                data.append(prob)

            # 创建稀疏矩阵
            self.sparse_matrices['second_order'][pos] = sparse.csr_matrix(
                (data, (rows, cols)), shape=(100, 10)
            )
    
    def train(self, data_limit: int = None):
        """
        训练马尔可夫链模型
        
        Args:
            data_limit: 数据限制
        """
        print(f"开始训练增强版马尔可夫链（最大阶数: {self.max_order}）...")
        
        # 加载训练数据
        data = self.load_training_data(data_limit)
        if len(data) < 10:
            raise ValueError("训练数据不足")
        
        self.training_data_size = len(data)
        
        # 构建一阶转移矩阵
        self.build_first_order_matrix(data)
        
        # 如果支持二阶，构建二阶转移矩阵
        if self.max_order >= 2:
            self.build_second_order_matrix(data)
        
        self.is_trained = True
        self.last_training_time = datetime.now()
        
        print(f"✅ 增强版马尔可夫链训练完成！")
        print(f"   训练数据量: {self.training_data_size}")
        print(f"   支持阶数: 1-{self.max_order}")
    
    def predict_first_order(self, current_numbers: str) -> Dict[str, Any]:
        """
        使用一阶马尔可夫链预测
        
        Args:
            current_numbers: 当前号码
            
        Returns:
            预测结果
        """
        if not self.is_trained:
            raise ValueError("模型未训练")
        
        predictions = []
        
        for pos in range(3):
            current_digit = int(current_numbers[pos])
            probs = self.first_order_matrix[pos][current_digit]
            
            # 根据概率选择下一个数字
            next_digit = np.random.choice(10, p=probs)
            predictions.append(str(next_digit))
        
        return {
            'numbers': ''.join(predictions),
            'method': 'first_order',
            'confidence': 0.3  # 一阶模型基础置信度
        }
    
    def predict_second_order(self, prev_numbers: str, current_numbers: str) -> Dict[str, Any]:
        """
        使用二阶马尔可夫链预测
        
        Args:
            prev_numbers: 前一期号码
            current_numbers: 当前号码
            
        Returns:
            预测结果
        """
        if not self.is_trained or self.max_order < 2:
            raise ValueError("二阶模型未训练或不支持")
        
        predictions = []
        
        for pos in range(3):
            prev_digit = int(prev_numbers[pos])
            current_digit = int(current_numbers[pos])
            state = (prev_digit, current_digit)
            
            if state in self.second_order_matrix[pos]:
                probs = self.second_order_matrix[pos][state]
            else:
                # 回退到一阶模型
                probs = self.first_order_matrix[pos][current_digit]
            
            # 根据概率选择下一个数字
            next_digit = np.random.choice(10, p=probs)
            predictions.append(str(next_digit))
        
        return {
            'numbers': ''.join(predictions),
            'method': 'second_order',
            'confidence': 0.5  # 二阶模型更高置信度
        }
    
    def predict_adaptive(self, history: List[str]) -> Dict[str, Any]:
        """
        自适应预测，根据数据情况选择最优阶数
        
        Args:
            history: 历史号码列表
            
        Returns:
            预测结果
        """
        if not self.is_trained:
            raise ValueError("模型未训练")
        
        if len(history) < 1:
            raise ValueError("历史数据不足")
        
        current_numbers = history[-1]
        
        # 如果有足够的历史数据且支持二阶，使用二阶模型
        if len(history) >= 2 and self.max_order >= 2:
            prev_numbers = history[-2]
            return self.predict_second_order(prev_numbers, current_numbers)
        else:
            # 使用一阶模型
            return self.predict_first_order(current_numbers)
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            模型信息
        """
        return {
            'max_order': self.max_order,
            'is_trained': self.is_trained,
            'training_data_size': self.training_data_size,
            'last_training_time': self.last_training_time.isoformat() if self.last_training_time else None,
            'smoothing_alpha': self.smoothing_alpha,
            'window_size': self.window_size,
            'first_order_states': len(self.first_order_matrix.get(0, {})),
            'second_order_states': len(self.second_order_matrix.get(0, {})) if self.max_order >= 2 else 0,
            'sparse_matrix_info': {
                'first_order_nnz': sum(matrix.nnz for matrix in self.sparse_matrices['first_order'].values()),
                'second_order_nnz': sum(matrix.nnz for matrix in self.sparse_matrices['second_order'].values()) if self.max_order >= 2 else 0,
                'memory_efficiency': 'Using sparse matrices for memory optimization'
            }
        }

    def get_sparse_matrix_stats(self) -> Dict[str, Any]:
        """
        获取稀疏矩阵统计信息

        Returns:
            稀疏矩阵统计
        """
        stats = {
            'first_order': {},
            'second_order': {}
        }

        # 一阶矩阵统计
        for pos in range(3):
            if pos in self.sparse_matrices['first_order']:
                matrix = self.sparse_matrices['first_order'][pos]
                stats['first_order'][f'position_{pos}'] = {
                    'shape': matrix.shape,
                    'nnz': matrix.nnz,
                    'density': matrix.nnz / (matrix.shape[0] * matrix.shape[1]),
                    'memory_usage': matrix.data.nbytes + matrix.indices.nbytes + matrix.indptr.nbytes
                }

        # 二阶矩阵统计
        if self.max_order >= 2:
            for pos in range(3):
                if pos in self.sparse_matrices['second_order']:
                    matrix = self.sparse_matrices['second_order'][pos]
                    stats['second_order'][f'position_{pos}'] = {
                        'shape': matrix.shape,
                        'nnz': matrix.nnz,
                        'density': matrix.nnz / (matrix.shape[0] * matrix.shape[1]),
                        'memory_usage': matrix.data.nbytes + matrix.indices.nbytes + matrix.indptr.nbytes
                    }

        return stats

    def calculate_aic_bic(self, data: List[str], order: int) -> Tuple[float, float]:
        """
        计算指定阶数的AIC和BIC值

        Args:
            data: 训练数据
            order: 马尔可夫链阶数

        Returns:
            (AIC, BIC)
        """
        if order == 1:
            # 一阶模型参数数量：3个位置 × 10个状态 × 10个转移 = 300
            num_params = 3 * 10 * 10
            log_likelihood = self._calculate_first_order_likelihood(data)
        elif order == 2:
            # 二阶模型参数数量：3个位置 × 100个状态 × 10个转移 = 3000
            num_params = 3 * 100 * 10
            log_likelihood = self._calculate_second_order_likelihood(data)
        else:
            raise ValueError(f"不支持的阶数: {order}")

        n = len(data) - order  # 有效样本数

        # 计算AIC和BIC
        aic = 2 * num_params - 2 * log_likelihood
        bic = np.log(n) * num_params - 2 * log_likelihood

        return aic, bic

    def _calculate_first_order_likelihood(self, data: List[str]) -> float:
        """计算一阶模型的对数似然"""
        log_likelihood = 0.0

        for i in range(len(data) - 1):
            current_numbers = data[i]
            next_numbers = data[i + 1]

            for pos in range(3):
                current_digit = int(current_numbers[pos])
                next_digit = int(next_numbers[pos])

                if current_digit in self.first_order_matrix[pos]:
                    prob = self.first_order_matrix[pos][current_digit][next_digit]
                    log_likelihood += np.log(max(prob, 1e-10))  # 避免log(0)

        return log_likelihood

    def _calculate_second_order_likelihood(self, data: List[str]) -> float:
        """计算二阶模型的对数似然"""
        log_likelihood = 0.0

        for i in range(len(data) - 2):
            prev_numbers = data[i]
            current_numbers = data[i + 1]
            next_numbers = data[i + 2]

            for pos in range(3):
                prev_digit = int(prev_numbers[pos])
                current_digit = int(current_numbers[pos])
                next_digit = int(next_numbers[pos])

                state = (prev_digit, current_digit)

                if state in self.second_order_matrix[pos]:
                    prob = self.second_order_matrix[pos][state][next_digit]
                    log_likelihood += np.log(max(prob, 1e-10))  # 避免log(0)
                else:
                    # 回退到一阶模型
                    if current_digit in self.first_order_matrix[pos]:
                        prob = self.first_order_matrix[pos][current_digit][next_digit]
                        log_likelihood += np.log(max(prob, 1e-10))

        return log_likelihood

    def select_optimal_order(self, data: List[str]) -> Dict[str, Any]:
        """
        基于AIC/BIC准则选择最优阶数

        Args:
            data: 训练数据

        Returns:
            选择结果
        """
        print("🔍 开始自适应阶数选择...")

        results = {}

        # 评估一阶模型
        if len(data) >= 10:
            aic1, bic1 = self.calculate_aic_bic(data, 1)
            results[1] = {'aic': aic1, 'bic': bic1}
            print(f"   一阶模型 - AIC: {aic1:.2f}, BIC: {bic1:.2f}")

        # 评估二阶模型（如果数据足够且支持）
        if len(data) >= 50 and self.max_order >= 2:
            aic2, bic2 = self.calculate_aic_bic(data, 2)
            results[2] = {'aic': aic2, 'bic': bic2}
            print(f"   二阶模型 - AIC: {aic2:.2f}, BIC: {bic2:.2f}")

        # 选择最优阶数
        if len(results) == 1:
            optimal_order = list(results.keys())[0]
            criterion = "数据量限制"
        else:
            # 基于AIC选择
            aic_optimal = min(results.keys(), key=lambda k: results[k]['aic'])
            # 基于BIC选择
            bic_optimal = min(results.keys(), key=lambda k: results[k]['bic'])

            # 如果AIC和BIC一致，选择该阶数；否则选择较低阶数（更保守）
            if aic_optimal == bic_optimal:
                optimal_order = aic_optimal
                criterion = "AIC和BIC一致"
            else:
                optimal_order = min(aic_optimal, bic_optimal)
                criterion = "保守选择（较低阶数）"

        result = {
            'optimal_order': optimal_order,
            'criterion': criterion,
            'all_results': results,
            'recommendation': f"建议使用{optimal_order}阶马尔可夫链"
        }

        print(f"✅ 最优阶数选择完成: {optimal_order}阶 ({criterion})")
        return result

    def predict_hybrid(self, history: List[str], alpha: float = 0.7) -> Dict[str, Any]:
        """
        混合模型预测，结合一阶和二阶马尔可夫链

        Args:
            history: 历史号码列表
            alpha: 二阶模型权重（0-1），一阶模型权重为(1-alpha)

        Returns:
            预测结果
        """
        if not self.is_trained:
            raise ValueError("模型未训练")

        if len(history) < 1:
            raise ValueError("历史数据不足")

        current_numbers = history[-1]
        predictions = []
        confidence_scores = []

        for pos in range(3):
            current_digit = int(current_numbers[pos])

            # 获取一阶预测概率
            first_order_probs = np.array(self.first_order_matrix[pos][current_digit])

            # 获取二阶预测概率（如果可用）
            if len(history) >= 2 and self.max_order >= 2:
                prev_numbers = history[-2]
                prev_digit = int(prev_numbers[pos])
                state = (prev_digit, current_digit)

                if state in self.second_order_matrix[pos]:
                    second_order_probs = np.array(self.second_order_matrix[pos][state])

                    # 混合概率
                    mixed_probs = alpha * second_order_probs + (1 - alpha) * first_order_probs
                    confidence = 0.4 + 0.3 * alpha  # 混合模型有更高置信度
                else:
                    # 二阶状态不存在，使用一阶模型
                    mixed_probs = first_order_probs
                    confidence = 0.3
            else:
                # 只能使用一阶模型
                mixed_probs = first_order_probs
                confidence = 0.3

            # 确保概率和为1
            mixed_probs = mixed_probs / np.sum(mixed_probs)

            # 根据概率选择数字
            next_digit = np.random.choice(10, p=mixed_probs)
            predictions.append(str(next_digit))
            confidence_scores.append(confidence)

        return {
            'numbers': ''.join(predictions),
            'method': 'hybrid',
            'confidence': np.mean(confidence_scores),
            'alpha': alpha,
            'details': {
                'position_confidences': confidence_scores,
                'used_second_order': len(history) >= 2 and self.max_order >= 2
            }
        }

    def adaptive_weight_prediction(self, history: List[str],
                                 performance_history: List[float] = None) -> Dict[str, Any]:
        """
        自适应权重预测，根据历史性能动态调整权重

        Args:
            history: 历史号码列表
            performance_history: 历史性能记录（准确率）

        Returns:
            预测结果
        """
        if not self.is_trained:
            raise ValueError("模型未训练")

        # 根据历史性能调整权重
        if performance_history and len(performance_history) >= 5:
            # 计算最近性能趋势
            recent_performance = np.mean(performance_history[-5:])

            if recent_performance > 0.02:  # 如果最近性能较好
                alpha = 0.8  # 更信任二阶模型
            elif recent_performance > 0.01:
                alpha = 0.6  # 平衡权重
            else:
                alpha = 0.4  # 更信任一阶模型
        else:
            # 默认权重
            alpha = 0.7

        # 根据数据量调整权重
        if len(history) < 10:
            alpha *= 0.5  # 数据不足时降低二阶模型权重
        elif len(history) > 100:
            alpha = min(alpha * 1.2, 0.9)  # 数据充足时提高二阶模型权重

        result = self.predict_hybrid(history, alpha)
        result['adaptive_alpha'] = alpha
        result['method'] = 'adaptive_hybrid'

        return result

    def ensemble_predict(self, history: List[str], num_predictions: int = 5) -> Dict[str, Any]:
        """
        集成预测，生成多个预测结果

        Args:
            history: 历史号码列表
            num_predictions: 预测数量

        Returns:
            集成预测结果
        """
        if not self.is_trained:
            raise ValueError("模型未训练")

        predictions = []
        methods_used = []

        # 生成多个预测
        for i in range(num_predictions):
            if i % 3 == 0:
                # 一阶预测
                pred = self.predict_first_order(history[-1])
                methods_used.append('first_order')
            elif i % 3 == 1 and len(history) >= 2 and self.max_order >= 2:
                # 二阶预测
                pred = self.predict_second_order(history[-2], history[-1])
                methods_used.append('second_order')
            else:
                # 混合预测
                alpha = 0.5 + 0.4 * np.random.random()  # 随机权重
                pred = self.predict_hybrid(history, alpha)
                methods_used.append('hybrid')

            predictions.append(pred['numbers'])

        # 统计最频繁的预测
        from collections import Counter
        prediction_counts = Counter(predictions)
        most_common = prediction_counts.most_common(1)[0]

        return {
            'numbers': most_common[0],
            'method': 'ensemble',
            'confidence': most_common[1] / num_predictions,
            'all_predictions': predictions,
            'methods_used': methods_used,
            'diversity': len(set(predictions)) / len(predictions)
        }

    def _get_cache_key(self, data_hash: str, params: Dict[str, Any]) -> str:
        """生成缓存键"""
        param_str = json.dumps(params, sort_keys=True)
        return hashlib.md5(f"{data_hash}_{param_str}_{self.cache_version}".encode()).hexdigest()

    def _get_data_hash(self, data: List[str]) -> str:
        """计算数据哈希"""
        data_str = ''.join(data)
        return hashlib.md5(data_str.encode()).hexdigest()

    def save_model_cache(self, data_hash: str):
        """保存模型到缓存"""
        if not self.use_cache:
            return

        try:
            cache_file = os.path.join(self.cache_dir, f"model_{data_hash}.pkl")

            cache_data = {
                'first_order_matrix': self.first_order_matrix,
                'second_order_matrix': self.second_order_matrix,
                'sparse_matrices': self.sparse_matrices,
                'training_data_size': self.training_data_size,
                'last_training_time': self.last_training_time,
                'model_params': {
                    'max_order': self.max_order,
                    'smoothing_alpha': self.smoothing_alpha,
                    'window_size': self.window_size
                }
            }

            with open(cache_file, 'wb') as f:
                pickle.dump(cache_data, f)

            print(f"✅ 模型已缓存: {cache_file}")

        except Exception as e:
            print(f"⚠️ 缓存保存失败: {e}")

    def load_model_cache(self, data_hash: str) -> bool:
        """从缓存加载模型"""
        if not self.use_cache:
            return False

        try:
            cache_file = os.path.join(self.cache_dir, f"model_{data_hash}.pkl")

            if not os.path.exists(cache_file):
                return False

            with open(cache_file, 'rb') as f:
                cache_data = pickle.load(f)

            # 验证缓存版本和参数
            cached_params = cache_data.get('model_params', {})
            if (cached_params.get('max_order') != self.max_order or
                cached_params.get('smoothing_alpha') != self.smoothing_alpha):
                return False

            # 加载缓存数据
            self.first_order_matrix = cache_data['first_order_matrix']
            self.second_order_matrix = cache_data['second_order_matrix']
            self.sparse_matrices = cache_data['sparse_matrices']
            self.training_data_size = cache_data['training_data_size']
            self.last_training_time = cache_data['last_training_time']
            self.is_trained = True

            print(f"✅ 从缓存加载模型: {cache_file}")
            return True

        except Exception as e:
            print(f"⚠️ 缓存加载失败: {e}")
            return False

    def parallel_matrix_build(self, data: List[str]):
        """并行构建转移矩阵"""
        if not self.use_parallel or len(data) < 1000:
            # 数据量小时使用串行处理
            self.build_first_order_matrix(data)
            if self.max_order >= 2:
                self.build_second_order_matrix(data)
            return

        print("🚀 使用并行处理构建转移矩阵...")

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 并行构建一阶和二阶矩阵
            futures = []

            # 一阶矩阵
            future1 = executor.submit(self.build_first_order_matrix, data)
            futures.append(future1)

            # 二阶矩阵
            if self.max_order >= 2:
                future2 = executor.submit(self.build_second_order_matrix, data)
                futures.append(future2)

            # 等待完成
            for future in futures:
                future.result()

        print("✅ 并行矩阵构建完成")

    def optimize_memory_usage(self):
        """优化内存使用"""
        print("🔧 优化内存使用...")

        # 清理不必要的计数数据
        if hasattr(self, 'first_order_counts'):
            del self.first_order_counts
        if hasattr(self, 'second_order_counts'):
            del self.second_order_counts

        # 压缩稀疏矩阵
        for pos in self.sparse_matrices['first_order']:
            matrix = self.sparse_matrices['first_order'][pos]
            matrix.eliminate_zeros()  # 移除显式的零元素
            matrix.prune()  # 压缩存储

        if self.max_order >= 2:
            for pos in self.sparse_matrices['second_order']:
                matrix = self.sparse_matrices['second_order'][pos]
                matrix.eliminate_zeros()
                matrix.prune()

        print("✅ 内存优化完成")

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = {
            'cache_enabled': self.use_cache,
            'parallel_enabled': self.use_parallel,
            'max_workers': self.max_workers,
            'cache_dir': self.cache_dir,
            'prediction_cache_size': len(self.prediction_cache)
        }

        # 内存使用统计
        if self.is_trained:
            memory_usage = 0

            # 一阶矩阵内存
            for pos in range(3):
                if pos in self.sparse_matrices['first_order']:
                    matrix = self.sparse_matrices['first_order'][pos]
                    memory_usage += matrix.data.nbytes + matrix.indices.nbytes + matrix.indptr.nbytes

            # 二阶矩阵内存
            if self.max_order >= 2:
                for pos in range(3):
                    if pos in self.sparse_matrices['second_order']:
                        matrix = self.sparse_matrices['second_order'][pos]
                        memory_usage += matrix.data.nbytes + matrix.indices.nbytes + matrix.indptr.nbytes

            stats['memory_usage_bytes'] = memory_usage
            stats['memory_usage_mb'] = memory_usage / (1024 * 1024)

        return stats


if __name__ == "__main__":
    # 测试代码
    enhanced_markov = EnhancedMarkovChain(max_order=2)
    
    try:
        # 训练模型
        enhanced_markov.train(data_limit=500)
        
        # 获取模型信息
        info = enhanced_markov.get_model_info()
        print(f"\n模型信息: {info}")
        
        # 测试预测
        test_history = ['123', '456', '789']
        
        # 一阶预测
        pred1 = enhanced_markov.predict_first_order('789')
        print(f"\n一阶预测: {pred1}")
        
        # 二阶预测
        pred2 = enhanced_markov.predict_second_order('456', '789')
        print(f"二阶预测: {pred2}")
        
        # 自适应预测
        pred3 = enhanced_markov.predict_adaptive(test_history)
        print(f"自适应预测: {pred3}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
