#!/usr/bin/env python3
"""
测试频率分析API接口
"""

import requests
import json
import time

def test_frequency_api():
    """测试频率分析API接口"""
    url = "http://127.0.0.1:8888/api/v1/analysis/frequency"
    
    try:
        print("🔍 测试频率分析API接口...")
        print(f"📡 请求URL: {url}")
        
        # 发送请求
        response = requests.get(url, timeout=10)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API请求成功!")
            
            # 检查关键字段
            required_fields = [
                'success', 'total_records', 'date_range', 'latest_period', 
                'hot_numbers', 'cold_numbers', 'analysis_time', 'digit_frequency',
                'position_frequency'
            ]
            
            print("\n📋 检查关键字段:")
            for field in required_fields:
                if field in data:
                    print(f"  ✅ {field}: {data[field]}")
                else:
                    print(f"  ❌ {field}: 缺失")
            
            print(f"\n📈 数据概览:")
            print(f"  总记录数: {data.get('total_records', 'N/A')}")
            print(f"  分析期间: {data.get('date_range', 'N/A')}")
            print(f"  最新期号: {data.get('latest_period', 'N/A')}")
            print(f"  分析时间: {data.get('analysis_time', 'N/A')}")
            
            if data.get('hot_numbers'):
                print(f"  热号前5: {data['hot_numbers'][:5]}")
            if data.get('cold_numbers'):
                print(f"  冷号前5: {data['cold_numbers'][:5]}")
                
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务，请确认服务已启动")
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    # 等待API服务启动
    print("⏳ 等待API服务启动...")
    time.sleep(3)
    
    test_frequency_api()
