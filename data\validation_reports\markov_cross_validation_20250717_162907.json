{"model_params": {"transition_window_size": 1000, "probability_window_size": 500, "smoothing_alpha": 1.0}, "validation_params": {"k_folds": 2, "data_limit": 400}, "fold_results": [{"fold_idx": 0, "train_size": 200, "val_size": 200, "predictions_count": 200, "accuracy_metrics": {"exact_match": 0.0, "digit_accuracy": 0.25333333333333335, "position_accuracy": [0.1, 0.095, 0.12], "total_predictions": 200}, "diversity_metrics": {"simpson_diversity": 0.9914, "unique_ratio": 0.72, "entropy": 7.029386924826833, "unique_count": 144, "total_count": 200}, "stability_metrics": {"variance": 75334.56177500001, "std_dev": 274.47142251061405, "coefficient_of_variation": 0.4964349231949032, "mean": 552.885}, "aic_bic": {"aic": 9430.340371976183, "bic": 9793.155282296466, "log_likelihood": -4605.170185988091, "num_params": 110, "num_samples": 200}}], "overall_results": {"accuracy_metrics": {"exact_match": 0.0, "digit_accuracy": 0.25333333333333335, "position_accuracy": [0.1, 0.095, 0.12], "total_predictions": 200}, "diversity_metrics": {"simpson_diversity": 0.9914, "unique_ratio": 0.72, "entropy": 7.029386924826833, "unique_count": 144, "total_count": 200}, "stability_metrics": {"variance": 75334.56177500001, "std_dev": 274.47142251061405, "coefficient_of_variation": 0.4964349231949032, "mean": 552.885}, "aic_bic": {"aic": 9430.340371976183, "bic": 9793.155282296466, "log_likelihood": -4605.170185988091, "num_params": 110, "num_samples": 200}, "total_predictions": 200}, "metadata": {"generated_at": "2025-07-17T16:29:07.863445", "validator_version": "1.0", "database_path": "data\\lottery.db"}}