#!/usr/bin/env python3
"""
测试完整13字段数据解析功能
"""

import sys
sys.path.append('src')

from data.models import LotteryRecord, DataValidator
from data.parser import DataParser
from data.formatter import DataFormatter
import json

def test_complete_field_parsing():
    """测试完整字段解析功能"""
    print("开始测试完整13字段数据解析...")
    
    # 测试数据（完整格式）
    test_line = "2025184 2025-07-13 7 9 9 6 3 3 1 1 110880482 0 1040 0 346 0 173"
    
    print(f"\n测试数据行:")
    print(f"'{test_line}'")
    
    # 1. 测试字段验证
    print(f"\n1. 测试字段验证...")
    parsed_data = DataValidator.validate_record_line(test_line)
    
    if parsed_data:
        print(f"✅ 字段验证成功")
        print(f"解析结果:")
        for key, value in parsed_data.items():
            print(f"  {key}: {value}")
    else:
        print(f"❌ 字段验证失败")
        return False
    
    # 2. 测试记录创建
    print(f"\n2. 测试记录创建...")
    try:
        parser = DataParser()
        record = parser.parse_line(test_line)
        
        if record:
            print(f"✅ 记录创建成功")
            print(f"记录信息:")
            print(f"  期号: {record.period}")
            print(f"  日期: {record.date}")
            print(f"  开奖号码: {record.numbers}")
            print(f"  试机号码: {record.trial_numbers}")
            print(f"  开奖机器: {record.draw_machine}")
            print(f"  试机机器: {record.trial_machine}")
            print(f"  销售额: {record.sales_amount:,} 元")
            print(f"  直选奖金: {record.direct_prize} 元")
            print(f"  组三奖金: {record.group3_prize} 元")
            print(f"  组六奖金: {record.group6_prize} 元")
        else:
            print(f"❌ 记录创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 记录创建异常: {e}")
        return False
    
    # 3. 测试统计计算
    print(f"\n3. 测试统计计算...")
    print(f"  开奖号码统计:")
    print(f"    号码列表: {record.number_list}")
    print(f"    和值: {record.sum_value}")
    print(f"    跨度: {record.span_value}")
    
    print(f"  试机号码统计:")
    print(f"    号码列表: {record.trial_number_list}")
    print(f"    和值: {record.trial_sum_value}")
    print(f"    跨度: {record.trial_span_value}")
    
    # 4. 测试数据导出
    print(f"\n4. 测试数据导出...")
    
    # JSON格式
    record_dict = record.to_dict()
    print(f"✅ JSON格式导出成功，字段数: {len(record_dict)}")
    
    # 保存JSON示例
    with open('data/processed/complete_field_example.json', 'w', encoding='utf-8') as f:
        json.dump(record_dict, f, ensure_ascii=False, indent=2)
    print(f"✅ JSON示例已保存到: data/processed/complete_field_example.json")
    
    # CSV格式测试
    formatter = DataFormatter()
    csv_data = formatter.to_csv([record])
    print(f"✅ CSV格式导出成功，包含完整字段")
    
    # 保存CSV示例
    with open('data/processed/complete_field_example.csv', 'w', encoding='utf-8') as f:
        f.write(csv_data)
    print(f"✅ CSV示例已保存到: data/processed/complete_field_example.csv")
    
    # 5. 测试实际数据文件解析
    print(f"\n5. 测试实际数据文件解析...")
    try:
        with open('data/raw/3d_data_20250714_144231.txt', 'r', encoding='utf-8') as f:
            raw_data = f.read()
        
        parser = DataParser()
        records, quality_report = parser.parse_data(raw_data)
        
        print(f"✅ 实际数据解析成功")
        print(f"  解析记录数: {len(records)}")
        print(f"  质量评分: {quality_report.quality_score}")
        
        if records:
            latest_record = records[-1]
            print(f"  最新记录示例:")
            print(f"    期号: {latest_record.period}")
            print(f"    开奖号码: {latest_record.numbers}")
            print(f"    试机号码: {latest_record.trial_numbers}")
            print(f"    销售额: {latest_record.sales_amount:,} 元")
            print(f"    直选奖金: {latest_record.direct_prize} 元")
        
        # 导出完整数据
        export_results = {}
        
        # JSON导出
        json_data = formatter.to_json(records[:100])  # 只导出前100条作为示例
        with open('data/processed/complete_fields_sample.json', 'w', encoding='utf-8') as f:
            f.write(json_data)
        export_results['json'] = True
        
        # CSV导出
        csv_data = formatter.to_csv(records[:100])  # 只导出前100条作为示例
        with open('data/processed/complete_fields_sample.csv', 'w', encoding='utf-8') as f:
            f.write(csv_data)
        export_results['csv'] = True
        
        print(f"✅ 完整字段数据导出成功:")
        for fmt, success in export_results.items():
            print(f"    {fmt.upper()}: {'✅' if success else '❌'}")
        
    except Exception as e:
        print(f"❌ 实际数据解析失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 6. 字段完整性验证
    print(f"\n6. 字段完整性验证...")
    expected_fields = [
        'period', 'date', 'numbers', 'trial_numbers',
        'draw_machine', 'trial_machine', 'sales_amount',
        'direct_prize', 'group3_prize', 'group6_prize',
        'unknown_field1', 'unknown_field2', 'unknown_field3'
    ]
    
    record_dict = record.to_dict()
    missing_fields = []
    for field in expected_fields:
        if field not in record_dict:
            missing_fields.append(field)
    
    if missing_fields:
        print(f"❌ 缺失字段: {missing_fields}")
        return False
    else:
        print(f"✅ 所有必需字段都已包含")
    
    print(f"\n🎉 完整13字段数据解析测试成功！")
    print(f"📋 测试摘要:")
    print(f"   - 字段验证: ✅")
    print(f"   - 记录创建: ✅")
    print(f"   - 统计计算: ✅")
    print(f"   - 数据导出: ✅")
    print(f"   - 实际数据: ✅")
    print(f"   - 字段完整性: ✅")
    
    return True

if __name__ == "__main__":
    test_complete_field_parsing()
