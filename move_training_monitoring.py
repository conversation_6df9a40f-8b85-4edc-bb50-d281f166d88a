import shutil
import os

# 移动训练监控深度页面
source = "src/ui/pages_disabled/training_monitoring_deep.py"
dest = "src/ui/pages/training_monitoring_deep.py"

if os.path.exists(source):
    shutil.move(source, dest)
    print(f"✅ 移动成功: {source} -> {dest}")
    print("✅ 训练监控深度页面恢复完成!")
else:
    print(f"❌ 源文件不存在: {source}")

# 验证移动结果
if os.path.exists(dest):
    print(f"✅ 目标文件存在: {dest}")
else:
    print(f"❌ 目标文件不存在: {dest}")
