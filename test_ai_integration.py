#!/usr/bin/env python3
"""
AI智能Bug检测系统集成测试
验证AI模块与现有系统的集成效果
"""

import sys
import os
sys.path.insert(0, '.')

from src.bug_detection.ai.ai_manager import AIBugDetectionManager
from src.bug_detection.core.database_manager import DatabaseManager
import json
from datetime import datetime

def test_ai_system_integration():
    """测试AI系统集成"""
    print("🤖 AI智能Bug检测系统集成测试")
    print("=" * 60)
    
    try:
        # 初始化AI管理器
        db_manager = DatabaseManager()
        ai_manager = AIBugDetectionManager(db_manager)
        
        # 1. 测试AI系统状态
        print("\n📊 1. AI系统状态检查")
        print("-" * 30)
        
        ai_status = ai_manager.get_ai_status()
        print(f"AI模块可用: {ai_status['ai_modules_available']}")
        print(f"错误分类器: {ai_status['error_classifier_loaded']}")
        print(f"相似度分析器: {ai_status['similarity_analyzer_loaded']}")
        print(f"缓存大小: {ai_status['cache_size']}")
        
        # 2. 测试单个错误分析
        print("\n🔬 2. 单个错误AI分析测试")
        print("-" * 30)
        
        test_errors = [
            {
                'message': 'Cannot read property "innerHTML" of null',
                'type': 'javascript',
                'page_url': 'http://127.0.0.1:8501/dashboard',
                'source': 'component.js',
                'stack_trace': 'at HTMLElement.render (component.js:45:12)'
            },
            {
                'message': 'HTTP 500 Internal Server Error',
                'type': 'network',
                'page_url': 'http://127.0.0.1:8501/api/data',
                'source': 'api_client.js',
                'stack_trace': 'at fetch (/api/v1/data)'
            },
            {
                'message': 'Database connection timeout',
                'type': 'database',
                'page_url': 'http://127.0.0.1:8501/data',
                'source': 'database.py',
                'stack_trace': 'at DatabaseConnection.connect (db.py:123)'
            }
        ]
        
        for i, error_data in enumerate(test_errors, 1):
            print(f"\n测试错误 {i}: {error_data['message'][:50]}...")
            
            # 执行AI分析
            result = ai_manager.analyze_error(error_data)
            
            print(f"  分类: {result.get('category', 'unknown')}")
            print(f"  严重程度: {result.get('severity', 'unknown')}")
            print(f"  优先级: {result.get('priority', 'unknown')}")
            print(f"  置信度: {result.get('confidence', 0):.3f}")
            print(f"  使用模块: {result.get('modules_used', [])}")
            print(f"  AI启用: {result.get('ai_enabled', False)}")
        
        # 3. 测试批量分析
        print("\n📦 3. 批量错误AI分析测试")
        print("-" * 30)
        
        batch_results = ai_manager.batch_analyze(test_errors)
        print(f"批量分析完成，处理了 {len(batch_results)} 个错误")
        
        # 统计分析结果
        categories = {}
        severities = {}
        ai_enabled_count = 0
        
        for result in batch_results:
            category = result.get('category', 'unknown')
            severity = result.get('severity', 'unknown')
            
            categories[category] = categories.get(category, 0) + 1
            severities[severity] = severities.get(severity, 0) + 1
            
            if result.get('ai_enabled', False):
                ai_enabled_count += 1
        
        print(f"  分类分布: {categories}")
        print(f"  严重程度分布: {severities}")
        print(f"  AI分析成功率: {ai_enabled_count}/{len(batch_results)} ({ai_enabled_count/len(batch_results)*100:.1f}%)")
        
        # 4. 测试相似度分析
        print("\n🔍 4. 相似度分析测试")
        print("-" * 30)
        
        if ai_manager.similarity_analyzer:
            # 创建相似错误进行测试
            similar_errors = [
                {'id': '1', 'error_message': 'Cannot read property "innerHTML" of null', 'created_at': '2025-07-24T10:00:00'},
                {'id': '2', 'error_message': 'Cannot read property "value" of null', 'created_at': '2025-07-24T10:05:00'},
                {'id': '3', 'error_message': 'Cannot read property "textContent" of null', 'created_at': '2025-07-24T10:10:00'},
                {'id': '4', 'error_message': 'HTTP 500 Internal Server Error', 'created_at': '2025-07-24T10:15:00'},
            ]
            
            # 执行聚类分析
            clusters = ai_manager.similarity_analyzer.cluster_similar_errors(similar_errors)
            print(f"  发现 {len(clusters)} 个错误聚类")
            
            for i, cluster in enumerate(clusters):
                print(f"  聚类 {i+1}: {len(cluster)} 个错误")
                for error in cluster:
                    print(f"    - {error['error_message'][:40]}...")
            
            # 执行去重分析
            unique_errors = ai_manager.similarity_analyzer.deduplicate_errors(similar_errors)
            duplicate_rate = (len(similar_errors) - len(unique_errors)) / len(similar_errors) * 100
            print(f"  去重结果: {len(similar_errors)} → {len(unique_errors)} (重复率: {duplicate_rate:.1f}%)")
        else:
            print("  相似度分析器未加载")
        
        # 5. 测试NLP分类器
        print("\n🧠 5. NLP分类器详细测试")
        print("-" * 30)
        
        if ai_manager.error_classifier:
            nlp_test_messages = [
                "Element not found in DOM tree",
                "API request failed with status 404",
                "SQL query execution timeout",
                "Memory leak detected in application",
                "Authentication token expired",
                "JSON parse error in response data"
            ]
            
            print("  NLP分类测试结果:")
            for msg in nlp_test_messages:
                result = ai_manager.error_classifier.classify_error(msg)
                print(f"    '{msg[:30]}...' → {result['category']} ({result['confidence']:.3f})")
        else:
            print("  NLP分类器未加载")
        
        # 6. 性能测试
        print("\n⚡ 6. AI系统性能测试")
        print("-" * 30)
        
        import time
        
        # 单次分析性能
        start_time = time.time()
        ai_manager.analyze_error(test_errors[0])
        single_analysis_time = (time.time() - start_time) * 1000
        
        print(f"  单次分析时间: {single_analysis_time:.2f}ms")
        
        # 批量分析性能
        start_time = time.time()
        ai_manager.batch_analyze(test_errors)
        batch_analysis_time = (time.time() - start_time) * 1000
        avg_batch_time = batch_analysis_time / len(test_errors)
        
        print(f"  批量分析总时间: {batch_analysis_time:.2f}ms")
        print(f"  批量分析平均时间: {avg_batch_time:.2f}ms/个")
        
        # 性能评估
        if single_analysis_time < 100:
            print("  ✅ 单次分析性能: 优秀")
        elif single_analysis_time < 500:
            print("  ⚠️ 单次分析性能: 良好")
        else:
            print("  ❌ 单次分析性能: 需要优化")
        
        # 7. 集成验证总结
        print("\n🎯 7. 集成验证总结")
        print("-" * 30)
        
        integration_score = 0
        max_score = 6
        
        # 评分标准
        if ai_status['ai_modules_available']:
            integration_score += 1
            print("  ✅ AI模块可用性: 通过")
        else:
            print("  ❌ AI模块可用性: 失败")
        
        if ai_status['error_classifier_loaded']:
            integration_score += 1
            print("  ✅ NLP分类器: 通过")
        else:
            print("  ❌ NLP分类器: 失败")
        
        if ai_status['similarity_analyzer_loaded']:
            integration_score += 1
            print("  ✅ 相似度分析器: 通过")
        else:
            print("  ❌ 相似度分析器: 失败")
        
        if ai_enabled_count > 0:
            integration_score += 1
            print("  ✅ AI分析功能: 通过")
        else:
            print("  ❌ AI分析功能: 失败")
        
        if single_analysis_time < 500:
            integration_score += 1
            print("  ✅ 性能表现: 通过")
        else:
            print("  ❌ 性能表现: 失败")
        
        if len(batch_results) == len(test_errors):
            integration_score += 1
            print("  ✅ 批量处理: 通过")
        else:
            print("  ❌ 批量处理: 失败")
        
        # 最终评分
        success_rate = (integration_score / max_score) * 100
        print(f"\n📊 集成测试评分: {integration_score}/{max_score} ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            print("🎉 AI智能Bug检测系统集成成功！")
            return True
        elif success_rate >= 60:
            print("⚠️ AI智能Bug检测系统部分集成成功，需要进一步优化")
            return False
        else:
            print("❌ AI智能Bug检测系统集成失败，需要检查配置")
            return False
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_streamlit_integration():
    """测试Streamlit界面集成"""
    print("\n🖥️ Streamlit界面集成测试")
    print("-" * 30)
    
    try:
        # 检查AI面板函数是否可导入
        from src.ui.pages.bug_detection_status import show_ai_analysis_panel, AI_SYSTEM_AVAILABLE
        
        print(f"AI系统可用性: {AI_SYSTEM_AVAILABLE}")
        print("✅ AI分析面板函数导入成功")
        
        if AI_SYSTEM_AVAILABLE:
            print("✅ Streamlit界面集成准备就绪")
            return True
        else:
            print("⚠️ AI系统不可用，界面将显示传统功能")
            return False
            
    except ImportError as e:
        print(f"❌ Streamlit界面集成失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 AI智能Bug检测系统 - 完整集成测试")
    print("=" * 80)
    
    # 执行AI系统集成测试
    ai_integration_success = test_ai_system_integration()
    
    # 执行Streamlit界面集成测试
    ui_integration_success = test_streamlit_integration()
    
    # 总结
    print("\n" + "=" * 80)
    print("📋 集成测试总结")
    print("=" * 80)
    
    print(f"AI系统集成: {'✅ 成功' if ai_integration_success else '❌ 失败'}")
    print(f"界面集成: {'✅ 成功' if ui_integration_success else '❌ 失败'}")
    
    overall_success = ai_integration_success and ui_integration_success
    
    if overall_success:
        print("\n🎉 AI智能Bug检测系统完整集成成功！")
        print("💡 现在可以在8501端口的Bug检测状态页面中使用AI功能")
        print("🔗 访问: http://127.0.0.1:8501/bug_detection_status")
    else:
        print("\n⚠️ 集成测试部分成功，请检查失败的组件")
    
    return overall_success

if __name__ == "__main__":
    main()
