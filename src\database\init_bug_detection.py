#!/usr/bin/env python3
"""
Bug检测系统数据库初始化脚本
创建日期: 2025年7月24日
用途: 初始化Bug检测相关数据表
"""

import sqlite3
import os
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_database_path():
    """获取数据库文件路径"""
    # 查找现有的数据库文件
    possible_paths = [
        "lottery_data.db",
        "src/database/lottery_data.db",
        "database/lottery_data.db",
        "../lottery_data.db"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    # 如果没有找到，使用默认路径
    return "lottery_data.db"

def load_schema():
    """加载数据库schema"""
    schema_path = Path(__file__).parent / "bug_detection_schema.sql"
    
    if not schema_path.exists():
        raise FileNotFoundError(f"Schema file not found: {schema_path}")
    
    with open(schema_path, 'r', encoding='utf-8') as f:
        return f.read()

def init_bug_detection_tables():
    """初始化Bug检测相关数据表"""
    db_path = get_database_path()
    logger.info(f"Initializing bug detection tables in database: {db_path}")
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 加载并执行schema
        schema_sql = load_schema()
        
        # 分割SQL语句并执行
        statements = [stmt.strip() for stmt in schema_sql.split(';') if stmt.strip()]
        
        for statement in statements:
            if statement:
                try:
                    cursor.execute(statement)
                    logger.debug(f"Executed: {statement[:50]}...")
                except sqlite3.Error as e:
                    logger.error(f"Error executing statement: {e}")
                    logger.error(f"Statement: {statement}")
        
        # 提交更改
        conn.commit()
        
        # 验证表是否创建成功
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%bug%' OR name LIKE '%test%' OR name LIKE '%performance%' OR name LIKE '%behavior%' OR name LIKE '%js_error%'")
        tables = cursor.fetchall()
        
        logger.info("Successfully created tables:")
        for table in tables:
            logger.info(f"  - {table[0]}")
        
        # 验证视图是否创建成功
        cursor.execute("SELECT name FROM sqlite_master WHERE type='view'")
        views = cursor.fetchall()
        
        if views:
            logger.info("Successfully created views:")
            for view in views:
                logger.info(f"  - {view[0]}")
        
        conn.close()
        logger.info("Bug detection database initialization completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Error initializing bug detection tables: {e}")
        return False

def verify_tables():
    """验证表结构"""
    db_path = get_database_path()
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查每个表的结构
        tables = ['bug_reports', 'user_behaviors', 'performance_metrics', 'test_executions', 'js_errors']
        
        for table in tables:
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            
            logger.info(f"Table {table} structure:")
            for col in columns:
                logger.info(f"  {col[1]} {col[2]} {'NOT NULL' if col[3] else ''} {'PRIMARY KEY' if col[5] else ''}")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"Error verifying tables: {e}")
        return False

if __name__ == "__main__":
    print("Initializing Bug Detection Database...")
    
    if init_bug_detection_tables():
        print("✅ Database initialization successful!")
        
        if verify_tables():
            print("✅ Table verification successful!")
        else:
            print("❌ Table verification failed!")
    else:
        print("❌ Database initialization failed!")
