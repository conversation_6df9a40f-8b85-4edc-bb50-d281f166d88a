#!/usr/bin/env python3
"""
测试频率分析API
"""

import requests
import json

def test_frequency_api():
    """测试频率分析API"""
    api_url = "http://127.0.0.1:8888/api/v1/analysis/frequency"
    
    try:
        print("🔍 测试频率分析API...")
        print(f"API端点: {api_url}")
        
        response = requests.get(api_url, timeout=15)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API调用成功")
            print(f"响应数据结构:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 检查关键字段
            if 'success' in data:
                print(f"✅ success字段: {data['success']}")
            else:
                print("❌ 缺少success字段")
                
            if 'digit_frequency' in data:
                print(f"✅ digit_frequency字段存在，包含{len(data['digit_frequency'])}个数字")
            else:
                print("❌ 缺少digit_frequency字段")
                
            if 'total_records' in data:
                print(f"✅ total_records字段: {data['total_records']}")
            else:
                print("❌ 缺少total_records字段")
                
        else:
            print(f"❌ API调用失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务")
        print("请确保API服务正在运行在 127.0.0.1:8888")
        
    except requests.exceptions.Timeout:
        print("❌ API请求超时")
        
    except Exception as e:
        print(f"❌ 测试出现错误: {e}")

if __name__ == "__main__":
    test_frequency_api()
