#!/usr/bin/env python3
"""
初始化现有Bug的工作流历史记录
"""

import sys
import os
sys.path.insert(0, '.')

from src.bug_detection.core.database_manager import DatabaseManager
from src.bug_detection.workflow.bug_workflow_manager import BugWorkflowManager
import sqlite3
import json
from datetime import datetime

def initialize_workflow_history():
    print("=== 初始化Bug工作流历史记录 ===")
    
    # 初始化组件
    db_manager = DatabaseManager()
    workflow_manager = BugWorkflowManager(db_manager)
    
    try:
        conn = sqlite3.connect(db_manager.db_path)
        cursor = conn.cursor()
        
        # 检查是否有workflow_history字段，如果没有则添加
        cursor.execute("PRAGMA table_info(bug_reports)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'workflow_history' not in columns:
            print("添加workflow_history字段到数据库...")
            cursor.execute("ALTER TABLE bug_reports ADD COLUMN workflow_history TEXT DEFAULT '[]'")
            conn.commit()
            print("✅ workflow_history字段添加成功")
        else:
            print("✅ workflow_history字段已存在")
        
        # 获取所有Bug
        cursor.execute("""
            SELECT id, status, created_at, updated_at, updated_by, workflow_history
            FROM bug_reports 
            ORDER BY created_at DESC
        """)
        
        bugs = cursor.fetchall()
        print(f"找到 {len(bugs)} 个Bug需要初始化工作流历史")
        
        updated_count = 0
        
        for bug in bugs:
            bug_id, status, created_at, updated_at, updated_by, workflow_history = bug
            
            print(f"\n处理Bug: {bug_id}")
            
            # 检查是否已有工作流历史
            existing_history = []
            if workflow_history:
                try:
                    existing_history = json.loads(workflow_history)
                    if existing_history:
                        print(f"  已有工作流历史记录 {len(existing_history)} 条，跳过")
                        continue
                except:
                    pass
            
            # 创建初始工作流历史
            initial_history = []
            
            # 添加创建记录
            initial_history.append({
                'action': 'create',
                'user': 'system',
                'timestamp': created_at or datetime.now().isoformat(),
                'description': f'Bug created with status: {status}'
            })
            
            # 如果有更新记录，添加状态变更记录
            if updated_at and updated_at != created_at:
                initial_history.append({
                    'action': 'update',
                    'user': updated_by or 'system',
                    'timestamp': updated_at,
                    'description': f'Bug status updated to: {status}'
                })
            
            # 如果Bug已解决，添加解决记录
            if status == 'resolved':
                initial_history.append({
                    'action': 'resolve',
                    'user': updated_by or 'system',
                    'timestamp': updated_at or datetime.now().isoformat(),
                    'description': 'Bug marked as resolved'
                })
            
            # 更新数据库
            history_json = json.dumps(initial_history, ensure_ascii=False)
            cursor.execute("""
                UPDATE bug_reports 
                SET workflow_history = ?
                WHERE id = ?
            """, (history_json, bug_id))
            
            updated_count += 1
            print(f"  ✅ 初始化完成，添加了 {len(initial_history)} 条历史记录")
        
        # 提交更改
        conn.commit()
        conn.close()
        
        print(f"\n=== 初始化完成 ===")
        print(f"总计处理: {len(bugs)} 个Bug")
        print(f"成功初始化: {updated_count} 个Bug")
        
        # 验证初始化结果
        print("\n=== 验证初始化结果 ===")
        for bug in bugs[:3]:  # 验证前3个Bug
            bug_id = bug[0]
            history = workflow_manager.get_bug_workflow_history(bug_id)
            print(f"Bug {bug_id}: {len(history)} 条工作流历史记录")
            for entry in history:
                print(f"  - {entry.get('action', 'unknown')}: {entry.get('description', '')}")
        
    except Exception as e:
        print(f"❌ 初始化过程出错: {e}")
        import traceback
        traceback.print_exc()

def test_workflow_operations():
    print("\n=== 测试工作流操作 ===")
    
    # 初始化组件
    db_manager = DatabaseManager()
    workflow_manager = BugWorkflowManager(db_manager)
    
    # 获取第一个待处理的Bug进行测试
    bugs = db_manager.get_bug_reports(limit=1)
    if not bugs:
        print("❌ 没有找到Bug进行测试")
        return
    
    test_bug = bugs[0]
    bug_id = test_bug['id']
    
    print(f"使用Bug {bug_id} 进行测试")
    
    # 测试添加评论
    print("\n1. 测试添加评论...")
    success = workflow_manager.add_comment(
        bug_id,
        "这是一个测试评论，用于验证工作流历史功能",
        "test_user"
    )
    if success:
        print("✅ 评论添加成功")
    else:
        print("❌ 评论添加失败")
    
    # 测试状态更新
    print("\n2. 测试状态更新...")
    current_status = test_bug.get('status', 'open')
    new_status = 'in_progress' if current_status == 'open' else 'open'
    
    success = workflow_manager.update_bug_status(
        bug_id,
        new_status,
        "test_user",
        "测试状态更新功能"
    )
    if success:
        print(f"✅ 状态更新成功: {current_status} -> {new_status}")
    else:
        print("❌ 状态更新失败")
    
    # 测试Bug分配
    print("\n3. 测试Bug分配...")
    success = workflow_manager.assign_bug(
        bug_id,
        "developer_001",
        "test_manager",
        "分配给开发人员处理"
    )
    if success:
        print("✅ Bug分配成功")
    else:
        print("❌ Bug分配失败")
    
    # 查看最终的工作流历史
    print(f"\n4. 查看Bug {bug_id} 的工作流历史:")
    history = workflow_manager.get_bug_workflow_history(bug_id)
    for i, entry in enumerate(history, 1):
        action = entry.get('action', 'unknown')
        user = entry.get('user', 'unknown')
        timestamp = entry.get('timestamp', '')
        description = entry.get('description', '')
        print(f"  {i}. [{action}] by {user} at {timestamp[:19]}")
        print(f"     {description}")

if __name__ == "__main__":
    initialize_workflow_history()
    test_workflow_operations()
