#!/usr/bin/env python3
"""
测试数据库内容脚本
用于检查Bug检测数据库中的数据
"""

import sqlite3
import json
from datetime import datetime

def check_database_content():
    """检查数据库内容"""
    db_path = "data/bug_detection.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 检查数据库内容...")
        print("=" * 50)
        
        # 检查bug_reports表
        print("\n📋 Bug Reports 表:")
        cursor.execute("SELECT COUNT(*) FROM bug_reports")
        bug_count = cursor.fetchone()[0]
        print(f"总Bug报告数: {bug_count}")
        
        if bug_count > 0:
            cursor.execute("""
                SELECT id, timestamp, error_type, severity, message 
                FROM bug_reports 
                ORDER BY timestamp DESC 
                LIMIT 5
            """)
            
            print("\n最近5个Bug报告:")
            for row in cursor.fetchall():
                print(f"  - ID: {row[0]}")
                print(f"    时间: {row[1]}")
                print(f"    类型: {row[2]}")
                print(f"    严重程度: {row[3]}")
                print(f"    消息: {row[4][:50]}...")
                print()
        
        # 检查realtime_events表
        print("\n📡 Realtime Events 表:")
        cursor.execute("SELECT COUNT(*) FROM realtime_events")
        event_count = cursor.fetchone()[0]
        print(f"总实时事件数: {event_count}")
        
        if event_count > 0:
            cursor.execute("""
                SELECT id, event_type, priority, source, timestamp, data 
                FROM realtime_events 
                ORDER BY timestamp DESC 
                LIMIT 5
            """)
            
            print("\n最近5个实时事件:")
            for row in cursor.fetchall():
                print(f"  - ID: {row[0]}")
                print(f"    事件类型: {row[1]}")
                print(f"    优先级: {row[2]}")
                print(f"    来源: {row[3]}")
                print(f"    时间戳: {row[4]} ({datetime.fromtimestamp(row[4]).strftime('%Y-%m-%d %H:%M:%S')})")
                
                # 解析数据
                try:
                    data = json.loads(row[5]) if row[5] else {}
                    print(f"    数据: {json.dumps(data, indent=2, ensure_ascii=False)[:100]}...")
                except:
                    print(f"    数据: {row[5][:50]}...")
                print()
        
        # 检查表结构
        print("\n🏗️ 表结构:")
        
        print("\nBug Reports 表结构:")
        cursor.execute("PRAGMA table_info(bug_reports)")
        for row in cursor.fetchall():
            print(f"  {row[1]} ({row[2]})")
        
        print("\nRealtime Events 表结构:")
        cursor.execute("PRAGMA table_info(realtime_events)")
        for row in cursor.fetchall():
            print(f"  {row[1]} ({row[2]})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")

if __name__ == "__main__":
    check_database_content()
