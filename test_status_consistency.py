#!/usr/bin/env python3
"""
状态一致性测试脚本
验证内存状态与数据库状态的一致性，确保修复方案的有效性
"""

import sqlite3
import sys
import os
from pathlib import Path
from datetime import datetime
import time

# 添加项目路径
sys.path.append('src')

def find_project_root():
    """查找项目根目录"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 向上查找，直到找到包含data目录的目录
    check_dir = current_dir
    while check_dir != os.path.dirname(check_dir):
        data_dir = os.path.join(check_dir, 'data')
        if os.path.exists(data_dir):
            return check_dir
        check_dir = os.path.dirname(check_dir)
    
    return os.getcwd()

def resolve_db_path():
    """解析数据库文件的绝对路径"""
    project_root = find_project_root()
    return os.path.join(project_root, "data", "model_library.db")

def get_db_status(model_id: str):
    """从数据库获取模型状态"""
    try:
        db_path = resolve_db_path()
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT trained, last_training_time, status
                FROM model_states 
                WHERE model_id = ?
            ''', (model_id,))
            result = cursor.fetchone()
            
            if result:
                return {
                    'trained': bool(result[0]),
                    'last_training_time': result[1],
                    'status': result[2]
                }
            return None
    except Exception as e:
        print(f"❌ 获取数据库状态失败: {e}")
        return None

def get_memory_status(model_id: str):
    """从内存获取模型状态"""
    try:
        from model_library.model_registry import ModelRegistry
        
        registry = ModelRegistry()
        model = registry.get_model(model_id)
        
        if model:
            return {
                'trained': model._is_trained,
                'last_training_time': model._last_training_time.isoformat() if model._last_training_time else None,
                'status': 'trained' if model._is_trained else 'ready'
            }
        return None
    except Exception as e:
        print(f"❌ 获取内存状态失败: {e}")
        return None

def test_status_consistency(model_id: str):
    """测试指定模型的状态一致性"""
    print(f"\n🔍 测试模型 {model_id} 的状态一致性...")
    print("=" * 60)
    
    # 获取数据库状态
    db_status = get_db_status(model_id)
    if not db_status:
        print(f"❌ 无法获取模型 {model_id} 的数据库状态")
        return False
    
    # 获取内存状态
    memory_status = get_memory_status(model_id)
    if not memory_status:
        print(f"❌ 无法获取模型 {model_id} 的内存状态")
        return False
    
    print(f"📊 数据库状态:")
    print(f"  - trained: {db_status['trained']}")
    print(f"  - last_training_time: {db_status['last_training_time']}")
    print(f"  - status: {db_status['status']}")
    
    print(f"\n🧠 内存状态:")
    print(f"  - trained: {memory_status['trained']}")
    print(f"  - last_training_time: {memory_status['last_training_time']}")
    print(f"  - status: {memory_status['status']}")
    
    # 检查一致性
    consistent = True
    
    if db_status['trained'] != memory_status['trained']:
        print(f"❌ trained状态不一致: 数据库={db_status['trained']}, 内存={memory_status['trained']}")
        consistent = False
    
    if db_status['status'] != memory_status['status']:
        print(f"❌ status状态不一致: 数据库={db_status['status']}, 内存={memory_status['status']}")
        consistent = False
    
    if consistent:
        print("✅ 状态一致性检查通过")
    else:
        print("❌ 状态一致性检查失败")
    
    return consistent

def test_status_sync_mechanism(model_id: str):
    """测试状态同步机制"""
    print(f"\n🔄 测试模型 {model_id} 的状态同步机制...")
    print("=" * 60)
    
    try:
        from model_library.model_registry import ModelRegistry
        
        registry = ModelRegistry()
        model = registry.get_model(model_id)
        
        if not model:
            print(f"❌ 模型 {model_id} 未在内存中注册")
            return False
        
        # 测试状态一致性检查方法
        if hasattr(model, '_verify_status_consistency'):
            print("🔍 测试状态一致性检查方法...")
            consistency_result = model._verify_status_consistency()
            print(f"  结果: {'✅ 一致' if consistency_result else '❌ 不一致'}")
        else:
            print("⚠️ 模型未实现状态一致性检查方法")
            return False
        
        # 测试状态同步方法
        if hasattr(model, '_sync_status_from_db'):
            print("🔄 测试状态同步方法...")
            model._sync_status_from_db()
            print("✅ 状态同步方法执行完成")
        else:
            print("⚠️ 模型未实现状态同步方法")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 状态同步机制测试失败: {e}")
        return False

def test_get_status_with_consistency_check(model_id: str):
    """测试get_status方法的一致性检查"""
    print(f"\n📋 测试模型 {model_id} 的get_status一致性检查...")
    print("=" * 60)
    
    try:
        from model_library.model_registry import ModelRegistry
        
        registry = ModelRegistry()
        model = registry.get_model(model_id)
        
        if not model:
            print(f"❌ 模型 {model_id} 未在内存中注册")
            return False
        
        # 调用get_status方法，应该自动触发一致性检查
        print("📋 调用get_status方法...")
        status_info = model.get_status()
        
        print(f"✅ get_status执行完成:")
        print(f"  - model_id: {status_info.model_id}")
        print(f"  - status: {status_info.status}")
        print(f"  - trained: {status_info.trained}")
        print(f"  - data_ready: {status_info.data_ready}")
        print(f"  - features_ready: {status_info.features_ready}")
        
        return True
        
    except Exception as e:
        print(f"❌ get_status一致性检查测试失败: {e}")
        return False

def run_comprehensive_test():
    """运行全面的状态一致性测试"""
    print("🚀 开始状态一致性全面测试")
    print("=" * 80)
    
    # 测试的模型列表
    test_models = ['markov_enhanced', 'deep_learning_cnn_lstm', 'trend_analyzer', 'intelligent_fusion']
    
    results = {}
    
    for model_id in test_models:
        print(f"\n🎯 测试模型: {model_id}")
        print("-" * 40)
        
        # 基础状态一致性测试
        consistency_result = test_status_consistency(model_id)
        
        # 状态同步机制测试
        sync_result = test_status_sync_mechanism(model_id)
        
        # get_status一致性检查测试
        get_status_result = test_get_status_with_consistency_check(model_id)
        
        results[model_id] = {
            'consistency': consistency_result,
            'sync_mechanism': sync_result,
            'get_status_check': get_status_result
        }
    
    # 输出测试总结
    print("\n" + "=" * 80)
    print("📊 测试结果总结")
    print("=" * 80)
    
    all_passed = True
    for model_id, result in results.items():
        status = "✅ 通过" if all(result.values()) else "❌ 失败"
        print(f"{model_id:20} | {status}")
        
        if not all(result.values()):
            all_passed = False
            print(f"  - 状态一致性: {'✅' if result['consistency'] else '❌'}")
            print(f"  - 同步机制: {'✅' if result['sync_mechanism'] else '❌'}")
            print(f"  - get_status检查: {'✅' if result['get_status_check'] else '❌'}")
    
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 所有测试通过！状态一致性修复方案有效")
    else:
        print("⚠️ 部分测试失败，需要进一步检查和修复")
    
    return all_passed

if __name__ == "__main__":
    print("🔧 状态一致性测试脚本")
    print(f"📁 项目根目录: {find_project_root()}")
    print(f"📁 数据库路径: {resolve_db_path()}")
    print(f"📁 数据库存在: {Path(resolve_db_path()).exists()}")
    
    # 运行全面测试
    success = run_comprehensive_test()
    
    # 退出码
    sys.exit(0 if success else 1)
