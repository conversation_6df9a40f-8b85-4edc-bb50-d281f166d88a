#!/usr/bin/env python3
"""
数据库备份脚本
"""

import sqlite3
import shutil
from datetime import datetime
import os

def backup_database():
    """备份数据库"""
    # 数据库路径
    db_path = 'data/lottery.db'
    backup_path = f'data/lottery_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'

    print('开始备份数据库...')
    print(f'源文件: {db_path}')
    print(f'备份文件: {backup_path}')

    # 检查源数据库
    if os.path.exists(db_path):
        # 创建备份
        shutil.copy2(db_path, backup_path)
        print(f'✅ 备份创建成功: {backup_path}')
        
        # 验证备份
        with sqlite3.connect(backup_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM lottery_records')
            count = cursor.fetchone()[0]
            print(f'✅ 备份验证成功，记录数: {count}')
            
            # 检查表结构
            cursor.execute('PRAGMA table_info(lottery_records)')
            columns = cursor.fetchall()
            print(f'✅ 表结构验证成功，字段数: {len(columns)}')
            for col in columns:
                null_status = "NOT NULL" if col[3] else "NULL"
                print(f'   字段: {col[1]} {col[2]} {null_status}')
            
        print(f'✅ 数据库备份完成')
        return backup_path
    else:
        print(f'❌ 源数据库不存在: {db_path}')
        return None

if __name__ == "__main__":
    backup_database()
