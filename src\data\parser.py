"""
福彩3D数据解析器

负责解析原始数据，清洗和格式化
"""

import re
from datetime import datetime, date
from typing import List, Optional, Tuple, Dict, Any
import logging

from .models import LotteryRecord, DataValidator, DataQualityReport, detect_data_format

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DataParser:
    """数据解析器"""
    
    def __init__(self):
        """初始化解析器"""
        self.encoding_candidates = ['utf-8', 'gbk', 'gb2312', 'utf-16']
        logger.info("数据解析器初始化完成")
    
    def detect_encoding(self, raw_data: bytes) -> str:
        """
        检测数据编码
        
        Args:
            raw_data: 原始字节数据
            
        Returns:
            检测到的编码格式
        """
        for encoding in self.encoding_candidates:
            try:
                raw_data.decode(encoding)
                logger.info(f"检测到编码格式: {encoding}")
                return encoding
            except UnicodeDecodeError:
                continue
        
        logger.warning("无法检测编码格式，使用utf-8并忽略错误")
        return 'utf-8'
    
    def clean_raw_data(self, raw_data: str) -> List[str]:
        """
        清洗原始数据
        
        Args:
            raw_data: 原始数据字符串
            
        Returns:
            清洗后的数据行列表
        """
        if not raw_data:
            return []
        
        # 分割行并清理
        lines = []
        for line in raw_data.split('\n'):
            # 移除首尾空白
            cleaned_line = line.strip()
            
            # 跳过空行
            if not cleaned_line:
                continue
            
            # 跳过注释行
            if cleaned_line.startswith('#') or cleaned_line.startswith('//'):
                continue
            
            # 移除多余的空白字符
            cleaned_line = re.sub(r'\s+', ' ', cleaned_line)
            
            lines.append(cleaned_line)
        
        logger.info(f"数据清洗完成，有效行数: {len(lines)}")
        return lines
    
    def parse_line(self, line: str) -> Optional[LotteryRecord]:
        """
        解析单行数据（完整版13字段）

        Args:
            line: 数据行

        Returns:
            解析后的记录，失败时返回None
        """
        try:
            # 使用验证器验证行格式
            parsed_data = DataValidator.validate_record_line(line)
            if not parsed_data:
                return None

            # 创建完整记录对象
            record = LotteryRecord(
                period=parsed_data["period"],
                date=datetime.strptime(parsed_data["date"], '%Y-%m-%d').date(),
                numbers=parsed_data["numbers"],
                trial_numbers=parsed_data["trial_numbers"],
                draw_machine=parsed_data["draw_machine"],
                trial_machine=parsed_data["trial_machine"],
                sales_amount=parsed_data["sales_amount"],
                direct_prize=parsed_data["direct_prize"],
                group3_prize=parsed_data["group3_prize"],
                group6_prize=parsed_data["group6_prize"],
                unknown_field1=parsed_data["unknown_field1"],
                unknown_field2=parsed_data["unknown_field2"],
                unknown_field3=parsed_data["unknown_field3"]
            )

            return record

        except (ValueError, KeyError) as e:
            logger.debug(f"解析行失败: {line} - {e}")
            return None
    
    def parse_data(self, raw_data: str) -> Tuple[List[LotteryRecord], DataQualityReport]:
        """
        解析完整数据
        
        Args:
            raw_data: 原始数据字符串
            
        Returns:
            (记录列表, 数据质量报告)
        """
        logger.info("开始解析数据...")
        
        # 清洗数据
        lines = self.clean_raw_data(raw_data)
        
        if not lines:
            logger.warning("没有有效的数据行")
            return [], DataQualityReport(
                total_records=0,
                valid_records=0,
                invalid_records=0,
                duplicate_records=0,
                missing_dates=0,
                date_range_start=None,
                date_range_end=None,
                errors=["没有有效的数据行"]
            )
        
        # 检测数据格式
        detected_format = detect_data_format(lines[:10])
        if detected_format:
            logger.info(f"检测到数据格式: {detected_format}")
        else:
            logger.warning("无法检测数据格式，使用通用解析")
        
        # 解析每一行
        records = []
        errors = []
        
        for i, line in enumerate(lines, 1):
            record = self.parse_line(line)
            if record:
                records.append(record)
            else:
                error_msg = f"第{i}行解析失败: {line[:50]}..."
                errors.append(error_msg)
                logger.debug(error_msg)
        
        # 去重处理
        unique_records = self._remove_duplicates(records)
        duplicate_count = len(records) - len(unique_records)
        
        if duplicate_count > 0:
            logger.warning(f"发现并移除了 {duplicate_count} 条重复记录")
        
        # 排序（按期号）
        unique_records.sort(key=lambda x: x.period)
        
        # 生成质量报告
        quality_report = DataValidator.create_quality_report(
            unique_records, lines, errors
        )
        
        logger.info(f"数据解析完成，有效记录: {len(unique_records)}")
        logger.info(f"数据质量评分: {quality_report.quality_score}")
        
        return unique_records, quality_report
    
    def _remove_duplicates(self, records: List[LotteryRecord]) -> List[LotteryRecord]:
        """
        移除重复记录
        
        Args:
            records: 记录列表
            
        Returns:
            去重后的记录列表
        """
        seen_periods = set()
        unique_records = []
        
        for record in records:
            if record.period not in seen_periods:
                seen_periods.add(record.period)
                unique_records.append(record)
        
        return unique_records
    
    def validate_data_integrity(self, records: List[LotteryRecord]) -> Dict[str, Any]:
        """
        验证数据完整性
        
        Args:
            records: 记录列表
            
        Returns:
            完整性检查结果
        """
        if not records:
            return {"status": "empty", "message": "没有数据记录"}
        
        # 检查期号连续性
        periods = [int(r.period) for r in records]
        periods.sort()
        
        missing_periods = []
        for i in range(len(periods) - 1):
            current = periods[i]
            next_period = periods[i + 1]
            
            # 检查是否有缺失的期号
            if next_period - current > 1:
                for missing in range(current + 1, next_period):
                    missing_periods.append(str(missing).zfill(7))
        
        # 检查日期连续性
        dates = [r.date for r in records]
        dates.sort()
        
        date_gaps = []
        for i in range(len(dates) - 1):
            current_date = dates[i]
            next_date = dates[i + 1]
            gap_days = (next_date - current_date).days
            
            if gap_days > 1:
                date_gaps.append({
                    "from": current_date.isoformat(),
                    "to": next_date.isoformat(),
                    "gap_days": gap_days
                })
        
        return {
            "status": "analyzed",
            "total_records": len(records),
            "period_range": {
                "start": records[0].period,
                "end": records[-1].period
            },
            "date_range": {
                "start": dates[0].isoformat(),
                "end": dates[-1].isoformat()
            },
            "missing_periods": missing_periods[:10],  # 只显示前10个
            "missing_periods_count": len(missing_periods),
            "date_gaps": date_gaps[:5],  # 只显示前5个
            "date_gaps_count": len(date_gaps)
        }
    
    def export_to_dict(self, records: List[LotteryRecord]) -> List[Dict[str, Any]]:
        """
        导出记录为字典列表
        
        Args:
            records: 记录列表
            
        Returns:
            字典列表
        """
        return [record.to_dict() for record in records]
    
    def get_statistics(self, records: List[LotteryRecord]) -> Dict[str, Any]:
        """
        获取数据统计信息
        
        Args:
            records: 记录列表
            
        Returns:
            统计信息字典
        """
        if not records:
            return {"error": "没有数据记录"}
        
        # 基本统计
        total_records = len(records)
        
        # 号码统计
        all_numbers = []
        sum_values = []
        span_values = []
        
        for record in records:
            all_numbers.extend(record.number_list)
            sum_values.append(record.sum_value)
            span_values.append(record.span_value)
        
        # 数字频率统计
        digit_frequency = {}
        for digit in range(10):
            digit_frequency[str(digit)] = all_numbers.count(digit)
        
        return {
            "total_records": total_records,
            "date_range": {
                "start": min(r.date for r in records).isoformat(),
                "end": max(r.date for r in records).isoformat()
            },
            "sum_value_stats": {
                "min": min(sum_values),
                "max": max(sum_values),
                "avg": round(sum(sum_values) / len(sum_values), 2)
            },
            "span_value_stats": {
                "min": min(span_values),
                "max": max(span_values),
                "avg": round(sum(span_values) / len(span_values), 2)
            },
            "digit_frequency": digit_frequency,
            "most_frequent_digit": max(digit_frequency, key=digit_frequency.get),
            "least_frequent_digit": min(digit_frequency, key=digit_frequency.get)
        }


# 便捷函数
def parse_lottery_data(raw_data: str) -> Tuple[List[LotteryRecord], DataQualityReport]:
    """
    便捷的数据解析函数
    
    Args:
        raw_data: 原始数据字符串
        
    Returns:
        (记录列表, 数据质量报告)
    """
    parser = DataParser()
    return parser.parse_data(raw_data)


if __name__ == "__main__":
    # 测试代码
    test_data = """
    # 福彩3D历史数据
    2024001	2024-01-01	123
    2024002	2024-01-02	456
    2024003	2024-01-03	789
    2024004	2024-01-04	012
    invalid line
    2024005	2024-01-05	345
    2024001	2024-01-01	123  # 重复记录
    """
    
    print("测试数据解析器...")
    parser = DataParser()
    
    records, quality_report = parser.parse_data(test_data)
    
    print(f"\n解析结果:")
    print(f"有效记录数: {len(records)}")
    print(f"数据质量评分: {quality_report.quality_score}")
    
    if records:
        print(f"\n前3条记录:")
        for record in records[:3]:
            print(f"  {record}")
        
        # 统计信息
        stats = parser.get_statistics(records)
        print(f"\n统计信息:")
        print(f"  总记录数: {stats['total_records']}")
        print(f"  日期范围: {stats['date_range']['start']} 到 {stats['date_range']['end']}")
        print(f"  和值范围: {stats['sum_value_stats']['min']}-{stats['sum_value_stats']['max']}")
        print(f"  最常见数字: {stats['most_frequent_digit']}")
        
        # 完整性检查
        integrity = parser.validate_data_integrity(records)
        print(f"\n完整性检查:")
        print(f"  缺失期号数: {integrity['missing_periods_count']}")
        print(f"  日期间隔数: {integrity['date_gaps_count']}")
    
    print(f"\n质量报告详情:")
    print(f"  总行数: {quality_report.total_records}")
    print(f"  有效记录: {quality_report.valid_records}")
    print(f"  无效记录: {quality_report.invalid_records}")
    print(f"  重复记录: {quality_report.duplicate_records}")
    print(f"  错误信息: {quality_report.errors[:3]}")  # 显示前3个错误
