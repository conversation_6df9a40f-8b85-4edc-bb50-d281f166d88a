"""
交互式图表组件
实现可复用的交互式图表组件，Plotly 3D图表封装、实时数据更新机制、图表交互控制器
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Callable
import json
from datetime import datetime, timedelta
import asyncio
import threading
import time


class InteractiveChartController:
    """交互式图表控制器"""
    
    def __init__(self):
        self.charts: Dict[str, Any] = {}
        self.update_callbacks: Dict[str, Callable] = {}
        self.auto_refresh_enabled = False
        self.refresh_interval = 5  # 秒
        self._refresh_thread = None
        self._stop_refresh = False
    
    def register_chart(self, chart_id: str, chart_config: Dict[str, Any]):
        """注册图表"""
        self.charts[chart_id] = chart_config
    
    def register_update_callback(self, chart_id: str, callback: Callable):
        """注册更新回调"""
        self.update_callbacks[chart_id] = callback
    
    def start_auto_refresh(self):
        """启动自动刷新"""
        if not self.auto_refresh_enabled:
            self.auto_refresh_enabled = True
            self._stop_refresh = False
            self._refresh_thread = threading.Thread(target=self._refresh_loop)
            self._refresh_thread.daemon = True
            self._refresh_thread.start()
    
    def stop_auto_refresh(self):
        """停止自动刷新"""
        self.auto_refresh_enabled = False
        self._stop_refresh = True
        if self._refresh_thread:
            self._refresh_thread.join(timeout=1)
    
    def _refresh_loop(self):
        """刷新循环"""
        while not self._stop_refresh:
            try:
                for chart_id, callback in self.update_callbacks.items():
                    if chart_id in self.charts:
                        callback()
                time.sleep(self.refresh_interval)
            except Exception as e:
                st.error(f"图表刷新失败: {e}")
                break


class InteractiveChart3D:
    """3D交互式图表组件"""
    
    def __init__(self, chart_id: str, title: str = "3D图表"):
        self.chart_id = chart_id
        self.title = title
        self.data_cache = {}
        self.config = {
            "displayModeBar": True,
            "displaylogo": False,
            "modeBarButtonsToRemove": ['pan2d', 'lasso2d']
        }
    
    def create_scatter_3d(self, data: pd.DataFrame, x_col: str, y_col: str, z_col: str,
                         color_col: Optional[str] = None, size_col: Optional[str] = None,
                         hover_data: Optional[List[str]] = None) -> go.Figure:
        """创建3D散点图"""
        try:
            if data.empty:
                return self._create_empty_3d_figure()
            
            # 准备标记参数
            marker_params = {
                "size": 8,
                "opacity": 0.8,
                "line": dict(width=0.5, color='DarkSlateGrey')
            }
            
            # 颜色映射
            if color_col and color_col in data.columns:
                marker_params["color"] = data[color_col]
                marker_params["colorscale"] = "Viridis"
                marker_params["colorbar"] = dict(title=color_col)
            
            # 大小映射
            if size_col and size_col in data.columns:
                marker_params["size"] = data[size_col]
                marker_params["sizemode"] = "diameter"
                marker_params["sizeref"] = 2. * max(data[size_col]) / (40.**2)
                marker_params["sizemin"] = 4
            
            # 悬停数据
            hover_template = f"<b>{x_col}</b>: %{{x}}<br><b>{y_col}</b>: %{{y}}<br><b>{z_col}</b>: %{{z}}<br>"
            if hover_data:
                for col in hover_data:
                    if col in data.columns:
                        hover_template += f"<b>{col}</b>: %{{customdata[{hover_data.index(col)}]}}<br>"
            hover_template += "<extra></extra>"
            
            # 创建图表
            fig = go.Figure(data=go.Scatter3d(
                x=data[x_col],
                y=data[y_col],
                z=data[z_col],
                mode='markers',
                marker=marker_params,
                customdata=data[hover_data].values if hover_data else None,
                hovertemplate=hover_template,
                name="数据点"
            ))
            
            # 添加控制面板
            self._add_3d_controls(fig)
            
            # 设置布局
            fig.update_layout(
                title=self.title,
                scene=dict(
                    xaxis_title=x_col,
                    yaxis_title=y_col,
                    zaxis_title=z_col,
                    camera=dict(
                        eye=dict(x=1.5, y=1.5, z=1.5)
                    )
                ),
                width=800,
                height=600
            )
            
            return fig
            
        except Exception as e:
            st.error(f"创建3D散点图失败: {e}")
            return self._create_empty_3d_figure()
    
    def create_surface_3d(self, x_data: np.ndarray, y_data: np.ndarray, z_data: np.ndarray,
                         colorscale: str = "Viridis") -> go.Figure:
        """创建3D表面图"""
        try:
            fig = go.Figure(data=go.Surface(
                x=x_data,
                y=y_data,
                z=z_data,
                colorscale=colorscale,
                colorbar=dict(title="值"),
                hovertemplate="X: %{x}<br>Y: %{y}<br>Z: %{z:.4f}<extra></extra>"
            ))
            
            # 添加等高线投影
            fig.add_trace(go.Contour(
                x=x_data,
                y=y_data,
                z=z_data,
                colorscale=colorscale,
                showscale=False,
                opacity=0.3,
                contours=dict(
                    z=dict(show=True, usecolormap=True, project_z=True)
                )
            ))
            
            fig.update_layout(
                title=self.title,
                scene=dict(
                    camera=dict(
                        eye=dict(x=1.5, y=1.5, z=1.5)
                    )
                ),
                width=800,
                height=600
            )
            
            return fig
            
        except Exception as e:
            st.error(f"创建3D表面图失败: {e}")
            return self._create_empty_3d_figure()
    
    def _add_3d_controls(self, fig: go.Figure):
        """添加3D控制"""
        # 添加相机控制按钮
        fig.update_layout(
            updatemenus=[
                dict(
                    type="buttons",
                    direction="left",
                    buttons=list([
                        dict(
                            args=[{"scene.camera.eye": {"x": 1.5, "y": 1.5, "z": 1.5}}],
                            label="默认视角",
                            method="relayout"
                        ),
                        dict(
                            args=[{"scene.camera.eye": {"x": 2.5, "y": 0, "z": 0}}],
                            label="侧视图",
                            method="relayout"
                        ),
                        dict(
                            args=[{"scene.camera.eye": {"x": 0, "y": 0, "z": 2.5}}],
                            label="俯视图",
                            method="relayout"
                        )
                    ]),
                    pad={"r": 10, "t": 10},
                    showactive=True,
                    x=0.01,
                    xanchor="left",
                    y=1.02,
                    yanchor="top"
                ),
            ]
        )
    
    def _create_empty_3d_figure(self) -> go.Figure:
        """创建空的3D图表"""
        fig = go.Figure()
        fig.add_trace(go.Scatter3d(x=[], y=[], z=[], mode='markers'))
        fig.update_layout(
            title=f"{self.title} (无数据)",
            scene=dict(
                xaxis_title="X",
                yaxis_title="Y",
                zaxis_title="Z"
            )
        )
        return fig


class RealTimeChart:
    """实时图表组件"""
    
    def __init__(self, chart_id: str, title: str = "实时图表", max_points: int = 100):
        self.chart_id = chart_id
        self.title = title
        self.max_points = max_points
        self.data_buffer = []
        self.timestamps = []
    
    def add_data_point(self, timestamp: datetime, **kwargs):
        """添加数据点"""
        self.timestamps.append(timestamp)
        self.data_buffer.append(kwargs)
        
        # 保持缓冲区大小
        if len(self.data_buffer) > self.max_points:
            self.data_buffer.pop(0)
            self.timestamps.pop(0)
    
    def create_realtime_line_chart(self, metrics: List[str]) -> go.Figure:
        """创建实时线图"""
        try:
            if not self.data_buffer:
                return self._create_empty_figure()
            
            fig = go.Figure()
            
            colors = px.colors.qualitative.Set1
            
            for i, metric in enumerate(metrics):
                values = [point.get(metric, 0) for point in self.data_buffer]
                
                fig.add_trace(go.Scatter(
                    x=self.timestamps,
                    y=values,
                    mode='lines+markers',
                    name=metric,
                    line=dict(color=colors[i % len(colors)], width=2),
                    marker=dict(size=4)
                ))
            
            fig.update_layout(
                title=self.title,
                xaxis_title="时间",
                yaxis_title="值",
                hovermode='x unified',
                showlegend=True
            )
            
            return fig
            
        except Exception as e:
            st.error(f"创建实时线图失败: {e}")
            return self._create_empty_figure()
    
    def create_realtime_gauge(self, metric: str, min_val: float = 0, max_val: float = 1) -> go.Figure:
        """创建实时仪表盘"""
        try:
            current_value = 0
            if self.data_buffer:
                current_value = self.data_buffer[-1].get(metric, 0)
            
            fig = go.Figure(go.Indicator(
                mode="gauge+number+delta",
                value=current_value,
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': metric},
                delta={'reference': self.data_buffer[-2].get(metric, 0) if len(self.data_buffer) > 1 else 0},
                gauge={
                    'axis': {'range': [None, max_val]},
                    'bar': {'color': "darkblue"},
                    'steps': [
                        {'range': [0, max_val * 0.5], 'color': "lightgray"},
                        {'range': [max_val * 0.5, max_val * 0.8], 'color': "gray"}
                    ],
                    'threshold': {
                        'line': {'color': "red", 'width': 4},
                        'thickness': 0.75,
                        'value': max_val * 0.9
                    }
                }
            ))
            
            fig.update_layout(
                title=self.title,
                height=400
            )
            
            return fig
            
        except Exception as e:
            st.error(f"创建实时仪表盘失败: {e}")
            return self._create_empty_figure()
    
    def _create_empty_figure(self) -> go.Figure:
        """创建空图表"""
        fig = go.Figure()
        fig.add_trace(go.Scatter(x=[], y=[], mode='markers'))
        fig.update_layout(title=f"{self.title} (无数据)")
        return fig


class InteractiveHeatmap:
    """交互式热力图组件"""
    
    def __init__(self, chart_id: str, title: str = "热力图"):
        self.chart_id = chart_id
        self.title = title
    
    def create_correlation_heatmap(self, data: pd.DataFrame, 
                                  annotations: bool = True) -> go.Figure:
        """创建相关性热力图"""
        try:
            if data.empty:
                return self._create_empty_figure()
            
            # 计算相关性矩阵
            corr_matrix = data.corr()
            
            # 创建热力图
            fig = go.Figure(data=go.Heatmap(
                z=corr_matrix.values,
                x=corr_matrix.columns,
                y=corr_matrix.index,
                colorscale='RdBu',
                zmid=0,
                colorbar=dict(title="相关系数"),
                hovertemplate="<b>%{x}</b> vs <b>%{y}</b><br>相关系数: %{z:.3f}<extra></extra>"
            ))
            
            # 添加文本注释
            if annotations:
                for i in range(len(corr_matrix.index)):
                    for j in range(len(corr_matrix.columns)):
                        value = corr_matrix.iloc[i, j]
                        fig.add_annotation(
                            x=corr_matrix.columns[j],
                            y=corr_matrix.index[i],
                            text=f"{value:.2f}",
                            showarrow=False,
                            font=dict(
                                color="white" if abs(value) > 0.5 else "black",
                                size=10
                            )
                        )
            
            fig.update_layout(
                title=self.title,
                width=600,
                height=500
            )
            
            return fig
            
        except Exception as e:
            st.error(f"创建相关性热力图失败: {e}")
            return self._create_empty_figure()
    
    def create_parameter_heatmap(self, results_data: List[Dict[str, Any]],
                               param1: str, param2: str, metric: str) -> go.Figure:
        """创建参数热力图"""
        try:
            df = pd.DataFrame(results_data)
            
            if df.empty:
                return self._create_empty_figure()
            
            # 创建数据透视表
            pivot_table = df.pivot_table(
                values=metric,
                index=param2,
                columns=param1,
                aggfunc='mean'
            )
            
            # 创建热力图
            fig = go.Figure(data=go.Heatmap(
                z=pivot_table.values,
                x=pivot_table.columns,
                y=pivot_table.index,
                colorscale='Viridis',
                colorbar=dict(title=metric),
                hovertemplate=f"<b>{param1}</b>: %{{x}}<br>" +
                             f"<b>{param2}</b>: %{{y}}<br>" +
                             f"<b>{metric}</b>: %{{z:.3f}}<extra></extra>"
            ))
            
            fig.update_layout(
                title=self.title,
                xaxis_title=param1,
                yaxis_title=param2,
                width=600,
                height=500
            )
            
            return fig
            
        except Exception as e:
            st.error(f"创建参数热力图失败: {e}")
            return self._create_empty_figure()
    
    def _create_empty_figure(self) -> go.Figure:
        """创建空图表"""
        fig = go.Figure()
        fig.add_trace(go.Heatmap(z=[[]], colorscale='Viridis'))
        fig.update_layout(title=f"{self.title} (无数据)")
        return fig


def create_interactive_dashboard(charts_config: List[Dict[str, Any]]) -> None:
    """创建交互式仪表板"""
    try:
        # 创建图表控制器
        controller = InteractiveChartController()
        
        # 布局配置
        cols = st.columns(2)
        
        for i, chart_config in enumerate(charts_config):
            chart_type = chart_config.get("type", "line")
            chart_id = chart_config.get("id", f"chart_{i}")
            title = chart_config.get("title", f"图表 {i+1}")
            
            with cols[i % 2]:
                st.subheader(title)
                
                # 根据图表类型创建相应的图表
                if chart_type == "3d_scatter":
                    chart = InteractiveChart3D(chart_id, title)
                    # 这里需要实际的数据
                    fig = chart._create_empty_3d_figure()
                    st.plotly_chart(fig, use_container_width=True)
                
                elif chart_type == "realtime_line":
                    chart = RealTimeChart(chart_id, title)
                    fig = chart._create_empty_figure()
                    st.plotly_chart(fig, use_container_width=True)
                
                elif chart_type == "heatmap":
                    chart = InteractiveHeatmap(chart_id, title)
                    fig = chart._create_empty_figure()
                    st.plotly_chart(fig, use_container_width=True)
                
                # 注册图表
                controller.register_chart(chart_id, chart_config)
        
        # 控制面板
        with st.sidebar:
            st.subheader("图表控制")
            
            auto_refresh = st.checkbox("自动刷新", value=False)
            if auto_refresh:
                refresh_interval = st.slider("刷新间隔(秒)", 1, 60, 5)
                controller.refresh_interval = refresh_interval
                controller.start_auto_refresh()
            else:
                controller.stop_auto_refresh()
            
            if st.button("手动刷新"):
                st.rerun()
    
    except Exception as e:
        st.error(f"创建交互式仪表板失败: {e}")


def test_interactive_charts():
    """测试交互式图表组件"""
    print("🧪 测试交互式图表组件...")
    
    # 测试3D图表
    chart_3d = InteractiveChart3D("test_3d", "测试3D图表")
    
    # 生成测试数据
    np.random.seed(42)
    test_data = pd.DataFrame({
        'x': np.random.randn(100),
        'y': np.random.randn(100),
        'z': np.random.randn(100),
        'color': np.random.rand(100),
        'size': np.random.randint(5, 20, 100)
    })
    
    fig_3d = chart_3d.create_scatter_3d(test_data, 'x', 'y', 'z', 'color', 'size')
    print("✅ 3D散点图创建完成")
    
    # 测试实时图表
    realtime_chart = RealTimeChart("test_realtime", "测试实时图表")
    
    # 添加测试数据点
    for i in range(20):
        timestamp = datetime.now() - timedelta(minutes=20-i)
        realtime_chart.add_data_point(
            timestamp,
            metric1=np.random.rand(),
            metric2=np.random.rand()
        )
    
    fig_realtime = realtime_chart.create_realtime_line_chart(['metric1', 'metric2'])
    print("✅ 实时线图创建完成")
    
    # 测试热力图
    heatmap_chart = InteractiveHeatmap("test_heatmap", "测试热力图")
    
    # 生成相关性数据
    corr_data = pd.DataFrame(np.random.randn(50, 5), columns=['A', 'B', 'C', 'D', 'E'])
    fig_heatmap = heatmap_chart.create_correlation_heatmap(corr_data)
    print("✅ 相关性热力图创建完成")
    
    print("✅ 交互式图表组件测试完成！")


if __name__ == "__main__":
    test_interactive_charts()
