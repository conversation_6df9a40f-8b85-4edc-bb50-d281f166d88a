"""
形态转换预测系统
实现组三/组六转换、奇偶比、大小比等形态转换预测
"""

import itertools
import os
import sqlite3
from collections import Counter, defaultdict, deque
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd


class PatternPredictor:
    """形态转换预测器"""
    
    def __init__(self, db_path: str = None, pattern_window: int = 50,
                 transition_window_size: int = 1000,
                 probability_window_size: int = 500,
                 smoothing_alpha: float = 1.0):
        """
        初始化形态预测器

        Args:
            db_path: 数据库路径
            pattern_window: 模式分析窗口大小
            transition_window_size: 转移矩阵数据窗口大小，默认1000期
            probability_window_size: 数字概率计算窗口大小，默认500期
            smoothing_alpha: 拉普拉斯平滑参数，默认1.0
        """
        self.db_path = db_path or os.path.join('data', 'lottery.db')
        self.pattern_window = pattern_window
        self.transition_window_size = transition_window_size
        self.probability_window_size = probability_window_size
        self.smoothing_alpha = smoothing_alpha
        self.pattern_models = {}
        
    def load_pattern_data(self, limit: int = 1000) -> List[Dict[str, Any]]:
        """
        加载用于模式分析的数据
        
        Args:
            limit: 加载的记录数量
            
        Returns:
            开奖记录列表
        """
        if not os.path.exists(self.db_path):
            raise FileNotFoundError(f"数据库文件不存在: {self.db_path}")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT period, date, numbers
            FROM lottery_records
            WHERE numbers IS NOT NULL
            AND numbers != ''
            AND LENGTH(numbers) = 3
            ORDER BY date DESC, period DESC
            LIMIT ?
        """, (limit,))
        
        records = []
        for row in cursor.fetchall():
            record = {
                'period': row[0],
                'date': row[1],
                'numbers': row[2]
            }
            records.append(record)
        
        conn.close()
        return records
    
    def extract_pattern_features(self, numbers: str) -> Dict[str, Any]:
        """
        提取号码的形态特征
        
        Args:
            numbers: 三位数号码
            
        Returns:
            形态特征字典
        """
        if len(numbers) != 3:
            return {}
        
        digits = [int(d) for d in numbers]
        
        features = {
            # 基础特征
            'sum_value': sum(digits),
            'span_value': max(digits) - min(digits),
            'unique_count': len(set(digits)),
            
            # 形态特征
            'form_type': self._get_form_type(digits),
            'parity_pattern': self._get_parity_pattern(digits),
            'size_pattern': self._get_size_pattern(digits),
            'position_pattern': self._get_position_pattern(digits),
            
            # 数字分布特征
            'digit_distribution': self._get_digit_distribution(digits),
            'consecutive_count': self._get_consecutive_count(digits),
            'repeat_pattern': self._get_repeat_pattern(digits),
            
            # 高级特征
            'symmetry_score': self._calculate_symmetry(digits),
            'complexity_score': self._calculate_complexity(digits),
            'balance_score': self._calculate_balance(digits)
        }
        
        return features
    
    def _get_form_type(self, digits: List[int]) -> str:
        """获取形态类型"""
        unique_count = len(set(digits))
        if unique_count == 1:
            return 'triple'  # 豹子
        elif unique_count == 2:
            return 'group3'  # 组三
        else:
            return 'group6'  # 组六
    
    def _get_parity_pattern(self, digits: List[int]) -> str:
        """获取奇偶模式"""
        odd_count = sum(1 for d in digits if d % 2 == 1)
        patterns = {3: '3odd', 2: '2odd1even', 1: '1odd2even', 0: '3even'}
        return patterns[odd_count]
    
    def _get_size_pattern(self, digits: List[int]) -> str:
        """获取大小模式"""
        big_count = sum(1 for d in digits if d >= 5)
        patterns = {3: '3big', 2: '2big1small', 1: '1big2small', 0: '3small'}
        return patterns[big_count]
    
    def _get_position_pattern(self, digits: List[int]) -> str:
        """获取位置关系模式"""
        h, t, u = digits
        if h > t > u:
            return 'descending'
        elif h < t < u:
            return 'ascending'
        elif h == t == u:
            return 'equal'
        elif h > t and t < u:
            return 'valley'
        elif h < t and t > u:
            return 'peak'
        else:
            return 'irregular'
    
    def _get_digit_distribution(self, digits: List[int]) -> Dict[str, int]:
        """获取数字分布"""
        ranges = {
            'range_0_2': sum(1 for d in digits if 0 <= d <= 2),
            'range_3_4': sum(1 for d in digits if 3 <= d <= 4),
            'range_5_6': sum(1 for d in digits if 5 <= d <= 6),
            'range_7_9': sum(1 for d in digits if 7 <= d <= 9)
        }
        return ranges
    
    def _get_consecutive_count(self, digits: List[int]) -> int:
        """获取连续数字个数"""
        sorted_digits = sorted(digits)
        consecutive = 0
        for i in range(len(sorted_digits) - 1):
            if sorted_digits[i+1] - sorted_digits[i] == 1:
                consecutive += 1
        return consecutive
    
    def _get_repeat_pattern(self, digits: List[int]) -> str:
        """获取重复模式"""
        counts = Counter(digits)
        max_count = max(counts.values())
        if max_count == 3:
            return 'triple'
        elif max_count == 2:
            return 'pair'
        else:
            return 'unique'
    
    def _calculate_symmetry(self, digits: List[int]) -> float:
        """计算对称性分数"""
        h, t, u = digits
        # 检查是否关于中位对称
        if h == u:
            return 1.0
        else:
            return 1.0 - abs(h - u) / 9.0
    
    def _calculate_complexity(self, digits: List[int]) -> float:
        """计算复杂度分数"""
        # 基于数字的分散程度
        mean_val = np.mean(digits)
        variance = np.var(digits)
        return min(variance / 10.0, 1.0)
    
    def _calculate_balance(self, digits: List[int]) -> float:
        """计算平衡性分数"""
        # 基于奇偶和大小的平衡
        odd_count = sum(1 for d in digits if d % 2 == 1)
        big_count = sum(1 for d in digits if d >= 5)
        
        odd_balance = 1.0 - abs(odd_count - 1.5) / 1.5
        size_balance = 1.0 - abs(big_count - 1.5) / 1.5
        
        return (odd_balance + size_balance) / 2.0
    
    def analyze_pattern_transitions(self, records: List[Dict]) -> Dict[str, Any]:
        """
        分析形态转换规律
        
        Args:
            records: 开奖记录列表
            
        Returns:
            形态转换分析结果
        """
        if len(records) < 2:
            return {}
        
        transitions = {
            'form_transitions': defaultdict(Counter),
            'parity_transitions': defaultdict(Counter),
            'size_transitions': defaultdict(Counter),
            'sum_transitions': defaultdict(list),
            'span_transitions': defaultdict(list),
            'transition_probabilities': {}
        }
        
        # 提取所有记录的特征
        features_sequence = []
        for record in records:
            features = self.extract_pattern_features(record['numbers'])
            features_sequence.append(features)
        
        # 分析转换模式
        for i in range(len(features_sequence) - 1):
            current = features_sequence[i]
            next_features = features_sequence[i + 1]
            
            # 形态转换
            current_form = current['form_type']
            next_form = next_features['form_type']
            transitions['form_transitions'][current_form][next_form] += 1
            
            # 奇偶转换
            current_parity = current['parity_pattern']
            next_parity = next_features['parity_pattern']
            transitions['parity_transitions'][current_parity][next_parity] += 1
            
            # 大小转换
            current_size = current['size_pattern']
            next_size = next_features['size_pattern']
            transitions['size_transitions'][current_size][next_size] += 1
            
            # 和值转换
            current_sum = current['sum_value']
            next_sum = next_features['sum_value']
            transitions['sum_transitions'][current_sum].append(next_sum)
            
            # 跨度转换
            current_span = current['span_value']
            next_span = next_features['span_value']
            transitions['span_transitions'][current_span].append(next_span)
        
        # 计算转换概率
        for transition_type in ['form_transitions', 'parity_transitions', 'size_transitions']:
            transitions['transition_probabilities'][transition_type] = {}
            
            for current_state, next_states in transitions[transition_type].items():
                total = sum(next_states.values())
                if total > 0:
                    probabilities = {next_state: count / total 
                                   for next_state, count in next_states.items()}
                    transitions['transition_probabilities'][transition_type][current_state] = probabilities
        
        return transitions
    
    def detect_pattern_cycles(self, records: List[Dict]) -> Dict[str, Any]:
        """
        检测形态周期性
        
        Args:
            records: 开奖记录列表
            
        Returns:
            周期性检测结果
        """
        cycles = {
            'form_cycles': {},
            'parity_cycles': {},
            'size_cycles': {},
            'sum_cycles': {},
            'detected_periods': []
        }
        
        if len(records) < 20:
            return cycles
        
        # 提取特征序列
        form_sequence = []
        parity_sequence = []
        size_sequence = []
        sum_sequence = []
        
        for record in records:
            features = self.extract_pattern_features(record['numbers'])
            form_sequence.append(features['form_type'])
            parity_sequence.append(features['parity_pattern'])
            size_sequence.append(features['size_pattern'])
            sum_sequence.append(features['sum_value'])
        
        # 检测周期性
        sequences = {
            'form_cycles': form_sequence,
            'parity_cycles': parity_sequence,
            'size_cycles': size_sequence,
            'sum_cycles': sum_sequence
        }
        
        for seq_name, sequence in sequences.items():
            cycles[seq_name] = self._detect_sequence_cycles(sequence)
        
        return cycles
    
    def _detect_sequence_cycles(self, sequence: List) -> Dict[str, Any]:
        """
        检测序列的周期性
        
        Args:
            sequence: 序列数据
            
        Returns:
            周期性检测结果
        """
        cycle_info = {
            'detected_cycles': [],
            'cycle_strength': 0,
            'dominant_period': None
        }
        
        if len(sequence) < 10:
            return cycle_info
        
        # 检测可能的周期长度（2到序列长度的一半）
        max_period = min(20, len(sequence) // 2)
        cycle_scores = {}
        
        for period in range(2, max_period + 1):
            matches = 0
            total_comparisons = 0
            
            for i in range(len(sequence) - period):
                if sequence[i] == sequence[i + period]:
                    matches += 1
                total_comparisons += 1
            
            if total_comparisons > 0:
                cycle_score = matches / total_comparisons
                cycle_scores[period] = cycle_score
                
                if cycle_score > 0.3:  # 阈值
                    cycle_info['detected_cycles'].append({
                        'period': period,
                        'strength': cycle_score,
                        'matches': matches,
                        'total': total_comparisons
                    })
        
        if cycle_scores:
            best_period = max(cycle_scores.items(), key=lambda x: x[1])
            cycle_info['dominant_period'] = best_period[0]
            cycle_info['cycle_strength'] = best_period[1]
        
        return cycle_info

    def _calculate_pattern_data_sensitivity(self, records: List[Dict]) -> float:
        """
        计算形态数据敏感性因子

        Args:
            records: 开奖记录列表

        Returns:
            数据敏感性因子 (0.8-1.2)
        """
        import random
        import time

        if len(records) < 5:
            return 1.0

        # 分析最近5期的形态变化
        recent_forms = []
        for record in records[:5]:
            if 'numbers' in record and record['numbers']:
                features = self.extract_pattern_features(record['numbers'])
                recent_forms.append(features['form_type'])

        # 计算形态多样性
        form_diversity = len(set(recent_forms)) / len(recent_forms) if recent_forms else 0.5

        # 基于时间和形态多样性计算敏感性
        time_factor = (int(time.time()) % 100) / 100.0
        random_factor = random.random() * 0.2

        # 组合敏感性因子
        sensitivity = 0.8 + (form_diversity * 0.2) + (time_factor * 0.1) + random_factor
        return min(max(sensitivity, 0.8), 1.2)

    def predict_next_patterns(self, records: List[Dict]) -> Dict[str, Any]:
        """
        预测下期形态
        
        Args:
            records: 开奖记录列表
            
        Returns:
            形态预测结果
        """
        if len(records) < 10:
            return {}
        
        # 获取转换分析和周期检测结果
        transitions = self.analyze_pattern_transitions(records)
        cycles = self.detect_pattern_cycles(records)
        
        # 获取最近一期的特征
        latest_features = self.extract_pattern_features(records[-1]['numbers'])

        # 计算数据敏感性因子
        data_sensitivity = self._calculate_pattern_data_sensitivity(records)

        predictions = {
            'form_prediction': {},
            'parity_prediction': {},
            'size_prediction': {},
            'sum_prediction': {},
            'span_prediction': {},
            'confidence_scores': {},
            'data_sensitivity_factor': data_sensitivity
        }
        
        # 基于转换概率预测形态
        if 'form_transitions' in transitions['transition_probabilities']:
            current_form = latest_features['form_type']
            if current_form in transitions['transition_probabilities']['form_transitions']:
                form_probs = transitions['transition_probabilities']['form_transitions'][current_form]
                predictions['form_prediction'] = {
                    'probabilities': form_probs,
                    'most_likely': max(form_probs.items(), key=lambda x: x[1]) if form_probs else None
                }
        
        # 基于转换概率预测奇偶
        if 'parity_transitions' in transitions['transition_probabilities']:
            current_parity = latest_features['parity_pattern']
            if current_parity in transitions['transition_probabilities']['parity_transitions']:
                parity_probs = transitions['transition_probabilities']['parity_transitions'][current_parity]
                predictions['parity_prediction'] = {
                    'probabilities': parity_probs,
                    'most_likely': max(parity_probs.items(), key=lambda x: x[1]) if parity_probs else None
                }
        
        # 基于转换概率预测大小
        if 'size_transitions' in transitions['transition_probabilities']:
            current_size = latest_features['size_pattern']
            if current_size in transitions['transition_probabilities']['size_transitions']:
                size_probs = transitions['transition_probabilities']['size_transitions'][current_size]
                predictions['size_prediction'] = {
                    'probabilities': size_probs,
                    'most_likely': max(size_probs.items(), key=lambda x: x[1]) if size_probs else None
                }
        
        # 预测和值范围
        current_sum = latest_features['sum_value']
        if current_sum in transitions['sum_transitions']:
            next_sums = transitions['sum_transitions'][current_sum]
            if next_sums:
                predictions['sum_prediction'] = {
                    'mean': np.mean(next_sums),
                    'std': np.std(next_sums),
                    'range': (min(next_sums), max(next_sums)),
                    'most_common': Counter(next_sums).most_common(3)
                }
        
        # 预测跨度范围
        current_span = latest_features['span_value']
        if current_span in transitions['span_transitions']:
            next_spans = transitions['span_transitions'][current_span]
            if next_spans:
                predictions['span_prediction'] = {
                    'mean': np.mean(next_spans),
                    'std': np.std(next_spans),
                    'range': (min(next_spans), max(next_spans)),
                    'most_common': Counter(next_spans).most_common(3)
                }
        
        # 计算置信度
        predictions['confidence_scores'] = {
            'form_confidence': len(predictions['form_prediction'].get('probabilities', {})) / 3,
            'parity_confidence': len(predictions['parity_prediction'].get('probabilities', {})) / 4,
            'size_confidence': len(predictions['size_prediction'].get('probabilities', {})) / 4,
            'cycle_confidence': max([c['cycle_strength'] for c in cycles.values() if isinstance(c, dict) and 'cycle_strength' in c] + [0])
        }
        
        return predictions

    def _build_transition_matrix(self, data: List[Dict], window_size: int = 1000) -> np.ndarray:
        """
        构建数字转移概率矩阵

        Args:
            data: 历史开奖数据
            window_size: 数据窗口大小，默认1000期

        Returns:
            10x10转移概率矩阵
        """
        matrix = np.zeros((10, 10))

        # 使用指定窗口大小的最近数据构建转移矩阵
        # 充分利用8000+期历史数据，从100期扩展到1000期
        recent_data = data[-window_size:] if len(data) > window_size else data

        for record in recent_data:
            numbers = record.get('numbers', '')
            if len(numbers) >= 3:
                # 分析数字间的转移关系
                for i in range(len(numbers) - 1):
                    try:
                        curr_digit = int(numbers[i])
                        next_digit = int(numbers[i + 1])
                        matrix[curr_digit][next_digit] += 1
                    except (ValueError, IndexError):
                        continue

        # 应用拉普拉斯平滑并归一化为概率矩阵
        matrix = self._apply_laplace_smoothing(matrix, alpha=self.smoothing_alpha)

        return matrix

    def _apply_laplace_smoothing(self, matrix: np.ndarray, alpha: float = 1.0) -> np.ndarray:
        """
        应用拉普拉斯平滑到转移矩阵

        Args:
            matrix: 原始计数矩阵
            alpha: 平滑参数，默认1.0

        Returns:
            平滑后的概率矩阵
        """
        # 拉普拉斯平滑公式: P(X_{t+1} | X_t) = (count + α) / (total + α × |S|)
        # |S| = 10 (状态空间大小，0-9数字)
        state_space_size = 10

        # 应用拉普拉斯平滑
        smoothed_matrix = matrix + alpha

        # 归一化为概率矩阵
        row_sums = smoothed_matrix.sum(axis=1)
        num_rows = smoothed_matrix.shape[0]
        for i in range(num_rows):
            if row_sums[i] > 0:
                smoothed_matrix[i] = smoothed_matrix[i] / row_sums[i]
            else:
                # 如果某行全为0，使用均匀分布
                smoothed_matrix[i] = np.ones(smoothed_matrix.shape[1]) / smoothed_matrix.shape[1]

        return smoothed_matrix

    def _calculate_digit_probabilities(self, data: List[Dict], window_size: int = 500) -> np.ndarray:
        """
        计算数字出现概率分布

        Args:
            data: 历史开奖数据
            window_size: 数据窗口大小，默认500期

        Returns:
            长度为10的概率数组
        """
        counts = np.zeros(10)

        # 使用指定窗口大小的最近数据计算概率
        # 从50期扩展到500期，提高概率估计的稳定性
        recent_data = data[-window_size:] if len(data) > window_size else data

        for record in recent_data:
            numbers = record.get('numbers', '')
            for digit_char in numbers:
                try:
                    digit = int(digit_char)
                    counts[digit] += 1
                except ValueError:
                    continue

        # 归一化为概率
        total = counts.sum()
        if total > 0:
            return counts / total
        else:
            # 如果没有数据，返回均匀分布
            return np.ones(10) / 10

    def _generate_markov_sequence(self, transition_matrix: np.ndarray,
                                 digit_probs: np.ndarray,
                                 temperature: float = 1.0) -> str:
        """
        使用马尔可夫链生成三位数序列

        Args:
            transition_matrix: 转移概率矩阵
            digit_probs: 数字概率分布
            temperature: 温度参数控制随机性

        Returns:
            三位数字符串
        """
        # 应用温度控制
        scaled_probs = digit_probs ** (1.0 / temperature)
        scaled_probs = scaled_probs / scaled_probs.sum()

        # 生成第一个数字
        first_digit = np.random.choice(10, p=scaled_probs)

        # 使用马尔可夫链生成后续数字
        sequence = [first_digit]
        current_digit = first_digit

        for _ in range(2):  # 生成剩余两位
            # 获取当前数字的转移概率
            next_probs = transition_matrix[current_digit]

            # 应用温度控制
            scaled_next_probs = next_probs ** (1.0 / temperature)
            if scaled_next_probs.sum() > 0:
                scaled_next_probs = scaled_next_probs / scaled_next_probs.sum()
            else:
                scaled_next_probs = np.ones(10) / 10

            # 选择下一个数字
            next_digit = np.random.choice(10, p=scaled_next_probs)
            sequence.append(next_digit)
            current_digit = next_digit

        return ''.join(map(str, sequence))

    def _calculate_markov_confidence(self, sequence: str, historical_data: List[Dict]) -> float:
        """
        计算马尔可夫链生成序列的置信度

        Args:
            sequence: 生成的三位数序列
            historical_data: 历史数据

        Returns:
            置信度分数
        """
        if len(sequence) != 3:
            return 0.0

        # 基于历史频率计算置信度
        recent_data = historical_data[-100:] if len(historical_data) > 100 else historical_data

        # 计算序列中各数字的历史频率
        digit_counts = Counter()
        for record in recent_data:
            numbers = record.get('numbers', '')
            for digit in numbers:
                if digit.isdigit():
                    digit_counts[digit] += 1

        total_digits = sum(digit_counts.values())
        if total_digits == 0:
            return 0.5

        # 计算序列的综合频率分数
        sequence_score = 1.0
        for digit in sequence:
            freq = digit_counts.get(digit, 0) / total_digits
            sequence_score *= (freq + 0.01)  # 添加平滑项

        # 归一化到[0,1]范围
        confidence = min(sequence_score * 100, 1.0)
        return max(confidence, 0.1)  # 最小置信度0.1

    def _ensure_diversity(self, candidates: List[Dict]) -> List[Dict]:
        """
        确保候选号码多样性

        Args:
            candidates: 候选号码列表

        Returns:
            多样性优化后的候选列表
        """
        if not candidates:
            return candidates

        # 计算辛普森多样性指数
        numbers = [c['numbers'] for c in candidates]
        unique_numbers = set(numbers)

        diversity_ratio = len(unique_numbers) / len(numbers)

        # 如果多样性不足，添加随机候选
        if diversity_ratio < 0.7:
            additional_count = max(5, int(len(candidates) * 0.3))
            random_candidates = self._generate_random_candidates(additional_count)
            candidates.extend(random_candidates)

        return candidates

    def _generate_random_candidates(self, count: int) -> List[Dict]:
        """
        生成随机候选号码

        Args:
            count: 生成数量

        Returns:
            随机候选列表
        """
        candidates = []
        for _ in range(count):
            # 生成随机三位数
            numbers = ''.join([str(np.random.randint(0, 10)) for _ in range(3)])
            candidates.append({
                'numbers': numbers,
                'confidence': np.random.uniform(0.1, 0.3),
                'strategy': 'random_diversity'
            })
        return candidates

    def generate_candidate_numbers(self, predictions: Dict[str, Any], top_k: int = 20) -> List[Dict[str, Any]]:
        """
        使用马尔可夫链生成候选号码

        Args:
            predictions: 形态预测结果
            top_k: 返回的候选号码数量

        Returns:
            候选号码列表
        """
        return self.generate_markov_candidates(predictions, top_k)

    def generate_markov_candidates(self, predictions: Dict[str, Any],
                                 num_candidates: int = 20,
                                 temperature: float = 1.0) -> List[Dict[str, Any]]:
        """
        使用马尔可夫链生成候选号码

        Args:
            predictions: 形态预测结果
            num_candidates: 候选数量
            temperature: 温度参数控制随机性

        Returns:
            候选号码列表
        """
        candidates = []

        try:
            # 加载历史数据
            historical_data = self.load_pattern_data(limit=200)

            if not historical_data:
                # 如果没有历史数据，生成随机候选
                return self._generate_random_candidates(num_candidates)

            # 构建马尔可夫链转移矩阵，使用配置的数据窗口
            transition_matrix = self._build_transition_matrix(historical_data, window_size=self.transition_window_size)

            # 计算数字概率分布，使用配置的数据窗口
            digit_probs = self._calculate_digit_probabilities(historical_data, window_size=self.probability_window_size)

            # 生成候选号码
            generated_numbers = set()  # 避免重复

            for i in range(num_candidates * 2):  # 生成更多候选以确保多样性
                # 使用马尔可夫链生成序列
                sequence = self._generate_markov_sequence(
                    transition_matrix, digit_probs, temperature
                )

                # 避免重复
                if sequence not in generated_numbers:
                    generated_numbers.add(sequence)

                    # 计算候选评分
                    confidence = self._calculate_markov_confidence(sequence, historical_data)

                    candidates.append({
                        'numbers': sequence,
                        'confidence': confidence,
                        'strategy': 'markov_chain',
                        'temperature': temperature
                    })

                # 如果已经有足够的候选，停止生成
                if len(candidates) >= num_candidates:
                    break

            # 确保多样性
            candidates = self._ensure_diversity(candidates)

            # 按置信度排序
            candidates.sort(key=lambda x: x['confidence'], reverse=True)

            # 返回指定数量的候选
            return candidates[:num_candidates]

        except Exception as e:
            print(f"马尔可夫链生成失败: {e}")
            # 回退到随机生成
            return self._generate_random_candidates(num_candidates)
    
    def train_model(self) -> Dict[str, Any]:
        """
        训练形态转换预测模型
        
        Returns:
            训练结果和模型性能
        """
        print("开始训练形态转换预测模型...")
        
        # 加载数据
        records = self.load_pattern_data(limit=1000)
        print(f"加载了 {len(records)} 条开奖记录")
        
        if len(records) < self.pattern_window:
            raise ValueError(f"数据不足，至少需要 {self.pattern_window} 条记录")
        
        # 执行形态分析
        print("分析形态转换规律...")
        transitions = self.analyze_pattern_transitions(records)
        
        print("检测形态周期性...")
        cycles = self.detect_pattern_cycles(records)
        
        print("生成形态预测...")
        predictions = self.predict_next_patterns(records)
        
        # 保存模型
        self.pattern_models = {
            'transitions': transitions,
            'cycles': cycles,
            'predictions': predictions,
            'data_summary': {
                'total_records': len(records),
                'pattern_window': self.pattern_window,
                'date_range': (records[0]['date'], records[-1]['date']) if records else None
            }
        }
        
        print("形态转换预测模型训练完成!")
        return {
            'success': True,
            'pattern_models': self.pattern_models
        }


if __name__ == "__main__":
    # 测试代码
    predictor = PatternPredictor(pattern_window=50)
    
    try:
        # 训练模型
        result = predictor.train_model()
        print("训练结果:", result['success'])
        
        if result['success']:
            # 显示一些预测信息
            models = result['pattern_models']
            
            print("\n形态转换概率摘要:")
            if 'form_transitions' in models['transitions']['transition_probabilities']:
                for current, probs in models['transitions']['transition_probabilities']['form_transitions'].items():
                    print(f"  {current} -> {max(probs.items(), key=lambda x: x[1])}")
            
            print("\n周期性检测摘要:")
            for cycle_type, cycle_info in models['cycles'].items():
                if isinstance(cycle_info, dict) and 'dominant_period' in cycle_info:
                    if cycle_info['dominant_period']:
                        print(f"  {cycle_type}: 周期{cycle_info['dominant_period']} (强度{cycle_info['cycle_strength']:.3f})")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
