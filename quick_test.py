import sys
sys.path.append('src')

from data.parser import DataParser

# 读取数据
with open('data/raw/3d_data_20250714_144231.txt', 'r', encoding='utf-8') as f:
    data = f.read()

# 解析数据
parser = DataParser()
records, report = parser.parse_data(data)

print(f"解析结果: {len(records)} 条记录")
print(f"质量评分: {report.quality_score}")

if records:
    print(f"最新3条记录:")
    for r in records[-3:]:
        print(f"  {r.period} {r.date} {r.numbers}")
