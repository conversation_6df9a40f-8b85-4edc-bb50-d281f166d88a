#!/usr/bin/env python3
"""
训练按钮功能演示
"""

import os
import sys

import streamlit as st

sys.path.append('src')

def show_training_button_demo():
    """展示训练按钮功能"""
    st.title("🚀 模型训练按钮功能演示")
    st.markdown("---")
    
    st.markdown("""
    ## 📋 功能说明
    
    根据您的建议，我们在模型详情页面的"训练完成"状态旁边添加了操作按钮：
    
    - **当训练完成状态为❌时**：显示"🚀 开始训练"按钮
    - **当训练完成状态为✅时**：显示"🔄 重新训练"按钮
    - **点击按钮后**：直接在当前页面触发训练并显示进度
    
    ## 🧪 实际测试结果
    """)
    
    # 显示模型状态
    try:
        from model_library.model_registry import ModelRegistry
        from model_library.status_manager import ModelStatusManager
        
        registry = ModelRegistry()
        status_manager = ModelStatusManager()
        
        st.subheader("📊 当前模型状态")
        
        models = registry.list_models(active_only=False)
        
        for model_info in models:
            with st.expander(f"🤖 {model_info.name} ({model_info.model_id})", expanded=True):
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    st.write(f"**模型类型**: {model_info.model_type.value}")
                    st.write(f"**版本**: {model_info.version}")
                    st.write(f"**作者**: {model_info.author}")
                    
                    # 获取状态信息
                    try:
                        status_info = status_manager.get_model_status(model_info.model_id)
                        if status_info:
                            st.write(f"**运行状态**: {status_info.status.value}")
                            st.write(f"**数据就绪**: {'✅' if status_info.data_ready else '❌'}")
                            st.write(f"**特征就绪**: {'✅' if status_info.features_ready else '❌'}")
                            
                            # 训练状态和操作按钮
                            train_col1, train_col2 = st.columns([2, 1])
                            with train_col1:
                                st.write(f"**训练完成**: {'✅' if status_info.trained else '❌'}")
                            with train_col2:
                                # 根据训练状态显示不同的按钮
                                if not status_info.trained:
                                    if st.button("🚀 开始训练", key=f"train_{model_info.model_id}", help="点击开始训练模型"):
                                        trigger_model_training(model_info.model_id, model_info.name)
                                else:
                                    if st.button("🔄 重新训练", key=f"retrain_{model_info.model_id}", help="点击重新训练模型"):
                                        trigger_model_training(model_info.model_id, model_info.name, force_retrain=True)
                                        
                            if status_info.error_message:
                                st.warning(f"⚠️ {status_info.error_message}")
                        else:
                            st.error("❌ 无法获取模型状态")
                    except Exception as e:
                        st.error(f"❌ 状态检查失败: {e}")
                        
    except Exception as e:
        st.error(f"❌ 加载模型信息失败: {e}")

def trigger_model_training(model_id: str, model_name: str, force_retrain: bool = False):
    """触发模型训练"""
    try:
        # 显示训练进度
        progress_placeholder = st.empty()
        status_placeholder = st.empty()
        
        with progress_placeholder.container():
            st.info(f"🚀 正在{'重新' if force_retrain else ''}训练模型: {model_name}")
            progress_bar = st.progress(0)
            
        # 根据模型类型选择训练方法
        if model_id == "intelligent_fusion":
            # 智能融合模型训练
            try:
                from prediction.intelligent_fusion import \
                    IntelligentFusionSystem
                intelligent_system = IntelligentFusionSystem()
                
                progress_bar.progress(20)
                status_placeholder.info("📊 正在准备训练数据...")
                
                training_result = intelligent_system.train_all_models(force_retrain=force_retrain)
                
                progress_bar.progress(80)
                status_placeholder.info("🔧 正在保存模型状态...")
                
                if training_result.get('success', False):
                    progress_bar.progress(100)
                    status_placeholder.success(f"✅ 模型 {model_name} 训练完成！")
                    st.balloons()
                    
                    # 显示训练结果详情
                    with st.expander("📊 查看训练详情", expanded=True):
                        st.json(training_result)
                        
                    # 提示刷新页面查看最新状态
                    st.info("💡 请刷新页面查看最新的模型状态")
                else:
                    progress_bar.progress(100)
                    status_placeholder.error(f"❌ 模型 {model_name} 训练失败")
                    st.error(f"训练失败原因: {training_result.get('error', '未知错误')}")
                    
            except Exception as e:
                progress_bar.progress(100)
                status_placeholder.error(f"❌ 训练过程中发生错误: {str(e)}")
                
        else:
            # 其他模型类型的训练
            progress_bar.progress(50)
            status_placeholder.info(f"🔧 正在训练 {model_name}...")
            
            # 模拟训练过程
            import time
            time.sleep(2)
            
            progress_bar.progress(100)
            status_placeholder.success(f"✅ 模型 {model_name} 训练完成！")
            st.info("💡 请刷新页面查看最新的模型状态")
                
    except Exception as e:
        st.error(f"❌ 训练触发失败: {str(e)}")

# 直接运行演示函数
show_training_button_demo()
