#!/usr/bin/env python3
"""
测试优化后的Bug检测算法
"""

import sys
import os
sys.path.insert(0, '.')

from src.bug_detection.algorithms.enhanced_detection import EnhancedBugDetector
from src.bug_detection.core.database_manager import DatabaseManager
import json

def test_enhanced_classification():
    print("=== 测试增强的错误分类算法 ===")
    
    # 初始化检测器
    db_manager = DatabaseManager()
    detector = EnhancedBugDetector(db_manager)
    
    # 测试用例
    test_cases = [
        {
            'name': 'UI错误',
            'data': {
                'type': 'javascript',
                'message': 'Cannot read property "innerHTML" of null',
                'stack_trace': 'at HTMLElement.render (component.js:45:12)',
                'page_url': 'http://127.0.0.1:8501/dashboard',
                'source': 'component.js'
            },
            'expected_category': 'ui'
        },
        {
            'name': 'API错误',
            'data': {
                'type': 'network',
                'message': 'HTTP 500 Internal Server Error',
                'stack_trace': 'at fetch (/api/v1/data)',
                'page_url': 'http://127.0.0.1:8501/api/data',
                'source': 'api_client.js'
            },
            'expected_category': 'api'
        },
        {
            'name': '数据库错误',
            'data': {
                'type': 'database',
                'message': 'Connection timeout to database server',
                'stack_trace': 'at DatabaseConnection.connect (db.py:123)',
                'page_url': 'http://127.0.0.1:8501/data',
                'source': 'database.py'
            },
            'expected_category': 'database'
        },
        {
            'name': '安全错误',
            'data': {
                'type': 'security',
                'message': 'Authentication failed - invalid token',
                'stack_trace': 'at AuthMiddleware.verify (auth.js:67)',
                'page_url': 'http://127.0.0.1:8501/login',
                'source': 'auth.js'
            },
            'expected_category': 'security'
        },
        {
            'name': '性能错误',
            'data': {
                'type': 'performance',
                'message': 'Request timeout after 30 seconds',
                'stack_trace': 'at TimeoutHandler (timeout.js:12)',
                'page_url': 'http://127.0.0.1:8501/slow-page',
                'source': 'timeout.js'
            },
            'expected_category': 'performance'
        }
    ]
    
    correct_classifications = 0
    total_tests = len(test_cases)
    
    for test_case in test_cases:
        print(f"\n🧪 测试: {test_case['name']}")
        
        result = detector.detect_and_classify(test_case['data'])
        
        actual_category = result.get('category', 'unknown')
        expected_category = test_case['expected_category']
        
        print(f"  预期分类: {expected_category}")
        print(f"  实际分类: {actual_category}")
        print(f"  严重程度: {result.get('severity', 'unknown')}")
        print(f"  优先级: {result.get('priority', 'unknown')}")
        print(f"  环境: {result.get('environment', 'unknown')}")
        print(f"  置信度: {result.get('confidence_score', 0):.2f}")
        
        if actual_category == expected_category:
            print("  ✅ 分类正确")
            correct_classifications += 1
        else:
            print("  ❌ 分类错误")
        
        # 显示修复建议
        fix_suggestions = result.get('fix_suggestions', [])
        if fix_suggestions:
            print(f"  💡 修复建议: {fix_suggestions[:2]}")  # 显示前2个建议
    
    accuracy = (correct_classifications / total_tests) * 100
    print(f"\n📊 分类准确率: {accuracy:.1f}% ({correct_classifications}/{total_tests})")
    
    return accuracy >= 80  # 期望准确率至少80%

def test_similarity_detection():
    print("\n=== 测试相似Bug检测算法 ===")
    
    db_manager = DatabaseManager()
    detector = EnhancedBugDetector(db_manager)
    
    # 创建两个相似的错误
    error1 = {
        'type': 'javascript',
        'message': 'Cannot read property "value" of null',
        'stack_trace': 'at HTMLInputElement.getValue (form.js:23:5)',
        'page_url': 'http://127.0.0.1:8501/form',
        'source': 'form.js'
    }
    
    error2 = {
        'error_type': 'javascript',
        'error_message': 'Cannot read property "innerHTML" of null',
        'stack_trace': 'at HTMLDivElement.render (component.js:45:12)',
        'page_name': 'form',
        'source': 'component.js'
    }
    
    similarity_score = detector._calculate_similarity_score(error1, error2)
    
    print(f"错误1: {error1['message']}")
    print(f"错误2: {error2['error_message']}")
    print(f"相似度得分: {similarity_score:.3f}")
    
    if similarity_score > 0.5:
        print("✅ 成功识别为相似错误")
        return True
    else:
        print("❌ 未能识别为相似错误")
        return False

def test_severity_assessment():
    print("\n=== 测试严重程度评估算法 ===")
    
    db_manager = DatabaseManager()
    detector = EnhancedBugDetector(db_manager)
    
    test_cases = [
        {
            'name': '关键安全错误',
            'data': {
                'type': 'security',
                'message': 'SQL injection detected in user input',
                'stack_trace': 'at SecurityFilter.validate (security.py:89)',
                'page_url': 'http://production.example.com/login'
            },
            'expected_severity': 'critical'
        },
        {
            'name': '数据库连接错误',
            'data': {
                'type': 'database',
                'message': 'Database connection failed',
                'stack_trace': 'at DatabaseManager.connect (db.py:45)',
                'page_url': 'http://127.0.0.1:8501/data'
            },
            'expected_severity': 'high'
        },
        {
            'name': '一般UI错误',
            'data': {
                'type': 'javascript',
                'message': 'Element not found in DOM',
                'stack_trace': 'at Component.render (ui.js:12)',
                'page_url': 'http://127.0.0.1:8501/dashboard'
            },
            'expected_severity': 'medium'
        }
    ]
    
    correct_assessments = 0
    total_tests = len(test_cases)
    
    for test_case in test_cases:
        print(f"\n🧪 测试: {test_case['name']}")
        
        result = detector.detect_and_classify(test_case['data'])
        
        actual_severity = result.get('severity', 'unknown')
        expected_severity = test_case['expected_severity']
        
        print(f"  预期严重程度: {expected_severity}")
        print(f"  实际严重程度: {actual_severity}")
        print(f"  分类: {result.get('category', 'unknown')}")
        print(f"  优先级: {result.get('priority', 'unknown')}")
        
        # 允许一定的严重程度差异
        severity_levels = ['low', 'medium', 'high', 'critical']
        expected_index = severity_levels.index(expected_severity) if expected_severity in severity_levels else -1
        actual_index = severity_levels.index(actual_severity) if actual_severity in severity_levels else -1
        
        if abs(expected_index - actual_index) <= 1:  # 允许相差1个级别
            print("  ✅ 严重程度评估合理")
            correct_assessments += 1
        else:
            print("  ❌ 严重程度评估不准确")
    
    accuracy = (correct_assessments / total_tests) * 100
    print(f"\n📊 严重程度评估准确率: {accuracy:.1f}% ({correct_assessments}/{total_tests})")
    
    return accuracy >= 70  # 期望准确率至少70%

def test_performance():
    print("\n=== 测试算法性能 ===")
    
    import time
    
    db_manager = DatabaseManager()
    detector = EnhancedBugDetector(db_manager)
    
    test_error = {
        'type': 'javascript',
        'message': 'Cannot read property "value" of null',
        'stack_trace': 'at HTMLInputElement.getValue (form.js:23:5)',
        'page_url': 'http://127.0.0.1:8501/form',
        'source': 'form.js'
    }
    
    # 测试单次检测性能
    start_time = time.time()
    result = detector.detect_and_classify(test_error)
    end_time = time.time()
    
    single_detection_time = (end_time - start_time) * 1000  # 转换为毫秒
    
    print(f"单次检测时间: {single_detection_time:.2f}ms")
    
    # 测试批量检测性能
    batch_size = 10
    start_time = time.time()
    
    for _ in range(batch_size):
        detector.detect_and_classify(test_error)
    
    end_time = time.time()
    batch_detection_time = ((end_time - start_time) / batch_size) * 1000
    
    print(f"批量检测平均时间: {batch_detection_time:.2f}ms")
    
    # 性能要求：单次检测应在100ms内完成
    if single_detection_time < 100:
        print("✅ 性能测试通过")
        return True
    else:
        print("❌ 性能测试失败")
        return False

def main():
    print("🔧 Bug检测算法优化测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(("分类准确性", test_enhanced_classification()))
    test_results.append(("相似度检测", test_similarity_detection()))
    test_results.append(("严重程度评估", test_severity_assessment()))
    test_results.append(("性能测试", test_performance()))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed_tests += 1
    
    success_rate = (passed_tests / total_tests) * 100
    print(f"\n总体成功率: {success_rate:.1f}% ({passed_tests}/{total_tests})")
    
    if success_rate >= 75:
        print("🎉 Bug检测算法优化成功！")
        return True
    else:
        print("⚠️ Bug检测算法需要进一步优化")
        return False

if __name__ == "__main__":
    main()
