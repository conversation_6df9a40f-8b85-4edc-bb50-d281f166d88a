import sys
sys.path.append('src')
from core.database import DatabaseManager
import sqlite3

db = DatabaseManager()
print('数据库路径:', db.db_path)

conn = sqlite3.connect(db.db_path)
cursor = conn.cursor()
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = cursor.fetchall()
print('数据库表:', tables)

if tables:
    table_name = tables[0][0]
    print(f'使用表: {table_name}')
    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
    count = cursor.fetchone()[0]
    print(f'记录数: {count}')

conn.close()
