#!/usr/bin/env python3
"""
清理测试Bug数据脚本
用途：清理系统中的测试Bug数据，确保统计数据准确性
"""

import sqlite3
import os
from datetime import datetime

def main():
    try:
        # 导入Bug检测数据库管理器
        from src.bug_detection.core.database_manager import DatabaseManager
        
        # 创建数据库管理器实例
        db_manager = DatabaseManager()
        
        print(f"📁 Bug检测数据库路径: {db_manager.db_path}")
        print(f"📁 文件是否存在: {os.path.exists(db_manager.db_path)}")
        
        if not os.path.exists(db_manager.db_path):
            print(f"❌ 数据库文件不存在: {db_manager.db_path}")
            return
            
        # 连接数据库
        conn = sqlite3.connect(db_manager.db_path)
        cursor = conn.cursor()
        
        print("\n" + "="*60)
        print("🔍 检查测试Bug数据")
        
        # 检查bug_reports表中的测试数据
        cursor.execute("""
            SELECT COUNT(*) FROM bug_reports 
            WHERE error_type IN ('test_error', 'integration_test') 
            OR error_message LIKE '%TestError%' 
            OR error_message LIKE '%BatchTest%'
            OR error_message LIKE '%测试%'
        """)
        test_bug_count = cursor.fetchone()[0]
        print(f"📊 测试Bug数据数量: {test_bug_count}")
        
        if test_bug_count > 0:
            # 显示测试数据详情
            cursor.execute("""
                SELECT id, error_type, error_message, created_at 
                FROM bug_reports 
                WHERE error_type IN ('test_error', 'integration_test') 
                OR error_message LIKE '%TestError%' 
                OR error_message LIKE '%BatchTest%'
                OR error_message LIKE '%测试%'
                ORDER BY created_at DESC
                LIMIT 10
            """)
            test_bugs = cursor.fetchall()
            
            print(f"\n📋 测试Bug数据详情（最新10条）:")
            for bug in test_bugs:
                bug_id, error_type, error_message, created_at = bug
                print(f"  - ID:{bug_id} | {error_type} | {error_message[:50]}... | {created_at}")
        
        # 检查js_errors表中的测试数据
        cursor.execute("""
            SELECT COUNT(*) FROM js_errors 
            WHERE error_message LIKE '%TestError%' 
            OR error_message LIKE '%测试%'
        """)
        test_js_count = cursor.fetchone()[0]
        print(f"\n📊 测试JavaScript错误数量: {test_js_count}")
        
        if test_js_count > 0:
            cursor.execute("""
                SELECT id, error_message, page_url, timestamp 
                FROM js_errors 
                WHERE error_message LIKE '%TestError%' 
                OR error_message LIKE '%测试%'
                ORDER BY timestamp DESC
                LIMIT 5
            """)
            test_js_errors = cursor.fetchall()
            
            print(f"\n📋 测试JavaScript错误详情:")
            for error in test_js_errors:
                error_id, error_message, page_url, timestamp = error
                print(f"  - ID:{error_id} | {error_message[:50]}... | {timestamp}")
        
        print("\n" + "="*60)
        print("🧹 开始清理测试数据")
        
        # 备份数据（可选）
        backup_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        print(f"📦 创建数据备份时间戳: {backup_time}")
        
        # 清理bug_reports表中的测试数据
        cursor.execute("""
            DELETE FROM bug_reports 
            WHERE error_type IN ('test_error', 'integration_test') 
            OR error_message LIKE '%TestError%' 
            OR error_message LIKE '%BatchTest%'
            OR error_message LIKE '%测试%'
        """)
        deleted_bugs = cursor.rowcount
        print(f"🗑️ 已删除测试Bug报告: {deleted_bugs} 条")
        
        # 清理js_errors表中的测试数据
        cursor.execute("""
            DELETE FROM js_errors 
            WHERE error_message LIKE '%TestError%' 
            OR error_message LIKE '%测试%'
        """)
        deleted_js_errors = cursor.rowcount
        print(f"🗑️ 已删除测试JavaScript错误: {deleted_js_errors} 条")
        
        # 提交更改
        conn.commit()
        print("✅ 数据清理完成，更改已提交")
        
        # 验证清理结果
        print("\n" + "="*60)
        print("🔍 验证清理结果")
        
        # 检查剩余的bug_reports数据
        cursor.execute("SELECT COUNT(*) FROM bug_reports")
        remaining_bugs = cursor.fetchone()[0]
        print(f"📊 剩余Bug报告数量: {remaining_bugs}")
        
        # 检查剩余的js_errors数据
        cursor.execute("SELECT COUNT(*) FROM js_errors")
        remaining_js_errors = cursor.fetchone()[0]
        print(f"📊 剩余JavaScript错误数量: {remaining_js_errors}")
        
        # 显示最新的真实数据
        if remaining_bugs > 0:
            cursor.execute("""
                SELECT error_type, error_message, created_at 
                FROM bug_reports 
                ORDER BY created_at DESC 
                LIMIT 3
            """)
            real_bugs = cursor.fetchall()
            print(f"\n📋 剩余真实Bug数据（最新3条）:")
            for bug in real_bugs:
                error_type, error_message, created_at = bug
                print(f"  - {error_type} | {error_message[:50]}... | {created_at}")
        
        conn.close()
        
        print(f"\n✅ 测试数据清理完成！")
        print(f"   - 删除测试Bug报告: {deleted_bugs} 条")
        print(f"   - 删除测试JS错误: {deleted_js_errors} 条")
        print(f"   - 剩余真实Bug数据: {remaining_bugs} 条")
        
    except Exception as e:
        print(f'❌ 清理测试Bug数据时出错: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
