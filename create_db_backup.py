import shutil
import os
from datetime import datetime

# 创建时间戳
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

# 备份主数据库
if os.path.exists("data/lottery_data.db"):
    backup_file = f"data/lottery_data_improvement_backup_{timestamp}.db"
    shutil.copy2("data/lottery_data.db", backup_file)
    print(f"✅ 数据库备份完成: {backup_file}")
    
    # 验证备份文件
    if os.path.exists(backup_file):
        original_size = os.path.getsize("data/lottery_data.db")
        backup_size = os.path.getsize(backup_file)
        print(f"原文件大小: {original_size} bytes")
        print(f"备份文件大小: {backup_size} bytes")
        if original_size == backup_size:
            print("✅ 备份验证成功")
        else:
            print("❌ 备份验证失败")
else:
    print("❌ 原数据库文件不存在")

print("🎉 数据库备份任务完成!")
