"""
自适应A/B测试框架
实现多臂老虎机策略、贝叶斯A/B测试算法、资源分配管理
"""

import numpy as np
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
from scipy import stats
import logging


class ExperimentStatus(Enum):
    """实验状态"""
    DRAFT = "draft"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class AllocationStrategy(Enum):
    """资源分配策略"""
    EQUAL = "equal"  # 均等分配
    THOMPSON_SAMPLING = "thompson_sampling"  # 汤普森采样
    UCB = "upper_confidence_bound"  # 置信上界
    EPSILON_GREEDY = "epsilon_greedy"  # ε-贪心
    BAYESIAN_BANDIT = "bayesian_bandit"  # 贝叶斯老虎机


@dataclass
class ExperimentArm:
    """实验分支"""
    arm_id: str
    name: str
    description: str
    configuration: Dict[str, Any]
    
    # 统计数据
    total_trials: int = 0
    successes: int = 0
    total_reward: float = 0.0
    
    # 贝叶斯参数
    alpha: float = 1.0  # 成功的先验
    beta: float = 1.0   # 失败的先验
    
    # 性能指标
    conversion_rate: float = 0.0
    confidence_interval: Tuple[float, float] = (0.0, 0.0)
    
    def update_stats(self, reward: float, success: bool = None):
        """更新统计数据"""
        self.total_trials += 1
        self.total_reward += reward
        
        if success is not None:
            if success:
                self.successes += 1
                self.alpha += 1
            else:
                self.beta += 1
        else:
            # 基于奖励判断成功
            if reward > 0.5:  # 假设阈值为0.5
                self.successes += 1
                self.alpha += 1
            else:
                self.beta += 1
        
        # 更新转化率
        if self.total_trials > 0:
            self.conversion_rate = self.successes / self.total_trials
        
        # 更新置信区间
        self._update_confidence_interval()
    
    def _update_confidence_interval(self, confidence_level: float = 0.95):
        """更新置信区间"""
        if self.total_trials == 0:
            self.confidence_interval = (0.0, 0.0)
            return
        
        # 使用贝塔分布计算置信区间
        alpha = (1 - confidence_level) / 2
        lower = stats.beta.ppf(alpha, self.alpha, self.beta)
        upper = stats.beta.ppf(1 - alpha, self.alpha, self.beta)
        
        self.confidence_interval = (lower, upper)
    
    def sample_reward(self) -> float:
        """从贝塔分布采样奖励"""
        return np.random.beta(self.alpha, self.beta)
    
    def get_ucb_score(self, total_trials: int, c: float = 1.96) -> float:
        """计算UCB分数"""
        if self.total_trials == 0:
            return float('inf')
        
        exploration_bonus = c * np.sqrt(np.log(total_trials) / self.total_trials)
        return self.conversion_rate + exploration_bonus


@dataclass
class ExperimentResult:
    """实验结果"""
    experiment_id: str
    winner_arm_id: Optional[str]
    confidence: float
    statistical_significance: bool
    p_value: float
    effect_size: float
    recommendations: List[str]
    detailed_results: Dict[str, Any]


class AdaptiveABTestingFramework:
    """自适应A/B测试框架"""
    
    def __init__(self):
        self.experiments: Dict[str, Dict[str, Any]] = {}
        self.allocation_strategies = {
            AllocationStrategy.EQUAL: self._equal_allocation,
            AllocationStrategy.THOMPSON_SAMPLING: self._thompson_sampling,
            AllocationStrategy.UCB: self._ucb_allocation,
            AllocationStrategy.EPSILON_GREEDY: self._epsilon_greedy,
            AllocationStrategy.BAYESIAN_BANDIT: self._bayesian_bandit
        }
        
        self.logger = logging.getLogger("ABTestingFramework")
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志"""
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def create_experiment(self, experiment_id: str, name: str, description: str,
                         arms: List[Dict[str, Any]], 
                         allocation_strategy: AllocationStrategy = AllocationStrategy.THOMPSON_SAMPLING,
                         target_metric: str = "accuracy",
                         min_sample_size: int = 100,
                         max_duration_days: int = 30,
                         significance_level: float = 0.05) -> bool:
        """创建A/B测试实验"""
        
        if experiment_id in self.experiments:
            self.logger.warning(f"实验 {experiment_id} 已存在")
            return False
        
        # 创建实验分支
        experiment_arms = []
        for arm_config in arms:
            arm = ExperimentArm(
                arm_id=arm_config["arm_id"],
                name=arm_config["name"],
                description=arm_config.get("description", ""),
                configuration=arm_config.get("configuration", {})
            )
            experiment_arms.append(arm)
        
        # 创建实验
        experiment = {
            "experiment_id": experiment_id,
            "name": name,
            "description": description,
            "arms": {arm.arm_id: arm for arm in experiment_arms},
            "status": ExperimentStatus.DRAFT,
            "allocation_strategy": allocation_strategy,
            "target_metric": target_metric,
            "min_sample_size": min_sample_size,
            "max_duration_days": max_duration_days,
            "significance_level": significance_level,
            "created_at": datetime.now(),
            "started_at": None,
            "ended_at": None,
            "total_trials": 0,
            "epsilon": 0.1,  # ε-贪心参数
            "ucb_c": 1.96    # UCB参数
        }
        
        self.experiments[experiment_id] = experiment
        self.logger.info(f"实验已创建: {experiment_id}")
        
        return True
    
    def start_experiment(self, experiment_id: str) -> bool:
        """启动实验"""
        if experiment_id not in self.experiments:
            self.logger.error(f"实验不存在: {experiment_id}")
            return False
        
        experiment = self.experiments[experiment_id]
        
        if experiment["status"] != ExperimentStatus.DRAFT:
            self.logger.warning(f"实验状态不允许启动: {experiment['status']}")
            return False
        
        experiment["status"] = ExperimentStatus.RUNNING
        experiment["started_at"] = datetime.now()
        
        self.logger.info(f"实验已启动: {experiment_id}")
        return True
    
    def allocate_arm(self, experiment_id: str) -> Optional[str]:
        """分配实验分支"""
        if experiment_id not in self.experiments:
            return None
        
        experiment = self.experiments[experiment_id]
        
        if experiment["status"] != ExperimentStatus.RUNNING:
            return None
        
        # 检查实验是否应该结束
        if self._should_stop_experiment(experiment):
            self._stop_experiment(experiment_id)
            return None
        
        # 使用分配策略选择分支
        allocation_func = self.allocation_strategies[experiment["allocation_strategy"]]
        selected_arm_id = allocation_func(experiment)
        
        return selected_arm_id
    
    def record_result(self, experiment_id: str, arm_id: str, 
                     metric_value: float, success: bool = None) -> bool:
        """记录实验结果"""
        if experiment_id not in self.experiments:
            return False
        
        experiment = self.experiments[experiment_id]
        
        if arm_id not in experiment["arms"]:
            return False
        
        # 更新分支统计
        arm = experiment["arms"][arm_id]
        arm.update_stats(metric_value, success)
        
        # 更新实验总试验次数
        experiment["total_trials"] += 1
        
        self.logger.debug(f"记录结果 - 实验: {experiment_id}, 分支: {arm_id}, 值: {metric_value}")
        
        return True
    
    def _equal_allocation(self, experiment: Dict[str, Any]) -> str:
        """均等分配策略"""
        arms = list(experiment["arms"].keys())
        return np.random.choice(arms)
    
    def _thompson_sampling(self, experiment: Dict[str, Any]) -> str:
        """汤普森采样策略"""
        arms = experiment["arms"]
        
        # 从每个分支的后验分布采样
        samples = {}
        for arm_id, arm in arms.items():
            samples[arm_id] = arm.sample_reward()
        
        # 选择采样值最高的分支
        best_arm = max(samples.keys(), key=lambda k: samples[k])
        return best_arm
    
    def _ucb_allocation(self, experiment: Dict[str, Any]) -> str:
        """置信上界分配策略"""
        arms = experiment["arms"]
        total_trials = experiment["total_trials"]
        c = experiment["ucb_c"]
        
        # 计算每个分支的UCB分数
        ucb_scores = {}
        for arm_id, arm in arms.items():
            ucb_scores[arm_id] = arm.get_ucb_score(total_trials, c)
        
        # 选择UCB分数最高的分支
        best_arm = max(ucb_scores.keys(), key=lambda k: ucb_scores[k])
        return best_arm
    
    def _epsilon_greedy(self, experiment: Dict[str, Any]) -> str:
        """ε-贪心策略"""
        arms = experiment["arms"]
        epsilon = experiment["epsilon"]
        
        # ε概率随机选择，1-ε概率选择最佳
        if np.random.random() < epsilon:
            return np.random.choice(list(arms.keys()))
        else:
            # 选择转化率最高的分支
            best_arm = max(arms.keys(), key=lambda k: arms[k].conversion_rate)
            return best_arm
    
    def _bayesian_bandit(self, experiment: Dict[str, Any]) -> str:
        """贝叶斯老虎机策略"""
        arms = experiment["arms"]
        
        # 计算每个分支成为最佳的概率
        probabilities = self._calculate_win_probabilities(arms)
        
        # 根据概率分配
        arm_ids = list(probabilities.keys())
        probs = list(probabilities.values())
        
        selected_arm = np.random.choice(arm_ids, p=probs)
        return selected_arm
    
    def _calculate_win_probabilities(self, arms: Dict[str, ExperimentArm], 
                                   num_samples: int = 10000) -> Dict[str, float]:
        """计算每个分支获胜的概率"""
        win_counts = {arm_id: 0 for arm_id in arms.keys()}
        
        for _ in range(num_samples):
            # 从每个分支采样
            samples = {arm_id: arm.sample_reward() for arm_id, arm in arms.items()}
            
            # 找到最佳分支
            best_arm = max(samples.keys(), key=lambda k: samples[k])
            win_counts[best_arm] += 1
        
        # 转换为概率
        probabilities = {arm_id: count / num_samples for arm_id, count in win_counts.items()}
        
        return probabilities
    
    def _should_stop_experiment(self, experiment: Dict[str, Any]) -> bool:
        """判断是否应该停止实验"""
        # 检查最小样本量
        if experiment["total_trials"] < experiment["min_sample_size"]:
            return False
        
        # 检查最大持续时间
        if experiment["started_at"]:
            duration = datetime.now() - experiment["started_at"]
            if duration.days >= experiment["max_duration_days"]:
                return True
        
        # 检查统计显著性
        result = self.analyze_experiment(experiment["experiment_id"])
        if result and result.statistical_significance:
            return True
        
        return False
    
    def _stop_experiment(self, experiment_id: str):
        """停止实验"""
        if experiment_id in self.experiments:
            experiment = self.experiments[experiment_id]
            experiment["status"] = ExperimentStatus.COMPLETED
            experiment["ended_at"] = datetime.now()
            
            self.logger.info(f"实验已停止: {experiment_id}")
    
    def analyze_experiment(self, experiment_id: str) -> Optional[ExperimentResult]:
        """分析实验结果"""
        if experiment_id not in self.experiments:
            return None
        
        experiment = self.experiments[experiment_id]
        arms = experiment["arms"]
        
        if len(arms) < 2:
            return None
        
        # 找到表现最好的分支
        best_arm_id = max(arms.keys(), key=lambda k: arms[k].conversion_rate)
        best_arm = arms[best_arm_id]
        
        # 计算统计显著性
        p_value, effect_size = self._calculate_statistical_significance(arms, best_arm_id)
        
        # 判断是否显著
        significance_level = experiment["significance_level"]
        is_significant = p_value < significance_level
        
        # 计算置信度
        confidence = 1 - p_value if is_significant else 0.5
        
        # 生成建议
        recommendations = self._generate_recommendations(experiment, best_arm_id, is_significant)
        
        # 详细结果
        detailed_results = {
            "arms_performance": {
                arm_id: {
                    "conversion_rate": arm.conversion_rate,
                    "total_trials": arm.total_trials,
                    "confidence_interval": arm.confidence_interval,
                    "relative_improvement": (arm.conversion_rate - best_arm.conversion_rate) / best_arm.conversion_rate if best_arm.conversion_rate > 0 else 0
                }
                for arm_id, arm in arms.items()
            },
            "experiment_duration": (datetime.now() - experiment["started_at"]).days if experiment["started_at"] else 0,
            "total_trials": experiment["total_trials"]
        }
        
        result = ExperimentResult(
            experiment_id=experiment_id,
            winner_arm_id=best_arm_id if is_significant else None,
            confidence=confidence,
            statistical_significance=is_significant,
            p_value=p_value,
            effect_size=effect_size,
            recommendations=recommendations,
            detailed_results=detailed_results
        )
        
        return result
    
    def _calculate_statistical_significance(self, arms: Dict[str, ExperimentArm], 
                                          best_arm_id: str) -> Tuple[float, float]:
        """计算统计显著性"""
        best_arm = arms[best_arm_id]
        
        # 与其他分支比较
        p_values = []
        effect_sizes = []
        
        for arm_id, arm in arms.items():
            if arm_id == best_arm_id:
                continue
            
            # 使用贝塔分布进行贝叶斯检验
            if arm.total_trials > 0 and best_arm.total_trials > 0:
                # 计算贝叶斯因子或使用近似方法
                p_val = self._bayesian_test(best_arm, arm)
                p_values.append(p_val)
                
                # 效应大小
                effect = abs(best_arm.conversion_rate - arm.conversion_rate)
                effect_sizes.append(effect)
        
        # 返回最小p值和最大效应大小
        min_p_value = min(p_values) if p_values else 1.0
        max_effect_size = max(effect_sizes) if effect_sizes else 0.0
        
        return min_p_value, max_effect_size
    
    def _bayesian_test(self, arm1: ExperimentArm, arm2: ExperimentArm, 
                      num_samples: int = 10000) -> float:
        """贝叶斯检验"""
        # 从两个分支的后验分布采样
        samples1 = np.random.beta(arm1.alpha, arm1.beta, num_samples)
        samples2 = np.random.beta(arm2.alpha, arm2.beta, num_samples)
        
        # 计算arm1优于arm2的概率
        prob_arm1_better = np.mean(samples1 > samples2)
        
        # 转换为p值（双尾检验）
        p_value = 2 * min(prob_arm1_better, 1 - prob_arm1_better)
        
        return p_value
    
    def _generate_recommendations(self, experiment: Dict[str, Any], 
                                best_arm_id: str, is_significant: bool) -> List[str]:
        """生成建议"""
        recommendations = []
        
        arms = experiment["arms"]
        best_arm = arms[best_arm_id]
        
        if is_significant:
            recommendations.append(f"建议采用分支 {best_arm.name}，转化率为 {best_arm.conversion_rate:.3f}")
            
            # 分析改进幅度
            other_arms = [arm for arm_id, arm in arms.items() if arm_id != best_arm_id]
            if other_arms:
                avg_other_rate = np.mean([arm.conversion_rate for arm in other_arms])
                improvement = (best_arm.conversion_rate - avg_other_rate) / avg_other_rate * 100
                recommendations.append(f"相比其他分支平均提升 {improvement:.1f}%")
        else:
            recommendations.append("实验结果不显著，建议继续收集数据或调整实验设计")
            
            # 分析可能的原因
            if experiment["total_trials"] < experiment["min_sample_size"] * 2:
                recommendations.append("样本量可能不足，建议增加样本量")
            
            # 检查分支间差异
            rates = [arm.conversion_rate for arm in arms.values()]
            if max(rates) - min(rates) < 0.01:
                recommendations.append("分支间差异较小，可能需要更大的效应量才能检测到显著性")
        
        return recommendations
    
    def get_experiment_status(self, experiment_id: str) -> Optional[Dict[str, Any]]:
        """获取实验状态"""
        if experiment_id not in self.experiments:
            return None
        
        experiment = self.experiments[experiment_id]
        
        status = {
            "experiment_id": experiment_id,
            "name": experiment["name"],
            "status": experiment["status"].value,
            "total_trials": experiment["total_trials"],
            "arms_count": len(experiment["arms"]),
            "allocation_strategy": experiment["allocation_strategy"].value,
            "created_at": experiment["created_at"].isoformat(),
            "started_at": experiment["started_at"].isoformat() if experiment["started_at"] else None,
            "ended_at": experiment["ended_at"].isoformat() if experiment["ended_at"] else None
        }
        
        # 添加分支状态
        status["arms"] = {}
        for arm_id, arm in experiment["arms"].items():
            status["arms"][arm_id] = {
                "name": arm.name,
                "total_trials": arm.total_trials,
                "conversion_rate": arm.conversion_rate,
                "confidence_interval": arm.confidence_interval
            }
        
        return status
    
    def list_experiments(self) -> List[Dict[str, Any]]:
        """列出所有实验"""
        experiments_list = []
        
        for experiment_id in self.experiments:
            status = self.get_experiment_status(experiment_id)
            if status:
                experiments_list.append(status)
        
        return experiments_list


def test_ab_testing_framework():
    """测试A/B测试框架"""
    print("🧪 测试自适应A/B测试框架...")
    
    # 创建框架实例
    ab_framework = AdaptiveABTestingFramework()
    
    # 创建实验
    arms = [
        {
            "arm_id": "control",
            "name": "控制组",
            "description": "当前配置",
            "configuration": {"learning_rate": 0.001, "batch_size": 64}
        },
        {
            "arm_id": "variant_a",
            "name": "变体A",
            "description": "更大学习率",
            "configuration": {"learning_rate": 0.005, "batch_size": 64}
        },
        {
            "arm_id": "variant_b",
            "name": "变体B", 
            "description": "更大批次",
            "configuration": {"learning_rate": 0.001, "batch_size": 128}
        }
    ]
    
    success = ab_framework.create_experiment(
        experiment_id="test_experiment_1",
        name="超参数优化实验",
        description="测试不同超参数配置的效果",
        arms=arms,
        allocation_strategy=AllocationStrategy.THOMPSON_SAMPLING
    )
    
    print(f"✅ 实验创建: {success}")
    
    # 启动实验
    ab_framework.start_experiment("test_experiment_1")
    print("🚀 实验已启动")
    
    # 模拟实验过程
    for i in range(300):
        # 分配分支
        arm_id = ab_framework.allocate_arm("test_experiment_1")
        
        if arm_id:
            # 模拟结果（不同分支有不同的成功率）
            if arm_id == "control":
                success_rate = 0.75
            elif arm_id == "variant_a":
                success_rate = 0.78  # 稍好
            else:  # variant_b
                success_rate = 0.73  # 稍差
            
            # 生成结果
            result = np.random.random() < success_rate
            metric_value = np.random.normal(success_rate, 0.1)
            
            # 记录结果
            ab_framework.record_result("test_experiment_1", arm_id, metric_value, result)
    
    # 分析结果
    analysis = ab_framework.analyze_experiment("test_experiment_1")
    
    if analysis:
        print(f"🏆 获胜分支: {analysis.winner_arm_id}")
        print(f"📊 置信度: {analysis.confidence:.3f}")
        print(f"📈 统计显著性: {analysis.statistical_significance}")
        print(f"💡 建议: {analysis.recommendations}")
    
    # 获取实验状态
    status = ab_framework.get_experiment_status("test_experiment_1")
    print(f"📋 实验状态: {status['status']}")
    print(f"🔢 总试验次数: {status['total_trials']}")
    
    print("✅ 自适应A/B测试框架测试完成！")


if __name__ == "__main__":
    test_ab_testing_framework()
