#!/usr/bin/env python3
"""
快速数据库升级脚本
"""

import sqlite3
import os
import shutil
from datetime import datetime

def upgrade_database():
    """执行数据库升级"""
    print("🚀 开始数据库升级...")
    
    # 查找数据库文件
    db_paths = ["data/bug_detection.db", "bug_detection.db", "lottery_data.db"]
    db_path = None
    
    for path in db_paths:
        if os.path.exists(path):
            db_path = path
            print(f"📁 找到数据库文件: {path}")
            break
    
    if not db_path:
        db_path = "data/bug_detection.db"
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        print(f"📁 使用默认数据库路径: {db_path}")
    
    # 备份数据库
    if os.path.exists(db_path):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"{db_path}.backup_{timestamp}"
        shutil.copy2(db_path, backup_path)
        print(f"💾 数据库已备份到: {backup_path}")
    
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 检查当前表结构
    cursor.execute("PRAGMA table_info(bug_reports)")
    columns = cursor.fetchall()
    column_names = [col[1] for col in columns]
    print(f"📋 当前列数: {len(column_names)}")
    
    # 需要添加的列
    columns_to_add = [
        ("environment", "TEXT DEFAULT 'production'"),
        ("category", "TEXT DEFAULT 'general'"),
        ("priority", "TEXT DEFAULT 'medium'"),
        ("tags", "TEXT"),
        ("source", "TEXT DEFAULT 'user'"),
        ("component_name", "TEXT"),
        ("reproduction_steps", "TEXT"),
        ("system_context", "TEXT"),
        ("user_journey", "TEXT"),
        ("screenshots", "TEXT"),
    ]
    
    added_count = 0
    
    for column_name, column_def in columns_to_add:
        if column_name not in column_names:
            try:
                sql = f"ALTER TABLE bug_reports ADD COLUMN {column_name} {column_def}"
                cursor.execute(sql)
                print(f"✅ 添加列: {column_name}")
                added_count += 1
            except Exception as e:
                print(f"❌ 添加列失败 {column_name}: {e}")
        else:
            print(f"⏭️ 列已存在: {column_name}")
    
    conn.commit()
    
    # 验证结果
    cursor.execute("PRAGMA table_info(bug_reports)")
    new_columns = cursor.fetchall()
    print(f"📊 升级后列数: {len(new_columns)}")
    print(f"📈 新增列数: {added_count}")
    
    # 测试插入
    try:
        test_data = {
            'id': 'test_upgrade_001',
            'error_type': 'test_error',
            'severity': 'low',
            'page_name': 'test_page',
            'error_message': '数据库升级测试',
            'environment': 'test',
            'category': 'database',
            'priority': 'low',
            'tags': 'upgrade,test',
            'source': 'upgrade_script'
        }
        
        cursor.execute("""
            INSERT INTO bug_reports 
            (id, error_type, severity, page_name, error_message, environment, category, priority, tags, source)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            test_data['id'], test_data['error_type'], test_data['severity'],
            test_data['page_name'], test_data['error_message'], test_data['environment'],
            test_data['category'], test_data['priority'], test_data['tags'], test_data['source']
        ))
        
        # 查询测试数据
        cursor.execute("SELECT * FROM bug_reports WHERE id = ?", (test_data['id'],))
        result = cursor.fetchone()
        
        if result:
            print("✅ 数据库操作测试成功")
            # 清理测试数据
            cursor.execute("DELETE FROM bug_reports WHERE id = ?", (test_data['id'],))
            conn.commit()
            print("🧹 测试数据已清理")
        else:
            print("❌ 数据库操作测试失败")
            
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
    
    conn.close()
    print("🎉 数据库升级完成！")
    return True

if __name__ == "__main__":
    upgrade_database()
