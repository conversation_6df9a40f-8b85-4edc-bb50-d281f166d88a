#!/usr/bin/env python3
"""
测试预测API端点
"""

import requests
import json
import time

def test_prediction_api():
    """测试预测API功能"""
    base_url = "http://127.0.0.1:8888"
    
    print("🧪 测试预测API端点...")
    
    # 1. 测试预测器信息
    try:
        print("\n1. 测试预测器信息...")
        response = requests.get(f"{base_url}/api/v1/prediction/info", timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功")
            print(f"   预测器总数: {data.get('total_predictors', 'N/A')}")
            predictors = data.get('predictors', {})
            for name, info in predictors.items():
                print(f"   - {name}: {info.get('description', 'N/A')}")
                print(f"     已训练: {info.get('is_trained', False)}")
        else:
            print(f"   ❌ 失败 - 状态码: {response.status_code}")
            print(f"   响应: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 2. 测试训练预测器
    try:
        print("\n2. 测试训练预测器...")
        response = requests.post(f"{base_url}/api/v1/prediction/train?force_retrain=false", timeout=30)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功")
            print(f"   训练成功: {data.get('success', False)}")
            print(f"   数据量: {data.get('data_count', 'N/A')}")
            print(f"   训练的预测器: {data.get('predictors_trained', 'N/A')}")
            
            results = data.get('results', {})
            for name, result in results.items():
                print(f"   - {name}: {result.get('success', False)}")
                if result.get('success'):
                    stats = result.get('statistics', {})
                    print(f"     热号: {stats.get('hot_numbers', [])}")
                    print(f"     冷号: {stats.get('cold_numbers', [])}")
        else:
            print(f"   ❌ 失败 - 状态码: {response.status_code}")
            print(f"   响应: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 3. 测试获取预测结果
    try:
        print("\n3. 测试获取预测结果...")
        response = requests.get(f"{base_url}/api/v1/prediction/predict", timeout=20)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功")
            print(f"   时间戳: {data.get('timestamp', 'N/A')}")
            
            predictions = data.get('predictions', {})
            for name, pred in predictions.items():
                print(f"   - {name}:")
                print(f"     成功: {pred.get('success', False)}")
                if pred.get('success'):
                    print(f"     预测号码: {pred.get('predictions', [])}")
                    print(f"     置信度: {pred.get('confidence', 0.0):.3f}")
                    print(f"     方法: {pred.get('method', 'N/A')}")
                    
                    analysis = pred.get('analysis', {})
                    if analysis:
                        print(f"     热号: {analysis.get('hot_numbers', [])}")
                        print(f"     冷号: {analysis.get('cold_numbers', [])}")
                        print(f"     推荐和值: {analysis.get('recommended_sums', [])}")
                else:
                    print(f"     错误: {pred.get('error', 'N/A')}")
            
            summary = data.get('summary', {})
            if summary:
                print(f"   摘要:")
                print(f"     成功预测器: {summary.get('successful_predictors', 0)}")
                print(f"     平均置信度: {summary.get('average_confidence', 0.0):.3f}")
                print(f"     总预测数: {summary.get('total_predictions', 0)}")
        else:
            print(f"   ❌ 失败 - 状态码: {response.status_code}")
            print(f"   响应: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 4. 测试预测历史
    try:
        print("\n4. 测试预测历史...")
        response = requests.get(f"{base_url}/api/v1/prediction/history?limit=5", timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功")
            print(f"   历史记录数: {len(data.get('history', []))}")
            print(f"   总记录数: {data.get('total_count', 'N/A')}")
            
            for i, record in enumerate(data.get('history', [])[:3]):
                print(f"   记录 {i+1}:")
                print(f"     时间: {record.get('timestamp', 'N/A')[:19]}")
                print(f"     预测器: {record.get('predictor', 'N/A')}")
                print(f"     预测数: {len(record.get('predictions', []))}")
                print(f"     置信度: {record.get('confidence', 0.0):.3f}")
        else:
            print(f"   ❌ 失败 - 状态码: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 5. 测试评估功能
    try:
        print("\n5. 测试评估功能...")
        response = requests.get(f"{base_url}/api/v1/prediction/evaluate", timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功")
            print(f"   评估成功: {data.get('success', False)}")
            
            evaluations = data.get('evaluations', {})
            for name, eval_result in evaluations.items():
                print(f"   - {name}: 准确率 {eval_result.get('accuracy', 0.0):.3f}")
        else:
            print(f"   ❌ 失败 - 状态码: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    print("\n📋 预测API测试完成")

if __name__ == "__main__":
    test_prediction_api()
