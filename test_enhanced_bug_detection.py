#!/usr/bin/env python3
"""
测试增强的Bug检测算法
验证新的检测、分类和严重程度评估功能
"""

import sys
import os
sys.path.insert(0, '.')

from src.bug_detection.algorithms.enhanced_detection import EnhancedBugDetector
from src.bug_detection.core.database_manager import DatabaseManager
from src.bug_detection.feedback.bug_reporter import IntelligentBugReporter

def test_enhanced_detection():
    """测试增强检测算法"""
    print("🚀 测试增强Bug检测算法")
    print("=" * 50)
    
    try:
        # 初始化组件
        db_manager = DatabaseManager()
        detector = EnhancedBugDetector(db_manager)
        bug_reporter = IntelligentBugReporter(db_manager)
        
        # 测试用例
        test_cases = [
            {
                'name': '数据库连接错误',
                'error_data': {
                    'type': 'database_error',
                    'message': 'Database connection failed: timeout exceeded',
                    'page_url': 'http://localhost:8501/dashboard',
                    'stack_trace': 'at DatabaseManager.connect()\nat UserService.getUser()',
                    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 Chrome/91.0.4472.124'
                }
            },
            {
                'name': 'JavaScript类型错误',
                'error_data': {
                    'type': 'javascript',
                    'message': 'TypeError: Cannot read property of undefined',
                    'page_url': 'http://localhost:8501/main',
                    'stack_trace': 'at handleClick(main.js:45)\nat HTMLButtonElement.onclick()',
                    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 Chrome/91.0.4472.124'
                }
            },
            {
                'name': 'API超时错误',
                'error_data': {
                    'type': 'api_error',
                    'message': 'Request timeout: API call exceeded 30 seconds',
                    'page_url': 'http://production.example.com/api/data',
                    'stack_trace': 'at fetch()\nat ApiService.getData()',
                    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 Chrome/91.0.4472.124'
                }
            },
            {
                'name': '安全认证错误',
                'error_data': {
                    'type': 'security_error',
                    'message': 'Authentication failed: invalid token',
                    'page_url': 'http://localhost:8501/login',
                    'stack_trace': 'at AuthService.validateToken()\nat LoginController.authenticate()',
                    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 Chrome/91.0.4472.124'
                }
            },
            {
                'name': '内存泄漏警告',
                'error_data': {
                    'type': 'performance_warning',
                    'message': 'Memory usage warning: heap size exceeded 1GB',
                    'page_url': 'http://localhost:8501/analysis',
                    'stack_trace': 'at MemoryMonitor.checkUsage()\nat PerformanceTracker.monitor()',
                    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 Chrome/91.0.4472.124'
                }
            }
        ]
        
        print("📊 测试结果:")
        print("-" * 50)
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. {test_case['name']}")
            print("-" * 30)
            
            # 使用增强检测算法
            result = detector.detect_and_classify(test_case['error_data'])
            
            print(f"   严重程度: {result['severity']}")
            print(f"   分类: {result['category']}")
            print(f"   优先级: {result['priority']}")
            print(f"   环境: {result['environment']}")
            print(f"   置信度: {result['confidence_score']:.2f}")
            
            # 显示影响分析
            impact = result['impact_analysis']
            print(f"   用户影响: {impact['user_impact']}")
            print(f"   系统影响: {impact['system_impact']}")
            print(f"   业务影响: {impact['business_impact']}")
            
            # 显示修复建议（前3个）
            suggestions = result['fix_suggestions'][:3]
            print(f"   修复建议: {', '.join(suggestions)}")
            
            # 显示标签（前5个）
            tags = result['tags'][:5]
            print(f"   标签: {', '.join(tags)}")
            
            # 测试智能Bug报告生成
            enhanced_report = bug_reporter.generate_enhanced_report(test_case['error_data'])
            print(f"   报告ID: {enhanced_report.get('id', 'N/A')}")
            
        print("\n" + "=" * 50)
        print("✅ 增强Bug检测算法测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_similarity_detection():
    """测试相似性检测"""
    print("\n🔍 测试相似性检测")
    print("=" * 50)
    
    try:
        db_manager = DatabaseManager()
        detector = EnhancedBugDetector(db_manager)
        
        # 创建一些测试Bug记录
        test_bugs = [
            {
                'type': 'javascript',
                'message': 'TypeError: Cannot read property of null',
                'page_url': 'http://localhost:8501/main'
            },
            {
                'type': 'javascript', 
                'message': 'TypeError: Cannot read property of undefined',
                'page_url': 'http://localhost:8501/main'
            },
            {
                'type': 'database_error',
                'message': 'Connection timeout exceeded',
                'page_url': 'http://localhost:8501/dashboard'
            }
        ]
        
        # 保存测试Bug到数据库
        for bug_data in test_bugs:
            db_manager.save_bug_report(bug_data)
        
        # 测试相似性检测
        new_error = {
            'type': 'javascript',
            'message': 'TypeError: Cannot read property of null object',
            'page_url': 'http://localhost:8501/main'
        }
        
        result = detector.detect_and_classify(new_error)
        similar_errors = result['similar_errors']
        
        print(f"📊 找到 {len(similar_errors)} 个相似错误:")
        for error in similar_errors:
            print(f"   - Bug ID: {error['bug_id'][:8]}...")
            print(f"     相似度: {error['similarity_score']:.2%}")
            print(f"     状态: {error['status']}")
        
        print("✅ 相似性检测测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 相似性检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance():
    """测试性能"""
    print("\n⚡ 测试检测性能")
    print("=" * 50)
    
    try:
        import time
        
        db_manager = DatabaseManager()
        detector = EnhancedBugDetector(db_manager)
        
        # 测试错误数据
        error_data = {
            'type': 'javascript',
            'message': 'TypeError: Cannot read property of undefined',
            'page_url': 'http://localhost:8501/main',
            'stack_trace': 'at handleClick(main.js:45)\nat HTMLButtonElement.onclick()',
            'user_agent': 'Mozilla/5.0 Chrome/91.0.4472.124'
        }
        
        # 性能测试
        iterations = 100
        start_time = time.time()
        
        for _ in range(iterations):
            detector.detect_and_classify(error_data)
        
        end_time = time.time()
        total_time = end_time - start_time
        avg_time = total_time / iterations
        
        print(f"📊 性能测试结果:")
        print(f"   总测试次数: {iterations}")
        print(f"   总耗时: {total_time:.3f}秒")
        print(f"   平均耗时: {avg_time:.3f}秒/次")
        print(f"   处理速度: {1/avg_time:.1f}次/秒")
        
        # 性能评估
        if avg_time < 0.1:
            print("✅ 性能优秀 (<0.1秒)")
        elif avg_time < 0.5:
            print("✅ 性能良好 (<0.5秒)")
        elif avg_time < 1.0:
            print("⚠️ 性能一般 (<1.0秒)")
        else:
            print("❌ 性能需要优化 (>1.0秒)")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 增强Bug检测算法测试套件")
    print("=" * 60)
    
    results = []
    
    # 运行测试
    results.append(("增强检测算法", test_enhanced_detection()))
    results.append(("相似性检测", test_similarity_detection()))
    results.append(("性能测试", test_performance()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📋 测试结果汇总:")
    print("-" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！增强Bug检测算法工作正常。")
    else:
        print("⚠️ 部分测试失败，需要检查和修复。")

if __name__ == "__main__":
    main()
