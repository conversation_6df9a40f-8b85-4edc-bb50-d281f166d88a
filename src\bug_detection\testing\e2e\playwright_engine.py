"""
Playwright E2E测试引擎
创建日期: 2025年7月24日
用途: 实现自动化测试17个功能页面的框架
"""

import asyncio
import json
import logging
import os
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

try:
    from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON>erContext, Page, Playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    print("⚠️  Playwright未安装，E2E测试功能将受限")

logger = logging.getLogger(__name__)

class PlaywrightEngine:
    """Playwright E2E测试引擎"""
    
    def __init__(self, 
                 base_url: str = "http://127.0.0.1:8501",
                 headless: bool = True,
                 browser_type: str = "chromium",
                 timeout: int = 30000):
        """
        初始化Playwright测试引擎
        
        Args:
            base_url: 基础URL
            headless: 是否无头模式
            browser_type: 浏览器类型 (chromium, firefox, webkit)
            timeout: 默认超时时间(毫秒)
        """
        self.base_url = base_url
        self.headless = headless
        self.browser_type = browser_type
        self.timeout = timeout
        
        # 测试状态
        self.playwright: Optional[Playwright] = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        
        # 测试结果
        self.test_results: List[Dict[str, Any]] = []
        self.screenshots_dir = Path("test_screenshots")
        self.screenshots_dir.mkdir(exist_ok=True)
        
        # 17个功能页面配置
        self.pages_config = self._get_pages_config()
    
    def _get_pages_config(self) -> Dict[str, Dict[str, Any]]:
        """获取17个功能页面的配置"""
        return {
            # 数据分析类 (5个)
            "data_overview": {
                "name": "数据概览",
                "path": "/?page=data_overview",
                "key_elements": ["数据概览", "总期数", "最新期号"],
                "actions": ["查看统计信息", "检查数据完整性"]
            },
            "frequency_analysis": {
                "name": "频率分析",
                "path": "/?page=frequency_analysis", 
                "key_elements": ["频率分析", "号码频率", "统计图表"],
                "actions": ["查看频率统计", "分析号码分布"]
            },
            "sum_distribution": {
                "name": "和值分布",
                "path": "/?page=sum_distribution",
                "key_elements": ["和值分布", "分布图", "统计数据"],
                "actions": ["查看和值分布", "分析趋势"]
            },
            "sales_analysis": {
                "name": "销售分析", 
                "path": "/?page=sales_analysis",
                "key_elements": ["销售分析", "销售额", "趋势图"],
                "actions": ["查看销售数据", "分析销售趋势"]
            },
            "data_query": {
                "name": "数据查询",
                "path": "/?page=data_query",
                "key_elements": ["数据查询", "查询条件", "结果展示"],
                "actions": ["设置查询条件", "执行查询", "查看结果"]
            },
            
            # 预测工具类 (4个)
            "prediction_analysis": {
                "name": "预测分析",
                "path": "/?page=prediction_analysis",
                "key_elements": ["预测分析", "预测结果", "置信度"],
                "actions": ["执行预测", "查看结果", "分析置信度"]
            },
            "intelligent_fusion": {
                "name": "智能融合",
                "path": "/?page=intelligent_fusion",
                "key_elements": ["智能融合", "融合预测", "模型权重"],
                "actions": ["配置模型", "执行融合预测", "查看结果"]
            },
            "trend_analysis": {
                "name": "趋势分析",
                "path": "/?page=trend_analysis", 
                "key_elements": ["趋势分析", "趋势图", "预测趋势"],
                "actions": ["分析趋势", "查看预测", "导出结果"]
            },
            "model_library": {
                "name": "模型库",
                "path": "/?page=model_library",
                "key_elements": ["模型库", "模型列表", "性能指标"],
                "actions": ["浏览模型", "查看性能", "选择模型"]
            },
            
            # 系统管理类 (4个)
            "data_update": {
                "name": "数据更新",
                "path": "/?page=data_update",
                "key_elements": ["数据更新", "更新状态", "最后更新时间"],
                "actions": ["检查更新状态", "手动更新", "查看日志"]
            },
            "real_time_monitoring": {
                "name": "实时监控",
                "path": "/?page=real_time_monitoring",
                "key_elements": ["实时监控", "系统状态", "性能指标"],
                "actions": ["查看系统状态", "监控性能", "检查健康度"]
            },
            "system_settings": {
                "name": "系统设置",
                "path": "/?page=system_settings",
                "key_elements": ["系统设置", "配置选项", "保存设置"],
                "actions": ["修改设置", "保存配置", "重置设置"]
            },
            "log_viewer": {
                "name": "日志查看",
                "path": "/?page=log_viewer",
                "key_elements": ["日志查看", "日志列表", "过滤条件"],
                "actions": ["查看日志", "过滤日志", "导出日志"]
            },
            
            # 高级功能类 (4个)
            "optimization_suggestions": {
                "name": "优化建议",
                "path": "/?page=optimization_suggestions",
                "key_elements": ["优化建议", "建议列表", "实施状态"],
                "actions": ["查看建议", "实施优化", "跟踪效果"]
            },
            "parameter_backtest": {
                "name": "参数回测",
                "path": "/?page=parameter_backtest",
                "key_elements": ["参数回测", "回测结果", "性能评估"],
                "actions": ["设置参数", "执行回测", "分析结果"]
            },
            "performance_analysis": {
                "name": "性能分析",
                "path": "/?page=performance_analysis",
                "key_elements": ["性能分析", "性能图表", "指标统计"],
                "actions": ["查看性能", "分析指标", "生成报告"]
            },
            "custom_model": {
                "name": "自定义模型",
                "path": "/?page=custom_model",
                "key_elements": ["自定义模型", "模型配置", "训练状态"],
                "actions": ["创建模型", "配置参数", "训练模型"]
            }
        }
    
    async def initialize(self) -> bool:
        """初始化Playwright"""
        if not PLAYWRIGHT_AVAILABLE:
            logger.error("Playwright not available")
            return False
        
        try:
            self.playwright = await async_playwright().start()
            
            # 启动浏览器
            if self.browser_type == "chromium":
                self.browser = await self.playwright.chromium.launch(headless=self.headless)
            elif self.browser_type == "firefox":
                self.browser = await self.playwright.firefox.launch(headless=self.headless)
            elif self.browser_type == "webkit":
                self.browser = await self.playwright.webkit.launch(headless=self.headless)
            else:
                raise ValueError(f"Unsupported browser type: {self.browser_type}")
            
            # 创建上下文
            self.context = await self.browser.new_context(
                viewport={"width": 1280, "height": 720},
                ignore_https_errors=True
            )
            
            # 创建页面
            self.page = await self.context.new_page()
            self.page.set_default_timeout(self.timeout)
            
            logger.info(f"Playwright initialized with {self.browser_type}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Playwright: {e}")
            return False
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
                
            logger.info("Playwright cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    async def take_screenshot(self, name: str, full_page: bool = True) -> str:
        """截图"""
        if not self.page:
            return ""
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{name}_{timestamp}.png"
            filepath = self.screenshots_dir / filename
            
            await self.page.screenshot(path=str(filepath), full_page=full_page)
            logger.info(f"Screenshot saved: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"Failed to take screenshot: {e}")
            return ""
    
    async def wait_for_page_load(self, timeout: int = None) -> bool:
        """等待页面加载完成"""
        if not self.page:
            return False
        
        try:
            # 等待网络空闲
            await self.page.wait_for_load_state("networkidle", timeout=timeout or self.timeout)
            
            # 等待Streamlit特定元素
            await self.page.wait_for_selector("[data-testid='stApp']", timeout=5000)
            
            return True
            
        except Exception as e:
            logger.warning(f"Page load wait failed: {e}")
            return False
    
    async def test_page(self, page_key: str) -> Dict[str, Any]:
        """测试单个页面"""
        if not self.page or page_key not in self.pages_config:
            return {"success": False, "error": "Invalid page or not initialized"}
        
        page_config = self.pages_config[page_key]
        test_result = {
            "page_key": page_key,
            "page_name": page_config["name"],
            "start_time": datetime.now().isoformat(),
            "success": False,
            "errors": [],
            "warnings": [],
            "screenshots": [],
            "performance": {},
            "elements_found": [],
            "actions_completed": []
        }
        
        try:
            # 1. 导航到页面
            url = f"{self.base_url}{page_config['path']}"
            logger.info(f"Testing page: {page_config['name']} ({url})")
            
            start_nav = time.time()
            await self.page.goto(url)
            await self.wait_for_page_load()
            nav_time = time.time() - start_nav
            
            test_result["performance"]["navigation_time"] = nav_time
            
            # 2. 截图
            screenshot_path = await self.take_screenshot(f"{page_key}_initial")
            if screenshot_path:
                test_result["screenshots"].append(screenshot_path)
            
            # 3. 检查关键元素
            for element_text in page_config["key_elements"]:
                try:
                    # 尝试多种选择器策略
                    selectors = [
                        f"text={element_text}",
                        f"[aria-label*='{element_text}']",
                        f"[title*='{element_text}']",
                        f":has-text('{element_text}')"
                    ]
                    
                    element_found = False
                    for selector in selectors:
                        try:
                            await self.page.wait_for_selector(selector, timeout=3000)
                            test_result["elements_found"].append(element_text)
                            element_found = True
                            break
                        except:
                            continue
                    
                    if not element_found:
                        test_result["warnings"].append(f"Element not found: {element_text}")
                        
                except Exception as e:
                    test_result["warnings"].append(f"Error checking element '{element_text}': {str(e)}")
            
            # 4. 执行页面特定操作
            for action in page_config["actions"]:
                try:
                    await self._execute_page_action(page_key, action)
                    test_result["actions_completed"].append(action)
                except Exception as e:
                    test_result["warnings"].append(f"Action failed '{action}': {str(e)}")
            
            # 5. 检查JavaScript错误
            js_errors = await self._check_js_errors()
            if js_errors:
                test_result["errors"].extend(js_errors)
            
            # 6. 性能检查
            await self._check_performance(test_result)
            
            # 7. 最终截图
            final_screenshot = await self.take_screenshot(f"{page_key}_final")
            if final_screenshot:
                test_result["screenshots"].append(final_screenshot)
            
            # 判断测试是否成功
            test_result["success"] = len(test_result["errors"]) == 0
            
        except Exception as e:
            test_result["errors"].append(f"Test execution failed: {str(e)}")
            test_result["success"] = False
            
        finally:
            test_result["end_time"] = datetime.now().isoformat()
            test_result["duration"] = (
                datetime.fromisoformat(test_result["end_time"]) - 
                datetime.fromisoformat(test_result["start_time"])
            ).total_seconds()
        
        return test_result
    
    async def _execute_page_action(self, page_key: str, action: str):
        """执行页面特定操作"""
        if not self.page:
            return
        
        # 根据页面和操作类型执行相应的操作
        if "查看" in action or "检查" in action:
            # 简单的查看操作，等待一下即可
            await asyncio.sleep(1)
            
        elif "点击" in action or "执行" in action:
            # 查找可点击的按钮
            buttons = await self.page.query_selector_all("button, [role='button']")
            if buttons:
                await buttons[0].click()
                await asyncio.sleep(2)
                
        elif "输入" in action or "设置" in action:
            # 查找输入框
            inputs = await self.page.query_selector_all("input, textarea, [contenteditable]")
            if inputs:
                await inputs[0].fill("test")
                await asyncio.sleep(1)
    
    async def _check_js_errors(self) -> List[str]:
        """检查JavaScript错误"""
        if not self.page:
            return []
        
        try:
            # 执行JavaScript来获取错误
            js_errors = await self.page.evaluate("""
                () => {
                    if (window.bugDetector && window.bugDetector.errors) {
                        return window.bugDetector.errors.map(error => error.message);
                    }
                    return [];
                }
            """)
            
            return js_errors or []
            
        except Exception as e:
            logger.warning(f"Failed to check JS errors: {e}")
            return []
    
    async def _check_performance(self, test_result: Dict[str, Any]):
        """检查页面性能"""
        if not self.page:
            return
        
        try:
            # 获取性能指标
            performance = await self.page.evaluate("""
                () => {
                    const perf = performance.getEntriesByType('navigation')[0];
                    return {
                        loadTime: perf.loadEventEnd - perf.loadEventStart,
                        domContentLoaded: perf.domContentLoadedEventEnd - perf.domContentLoadedEventStart,
                        firstPaint: performance.getEntriesByType('paint').find(p => p.name === 'first-paint')?.startTime || 0,
                        firstContentfulPaint: performance.getEntriesByType('paint').find(p => p.name === 'first-contentful-paint')?.startTime || 0
                    };
                }
            """)
            
            test_result["performance"].update(performance)
            
            # 性能警告
            if performance.get("loadTime", 0) > 3000:
                test_result["warnings"].append("Page load time > 3s")
                
        except Exception as e:
            logger.warning(f"Failed to check performance: {e}")
    
    async def test_all_pages(self) -> Dict[str, Any]:
        """测试所有17个页面"""
        if not await self.initialize():
            return {"success": False, "error": "Failed to initialize"}
        
        overall_result = {
            "start_time": datetime.now().isoformat(),
            "total_pages": len(self.pages_config),
            "pages_tested": 0,
            "pages_passed": 0,
            "pages_failed": 0,
            "page_results": {},
            "summary": {}
        }
        
        try:
            for page_key in self.pages_config.keys():
                logger.info(f"Testing page {overall_result['pages_tested'] + 1}/{overall_result['total_pages']}: {page_key}")
                
                page_result = await self.test_page(page_key)
                overall_result["page_results"][page_key] = page_result
                overall_result["pages_tested"] += 1
                
                if page_result["success"]:
                    overall_result["pages_passed"] += 1
                else:
                    overall_result["pages_failed"] += 1
                
                # 短暂休息避免过快请求
                await asyncio.sleep(1)
            
            # 生成摘要
            overall_result["summary"] = {
                "success_rate": overall_result["pages_passed"] / overall_result["total_pages"],
                "total_errors": sum(len(result.get("errors", [])) for result in overall_result["page_results"].values()),
                "total_warnings": sum(len(result.get("warnings", [])) for result in overall_result["page_results"].values()),
                "avg_load_time": sum(result.get("performance", {}).get("navigation_time", 0) for result in overall_result["page_results"].values()) / overall_result["total_pages"]
            }
            
        except Exception as e:
            overall_result["error"] = str(e)
            logger.error(f"Test execution failed: {e}")
            
        finally:
            overall_result["end_time"] = datetime.now().isoformat()
            await self.cleanup()
        
        return overall_result
    
    def save_test_results(self, results: Dict[str, Any], filename: str = None):
        """保存测试结果"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"e2e_test_results_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Test results saved to: {filename}")
            
        except Exception as e:
            logger.error(f"Failed to save test results: {e}")

# 便捷函数
async def run_e2e_tests(base_url: str = "http://127.0.0.1:8501", 
                       headless: bool = True,
                       save_results: bool = True) -> Dict[str, Any]:
    """运行E2E测试的便捷函数"""
    engine = PlaywrightEngine(base_url=base_url, headless=headless)
    results = await engine.test_all_pages()
    
    if save_results:
        engine.save_test_results(results)
    
    return results

def create_test_report(results: Dict[str, Any]) -> str:
    """创建测试报告"""
    if not results:
        return "No test results available"
    
    report = []
    report.append("# E2E测试报告")
    report.append(f"测试时间: {results.get('start_time', 'Unknown')}")
    report.append(f"总页面数: {results.get('total_pages', 0)}")
    report.append(f"测试通过: {results.get('pages_passed', 0)}")
    report.append(f"测试失败: {results.get('pages_failed', 0)}")
    
    summary = results.get('summary', {})
    if summary:
        report.append(f"成功率: {summary.get('success_rate', 0):.1%}")
        report.append(f"平均加载时间: {summary.get('avg_load_time', 0):.2f}s")
        report.append(f"总错误数: {summary.get('total_errors', 0)}")
        report.append(f"总警告数: {summary.get('total_warnings', 0)}")
    
    report.append("\n## 页面测试详情")
    
    for page_key, page_result in results.get('page_results', {}).items():
        status = "✅ 通过" if page_result.get('success') else "❌ 失败"
        report.append(f"- {page_result.get('page_name', page_key)}: {status}")
        
        if page_result.get('errors'):
            report.append(f"  错误: {', '.join(page_result['errors'])}")
        
        if page_result.get('warnings'):
            report.append(f"  警告: {', '.join(page_result['warnings'][:3])}...")  # 只显示前3个警告
    
    return "\n".join(report)
