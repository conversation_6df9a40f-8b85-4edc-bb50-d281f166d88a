#!/usr/bin/env python3
"""
快速API测试
"""

import requests
import json
import time

def test_api():
    base_url = "http://localhost:8000"
    
    print("测试FastAPI接口...")
    
    # 1. 健康检查
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"健康检查: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  数据库记录: {data['database_records']}")
            print(f"  日期范围: {data['date_range']}")
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False
    
    # 2. 基础统计
    try:
        start_time = time.time()
        response = requests.get(f"{base_url}/api/v1/stats/basic", timeout=10)
        response_time = time.time() - start_time
        
        print(f"基础统计: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  总记录数: {data['total_records']}")
            print(f"  响应时间: {response_time*1000:.2f}ms")
            print(f"  查询时间: {data['query_time_ms']}ms")
    except Exception as e:
        print(f"基础统计失败: {e}")
    
    # 3. 频率分析
    try:
        response = requests.get(f"{base_url}/api/v1/analysis/frequency", timeout=10)
        print(f"频率分析: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  查询时间: {data['query_time_ms']}ms")
    except Exception as e:
        print(f"频率分析失败: {e}")
    
    # 4. 数据查询
    try:
        response = requests.get(f"{base_url}/api/v1/data/query?min_sum=13&max_sum=14&limit=5", timeout=10)
        print(f"数据查询: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  返回记录: {data['total_count']}")
            print(f"  查询时间: {data['query_time_ms']}ms")
    except Exception as e:
        print(f"数据查询失败: {e}")
    
    print("API测试完成!")
    return True

if __name__ == "__main__":
    test_api()
