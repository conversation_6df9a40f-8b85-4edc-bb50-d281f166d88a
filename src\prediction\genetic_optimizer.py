#!/usr/bin/env python3
"""
遗传算法参数优化器
使用遗传算法优化神经网络参数，多目标优化准确率和多样性
"""

import random
import numpy as np
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime
import json
import os


class GeneticOptimizer:
    """遗传算法优化器"""
    
    def __init__(self, population_size: int = 100, mutation_rate: float = 0.05, 
                 crossover_type: str = "uniform", cache_dir: str = "data/cache"):
        """
        初始化遗传算法优化器
        
        Args:
            population_size: 种群大小
            mutation_rate: 变异率
            crossover_type: 交叉类型
            cache_dir: 缓存目录
        """
        self.population_size = population_size
        self.mutation_rate = mutation_rate
        self.crossover_type = crossover_type
        self.cache_dir = cache_dir
        
        # 确保缓存目录存在
        os.makedirs(cache_dir, exist_ok=True)
        
        # 参数范围定义
        self.param_ranges = {
            'temperature': (0.5, 2.0),           # 温度参数范围
            'alpha': (1.0, 3.0),                 # 锐化系数范围
            'lambda_stability': (0.01, 0.3),     # 稳定性权重范围
            'diversity_threshold': (0.5, 0.9),   # 多样性阈值范围
            'confidence_threshold': (0.1, 0.8),  # 置信度阈值范围
            'window_size': (20, 100),            # 窗口大小范围
            'exploration_rate': (0.1, 0.5)       # 探索率范围
        }
        
        # 种群和历史记录
        self.population = []
        self.fitness_history = []
        self.best_individual = None
        self.generation = 0
    
    def fitness_function(self, nn_params: Dict[str, float]) -> float:
        """
        适应度函数
        
        Args:
            nn_params: 神经网络参数
            
        Returns:
            适应度分数
        """
        try:
            # 模拟构建神经网络并测试
            accuracy = self._simulate_accuracy(nn_params)
            diversity = self._simulate_diversity(nn_params)
            
            # 多目标优化：70%准确率 + 30%多样性
            fitness = 0.7 * accuracy + 0.3 * diversity
            
            return fitness
            
        except Exception as e:
            print(f"适应度计算失败: {e}")
            return 0.0
    
    def _simulate_accuracy(self, params: Dict[str, float]) -> float:
        """
        模拟准确率计算
        
        Args:
            params: 参数字典
            
        Returns:
            模拟准确率
        """
        # 基于参数计算模拟准确率
        temperature = params.get('temperature', 1.0)
        alpha = params.get('alpha', 2.0)
        window_size = params.get('window_size', 50)
        
        # 简化的准确率模型
        base_accuracy = 0.6
        
        # 温度参数影响
        temp_factor = 1.0 - abs(temperature - 1.0) * 0.2
        
        # 锐化系数影响
        alpha_factor = min(alpha / 2.0, 1.0)
        
        # 窗口大小影响
        window_factor = min(window_size / 100.0, 1.0)
        
        # 添加随机噪声
        noise = random.uniform(-0.1, 0.1)
        
        accuracy = base_accuracy * temp_factor * alpha_factor * window_factor + noise
        
        return max(0.0, min(1.0, accuracy))
    
    def _simulate_diversity(self, params: Dict[str, float]) -> float:
        """
        模拟多样性计算
        
        Args:
            params: 参数字典
            
        Returns:
            模拟多样性
        """
        # 基于参数计算模拟多样性
        temperature = params.get('temperature', 1.0)
        diversity_threshold = params.get('diversity_threshold', 0.7)
        exploration_rate = params.get('exploration_rate', 0.2)
        
        # 简化的多样性模型
        base_diversity = 0.5
        
        # 温度参数对多样性的正向影响
        temp_factor = min(temperature / 2.0, 1.0)
        
        # 多样性阈值影响
        threshold_factor = diversity_threshold
        
        # 探索率影响
        exploration_factor = exploration_rate * 2.0
        
        # 添加随机噪声
        noise = random.uniform(-0.1, 0.1)
        
        diversity = base_diversity * temp_factor * threshold_factor * exploration_factor + noise
        
        return max(0.0, min(1.0, diversity))
    
    def create_individual(self) -> Dict[str, float]:
        """
        创建个体
        
        Returns:
            个体参数字典
        """
        individual = {}
        
        for param_name, (min_val, max_val) in self.param_ranges.items():
            if param_name == 'window_size':
                # 整数参数
                individual[param_name] = float(random.randint(int(min_val), int(max_val)))
            else:
                # 浮点数参数
                individual[param_name] = random.uniform(min_val, max_val)
        
        return individual
    
    def initialize_population(self) -> None:
        """初始化种群"""
        self.population = []
        
        for _ in range(self.population_size):
            individual = self.create_individual()
            fitness = self.fitness_function(individual)
            
            self.population.append({
                'params': individual,
                'fitness': fitness
            })
        
        # 按适应度排序
        self.population.sort(key=lambda x: x['fitness'], reverse=True)
        self.best_individual = self.population[0]
    
    def selection(self, tournament_size: int = 3) -> Dict[str, Any]:
        """
        锦标赛选择
        
        Args:
            tournament_size: 锦标赛大小
            
        Returns:
            选中的个体
        """
        tournament = random.sample(self.population, tournament_size)
        return max(tournament, key=lambda x: x['fitness'])
    
    def crossover(self, parent1: Dict[str, Any], parent2: Dict[str, Any]) -> Tuple[Dict[str, float], Dict[str, float]]:
        """
        交叉操作
        
        Args:
            parent1: 父代1
            parent2: 父代2
            
        Returns:
            两个子代
        """
        params1 = parent1['params']
        params2 = parent2['params']
        
        child1 = {}
        child2 = {}
        
        if self.crossover_type == "uniform":
            # 均匀交叉
            for param_name in params1.keys():
                if random.random() < 0.5:
                    child1[param_name] = params1[param_name]
                    child2[param_name] = params2[param_name]
                else:
                    child1[param_name] = params2[param_name]
                    child2[param_name] = params1[param_name]
        else:
            # 单点交叉
            param_names = list(params1.keys())
            crossover_point = random.randint(1, len(param_names) - 1)
            
            for i, param_name in enumerate(param_names):
                if i < crossover_point:
                    child1[param_name] = params1[param_name]
                    child2[param_name] = params2[param_name]
                else:
                    child1[param_name] = params2[param_name]
                    child2[param_name] = params1[param_name]
        
        return child1, child2
    
    def mutation(self, individual: Dict[str, float]) -> Dict[str, float]:
        """
        变异操作
        
        Args:
            individual: 个体参数
            
        Returns:
            变异后的个体
        """
        mutated = individual.copy()
        
        for param_name, value in mutated.items():
            if random.random() < self.mutation_rate:
                min_val, max_val = self.param_ranges[param_name]
                
                # 高斯变异
                sigma = (max_val - min_val) * 0.1  # 变异强度
                new_value = value + random.gauss(0, sigma)
                
                # 确保在范围内
                new_value = max(min_val, min(max_val, new_value))
                
                # 整数参数处理
                if param_name == 'window_size':
                    new_value = float(int(new_value))
                
                mutated[param_name] = new_value
        
        return mutated
    
    def evolve_generation(self) -> None:
        """进化一代"""
        new_population = []
        
        # 保留最优个体（精英策略）
        elite_count = max(1, self.population_size // 10)
        new_population.extend(self.population[:elite_count])
        
        # 生成新个体
        while len(new_population) < self.population_size:
            # 选择父代
            parent1 = self.selection()
            parent2 = self.selection()
            
            # 交叉
            child1_params, child2_params = self.crossover(parent1, parent2)
            
            # 变异
            child1_params = self.mutation(child1_params)
            child2_params = self.mutation(child2_params)
            
            # 计算适应度
            child1_fitness = self.fitness_function(child1_params)
            child2_fitness = self.fitness_function(child2_params)
            
            # 添加到新种群
            if len(new_population) < self.population_size:
                new_population.append({
                    'params': child1_params,
                    'fitness': child1_fitness
                })
            
            if len(new_population) < self.population_size:
                new_population.append({
                    'params': child2_params,
                    'fitness': child2_fitness
                })
        
        # 更新种群
        self.population = new_population
        self.population.sort(key=lambda x: x['fitness'], reverse=True)
        
        # 更新最优个体
        if self.population[0]['fitness'] > self.best_individual['fitness']:
            self.best_individual = self.population[0]
        
        # 记录历史
        generation_stats = {
            'generation': self.generation,
            'best_fitness': self.population[0]['fitness'],
            'average_fitness': sum(ind['fitness'] for ind in self.population) / len(self.population),
            'worst_fitness': self.population[-1]['fitness']
        }
        
        self.fitness_history.append(generation_stats)
        self.generation += 1
    
    def optimize(self, generations: int = 50) -> Dict[str, Any]:
        """
        执行优化
        
        Args:
            generations: 进化代数
            
        Returns:
            优化结果
        """
        print(f"开始遗传算法优化，种群大小: {self.population_size}, 进化代数: {generations}")
        
        # 初始化种群
        self.initialize_population()
        
        print(f"初始最优适应度: {self.best_individual['fitness']:.4f}")
        
        # 进化
        for gen in range(generations):
            self.evolve_generation()
            
            if gen % 10 == 0:
                current_best = self.population[0]['fitness']
                avg_fitness = sum(ind['fitness'] for ind in self.population) / len(self.population)
                print(f"第{gen}代: 最优={current_best:.4f}, 平均={avg_fitness:.4f}")
        
        # 保存结果
        result = {
            'best_params': self.best_individual['params'],
            'best_fitness': self.best_individual['fitness'],
            'generations': generations,
            'final_population_size': len(self.population),
            'fitness_history': self.fitness_history,
            'optimization_time': datetime.now().isoformat()
        }
        
        self._save_optimization_result(result)
        
        print(f"优化完成！最优适应度: {self.best_individual['fitness']:.4f}")
        print(f"最优参数: {self.best_individual['params']}")
        
        return result
    
    def _save_optimization_result(self, result: Dict[str, Any]) -> None:
        """保存优化结果"""
        try:
            result_file = os.path.join(self.cache_dir, f"genetic_optimization_{int(datetime.now().timestamp())}.json")
            
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            print(f"优化结果已保存到: {result_file}")
            
        except Exception as e:
            print(f"保存优化结果失败: {e}")


def main():
    """主函数"""
    print("🧬 遗传算法参数优化器")
    print("=" * 50)
    
    # 创建优化器
    optimizer = GeneticOptimizer(
        population_size=50,  # 减小种群以加快测试
        mutation_rate=0.05,
        crossover_type="uniform"
    )
    
    # 执行优化
    result = optimizer.optimize(generations=20)  # 减少代数以加快测试
    
    print("\n🎯 优化结果:")
    print(f"最优适应度: {result['best_fitness']:.4f}")
    print(f"最优参数:")
    for param, value in result['best_params'].items():
        print(f"  {param}: {value:.4f}")


if __name__ == "__main__":
    main()
