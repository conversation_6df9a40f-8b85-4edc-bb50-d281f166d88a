# 福彩3D预测系统深度交互版 - 主应用Dockerfile
FROM python:3.11.9-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app/src
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    wget \
    git \
    libpq-dev \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY requirements.txt .
COPY requirements-prod.txt .

# 安装Python依赖
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir -r requirements-prod.txt

# 复制应用代码
COPY src/ ./src/
COPY scripts/ ./scripts/
COPY data/ ./data/
COPY docs/ ./docs/

# 创建必要的目录
RUN mkdir -p /app/logs /app/data/cache /app/data/models

# 设置权限
RUN chmod +x scripts/*.py

# 初始化数据库
RUN python scripts/init_memory_database.py

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8501/health || exit 1

# 暴露端口
EXPOSE 8501

# 启动命令
CMD ["streamlit", "run", "src/ui/main_enhanced.py", "--server.port=8501", "--server.address=0.0.0.0", "--server.headless=true", "--server.enableCORS=false", "--server.enableXsrfProtection=false"]
