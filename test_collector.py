#!/usr/bin/env python3
"""
测试数据采集器
"""

import sys
import os
sys.path.append('src')

from data.collector import LotteryDataCollector
from data.parser import DataParser

def test_collector():
    """测试数据采集器"""
    print("开始测试数据采集器...")
    
    # 创建采集器
    collector = LotteryDataCollector()
    
    # 尝试采集数据
    print("正在采集数据...")
    data = collector.fetch_latest_data_sync()
    
    if data:
        print(f"✅ 数据采集成功！")
        print(f"数据长度: {len(data)} 字符")
        
        # 获取数据信息
        info = collector.get_data_info(data)
        print(f"数据行数: {info['total_lines']}")
        print(f"前几行示例:")
        for i, line in enumerate(info['sample_lines'], 1):
            print(f"  {i}. {line}")
        
        # 保存原始数据
        success = collector.save_raw_data(data)
        if success:
            print("✅ 原始数据已保存")
        
        # 测试数据解析
        print("\n开始测试数据解析...")
        parser = DataParser()
        records, quality_report = parser.parse_data(data)
        
        print(f"✅ 数据解析完成！")
        print(f"有效记录数: {len(records)}")
        print(f"数据质量评分: {quality_report.quality_score}")
        
        if records:
            print(f"最新几条记录:")
            for record in records[-3:]:
                print(f"  期号: {record.period}, 日期: {record.date}, 号码: {record.numbers}")
        
        return True
        
    else:
        print("❌ 数据采集失败！")
        return False

if __name__ == "__main__":
    test_collector()
