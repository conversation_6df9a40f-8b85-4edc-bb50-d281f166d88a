#!/usr/bin/env python3
"""
数据库迁移脚本
执行预测跟踪相关的数据库结构扩展
"""

import sqlite3
import os
import sys
from pathlib import Path

def main():
    """执行数据库迁移"""
    
    # 设置路径
    project_root = Path(__file__).parent.parent
    db_path = project_root / 'data' / 'model_library.db'
    migration_script = project_root / 'src' / 'database' / 'migrations' / 'add_prediction_tracking.sql'
    
    print(f"项目根目录: {project_root}")
    print(f"数据库路径: {db_path}")
    print(f"迁移脚本路径: {migration_script}")
    
    # 检查迁移脚本是否存在
    if not migration_script.exists():
        print(f"❌ 迁移脚本不存在: {migration_script}")
        return False
    
    # 确保数据库目录存在
    db_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        # 读取迁移脚本
        with open(migration_script, 'r', encoding='utf-8') as f:
            sql_script = f.read()
        
        print("📖 迁移脚本读取成功")
        
        # 执行迁移
        with sqlite3.connect(str(db_path)) as conn:
            print("🔗 数据库连接成功")
            
            # 执行迁移脚本
            conn.executescript(sql_script)
            print("✅ 数据库迁移脚本执行成功")
            
            # 验证表是否创建成功
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%prediction%'")
            tables = cursor.fetchall()
            
            print(f"📊 创建的预测相关表: {[table[0] for table in tables]}")
            
            # 验证视图是否创建成功
            cursor.execute("SELECT name FROM sqlite_master WHERE type='view' AND name LIKE 'v_%'")
            views = cursor.fetchall()
            
            print(f"👁️ 创建的视图: {[view[0] for view in views]}")
            
            # 检查索引
            cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND name LIKE 'idx_%'")
            indexes = cursor.fetchall()
            
            print(f"🔍 创建的索引: {len([index[0] for index in indexes])} 个")
            
            print("🎉 数据库迁移完成！")
            return True
            
    except Exception as e:
        print(f"❌ 数据库迁移失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
