"""
多维度可视化引擎
实现3D参数空间可视化、损失地形图生成、收敛分析图表
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import json
from datetime import datetime
import logging


class MultiDimensionalTrainingVisualizer:
    """多维度训练可视化器"""
    
    def __init__(self):
        self.logger = logging.getLogger("MultiDimensionalVisualizer")
        self.color_schemes = {
            "viridis": px.colors.sequential.Viridis,
            "plasma": px.colors.sequential.Plasma,
            "inferno": px.colors.sequential.Inferno,
            "magma": px.colors.sequential.Magma
        }
    
    def create_3d_parameter_space(self, parameter_data: List[Dict[str, Any]], 
                                 x_param: str, y_param: str, z_param: str,
                                 color_param: str = "accuracy",
                                 title: str = "3D参数空间可视化") -> go.Figure:
        """创建3D参数空间可视化"""
        try:
            # 转换数据
            df = pd.DataFrame(parameter_data)
            
            if df.empty:
                return self._create_empty_3d_plot(title)
            
            # 确保参数存在
            required_params = [x_param, y_param, z_param, color_param]
            missing_params = [p for p in required_params if p not in df.columns]
            
            if missing_params:
                self.logger.warning(f"缺少参数: {missing_params}")
                return self._create_empty_3d_plot(title)
            
            # 创建3D散点图
            fig = go.Figure(data=go.Scatter3d(
                x=df[x_param],
                y=df[y_param],
                z=df[z_param],
                mode='markers',
                marker=dict(
                    size=8,
                    color=df[color_param],
                    colorscale='Viridis',
                    colorbar=dict(title=color_param),
                    opacity=0.8,
                    line=dict(width=0.5, color='DarkSlateGrey')
                ),
                text=[f"{color_param}: {val:.3f}" for val in df[color_param]],
                hovertemplate=f"<b>{x_param}</b>: %{{x}}<br>" +
                             f"<b>{y_param}</b>: %{{y}}<br>" +
                             f"<b>{z_param}</b>: %{{z}}<br>" +
                             f"<b>{color_param}</b>: %{{text}}<extra></extra>"
            ))
            
            # 添加最佳点标记
            if not df.empty:
                best_idx = df[color_param].idxmax()
                best_point = df.iloc[best_idx]
                
                fig.add_trace(go.Scatter3d(
                    x=[best_point[x_param]],
                    y=[best_point[y_param]],
                    z=[best_point[z_param]],
                    mode='markers',
                    marker=dict(
                        size=15,
                        color='red',
                        symbol='diamond',
                        line=dict(width=2, color='darkred')
                    ),
                    name='最佳点',
                    hovertemplate=f"<b>最佳配置</b><br>" +
                                 f"{x_param}: {best_point[x_param]}<br>" +
                                 f"{y_param}: {best_point[y_param]}<br>" +
                                 f"{z_param}: {best_point[z_param]}<br>" +
                                 f"{color_param}: {best_point[color_param]:.3f}<extra></extra>"
                ))
            
            # 设置布局
            fig.update_layout(
                title=title,
                scene=dict(
                    xaxis_title=x_param,
                    yaxis_title=y_param,
                    zaxis_title=z_param,
                    camera=dict(
                        eye=dict(x=1.5, y=1.5, z=1.5)
                    )
                ),
                width=800,
                height=600
            )
            
            return fig
            
        except Exception as e:
            self.logger.error(f"创建3D参数空间失败: {e}")
            return self._create_empty_3d_plot(title)
    
    def create_loss_landscape(self, parameter_grid: Dict[str, np.ndarray],
                             loss_values: np.ndarray,
                             param1_name: str = "learning_rate",
                             param2_name: str = "batch_size",
                             title: str = "损失地形图") -> go.Figure:
        """创建损失地形图"""
        try:
            param1_values = parameter_grid[param1_name]
            param2_values = parameter_grid[param2_name]
            
            # 创建3D表面图
            fig = go.Figure(data=go.Surface(
                x=param1_values,
                y=param2_values,
                z=loss_values,
                colorscale='Viridis',
                colorbar=dict(title="损失值"),
                hovertemplate=f"<b>{param1_name}</b>: %{{x}}<br>" +
                             f"<b>{param2_name}</b>: %{{y}}<br>" +
                             f"<b>损失</b>: %{{z:.4f}}<extra></extra>"
            ))
            
            # 添加等高线投影
            fig.add_trace(go.Contour(
                x=param1_values,
                y=param2_values,
                z=loss_values,
                colorscale='Viridis',
                showscale=False,
                opacity=0.3,
                contours=dict(
                    z=dict(show=True, usecolormap=True, highlightcolor="limegreen", project_z=True)
                )
            ))
            
            # 找到最小损失点
            min_idx = np.unravel_index(np.argmin(loss_values), loss_values.shape)
            min_param1 = param1_values[min_idx[1]]
            min_param2 = param2_values[min_idx[0]]
            min_loss = loss_values[min_idx]
            
            # 标记最小损失点
            fig.add_trace(go.Scatter3d(
                x=[min_param1],
                y=[min_param2],
                z=[min_loss],
                mode='markers',
                marker=dict(
                    size=10,
                    color='red',
                    symbol='diamond'
                ),
                name='最小损失点',
                hovertemplate=f"<b>最优配置</b><br>" +
                             f"{param1_name}: {min_param1}<br>" +
                             f"{param2_name}: {min_param2}<br>" +
                             f"损失: {min_loss:.4f}<extra></extra>"
            ))
            
            # 设置布局
            fig.update_layout(
                title=title,
                scene=dict(
                    xaxis_title=param1_name,
                    yaxis_title=param2_name,
                    zaxis_title="损失值",
                    camera=dict(
                        eye=dict(x=1.5, y=1.5, z=1.5)
                    )
                ),
                width=800,
                height=600
            )
            
            return fig
            
        except Exception as e:
            self.logger.error(f"创建损失地形图失败: {e}")
            return self._create_empty_3d_plot(title)
    
    def create_convergence_analysis(self, training_history: List[Dict[str, Any]],
                                   metrics: List[str] = ["loss", "accuracy"],
                                   title: str = "收敛分析") -> go.Figure:
        """创建收敛分析图表"""
        try:
            if not training_history:
                return self._create_empty_plot(title)
            
            df = pd.DataFrame(training_history)
            
            # 创建子图
            fig = make_subplots(
                rows=len(metrics), cols=1,
                subplot_titles=[f"{metric.title()} 收敛曲线" for metric in metrics],
                vertical_spacing=0.1
            )
            
            colors = px.colors.qualitative.Set1
            
            for i, metric in enumerate(metrics):
                if metric not in df.columns:
                    continue
                
                row = i + 1
                
                # 训练曲线
                fig.add_trace(
                    go.Scatter(
                        x=df.index,
                        y=df[metric],
                        mode='lines+markers',
                        name=f'训练 {metric}',
                        line=dict(color=colors[i % len(colors)], width=2),
                        marker=dict(size=4)
                    ),
                    row=row, col=1
                )
                
                # 验证曲线（如果存在）
                val_metric = f"val_{metric}"
                if val_metric in df.columns:
                    fig.add_trace(
                        go.Scatter(
                            x=df.index,
                            y=df[val_metric],
                            mode='lines+markers',
                            name=f'验证 {metric}',
                            line=dict(color=colors[i % len(colors)], width=2, dash='dash'),
                            marker=dict(size=4)
                        ),
                        row=row, col=1
                    )
                
                # 添加趋势线
                if len(df) > 5:
                    z = np.polyfit(df.index, df[metric], 1)
                    trend_line = np.poly1d(z)
                    
                    fig.add_trace(
                        go.Scatter(
                            x=df.index,
                            y=trend_line(df.index),
                            mode='lines',
                            name=f'{metric} 趋势',
                            line=dict(color='gray', width=1, dash='dot'),
                            opacity=0.7
                        ),
                        row=row, col=1
                    )
                
                # 标记最佳点
                if metric == "accuracy" or "acc" in metric.lower():
                    best_idx = df[metric].idxmax()
                    best_value = df[metric].iloc[best_idx]
                elif "loss" in metric.lower():
                    best_idx = df[metric].idxmin()
                    best_value = df[metric].iloc[best_idx]
                else:
                    best_idx = df[metric].idxmax()
                    best_value = df[metric].iloc[best_idx]
                
                fig.add_trace(
                    go.Scatter(
                        x=[best_idx],
                        y=[best_value],
                        mode='markers',
                        name=f'最佳 {metric}',
                        marker=dict(
                            size=10,
                            color='red',
                            symbol='star',
                            line=dict(width=2, color='darkred')
                        )
                    ),
                    row=row, col=1
                )
            
            # 更新布局
            fig.update_layout(
                title=title,
                height=300 * len(metrics),
                showlegend=True
            )
            
            # 更新x轴标签
            fig.update_xaxes(title_text="训练轮次")
            
            return fig
            
        except Exception as e:
            self.logger.error(f"创建收敛分析失败: {e}")
            return self._create_empty_plot(title)
    
    def create_hyperparameter_heatmap(self, results_data: List[Dict[str, Any]],
                                     param1: str, param2: str, metric: str = "accuracy",
                                     title: str = "超参数热力图") -> go.Figure:
        """创建超参数热力图"""
        try:
            df = pd.DataFrame(results_data)
            
            if df.empty or param1 not in df.columns or param2 not in df.columns or metric not in df.columns:
                return self._create_empty_plot(title)
            
            # 创建数据透视表
            pivot_table = df.pivot_table(
                values=metric,
                index=param2,
                columns=param1,
                aggfunc='mean'
            )
            
            # 创建热力图
            fig = go.Figure(data=go.Heatmap(
                z=pivot_table.values,
                x=pivot_table.columns,
                y=pivot_table.index,
                colorscale='Viridis',
                colorbar=dict(title=metric),
                hovertemplate=f"<b>{param1}</b>: %{{x}}<br>" +
                             f"<b>{param2}</b>: %{{y}}<br>" +
                             f"<b>{metric}</b>: %{{z:.3f}}<extra></extra>"
            ))
            
            # 添加文本注释
            for i in range(len(pivot_table.index)):
                for j in range(len(pivot_table.columns)):
                    value = pivot_table.iloc[i, j]
                    if not np.isnan(value):
                        fig.add_annotation(
                            x=pivot_table.columns[j],
                            y=pivot_table.index[i],
                            text=f"{value:.3f}",
                            showarrow=False,
                            font=dict(color="white" if value < pivot_table.values.mean() else "black")
                        )
            
            fig.update_layout(
                title=title,
                xaxis_title=param1,
                yaxis_title=param2,
                width=600,
                height=500
            )
            
            return fig
            
        except Exception as e:
            self.logger.error(f"创建超参数热力图失败: {e}")
            return self._create_empty_plot(title)
    
    def create_performance_radar_chart(self, metrics_data: Dict[str, float],
                                      title: str = "性能雷达图") -> go.Figure:
        """创建性能雷达图"""
        try:
            if not metrics_data:
                return self._create_empty_plot(title)
            
            # 准备数据
            metrics = list(metrics_data.keys())
            values = list(metrics_data.values())
            
            # 标准化值到0-1范围
            max_val = max(values) if values else 1
            normalized_values = [v / max_val for v in values]
            
            # 创建雷达图
            fig = go.Figure()
            
            fig.add_trace(go.Scatterpolar(
                r=normalized_values + [normalized_values[0]],  # 闭合图形
                theta=metrics + [metrics[0]],  # 闭合图形
                fill='toself',
                name='性能指标',
                line=dict(color='blue', width=2),
                fillcolor='rgba(0, 100, 255, 0.3)'
            ))
            
            # 添加原始值的文本
            for i, (metric, value) in enumerate(metrics_data.items()):
                fig.add_annotation(
                    x=0.5 + 0.4 * np.cos(2 * np.pi * i / len(metrics)),
                    y=0.5 + 0.4 * np.sin(2 * np.pi * i / len(metrics)),
                    text=f"{value:.3f}",
                    showarrow=False,
                    xref="paper",
                    yref="paper"
                )
            
            fig.update_layout(
                polar=dict(
                    radialaxis=dict(
                        visible=True,
                        range=[0, 1]
                    )
                ),
                title=title,
                showlegend=True
            )
            
            return fig
            
        except Exception as e:
            self.logger.error(f"创建性能雷达图失败: {e}")
            return self._create_empty_plot(title)
    
    def create_training_timeline(self, training_events: List[Dict[str, Any]],
                               title: str = "训练时间线") -> go.Figure:
        """创建训练时间线"""
        try:
            if not training_events:
                return self._create_empty_plot(title)
            
            df = pd.DataFrame(training_events)
            
            # 确保有时间戳
            if 'timestamp' not in df.columns:
                df['timestamp'] = pd.date_range(start='2024-01-01', periods=len(df), freq='H')
            else:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # 创建时间线图
            fig = go.Figure()
            
            # 不同事件类型使用不同颜色
            event_colors = {
                'start': 'green',
                'checkpoint': 'blue',
                'improvement': 'orange',
                'completion': 'red',
                'error': 'darkred'
            }
            
            for event_type in df['event_type'].unique():
                event_data = df[df['event_type'] == event_type]
                
                fig.add_trace(go.Scatter(
                    x=event_data['timestamp'],
                    y=event_data.get('value', [1] * len(event_data)),
                    mode='markers+lines',
                    name=event_type,
                    marker=dict(
                        size=10,
                        color=event_colors.get(event_type, 'gray')
                    ),
                    hovertemplate=f"<b>{event_type}</b><br>" +
                                 "时间: %{x}<br>" +
                                 "值: %{y}<br>" +
                                 "<extra></extra>"
                ))
            
            fig.update_layout(
                title=title,
                xaxis_title="时间",
                yaxis_title="值",
                hovermode='x unified'
            )
            
            return fig
            
        except Exception as e:
            self.logger.error(f"创建训练时间线失败: {e}")
            return self._create_empty_plot(title)
    
    def _create_empty_3d_plot(self, title: str) -> go.Figure:
        """创建空的3D图表"""
        fig = go.Figure()
        fig.add_trace(go.Scatter3d(x=[], y=[], z=[], mode='markers'))
        fig.update_layout(
            title=f"{title} (无数据)",
            scene=dict(
                xaxis_title="X",
                yaxis_title="Y",
                zaxis_title="Z"
            )
        )
        return fig
    
    def _create_empty_plot(self, title: str) -> go.Figure:
        """创建空的2D图表"""
        fig = go.Figure()
        fig.add_trace(go.Scatter(x=[], y=[], mode='markers'))
        fig.update_layout(title=f"{title} (无数据)")
        return fig


def test_multi_dimensional_visualizer():
    """测试多维度可视化器"""
    print("🧪 测试多维度可视化器...")
    
    visualizer = MultiDimensionalTrainingVisualizer()
    
    # 生成测试数据
    np.random.seed(42)
    
    # 3D参数空间数据
    parameter_data = []
    for i in range(50):
        lr = np.random.uniform(0.0001, 0.01)
        batch_size = np.random.choice([32, 64, 128])
        epochs = np.random.randint(50, 200)
        accuracy = 0.7 + 0.2 * np.random.random() + 0.1 * np.sin(lr * 1000)
        
        parameter_data.append({
            "learning_rate": lr,
            "batch_size": batch_size,
            "epochs": epochs,
            "accuracy": accuracy
        })
    
    # 创建3D参数空间
    fig_3d = visualizer.create_3d_parameter_space(
        parameter_data, "learning_rate", "batch_size", "epochs", "accuracy"
    )
    print("✅ 3D参数空间创建完成")
    
    # 损失地形图数据
    lr_range = np.linspace(0.001, 0.01, 20)
    bs_range = np.linspace(32, 128, 20)
    lr_grid, bs_grid = np.meshgrid(lr_range, bs_range)
    
    # 模拟损失函数
    loss_values = 1.0 - 0.5 * np.exp(-(lr_grid - 0.005)**2 / 0.00001) * np.exp(-(bs_grid - 64)**2 / 1000)
    loss_values += 0.1 * np.random.random(loss_values.shape)
    
    parameter_grid = {
        "learning_rate": lr_range,
        "batch_size": bs_range
    }
    
    # 创建损失地形图
    fig_landscape = visualizer.create_loss_landscape(
        parameter_grid, loss_values, "learning_rate", "batch_size"
    )
    print("✅ 损失地形图创建完成")
    
    # 训练历史数据
    training_history = []
    for epoch in range(100):
        loss = 1.0 * np.exp(-epoch * 0.05) + 0.1 * np.random.random()
        accuracy = 0.5 + 0.4 * (1 - np.exp(-epoch * 0.03)) + 0.05 * np.random.random()
        
        training_history.append({
            "epoch": epoch,
            "loss": loss,
            "accuracy": accuracy,
            "val_loss": loss + 0.05 * np.random.random(),
            "val_accuracy": accuracy - 0.02 + 0.03 * np.random.random()
        })
    
    # 创建收敛分析
    fig_convergence = visualizer.create_convergence_analysis(
        training_history, ["loss", "accuracy"]
    )
    print("✅ 收敛分析创建完成")
    
    # 性能雷达图数据
    metrics_data = {
        "准确率": 0.85,
        "精确率": 0.82,
        "召回率": 0.88,
        "F1分数": 0.85,
        "AUC": 0.91
    }
    
    # 创建性能雷达图
    fig_radar = visualizer.create_performance_radar_chart(metrics_data)
    print("✅ 性能雷达图创建完成")
    
    print("✅ 多维度可视化器测试完成！")


if __name__ == "__main__":
    test_multi_dimensional_visualizer()
