#!/usr/bin/env python3
"""
预测偏差分析器
Prediction Deviation Analyzer

实现多维度预测偏差分析
"""

import logging
import os
import statistics
import sys
from collections import Counter
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import numpy as np

sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from core.unified_prediction_storage import PredictionRecord


@dataclass
class DeviationAnalysisResult:
    """偏差分析结果"""
    numerical_deviation: Dict[str, Any]
    pattern_deviation: Dict[str, Any]
    confidence_calibration: Dict[str, Any]
    temporal_consistency: Dict[str, Any]
    feature_contribution: Dict[str, Any]
    overall_score: float
    analysis_timestamp: datetime


class PredictionDeviationAnalyzer:
    """预测偏差分析器"""
    
    def __init__(self):
        """初始化预测偏差分析器"""
        self.logger = logging.getLogger(__name__)
        
        # 分析维度权重
        self.dimension_weights = {
            'numerical_deviation': 0.3,
            'pattern_deviation': 0.25,
            'confidence_calibration': 0.2,
            'temporal_consistency': 0.15,
            'feature_contribution': 0.1
        }
    
    def analyze_deviation(self, prediction: PredictionRecord, actual_result: str) -> DeviationAnalysisResult:
        """
        多维度偏差分析
        
        Args:
            prediction: 预测记录
            actual_result: 实际结果
            
        Returns:
            偏差分析结果
        """
        try:
            self.logger.info(f"开始分析预测偏差: {prediction.model_name} - {prediction.period_number}")
            
            # 1. 数值偏差分析
            numerical_deviation = self.analyze_numerical_deviation(prediction, actual_result)
            
            # 2. 模式偏差分析
            pattern_deviation = self.analyze_pattern_deviation(prediction, actual_result)
            
            # 3. 置信度校准分析
            confidence_calibration = self.analyze_confidence_calibration(prediction, actual_result)
            
            # 4. 时间一致性分析
            temporal_consistency = self.analyze_temporal_consistency(prediction)
            
            # 5. 特征贡献度分析
            feature_contribution = self.analyze_feature_contribution(prediction, actual_result)
            
            # 6. 计算综合评分
            overall_score = self._calculate_overall_score({
                'numerical_deviation': numerical_deviation,
                'pattern_deviation': pattern_deviation,
                'confidence_calibration': confidence_calibration,
                'temporal_consistency': temporal_consistency,
                'feature_contribution': feature_contribution
            })
            
            return DeviationAnalysisResult(
                numerical_deviation=numerical_deviation,
                pattern_deviation=pattern_deviation,
                confidence_calibration=confidence_calibration,
                temporal_consistency=temporal_consistency,
                feature_contribution=feature_contribution,
                overall_score=overall_score,
                analysis_timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"偏差分析失败: {e}")
            raise
    
    def analyze_numerical_deviation(self, prediction: PredictionRecord, actual_result: str) -> Dict[str, Any]:
        """
        数值偏差详细分析
        
        Args:
            prediction: 预测记录
            actual_result: 实际结果
            
        Returns:
            数值偏差分析结果
        """
        pred_numbers = prediction.predicted_numbers
        actual_numbers = actual_result
        
        analysis = {
            'exact_match': pred_numbers == actual_numbers,
            'position_accuracy': {},
            'sum_deviation': {},
            'span_deviation': {},
            'proximity_scores': {},
            'digit_distribution_deviation': {}
        }
        
        # 位置准确性分析
        for i, (pred, actual) in enumerate(zip(pred_numbers, actual_numbers)):
            position = ['百位', '十位', '个位'][i]
            deviation = abs(int(pred) - int(actual))
            proximity_score = max(0, (10 - deviation) / 10)
            
            analysis['position_accuracy'][position] = {
                'exact_match': pred == actual,
                'deviation': deviation,
                'proximity_score': proximity_score,
                'relative_error': deviation / 9.0  # 最大偏差为9
            }
        
        # 和值偏差分析
        pred_sum = sum(int(d) for d in pred_numbers)
        actual_sum = sum(int(d) for d in actual_numbers)
        sum_deviation = abs(pred_sum - actual_sum)
        
        analysis['sum_deviation'] = {
            'predicted_sum': pred_sum,
            'actual_sum': actual_sum,
            'absolute_deviation': sum_deviation,
            'relative_error': sum_deviation / actual_sum if actual_sum > 0 else 0,
            'severity': self._classify_deviation_severity(sum_deviation, 'sum')
        }
        
        # 跨度偏差分析
        pred_span = max(int(d) for d in pred_numbers) - min(int(d) for d in pred_numbers)
        actual_span = max(int(d) for d in actual_numbers) - min(int(d) for d in actual_numbers)
        span_deviation = abs(pred_span - actual_span)
        
        analysis['span_deviation'] = {
            'predicted_span': pred_span,
            'actual_span': actual_span,
            'absolute_deviation': span_deviation,
            'severity': self._classify_deviation_severity(span_deviation, 'span')
        }
        
        # 数字分布偏差
        pred_digits = [int(d) for d in pred_numbers]
        actual_digits = [int(d) for d in actual_numbers]
        
        analysis['digit_distribution_deviation'] = {
            'predicted_distribution': Counter(pred_digits),
            'actual_distribution': Counter(actual_digits),
            'distribution_similarity': self._calculate_distribution_similarity(pred_digits, actual_digits)
        }
        
        return analysis
    
    def analyze_pattern_deviation(self, prediction: PredictionRecord, actual_result: str) -> Dict[str, Any]:
        """
        模式偏差分析
        
        Args:
            prediction: 预测记录
            actual_result: 实际结果
            
        Returns:
            模式偏差分析结果
        """
        pred_numbers = prediction.predicted_numbers
        actual_numbers = actual_result
        
        analysis = {
            'sequence_pattern': {},
            'parity_pattern': {},
            'size_pattern': {},
            'repetition_pattern': {}
        }
        
        # 序列模式分析
        pred_sequence = self._analyze_sequence_pattern(pred_numbers)
        actual_sequence = self._analyze_sequence_pattern(actual_numbers)
        
        analysis['sequence_pattern'] = {
            'predicted': pred_sequence,
            'actual': actual_sequence,
            'pattern_match': pred_sequence == actual_sequence,
            'pattern_similarity': self._calculate_pattern_similarity(pred_sequence, actual_sequence)
        }
        
        # 奇偶模式分析
        pred_parity = [int(d) % 2 for d in pred_numbers]
        actual_parity = [int(d) % 2 for d in actual_numbers]
        
        analysis['parity_pattern'] = {
            'predicted': pred_parity,
            'actual': actual_parity,
            'pattern_match': pred_parity == actual_parity,
            'match_count': sum(1 for p, a in zip(pred_parity, actual_parity) if p == a)
        }
        
        # 大小模式分析（以5为界）
        pred_size = [1 if int(d) >= 5 else 0 for d in pred_numbers]
        actual_size = [1 if int(d) >= 5 else 0 for d in actual_numbers]
        
        analysis['size_pattern'] = {
            'predicted': pred_size,
            'actual': actual_size,
            'pattern_match': pred_size == actual_size,
            'match_count': sum(1 for p, a in zip(pred_size, actual_size) if p == a)
        }
        
        # 重复模式分析
        pred_repeats = len(pred_numbers) - len(set(pred_numbers))
        actual_repeats = len(actual_numbers) - len(set(actual_numbers))
        
        analysis['repetition_pattern'] = {
            'predicted_repeats': pred_repeats,
            'actual_repeats': actual_repeats,
            'repeat_match': pred_repeats == actual_repeats,
            'repeat_deviation': abs(pred_repeats - actual_repeats)
        }
        
        return analysis
    
    def analyze_confidence_calibration(self, prediction: PredictionRecord, actual_result: str) -> Dict[str, Any]:
        """
        置信度校准分析
        
        Args:
            prediction: 预测记录
            actual_result: 实际结果
            
        Returns:
            置信度校准分析结果
        """
        confidence = prediction.confidence
        accuracy_score = prediction.accuracy_score or 0.0
        
        analysis = {
            'confidence_level': confidence,
            'actual_accuracy': accuracy_score,
            'calibration_error': abs(confidence - accuracy_score),
            'overconfidence': confidence > accuracy_score,
            'underconfidence': confidence < accuracy_score,
            'calibration_quality': {}
        }
        
        # 校准质量评估
        calibration_error = analysis['calibration_error']
        if calibration_error <= 0.1:
            calibration_quality = 'excellent'
        elif calibration_error <= 0.2:
            calibration_quality = 'good'
        elif calibration_error <= 0.3:
            calibration_quality = 'fair'
        else:
            calibration_quality = 'poor'
        
        analysis['calibration_quality'] = {
            'level': calibration_quality,
            'score': max(0, 1 - calibration_error),
            'improvement_needed': calibration_error > 0.2
        }
        
        return analysis
    
    def analyze_temporal_consistency(self, prediction: PredictionRecord) -> Dict[str, Any]:
        """
        时间一致性分析
        
        Args:
            prediction: 预测记录
            
        Returns:
            时间一致性分析结果
        """
        analysis = {
            'prediction_timing': {},
            'model_stability': {},
            'temporal_patterns': {}
        }
        
        # 预测时间分析
        if prediction.prediction_time:
            prediction_hour = prediction.prediction_time.hour
            prediction_weekday = prediction.prediction_time.weekday()
            
            analysis['prediction_timing'] = {
                'hour': prediction_hour,
                'weekday': prediction_weekday,
                'is_business_hours': 9 <= prediction_hour <= 17,
                'is_weekend': prediction_weekday >= 5
            }
        
        # 模型稳定性（需要历史数据，这里提供框架）
        analysis['model_stability'] = {
            'consistency_score': 0.8,  # 占位符，需要历史数据计算
            'volatility': 0.2,  # 占位符
            'trend_stability': 'stable'  # 占位符
        }
        
        return analysis
    
    def analyze_feature_contribution(self, prediction: PredictionRecord, actual_result: str) -> Dict[str, Any]:
        """
        特征贡献度分析
        
        Args:
            prediction: 预测记录
            actual_result: 实际结果
            
        Returns:
            特征贡献度分析结果
        """
        analysis = {
            'metadata_features': {},
            'prediction_features': {},
            'contribution_scores': {}
        }
        
        # 分析元数据中的特征
        if prediction.metadata:
            analysis['metadata_features'] = {
                'available_features': list(prediction.metadata.keys()),
                'feature_count': len(prediction.metadata),
                'feature_types': self._classify_feature_types(prediction.metadata)
            }
        
        # 预测特征分析
        pred_numbers = prediction.predicted_numbers
        analysis['prediction_features'] = {
            'number_complexity': self._calculate_number_complexity(pred_numbers),
            'pattern_strength': self._calculate_pattern_strength(pred_numbers),
            'randomness_score': self._calculate_randomness_score(pred_numbers)
        }
        
        return analysis
    
    def _calculate_overall_score(self, analysis_results: Dict[str, Any]) -> float:
        """计算综合评分"""
        scores = {}
        
        # 数值偏差评分
        numerical = analysis_results['numerical_deviation']
        if numerical['exact_match']:
            scores['numerical_deviation'] = 1.0
        else:
            # 基于位置准确性和接近度计算
            position_scores = [pos['proximity_score'] for pos in numerical['position_accuracy'].values()]
            scores['numerical_deviation'] = np.mean(position_scores)
        
        # 模式偏差评分
        pattern = analysis_results['pattern_deviation']
        pattern_matches = [
            pattern['sequence_pattern']['pattern_similarity'],
            pattern['parity_pattern']['match_count'] / 3,
            pattern['size_pattern']['match_count'] / 3,
            1.0 if pattern['repetition_pattern']['repeat_match'] else 0.0
        ]
        scores['pattern_deviation'] = np.mean(pattern_matches)
        
        # 置信度校准评分
        confidence = analysis_results['confidence_calibration']
        scores['confidence_calibration'] = confidence['calibration_quality']['score']
        
        # 时间一致性评分
        temporal = analysis_results['temporal_consistency']
        scores['temporal_consistency'] = temporal['model_stability']['consistency_score']
        
        # 特征贡献评分
        scores['feature_contribution'] = 0.7  # 占位符
        
        # 加权平均
        overall_score = sum(
            scores[dim] * weight 
            for dim, weight in self.dimension_weights.items()
        )
        
        return overall_score
    
    def _classify_deviation_severity(self, deviation: float, deviation_type: str) -> str:
        """分类偏差严重程度"""
        if deviation_type == 'sum':
            if deviation <= 2:
                return 'low'
            elif deviation <= 5:
                return 'medium'
            else:
                return 'high'
        elif deviation_type == 'span':
            if deviation <= 1:
                return 'low'
            elif deviation <= 3:
                return 'medium'
            else:
                return 'high'
        
        return 'unknown'
    
    def _calculate_distribution_similarity(self, pred_digits: List[int], actual_digits: List[int]) -> float:
        """计算数字分布相似度"""
        pred_counter = Counter(pred_digits)
        actual_counter = Counter(actual_digits)
        
        # 计算Jaccard相似度
        intersection = sum((pred_counter & actual_counter).values())
        union = sum((pred_counter | actual_counter).values())
        
        return intersection / union if union > 0 else 0.0
    
    def _analyze_sequence_pattern(self, numbers: str) -> str:
        """分析序列模式"""
        digits = [int(d) for d in numbers]
        
        # 检查是否为递增序列
        if all(digits[i] <= digits[i+1] for i in range(len(digits)-1)):
            return 'ascending'
        
        # 检查是否为递减序列
        if all(digits[i] >= digits[i+1] for i in range(len(digits)-1)):
            return 'descending'
        
        # 检查是否为等差序列
        if len(set(digits[i+1] - digits[i] for i in range(len(digits)-1))) == 1:
            return 'arithmetic'
        
        return 'random'
    
    def _calculate_pattern_similarity(self, pattern1: str, pattern2: str) -> float:
        """计算模式相似度"""
        if pattern1 == pattern2:
            return 1.0
        
        # 定义模式相似度矩阵
        similarity_matrix = {
            ('ascending', 'arithmetic'): 0.7,
            ('descending', 'arithmetic'): 0.7,
            ('ascending', 'descending'): 0.3,
        }
        
        key = tuple(sorted([pattern1, pattern2]))
        return similarity_matrix.get(key, 0.0)
    
    def _classify_feature_types(self, metadata: Dict) -> Dict[str, int]:
        """分类特征类型"""
        feature_types = {'numerical': 0, 'categorical': 0, 'temporal': 0, 'other': 0}
        
        for key, value in metadata.items():
            if isinstance(value, (int, float)):
                feature_types['numerical'] += 1
            elif isinstance(value, str):
                if 'time' in key.lower() or 'date' in key.lower():
                    feature_types['temporal'] += 1
                else:
                    feature_types['categorical'] += 1
            else:
                feature_types['other'] += 1
        
        return feature_types
    
    def _calculate_number_complexity(self, numbers: str) -> float:
        """计算号码复杂度"""
        digits = [int(d) for d in numbers]
        
        # 基于数字分布的复杂度
        unique_count = len(set(digits))
        variance = np.var(digits)
        
        complexity = (unique_count / 3) * 0.6 + min(variance / 10, 1.0) * 0.4
        return complexity
    
    def _calculate_pattern_strength(self, numbers: str) -> float:
        """计算模式强度"""
        digits = [int(d) for d in numbers]
        
        # 检查各种模式的强度
        patterns = []
        
        # 连续性
        consecutive = sum(1 for i in range(len(digits)-1) if abs(digits[i+1] - digits[i]) == 1)
        patterns.append(consecutive / (len(digits) - 1))
        
        # 重复性
        repetition = (len(digits) - len(set(digits))) / len(digits)
        patterns.append(repetition)
        
        return max(patterns)
    
    def _calculate_randomness_score(self, numbers: str) -> float:
        """计算随机性评分"""
        digits = [int(d) for d in numbers]
        
        # 基于熵计算随机性
        counter = Counter(digits)
        probabilities = [count / len(digits) for count in counter.values()]
        entropy = -sum(p * np.log2(p) for p in probabilities if p > 0)
        
        # 标准化到0-1范围
        max_entropy = np.log2(len(set(digits)))
        return entropy / max_entropy if max_entropy > 0 else 0.0


if __name__ == "__main__":
    # 测试代码
    from datetime import datetime

    from ..core.unified_prediction_storage import PredictionRecord

    # 创建测试预测记录
    test_prediction = PredictionRecord(
        period_number="2025194",
        model_name="test_model",
        predicted_numbers="123",
        confidence=0.75,
        prediction_time=datetime.now(),
        accuracy_score=0.6
    )
    
    # 创建分析器
    analyzer = PredictionDeviationAnalyzer()
    
    # 执行分析
    result = analyzer.analyze_deviation(test_prediction, "124")
    
    print("偏差分析结果:")
    print(f"数值偏差: {result.numerical_deviation}")
    print(f"模式偏差: {result.pattern_deviation}")
    print(f"置信度校准: {result.confidence_calibration}")
    print(f"综合评分: {result.overall_score:.3f}")
