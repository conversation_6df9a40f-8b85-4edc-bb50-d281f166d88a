"""
福彩3D系统错误处理工具

提供统一的错误处理、日志记录和用户友好的错误提示
"""

import logging
import traceback
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, Callable
import streamlit as st

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ErrorType(Enum):
    """错误类型枚举"""
    SCHEDULER_START_FAILED = "scheduler_start_failed"
    SCHEDULER_STOP_FAILED = "scheduler_stop_failed"
    CONFIG_LOAD_FAILED = "config_load_failed"
    CONFIG_SAVE_FAILED = "config_save_failed"
    PERMISSION_DENIED = "permission_denied"
    NETWORK_ERROR = "network_error"
    DATA_UPDATE_FAILED = "data_update_failed"
    VALIDATION_ERROR = "validation_error"
    UNKNOWN_ERROR = "unknown_error"


class ErrorHandler:
    """错误处理器"""
    
    def __init__(self):
        self.error_messages = {
            ErrorType.SCHEDULER_START_FAILED: {
                "title": "调度器启动失败",
                "description": "无法启动自动更新调度器",
                "suggestions": [
                    "检查是否有足够的系统权限",
                    "确认端口没有被其他程序占用",
                    "检查配置文件是否正确",
                    "尝试重启应用程序"
                ]
            },
            ErrorType.SCHEDULER_STOP_FAILED: {
                "title": "调度器停止失败",
                "description": "无法正常停止调度器服务",
                "suggestions": [
                    "等待几秒钟后重试",
                    "检查进程是否仍在运行",
                    "如果问题持续，请重启应用程序"
                ]
            },
            ErrorType.CONFIG_LOAD_FAILED: {
                "title": "配置加载失败",
                "description": "无法读取或解析配置文件",
                "suggestions": [
                    "检查配置文件是否存在",
                    "验证JSON格式是否正确",
                    "检查文件读取权限",
                    "尝试重置为默认配置"
                ]
            },
            ErrorType.CONFIG_SAVE_FAILED: {
                "title": "配置保存失败",
                "description": "无法保存配置到文件",
                "suggestions": [
                    "检查文件写入权限",
                    "确认磁盘空间充足",
                    "检查文件是否被其他程序占用"
                ]
            },
            ErrorType.PERMISSION_DENIED: {
                "title": "权限不足",
                "description": "没有足够的权限执行此操作",
                "suggestions": [
                    "以管理员身份运行程序",
                    "检查文件和目录权限",
                    "确认用户有相应的操作权限"
                ]
            },
            ErrorType.NETWORK_ERROR: {
                "title": "网络连接错误",
                "description": "无法连接到数据源或网络服务",
                "suggestions": [
                    "检查网络连接是否正常",
                    "确认防火墙设置",
                    "检查代理设置",
                    "稍后重试"
                ]
            },
            ErrorType.DATA_UPDATE_FAILED: {
                "title": "数据更新失败",
                "description": "无法完成数据更新操作",
                "suggestions": [
                    "检查数据源是否可访问",
                    "验证数据格式是否正确",
                    "检查存储空间是否充足",
                    "尝试使用增量更新模式"
                ]
            },
            ErrorType.VALIDATION_ERROR: {
                "title": "数据验证错误",
                "description": "数据格式或内容不符合要求",
                "suggestions": [
                    "检查数据源格式",
                    "验证数据完整性",
                    "查看详细的验证报告"
                ]
            },
            ErrorType.UNKNOWN_ERROR: {
                "title": "未知错误",
                "description": "发生了未预期的错误",
                "suggestions": [
                    "查看详细错误信息",
                    "尝试重新操作",
                    "如果问题持续，请联系技术支持"
                ]
            }
        }
    
    def handle_error(self, error: Exception, error_type: ErrorType = ErrorType.UNKNOWN_ERROR, 
                    context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        处理错误并返回格式化的错误信息
        
        Args:
            error: 异常对象
            error_type: 错误类型
            context: 错误上下文信息
            
        Returns:
            格式化的错误信息字典
        """
        error_info = self.error_messages.get(error_type, self.error_messages[ErrorType.UNKNOWN_ERROR])
        
        # 记录错误日志
        logger.error(f"{error_info['title']}: {str(error)}")
        if context:
            logger.error(f"错误上下文: {context}")
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        
        # 构建错误响应
        error_response = {
            "success": False,
            "error_type": error_type.value,
            "title": error_info["title"],
            "description": error_info["description"],
            "message": str(error),
            "suggestions": error_info["suggestions"],
            "timestamp": datetime.now().isoformat(),
            "context": context or {}
        }
        
        return error_response
    
    def show_error_in_ui(self, error_response: Dict[str, Any]):
        """在Streamlit界面中显示错误信息"""
        st.error(f"❌ **{error_response['title']}**")
        st.write(f"📝 {error_response['description']}")
        
        if error_response.get('message'):
            with st.expander("🔍 查看详细错误信息"):
                st.code(error_response['message'])
        
        # 显示建议
        if error_response.get('suggestions'):
            st.markdown("💡 **解决建议**:")
            for i, suggestion in enumerate(error_response['suggestions'], 1):
                st.markdown(f"{i}. {suggestion}")
        
        # 显示时间戳
        if error_response.get('timestamp'):
            st.caption(f"错误时间: {error_response['timestamp']}")
    
    def safe_execute(self, func: Callable, error_type: ErrorType = ErrorType.UNKNOWN_ERROR,
                    context: Optional[Dict[str, Any]] = None, show_in_ui: bool = True) -> Dict[str, Any]:
        """
        安全执行函数，自动处理异常
        
        Args:
            func: 要执行的函数
            error_type: 错误类型
            context: 错误上下文
            show_in_ui: 是否在UI中显示错误
            
        Returns:
            执行结果或错误信息
        """
        try:
            result = func()
            if isinstance(result, dict) and not result.get("success", True):
                # 函数返回了错误结果
                if show_in_ui:
                    st.error(f"❌ {result.get('message', '操作失败')}")
                return result
            else:
                # 成功执行
                return {"success": True, "result": result}
        except Exception as e:
            error_response = self.handle_error(e, error_type, context)
            if show_in_ui:
                self.show_error_in_ui(error_response)
            return error_response


class SchedulerErrorHandler(ErrorHandler):
    """调度器专用错误处理器"""
    
    def handle_scheduler_start_error(self, error: Exception, context: Optional[Dict[str, Any]] = None):
        """处理调度器启动错误"""
        return self.handle_error(error, ErrorType.SCHEDULER_START_FAILED, context)
    
    def handle_scheduler_stop_error(self, error: Exception, context: Optional[Dict[str, Any]] = None):
        """处理调度器停止错误"""
        return self.handle_error(error, ErrorType.SCHEDULER_STOP_FAILED, context)
    
    def handle_config_error(self, error: Exception, operation: str = "load", 
                           context: Optional[Dict[str, Any]] = None):
        """处理配置相关错误"""
        error_type = ErrorType.CONFIG_LOAD_FAILED if operation == "load" else ErrorType.CONFIG_SAVE_FAILED
        return self.handle_error(error, error_type, context)
    
    def handle_permission_error(self, error: Exception, context: Optional[Dict[str, Any]] = None):
        """处理权限错误"""
        return self.handle_error(error, ErrorType.PERMISSION_DENIED, context)


class DataUpdateErrorHandler(ErrorHandler):
    """数据更新专用错误处理器"""
    
    def handle_network_error(self, error: Exception, context: Optional[Dict[str, Any]] = None):
        """处理网络错误"""
        return self.handle_error(error, ErrorType.NETWORK_ERROR, context)
    
    def handle_data_update_error(self, error: Exception, context: Optional[Dict[str, Any]] = None):
        """处理数据更新错误"""
        return self.handle_error(error, ErrorType.DATA_UPDATE_FAILED, context)
    
    def handle_validation_error(self, error: Exception, context: Optional[Dict[str, Any]] = None):
        """处理数据验证错误"""
        return self.handle_error(error, ErrorType.VALIDATION_ERROR, context)


# 全局错误处理器实例
_scheduler_error_handler = None
_data_update_error_handler = None

def get_scheduler_error_handler() -> SchedulerErrorHandler:
    """获取调度器错误处理器单例"""
    global _scheduler_error_handler
    if _scheduler_error_handler is None:
        _scheduler_error_handler = SchedulerErrorHandler()
    return _scheduler_error_handler

def get_data_update_error_handler() -> DataUpdateErrorHandler:
    """获取数据更新错误处理器单例"""
    global _data_update_error_handler
    if _data_update_error_handler is None:
        _data_update_error_handler = DataUpdateErrorHandler()
    return _data_update_error_handler


# 装饰器函数
def handle_scheduler_errors(error_type: ErrorType = ErrorType.UNKNOWN_ERROR):
    """调度器错误处理装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            handler = get_scheduler_error_handler()
            return handler.safe_execute(
                lambda: func(*args, **kwargs),
                error_type=error_type,
                context={"function": func.__name__, "args": str(args), "kwargs": str(kwargs)}
            )
        return wrapper
    return decorator

def handle_data_update_errors(error_type: ErrorType = ErrorType.DATA_UPDATE_FAILED):
    """数据更新错误处理装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            handler = get_data_update_error_handler()
            return handler.safe_execute(
                lambda: func(*args, **kwargs),
                error_type=error_type,
                context={"function": func.__name__, "args": str(args), "kwargs": str(kwargs)}
            )
        return wrapper
    return decorator
