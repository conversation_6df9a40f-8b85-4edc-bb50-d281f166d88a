"""
预测交互组件开发
开发预测相关的交互组件，支持个性化设置和用户自定义参数
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import numpy as np

def prediction_control_panel() -> Dict[str, Any]:
    """
    预测控制面板组件
    
    Returns:
        用户设置参数字典
    """
    st.markdown("### ⚙️ 预测控制面板")
    
    with st.container():
        # 基础设置
        st.markdown("#### 📊 基础设置")
        
        col1, col2 = st.columns(2)
        
        with col1:
            ranking_count = st.slider(
                "排行榜显示数量",
                min_value=5,
                max_value=20,
                value=10,
                help="设置候选号码排行榜显示的数量"
            )
            
            confidence_threshold = st.slider(
                "置信度阈值",
                min_value=0.1,
                max_value=0.9,
                value=0.3,
                step=0.1,
                format="%.1f",
                help="设置预测结果的最低置信度要求"
            )
        
        with col2:
            window_size = st.selectbox(
                "历史数据窗口",
                options=[30, 50, 100, 200],
                index=1,
                help="设置用于预测分析的历史数据期数"
            )
            
            fusion_strategy = st.selectbox(
                "融合策略",
                options=["comprehensive", "conservative", "aggressive"],
                index=0,
                format_func=lambda x: {
                    "comprehensive": "综合策略",
                    "conservative": "保守策略", 
                    "aggressive": "激进策略"
                }[x],
                help="选择预测融合策略"
            )
        
        # 高级设置
        with st.expander("🔧 高级设置"):
            col3, col4 = st.columns(2)
            
            with col3:
                enable_historical_bonus = st.checkbox(
                    "启用历史命中率加成",
                    value=True,
                    help="是否在评分中考虑历史命中率"
                )
                
                show_technical_details = st.checkbox(
                    "显示技术详情",
                    value=True,
                    help="是否显示预测的技术详情"
                )
            
            with col4:
                auto_refresh = st.checkbox(
                    "自动刷新",
                    value=False,
                    help="是否自动刷新预测结果"
                )
                
                if auto_refresh:
                    refresh_interval = st.slider(
                        "刷新间隔(秒)",
                        min_value=30,
                        max_value=300,
                        value=60,
                        step=30
                    )
                else:
                    refresh_interval = 0
        
        # 模型权重自定义
        with st.expander("⚖️ 模型权重自定义"):
            st.markdown("**手动调整模型权重（可选）**")
            
            use_custom_weights = st.checkbox("使用自定义权重", value=False)
            
            if use_custom_weights:
                col5, col6 = st.columns(2)
                
                with col5:
                    markov_weight = st.slider("马尔可夫模型", 0.0, 1.0, 0.25, 0.05)
                    cnn_lstm_weight = st.slider("CNN-LSTM模型", 0.0, 1.0, 0.25, 0.05)
                
                with col6:
                    trend_weight = st.slider("趋势分析模型", 0.0, 1.0, 0.25, 0.05)
                    fusion_weight = st.slider("智能融合模型", 0.0, 1.0, 0.25, 0.05)
                
                # 权重归一化提醒
                total_weight = markov_weight + cnn_lstm_weight + trend_weight + fusion_weight
                if abs(total_weight - 1.0) > 0.01:
                    st.warning(f"⚠️ 权重总和为 {total_weight:.2f}，建议调整为 1.0")
                
                custom_weights = {
                    'markov_enhanced': markov_weight,
                    'deep_learning_cnn_lstm': cnn_lstm_weight,
                    'trend_analyzer': trend_weight,
                    'intelligent_fusion': fusion_weight
                }
            else:
                custom_weights = None
    
    return {
        'ranking_count': ranking_count,
        'confidence_threshold': confidence_threshold,
        'window_size': window_size,
        'fusion_strategy': fusion_strategy,
        'enable_historical_bonus': enable_historical_bonus,
        'show_technical_details': show_technical_details,
        'auto_refresh': auto_refresh,
        'refresh_interval': refresh_interval,
        'custom_weights': custom_weights
    }

def prediction_result_card(prediction: Dict[str, Any], show_details: bool = True) -> None:
    """
    预测结果卡片组件
    
    Args:
        prediction: 预测结果数据
        show_details: 是否显示详细信息
    """
    # 主卡片样式
    card_style = """
    <div style="
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 25px;
        color: white;
        text-align: center;
        margin: 15px 0;
        box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    ">
        <h1 style="
            font-size: 3.5rem;
            font-weight: bold;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        ">{number}</h1>
        <p style="font-size: 1.1rem; margin: 10px 0 0 0; opacity: 0.9;">
            {method} | 置信度: {confidence:.1%}
        </p>
    </div>
    """
    
    st.markdown(
        card_style.format(
            number=prediction['number'],
            method=prediction['fusion_method'],
            confidence=prediction['confidence']
        ),
        unsafe_allow_html=True
    )
    
    if show_details:
        # 详细信息
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric(
                "🎯 推荐等级",
                prediction['recommendation_level'],
                help="基于置信度的推荐等级"
            )
        
        with col2:
            hit_rate = prediction.get('historical_hit_rate', 0) * 100
            st.metric(
                "📈 历史命中率",
                f"{hit_rate:.1f}%",
                help="该号码在历史数据中的出现频率"
            )
        
        with col3:
            support_count = len(prediction.get('model_support', []))
            st.metric(
                "🤝 模型支持",
                f"{support_count}/4",
                help="支持该预测的模型数量"
            )

def ranking_table(ranking_list: List[Dict[str, Any]], interactive: bool = True) -> Optional[str]:
    """
    排行榜表格组件
    
    Args:
        ranking_list: 排行榜数据列表
        interactive: 是否启用交互功能
        
    Returns:
        选中的号码（如果启用交互）
    """
    if not ranking_list:
        st.warning("📋 暂无排行榜数据")
        return None
    
    # 转换为DataFrame
    df = pd.DataFrame(ranking_list)
    
    # 格式化数据
    display_data = []
    for item in ranking_list:
        # 推荐等级图标
        level_icons = {
            "强烈推荐": "🔴",
            "推荐": "🟠",
            "可考虑": "🟡",
            "谨慎": "🟢",
            "不推荐": "⚪"
        }
        
        display_data.append({
            "排名": item['rank'],
            "号码": item['number'],
            "置信度": f"{item['confidence']:.1%}",
            "综合评分": f"{item['composite_score']:.3f}",
            "模型支持": f"{item['model_support_count']}/4",
            "历史命中率": f"{item['historical_hit_rate']:.1%}",
            "推荐等级": f"{level_icons.get(item['recommendation_level'], '⚪')} {item['recommendation_level']}"
        })
    
    display_df = pd.DataFrame(display_data)
    
    if interactive:
        # 交互式表格
        selected_rows = st.dataframe(
            display_df,
            use_container_width=True,
            hide_index=True,
            on_select="rerun",
            selection_mode="single-row"
        )
        
        # 返回选中的号码
        if selected_rows and len(selected_rows['selection']['rows']) > 0:
            selected_idx = selected_rows['selection']['rows'][0]
            return ranking_list[selected_idx]['number']
    else:
        # 静态表格
        st.dataframe(
            display_df,
            use_container_width=True,
            hide_index=True
        )
    
    return None

def model_performance_chart(performance_data: List[Dict[str, Any]], chart_type: str = "bar") -> None:
    """
    模型性能图表组件
    
    Args:
        performance_data: 模型性能数据
        chart_type: 图表类型 ("bar", "radar", "line")
    """
    if not performance_data:
        st.warning("📈 暂无模型性能数据")
        return
    
    df = pd.DataFrame(performance_data)
    
    if chart_type == "bar":
        # 柱状图
        fig = px.bar(
            df,
            x='model_name',
            y='accuracy_rate',
            title='模型准确率对比',
            labels={'model_name': '模型名称', 'accuracy_rate': '准确率'},
            color='accuracy_rate',
            color_continuous_scale='viridis',
            text='accuracy_rate'
        )
        
        fig.update_traces(texttemplate='%{text:.1%}', textposition='outside')
        fig.update_layout(height=400, showlegend=False)
        
    elif chart_type == "radar":
        # 雷达图
        categories = ['准确率', '预测次数', '当前权重', '稳定性']
        
        fig = go.Figure()
        
        for _, model in df.iterrows():
            # 归一化数据用于雷达图
            values = [
                model['accuracy_rate'],
                model['total_predictions'] / df['total_predictions'].max(),
                model['current_weight'],
                0.8  # 稳定性占位符
            ]
            
            fig.add_trace(go.Scatterpolar(
                r=values,
                theta=categories,
                fill='toself',
                name=model['model_name']
            ))
        
        fig.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 1]
                )),
            showlegend=True,
            title="模型性能雷达图",
            height=500
        )
    
    elif chart_type == "line":
        # 趋势线图（模拟历史数据）
        fig = go.Figure()
        
        # 为每个模型生成模拟的历史准确率趋势
        dates = pd.date_range(start=datetime.now() - timedelta(days=30), end=datetime.now(), freq='D')
        
        for _, model in df.iterrows():
            # 生成模拟趋势数据
            base_accuracy = model['accuracy_rate']
            trend_data = np.random.normal(base_accuracy, 0.05, len(dates))
            trend_data = np.clip(trend_data, 0, 1)  # 限制在0-1范围内
            
            fig.add_trace(go.Scatter(
                x=dates,
                y=trend_data,
                mode='lines+markers',
                name=model['model_name'],
                line=dict(width=2)
            ))
        
        fig.update_layout(
            title="模型准确率趋势",
            xaxis_title="日期",
            yaxis_title="准确率",
            height=400,
            hovermode='x unified'
        )
    
    st.plotly_chart(fig, use_container_width=True)

def prediction_history_timeline(history_data: List[Dict[str, Any]]) -> None:
    """
    预测历史时间线组件
    
    Args:
        history_data: 历史预测数据
    """
    if not history_data:
        st.info("📅 暂无历史预测数据")
        return
    
    st.markdown("### 📅 预测历史时间线")
    
    # 创建时间线数据
    timeline_data = []
    for item in history_data:
        timeline_data.append({
            "期号": item['period_number'],
            "预测号码": item['predicted_number'],
            "实际号码": item.get('actual_number', '待开奖'),
            "是否命中": "✅" if item.get('is_hit') else "❌" if item.get('actual_number') else "⏳",
            "置信度": f"{item['confidence']:.1%}",
            "预测时间": item['prediction_date']
        })
    
    timeline_df = pd.DataFrame(timeline_data)
    
    # 显示时间线表格
    st.dataframe(
        timeline_df,
        use_container_width=True,
        hide_index=True,
        column_config={
            "期号": st.column_config.TextColumn("期号", width="small"),
            "预测号码": st.column_config.TextColumn("预测号码", width="small"),
            "实际号码": st.column_config.TextColumn("实际号码", width="small"),
            "是否命中": st.column_config.TextColumn("结果", width="small"),
            "置信度": st.column_config.TextColumn("置信度", width="small"),
            "预测时间": st.column_config.DatetimeColumn("预测时间", width="medium")
        }
    )

def confidence_gauge(confidence: float, title: str = "预测置信度") -> None:
    """
    置信度仪表盘组件
    
    Args:
        confidence: 置信度值 (0-1)
        title: 仪表盘标题
    """
    # 创建仪表盘图
    fig = go.Figure(go.Indicator(
        mode = "gauge+number+delta",
        value = confidence * 100,
        domain = {'x': [0, 1], 'y': [0, 1]},
        title = {'text': title},
        delta = {'reference': 50},
        gauge = {
            'axis': {'range': [None, 100]},
            'bar': {'color': "darkblue"},
            'steps': [
                {'range': [0, 30], 'color': "lightgray"},
                {'range': [30, 60], 'color': "yellow"},
                {'range': [60, 100], 'color': "green"}
            ],
            'threshold': {
                'line': {'color': "red", 'width': 4},
                'thickness': 0.75,
                'value': 80
            }
        }
    ))
    
    fig.update_layout(height=300)
    st.plotly_chart(fig, use_container_width=True)

def prediction_comparison_chart(predictions: List[Dict[str, Any]]) -> None:
    """
    预测对比图表组件
    
    Args:
        predictions: 多个预测结果的对比数据
    """
    if len(predictions) < 2:
        st.info("📊 需要至少2个预测结果进行对比")
        return
    
    # 创建对比数据
    comparison_data = []
    for i, pred in enumerate(predictions):
        comparison_data.append({
            "预测方法": pred['method'],
            "号码": pred['number'],
            "置信度": pred['confidence'],
            "序号": i + 1
        })
    
    df = pd.DataFrame(comparison_data)
    
    # 创建对比图表
    fig = px.bar(
        df,
        x='预测方法',
        y='置信度',
        color='号码',
        title='预测方法对比',
        text='号码',
        hover_data=['置信度']
    )
    
    fig.update_traces(textposition='outside')
    fig.update_layout(height=400)
    
    st.plotly_chart(fig, use_container_width=True)

def export_prediction_report(prediction_data: Dict[str, Any]) -> str:
    """
    导出预测报告组件
    
    Args:
        prediction_data: 预测数据
        
    Returns:
        报告内容字符串
    """
    report = f"""
# 福彩3D预测报告

## 📊 预测摘要
- **推荐号码**: {prediction_data['best_prediction']['number']}
- **预测置信度**: {prediction_data['best_prediction']['confidence']:.1%}
- **融合方法**: {prediction_data['best_prediction']['fusion_method']}
- **推荐等级**: {prediction_data['best_prediction']['recommendation_level']}
- **生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 🏆 候选排行榜
"""
    
    for i, item in enumerate(prediction_data['ranking_list'][:10], 1):
        report += f"{i}. {item['number']} - 置信度: {item['confidence']:.1%} - {item['recommendation_level']}\n"
    
    report += f"""
## 🔧 技术详情
- **数据窗口**: {prediction_data['prediction_metadata']['data_window_size']} 期
- **候选总数**: {prediction_data['prediction_metadata']['total_candidates']} 个
- **融合策略**: {prediction_data['prediction_metadata']['fusion_strategy']}

## ⚖️ 模型权重
"""
    
    for model, weight in prediction_data['prediction_metadata']['model_weights'].items():
        report += f"- {model}: {weight:.1%}\n"
    
    report += "\n---\n*本报告由福彩3D智能预测系统生成*"
    
    return report
