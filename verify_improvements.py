#!/usr/bin/env python3
"""
改进验证脚本
验证所有优化措施的效果，确保系统达到预期目标
"""

import os
import sys
import sqlite3
import logging
import requests
import time
from datetime import datetime
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImprovementVerifier:
    """改进验证器"""
    
    def __init__(self):
        self.verification_results = {}
        self.api_url = "http://127.0.0.1:8888"
        self.streamlit_url = "http://127.0.0.1:8501"
    
    def verify_database_fixes(self) -> bool:
        """验证数据库修复"""
        logger.info("🗄️ 验证数据库结构修复...")
        
        try:
            # 连接数据库
            db_path = "data/bug_detection.db"
            if not os.path.exists(db_path):
                logger.error(f"❌ 数据库文件不存在: {db_path}")
                return False
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute("PRAGMA table_info(bug_reports)")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            # 检查必需列
            required_columns = ['environment', 'category', 'priority', 'tags', 'source']
            missing_columns = [col for col in required_columns if col not in column_names]
            
            if missing_columns:
                logger.error(f"❌ 仍缺失必需列: {missing_columns}")
                return False
            
            logger.info(f"✅ 数据库结构验证通过，包含 {len(columns)} 列")
            
            # 测试Bug分类统计功能
            try:
                cursor.execute("""
                    SELECT environment, category, COUNT(*) as count 
                    FROM bug_reports 
                    GROUP BY environment, category
                """)
                stats = cursor.fetchall()
                logger.info(f"✅ Bug分类统计功能正常，返回 {len(stats)} 条记录")
            except Exception as e:
                logger.error(f"❌ Bug分类统计功能失败: {e}")
                return False
            
            conn.close()
            self.verification_results['database_fixes'] = True
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库验证失败: {e}")
            self.verification_results['database_fixes'] = False
            return False
    
    def verify_ai_optimizations(self) -> bool:
        """验证AI优化"""
        logger.info("🤖 验证AI模型优化...")
        
        try:
            # 检查模型文件
            models_dir = Path("models")
            if not models_dir.exists():
                logger.warning("⚠️ 模型目录不存在")
                return False
            
            # 检查离线配置
            config_file = models_dir / "offline_config.py"
            if config_file.exists():
                logger.info("✅ 离线配置文件存在")
            else:
                logger.warning("⚠️ 离线配置文件不存在")
            
            # 测试AI功能
            sys.path.insert(0, str(models_dir))
            
            try:
                # 测试TF-IDF
                from sklearn.feature_extraction.text import TfidfVectorizer
                vectorizer = TfidfVectorizer()
                test_texts = ["测试错误1", "测试错误2"]
                tfidf_matrix = vectorizer.fit_transform(test_texts)
                logger.info(f"✅ TF-IDF功能正常: {tfidf_matrix.shape}")
                
                # 测试错误分类
                sys.path.insert(0, "src")
                from bug_detection.ai.nlp.error_classifier import ErrorClassifier
                
                classifier = ErrorClassifier()
                test_error = "TypeError: Cannot read property 'value' of undefined"
                result = classifier.classify_error(test_error)
                
                if result and 'category' in result:
                    logger.info(f"✅ 错误分类功能正常: {result['category']}")
                else:
                    logger.warning("⚠️ 错误分类功能异常")
                
                self.verification_results['ai_optimizations'] = True
                return True
                
            except Exception as e:
                logger.warning(f"⚠️ AI功能测试失败: {e}")
                logger.info("💡 系统将使用规则方法作为回退方案")
                self.verification_results['ai_optimizations'] = False
                return False
                
        except Exception as e:
            logger.error(f"❌ AI优化验证失败: {e}")
            self.verification_results['ai_optimizations'] = False
            return False
    
    def verify_performance_optimizations(self) -> bool:
        """验证性能优化"""
        logger.info("⚡ 验证性能优化...")
        
        try:
            # 检查优化文件
            opt_dir = Path("optimizations")
            if not opt_dir.exists():
                logger.warning("⚠️ 优化目录不存在")
                return False
            
            # 检查单例模式
            singleton_file = opt_dir / "singleton_pattern.py"
            if singleton_file.exists():
                logger.info("✅ 单例模式实现存在")
            else:
                logger.warning("⚠️ 单例模式实现不存在")
            
            # 检查连接池
            pool_file = opt_dir / "connection_pool.py"
            if pool_file.exists():
                logger.info("✅ 连接池优化存在")
            else:
                logger.warning("⚠️ 连接池优化不存在")
            
            # 检查性能监控
            monitor_file = opt_dir / "performance_monitor.py"
            if monitor_file.exists():
                logger.info("✅ 性能监控模块存在")
                
                # 测试性能监控
                sys.path.insert(0, str(opt_dir))
                from performance_monitor import get_performance_monitor
                
                monitor = get_performance_monitor()
                metrics = monitor.get_current_metrics()
                
                if metrics:
                    logger.info(f"✅ 性能监控正常: 内存 {metrics.get('memory_mb', 0):.1f}MB")
                else:
                    logger.warning("⚠️ 性能监控数据为空")
            else:
                logger.warning("⚠️ 性能监控模块不存在")
            
            self.verification_results['performance_optimizations'] = True
            return True
            
        except Exception as e:
            logger.error(f"❌ 性能优化验证失败: {e}")
            self.verification_results['performance_optimizations'] = False
            return False
    
    def verify_service_functionality(self) -> bool:
        """验证服务功能"""
        logger.info("🌐 验证服务功能...")
        
        try:
            # 测试API服务
            try:
                response = requests.get(f"{self.api_url}/health", timeout=10)
                if response.status_code == 200:
                    health_data = response.json()
                    logger.info(f"✅ API服务正常: {health_data.get('status', 'unknown')}")
                else:
                    logger.error(f"❌ API服务异常: {response.status_code}")
                    return False
            except Exception as e:
                logger.error(f"❌ API服务连接失败: {e}")
                return False
            
            # 测试Streamlit服务
            try:
                response = requests.get(self.streamlit_url, timeout=10)
                if response.status_code == 200:
                    logger.info("✅ Streamlit服务正常")
                else:
                    logger.error(f"❌ Streamlit服务异常: {response.status_code}")
                    return False
            except Exception as e:
                logger.error(f"❌ Streamlit服务连接失败: {e}")
                return False
            
            # 测试Bug检测API
            try:
                test_bug_data = {
                    "error_type": "javascript_error",
                    "error_message": "验证测试错误",
                    "page_name": "test_page",
                    "severity": "medium"
                }
                
                response = requests.post(
                    f"{self.api_url}/api/v1/bug-detection/js-error",
                    json=test_bug_data,
                    timeout=10
                )
                
                if response.status_code == 200:
                    logger.info("✅ Bug检测API正常")
                else:
                    logger.warning(f"⚠️ Bug检测API异常: {response.status_code}")
                    
            except Exception as e:
                logger.warning(f"⚠️ Bug检测API测试失败: {e}")
            
            self.verification_results['service_functionality'] = True
            return True
            
        except Exception as e:
            logger.error(f"❌ 服务功能验证失败: {e}")
            self.verification_results['service_functionality'] = False
            return False
    
    def verify_system_stability(self) -> bool:
        """验证系统稳定性"""
        logger.info("🔒 验证系统稳定性...")
        
        try:
            # 连续测试API响应
            response_times = []
            success_count = 0
            test_count = 5
            
            for i in range(test_count):
                try:
                    start_time = time.time()
                    response = requests.get(f"{self.api_url}/health", timeout=5)
                    end_time = time.time()
                    
                    if response.status_code == 200:
                        success_count += 1
                        response_times.append(end_time - start_time)
                    
                    time.sleep(1)  # 间隔1秒
                    
                except Exception as e:
                    logger.warning(f"⚠️ 第{i+1}次测试失败: {e}")
            
            if success_count >= test_count * 0.8:  # 80%成功率
                avg_response_time = sum(response_times) / len(response_times) if response_times else 0
                logger.info(f"✅ 系统稳定性良好: {success_count}/{test_count} 成功")
                logger.info(f"📊 平均响应时间: {avg_response_time:.3f}秒")
                
                self.verification_results['system_stability'] = True
                return True
            else:
                logger.error(f"❌ 系统稳定性不足: {success_count}/{test_count} 成功")
                self.verification_results['system_stability'] = False
                return False
                
        except Exception as e:
            logger.error(f"❌ 系统稳定性验证失败: {e}")
            self.verification_results['system_stability'] = False
            return False
    
    def generate_verification_report(self) -> dict:
        """生成验证报告"""
        logger.info("📊 生成验证报告...")
        
        total_tests = len(self.verification_results)
        passed_tests = sum(self.verification_results.values())
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'success_rate': success_rate,
            'results': self.verification_results,
            'overall_status': 'PASS' if success_rate >= 80 else 'FAIL'
        }
        
        # 保存报告
        report_file = Path("verification_report.json")
        import json
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📋 验证报告已保存: {report_file}")
        return report
    
    def verify_all_improvements(self) -> bool:
        """验证所有改进"""
        logger.info("🚀 开始验证所有改进...")
        logger.info("=" * 60)
        
        # 执行所有验证
        verifications = [
            ("数据库修复", self.verify_database_fixes),
            ("AI优化", self.verify_ai_optimizations),
            ("性能优化", self.verify_performance_optimizations),
            ("服务功能", self.verify_service_functionality),
            ("系统稳定性", self.verify_system_stability)
        ]
        
        for name, verify_func in verifications:
            logger.info(f"🔍 验证 {name}...")
            try:
                result = verify_func()
                status = "✅ 通过" if result else "❌ 失败"
                logger.info(f"   {name}: {status}")
            except Exception as e:
                logger.error(f"   {name}: ❌ 异常 - {e}")
                self.verification_results[name.lower().replace(' ', '_')] = False
        
        # 生成报告
        report = self.generate_verification_report()
        
        logger.info("=" * 60)
        logger.info(f"📊 验证完成: {report['passed_tests']}/{report['total_tests']} 通过")
        logger.info(f"🎯 成功率: {report['success_rate']:.1f}%")
        logger.info(f"🏆 总体状态: {report['overall_status']}")
        
        return report['overall_status'] == 'PASS'

def main():
    """主函数"""
    logger.info("🔍 改进验证工具")
    logger.info("=" * 60)
    
    verifier = ImprovementVerifier()
    
    success = verifier.verify_all_improvements()
    
    if success:
        print("\\n🎉 所有改进验证通过！")
        print("✅ AI智能Bug检测系统已达到生产就绪状态")
        print("\\n📋 系统状态:")
        print("- 数据库结构完整")
        print("- AI功能正常运行")
        print("- 性能优化生效")
        print("- 服务稳定可用")
    else:
        print("\\n⚠️ 部分改进验证失败")
        print("请检查verification_report.json了解详细信息")
        print("建议重新执行相应的优化脚本")
    
    return success

if __name__ == "__main__":
    main()
