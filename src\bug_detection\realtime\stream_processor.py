#!/usr/bin/env python3
"""
流式事件处理器
实现异步事件处理、事件过滤和转换、批量处理优化
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, field
from collections import defaultdict, deque
from enum import Enum
import json

from .event_bus import Event, EventType, EventPriority, event_bus

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProcessingMode(Enum):
    """处理模式"""
    REAL_TIME = "real_time"      # 实时处理
    BATCH = "batch"              # 批量处理
    SLIDING_WINDOW = "sliding_window"  # 滑动窗口

@dataclass
class ProcessingRule:
    """处理规则"""
    name: str
    event_types: List[EventType]
    filter_func: Optional[Callable[[Event], bool]] = None
    transform_func: Optional[Callable[[Event], Event]] = None
    priority_threshold: Optional[EventPriority] = None
    rate_limit: Optional[int] = None  # 每秒最大处理数
    batch_size: Optional[int] = None
    window_size: Optional[int] = None  # 窗口大小（秒）
    enabled: bool = True

@dataclass
class ProcessingStats:
    """处理统计"""
    total_processed: int = 0
    successful: int = 0
    filtered: int = 0
    errors: int = 0
    avg_processing_time: float = 0.0
    last_processed: Optional[float] = None
    processing_times: deque = field(default_factory=lambda: deque(maxlen=100))

class StreamProcessor:
    """流式事件处理器"""
    
    def __init__(self):
        # 处理规则
        self.rules: Dict[str, ProcessingRule] = {}
        
        # 处理器函数
        self.processors: Dict[str, Callable[[List[Event]], None]] = {}
        
        # 事件缓冲区
        self.event_buffers: Dict[str, List[Event]] = defaultdict(list)
        
        # 处理统计
        self.stats: Dict[str, ProcessingStats] = defaultdict(ProcessingStats)
        
        # 速率限制
        self.rate_limiters: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # 配置
        self.max_buffer_size = 10000
        self.processing_interval = 1.0  # 处理间隔（秒）
        self.cleanup_interval = 300  # 清理间隔（秒）
        
        # 运行状态
        self.running = False
        self.processing_tasks: List[asyncio.Task] = []
        self.cleanup_task: Optional[asyncio.Task] = None
    
    def add_rule(self, rule: ProcessingRule):
        """添加处理规则"""
        self.rules[rule.name] = rule
        self.stats[rule.name] = ProcessingStats()
        logger.info(f"📋 添加处理规则: {rule.name}")
    
    def remove_rule(self, rule_name: str):
        """移除处理规则"""
        if rule_name in self.rules:
            del self.rules[rule_name]
            if rule_name in self.stats:
                del self.stats[rule_name]
            logger.info(f"🗑️ 移除处理规则: {rule_name}")
    
    def register_processor(self, rule_name: str, processor_func: Callable[[List[Event]], None]):
        """注册处理器函数"""
        self.processors[rule_name] = processor_func
        logger.info(f"🔧 注册处理器: {rule_name}")
    
    async def process_event(self, event: Event):
        """处理单个事件"""
        try:
            # 遍历所有规则
            for rule_name, rule in self.rules.items():
                if not rule.enabled:
                    continue
                
                # 检查事件类型匹配
                if event.type not in rule.event_types:
                    continue
                
                # 应用过滤器
                if rule.filter_func and not rule.filter_func(event):
                    self.stats[rule_name].filtered += 1
                    continue
                
                # 检查优先级阈值
                if rule.priority_threshold and event.priority.value < rule.priority_threshold.value:
                    self.stats[rule_name].filtered += 1
                    continue
                
                # 检查速率限制
                if rule.rate_limit and not self._check_rate_limit(rule_name, rule.rate_limit):
                    self.stats[rule_name].filtered += 1
                    continue
                
                # 应用转换
                processed_event = event
                if rule.transform_func:
                    processed_event = rule.transform_func(event)
                
                # 添加到缓冲区
                await self._add_to_buffer(rule_name, processed_event, rule)
                
        except Exception as e:
            logger.error(f"❌ 事件处理失败: {e}")
    
    async def _add_to_buffer(self, rule_name: str, event: Event, rule: ProcessingRule):
        """添加事件到缓冲区"""
        try:
            buffer = self.event_buffers[rule_name]
            
            # 检查缓冲区大小
            if len(buffer) >= self.max_buffer_size:
                logger.warning(f"⚠️ 缓冲区已满，丢弃旧事件: {rule_name}")
                buffer.pop(0)
            
            buffer.append(event)
            
            # 检查是否需要立即处理
            should_process = False
            
            if rule.batch_size and len(buffer) >= rule.batch_size:
                should_process = True
            elif event.priority in [EventPriority.HIGH, EventPriority.CRITICAL]:
                should_process = True
            
            if should_process:
                await self._process_buffer(rule_name, rule)
                
        except Exception as e:
            logger.error(f"❌ 缓冲区操作失败: {e}")
    
    async def _process_buffer(self, rule_name: str, rule: ProcessingRule):
        """处理缓冲区中的事件"""
        try:
            buffer = self.event_buffers[rule_name]
            if not buffer:
                return
            
            # 获取要处理的事件
            events_to_process = []
            
            if rule.batch_size:
                events_to_process = buffer[:rule.batch_size]
                self.event_buffers[rule_name] = buffer[rule.batch_size:]
            else:
                events_to_process = buffer.copy()
                self.event_buffers[rule_name] = []
            
            if not events_to_process:
                return
            
            # 记录处理开始时间
            start_time = time.time()
            
            # 调用处理器
            if rule_name in self.processors:
                processor = self.processors[rule_name]
                
                if asyncio.iscoroutinefunction(processor):
                    await processor(events_to_process)
                else:
                    processor(events_to_process)
                
                # 更新统计
                processing_time = time.time() - start_time
                stats = self.stats[rule_name]
                stats.total_processed += len(events_to_process)
                stats.successful += len(events_to_process)
                stats.last_processed = time.time()
                stats.processing_times.append(processing_time)
                
                # 计算平均处理时间
                if stats.processing_times:
                    stats.avg_processing_time = sum(stats.processing_times) / len(stats.processing_times)
                
                logger.debug(f"✅ 处理完成: {rule_name}, 事件数: {len(events_to_process)}, 耗时: {processing_time:.3f}s")
            else:
                logger.warning(f"⚠️ 未找到处理器: {rule_name}")
                
        except Exception as e:
            logger.error(f"❌ 缓冲区处理失败 {rule_name}: {e}")
            self.stats[rule_name].errors += 1
    
    def _check_rate_limit(self, rule_name: str, rate_limit: int) -> bool:
        """检查速率限制"""
        current_time = time.time()
        rate_limiter = self.rate_limiters[rule_name]
        
        # 清理过期记录
        while rate_limiter and current_time - rate_limiter[0] > 1.0:
            rate_limiter.popleft()
        
        # 检查是否超过限制
        if len(rate_limiter) >= rate_limit:
            return False
        
        # 记录当前时间
        rate_limiter.append(current_time)
        return True
    
    async def start(self):
        """启动流式处理器"""
        if self.running:
            return
        
        self.running = True
        
        # 订阅所有事件类型
        for event_type in EventType:
            await event_bus.subscribe(event_type, self.process_event)
        
        # 启动处理任务
        self.processing_tasks = [
            asyncio.create_task(self._processing_loop()),
            asyncio.create_task(self._sliding_window_loop())
        ]
        
        # 启动清理任务
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        logger.info("🚀 流式事件处理器已启动")
    
    async def stop(self):
        """停止流式处理器"""
        self.running = False
        
        # 取消所有任务
        for task in self.processing_tasks:
            task.cancel()
        
        if self.cleanup_task:
            self.cleanup_task.cancel()
        
        # 等待任务完成
        all_tasks = self.processing_tasks + ([self.cleanup_task] if self.cleanup_task else [])
        if all_tasks:
            await asyncio.gather(*all_tasks, return_exceptions=True)
        
        # 处理剩余缓冲区
        await self._flush_all_buffers()
        
        logger.info("🛑 流式事件处理器已停止")
    
    async def _processing_loop(self):
        """处理循环"""
        while self.running:
            try:
                # 处理所有缓冲区
                for rule_name, rule in self.rules.items():
                    if rule.enabled and self.event_buffers[rule_name]:
                        await self._process_buffer(rule_name, rule)
                
                await asyncio.sleep(self.processing_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ 处理循环失败: {e}")
                await asyncio.sleep(self.processing_interval)
    
    async def _sliding_window_loop(self):
        """滑动窗口处理循环"""
        while self.running:
            try:
                current_time = time.time()
                
                for rule_name, rule in self.rules.items():
                    if not rule.enabled or not rule.window_size:
                        continue
                    
                    buffer = self.event_buffers[rule_name]
                    if not buffer:
                        continue
                    
                    # 过滤窗口内的事件
                    window_start = current_time - rule.window_size
                    window_events = [
                        event for event in buffer 
                        if event.timestamp >= window_start
                    ]
                    
                    if window_events and len(window_events) != len(buffer):
                        # 更新缓冲区
                        self.event_buffers[rule_name] = window_events
                        
                        # 处理窗口事件
                        if rule_name in self.processors:
                            processor = self.processors[rule_name]
                            if asyncio.iscoroutinefunction(processor):
                                await processor(window_events)
                            else:
                                processor(window_events)
                
                await asyncio.sleep(60)  # 每分钟检查一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ 滑动窗口处理失败: {e}")
                await asyncio.sleep(60)
    
    async def _cleanup_loop(self):
        """清理循环"""
        while self.running:
            try:
                await self._cleanup_old_data()
                await asyncio.sleep(self.cleanup_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ 清理任务失败: {e}")
                await asyncio.sleep(self.cleanup_interval)
    
    async def _cleanup_old_data(self):
        """清理过期数据"""
        current_time = time.time()
        
        # 清理速率限制记录
        for rule_name, rate_limiter in self.rate_limiters.items():
            while rate_limiter and current_time - rate_limiter[0] > 60:
                rate_limiter.popleft()
        
        # 清理过期事件缓冲区
        for rule_name, buffer in self.event_buffers.items():
            if not buffer:
                continue
            
            # 保留最近1小时的事件
            cutoff_time = current_time - 3600
            self.event_buffers[rule_name] = [
                event for event in buffer 
                if event.timestamp >= cutoff_time
            ]
    
    async def _flush_all_buffers(self):
        """刷新所有缓冲区"""
        for rule_name, rule in self.rules.items():
            if self.event_buffers[rule_name]:
                await self._process_buffer(rule_name, rule)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取处理统计"""
        return {
            rule_name: {
                'total_processed': stats.total_processed,
                'successful': stats.successful,
                'filtered': stats.filtered,
                'errors': stats.errors,
                'avg_processing_time': stats.avg_processing_time,
                'last_processed': stats.last_processed,
                'buffer_size': len(self.event_buffers[rule_name])
            }
            for rule_name, stats in self.stats.items()
        }
    
    def get_buffer_status(self) -> Dict[str, int]:
        """获取缓冲区状态"""
        return {
            rule_name: len(buffer)
            for rule_name, buffer in self.event_buffers.items()
        }

# 全局流式处理器实例
stream_processor = StreamProcessor()

# 便捷函数
def add_processing_rule(name: str, 
                       event_types: List[EventType],
                       processor_func: Callable[[List[Event]], None],
                       **kwargs):
    """添加处理规则的便捷函数"""
    rule = ProcessingRule(name=name, event_types=event_types, **kwargs)
    stream_processor.add_rule(rule)
    stream_processor.register_processor(name, processor_func)

# 初始化函数
async def initialize_stream_processor():
    """初始化流式处理器"""
    await stream_processor.start()
    logger.info("✅ 流式事件处理器初始化完成")
    return stream_processor

if __name__ == "__main__":
    # 测试代码
    async def test_stream_processor():
        # 初始化
        await initialize_stream_processor()
        
        # 添加测试规则
        def test_processor(events: List[Event]):
            print(f"🔄 处理了 {len(events)} 个事件")
        
        add_processing_rule(
            "test_rule",
            [EventType.JAVASCRIPT_ERROR],
            test_processor,
            batch_size=5
        )
        
        # 等待处理
        await asyncio.sleep(10)
        
        # 停止
        await stream_processor.stop()
    
    asyncio.run(test_stream_processor())
