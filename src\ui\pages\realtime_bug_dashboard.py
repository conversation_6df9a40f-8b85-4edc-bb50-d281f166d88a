#!/usr/bin/env python3
"""
实时Bug检测仪表板
创建日期: 2025年7月24日
用途: 实现实时Bug流显示、交互式性能图表、实时告警通知
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import streamlit as st
from plotly.subplots import make_subplots

# 页面配置
st.set_page_config(
    page_title="实时Bug检测仪表板",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

def load_realtime_data():
    """加载实时数据"""
    try:
        # 导入数据库管理器
        from src.bug_detection.core.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 获取实时统计
        stats = db_manager.get_realtime_stats()
        
        # 获取最近的事件
        recent_events = db_manager.get_realtime_events(limit=50)
        
        # 获取异常告警
        alerts = db_manager.get_anomaly_alerts(limit=20, acknowledged=False)
        
        # 获取Bug模式
        patterns = db_manager.get_bug_patterns()
        
        return {
            'stats': stats,
            'events': recent_events,
            'alerts': alerts,
            'patterns': patterns
        }
        
    except Exception as e:
        st.error(f"加载实时数据失败: {e}")
        return {
            'stats': {},
            'events': [],
            'alerts': [],
            'patterns': []
        }

def create_real_time_metrics_cards(stats: Dict[str, Any]):
    """创建实时指标卡片"""
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        events_count = sum(stats.get('events_last_hour', {}).values())
        st.metric(
            label="🔥 最近1小时事件",
            value=events_count,
            delta=f"+{events_count // 10}" if events_count > 0 else "0"
        )
    
    with col2:
        alerts_count = sum(stats.get('unacknowledged_alerts', {}).values())
        st.metric(
            label="🚨 未确认告警",
            value=alerts_count,
            delta=f"+{alerts_count}" if alerts_count > 0 else "0",
            delta_color="inverse"
        )
    
    with col3:
        patterns_info = stats.get('bug_patterns', {})
        total_patterns = patterns_info.get('total_patterns', 0)
        st.metric(
            label="🧩 Bug模式",
            value=total_patterns,
            delta=f"平均置信度: {patterns_info.get('avg_confidence', 0):.2f}"
        )
    
    with col4:
        max_frequency = patterns_info.get('max_frequency', 0)
        st.metric(
            label="📊 最高频率",
            value=max_frequency,
            delta="次/小时"
        )

def create_event_timeline_chart(events: List[Dict[str, Any]]):
    """创建事件时间线图表"""
    if not events:
        st.info("暂无事件数据")
        return
    
    # 准备数据
    df_events = pd.DataFrame(events)
    df_events['timestamp'] = pd.to_datetime(df_events['timestamp'], unit='s')
    
    # 按事件类型分组
    event_counts = df_events.groupby(['timestamp', 'event_type']).size().reset_index(name='count')
    
    # 创建时间线图表
    fig = px.line(
        event_counts,
        x='timestamp',
        y='count',
        color='event_type',
        title="📈 实时事件时间线",
        labels={'timestamp': '时间', 'count': '事件数量', 'event_type': '事件类型'}
    )
    
    fig.update_layout(
        height=400,
        showlegend=True,
        hovermode='x unified'
    )
    
    st.plotly_chart(fig, use_container_width=True)

def create_alert_severity_chart(alerts: List[Dict[str, Any]]):
    """创建告警严重程度图表"""
    if not alerts:
        st.info("暂无告警数据")
        return
    
    # 统计告警严重程度
    severity_counts = {}
    for alert in alerts:
        severity = alert.get('severity', 'unknown')
        severity_counts[severity] = severity_counts.get(severity, 0) + 1
    
    # 创建饼图
    fig = go.Figure(data=[
        go.Pie(
            labels=list(severity_counts.keys()),
            values=list(severity_counts.values()),
            hole=0.4,
            textinfo='label+percent',
            textposition='outside'
        )
    ])
    
    fig.update_layout(
        title="🚨 告警严重程度分布",
        height=400,
        showlegend=True
    )
    
    st.plotly_chart(fig, use_container_width=True)

def create_bug_patterns_chart(patterns: List[Dict[str, Any]]):
    """创建Bug模式图表"""
    if not patterns:
        st.info("暂无Bug模式数据")
        return
    
    # 准备数据
    df_patterns = pd.DataFrame(patterns)
    
    # 创建散点图：频率 vs 置信度
    fig = px.scatter(
        df_patterns,
        x='frequency',
        y='confidence',
        size='severity_score',
        color='pattern_type',
        hover_name='name',
        title="🧩 Bug模式分析",
        labels={
            'frequency': '频率',
            'confidence': '置信度',
            'severity_score': '严重程度',
            'pattern_type': '模式类型'
        }
    )
    
    fig.update_layout(
        height=400,
        showlegend=True
    )
    
    st.plotly_chart(fig, use_container_width=True)

def create_real_time_event_stream(events: List[Dict[str, Any]]):
    """创建实时事件流"""
    st.subheader("📡 实时事件流")
    
    if not events:
        st.info("暂无实时事件")
        return
    
    # 创建事件流容器
    event_container = st.container()
    
    with event_container:
        # 显示最近的10个事件
        for event in events[:10]:
            event_time = datetime.fromtimestamp(event.get('timestamp', 0))
            event_type = event.get('event_type', 'unknown')
            event_data = event.get('data', {})
            
            # 根据事件类型选择图标和颜色
            if event_type == 'javascript_error':
                icon = "🐛"
                color = "red"
            elif event_type == 'api_performance':
                icon = "⚡"
                color = "blue"
            elif event_type == 'alert_triggered':
                icon = "🚨"
                color = "orange"
            else:
                icon = "📊"
                color = "gray"
            
            # 显示事件
            with st.expander(f"{icon} {event_type} - {event_time.strftime('%H:%M:%S')}", expanded=False):
                col1, col2 = st.columns([1, 3])
                
                with col1:
                    st.write("**事件ID:**", event.get('id', 'N/A'))
                    st.write("**来源:**", event.get('source', 'N/A'))
                    st.write("**优先级:**", event.get('priority', 'N/A'))
                
                with col2:
                    st.write("**事件数据:**")
                    st.json(event_data)

def create_alert_management_panel(alerts: List[Dict[str, Any]]):
    """创建告警管理面板"""
    st.subheader("🚨 告警管理")
    
    if not alerts:
        st.success("🎉 当前无未确认告警")
        return
    
    # 显示告警列表
    for i, alert in enumerate(alerts):
        alert_time = datetime.fromtimestamp(alert.get('timestamp', 0))
        severity = alert.get('severity', 'unknown')
        message = alert.get('message', 'No message')
        
        # 根据严重程度选择颜色
        if severity == 'critical':
            alert_type = "error"
        elif severity == 'warning':
            alert_type = "warning"
        else:
            alert_type = "info"
        
        with st.container():
            col1, col2, col3 = st.columns([6, 2, 2])
            
            with col1:
                getattr(st, alert_type)(f"**{severity.upper()}**: {message}")
                st.caption(f"时间: {alert_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            with col2:
                st.write(f"**置信度**: {alert.get('confidence', 0):.2f}")
            
            with col3:
                if st.button(f"确认", key=f"ack_{i}"):
                    # 这里可以添加确认告警的逻辑
                    st.success("告警已确认")
                    st.rerun()

def create_performance_monitoring_panel():
    """创建性能监控面板"""
    st.subheader("📊 性能监控")
    
    try:
        # 模拟性能数据
        current_time = time.time()
        time_points = [current_time - i * 60 for i in range(30, 0, -1)]  # 最近30分钟
        
        # 生成模拟数据
        response_times = [0.1 + 0.05 * (i % 5) for i in range(30)]
        error_rates = [0.01 + 0.02 * (i % 3) for i in range(30)]
        throughput = [100 + 20 * (i % 4) for i in range(30)]
        
        # 创建子图
        fig = make_subplots(
            rows=3, cols=1,
            subplot_titles=('响应时间 (秒)', '错误率 (%)', '吞吐量 (请求/分钟)'),
            vertical_spacing=0.1
        )
        
        # 添加响应时间图
        fig.add_trace(
            go.Scatter(
                x=[datetime.fromtimestamp(t) for t in time_points],
                y=response_times,
                mode='lines+markers',
                name='响应时间',
                line=dict(color='blue')
            ),
            row=1, col=1
        )
        
        # 添加错误率图
        fig.add_trace(
            go.Scatter(
                x=[datetime.fromtimestamp(t) for t in time_points],
                y=[r * 100 for r in error_rates],
                mode='lines+markers',
                name='错误率',
                line=dict(color='red')
            ),
            row=2, col=1
        )
        
        # 添加吞吐量图
        fig.add_trace(
            go.Scatter(
                x=[datetime.fromtimestamp(t) for t in time_points],
                y=throughput,
                mode='lines+markers',
                name='吞吐量',
                line=dict(color='green')
            ),
            row=3, col=1
        )
        
        fig.update_layout(
            height=600,
            showlegend=False,
            title_text="系统性能监控"
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
    except Exception as e:
        st.error(f"性能监控面板加载失败: {e}")

def main():
    """主函数"""
    st.title("🔍 实时Bug检测仪表板")
    st.markdown("---")
    
    # 添加自动刷新控制
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        st.markdown("### 📊 实时监控中心")
    
    with col2:
        auto_refresh = st.checkbox("自动刷新", value=True)
    
    with col3:
        if st.button("🔄 手动刷新"):
            st.rerun()
    
    # 加载实时数据
    with st.spinner("加载实时数据..."):
        data = load_realtime_data()
    
    # 显示实时指标卡片
    create_real_time_metrics_cards(data['stats'])
    
    st.markdown("---")
    
    # 创建主要内容区域
    tab1, tab2, tab3, tab4 = st.tabs(["📈 事件监控", "🚨 告警管理", "🧩 模式分析", "📊 性能监控"])
    
    with tab1:
        col1, col2 = st.columns([2, 1])
        
        with col1:
            create_event_timeline_chart(data['events'])
        
        with col2:
            create_real_time_event_stream(data['events'])
    
    with tab2:
        col1, col2 = st.columns([1, 1])
        
        with col1:
            create_alert_management_panel(data['alerts'])
        
        with col2:
            create_alert_severity_chart(data['alerts'])
    
    with tab3:
        create_bug_patterns_chart(data['patterns'])
    
    with tab4:
        create_performance_monitoring_panel()
    
    # 自动刷新逻辑
    if auto_refresh:
        time.sleep(5)  # 5秒后刷新
        st.rerun()

if __name__ == "__main__":
    main()
