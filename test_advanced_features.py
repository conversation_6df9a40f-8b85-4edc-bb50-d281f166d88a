"""
测试高级特征工程模块
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from prediction.advanced_features import AdvancedFeatureExtractor

def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试高级特征工程模块 ===")
    
    # 创建测试数据
    test_data = [
        '123', '456', '789', '012', '345', 
        '678', '901', '234', '567', '890',
        '135', '246', '357', '468', '579',
        '024', '135', '246', '357', '468'
    ]
    
    print(f"测试数据: {len(test_data)} 条记录")
    print(f"示例数据: {test_data[:5]}")
    
    # 创建特征提取器
    extractor = AdvancedFeatureExtractor()
    
    # 提取特征
    print("\n开始提取高级特征...")
    features = extractor.extract_all_features(test_data)
    
    print(f"\n成功提取 {len(features)} 个高级特征:")
    
    # 按类别显示特征
    feature_categories = {
        'wavelet': [],
        'fractal': [],
        'chaos': [],
        'phase': [],
        'time_series': []
    }
    
    for key in features.keys():
        if 'wavelet' in key or 'spectral' in key:
            feature_categories['wavelet'].append(key)
        elif 'hurst' in key or 'dfa' in key or 'entropy' in key:
            feature_categories['fractal'].append(key)
        elif 'lyapunov' in key or 'correlation' in key or 'recurrence' in key:
            feature_categories['chaos'].append(key)
        elif 'phase' in key:
            feature_categories['phase'].append(key)
        elif 'autocorr' in key or 'trend' in key or 'seasonality' in key:
            feature_categories['time_series'].append(key)
    
    for category, feature_list in feature_categories.items():
        if feature_list:
            print(f"\n{category.upper()} 特征 ({len(feature_list)} 个):")
            for feature in feature_list[:3]:  # 显示前3个
                value = features[feature]
                print(f"  {feature}: {value:.6f}")
            if len(feature_list) > 3:
                print(f"  ... 还有 {len(feature_list) - 3} 个特征")
    
    # 检查特征值的合理性
    print("\n=== 特征质量检查 ===")
    
    # 检查是否有NaN或无穷大值
    nan_count = sum(1 for v in features.values() if str(v) in ['nan', 'inf', '-inf'])
    print(f"异常值数量: {nan_count}")
    
    # 检查特征值范围
    values = [v for v in features.values() if isinstance(v, (int, float)) and str(v) not in ['nan', 'inf', '-inf']]
    if values:
        print(f"特征值范围: [{min(values):.6f}, {max(values):.6f}]")
        print(f"特征值平均: {sum(values)/len(values):.6f}")
    
    # 检查零值比例
    zero_count = sum(1 for v in features.values() if v == 0.0)
    print(f"零值特征数量: {zero_count} / {len(features)} ({zero_count/len(features)*100:.1f}%)")
    
    return features

def test_with_real_data():
    """使用真实数据测试"""
    print("\n=== 使用真实数据测试 ===")
    
    try:
        # 尝试从数据库获取真实数据
        from core.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        records = db_manager.get_recent_records(100)  # 获取最近100条记录
        
        if records:
            real_data = [record.numbers for record in records]
            print(f"从数据库获取 {len(real_data)} 条真实数据")
            
            extractor = AdvancedFeatureExtractor()
            features = extractor.extract_all_features(real_data)
            
            print(f"真实数据特征提取成功: {len(features)} 个特征")
            
            # 显示一些关键特征
            key_features = [k for k in features.keys() if 'sums_' in k][:5]
            print("和值序列关键特征:")
            for key in key_features:
                print(f"  {key}: {features[key]:.6f}")
                
            return True
        else:
            print("数据库中没有数据，跳过真实数据测试")
            return False
            
    except Exception as e:
        print(f"真实数据测试失败: {e}")
        return False

def main():
    """主测试函数"""
    try:
        # 基本功能测试
        features = test_basic_functionality()
        
        # 真实数据测试
        test_with_real_data()
        
        print("\n=== 测试完成 ===")
        print("高级特征工程模块测试成功!")
        print(f"总共提取了 {len(features)} 个高级特征")
        
        return True
        
    except Exception as e:
        print(f"\n测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
