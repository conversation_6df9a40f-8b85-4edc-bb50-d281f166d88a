#!/usr/bin/env python3
'''
福彩3D预测系统改进项目回滚脚本
自动生成于: 20250725_224702
'''

import os
import shutil
import json
from pathlib import Path

def rollback_project():
    backup_dir = "backup/improvement_project_20250725_224702"
    
    print("🔄 开始回滚项目改进...")
    
    # 回滚数据库
    databases = [
        "data/lottery_data.db",
        "data/bug_detection.db",
        "data/model_library.db",
        "data/scheduler.db",
        "data/unified_predictions.db"
]
    for db in databases:
        if os.path.exists(f"{backup_dir}/{db}"):
            shutil.copy2(f"{backup_dir}/{db}", db)
            print(f"✅ 回滚数据库: {db}")
    
    # 回滚关键文件
    files = [
        "src/ui/main.py",
        "src/core/database.py",
        "src/data/collector.py",
        "src/api/production_main.py",
        "src/bug_detection/realtime/websocket_manager.py",
        "src/ui/pages_disabled"
]
    for file_path in files:
        if os.path.exists(f"{backup_dir}/{file_path}"):
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            shutil.copy2(f"{backup_dir}/{file_path}", file_path)
            print(f"✅ 回滚文件: {file_path}")
    
    # 恢复pages_disabled目录
    pages_disabled_backup = f"{backup_dir}/src/ui/pages_disabled"
    if os.path.exists(pages_disabled_backup):
        if os.path.exists("src/ui/pages_disabled"):
            shutil.rmtree("src/ui/pages_disabled")
        shutil.copytree(pages_disabled_backup, "src/ui/pages_disabled")
        print("✅ 恢复pages_disabled目录")
    
    print("🎉 项目回滚完成!")

if __name__ == "__main__":
    rollback_project()
