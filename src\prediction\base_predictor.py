#!/usr/bin/env python3
"""
预测器基类

定义预测器的通用接口和基础功能
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class BasePredictor(ABC):
    """预测器基类"""
    
    def __init__(self, name: str, description: str = ""):
        """
        初始化预测器
        
        Args:
            name: 预测器名称
            description: 预测器描述
        """
        self.name = name
        self.description = description
        self.is_trained = False
        self.last_training_time = None
        self.model_version = "1.0"
        
        logger.info(f"初始化预测器: {self.name}")
    
    @abstractmethod
    def train(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        训练预测模型
        
        Args:
            data: 训练数据列表，每个元素包含历史记录信息
            
        Returns:
            训练结果信息
        """
        pass
    
    @abstractmethod
    def predict(self, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        执行预测
        
        Args:
            context: 预测上下文信息
            
        Returns:
            预测结果，包含以下字段：
            - predictions: 预测的号码组合
            - confidence: 置信度
            - method: 预测方法
            - details: 详细信息
        """
        pass
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        return {
            "name": self.name,
            "description": self.description,
            "is_trained": self.is_trained,
            "last_training_time": self.last_training_time.isoformat() if self.last_training_time else None,
            "model_version": self.model_version
        }
    
    def validate_data(self, data: List[Dict[str, Any]]) -> bool:
        """
        验证数据格式
        
        Args:
            data: 待验证的数据
            
        Returns:
            数据是否有效
        """
        if not data:
            logger.warning("数据为空")
            return False
        
        required_fields = ['period', 'date', 'numbers']
        
        for record in data:
            if not isinstance(record, dict):
                logger.warning("数据记录不是字典格式")
                return False
            
            for field in required_fields:
                if field not in record:
                    logger.warning(f"缺少必需字段: {field}")
                    return False
        
        logger.info(f"数据验证通过，共 {len(data)} 条记录")
        return True
    
    def extract_features(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        提取特征
        
        Args:
            data: 原始数据
            
        Returns:
            提取的特征
        """
        if not data:
            return {}
        
        features = {
            "total_records": len(data),
            "date_range": {
                "start": data[0].get('date'),
                "end": data[-1].get('date')
            },
            "number_frequency": self._calculate_number_frequency(data),
            "sum_distribution": self._calculate_sum_distribution(data),
            "pattern_analysis": self._analyze_patterns(data)
        }
        
        return features
    
    def _calculate_number_frequency(self, data: List[Dict[str, Any]]) -> Dict[str, int]:
        """计算数字频率"""
        frequency = {}
        
        for record in data:
            numbers = record.get('numbers', '')
            if isinstance(numbers, str) and len(numbers) == 3:
                for digit in numbers:
                    if digit.isdigit():
                        frequency[digit] = frequency.get(digit, 0) + 1
        
        return frequency
    
    def _calculate_sum_distribution(self, data: List[Dict[str, Any]]) -> Dict[str, int]:
        """计算和值分布"""
        sum_distribution = {}
        
        for record in data:
            numbers = record.get('numbers', '')
            if isinstance(numbers, str) and len(numbers) == 3 and numbers.isdigit():
                total = sum(int(digit) for digit in numbers)
                sum_distribution[str(total)] = sum_distribution.get(str(total), 0) + 1
        
        return sum_distribution
    
    def _analyze_patterns(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析号码模式"""
        patterns = {
            "consecutive_count": 0,
            "repeat_count": 0,
            "ascending_count": 0,
            "descending_count": 0
        }
        
        for record in data:
            numbers = record.get('numbers', '')
            if isinstance(numbers, str) and len(numbers) == 3 and numbers.isdigit():
                digits = [int(d) for d in numbers]
                
                # 连号检查
                if self._is_consecutive(digits):
                    patterns["consecutive_count"] += 1
                
                # 重号检查
                if len(set(digits)) < 3:
                    patterns["repeat_count"] += 1
                
                # 升序检查
                if digits == sorted(digits):
                    patterns["ascending_count"] += 1
                
                # 降序检查
                if digits == sorted(digits, reverse=True):
                    patterns["descending_count"] += 1
        
        return patterns
    
    def _is_consecutive(self, digits: List[int]) -> bool:
        """检查是否为连号"""
        sorted_digits = sorted(digits)
        for i in range(len(sorted_digits) - 1):
            if sorted_digits[i+1] - sorted_digits[i] != 1:
                return False
        return True
    
    def calculate_accuracy(self, predictions: List[Dict[str, Any]], 
                          actual_results: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        计算预测准确率
        
        Args:
            predictions: 预测结果列表
            actual_results: 实际结果列表
            
        Returns:
            准确率统计
        """
        if not predictions or not actual_results:
            return {"accuracy": 0.0, "total_predictions": 0}
        
        correct_predictions = 0
        total_predictions = min(len(predictions), len(actual_results))
        
        for i in range(total_predictions):
            pred = predictions[i].get('predictions', [])
            actual = actual_results[i].get('numbers', '')
            
            if actual in pred:
                correct_predictions += 1
        
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0.0
        
        return {
            "accuracy": accuracy,
            "correct_predictions": correct_predictions,
            "total_predictions": total_predictions
        }
