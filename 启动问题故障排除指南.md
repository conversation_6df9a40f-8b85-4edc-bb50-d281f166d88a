# 福彩3D预测系统启动问题故障排除指南

## 🚨 常见启动错误及解决方案

### 1. "启动失败: None" 错误

**问题描述**: 系统显示"启动失败: None"，无法正常启动

**可能原因**:
- API服务未启动或异常停止
- 端口被其他程序占用
- 依赖服务连接失败
- 配置文件问题

**解决方案**:

#### 方案A: 使用一键启动脚本
```bash
# Windows用户
start_system.bat

# 或者使用Python脚本
python start_system.py
```

#### 方案B: 手动分步启动
1. **检查端口占用**
   ```bash
   netstat -ano | findstr :8888
   netstat -ano | findstr :8501
   ```

2. **启动API服务**
   ```bash
   python -m uvicorn src.api.production_main:app --host 127.0.0.1 --port 8888
   ```

3. **验证API服务**
   ```bash
   curl http://127.0.0.1:8888/health
   ```

4. **启动Streamlit界面**
   ```bash
   streamlit run src/ui/main.py --server.port 8501 --server.address 127.0.0.1
   ```

### 2. 端口占用错误

**问题描述**: 提示端口8888或8501已被占用

**解决方案**:
1. **查找占用进程**
   ```bash
   netstat -ano | findstr :8888
   ```

2. **终止占用进程**
   ```bash
   taskkill /pid [进程ID] /f
   ```

3. **或者使用不同端口**
   ```bash
   # API服务使用8889端口
   python -m uvicorn src.api.production_main:app --host 127.0.0.1 --port 8889
   
   # Streamlit使用8502端口
   streamlit run src/ui/main.py --server.port 8502 --server.address 127.0.0.1
   ```

### 3. 模块导入错误

**问题描述**: 提示找不到模块或导入失败

**解决方案**:
1. **检查Python环境**
   ```bash
   python --version
   pip list
   ```

2. **安装缺失依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **检查工作目录**
   ```bash
   # 确保在项目根目录下运行
   cd d:\github\3dyuce
   ```

### 4. 数据库连接错误

**问题描述**: 无法连接到数据库或数据库文件不存在

**解决方案**:
1. **检查数据库文件**
   ```bash
   # 检查数据库文件是否存在
   dir data\lottery_data.db
   ```

2. **重新创建数据库**
   ```bash
   python src/data_collection/data_collector.py
   ```

### 5. 权限错误

**问题描述**: 提示权限不足或访问被拒绝

**解决方案**:
1. **以管理员身份运行**
   - 右键点击命令提示符
   - 选择"以管理员身份运行"

2. **检查文件权限**
   ```bash
   # 确保有读写权限
   icacls data\lottery_data.db
   ```

## 🔧 系统诊断工具

### 快速诊断脚本
创建 `diagnose_system.py`:

```python
import requests
import subprocess
import os

def diagnose_system():
    print("🔍 系统诊断开始...")
    
    # 检查API服务
    try:
        response = requests.get("http://127.0.0.1:8888/health", timeout=5)
        print(f"✅ API服务状态: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   数据库记录: {data.get('database_records', 'N/A')}")
    except Exception as e:
        print(f"❌ API服务异常: {e}")
    
    # 检查Streamlit服务
    try:
        response = requests.get("http://127.0.0.1:8501", timeout=5)
        print(f"✅ Streamlit状态: {response.status_code}")
    except Exception as e:
        print(f"❌ Streamlit异常: {e}")
    
    # 检查数据库文件
    db_path = "data/lottery_data.db"
    if os.path.exists(db_path):
        size = os.path.getsize(db_path)
        print(f"✅ 数据库文件存在: {size} bytes")
    else:
        print("❌ 数据库文件不存在")
    
    print("🔍 系统诊断完成")

if __name__ == "__main__":
    diagnose_system()
```

## 📞 获取帮助

### 日志文件位置
- API服务日志: 控制台输出
- Streamlit日志: 控制台输出
- 系统错误日志: Windows事件查看器

### 常用检查命令
```bash
# 检查Python版本
python --version

# 检查已安装包
pip list

# 检查端口占用
netstat -ano | findstr :8888
netstat -ano | findstr :8501

# 检查进程
tasklist | findstr python
tasklist | findstr streamlit

# 检查网络连接
ping 127.0.0.1
telnet 127.0.0.1 8888
```

### 重置系统
如果所有方法都失败，可以尝试完全重置：

1. **停止所有相关进程**
   ```bash
   taskkill /f /im python.exe
   taskkill /f /im streamlit.exe
   ```

2. **清理临时文件**
   ```bash
   del /q __pycache__\*.*
   rmdir /s __pycache__
   ```

3. **重新安装依赖**
   ```bash
   pip uninstall -r requirements.txt -y
   pip install -r requirements.txt
   ```

4. **使用一键启动脚本**
   ```bash
   python start_system.py
   ```

## ✅ 成功启动的标志

系统正常启动后，您应该看到：

1. **API服务响应**
   ```json
   {
     "status": "healthy",
     "timestamp": "2025-07-17T01:46:56.913058",
     "database_records": 8343,
     "date_range": "2002-01-01 to 2025-07-15"
   }
   ```

2. **Streamlit界面**
   - 浏览器自动打开 http://127.0.0.1:8501
   - 显示福彩3D预测系统主界面
   - 左侧功能导航正常显示

3. **功能正常**
   - 数据更新功能可用
   - 智能融合优化功能可用
   - 所有预测模式正常工作

如果遇到其他问题，请提供具体的错误信息以便进一步诊断。
