"""
预测结果页面重构
重新设计预测结果展示页面，实现准确性导向的用户界面
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import requests
import streamlit as st
from plotly.subplots import make_subplots

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API基础URL
API_BASE_URL = "http://127.0.0.1:8888/api/v1"

def render_prediction_result_page():
    """渲染预测结果页面"""
    st.set_page_config(
        page_title="🎯 福彩3D智能预测",
        page_icon="🎯",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 页面标题
    st.title("🎯 福彩3D智能预测系统")
    st.markdown("### 准确性导向的单一最优预测")
    
    # 侧边栏控制面板
    prediction_settings = render_prediction_control_panel()
    
    # 主要内容区域
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # 预测按钮
        if st.button("🚀 开始预测", type="primary", use_container_width=True):
            with st.spinner("正在进行智能预测分析..."):
                prediction_data = get_prediction_data(prediction_settings)
                
                if prediction_data:
                    # 存储预测数据到session state
                    st.session_state.prediction_data = prediction_data
                    st.success("✅ 预测完成！")
                else:
                    st.error("❌ 预测失败，请稍后重试")
    
    with col2:
        # 显示当前设置
        st.markdown("#### ⚙️ 当前设置")
        st.info(f"""
        📊 排行榜数量: {prediction_settings['ranking_count']}  
        🎯 置信度阈值: {prediction_settings['confidence_threshold']:.1%}  
        📈 数据窗口: {prediction_settings['window_size']} 期  
        """)
    
    # 显示预测结果
    if 'prediction_data' in st.session_state:
        render_prediction_results(st.session_state.prediction_data)

def render_prediction_control_panel() -> Dict[str, Any]:
    """渲染预测控制面板"""
    with st.sidebar:
        st.markdown("### ⚙️ 预测设置")
        
        # 排行榜显示数量
        ranking_count = st.slider(
            "📊 排行榜显示数量",
            min_value=5,
            max_value=20,
            value=10,
            help="设置候选号码排行榜显示的数量"
        )
        
        # 置信度阈值
        confidence_threshold = st.slider(
            "🎯 置信度阈值",
            min_value=0.1,
            max_value=0.9,
            value=0.3,
            step=0.1,
            format="%.1f",
            help="设置预测结果的最低置信度要求"
        )
        
        # 历史数据窗口
        window_size = st.selectbox(
            "📈 历史数据窗口",
            options=[30, 50, 100, 200],
            index=1,
            help="设置用于预测分析的历史数据期数"
        )
        
        # 高级设置
        with st.expander("🔧 高级设置"):
            show_technical_details = st.checkbox("显示技术详情", value=True)
            show_model_performance = st.checkbox("显示模型性能", value=True)
            auto_refresh = st.checkbox("自动刷新", value=False)
            
            if auto_refresh:
                refresh_interval = st.slider("刷新间隔(秒)", 30, 300, 60)
        
        return {
            'ranking_count': ranking_count,
            'confidence_threshold': confidence_threshold,
            'window_size': window_size,
            'show_technical_details': show_technical_details,
            'show_model_performance': show_model_performance,
            'auto_refresh': auto_refresh
        }

def get_prediction_data(settings: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """获取预测数据"""
    try:
        # 构建请求参数
        request_data = {
            "candidate_count": settings['ranking_count'],
            "confidence_threshold": settings['confidence_threshold'],
            "window_size": settings['window_size']
        }
        
        # 调用新的预测API
        response = requests.post(
            f"{API_BASE_URL}/prediction/single-best",
            json=request_data,
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"API调用失败: {response.status_code}")
            return None
            
    except Exception as e:
        logger.error(f"获取预测数据失败: {e}")
        return None

def render_prediction_results(data: Dict[str, Any]):
    """渲染预测结果"""
    
    # 主要推荐区域
    render_best_prediction_section(data['best_prediction'])
    
    st.markdown("---")
    
    # 候选排行榜区域
    render_ranking_section(data['ranking_list'])
    
    st.markdown("---")
    
    # 技术详情和模型性能
    col1, col2 = st.columns(2)
    
    with col1:
        render_technical_details_section(data)
    
    with col2:
        render_model_performance_section(data['model_performance'])

def render_best_prediction_section(best_prediction: Dict[str, Any]):
    """渲染最佳推荐区域"""
    st.markdown("## 🎯 最佳推荐号码")
    
    # 创建三列布局
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        # 大号码显示
        st.markdown(f"""
        <div style="
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            color: white;
            margin: 20px 0;
        ">
            <h1 style="
                font-size: 4rem;
                font-weight: bold;
                margin: 0;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            ">{best_prediction['number']}</h1>
            <p style="font-size: 1.2rem; margin: 10px 0 0 0;">推荐号码</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        # 置信度指标
        confidence_pct = best_prediction['confidence'] * 100
        st.metric(
            "🎯 预测置信度",
            f"{confidence_pct:.1f}%",
            delta=f"+{confidence_pct-50:.1f}%" if confidence_pct > 50 else None
        )
        
        # 推荐等级
        level = best_prediction['recommendation_level']
        level_color = {
            "强烈推荐": "🔴",
            "推荐": "🟠", 
            "可考虑": "🟡",
            "谨慎": "🟢"
        }.get(level, "⚪")
        
        st.metric("📊 推荐等级", f"{level_color} {level}")
    
    with col3:
        # 历史命中率
        hit_rate_pct = best_prediction['historical_hit_rate'] * 100
        st.metric(
            "📈 历史命中率",
            f"{hit_rate_pct:.1f}%",
            delta=f"+{hit_rate_pct-10:.1f}%" if hit_rate_pct > 10 else None
        )
        
        # 融合方法
        st.metric("🔧 融合方法", best_prediction['fusion_method'])
    
    # 预测依据说明
    with st.expander("💡 预测依据详情", expanded=False):
        st.text_area(
            "预测分析说明",
            value=best_prediction['prediction_basis'],
            height=100,
            disabled=True
        )
        
        # 支持模型
        if best_prediction['model_support']:
            st.markdown("**支持模型:**")
            for model in best_prediction['model_support']:
                st.markdown(f"• {model}")

def render_ranking_section(ranking_list: List[Dict[str, Any]]):
    """渲染排行榜区域"""
    st.markdown("## 📋 候选号码排行榜")
    
    if not ranking_list:
        st.warning("暂无排行榜数据")
        return
    
    # 转换为DataFrame
    df = pd.DataFrame(ranking_list)
    
    # 添加样式化的推荐等级
    def style_recommendation_level(level):
        colors = {
            "强烈推荐": "🔴",
            "推荐": "🟠",
            "可考虑": "🟡", 
            "谨慎": "🟢",
            "不推荐": "⚪"
        }
        return f"{colors.get(level, '⚪')} {level}"
    
    # 格式化数据
    display_df = df.copy()
    display_df['置信度'] = (df['confidence'] * 100).round(1).astype(str) + '%'
    display_df['综合评分'] = df['composite_score'].round(3)
    display_df['模型支持'] = df['model_support_count'].astype(str) + '/4'
    display_df['历史命中率'] = (df['historical_hit_rate'] * 100).round(1).astype(str) + '%'
    display_df['推荐等级'] = df['recommendation_level'].apply(style_recommendation_level)
    
    # 选择显示列
    display_columns = {
        'rank': '排名',
        'number': '号码',
        '置信度': '置信度',
        '综合评分': '综合评分',
        '模型支持': '模型支持',
        '历史命中率': '历史命中率',
        '推荐等级': '推荐等级'
    }
    
    final_df = display_df[list(display_columns.keys())].rename(columns=display_columns)
    
    # 显示表格
    st.dataframe(
        final_df,
        use_container_width=True,
        hide_index=True,
        column_config={
            "排名": st.column_config.NumberColumn("排名", width="small"),
            "号码": st.column_config.TextColumn("号码", width="small"),
            "置信度": st.column_config.TextColumn("置信度", width="small"),
            "综合评分": st.column_config.NumberColumn("综合评分", width="small"),
            "模型支持": st.column_config.TextColumn("模型支持", width="small"),
            "历史命中率": st.column_config.TextColumn("历史命中率", width="small"),
            "推荐等级": st.column_config.TextColumn("推荐等级", width="medium")
        }
    )
    
    # 排行榜可视化
    if len(df) >= 5:
        render_ranking_chart(df)

def render_ranking_chart(df: pd.DataFrame):
    """渲染排行榜图表"""
    st.markdown("### 📊 排行榜可视化")
    
    # 创建子图
    fig = make_subplots(
        rows=1, cols=2,
        subplot_titles=('置信度分布', '模型支持度分布'),
        specs=[[{"secondary_y": False}, {"secondary_y": False}]]
    )
    
    # 置信度柱状图
    fig.add_trace(
        go.Bar(
            x=df['number'][:10],
            y=df['confidence'][:10],
            name='置信度',
            marker_color='rgba(102, 126, 234, 0.8)',
            text=[f"{x:.1%}" for x in df['confidence'][:10]],
            textposition='auto'
        ),
        row=1, col=1
    )
    
    # 模型支持度柱状图
    fig.add_trace(
        go.Bar(
            x=df['number'][:10],
            y=df['model_support_count'][:10],
            name='模型支持数',
            marker_color='rgba(118, 75, 162, 0.8)',
            text=df['model_support_count'][:10],
            textposition='auto'
        ),
        row=1, col=2
    )
    
    # 更新布局
    fig.update_layout(
        height=400,
        showlegend=False,
        title_text="Top 10 候选号码分析"
    )
    
    fig.update_xaxes(title_text="号码", row=1, col=1)
    fig.update_xaxes(title_text="号码", row=1, col=2)
    fig.update_yaxes(title_text="置信度", row=1, col=1)
    fig.update_yaxes(title_text="支持模型数", row=1, col=2)
    
    st.plotly_chart(fig, use_container_width=True)

def render_technical_details_section(data: Dict[str, Any]):
    """渲染技术详情区域"""
    st.markdown("### 🔧 技术详情")
    
    metadata = data['prediction_metadata']
    
    with st.expander("融合策略详情", expanded=False):
        st.markdown(f"**融合策略:** {metadata['fusion_strategy']}")
        st.markdown(f"**数据窗口:** {metadata['data_window_size']} 期")
        st.markdown(f"**候选总数:** {metadata['total_candidates']} 个")
        st.markdown(f"**预测时间:** {metadata['prediction_time']}")
        
        # 模型权重分配
        st.markdown("**模型权重分配:**")
        weights_df = pd.DataFrame([
            {"模型": model, "权重": f"{weight:.1%}"}
            for model, weight in metadata['model_weights'].items()
        ])
        st.dataframe(weights_df, hide_index=True, use_container_width=True)

def render_model_performance_section(model_performance: List[Dict[str, Any]]):
    """渲染模型性能区域"""
    st.markdown("### 📈 模型性能")
    
    if not model_performance:
        st.warning("暂无模型性能数据")
        return
    
    # 转换为DataFrame
    perf_df = pd.DataFrame(model_performance)
    
    # 格式化显示
    display_df = perf_df.copy()
    display_df['准确率'] = (perf_df['accuracy_rate'] * 100).round(1).astype(str) + '%'
    display_df['当前权重'] = (perf_df['current_weight'] * 100).round(1).astype(str) + '%'
    
    # 选择显示列
    columns = {
        'model_name': '模型名称',
        '准确率': '准确率',
        'total_predictions': '总预测次数',
        'correct_predictions': '正确次数',
        '当前权重': '当前权重'
    }
    
    final_df = display_df[list(columns.keys())].rename(columns=columns)
    
    st.dataframe(final_df, hide_index=True, use_container_width=True)
    
    # 性能图表
    if len(perf_df) > 1:
        fig = px.bar(
            perf_df,
            x='model_name',
            y='accuracy_rate',
            title='模型准确率对比',
            labels={'model_name': '模型名称', 'accuracy_rate': '准确率'},
            color='accuracy_rate',
            color_continuous_scale='viridis'
        )
        
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)

# 主函数
if __name__ == "__main__":
    render_prediction_result_page()
