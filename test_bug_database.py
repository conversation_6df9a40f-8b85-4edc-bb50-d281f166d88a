#!/usr/bin/env python3
"""
测试Bug检测数据库
"""

import sys
import os
sys.path.insert(0, '.')

from src.bug_detection.core.database_manager import DatabaseManager
import sqlite3

def test_bug_database():
    print("=== 测试Bug检测数据库 ===")
    
    try:
        # 初始化数据库管理器
        db_manager = DatabaseManager()
        print("✅ DatabaseManager 初始化完成")
        
        # 检查数据库文件
        db_path = db_manager.db_path
        print(f"数据库路径: {db_path}")
        print(f"文件存在: {os.path.exists(db_path)}")
        
        # 直接检查数据库表
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查看所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [table[0] for table in cursor.fetchall()]
        print(f"数据库表: {tables}")
        
        # 检查是否有bug_reports表
        if 'bug_reports' in tables:
            print("✅ bug_reports表存在")
            
            # 查看表结构
            cursor.execute("PRAGMA table_info(bug_reports)")
            columns = cursor.fetchall()
            print("表结构:")
            for col in columns:
                print(f"  {col[1]}: {col[2]}")
            
            # 查看数据
            cursor.execute("SELECT COUNT(*) FROM bug_reports")
            count = cursor.fetchone()[0]
            print(f"Bug报告数量: {count}")
            
            if count > 0:
                cursor.execute("SELECT id, error_type, severity, status, created_at FROM bug_reports ORDER BY created_at DESC LIMIT 3")
                bugs = cursor.fetchall()
                print("最新Bug报告:")
                for bug in bugs:
                    print(f"  ID: {bug[0]}, Type: {bug[1]}, Severity: {bug[2]}, Status: {bug[3]}, Created: {bug[4]}")
        else:
            print("❌ bug_reports表不存在")
            print("尝试创建表...")
            db_manager._init_tables()
            print("✅ 表创建完成")
        
        # 检查js_errors表
        if 'js_errors' in tables:
            cursor.execute("SELECT COUNT(*) FROM js_errors")
            js_count = cursor.fetchone()[0]
            print(f"JavaScript错误数量: {js_count}")
            
            if js_count > 0:
                cursor.execute("SELECT * FROM js_errors ORDER BY timestamp DESC LIMIT 3")
                js_errors = cursor.fetchall()
                print("最新JavaScript错误:")
                for error in js_errors:
                    print(f"  {error}")
        
        conn.close()
        
        # 测试Bug报告功能
        print("\n=== 测试Bug报告功能 ===")
        try:
            bugs = db_manager.get_bug_reports(limit=5)
            print(f"获取到 {len(bugs)} 个Bug报告")
            
            for bug in bugs:
                print(f"Bug ID: {bug.get('id', 'N/A')}")
                print(f"  错误类型: {bug.get('error_type', 'N/A')}")
                print(f"  严重程度: {bug.get('severity', 'N/A')}")
                print(f"  状态: {bug.get('status', 'N/A')}")
                print(f"  创建时间: {bug.get('created_at', 'N/A')}")
                print()
                
        except Exception as e:
            print(f"❌ 获取Bug报告失败: {e}")
        
        print("=== 测试完成 ===")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_bug_database()
