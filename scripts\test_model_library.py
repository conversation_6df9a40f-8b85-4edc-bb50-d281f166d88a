#!/usr/bin/env python3
"""
测试模型库功能

验证模型库的核心功能是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.model_library.model_registry import ModelRegistry
from src.model_library.prediction_engine import ModelPredictor
from src.model_library.performance_tracker import PerformanceTracker
from src.model_library.utils.data_utils import LotteryDataLoader
from src.model_library.wrappers import (
    MarkovModelWrapper,
    FusionModelWrapper,
    TrendModelWrapper,
    DeepLearningModelWrapper
)


def test_model_registration():
    """测试模型注册功能"""
    print("🧪 测试模型注册功能...")
    
    try:
        registry = ModelRegistry()
        
        # 创建测试模型
        test_model = MarkovModelWrapper()
        
        # 注册模型
        success = registry.register_model(test_model)
        print(f"  ✅ 模型注册: {'成功' if success else '失败'}")
        
        # 获取模型
        retrieved_model = registry.get_model(test_model.model_id)
        print(f"  ✅ 模型获取: {'成功' if retrieved_model else '失败'}")
        
        # 列出模型
        models = registry.list_models()
        print(f"  ✅ 模型列表: 共 {len(models)} 个模型")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 模型注册测试失败: {e}")
        return False


def test_model_prediction():
    """测试模型预测功能"""
    print("🧪 测试模型预测功能...")
    
    try:
        # 加载测试数据
        data_loader = LotteryDataLoader()
        history = data_loader.load_recent_records(30)
        
        if not history:
            print("  ⚠️ 没有历史数据，跳过预测测试")
            return True
        
        # 创建预测引擎
        predictor = ModelPredictor()
        
        # 测试单模型预测
        try:
            prediction = predictor.predict_single("markov_enhanced", history[:20])
            print(f"  ✅ 单模型预测: 成功 (置信度: {prediction.confidence:.2f})")
        except Exception as e:
            print(f"  ❌ 单模型预测失败: {e}")
        
        # 测试组合预测
        try:
            model_ids = ["markov_enhanced", "intelligent_fusion"]
            combined_prediction = predictor.predict_combined(model_ids, history[:20])
            print(f"  ✅ 组合预测: 成功 (置信度: {combined_prediction.confidence:.2f})")
        except Exception as e:
            print(f"  ❌ 组合预测失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 预测测试失败: {e}")
        return False


def test_performance_tracking():
    """测试性能监控功能"""
    print("🧪 测试性能监控功能...")
    
    try:
        tracker = PerformanceTracker()
        
        # 测试性能获取
        performance = tracker.get_model_performance("markov_enhanced")
        print(f"  ✅ 性能获取: {'成功' if performance else '无数据'}")
        
        # 测试排行榜
        ranking = tracker.get_performance_ranking()
        print(f"  ✅ 性能排行榜: 共 {len(ranking)} 个模型")
        
        # 测试统计信息
        stats = tracker.get_model_statistics("markov_enhanced")
        print(f"  ✅ 统计信息: {'成功' if stats else '无数据'}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 性能监控测试失败: {e}")
        return False


def test_model_wrappers():
    """测试模型包装器"""
    print("🧪 测试模型包装器...")
    
    wrappers = [
        ("马尔可夫模型", MarkovModelWrapper),
        ("融合模型", FusionModelWrapper),
        ("趋势模型", TrendModelWrapper),
        ("深度学习模型", DeepLearningModelWrapper)
    ]
    
    success_count = 0
    
    for name, wrapper_class in wrappers:
        try:
            # 创建包装器实例
            wrapper = wrapper_class()
            
            # 测试基本方法
            info = wrapper.get_info()
            status = wrapper.get_status()
            params = wrapper.get_parameters()
            
            print(f"  ✅ {name}: 创建成功 (状态: {status.status.value})")
            success_count += 1
            
        except Exception as e:
            print(f"  ❌ {name}: 创建失败 - {e}")
    
    print(f"  📊 包装器测试: {success_count}/{len(wrappers)} 成功")
    return success_count == len(wrappers)


def test_data_loading():
    """测试数据加载功能"""
    print("🧪 测试数据加载功能...")
    
    try:
        data_loader = LotteryDataLoader()
        
        # 测试加载所有记录
        all_records = data_loader.load_all_records()
        print(f"  ✅ 加载所有记录: {len(all_records)} 条")
        
        # 测试加载最近记录
        recent_records = data_loader.load_recent_records(7)
        print(f"  ✅ 加载最近记录: {len(recent_records)} 条")
        
        # 测试获取最新期号
        latest_period = data_loader.get_latest_period()
        print(f"  ✅ 最新期号: {latest_period}")
        
        return len(all_records) > 0
        
    except Exception as e:
        print(f"  ❌ 数据加载测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始模型库功能测试...\n")
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("数据加载", test_data_loading()))
    test_results.append(("模型包装器", test_model_wrappers()))
    test_results.append(("模型注册", test_model_registration()))
    test_results.append(("模型预测", test_model_prediction()))
    test_results.append(("性能监控", test_performance_tracking()))
    
    # 汇总测试结果
    print("\n📊 测试结果汇总:")
    success_count = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            success_count += 1
    
    total_tests = len(test_results)
    success_rate = success_count / total_tests * 100
    
    print(f"\n🎯 测试总结:")
    print(f"  - 总测试数: {total_tests}")
    print(f"  - 通过数量: {success_count}")
    print(f"  - 成功率: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print(f"\n🎉 模型库功能测试基本通过！")
        return 0
    else:
        print(f"\n⚠️ 模型库功能存在问题，需要进一步调试")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
