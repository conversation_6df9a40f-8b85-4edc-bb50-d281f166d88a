#!/usr/bin/env python3
"""
清理测试Bug数据的脚本
"""

import sys
import os
sys.path.append('.')

import sqlite3
from src.bug_detection.core.database_manager import DatabaseManager

def main():
    try:
        print("🧹 开始清理测试Bug数据...")
        
        # 创建数据库管理器
        db_manager = DatabaseManager()
        print(f"📁 数据库路径: {db_manager.db_path}")
        
        # 直接使用sqlite3连接
        conn = sqlite3.connect(db_manager.db_path)
        cursor = conn.cursor()
        
        # 查看清理前的数据
        print("\n📊 清理前的数据统计:")
        cursor.execute("SELECT COUNT(*) FROM bug_reports")
        total_before = cursor.fetchone()[0]
        print(f"   总Bug数: {total_before}")
        
        cursor.execute("SELECT error_type, COUNT(*) FROM bug_reports GROUP BY error_type")
        types_before = cursor.fetchall()
        print("   按类型分布:")
        for error_type, count in types_before:
            print(f"     {error_type}: {count}个")
        
        # 执行清理操作
        print("\n🗑️ 执行清理操作...")
        
        # 清理测试相关的Bug报告
        cursor.execute("""
            DELETE FROM bug_reports 
            WHERE error_type IN ('test_error', 'integration_test')
            OR error_message LIKE '%测试%' 
            OR error_message LIKE '%BatchTest%'
            OR error_message LIKE '%TestError%'
            OR page_name LIKE '%bug_test.js%'
            OR page_name LIKE '%batch_test.js%'
            OR page_name = 'test_page'
        """)
        
        deleted_count = cursor.rowcount
        print(f"✅ 已删除 {deleted_count} 条测试数据")
        
        # 提交更改
        conn.commit()
        
        # 查看清理后的数据
        print("\n📊 清理后的数据统计:")
        cursor.execute("SELECT COUNT(*) FROM bug_reports")
        total_after = cursor.fetchone()[0]
        print(f"   总Bug数: {total_after}")
        
        cursor.execute("SELECT error_type, COUNT(*) FROM bug_reports GROUP BY error_type")
        types_after = cursor.fetchall()
        if types_after:
            print("   按类型分布:")
            for error_type, count in types_after:
                print(f"     {error_type}: {count}个")
        else:
            print("   ✨ 所有Bug数据已清理完毕")
        
        # 显示清理摘要
        print(f"\n📋 清理摘要:")
        print(f"   清理前: {total_before} 个Bug")
        print(f"   清理后: {total_after} 个Bug")
        print(f"   已清理: {deleted_count} 个测试Bug")
        print(f"   清理率: {(deleted_count/total_before*100):.1f}%" if total_before > 0 else "   清理率: 0%")
        
        conn.close()
        
        print("\n🎉 测试Bug数据清理完成!")
        
    except Exception as e:
        print(f"❌ 清理测试Bug数据时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
