# 阶段B：数据库结构升级

## 📋 任务概述

**任务ID**: kdK21nPhC3D94pARamPjL9  
**阶段**: B  
**优先级**: 高  
**状态**: 未开始  
**预计工期**: 45分钟  
**前置任务**: 阶段A - 环境准备与验证  
**后续任务**: 阶段C - AI依赖库安装  

## 🎯 任务目标

执行数据库schema升级，添加缺失的environment列和其他AI相关字段，解决数据库结构不匹配问题，为AI功能提供完整的数据支持。

## 📊 子任务详情

### B1：数据库结构分析

**任务ID**: jC9xNsXd5V2pVWei4YQH6x  
**预计时间**: 10分钟  

#### 🔧 执行步骤

1. **连接数据库并分析表结构**
   ```python
   import sqlite3
   
   def analyze_database_structure(db_path):
       conn = sqlite3.connect(db_path)
       cursor = conn.cursor()
       
       # 获取所有表
       cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
       tables = cursor.fetchall()
       
       for table in tables:
           table_name = table[0]
           print(f"\n=== 表: {table_name} ===")
           
           # 获取表结构
           cursor.execute(f"PRAGMA table_info({table_name})")
           columns = cursor.fetchall()
           
           for col in columns:
               print(f"  {col[1]} {col[2]} (默认值: {col[4]})")
       
       conn.close()
   ```

2. **检查缺失的列**
   ```python
   def check_missing_columns():
       required_columns = {
           'bug_reports': [
               'environment', 'category', 'priority', 'tags',
               'source', 'component_name', 'reproduction_steps',
               'system_context', 'user_journey', 'screenshots'
           ]
       }
       
       # 检查每个必需列是否存在
       # 返回缺失列列表
   ```

#### 📁 涉及文件
- `data/bug_detection.db`
- `src/bug_detection/core/database_manager.py` (参考)

#### ✅ 成功标准
- 完整的表结构分析报告
- 缺失列清单
- 现有数据统计

---

### B2：执行数据库升级脚本

**任务ID**: aPKUWYdH36CzLu89XFXwv7  
**预计时间**: 15分钟  

#### 🔧 执行步骤

1. **运行升级脚本**
   ```bash
   python upgrade_database_schema.py
   ```

2. **脚本执行的具体操作**
   ```sql
   -- 添加缺失的列
   ALTER TABLE bug_reports ADD COLUMN environment TEXT DEFAULT 'production';
   ALTER TABLE bug_reports ADD COLUMN category TEXT DEFAULT 'general';
   ALTER TABLE bug_reports ADD COLUMN priority TEXT DEFAULT 'medium';
   ALTER TABLE bug_reports ADD COLUMN tags TEXT;
   ALTER TABLE bug_reports ADD COLUMN source TEXT DEFAULT 'user';
   ALTER TABLE bug_reports ADD COLUMN component_name TEXT;
   ALTER TABLE bug_reports ADD COLUMN reproduction_steps TEXT;
   ALTER TABLE bug_reports ADD COLUMN system_context TEXT;
   ALTER TABLE bug_reports ADD COLUMN user_journey TEXT;
   ALTER TABLE bug_reports ADD COLUMN screenshots TEXT;
   ```

3. **监控执行过程**
   - 检查每个ALTER TABLE语句的执行结果
   - 记录成功/失败的操作
   - 处理可能的错误情况

#### 📁 涉及文件
- `upgrade_database_schema.py` (执行脚本)
- `data/bug_detection.db` (目标数据库)
- 备份文件 (安全保障)

#### 🔧 关键类和方法
- `DatabaseUpgrader.__init__()`
- `DatabaseUpgrader.backup_database()`
- `DatabaseUpgrader.add_missing_columns()`
- `DatabaseUpgrader.check_column_exists()`

#### ✅ 成功标准
- 所有缺失列成功添加
- 无SQL执行错误
- 数据库完整性保持
- 升级日志完整

---

### B3：数据库结构验证

**任务ID**: g851NdSGqbSMpL9SrAaLMf  
**预计时间**: 10分钟  

#### 🔧 执行步骤

1. **验证表结构**
   ```python
   def verify_database_schema():
       conn = sqlite3.connect(db_path)
       cursor = conn.cursor()
       
       # 检查bug_reports表结构
       cursor.execute("PRAGMA table_info(bug_reports)")
       columns = cursor.fetchall()
       
       required_columns = [
           'environment', 'category', 'priority', 'tags',
           'source', 'component_name', 'reproduction_steps',
           'system_context', 'user_journey', 'screenshots'
       ]
       
       column_names = [col[1] for col in columns]
       missing = [col for col in required_columns if col not in column_names]
       
       if missing:
           print(f"❌ 缺失列: {missing}")
           return False
       else:
           print("✅ 所有必需列都存在")
           return True
   ```

2. **检查默认值设置**
   ```python
   def check_default_values():
       # 验证默认值是否正确设置
       expected_defaults = {
           'environment': 'production',
           'category': 'general',
           'priority': 'medium',
           'source': 'user'
       }
       # 检查每个列的默认值
   ```

#### 📁 涉及文件
- `data/bug_detection.db` (验证目标)

#### ✅ 成功标准
- 所有必需列存在
- 默认值正确设置
- 表结构完整
- 无结构异常

---

### B4：数据库操作测试

**任务ID**: 44Sg5gdg2QtUPEGFe5mBKT  
**预计时间**: 10分钟  

#### 🔧 执行步骤

1. **测试插入操作**
   ```python
   def test_insert_operation():
       test_data = {
           'id': 'test_upgrade_001',
           'error_type': 'test_error',
           'severity': 'low',
           'page_name': 'test_page',
           'error_message': '数据库升级测试',
           'environment': 'test',
           'category': 'database',
           'priority': 'low',
           'tags': 'upgrade,test',
           'source': 'upgrade_script'
       }
       
       # 执行插入操作
       cursor.execute("""
           INSERT INTO bug_reports 
           (id, error_type, severity, page_name, error_message, 
            environment, category, priority, tags, source)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
       """, tuple(test_data.values()))
   ```

2. **测试查询操作**
   ```python
   def test_query_operation():
       # 测试包含新字段的查询
       cursor.execute("""
           SELECT environment, category, priority, tags, source 
           FROM bug_reports 
           WHERE id = 'test_upgrade_001'
       """)
       result = cursor.fetchone()
       return result is not None
   ```

3. **测试更新操作**
   ```python
   def test_update_operation():
       # 测试更新新字段
       cursor.execute("""
           UPDATE bug_reports 
           SET environment = 'staging', category = 'updated'
           WHERE id = 'test_upgrade_001'
       """)
   ```

4. **清理测试数据**
   ```python
   def cleanup_test_data():
       cursor.execute("DELETE FROM bug_reports WHERE id = 'test_upgrade_001'")
   ```

#### 📁 涉及文件
- `data/bug_detection.db` (测试目标)

#### ✅ 成功标准
- 插入操作成功
- 查询操作正常
- 更新操作有效
- 删除操作完成
- 测试数据已清理

## 🔄 依赖关系

```mermaid
graph TD
    A[阶段A: 环境准备] --> B1[B1: 数据库结构分析]
    B1 --> B2[B2: 执行数据库升级脚本]
    B2 --> B3[B3: 数据库结构验证]
    B3 --> B4[B4: 数据库操作测试]
    B4 --> C[阶段C: AI依赖库安装]
```

## ⚠️ 风险点与缓解措施

### 🚨 高风险

1. **数据库升级失败**
   - **风险**: 表结构损坏，数据丢失
   - **缓解**: 完整备份，事务回滚
   - **回滚**: 从备份恢复数据库

2. **ALTER TABLE操作失败**
   - **风险**: 部分列添加失败
   - **缓解**: 逐列检查，错误处理
   - **回滚**: 删除已添加的列

### ⚠️ 中风险

1. **默认值设置错误**
   - **风险**: 数据不一致
   - **缓解**: 验证默认值，数据清理
   - **回滚**: 修正默认值

2. **权限问题**
   - **风险**: 无法修改数据库
   - **缓解**: 检查文件权限
   - **回滚**: 修改权限设置

## 📋 验证清单

### ✅ 阶段B完成标准

- [ ] **B1**: 数据库结构分析完成，缺失列清单确认
- [ ] **B2**: 升级脚本执行成功，所有列添加完成
- [ ] **B3**: 数据库结构验证通过，默认值正确
- [ ] **B4**: 数据库操作测试通过，功能正常

### 🔍 质量检查

- [ ] 所有必需列存在
- [ ] 默认值设置正确
- [ ] 数据完整性保持
- [ ] 操作功能正常

## 📊 预期结果

### 🗄️ 升级后的bug_reports表结构

```sql
CREATE TABLE bug_reports (
    id TEXT PRIMARY KEY,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    error_type TEXT NOT NULL,
    severity TEXT DEFAULT 'medium',
    page_name TEXT,
    error_message TEXT,
    stack_trace TEXT,
    environment TEXT DEFAULT 'production',      -- 新增
    category TEXT DEFAULT 'general',            -- 新增
    priority TEXT DEFAULT 'medium',             -- 新增
    tags TEXT,                                  -- 新增
    source TEXT DEFAULT 'user',                 -- 新增
    component_name TEXT,                        -- 新增
    reproduction_steps TEXT,                    -- 新增
    system_context TEXT,                        -- 新增
    user_journey TEXT,                          -- 新增
    screenshots TEXT,                           -- 新增
    status TEXT DEFAULT 'open',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 📈 功能增强

1. **环境区分**: 支持production/staging/development环境
2. **智能分类**: 支持AI驱动的错误分类
3. **优先级管理**: 支持critical/high/medium/low优先级
4. **标签系统**: 支持灵活的标签分类
5. **来源追踪**: 支持user/system/api等来源识别
6. **详细信息**: 支持更丰富的错误上下文

---

**创建时间**: 2025-07-25  
**最后更新**: 2025-07-25  
**版本**: v1.0
