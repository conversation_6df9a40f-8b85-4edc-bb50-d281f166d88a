"""
交互式特征选择器

提供按分类组织的特征选择界面和动态参数配置功能
"""

import asyncio
import time
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Set, Tuple

import numpy as np
import pandas as pd

from .feature_ranking import MultiAlgorithmFeatureRanking


@dataclass
class FeatureConfig:
    """特征配置"""
    name: str
    category: str
    description: str
    parameters: Dict[str, Any]
    enabled: bool = True
    importance_score: float = 0.0


@dataclass
class FeaturePreviewResult:
    """特征预览结果"""
    feature_name: str
    values: np.ndarray
    statistics: Dict[str, float]
    quality_score: float
    extraction_time: float
    error: Optional[str] = None


class InteractiveFeatureSelector:
    """交互式特征选择器"""
    
    def __init__(self):
        self.feature_ranking = MultiAlgorithmFeatureRanking()
        self.available_features = self._initialize_available_features()
        self.selected_features: Set[str] = set()
        self.feature_configs: Dict[str, FeatureConfig] = {}
        self.preview_cache: Dict[str, FeaturePreviewResult] = {}
        self.executor = ThreadPoolExecutor(max_workers=4)
    
    def get_available_feature_categories(self) -> Dict[str, List[str]]:
        """获取所有可用特征分类"""
        return {
            "基础统计特征": [
                "数字频率统计", "和值分析", "跨度分析", "奇偶比例", 
                "大小比例", "质数分析", "余数分析", "AC值分析"
            ],
            "时间序列特征": [
                "滞后相关性", "移动平均", "波动率", "动量指标",
                "趋势分析", "季节性分析", "周期性检测", "自相关分析"
            ],
            "高级数学特征": [
                "小波变换", "分形分析", "混沌特征", "相位同步",
                "熵分析", "复杂度分析", "频域分析", "非线性分析"
            ],
            "创新特征": [
                "试机号关联", "销售额影响", "机器设备偏好",
                "智能融合特征", "形态识别", "模式挖掘", "异常检测"
            ],
            "组合特征": [
                "数字组合模式", "位置关系分析", "间隔分析",
                "重复模式检测", "连号分析", "同尾分析", "镜像分析"
            ]
        }
    
    def _initialize_available_features(self) -> Dict[str, FeatureConfig]:
        """初始化可用特征配置"""
        features = {}
        categories = self.get_available_feature_categories()
        
        for category, feature_list in categories.items():
            for feature_name in feature_list:
                config = FeatureConfig(
                    name=feature_name,
                    category=category,
                    description=self._get_feature_description(feature_name),
                    parameters=self._get_default_parameters(feature_name)
                )
                features[feature_name] = config
        
        return features
    
    def _get_feature_description(self, feature_name: str) -> str:
        """获取特征描述"""
        descriptions = {
            "数字频率统计": "统计每个数字在历史开奖中的出现频率",
            "和值分析": "分析三个数字的和值分布和趋势",
            "跨度分析": "分析最大数字与最小数字的差值",
            "奇偶比例": "分析奇数和偶数的比例关系",
            "大小比例": "分析大数(5-9)和小数(0-4)的比例",
            "质数分析": "分析质数在开奖号码中的分布",
            "滞后相关性": "分析当前期与历史期数的相关性",
            "移动平均": "计算指定窗口的移动平均值",
            "波动率": "计算数据的波动程度",
            "试机号关联": "分析试机号与正式开奖号的关联性",
            "智能融合特征": "多种特征的智能组合",
            # 更多描述...
        }
        return descriptions.get(feature_name, f"{feature_name}的详细分析")
    
    def _get_default_parameters(self, feature_name: str) -> Dict[str, Any]:
        """获取特征的默认参数"""
        default_params = {
            "数字频率统计": {"window_size": 100, "normalize": True},
            "和值分析": {"window_size": 50, "trend_analysis": True},
            "跨度分析": {"window_size": 30, "distribution_analysis": True},
            "滞后相关性": {"max_lag": 10, "correlation_threshold": 0.1},
            "移动平均": {"window_sizes": [5, 10, 20], "center": False},
            "波动率": {"window_size": 20, "method": "std"},
            "试机号关联": {"correlation_method": "pearson", "lag_range": 5},
            "智能融合特征": {"fusion_method": "weighted", "auto_weight": True},
            # 更多参数...
        }
        return default_params.get(feature_name, {"window_size": 20})
    
    def select_features(self, feature_names: List[str]) -> Dict[str, Any]:
        """选择特征"""
        results = {
            "selected": [],
            "invalid": [],
            "warnings": []
        }
        
        for feature_name in feature_names:
            if feature_name in self.available_features:
                self.selected_features.add(feature_name)
                self.feature_configs[feature_name] = self.available_features[feature_name]
                results["selected"].append(feature_name)
            else:
                results["invalid"].append(feature_name)
        
        # 检查特征组合的合理性
        warnings = self._validate_feature_combination()
        results["warnings"] = warnings
        
        return results
    
    def deselect_features(self, feature_names: List[str]) -> Dict[str, Any]:
        """取消选择特征"""
        results = {
            "deselected": [],
            "not_selected": []
        }
        
        for feature_name in feature_names:
            if feature_name in self.selected_features:
                self.selected_features.remove(feature_name)
                if feature_name in self.feature_configs:
                    del self.feature_configs[feature_name]
                results["deselected"].append(feature_name)
            else:
                results["not_selected"].append(feature_name)
        
        return results
    
    def update_feature_parameters(self, feature_name: str, parameters: Dict[str, Any]) -> bool:
        """更新特征参数"""
        if feature_name not in self.selected_features:
            return False
        
        if feature_name in self.feature_configs:
            # 验证参数
            validated_params = self._validate_parameters(feature_name, parameters)
            self.feature_configs[feature_name].parameters.update(validated_params)
            
            # 清除相关缓存
            self._clear_feature_cache(feature_name)
            
            return True
        
        return False
    
    def _validate_parameters(self, feature_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """验证参数"""
        validated = {}
        
        for param_name, param_value in parameters.items():
            # 基本类型验证
            if param_name == "window_size":
                validated[param_name] = max(1, min(int(param_value), 1000))
            elif param_name == "correlation_threshold":
                validated[param_name] = max(0.0, min(float(param_value), 1.0))
            elif param_name == "normalize":
                validated[param_name] = bool(param_value)
            else:
                validated[param_name] = param_value
        
        return validated
    
    def _validate_feature_combination(self) -> List[str]:
        """验证特征组合的合理性"""
        warnings = []
        
        # 检查特征数量
        if len(self.selected_features) > 50:
            warnings.append("选择的特征过多，可能导致过拟合")
        elif len(self.selected_features) < 5:
            warnings.append("选择的特征过少，可能影响预测效果")
        
        # 检查特征类别平衡
        category_counts = {}
        for feature_name in self.selected_features:
            if feature_name in self.available_features:
                category = self.available_features[feature_name].category
                category_counts[category] = category_counts.get(category, 0) + 1
        
        total_features = len(self.selected_features)
        for category, count in category_counts.items():
            ratio = count / total_features
            if ratio > 0.6:
                warnings.append(f"{category}特征占比过高({ratio:.1%})，建议平衡特征类别")
        
        return warnings
    
    async def generate_feature_preview(self, feature_names: List[str], 
                                     sample_data: List[Dict[str, Any]]) -> Dict[str, FeaturePreviewResult]:
        """生成特征预览"""
        if not sample_data:
            return {}
        
        preview_tasks = []
        for feature_name in feature_names:
            if feature_name in self.available_features:
                task = asyncio.create_task(
                    self._extract_single_feature_preview(feature_name, sample_data)
                )
                preview_tasks.append((feature_name, task))
        
        # 等待所有任务完成
        preview_results = {}
        for feature_name, task in preview_tasks:
            try:
                result = await task
                preview_results[feature_name] = result
                # 缓存结果
                self.preview_cache[feature_name] = result
            except Exception as e:
                preview_results[feature_name] = FeaturePreviewResult(
                    feature_name=feature_name,
                    values=np.array([]),
                    statistics={},
                    quality_score=0.0,
                    extraction_time=0.0,
                    error=str(e)
                )
        
        return preview_results
    
    async def _extract_single_feature_preview(self, feature_name: str, 
                                            sample_data: List[Dict[str, Any]]) -> FeaturePreviewResult:
        """提取单个特征的预览"""
        start_time = time.time()
        
        # 检查缓存
        cache_key = f"{feature_name}_{hash(str(sample_data[:10]))}"  # 使用前10条数据作为缓存键
        if cache_key in self.preview_cache:
            return self.preview_cache[cache_key]
        
        try:
            # 模拟特征提取（实际实现中会调用具体的特征提取器）
            feature_values = await self._simulate_feature_extraction(feature_name, sample_data)
            
            # 计算统计信息
            statistics = self._calculate_feature_statistics(feature_values)
            
            # 评估特征质量
            quality_score = self._assess_feature_quality(feature_values)
            
            extraction_time = time.time() - start_time
            
            return FeaturePreviewResult(
                feature_name=feature_name,
                values=feature_values,
                statistics=statistics,
                quality_score=quality_score,
                extraction_time=extraction_time
            )
            
        except Exception as e:
            return FeaturePreviewResult(
                feature_name=feature_name,
                values=np.array([]),
                statistics={},
                quality_score=0.0,
                extraction_time=time.time() - start_time,
                error=str(e)
            )
    
    async def _simulate_feature_extraction(self, feature_name: str, 
                                         sample_data: List[Dict[str, Any]]) -> np.ndarray:
        """模拟特征提取（实际实现中会调用具体的特征提取器）"""
        # 这里是简化的模拟实现
        if not sample_data:
            return np.array([])
        
        # 根据特征类型生成模拟数据
        if "频率" in feature_name:
            return np.random.uniform(0, 1, len(sample_data))
        elif "和值" in feature_name:
            return np.random.uniform(0, 27, len(sample_data))
        elif "跨度" in feature_name:
            return np.random.uniform(0, 9, len(sample_data))
        elif "相关性" in feature_name:
            return np.random.uniform(-1, 1, len(sample_data))
        else:
            return np.random.normal(0, 1, len(sample_data))
    
    def _calculate_feature_statistics(self, feature_values: np.ndarray) -> Dict[str, float]:
        """计算特征统计信息"""
        if len(feature_values) == 0:
            return {}
        
        return {
            "mean": float(np.mean(feature_values)),
            "std": float(np.std(feature_values)),
            "min": float(np.min(feature_values)),
            "max": float(np.max(feature_values)),
            "median": float(np.median(feature_values)),
            "skewness": float(self._calculate_skewness(feature_values)),
            "kurtosis": float(self._calculate_kurtosis(feature_values))
        }
    
    def _calculate_skewness(self, values: np.ndarray) -> float:
        """计算偏度"""
        if len(values) < 3:
            return 0.0
        
        mean = np.mean(values)
        std = np.std(values)
        if std == 0:
            return 0.0
        
        return np.mean(((values - mean) / std) ** 3)
    
    def _calculate_kurtosis(self, values: np.ndarray) -> float:
        """计算峰度"""
        if len(values) < 4:
            return 0.0
        
        mean = np.mean(values)
        std = np.std(values)
        if std == 0:
            return 0.0
        
        return np.mean(((values - mean) / std) ** 4) - 3
    
    def _assess_feature_quality(self, feature_values: np.ndarray) -> float:
        """评估特征质量"""
        if len(feature_values) == 0:
            return 0.0
        
        # 基于多个指标评估特征质量
        scores = []
        
        # 1. 信息量（基于方差）
        variance = np.var(feature_values)
        info_score = min(variance / (variance + 1), 1.0)
        scores.append(info_score)
        
        # 2. 稳定性（基于变异系数）
        cv = np.std(feature_values) / (abs(np.mean(feature_values)) + 1e-8)
        stability_score = 1 / (1 + cv)
        scores.append(stability_score)
        
        # 3. 分布合理性
        skewness = abs(self._calculate_skewness(feature_values))
        distribution_score = max(0, 1 - skewness / 3)
        scores.append(distribution_score)
        
        return float(np.mean(scores))
    
    def _clear_feature_cache(self, feature_name: str):
        """清除特征缓存"""
        keys_to_remove = [key for key in self.preview_cache.keys() if feature_name in key]
        for key in keys_to_remove:
            del self.preview_cache[key]
    
    def get_selected_features_summary(self) -> Dict[str, Any]:
        """获取已选择特征的摘要"""
        if not self.selected_features:
            return {
                "total_count": 0,
                "category_distribution": {},
                "parameter_summary": {},
                "estimated_extraction_time": 0.0,
                "selected_features": []
            }
        
        # 统计类别分布
        category_distribution = {}
        for feature_name in self.selected_features:
            if feature_name in self.available_features:
                category = self.available_features[feature_name].category
                category_distribution[category] = category_distribution.get(category, 0) + 1
        
        # 参数摘要
        parameter_summary = {}
        for feature_name, config in self.feature_configs.items():
            parameter_summary[feature_name] = config.parameters
        
        # 估算提取时间
        estimated_time = len(self.selected_features) * 0.1  # 假设每个特征0.1秒
        
        return {
            "total_count": len(self.selected_features),
            "category_distribution": category_distribution,
            "parameter_summary": parameter_summary,
            "estimated_extraction_time": estimated_time,
            "selected_features": list(self.selected_features)
        }
