#!/usr/bin/env python3
"""
预测系统监控和告警配置
配置系统监控和告警机制，建立实时监控仪表板
"""

import os
import sys
import time
import json
import sqlite3
import logging
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import smtplib
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('monitoring.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class MonitoringMetric:
    """监控指标数据结构"""
    name: str
    value: float
    timestamp: datetime
    unit: str = ""
    tags: Dict[str, str] = None
    threshold_warning: Optional[float] = None
    threshold_critical: Optional[float] = None

@dataclass
class AlertRule:
    """告警规则数据结构"""
    name: str
    metric_name: str
    condition: str  # "gt", "lt", "eq", "ne"
    threshold: float
    severity: str  # "warning", "critical", "info"
    enabled: bool = True
    cooldown_minutes: int = 30
    notification_channels: List[str] = None

class PredictionMonitoring:
    """预测系统监控器"""
    
    def __init__(self, project_root: str = None, config_file: str = None):
        """
        初始化监控系统
        
        Args:
            project_root: 项目根目录
            config_file: 配置文件路径
        """
        self.project_root = Path(project_root) if project_root else Path(__file__).parent.parent
        self.config = self._load_monitoring_config(config_file)
        self.metrics_buffer = deque(maxlen=1000)  # 最近1000个指标
        self.alert_history = deque(maxlen=100)    # 最近100个告警
        self.last_alert_times = {}  # 告警冷却时间跟踪
        self.monitoring_active = False
        self.monitoring_thread = None
        
        # 初始化数据库连接
        self.db_path = self.project_root / self.config['database']['model_library_db']
        
        logger.info(f"预测系统监控器初始化完成，项目根目录: {self.project_root}")
    
    def _load_monitoring_config(self, config_file: str = None) -> Dict[str, Any]:
        """加载监控配置"""
        if config_file:
            config_path = Path(config_file)
        else:
            config_path = self.project_root / 'monitoring' / 'config.json'
        
        default_config = {
            "database": {
                "model_library_db": "data/model_library.db"
            },
            "monitoring": {
                "interval_seconds": 60,
                "metrics_retention_hours": 24,
                "enable_email_alerts": False,
                "enable_log_alerts": True
            },
            "thresholds": {
                "accuracy_warning": 0.3,
                "accuracy_critical": 0.2,
                "response_time_warning": 5.0,
                "response_time_critical": 10.0,
                "consistency_warning": 0.8,
                "consistency_critical": 0.7
            },
            "email": {
                "smtp_server": "smtp.gmail.com",
                "smtp_port": 587,
                "username": "",
                "password": "",
                "recipients": []
            }
        }
        
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                logger.warning(f"加载监控配置失败，使用默认配置: {e}")
        
        return default_config
    
    def setup_accuracy_monitoring(self) -> None:
        """设置准确率监控"""
        logger.info("📊 设置准确率监控")
        
        # 定义准确率告警规则
        accuracy_rules = [
            AlertRule(
                name="model_accuracy_warning",
                metric_name="model_accuracy",
                condition="lt",
                threshold=self.config['thresholds']['accuracy_warning'],
                severity="warning",
                notification_channels=["log", "email"]
            ),
            AlertRule(
                name="model_accuracy_critical",
                metric_name="model_accuracy",
                condition="lt", 
                threshold=self.config['thresholds']['accuracy_critical'],
                severity="critical",
                notification_channels=["log", "email"]
            ),
            AlertRule(
                name="overall_accuracy_warning",
                metric_name="overall_accuracy",
                condition="lt",
                threshold=self.config['thresholds']['accuracy_warning'],
                severity="warning",
                notification_channels=["log"]
            )
        ]
        
        # 注册告警规则
        for rule in accuracy_rules:
            self._register_alert_rule(rule)
        
        logger.info("✅ 准确率监控设置完成")
    
    def setup_performance_monitoring(self) -> None:
        """设置性能监控"""
        logger.info("⚡ 设置性能监控")
        
        # 定义性能告警规则
        performance_rules = [
            AlertRule(
                name="prediction_response_time_warning",
                metric_name="prediction_response_time",
                condition="gt",
                threshold=self.config['thresholds']['response_time_warning'],
                severity="warning",
                notification_channels=["log"]
            ),
            AlertRule(
                name="prediction_response_time_critical",
                metric_name="prediction_response_time",
                condition="gt",
                threshold=self.config['thresholds']['response_time_critical'],
                severity="critical",
                notification_channels=["log", "email"]
            ),
            AlertRule(
                name="database_query_time_warning",
                metric_name="database_query_time",
                condition="gt",
                threshold=1.0,
                severity="warning",
                notification_channels=["log"]
            )
        ]
        
        # 注册告警规则
        for rule in performance_rules:
            self._register_alert_rule(rule)
        
        logger.info("✅ 性能监控设置完成")
    
    def setup_consistency_monitoring(self) -> None:
        """设置一致性监控"""
        logger.info("🔄 设置一致性监控")
        
        # 定义一致性告警规则
        consistency_rules = [
            AlertRule(
                name="prediction_consistency_warning",
                metric_name="prediction_consistency",
                condition="lt",
                threshold=self.config['thresholds']['consistency_warning'],
                severity="warning",
                notification_channels=["log"]
            ),
            AlertRule(
                name="prediction_consistency_critical",
                metric_name="prediction_consistency",
                condition="lt",
                threshold=self.config['thresholds']['consistency_critical'],
                severity="critical",
                notification_channels=["log", "email"]
            ),
            AlertRule(
                name="model_weight_stability",
                metric_name="weight_change_rate",
                condition="gt",
                threshold=0.3,
                severity="warning",
                notification_channels=["log"]
            )
        ]
        
        # 注册告警规则
        for rule in consistency_rules:
            self._register_alert_rule(rule)
        
        logger.info("✅ 一致性监控设置完成")
    
    def configure_alerts(self) -> None:
        """配置告警规则"""
        logger.info("🚨 配置告警规则")
        
        # 设置各类监控
        self.setup_accuracy_monitoring()
        self.setup_performance_monitoring()
        self.setup_consistency_monitoring()
        
        # 配置通知渠道
        if self.config['monitoring']['enable_email_alerts']:
            self._setup_email_notifications()
        
        logger.info("✅ 告警规则配置完成")
    
    def start_monitoring(self) -> None:
        """启动监控"""
        if self.monitoring_active:
            logger.warning("监控已在运行中")
            return
        
        logger.info("🚀 启动预测系统监控")
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
        logger.info("✅ 监控启动成功")
    
    def stop_monitoring(self) -> None:
        """停止监控"""
        logger.info("⏹️ 停止预测系统监控")
        
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        logger.info("✅ 监控已停止")
    
    def _monitoring_loop(self) -> None:
        """监控主循环"""
        interval = self.config['monitoring']['interval_seconds']
        
        while self.monitoring_active:
            try:
                # 收集指标
                self._collect_metrics()
                
                # 检查告警
                self._check_alerts()
                
                # 清理过期数据
                self._cleanup_old_data()
                
                # 等待下一个监控周期
                time.sleep(interval)
                
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                time.sleep(interval)
    
    def _collect_metrics(self) -> None:
        """收集监控指标"""
        try:
            # 添加项目根目录到Python路径
            sys.path.insert(0, str(self.project_root))
            
            from src.core.model_performance_tracker import ModelPerformanceTracker
            
            # 初始化性能跟踪器
            tracker = ModelPerformanceTracker(db_path=str(self.db_path))
            
            # 收集模型准确率指标
            performance_summary = tracker.get_model_performance_summary()
            
            for model_name, stats in performance_summary.items():
                metric = MonitoringMetric(
                    name="model_accuracy",
                    value=stats['accuracy_rate'],
                    timestamp=datetime.now(),
                    unit="percentage",
                    tags={"model": model_name}
                )
                self._record_metric(metric)
            
            # 收集整体准确率
            if performance_summary:
                total_predictions = sum(stats['total_predictions'] for stats in performance_summary.values())
                total_correct = sum(stats['correct_predictions'] for stats in performance_summary.values())
                overall_accuracy = total_correct / total_predictions if total_predictions > 0 else 0
                
                metric = MonitoringMetric(
                    name="overall_accuracy",
                    value=overall_accuracy,
                    timestamp=datetime.now(),
                    unit="percentage"
                )
                self._record_metric(metric)
            
            # 收集数据库性能指标
            self._collect_database_metrics()
            
            # 收集预测一致性指标
            self._collect_consistency_metrics()
            
        except Exception as e:
            logger.error(f"收集指标失败: {e}")
    
    def _collect_database_metrics(self) -> None:
        """收集数据库性能指标"""
        try:
            start_time = time.time()
            
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()
                
                # 测试查询性能
                cursor.execute("SELECT COUNT(*) FROM model_predictions")
                count = cursor.fetchone()[0]
                
                query_time = time.time() - start_time
                
                # 记录查询时间指标
                metric = MonitoringMetric(
                    name="database_query_time",
                    value=query_time,
                    timestamp=datetime.now(),
                    unit="seconds"
                )
                self._record_metric(metric)
                
                # 记录数据量指标
                metric = MonitoringMetric(
                    name="prediction_records_count",
                    value=count,
                    timestamp=datetime.now(),
                    unit="count"
                )
                self._record_metric(metric)
                
        except Exception as e:
            logger.error(f"收集数据库指标失败: {e}")
    
    def _collect_consistency_metrics(self) -> None:
        """收集预测一致性指标"""
        try:
            # 这里可以实现预测一致性检查逻辑
            # 暂时使用模拟数据
            consistency_score = 0.95  # 模拟95%的一致性
            
            metric = MonitoringMetric(
                name="prediction_consistency",
                value=consistency_score,
                timestamp=datetime.now(),
                unit="percentage"
            )
            self._record_metric(metric)
            
        except Exception as e:
            logger.error(f"收集一致性指标失败: {e}")
    
    def _record_metric(self, metric: MonitoringMetric) -> None:
        """记录指标"""
        self.metrics_buffer.append(metric)
        
        # 同时记录到数据库
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()
                
                # 确保表存在
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS prediction_performance_log (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        log_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                        metric_name TEXT NOT NULL,
                        metric_value REAL NOT NULL,
                        metric_unit TEXT,
                        period_number TEXT,
                        model_name TEXT,
                        additional_info TEXT
                    )
                """)
                
                # 插入指标数据
                additional_info = json.dumps(metric.tags) if metric.tags else None
                model_name = metric.tags.get('model') if metric.tags else None
                
                cursor.execute("""
                    INSERT INTO prediction_performance_log 
                    (log_date, metric_name, metric_value, metric_unit, model_name, additional_info)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    metric.timestamp,
                    metric.name,
                    metric.value,
                    metric.unit,
                    model_name,
                    additional_info
                ))
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"记录指标到数据库失败: {e}")
    
    def _register_alert_rule(self, rule: AlertRule) -> None:
        """注册告警规则"""
        if not hasattr(self, 'alert_rules'):
            self.alert_rules = []
        
        self.alert_rules.append(rule)
        logger.debug(f"注册告警规则: {rule.name}")
    
    def _check_alerts(self) -> None:
        """检查告警条件"""
        if not hasattr(self, 'alert_rules'):
            return
        
        # 获取最近的指标
        recent_metrics = list(self.metrics_buffer)[-50:]  # 最近50个指标
        
        for rule in self.alert_rules:
            if not rule.enabled:
                continue
            
            # 查找匹配的指标
            matching_metrics = [m for m in recent_metrics if m.name == rule.metric_name]
            
            if not matching_metrics:
                continue
            
            # 使用最新的指标值
            latest_metric = matching_metrics[-1]
            
            # 检查告警条件
            if self._evaluate_alert_condition(latest_metric.value, rule):
                self._trigger_alert(rule, latest_metric)
    
    def _evaluate_alert_condition(self, value: float, rule: AlertRule) -> bool:
        """评估告警条件"""
        if rule.condition == "gt":
            return value > rule.threshold
        elif rule.condition == "lt":
            return value < rule.threshold
        elif rule.condition == "eq":
            return abs(value - rule.threshold) < 0.001
        elif rule.condition == "ne":
            return abs(value - rule.threshold) >= 0.001
        else:
            return False
    
    def _trigger_alert(self, rule: AlertRule, metric: MonitoringMetric) -> None:
        """触发告警"""
        # 检查冷却时间
        now = datetime.now()
        last_alert_time = self.last_alert_times.get(rule.name)
        
        if last_alert_time:
            time_diff = now - last_alert_time
            if time_diff.total_seconds() < rule.cooldown_minutes * 60:
                return  # 还在冷却期内
        
        # 创建告警消息
        alert_message = self._create_alert_message(rule, metric)
        
        # 发送告警
        self._send_alert(rule, alert_message)
        
        # 记录告警历史
        self.alert_history.append({
            'rule_name': rule.name,
            'metric_name': metric.name,
            'metric_value': metric.value,
            'threshold': rule.threshold,
            'severity': rule.severity,
            'timestamp': now,
            'message': alert_message
        })
        
        # 更新最后告警时间
        self.last_alert_times[rule.name] = now
    
    def _create_alert_message(self, rule: AlertRule, metric: MonitoringMetric) -> str:
        """创建告警消息"""
        return f"""
🚨 预测系统告警

告警规则: {rule.name}
严重程度: {rule.severity.upper()}
指标名称: {metric.name}
当前值: {metric.value:.3f} {metric.unit}
阈值: {rule.threshold:.3f}
条件: {rule.condition}
时间: {metric.timestamp.strftime('%Y-%m-%d %H:%M:%S')}

请及时检查系统状态！
        """.strip()
    
    def _send_alert(self, rule: AlertRule, message: str) -> None:
        """发送告警"""
        if not rule.notification_channels:
            return
        
        for channel in rule.notification_channels:
            if channel == "log":
                if rule.severity == "critical":
                    logger.critical(message)
                elif rule.severity == "warning":
                    logger.warning(message)
                else:
                    logger.info(message)
            
            elif channel == "email" and self.config['monitoring']['enable_email_alerts']:
                self._send_email_alert(message)
    
    def _send_email_alert(self, message: str) -> None:
        """发送邮件告警"""
        try:
            email_config = self.config['email']
            
            if not email_config['recipients']:
                return
            
            msg = MimeMultipart()
            msg['From'] = email_config['username']
            msg['To'] = ', '.join(email_config['recipients'])
            msg['Subject'] = "福彩3D预测系统告警"
            
            msg.attach(MimeText(message, 'plain', 'utf-8'))
            
            server = smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port'])
            server.starttls()
            server.login(email_config['username'], email_config['password'])
            
            text = msg.as_string()
            server.sendmail(email_config['username'], email_config['recipients'], text)
            server.quit()
            
            logger.info("邮件告警发送成功")
            
        except Exception as e:
            logger.error(f"发送邮件告警失败: {e}")
    
    def _setup_email_notifications(self) -> None:
        """设置邮件通知"""
        email_config = self.config['email']
        
        if not email_config['username'] or not email_config['password']:
            logger.warning("邮件配置不完整，禁用邮件告警")
            self.config['monitoring']['enable_email_alerts'] = False
        else:
            logger.info("邮件告警配置完成")
    
    def _cleanup_old_data(self) -> None:
        """清理过期数据"""
        try:
            retention_hours = self.config['monitoring']['metrics_retention_hours']
            cutoff_time = datetime.now() - timedelta(hours=retention_hours)
            
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()
                
                # 清理过期的性能日志
                cursor.execute("""
                    DELETE FROM prediction_performance_log 
                    WHERE log_date < ?
                """, (cutoff_time,))
                
                deleted_count = cursor.rowcount
                if deleted_count > 0:
                    logger.info(f"清理了 {deleted_count} 条过期监控数据")
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"清理过期数据失败: {e}")
    
    def get_monitoring_dashboard_data(self) -> Dict[str, Any]:
        """获取监控仪表板数据"""
        try:
            # 获取最近的指标
            recent_metrics = list(self.metrics_buffer)[-100:]
            
            # 按指标名称分组
            metrics_by_name = defaultdict(list)
            for metric in recent_metrics:
                metrics_by_name[metric.name].append(metric)
            
            # 构建仪表板数据
            dashboard_data = {
                'timestamp': datetime.now().isoformat(),
                'metrics': {},
                'alerts': list(self.alert_history)[-10:],  # 最近10个告警
                'system_status': 'healthy'
            }
            
            # 处理各类指标
            for metric_name, metrics in metrics_by_name.items():
                if metrics:
                    latest_metric = metrics[-1]
                    dashboard_data['metrics'][metric_name] = {
                        'current_value': latest_metric.value,
                        'unit': latest_metric.unit,
                        'timestamp': latest_metric.timestamp.isoformat(),
                        'history': [{'value': m.value, 'timestamp': m.timestamp.isoformat()} for m in metrics[-20:]]
                    }
            
            # 判断系统状态
            if any(alert['severity'] == 'critical' for alert in dashboard_data['alerts'][-5:]):
                dashboard_data['system_status'] = 'critical'
            elif any(alert['severity'] == 'warning' for alert in dashboard_data['alerts'][-5:]):
                dashboard_data['system_status'] = 'warning'
            
            return dashboard_data
            
        except Exception as e:
            logger.error(f"获取仪表板数据失败: {e}")
            return {'error': str(e)}

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='福彩3D预测系统监控')
    parser.add_argument('--project-root', help='项目根目录路径')
    parser.add_argument('--config', help='配置文件路径')
    parser.add_argument('--daemon', action='store_true', help='后台运行模式')
    
    args = parser.parse_args()
    
    # 初始化监控系统
    monitor = PredictionMonitoring(project_root=args.project_root, config_file=args.config)
    
    # 配置告警
    monitor.configure_alerts()
    
    # 启动监控
    monitor.start_monitoring()
    
    if args.daemon:
        logger.info("监控系统在后台运行中...")
        try:
            while True:
                time.sleep(60)
        except KeyboardInterrupt:
            logger.info("收到停止信号")
    else:
        logger.info("监控系统运行中，按 Ctrl+C 停止...")
        try:
            while True:
                time.sleep(10)
                # 显示简单状态
                dashboard = monitor.get_monitoring_dashboard_data()
                print(f"系统状态: {dashboard.get('system_status', 'unknown')}")
        except KeyboardInterrupt:
            logger.info("收到停止信号")
    
    # 停止监控
    monitor.stop_monitoring()
    logger.info("监控系统已停止")

if __name__ == "__main__":
    main()
