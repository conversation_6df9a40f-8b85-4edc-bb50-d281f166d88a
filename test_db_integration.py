#!/usr/bin/env python3
"""
数据库管理器集成测试
"""

import sys
sys.path.append('src')

from core.database import DatabaseManager

def test_integration():
    print("🔍 测试数据库管理器集成...")
    
    # 测试1: 创建数据库管理器
    try:
        db_manager = DatabaseManager('data/test_lottery.db')
        print("✅ 数据库管理器创建成功")
    except Exception as e:
        print(f"❌ 数据库管理器创建失败: {e}")
        return False
    
    # 测试2: 健康检查
    try:
        health_status = db_manager.health_check()
        healthy_text = "健康" if health_status['healthy'] else "不健康"
        print(f"✅ 健康检查完成: {healthy_text}")
        print(f"   检查项目数: {len(health_status['checks'])}")
        print(f"   响应时间: {health_status['response_time_ms']}ms")
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False
    
    # 测试3: 连接池状态
    try:
        pool_status = db_manager.get_connection_pool_status()
        print("✅ 连接池状态获取成功")
        print(f"   最大连接数: {pool_status['max_connections']}")
        print(f"   已创建连接: {pool_status['created_connections']}")
        print(f"   可用连接: {pool_status['available_connections']}")
        print(f"   活跃连接: {pool_status['active_connections']}")
    except Exception as e:
        print(f"❌ 连接池状态获取失败: {e}")
        return False
    
    # 测试4: 记录数量查询
    try:
        record_count = db_manager.get_records_count()
        print(f"✅ 记录数量查询成功: {record_count} 条记录")
    except Exception as e:
        print(f"❌ 记录数量查询失败: {e}")
        return False
    
    # 清理
    try:
        db_manager.close()
        print("✅ 数据库管理器关闭成功")
    except Exception as e:
        print(f"⚠️  关闭数据库管理器时出现问题: {e}")
    
    print("🎉 数据库管理器集成测试完成！")
    return True

if __name__ == "__main__":
    test_integration()
