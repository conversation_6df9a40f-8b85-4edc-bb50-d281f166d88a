#!/usr/bin/env python3
"""
WebSocket管理器
实现WebSocket连接管理、客户端会话管理、消息广播和单播
"""

import asyncio
import json
import logging
import time
import uuid
from dataclasses import asdict, dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional, Set

from fastapi import WebSocket, WebSocketDisconnect

# 导入数据流追踪器
from ..monitoring.data_flow_tracer import (FlowStage, FlowStatus,
                                           add_trace_point)
from .event_bus import Event, EventPriority, EventType, event_bus

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ClientSession:
    """客户端会话信息"""
    id: str
    websocket: WebSocket
    user_id: Optional[str]
    connected_at: float
    last_ping: float
    subscriptions: Set[str]
    metadata: Dict[str, Any]
    
    def __post_init__(self):
        if self.subscriptions is None:
            self.subscriptions = set()
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典（排除WebSocket对象）"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'connected_at': self.connected_at,
            'last_ping': self.last_ping,
            'subscriptions': list(self.subscriptions),
            'metadata': self.metadata
        }

class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 活跃连接
        self.active_connections: Dict[str, ClientSession] = {}
        
        # 订阅管理
        self.subscriptions: Dict[str, Set[str]] = {}  # topic -> client_ids
        
        # 配置
        self.ping_interval = 30  # 心跳间隔（秒）
        self.connection_timeout = 300  # 连接超时（秒）
        self.max_connections = 1000  # 最大连接数
        
        # 统计信息
        self.stats = {
            'total_connections': 0,
            'current_connections': 0,
            'messages_sent': 0,
            'messages_received': 0,
            'errors': 0
        }
        
        # 后台任务
        self.cleanup_task: Optional[asyncio.Task] = None
        self.running = False
    
    async def connect(self, websocket: WebSocket, 
                     user_id: Optional[str] = None,
                     metadata: Optional[Dict[str, Any]] = None) -> str:
        """建立WebSocket连接"""
        try:
            # 检查连接数限制
            if len(self.active_connections) >= self.max_connections:
                await websocket.close(code=1008, reason="Too many connections")
                raise Exception("连接数已达上限")
            
            # 接受连接
            await websocket.accept()
            
            # 创建会话
            session_id = str(uuid.uuid4())
            current_time = time.time()
            
            session = ClientSession(
                id=session_id,
                websocket=websocket,
                user_id=user_id,
                connected_at=current_time,
                last_ping=current_time,
                subscriptions=set(),
                metadata=metadata or {}
            )
            
            # 添加到活跃连接
            self.active_connections[session_id] = session
            
            # 更新统计
            self.stats['total_connections'] += 1
            self.stats['current_connections'] = len(self.active_connections)
            
            logger.info(f"✅ WebSocket连接建立: {session_id} (用户: {user_id})")
            
            # 发送欢迎消息
            await self.send_to_client(session_id, {
                'type': 'connection_established',
                'session_id': session_id,
                'timestamp': current_time
            })
            
            return session_id
            
        except Exception as e:
            logger.error(f"❌ WebSocket连接失败: {e}")
            self.stats['errors'] += 1
            raise
    
    async def disconnect(self, session_id: str):
        """断开WebSocket连接"""
        try:
            if session_id in self.active_connections:
                session = self.active_connections[session_id]
                
                # 清理订阅
                for topic in session.subscriptions:
                    if topic in self.subscriptions:
                        self.subscriptions[topic].discard(session_id)
                        if not self.subscriptions[topic]:
                            del self.subscriptions[topic]
                
                # 移除连接
                del self.active_connections[session_id]
                
                # 更新统计
                self.stats['current_connections'] = len(self.active_connections)
                
                logger.info(f"🔌 WebSocket连接断开: {session_id}")
                
        except Exception as e:
            logger.error(f"❌ WebSocket断开失败: {e}")
            self.stats['errors'] += 1
    
    async def send_to_client(self, session_id: str, message: Dict[str, Any]) -> bool:
        """发送消息给指定客户端"""
        try:
            if session_id not in self.active_connections:
                return False
            
            session = self.active_connections[session_id]
            message_json = json.dumps(message, ensure_ascii=False)
            
            await session.websocket.send_text(message_json)
            
            self.stats['messages_sent'] += 1
            return True
            
        except WebSocketDisconnect:
            # 连接已断开，清理会话
            await self.disconnect(session_id)
            return False
        except Exception as e:
            logger.error(f"❌ 消息发送失败 {session_id}: {e}")
            self.stats['errors'] += 1
            return False
    
    async def broadcast(self, message: Dict[str, Any], 
                       exclude: Optional[Set[str]] = None) -> int:
        """广播消息给所有客户端"""
        exclude = exclude or set()
        sent_count = 0
        
        # 创建发送任务列表
        tasks = []
        for session_id in self.active_connections:
            if session_id not in exclude:
                task = self.send_to_client(session_id, message)
                tasks.append(task)
        
        # 并发发送
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            sent_count = sum(1 for result in results if result is True)
        
        logger.debug(f"📡 广播消息发送给 {sent_count} 个客户端")
        return sent_count
    
    async def send_to_topic(self, topic: str, message: Dict[str, Any]) -> int:
        """发送消息给订阅特定主题的客户端"""
        if topic not in self.subscriptions:
            return 0
        
        sent_count = 0
        subscriber_ids = self.subscriptions[topic].copy()
        
        # 创建发送任务
        tasks = []
        for session_id in subscriber_ids:
            if session_id in self.active_connections:
                task = self.send_to_client(session_id, message)
                tasks.append(task)
        
        # 并发发送
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            sent_count = sum(1 for result in results if result is True)
        
        logger.debug(f"📢 主题消息 '{topic}' 发送给 {sent_count} 个订阅者")
        return sent_count
    
    async def subscribe_client(self, session_id: str, topic: str) -> bool:
        """客户端订阅主题"""
        try:
            if session_id not in self.active_connections:
                return False
            
            # 添加到客户端订阅列表
            session = self.active_connections[session_id]
            session.subscriptions.add(topic)
            
            # 添加到主题订阅者列表
            if topic not in self.subscriptions:
                self.subscriptions[topic] = set()
            self.subscriptions[topic].add(session_id)
            
            logger.debug(f"📥 客户端 {session_id} 订阅主题: {topic}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 客户端订阅失败: {e}")
            return False
    
    async def unsubscribe_client(self, session_id: str, topic: str) -> bool:
        """客户端取消订阅主题"""
        try:
            if session_id not in self.active_connections:
                return False
            
            # 从客户端订阅列表移除
            session = self.active_connections[session_id]
            session.subscriptions.discard(topic)
            
            # 从主题订阅者列表移除
            if topic in self.subscriptions:
                self.subscriptions[topic].discard(session_id)
                if not self.subscriptions[topic]:
                    del self.subscriptions[topic]
            
            logger.debug(f"📤 客户端 {session_id} 取消订阅主题: {topic}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 客户端取消订阅失败: {e}")
            return False
    
    async def handle_client_message(self, session_id: str, message: str):
        """处理客户端消息"""
        try:
            data = json.loads(message)
            message_type = data.get('type')

            self.stats['messages_received'] += 1

            # 如果是错误相关消息，添加数据流追踪
            if message_type in ['javascript_error', 'error_event', 'bug_report']:
                trace_id = data.get('trace_id')
                if trace_id:
                    add_trace_point(
                        trace_id,
                        FlowStage.WEBSOCKET_RECEIVE,
                        FlowStatus.SUCCESS,
                        {"message_type": message_type, "session_id": session_id}
                    )
            
            if message_type == 'ping':
                # 心跳响应
                await self._handle_ping(session_id, data)
            elif message_type == 'subscribe':
                # 订阅主题
                topic = data.get('topic')
                if topic:
                    await self.subscribe_client(session_id, topic)
                    await self.send_to_client(session_id, {
                        'type': 'subscription_confirmed',
                        'topic': topic
                    })
            elif message_type == 'unsubscribe':
                # 取消订阅
                topic = data.get('topic')
                if topic:
                    await self.unsubscribe_client(session_id, topic)
                    await self.send_to_client(session_id, {
                        'type': 'unsubscription_confirmed',
                        'topic': topic
                    })
            elif message_type == 'get_stats':
                # 获取统计信息
                await self.send_to_client(session_id, {
                    'type': 'stats',
                    'data': self.get_stats()
                })
            else:
                logger.warning(f"⚠️ 未知消息类型: {message_type}")
                
        except json.JSONDecodeError:
            logger.error(f"❌ 无效JSON消息: {message}")
        except Exception as e:
            logger.error(f"❌ 消息处理失败: {e}")
            self.stats['errors'] += 1
    
    async def _handle_ping(self, session_id: str, data: Dict[str, Any]):
        """处理心跳消息"""
        if session_id in self.active_connections:
            session = self.active_connections[session_id]
            session.last_ping = time.time()
            
            # 发送pong响应
            await self.send_to_client(session_id, {
                'type': 'pong',
                'timestamp': session.last_ping
            })
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            'subscriptions': {
                topic: len(subscribers) 
                for topic, subscribers in self.subscriptions.items()
            },
            'active_sessions': [
                session.to_dict() 
                for session in self.active_connections.values()
            ]
        }
    
    async def start_background_tasks(self):
        """启动后台任务"""
        if not self.running:
            self.running = True
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())
            logger.info("🚀 WebSocket管理器后台任务已启动")
    
    async def stop_background_tasks(self):
        """停止后台任务"""
        self.running = False
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
        logger.info("🛑 WebSocket管理器后台任务已停止")
    
    async def _cleanup_loop(self):
        """清理循环"""
        while self.running:
            try:
                await self._cleanup_stale_connections()
                await asyncio.sleep(60)  # 每分钟清理一次
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ 清理任务失败: {e}")
                await asyncio.sleep(60)
    
    async def _cleanup_stale_connections(self):
        """清理过期连接"""
        current_time = time.time()
        stale_sessions = []
        
        for session_id, session in self.active_connections.items():
            # 检查连接是否超时
            if current_time - session.last_ping > self.connection_timeout:
                stale_sessions.append(session_id)
        
        # 清理过期连接
        for session_id in stale_sessions:
            logger.info(f"🧹 清理过期连接: {session_id}")
            await self.disconnect(session_id)
    
    async def handle_event(self, event: Event):
        """处理事件总线事件"""
        try:
            # 构建WebSocket消息
            message = {
                'type': 'event',
                'event_type': event.type.value,
                'event_id': event.id,
                'timestamp': event.timestamp,
                'priority': event.priority.value,
                'source': event.source,
                'data': event.data,
                'tags': event.tags
            }
            
            # 发送给订阅相应事件类型的客户端
            topic = f"events:{event.type.value}"
            await self.send_to_topic(topic, message)
            
            # 高优先级事件广播给所有客户端
            if event.priority in [EventPriority.HIGH, EventPriority.CRITICAL]:
                await self.broadcast({
                    **message,
                    'type': 'alert',
                    'alert_level': event.priority.value
                })
                
        except Exception as e:
            logger.error(f"❌ 事件处理失败: {e}")

# 全局WebSocket管理器实例
websocket_manager = WebSocketManager()

# 初始化函数
async def initialize_websocket_manager():
    """初始化WebSocket管理器"""
    await websocket_manager.start_background_tasks()
    
    # 订阅所有事件类型
    for event_type in EventType:
        await event_bus.subscribe(event_type, websocket_manager.handle_event)
    
    logger.info("✅ WebSocket管理器初始化完成")
    return websocket_manager

if __name__ == "__main__":
    # 测试代码
    async def test_websocket_manager():
        await initialize_websocket_manager()
        
        # 模拟一些操作
        await asyncio.sleep(5)
        
        # 停止
        await websocket_manager.stop_background_tasks()
    
    asyncio.run(test_websocket_manager())
