"""
福彩3D数据采集器模块

实现自动化数据采集功能，包括：
- 从指定URL获取历史数据
- 网络异常处理和重试机制
- 请求头和反爬虫处理
- 数据格式验证
"""

import asyncio
import logging
import sys
import time
from pathlib import Path
from typing import Any, Dict, List, Optional

# 添加项目路径
sys.path.append('src')

try:
    import httpx
    from loguru import logger
except ImportError:
    # 如果依赖未安装，使用标准库替代
    import logging as logger
    import urllib.error
    import urllib.request

# 导入新的数据源管理器
from core.data_source_manager import DataSourceManager
from data.models import LotteryRecord


class LotteryDataCollector:
    """福彩3D数据采集器（增强版）"""

    def __init__(
        self,
        base_url: str = "https://data.17500.cn/3d_asc.txt",
        timeout: int = 30,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        cache_dir: str = "data/cache"
    ):
        """
        初始化数据采集器

        Args:
            base_url: 数据源URL
            timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
            retry_delay: 重试延迟时间（秒）
            cache_dir: 缓存目录
        """
        self.base_url = base_url
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay

        # 设置请求头，模拟浏览器访问
        self.headers = {
            "User-Agent": (
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                "AppleWebKit/537.36 (KHTML, like Gecko) "
                "Chrome/120.0.0.0 Safari/537.36"
            ),
            "Accept": "text/plain,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
        }

        # 使用新的数据源管理器
        self.data_source_manager = DataSourceManager(cache_dir, timeout)

        # 配置日志
        self.logger = logging.getLogger(__name__)

        logger.info(f"数据采集器初始化完成，目标URL: {self.base_url}")

    def fetch_data_enhanced(self, use_cache: bool = True, parse: bool = True):
        """
        使用增强数据源管理器获取数据

        Args:
            use_cache: 是否使用缓存
            parse: 是否解析数据

        Returns:
            解析后的记录列表或None
        """
        try:
            self.logger.info("开始使用增强数据源管理器获取数据...")

            # 使用数据源管理器获取数据
            result = self.data_source_manager.get_latest_data(use_cache=use_cache, parse=parse)

            if result is None:
                self.logger.error("数据获取失败")
                return None

            if parse and isinstance(result, list):
                self.logger.info(f"数据获取成功，解析了 {len(result)} 条记录")
                return result
            elif not parse and isinstance(result, str):
                self.logger.info(f"数据获取成功，原始数据长度: {len(result)} 字符")
                return result
            else:
                self.logger.warning("数据格式不符合预期")
                return None

        except Exception as e:
            self.logger.error(f"增强数据获取失败: {e}")
            return None

    def get_data_source_status(self) -> Dict[str, Any]:
        """获取数据源状态"""
        try:
            cache_status = self.data_source_manager.get_cache_status()
            health_status = self.data_source_manager.health_check()

            return {
                "cache_status": cache_status,
                "health_status": health_status,
                "collector_config": {
                    "base_url": self.base_url,
                    "timeout": self.timeout,
                    "max_retries": self.max_retries,
                    "retry_delay": self.retry_delay
                }
            }
        except Exception as e:
            self.logger.error(f"获取数据源状态失败: {e}")
            return {"error": str(e)}

    def clear_cache(self) -> bool:
        """清理缓存"""
        try:
            cleared_count = self.data_source_manager.clear_cache()
            self.logger.info(f"清理了 {cleared_count} 个缓存文件")
            return True
        except Exception as e:
            self.logger.error(f"清理缓存失败: {e}")
            return False

    def close(self):
        """关闭采集器"""
        try:
            if hasattr(self, 'data_source_manager'):
                self.data_source_manager.close()
            self.logger.info("数据采集器已关闭")
        except Exception as e:
            self.logger.warning(f"关闭数据采集器时出现问题: {e}")
    
    async def fetch_data_async(self) -> Optional[str]:
        """
        异步获取数据（如果httpx可用）
        
        Returns:
            原始数据字符串，失败时返回None
        """
        try:
            import httpx
            
            async with httpx.AsyncClient(
                timeout=self.timeout,
                headers=self.headers,
                follow_redirects=True
            ) as client:
                
                for attempt in range(self.max_retries):
                    try:
                        logger.info(f"开始第 {attempt + 1} 次数据获取尝试")
                        
                        response = await client.get(self.base_url)
                        response.raise_for_status()
                        
                        # 检查响应内容
                        content = response.text
                        if not content or len(content) < 100:
                            raise ValueError("响应内容为空或过短")
                        
                        logger.info(f"数据获取成功，内容长度: {len(content)} 字符")
                        return content
                        
                    except (httpx.RequestError, httpx.HTTPStatusError, ValueError) as e:
                        logger.warning(f"第 {attempt + 1} 次尝试失败: {e}")
                        
                        if attempt < self.max_retries - 1:
                            await asyncio.sleep(self.retry_delay * (attempt + 1))
                        else:
                            logger.error("所有重试尝试均失败")
                            
        except ImportError:
            logger.warning("httpx未安装，将使用同步方法")
            return self.fetch_data_sync()
        
        return None
    
    def fetch_data_sync(self) -> Optional[str]:
        """
        同步获取数据（使用标准库）
        
        Returns:
            原始数据字符串，失败时返回None
        """
        import urllib.error
        import urllib.request
        
        for attempt in range(self.max_retries):
            try:
                logger.info(f"开始第 {attempt + 1} 次数据获取尝试（同步模式）")
                
                # 创建请求对象
                request = urllib.request.Request(
                    self.base_url,
                    headers=self.headers
                )
                
                # 发送请求
                with urllib.request.urlopen(request, timeout=self.timeout) as response:
                    content = response.read().decode('utf-8', errors='ignore')
                    
                    if not content or len(content) < 100:
                        raise ValueError("响应内容为空或过短")
                    
                    logger.info(f"数据获取成功，内容长度: {len(content)} 字符")
                    return content
                    
            except (urllib.error.URLError, urllib.error.HTTPError, ValueError) as e:
                logger.warning(f"第 {attempt + 1} 次尝试失败: {e}")
                
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay * (attempt + 1))
                else:
                    logger.error("所有重试尝试均失败")
        
        return None
    
    async def fetch_latest_data(self) -> Optional[str]:
        """
        获取最新数据的主要接口
        
        Returns:
            原始数据字符串，失败时返回None
        """
        try:
            # 优先使用异步方法
            return await self.fetch_data_async()
        except Exception as e:
            logger.error(f"数据获取失败: {e}")
            return None
    
    def fetch_latest_data_sync(self) -> Optional[str]:
        """
        同步获取最新数据的接口
        
        Returns:
            原始数据字符串，失败时返回None
        """
        try:
            return self.fetch_data_sync()
        except Exception as e:
            logger.error(f"数据获取失败: {e}")
            return None
    
    def save_raw_data(self, data: str, filepath: Optional[Path] = None) -> bool:
        """
        保存原始数据到文件
        
        Args:
            data: 要保存的数据
            filepath: 保存路径，默认为data/raw/目录
            
        Returns:
            保存是否成功
        """
        try:
            if filepath is None:
                # 创建默认保存路径
                raw_dir = Path("data/raw")
                raw_dir.mkdir(parents=True, exist_ok=True)
                
                # 使用时间戳命名文件
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filepath = raw_dir / f"3d_data_{timestamp}.txt"
            
            # 保存数据
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(data)
            
            logger.info(f"原始数据已保存到: {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"保存数据失败: {e}")
            return False
    
    def get_data_info(self, data: str) -> Dict[str, Any]:
        """
        获取数据基本信息
        
        Args:
            data: 原始数据字符串
            
        Returns:
            数据信息字典
        """
        if not data:
            return {"error": "数据为空"}
        
        lines = data.strip().split('\n')
        
        return {
            "total_lines": len(lines),
            "data_size": len(data),
            "first_line": lines[0] if lines else "",
            "last_line": lines[-1] if lines else "",
            "sample_lines": lines[:5] if len(lines) >= 5 else lines,
        }


# 便捷函数
async def collect_lottery_data() -> Optional[str]:
    """
    便捷的数据采集函数
    
    Returns:
        采集到的数据，失败时返回None
    """
    collector = LotteryDataCollector()
    return await collector.fetch_latest_data()


def collect_lottery_data_sync() -> Optional[str]:
    """
    同步版本的便捷数据采集函数
    
    Returns:
        采集到的数据，失败时返回None
    """
    collector = LotteryDataCollector()
    return collector.fetch_latest_data_sync()


if __name__ == "__main__":
    # 测试代码
    import asyncio
    
    async def test_collector():
        """测试数据采集器"""
        collector = LotteryDataCollector()
        
        print("开始测试数据采集器...")
        data = await collector.fetch_latest_data()
        
        if data:
            info = collector.get_data_info(data)
            print(f"数据采集成功！")
            print(f"数据行数: {info['total_lines']}")
            print(f"数据大小: {info['data_size']} 字符")
            print(f"前几行示例: {info['sample_lines']}")
            
            # 保存数据
            collector.save_raw_data(data)
        else:
            print("数据采集失败！")
    
    # 运行测试
    asyncio.run(test_collector())
