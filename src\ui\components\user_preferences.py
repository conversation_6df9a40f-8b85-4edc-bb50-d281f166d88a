"""
用户偏好管理模块
管理用户行为统计、收藏页面、偏好设置等功能
"""

import streamlit as st
from datetime import datetime
from typing import List, Tuple, Dict, Any


class UserPreferenceManager:
    """用户偏好和使用统计管理类"""
    
    def __init__(self):
        """初始化用户偏好管理器"""
        self._initialize_preferences()
    
    def _initialize_preferences(self):
        """初始化用户偏好数据结构"""
        if 'user_preferences' not in st.session_state:
            st.session_state.user_preferences = {
                'page_usage': {},  # 页面使用次数统计
                'favorite_pages': [],  # 收藏的页面列表
                'navigation_mode': '分类浏览',  # 当前导航模式
                'last_visited': {},  # 最后访问时间记录
                'session_start': datetime.now().isoformat(),  # 会话开始时间
                'total_sessions': 0  # 总会话数
            }
            # 增加会话计数
            st.session_state.user_preferences['total_sessions'] += 1
    
    def record_page_visit(self, page_name: str):
        """
        记录页面访问
        
        Args:
            page_name: 页面名称
        """
        if not page_name:
            return
            
        prefs = st.session_state.user_preferences
        
        # 更新访问次数
        prefs['page_usage'][page_name] = prefs['page_usage'].get(page_name, 0) + 1
        
        # 更新最后访问时间
        prefs['last_visited'][page_name] = datetime.now().isoformat()
    
    def get_frequent_pages(self, limit: int = 5) -> List[Tuple[str, int]]:
        """
        获取使用频率最高的页面
        
        Args:
            limit: 返回页面数量限制
            
        Returns:
            页面名称和使用次数的元组列表
        """
        usage = st.session_state.user_preferences['page_usage']
        if not usage:
            return []
            
        # 按使用次数排序
        sorted_pages = sorted(usage.items(), key=lambda x: x[1], reverse=True)
        return sorted_pages[:limit]
    
    def get_recent_pages(self, limit: int = 3) -> List[str]:
        """
        获取最近访问的页面
        
        Args:
            limit: 返回页面数量限制
            
        Returns:
            最近访问的页面名称列表
        """
        recent = st.session_state.user_preferences['last_visited']
        if not recent:
            return []
            
        # 按访问时间排序
        sorted_recent = sorted(recent.items(), key=lambda x: x[1], reverse=True)
        return [page for page, _ in sorted_recent[:limit]]
    
    def toggle_favorite(self, page_name: str) -> bool:
        """
        切换页面收藏状态
        
        Args:
            page_name: 页面名称
            
        Returns:
            收藏后的状态 (True: 已收藏, False: 已取消收藏)
        """
        if not page_name:
            return False
            
        favorites = st.session_state.user_preferences['favorite_pages']
        
        if page_name in favorites:
            favorites.remove(page_name)
            return False
        else:
            favorites.append(page_name)
            return True
    
    def is_favorite(self, page_name: str) -> bool:
        """
        检查页面是否已收藏
        
        Args:
            page_name: 页面名称
            
        Returns:
            是否已收藏
        """
        return page_name in st.session_state.user_preferences['favorite_pages']
    
    def get_favorite_pages(self) -> List[str]:
        """
        获取收藏的页面列表
        
        Returns:
            收藏页面名称列表
        """
        return st.session_state.user_preferences['favorite_pages'].copy()
    
    def set_navigation_mode(self, mode: str):
        """
        设置导航模式
        
        Args:
            mode: 导航模式名称
        """
        st.session_state.user_preferences['navigation_mode'] = mode
    
    def get_navigation_mode(self) -> str:
        """
        获取当前导航模式
        
        Returns:
            当前导航模式
        """
        return st.session_state.user_preferences.get('navigation_mode', '分类浏览')
    
    def get_usage_statistics(self) -> Dict[str, Any]:
        """
        获取使用统计信息
        
        Returns:
            使用统计数据字典
        """
        prefs = st.session_state.user_preferences
        
        total_visits = sum(prefs['page_usage'].values())
        unique_pages = len(prefs['page_usage'])
        favorite_count = len(prefs['favorite_pages'])
        
        return {
            'total_visits': total_visits,
            'unique_pages': unique_pages,
            'favorite_count': favorite_count,
            'total_sessions': prefs.get('total_sessions', 0),
            'session_start': prefs.get('session_start', ''),
            'most_visited': self.get_frequent_pages(1)
        }
    
    def clear_statistics(self):
        """清除所有统计数据"""
        st.session_state.user_preferences = {
            'page_usage': {},
            'favorite_pages': [],
            'navigation_mode': '分类浏览',
            'last_visited': {},
            'session_start': datetime.now().isoformat(),
            'total_sessions': 1
        }
    
    def export_preferences(self) -> Dict[str, Any]:
        """
        导出用户偏好数据
        
        Returns:
            用户偏好数据字典
        """
        return st.session_state.user_preferences.copy()
    
    def import_preferences(self, preferences: Dict[str, Any]):
        """
        导入用户偏好数据
        
        Args:
            preferences: 用户偏好数据字典
        """
        if isinstance(preferences, dict):
            st.session_state.user_preferences.update(preferences)
