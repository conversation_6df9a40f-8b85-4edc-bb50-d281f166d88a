#!/usr/bin/env python3
"""
手动初始化Bug检测数据库
"""

import sqlite3
import os

def init_bug_detection_db():
    """初始化Bug检测数据库"""
    db_path = "data/bug_detection.db"
    
    # 确保data目录存在
    os.makedirs("data", exist_ok=True)
    
    print(f"初始化Bug检测数据库: {db_path}")
    
    schema_sql = """
    -- Bug报告表
    CREATE TABLE IF NOT EXISTS bug_reports (
        id TEXT PRIMARY KEY,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        error_type TEXT NOT NULL,
        severity TEXT DEFAULT 'medium',
        page_name TEXT,
        error_message TEXT,
        stack_trace TEXT,
        status TEXT DEFAULT 'open',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
    
    -- 用户行为表
    CREATE TABLE IF NOT EXISTS user_behaviors (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_id TEXT NOT NULL,
        page_name TEXT,
        action_type TEXT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
    );
    
    -- 性能监控表
    CREATE TABLE IF NOT EXISTS performance_metrics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        endpoint TEXT,
        response_time REAL,
        status_code INTEGER,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
    );
    
    -- JavaScript错误表
    CREATE TABLE IF NOT EXISTS js_errors (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_id TEXT,
        error_message TEXT,
        page_url TEXT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
    );

    -- 实时事件表
    CREATE TABLE IF NOT EXISTS realtime_events (
        id TEXT PRIMARY KEY,
        event_type TEXT NOT NULL,
        priority INTEGER NOT NULL,
        source TEXT NOT NULL,
        timestamp REAL NOT NULL,
        data TEXT,
        tags TEXT,
        correlation_id TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );

    -- 实时统计表
    CREATE TABLE IF NOT EXISTS realtime_stats (
        id TEXT PRIMARY KEY,
        stat_type TEXT NOT NULL,
        value REAL NOT NULL,
        timestamp REAL NOT NULL,
        metadata TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );

    -- Bug模式表
    CREATE TABLE IF NOT EXISTS bug_patterns (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        pattern_type TEXT NOT NULL,
        regex_patterns TEXT,
        keywords TEXT,
        severity_score REAL,
        frequency INTEGER DEFAULT 0,
        confidence REAL DEFAULT 0.0,
        last_seen REAL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );

    -- 异常告警表
    CREATE TABLE IF NOT EXISTS anomaly_alerts (
        id TEXT PRIMARY KEY,
        alert_type TEXT NOT NULL,
        severity TEXT NOT NULL,
        message TEXT NOT NULL,
        data TEXT,
        confidence REAL,
        timestamp REAL NOT NULL,
        acknowledged BOOLEAN DEFAULT FALSE,
        acknowledged_by TEXT,
        acknowledged_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
    """
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 执行schema
        cursor.executescript(schema_sql)
        
        conn.commit()
        conn.close()
        
        print("✅ Bug检测数据库初始化成功")
        
        # 验证表是否创建成功
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        conn.close()
        
        print(f"✅ 创建的表: {[table[0] for table in tables]}")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    init_bug_detection_db()
