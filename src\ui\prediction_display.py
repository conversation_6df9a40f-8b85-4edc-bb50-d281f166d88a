"""
预测结果展示优化组件
提供丰富的预测结果可视化和交互功能
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

def show_enhanced_prediction_results(prediction_data: Dict[str, Any], 
                                   historical_data: Optional[List[Dict]] = None):
    """
    显示增强的预测结果
    
    Args:
        prediction_data: 预测结果数据
        historical_data: 历史数据用于对比
    """
    
    if not prediction_data or 'error' in prediction_data:
        st.error(f"❌ 预测数据无效: {prediction_data.get('error', '未知错误')}")
        return
    
    # 主要预测结果展示
    show_main_prediction_summary(prediction_data)
    
    # 创建详细分析选项卡
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "📊 预测详情", "📈 置信度分析", "🎯 候选分析", 
        "📋 历史对比", "💡 预测建议"
    ])
    
    with tab1:
        show_prediction_details(prediction_data)
    
    with tab2:
        show_confidence_analysis(prediction_data)
    
    with tab3:
        show_candidate_analysis(prediction_data)
    
    with tab4:
        show_historical_comparison(prediction_data, historical_data)
    
    with tab5:
        show_prediction_recommendations(prediction_data)

def show_main_prediction_summary(prediction_data: Dict[str, Any]):
    """显示主要预测结果摘要"""
    st.markdown("### 🎯 预测结果摘要")
    
    # 主要指标展示
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        numbers = prediction_data.get('numbers', 'N/A')
        st.metric(
            label="🎯 推荐号码",
            value=numbers,
            help="基于智能融合算法的最佳预测号码"
        )
    
    with col2:
        confidence = prediction_data.get('confidence', 0)
        confidence_color = "normal"
        if confidence >= 0.7:
            confidence_color = "inverse"
        elif confidence <= 0.3:
            confidence_color = "off"
        
        st.metric(
            label="📊 预测置信度",
            value=f"{confidence:.1%}",
            help="预测结果的可信程度"
        )
    
    with col3:
        fusion_score = prediction_data.get('fusion_score', 0)
        st.metric(
            label="⚖️ 融合分数",
            value=f"{fusion_score:.3f}",
            help="多模型融合后的综合评分"
        )
    
    with col4:
        candidates_count = len(prediction_data.get('candidates', []))
        st.metric(
            label="📋 候选数量",
            value=candidates_count,
            help="生成的候选号码总数"
        )
    
    # 预测质量指示器
    show_prediction_quality_indicator(prediction_data)

def show_prediction_quality_indicator(prediction_data: Dict[str, Any]):
    """显示预测质量指示器"""
    st.markdown("#### 🔍 预测质量评估")
    
    confidence = prediction_data.get('confidence', 0)
    fusion_score = prediction_data.get('fusion_score', 0)
    candidates_count = len(prediction_data.get('candidates', []))
    
    # 计算综合质量分数
    quality_score = (confidence * 0.4 + 
                    min(fusion_score, 1.0) * 0.3 + 
                    min(candidates_count / 20, 1.0) * 0.3)
    
    # 质量等级
    if quality_score >= 0.8:
        quality_level = "优秀"
        quality_color = "🟢"
    elif quality_score >= 0.6:
        quality_level = "良好"
        quality_color = "🟡"
    elif quality_score >= 0.4:
        quality_level = "一般"
        quality_color = "🟠"
    else:
        quality_level = "较低"
        quality_color = "🔴"
    
    col1, col2 = st.columns([1, 3])
    
    with col1:
        st.markdown(f"**质量等级:** {quality_color} {quality_level}")
        st.markdown(f"**综合分数:** {quality_score:.1%}")
    
    with col2:
        # 质量分数进度条
        progress_bar = st.progress(quality_score)
        
        # 质量建议
        if quality_score >= 0.8:
            st.success("✅ 预测质量优秀，建议重点关注")
        elif quality_score >= 0.6:
            st.info("ℹ️ 预测质量良好，可以参考")
        elif quality_score >= 0.4:
            st.warning("⚠️ 预测质量一般，谨慎参考")
        else:
            st.error("❌ 预测质量较低，不建议参考")

def show_prediction_details(prediction_data: Dict[str, Any]):
    """显示预测详情"""
    st.markdown("### 📊 预测详情分析")
    
    # 融合信息
    fusion_info = prediction_data.get('fusion_info', {})
    if fusion_info:
        st.markdown("#### 🔄 融合信息")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**参与模型:**")
            participating_models = fusion_info.get('participating_models', [])
            for model in participating_models:
                st.write(f"• {model}")
        
        with col2:
            st.markdown("**模型权重:**")
            model_weights = fusion_info.get('model_weights', {})
            for model, weight in model_weights.items():
                st.write(f"• {model}: {weight:.1%}")
        
        # 权重分布可视化
        if model_weights:
            fig = px.pie(
                values=list(model_weights.values()),
                names=list(model_weights.keys()),
                title="模型权重分布"
            )
            st.plotly_chart(fig, use_container_width=True)
    
    # 模型贡献
    model_contributions = prediction_data.get('model_contributions', [])
    if model_contributions:
        st.markdown("#### 🤝 模型贡献分析")
        
        contrib_data = []
        for contrib in model_contributions:
            contrib_data.append({
                '模型': contrib.get('model', 'N/A'),
                '置信度': f"{contrib.get('confidence', 0):.1%}",
                '权重': f"{contrib.get('weight', 0):.3f}",
                '贡献度': f"{contrib.get('contribution', 0):.3f}"
            })
        
        df_contrib = pd.DataFrame(contrib_data)
        st.dataframe(df_contrib, use_container_width=True)
        
        # 贡献度可视化
        fig = px.bar(
            df_contrib,
            x='模型',
            y='贡献度',
            color='置信度',
            title="模型贡献度分析"
        )
        st.plotly_chart(fig, use_container_width=True)

def show_confidence_analysis(prediction_data: Dict[str, Any]):
    """显示置信度分析"""
    st.markdown("### 📈 置信度分析")
    
    main_confidence = prediction_data.get('confidence', 0)
    candidates = prediction_data.get('candidates', [])
    
    if candidates:
        # 候选置信度分布
        confidences = [c.get('confidence', 0) for c in candidates]
        
        col1, col2 = st.columns(2)
        
        with col1:
            # 置信度统计
            st.markdown("#### 📊 置信度统计")
            st.metric("最高置信度", f"{max(confidences):.1%}")
            st.metric("平均置信度", f"{np.mean(confidences):.1%}")
            st.metric("最低置信度", f"{min(confidences):.1%}")
            st.metric("置信度标准差", f"{np.std(confidences):.3f}")
        
        with col2:
            # 置信度分布图
            fig = px.histogram(
                x=confidences,
                nbins=10,
                title="候选号码置信度分布",
                labels={'x': '置信度', 'y': '频次'}
            )
            st.plotly_chart(fig, use_container_width=True)
        
        # 置信度区间分析
        st.markdown("#### 📋 置信度区间分析")
        
        confidence_ranges = {
            '高置信度 (≥70%)': [c for c in confidences if c >= 0.7],
            '中等置信度 (50%-70%)': [c for c in confidences if 0.5 <= c < 0.7],
            '低置信度 (30%-50%)': [c for c in confidences if 0.3 <= c < 0.5],
            '极低置信度 (<30%)': [c for c in confidences if c < 0.3]
        }
        
        range_data = []
        for range_name, range_confidences in confidence_ranges.items():
            range_data.append({
                '置信度区间': range_name,
                '候选数量': len(range_confidences),
                '占比': f"{len(range_confidences) / len(confidences):.1%}" if confidences else "0%",
                '平均置信度': f"{np.mean(range_confidences):.1%}" if range_confidences else "N/A"
            })
        
        df_ranges = pd.DataFrame(range_data)
        st.dataframe(df_ranges, use_container_width=True)

def show_candidate_analysis(prediction_data: Dict[str, Any]):
    """显示候选分析"""
    st.markdown("### 🎯 候选号码分析")
    
    candidates = prediction_data.get('candidates', [])
    
    if not candidates:
        st.warning("无候选号码数据")
        return
    
    # 候选号码表格
    st.markdown("#### 📋 候选号码列表")
    
    # 添加筛选选项
    col1, col2, col3 = st.columns(3)
    
    with col1:
        min_confidence = st.slider(
            "最低置信度",
            min_value=0.0,
            max_value=1.0,
            value=0.0,
            step=0.1
        )
    
    with col2:
        max_candidates = st.number_input(
            "显示数量",
            min_value=5,
            max_value=len(candidates),
            value=min(20, len(candidates))
        )
    
    with col3:
        sort_by = st.selectbox(
            "排序方式",
            ["置信度", "融合分数", "支持模型数"]
        )
    
    # 筛选和排序候选
    filtered_candidates = [
        c for c in candidates 
        if c.get('confidence', 0) >= min_confidence
    ][:max_candidates]
    
    if filtered_candidates:
        # 构建候选数据表
        candidate_data = []
        for i, candidate in enumerate(filtered_candidates):
            candidate_data.append({
                '排名': i + 1,
                '号码': candidate.get('numbers', 'N/A'),
                '置信度': f"{candidate.get('confidence', 0):.1%}",
                '融合分数': f"{candidate.get('fusion_score', 0):.3f}",
                '支持模型数': candidate.get('supporting_models', 0),
                '策略': candidate.get('strategy', 'N/A')
            })
        
        df_candidates = pd.DataFrame(candidate_data)
        st.dataframe(df_candidates, use_container_width=True)
        
        # 候选分析图表
        show_candidate_charts(filtered_candidates)
    else:
        st.warning(f"没有满足置信度阈值 {min_confidence:.1%} 的候选号码")

def show_candidate_charts(candidates: List[Dict[str, Any]]):
    """显示候选分析图表"""
    st.markdown("#### 📊 候选分析图表")
    
    # 准备数据
    numbers = [c.get('numbers', 'N/A') for c in candidates[:10]]  # 只显示前10个
    confidences = [c.get('confidence', 0) for c in candidates[:10]]
    fusion_scores = [c.get('fusion_score', 0) for c in candidates[:10]]
    
    # 创建子图
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('置信度排名', '融合分数排名', '置信度vs融合分数', '号码分布'),
        specs=[[{"secondary_y": False}, {"secondary_y": False}],
               [{"secondary_y": False}, {"secondary_y": False}]]
    )
    
    # 置信度排名
    fig.add_trace(
        go.Bar(x=numbers, y=confidences, name='置信度', marker_color='blue'),
        row=1, col=1
    )
    
    # 融合分数排名
    fig.add_trace(
        go.Bar(x=numbers, y=fusion_scores, name='融合分数', marker_color='green'),
        row=1, col=2
    )
    
    # 置信度vs融合分数散点图
    fig.add_trace(
        go.Scatter(x=confidences, y=fusion_scores, mode='markers+text',
                  text=numbers, textposition="top center",
                  name='候选号码', marker_color='red'),
        row=2, col=1
    )
    
    # 号码数字分布
    all_digits = []
    for number in numbers:
        if len(str(number)) == 3:
            all_digits.extend([int(d) for d in str(number)])
    
    if all_digits:
        digit_counts = pd.Series(all_digits).value_counts().sort_index()
        fig.add_trace(
            go.Bar(x=digit_counts.index, y=digit_counts.values, 
                  name='数字频次', marker_color='orange'),
            row=2, col=2
        )
    
    fig.update_layout(height=600, showlegend=False, title_text="候选号码综合分析")
    st.plotly_chart(fig, use_container_width=True)

def show_historical_comparison(prediction_data: Dict[str, Any], 
                             historical_data: Optional[List[Dict]] = None):
    """显示历史对比"""
    st.markdown("### 📋 历史对比分析")
    
    if not historical_data:
        st.info("暂无历史数据进行对比")
        return
    
    # 模拟历史预测准确率数据
    st.markdown("#### 📈 历史预测表现")
    
    # 创建模拟的历史表现数据
    dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='D')
    historical_accuracy = np.random.normal(0.15, 0.05, len(dates))
    historical_accuracy = np.clip(historical_accuracy, 0.05, 0.35)
    
    df_history = pd.DataFrame({
        '日期': dates,
        '预测准确率': historical_accuracy
    })
    
    # 历史准确率趋势图
    fig = px.line(
        df_history,
        x='日期',
        y='预测准确率',
        title='历史预测准确率趋势'
    )
    
    # 添加当前预测置信度线
    current_confidence = prediction_data.get('confidence', 0)
    fig.add_hline(
        y=current_confidence,
        line_dash="dash",
        line_color="red",
        annotation_text=f"当前预测置信度: {current_confidence:.1%}"
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 历史统计摘要
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        avg_accuracy = df_history['预测准确率'].mean()
        st.metric("平均准确率", f"{avg_accuracy:.1%}")
    
    with col2:
        max_accuracy = df_history['预测准确率'].max()
        st.metric("最高准确率", f"{max_accuracy:.1%}")
    
    with col3:
        min_accuracy = df_history['预测准确率'].min()
        st.metric("最低准确率", f"{min_accuracy:.1%}")
    
    with col4:
        std_accuracy = df_history['预测准确率'].std()
        st.metric("准确率标准差", f"{std_accuracy:.3f}")

def show_prediction_recommendations(prediction_data: Dict[str, Any]):
    """显示预测建议"""
    st.markdown("### 💡 预测建议")
    
    confidence = prediction_data.get('confidence', 0)
    fusion_score = prediction_data.get('fusion_score', 0)
    candidates_count = len(prediction_data.get('candidates', []))
    
    # 基于预测质量给出建议
    recommendations = []
    
    if confidence >= 0.7:
        recommendations.append("🎯 **高置信度预测** - 建议重点关注推荐号码")
    elif confidence >= 0.5:
        recommendations.append("📊 **中等置信度预测** - 可以作为参考，建议结合其他分析")
    else:
        recommendations.append("⚠️ **低置信度预测** - 建议谨慎参考，可能需要更多数据")
    
    if fusion_score >= 0.8:
        recommendations.append("⚖️ **高融合分数** - 多模型高度一致，预测可靠性较高")
    elif fusion_score >= 0.5:
        recommendations.append("🔄 **中等融合分数** - 模型间存在一定分歧，建议综合考虑")
    else:
        recommendations.append("🤔 **低融合分数** - 模型间分歧较大，建议等待更多信息")
    
    if candidates_count >= 15:
        recommendations.append("📋 **丰富候选池** - 候选号码充足，可以进行多样化选择")
    elif candidates_count >= 8:
        recommendations.append("📝 **适中候选池** - 候选数量合理，建议重点关注前几名")
    else:
        recommendations.append("📉 **有限候选池** - 候选较少，建议谨慎选择")
    
    # 显示建议
    for recommendation in recommendations:
        st.markdown(recommendation)
    
    # 投注建议
    st.markdown("#### 🎲 投注建议")
    
    if confidence >= 0.6 and fusion_score >= 0.6:
        st.success("✅ **建议投注** - 预测质量较高，可以考虑适量投注")
        st.markdown("**建议策略:**")
        st.markdown("- 重点关注推荐号码")
        st.markdown("- 可以考虑前3-5个候选号码")
        st.markdown("- 建议分散投注，控制风险")
    elif confidence >= 0.4 or fusion_score >= 0.4:
        st.warning("⚠️ **谨慎投注** - 预测质量一般，建议小额试探")
        st.markdown("**建议策略:**")
        st.markdown("- 小额投注，控制风险")
        st.markdown("- 重点关注高置信度候选")
        st.markdown("- 结合其他分析方法")
    else:
        st.error("❌ **不建议投注** - 预测质量较低，建议观望")
        st.markdown("**建议策略:**")
        st.markdown("- 暂时观望，等待更好机会")
        st.markdown("- 收集更多数据进行分析")
        st.markdown("- 关注模型表现变化")
    
    # 风险提示
    st.markdown("#### ⚠️ 风险提示")
    st.warning("""
    **重要提醒:**
    - 彩票具有随机性，任何预测都不能保证中奖
    - 请理性投注，量力而行
    - 本预测仅供参考，不构成投注建议
    - 请遵守相关法律法规，适度娱乐
    """)
