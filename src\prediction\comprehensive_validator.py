#!/usr/bin/env python3
"""
综合验证体系
集成所有验证机制，建立完整的预测质量评估和持续改进体系
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional

from .prediction_validator import PredictionValidator
from .prediction_monitor import PredictionQualityMonitor
from .genetic_optimizer import GeneticOptimizer
from .hmm_randomness import HMMRandomnessModeler


class ComprehensiveValidator:
    """综合验证器"""
    
    def __init__(self, cache_dir: str = "data/cache"):
        """
        初始化综合验证器
        
        Args:
            cache_dir: 缓存目录
        """
        self.cache_dir = cache_dir
        
        # 确保缓存目录存在
        os.makedirs(cache_dir, exist_ok=True)
        
        # 初始化各个验证组件
        self.validator = PredictionValidator()
        self.monitor = PredictionQualityMonitor(cache_dir)
        self.optimizer = GeneticOptimizer(cache_dir=cache_dir)
        self.hmm_modeler = HMMRandomnessModeler(cache_dir)
        
        # 验证配置
        self.validation_config = {
            'diversity_threshold': 0.7,
            'exploration_threshold': 0.15,
            'stability_threshold': 0.01,
            'quality_threshold': 60.0,
            'consecutive_failures_limit': 5
        }
    
    def comprehensive_validation(self, predictions: List[Dict[str, Any]], 
                               historical_data: List[str],
                               test_name: str = "comprehensive_test") -> Dict[str, Any]:
        """
        执行综合验证
        
        Args:
            predictions: 预测结果列表
            historical_data: 历史数据
            test_name: 测试名称
            
        Returns:
            综合验证报告
        """
        print(f"🔍 开始综合验证: {test_name}")
        print("=" * 60)
        
        validation_result = {
            'test_name': test_name,
            'timestamp': datetime.now().isoformat(),
            'input_data': {
                'predictions_count': len(predictions),
                'historical_data_count': len(historical_data)
            },
            'validation_results': {},
            'overall_assessment': {},
            'recommendations': [],
            'action_items': []
        }
        
        try:
            # 1. 多样性验证
            print("1️⃣ 执行多样性验证...")
            diversity_result = self._validate_diversity(predictions)
            validation_result['validation_results']['diversity'] = diversity_result
            
            # 2. 变化性验证
            print("2️⃣ 执行变化性验证...")
            variability_result = self._validate_variability(predictions)
            validation_result['validation_results']['variability'] = variability_result
            
            # 3. 质量监控
            print("3️⃣ 执行质量监控...")
            monitoring_result = self._execute_monitoring(predictions)
            validation_result['validation_results']['monitoring'] = monitoring_result
            
            # 4. HMM状态分析
            print("4️⃣ 执行HMM状态分析...")
            hmm_result = self._analyze_hmm_states(historical_data)
            validation_result['validation_results']['hmm_analysis'] = hmm_result
            
            # 5. 综合评估
            print("5️⃣ 生成综合评估...")
            overall_assessment = self._generate_overall_assessment(validation_result['validation_results'])
            validation_result['overall_assessment'] = overall_assessment
            
            # 6. 生成建议和行动项
            recommendations, action_items = self._generate_recommendations_and_actions(validation_result)
            validation_result['recommendations'] = recommendations
            validation_result['action_items'] = action_items
            
            # 7. 保存验证报告
            self._save_comprehensive_report(validation_result)
            
            print("✅ 综合验证完成")
            
        except Exception as e:
            validation_result['error'] = f"综合验证失败: {e}"
            print(f"❌ 综合验证失败: {e}")
        
        return validation_result
    
    def _validate_diversity(self, predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """多样性验证"""
        try:
            # 提取预测号码
            numbers = [p.get('numbers') for p in predictions if 'numbers' in p]
            
            if not numbers:
                return {'error': '没有有效的预测号码'}
            
            # 执行多样性验证
            diversity_report = self.validator.validate_prediction_diversity(numbers, "diversity_validation")
            
            return {
                'success': True,
                'report': diversity_report,
                'key_metrics': {
                    'simpson_diversity_index': diversity_report.get('simpson_diversity_index', 0),
                    'exploration_rate': diversity_report.get('exploration_rate', 0),
                    'diversity_score': diversity_report.get('diversity_score', 0),
                    'quality_level': diversity_report.get('quality_level', 'unknown')
                }
            }
            
        except Exception as e:
            return {'error': f'多样性验证失败: {e}'}
    
    def _validate_variability(self, predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """变化性验证"""
        try:
            # 执行变化性验证
            variability_report = self.validator.validate_prediction_variability(predictions, "variability_validation")
            
            return {
                'success': True,
                'report': variability_report,
                'key_metrics': {
                    'number_variability_ratio': variability_report.get('number_variability_ratio', 0),
                    'confidence_variance': variability_report.get('confidence_variance', 0),
                    'quality_score': variability_report.get('quality_score', 0)
                }
            }
            
        except Exception as e:
            return {'error': f'变化性验证失败: {e}'}
    
    def _execute_monitoring(self, predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """执行质量监控"""
        try:
            # 提取预测号码
            numbers = [p.get('numbers') for p in predictions if 'numbers' in p]
            
            if not numbers:
                return {'error': '没有有效的预测号码'}
            
            # 执行质量监控
            monitoring_report = self.monitor.monitor_prediction_quality(numbers, "comprehensive_monitoring")
            
            return {
                'success': True,
                'report': monitoring_report,
                'key_metrics': {
                    'need_retrain': monitoring_report.get('need_retrain', False),
                    'alerts_count': len(monitoring_report.get('alerts', [])),
                    'quality_trend': monitoring_report.get('quality_trend', {})
                }
            }
            
        except Exception as e:
            return {'error': f'质量监控失败: {e}'}
    
    def _analyze_hmm_states(self, historical_data: List[str]) -> Dict[str, Any]:
        """HMM状态分析"""
        try:
            # 训练HMM模型
            training_result = self.hmm_modeler.train_hmm(historical_data)
            
            if not training_result.get('success'):
                return {'error': 'HMM模型训练失败'}
            
            # 预测当前状态
            current_state = self.hmm_modeler.predict_current_state(historical_data, window_size=10)
            
            return {
                'success': True,
                'training_result': training_result,
                'current_state': current_state,
                'key_metrics': {
                    'current_state_name': current_state.get('current_state', 'unknown'),
                    'state_analysis': training_result.get('state_analysis', {}),
                    'observations_count': training_result.get('observations_count', 0)
                }
            }
            
        except Exception as e:
            return {'error': f'HMM状态分析失败: {e}'}
    
    def _generate_overall_assessment(self, validation_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成综合评估"""
        assessment = {
            'overall_score': 0.0,
            'quality_level': 'unknown',
            'critical_issues': [],
            'strengths': [],
            'areas_for_improvement': []
        }
        
        scores = []
        
        # 多样性评估
        diversity_result = validation_results.get('diversity', {})
        if diversity_result.get('success'):
            diversity_score = diversity_result['key_metrics'].get('diversity_score', 0) * 100
            scores.append(diversity_score)
            
            if diversity_score >= 70:
                assessment['strengths'].append("预测多样性良好")
            else:
                assessment['critical_issues'].append("预测多样性不足")
                assessment['areas_for_improvement'].append("提升预测算法的多样性")
        
        # 变化性评估
        variability_result = validation_results.get('variability', {})
        if variability_result.get('success'):
            variability_score = variability_result['key_metrics'].get('quality_score', 0)
            scores.append(variability_score)
            
            if variability_score >= 60:
                assessment['strengths'].append("预测变化性合理")
            else:
                assessment['critical_issues'].append("预测变化性不足")
                assessment['areas_for_improvement'].append("增强预测结果的变化性")
        
        # 监控评估
        monitoring_result = validation_results.get('monitoring', {})
        if monitoring_result.get('success'):
            need_retrain = monitoring_result['key_metrics'].get('need_retrain', False)
            alerts_count = monitoring_result['key_metrics'].get('alerts_count', 0)
            
            if not need_retrain and alerts_count == 0:
                assessment['strengths'].append("质量监控状态良好")
                scores.append(80)
            elif alerts_count <= 2:
                assessment['areas_for_improvement'].append("存在少量质量告警")
                scores.append(60)
            else:
                assessment['critical_issues'].append("质量监控发现多个问题")
                assessment['areas_for_improvement'].append("需要立即解决质量问题")
                scores.append(30)
        
        # HMM分析评估
        hmm_result = validation_results.get('hmm_analysis', {})
        if hmm_result.get('success'):
            assessment['strengths'].append("HMM状态分析正常")
            scores.append(70)
        else:
            assessment['areas_for_improvement'].append("HMM状态分析需要优化")
            scores.append(50)
        
        # 计算综合评分
        if scores:
            assessment['overall_score'] = sum(scores) / len(scores)
            
            if assessment['overall_score'] >= 80:
                assessment['quality_level'] = 'excellent'
            elif assessment['overall_score'] >= 60:
                assessment['quality_level'] = 'good'
            elif assessment['overall_score'] >= 40:
                assessment['quality_level'] = 'fair'
            else:
                assessment['quality_level'] = 'poor'
        
        return assessment
    
    def _generate_recommendations_and_actions(self, validation_result: Dict[str, Any]) -> tuple:
        """生成建议和行动项"""
        recommendations = []
        action_items = []
        
        overall_assessment = validation_result.get('overall_assessment', {})
        validation_results = validation_result.get('validation_results', {})
        
        # 基于综合评估生成建议
        quality_level = overall_assessment.get('quality_level', 'unknown')
        
        if quality_level == 'poor':
            recommendations.append("系统质量较差，建议立即进行全面重构")
            action_items.append("执行紧急系统优化计划")
        elif quality_level == 'fair':
            recommendations.append("系统质量一般，建议重点优化关键问题")
            action_items.append("制定系统改进计划")
        elif quality_level == 'good':
            recommendations.append("系统质量良好，建议持续优化")
            action_items.append("定期监控和微调")
        else:
            recommendations.append("系统质量优秀，保持当前策略")
            action_items.append("继续监控和维护")
        
        # 基于具体问题生成建议
        critical_issues = overall_assessment.get('critical_issues', [])
        
        if "预测多样性不足" in critical_issues:
            recommendations.append("增加预测算法的随机性，考虑引入温度参数")
            action_items.append("重构候选生成算法")
        
        if "预测变化性不足" in critical_issues:
            recommendations.append("优化数据敏感性，确保新数据影响预测结果")
            action_items.append("检查数据处理流程")
        
        # 基于监控结果生成建议
        monitoring_result = validation_results.get('monitoring', {})
        if monitoring_result.get('success'):
            need_retrain = monitoring_result['key_metrics'].get('need_retrain', False)
            if need_retrain:
                recommendations.append("质量监控建议重新训练模型")
                action_items.append("执行模型重训练")
        
        return recommendations, action_items
    
    def _save_comprehensive_report(self, validation_result: Dict[str, Any]) -> None:
        """保存综合验证报告"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"comprehensive_validation_{validation_result['test_name']}_{timestamp}.json"
            filepath = os.path.join(self.cache_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(validation_result, f, ensure_ascii=False, indent=2)
            
            print(f"📋 综合验证报告已保存: {filepath}")
            
        except Exception as e:
            print(f"保存综合验证报告失败: {e}")
    
    def quick_health_check(self, predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """快速健康检查"""
        try:
            # 提取预测号码
            numbers = [p.get('numbers') for p in predictions if 'numbers' in p]
            
            if not numbers:
                return {'status': 'error', 'message': '没有有效的预测号码'}
            
            # 快速多样性检查
            unique_count = len(set(numbers))
            total_count = len(numbers)
            diversity_ratio = unique_count / total_count if total_count > 0 else 0
            
            # 快速质量评估
            if diversity_ratio >= 0.7:
                status = 'healthy'
                message = '预测系统运行正常'
            elif diversity_ratio >= 0.3:
                status = 'warning'
                message = '预测多样性偏低，需要关注'
            else:
                status = 'critical'
                message = '预测多样性严重不足，需要立即处理'
            
            return {
                'status': status,
                'message': message,
                'diversity_ratio': diversity_ratio,
                'unique_predictions': unique_count,
                'total_predictions': total_count,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {'status': 'error', 'message': f'健康检查失败: {e}'}


def main():
    """主函数"""
    print("🔍 综合验证体系测试")
    print("=" * 60)
    
    # 创建综合验证器
    validator = ComprehensiveValidator()
    
    # 模拟测试数据
    test_predictions = [
        {'numbers': '123', 'confidence': 0.8},
        {'numbers': '456', 'confidence': 0.7},
        {'numbers': '789', 'confidence': 0.6},
        {'numbers': '012', 'confidence': 0.5},
        {'numbers': '345', 'confidence': 0.4}
    ]
    
    test_historical_data = [
        "123", "456", "789", "012", "345", "678", "901", "234", "567", "890"
    ]
    
    # 执行综合验证
    result = validator.comprehensive_validation(
        test_predictions, 
        test_historical_data, 
        "system_test"
    )
    
    # 显示结果
    if 'error' not in result:
        print(f"✅ 综合验证完成")
        print(f"总体评分: {result['overall_assessment'].get('overall_score', 0):.1f}")
        print(f"质量级别: {result['overall_assessment'].get('quality_level', 'unknown')}")
    else:
        print(f"❌ 综合验证失败: {result['error']}")


if __name__ == "__main__":
    main()
