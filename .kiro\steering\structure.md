# 项目结构和组织

## 项目根目录结构

```
3dyuce/
├── src/                    # 源代码目录
├── data/                   # 数据文件目录
├── tests/                  # 测试代码目录
├── docs/                   # 文档目录
├── scripts/                # 脚本文件目录
├── backup/                 # 备份文件目录
├── logs/                   # 日志文件目录
├── venv/                   # 虚拟环境目录
├── .kiro/                  # Kiro配置目录
├── pyproject.toml          # 项目配置文件
├── requirements-prod.txt   # 生产环境依赖
├── README.md              # 项目说明文档
└── start_*.py             # 启动脚本
```

## 源代码结构 (src/)

### 核心架构
```
src/
├── api/                    # API服务层
│   ├── main.py            # 开发版API（已弃用）
│   ├── production_main.py # 生产版API（推荐使用）
│   ├── models.py          # API数据模型
│   └── dependencies.py   # API依赖注入
├── core/                  # 核心业务逻辑
│   ├── database.py        # 数据库管理（兼容性包装）
│   ├── database_manager.py # 增强数据库管理器
│   ├── data_engine.py     # 数据引擎
│   └── polars_engine.py   # Polars高性能引擎
├── data/                  # 数据处理模块
│   ├── collector.py       # 数据采集器
│   ├── parser.py          # 数据解析器
│   ├── cleaner.py         # 数据清洗器
│   └── models.py          # 数据模型定义
├── prediction/            # 预测模块
│   ├── base_predictor.py  # 预测器基类
│   ├── statistical_predictor.py # 统计学预测
│   ├── adaptive_fusion.py # 自适应融合系统
│   ├── intelligent_fusion.py # 智能融合系统
│   ├── advanced_features.py # 高级特征工程
│   └── prediction_service.py # 预测服务
├── ui/                    # Streamlit用户界面
│   ├── main.py           # 主界面入口
│   ├── components/       # UI组件
│   │   ├── navigation.py # 导航组件
│   │   ├── page_manager.py # 页面管理器
│   │   ├── user_preferences.py # 用户偏好管理
│   │   ├── error_handler.py # 错误处理组件
│   │   └── websocket_client.py # WebSocket客户端
│   ├── pages/            # 页面模块
│   │   ├── data_overview.py # 数据概览页面
│   │   ├── prediction_analysis.py # 预测分析页面
│   │   └── intelligent_fusion_optimization.py # 智能融合页面
│   ├── data_update_components.py # 数据更新组件
│   └── intelligent_fusion_components.py # 智能融合组件
├── services/              # 服务模块
│   └── data_update_service.py # 数据更新服务
├── scheduler/             # 调度模块
│   └── task_scheduler.py  # 任务调度器
├── bug_detection/         # Bug检测系统
│   ├── monitoring/        # 监控模块
│   └── reporting/         # 报告模块
└── utils/                 # 工具函数
    ├── helpers.py         # 通用帮助函数
    └── validators.py      # 数据验证器
```

## 数据目录结构 (data/)

```
data/
├── lottery.db             # 主数据库文件
├── lottery.db-shm         # SQLite共享内存文件
├── lottery.db-wal         # SQLite预写日志文件
├── bug_detection.db       # Bug检测数据库
├── model_library.db       # 模型库数据库
├── scheduler.db           # 调度器数据库
├── backups/               # 数据库备份
│   ├── lottery_backup_*.db
│   └── automated_backups/
├── cache/                 # 缓存文件
│   ├── predictions/       # 预测结果缓存
│   ├── statistics/        # 统计数据缓存
│   └── features/          # 特征数据缓存
├── logs/                  # 日志文件
│   ├── error_*.log        # 错误日志
│   ├── access_*.log       # 访问日志
│   └── performance_*.log  # 性能日志
├── raw/                   # 原始数据
│   └── external_data/     # 外部数据源
├── processed/             # 处理后数据
│   ├── features/          # 特征数据
│   └── models/            # 模型数据
└── validation_reports/    # 验证报告
    └── data_quality/      # 数据质量报告
```

## 测试目录结构 (tests/)

```
tests/
├── unit/                  # 单元测试
│   ├── test_database.py   # 数据库测试
│   ├── test_prediction.py # 预测功能测试
│   └── test_api.py        # API测试
├── integration/           # 集成测试
│   ├── test_data_flow.py  # 数据流测试
│   └── test_ui_api.py     # UI-API集成测试
├── performance/           # 性能测试
│   ├── test_response_time.py # 响应时间测试
│   └── test_load.py       # 负载测试
└── fixtures/              # 测试数据
    ├── sample_data.json   # 样本数据
    └── mock_responses.py  # 模拟响应
```

## 文档目录结构 (docs/)

```
docs/
├── API文档.md             # API接口文档
├── USER_GUIDE.md          # 用户使用指南
├── SYSTEM_ARCHITECTURE.md # 系统架构文档
├── OPERATIONS_MANUAL.md   # 运维手册
├── auto_update_user_guide.md # 自动更新指南
└── project_completion_report.md # 项目完成报告
```

## 配置和脚本

### 启动脚本
```
start_production_api.py    # 启动生产API服务
start_streamlit.py         # 启动Streamlit界面
start.bat                  # Windows一键启动脚本
一键启动.bat               # 中文一键启动脚本
```

### 测试和验证脚本
```
quick_api_test.py          # 快速API测试
comprehensive_test.py      # 综合测试
final_acceptance_report.py # 最终验收测试
performance_benchmark.py   # 性能基准测试
```

### 数据管理脚本
```
check_database.py          # 数据库健康检查
backup_database.py         # 数据库备份
test_data_update_complete.py # 数据更新测试
```

## 重要文件说明

### 配置文件
- **pyproject.toml**: 项目元数据、依赖管理、工具配置
- **requirements-prod.txt**: 生产环境专用依赖
- **scheduler_config.json**: 调度器配置
- **mcp.json**: MCP工具配置

### 核心入口文件
- **src/api/production_main.py**: API服务主入口（推荐）
- **src/ui/main.py**: Streamlit界面主入口
- **src/core/database_manager.py**: 数据库管理核心

### 关键业务文件
- **src/prediction/intelligent_fusion.py**: 智能融合核心算法
- **src/data/collector.py**: 数据采集核心逻辑
- **src/ui/components/navigation.py**: 导航系统核心

## 命名约定

### 目录命名
- 使用小写字母和下划线
- 功能相关的文件放在同一目录
- 按层次结构组织（api, core, ui等）

### 文件命名
- Python文件：snake_case.py
- 配置文件：lowercase.json/toml
- 文档文件：UPPERCASE.md 或 中文名.md
- 脚本文件：descriptive_name.py

### 模块导入路径
```python
# 从src根目录开始的绝对导入
from src.core.database_manager import EnhancedDatabaseManager
from src.prediction.intelligent_fusion import IntelligentFusionSystem
from src.ui.components.navigation import NavigationComponent
```

## 开发工作流

### 新功能开发
1. 在对应的src子目录创建模块
2. 编写单元测试在tests/unit/
3. 更新相关文档在docs/
4. 添加配置到pyproject.toml（如需要）

### 页面开发
1. 在src/ui/pages/创建页面模块
2. 在src/ui/components/注册导航
3. 在src/ui/main.py添加路由
4. 编写对应的测试

### API开发
1. 在src/api/添加端点
2. 在src/core/添加业务逻辑
3. 更新API文档
4. 编写集成测试

## 部署结构

### 生产环境文件
- 只包含src/目录和必要的配置文件
- 数据库文件在data/目录
- 日志输出到logs/目录
- 静态资源缓存在data/cache/

### 开发环境文件
- 包含完整的项目结构
- 测试文件和开发工具
- 详细的日志和调试信息