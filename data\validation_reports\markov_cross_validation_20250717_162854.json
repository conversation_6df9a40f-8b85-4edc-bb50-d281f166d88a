{"model_params": {"transition_window_size": 500, "probability_window_size": 250, "smoothing_alpha": 1.0}, "validation_params": {"k_folds": 2, "data_limit": 400}, "fold_results": [{"fold_idx": 0, "train_size": 200, "val_size": 200, "predictions_count": 200, "accuracy_metrics": {"exact_match": 0.0, "digit_accuracy": 0.25833333333333336, "position_accuracy": [0.095, 0.09, 0.12], "total_predictions": 200}, "diversity_metrics": {"simpson_diversity": 0.9907, "unique_ratio": 0.69, "entropy": 6.9405147372727445, "unique_count": 138, "total_count": 200}, "stability_metrics": {"variance": 98081.18497499998, "std_dev": 313.17915795116375, "coefficient_of_variation": 0.5920154969256695, "mean": 529.005}, "aic_bic": {"aic": 9430.340371976183, "bic": 9793.155282296466, "log_likelihood": -4605.170185988091, "num_params": 110, "num_samples": 200}}], "overall_results": {"accuracy_metrics": {"exact_match": 0.0, "digit_accuracy": 0.25833333333333336, "position_accuracy": [0.095, 0.09, 0.12], "total_predictions": 200}, "diversity_metrics": {"simpson_diversity": 0.9907, "unique_ratio": 0.69, "entropy": 6.9405147372727445, "unique_count": 138, "total_count": 200}, "stability_metrics": {"variance": 98081.18497499998, "std_dev": 313.17915795116375, "coefficient_of_variation": 0.5920154969256695, "mean": 529.005}, "aic_bic": {"aic": 9430.340371976183, "bic": 9793.155282296466, "log_likelihood": -4605.170185988091, "num_params": 110, "num_samples": 200}, "total_predictions": 200}, "metadata": {"generated_at": "2025-07-17T16:28:54.062666", "validator_version": "1.0", "database_path": "data\\lottery.db"}}