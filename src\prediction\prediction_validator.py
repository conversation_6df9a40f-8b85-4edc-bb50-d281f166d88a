#!/usr/bin/env python3
"""
预测质量验证机制
验证预测结果的合理变化性，检测异常固定模式，评估数据敏感性，提供科学性评估报告
"""

import json
import os
import statistics
import time
from collections import Counter, defaultdict
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple


class PredictionValidator:
    """预测质量验证器"""
    
    def __init__(self, validation_dir: str = "data/validation_reports"):
        """
        初始化验证器
        
        Args:
            validation_dir: 验证报告存储目录
        """
        self.validation_dir = validation_dir
        os.makedirs(validation_dir, exist_ok=True)
    
    def validate_prediction_variability(self, predictions: List[Dict[str, Any]], 
                                      test_name: str = "variability_test") -> Dict[str, Any]:
        """
        验证预测结果的变化性
        
        Args:
            predictions: 预测结果列表
            test_name: 测试名称
            
        Returns:
            验证报告
        """
        if not predictions:
            return {'error': '没有预测结果可验证'}
        
        # 提取预测号码和置信度
        numbers = [p.get('numbers', 'N/A') for p in predictions]
        confidences = [p.get('confidence', 0) for p in predictions if 'confidence' in p]
        
        # 分析号码变化性
        unique_numbers = set(numbers)
        number_variability = len(unique_numbers) / len(numbers) if numbers else 0
        
        # 分析置信度变化性
        confidence_variability = 0
        if len(confidences) > 1:
            confidence_std = statistics.stdev(confidences)
            confidence_mean = statistics.mean(confidences)
            confidence_variability = confidence_std / confidence_mean if confidence_mean > 0 else 0
        
        # 检测异常固定模式
        fixed_pattern_detected = number_variability < 0.1  # 如果90%以上结果相同
        
        # 生成评估结果
        assessment = {
            'test_name': test_name,
            'timestamp': datetime.now().isoformat(),
            'total_predictions': len(predictions),
            'unique_numbers_count': len(unique_numbers),
            'number_variability_ratio': number_variability,
            'confidence_variability_coefficient': confidence_variability,
            'fixed_pattern_detected': fixed_pattern_detected,
            'most_frequent_number': Counter(numbers).most_common(1)[0] if numbers else None,
            'confidence_range': {
                'min': min(confidences) if confidences else 0,
                'max': max(confidences) if confidences else 0,
                'mean': statistics.mean(confidences) if confidences else 0
            },
            'quality_score': self._calculate_quality_score(number_variability, confidence_variability, fixed_pattern_detected),
            'recommendations': self._generate_recommendations(number_variability, confidence_variability, fixed_pattern_detected)
        }
        
        return assessment
    
    def validate_data_sensitivity(self, predictions_before: List[Dict[str, Any]], 
                                predictions_after: List[Dict[str, Any]], 
                                data_change_description: str) -> Dict[str, Any]:
        """
        验证数据敏感性
        
        Args:
            predictions_before: 数据变化前的预测结果
            predictions_after: 数据变化后的预测结果
            data_change_description: 数据变化描述
            
        Returns:
            敏感性验证报告
        """
        if not predictions_before or not predictions_after:
            return {'error': '缺少对比预测结果'}
        
        # 提取预测号码
        numbers_before = [p.get('numbers', 'N/A') for p in predictions_before]
        numbers_after = [p.get('numbers', 'N/A') for p in predictions_after]
        
        # 提取置信度
        conf_before = [p.get('confidence', 0) for p in predictions_before if 'confidence' in p]
        conf_after = [p.get('confidence', 0) for p in predictions_after if 'confidence' in p]
        
        # 分析号码变化
        numbers_changed = set(numbers_before) != set(numbers_after)
        number_change_ratio = len(set(numbers_after) - set(numbers_before)) / len(set(numbers_before)) if numbers_before else 0
        
        # 分析置信度变化
        confidence_change = 0
        if conf_before and conf_after:
            avg_conf_before = statistics.mean(conf_before)
            avg_conf_after = statistics.mean(conf_after)
            confidence_change = abs(avg_conf_after - avg_conf_before) / avg_conf_before if avg_conf_before > 0 else 0
        
        # 评估敏感性
        sensitivity_level = self._assess_sensitivity_level(numbers_changed, number_change_ratio, confidence_change)
        
        sensitivity_report = {
            'test_name': 'data_sensitivity_test',
            'timestamp': datetime.now().isoformat(),
            'data_change_description': data_change_description,
            'predictions_before_count': len(predictions_before),
            'predictions_after_count': len(predictions_after),
            'numbers_changed': numbers_changed,
            'number_change_ratio': number_change_ratio,
            'confidence_change_ratio': confidence_change,
            'sensitivity_level': sensitivity_level,
            'sensitivity_score': self._calculate_sensitivity_score(numbers_changed, number_change_ratio, confidence_change),
            'detailed_analysis': {
                'numbers_before': list(set(numbers_before)),
                'numbers_after': list(set(numbers_after)),
                'new_numbers': list(set(numbers_after) - set(numbers_before)),
                'removed_numbers': list(set(numbers_before) - set(numbers_after)),
                'avg_confidence_before': statistics.mean(conf_before) if conf_before else 0,
                'avg_confidence_after': statistics.mean(conf_after) if conf_after else 0
            },
            'recommendations': self._generate_sensitivity_recommendations(sensitivity_level)
        }
        
        return sensitivity_report
    
    def detect_anomalous_patterns(self, predictions: List[Dict[str, Any]], 
                                 time_window_hours: int = 24) -> Dict[str, Any]:
        """
        检测异常预测模式
        
        Args:
            predictions: 预测结果列表（需包含时间戳）
            time_window_hours: 时间窗口（小时）
            
        Returns:
            异常检测报告
        """
        if not predictions:
            return {'error': '没有预测结果可检测'}
        
        # 按时间分组
        time_groups = defaultdict(list)
        current_time = datetime.now()
        
        for pred in predictions:
            pred_time = pred.get('timestamp')
            if pred_time:
                try:
                    pred_datetime = datetime.fromisoformat(pred_time)
                    hours_ago = (current_time - pred_datetime).total_seconds() / 3600
                    
                    if hours_ago <= time_window_hours:
                        time_groups[int(hours_ago)].append(pred)
                except:
                    continue
        
        # 检测异常模式
        anomalies = []
        
        # 1. 检测完全相同的预测结果
        all_numbers = [p.get('numbers') for p in predictions if p.get('numbers')]
        if len(set(all_numbers)) == 1 and len(all_numbers) > 5:
            anomalies.append({
                'type': 'identical_predictions',
                'severity': 'high',
                'description': f'所有{len(all_numbers)}个预测结果完全相同: {all_numbers[0]}',
                'recommendation': '检查预测算法是否存在固定逻辑或缓存问题'
            })
        
        # 2. 检测置信度异常
        confidences = [p.get('confidence', 0) for p in predictions if 'confidence' in p]
        if confidences:
            conf_std = statistics.stdev(confidences) if len(confidences) > 1 else 0
            if conf_std < 0.01:  # 置信度变化极小
                anomalies.append({
                    'type': 'low_confidence_variability',
                    'severity': 'medium',
                    'description': f'置信度变化极小，标准差: {conf_std:.4f}',
                    'recommendation': '检查置信度计算逻辑是否过于固定'
                })
        
        # 3. 检测时间相关异常
        for hour, hour_predictions in time_groups.items():
            if len(hour_predictions) > 3:
                hour_numbers = [p.get('numbers') for p in hour_predictions]
                if len(set(hour_numbers)) == 1:
                    anomalies.append({
                        'type': 'time_period_identical',
                        'severity': 'medium',
                        'description': f'{hour}小时前的{len(hour_predictions)}个预测完全相同',
                        'recommendation': '检查短时间内的预测是否应该有更多变化'
                    })
        
        anomaly_report = {
            'test_name': 'anomaly_detection',
            'timestamp': datetime.now().isoformat(),
            'time_window_hours': time_window_hours,
            'total_predictions_analyzed': len(predictions),
            'anomalies_detected': len(anomalies),
            'anomalies': anomalies,
            'overall_health_score': max(0, 100 - len(anomalies) * 20),  # 每个异常扣20分
            'recommendations': self._generate_anomaly_recommendations(anomalies)
        }
        
        return anomaly_report
    
    def generate_comprehensive_report(self, predictions: List[Dict[str, Any]], 
                                    comparison_predictions: List[Dict[str, Any]] = None,
                                    data_change_description: str = None) -> Dict[str, Any]:
        """
        生成综合质量评估报告
        
        Args:
            predictions: 当前预测结果
            comparison_predictions: 对比预测结果（用于敏感性测试）
            data_change_description: 数据变化描述
            
        Returns:
            综合评估报告
        """
        report = {
            'report_id': f"quality_report_{int(time.time())}",
            'timestamp': datetime.now().isoformat(),
            'summary': {},
            'detailed_tests': {}
        }
        
        # 1. 变化性验证
        variability_test = self.validate_prediction_variability(predictions, "comprehensive_variability")
        report['detailed_tests']['variability'] = variability_test
        
        # 2. 异常检测
        anomaly_test = self.detect_anomalous_patterns(predictions)
        report['detailed_tests']['anomaly_detection'] = anomaly_test
        
        # 3. 数据敏感性测试（如果有对比数据）
        if comparison_predictions and data_change_description:
            sensitivity_test = self.validate_data_sensitivity(
                comparison_predictions, predictions, data_change_description
            )
            report['detailed_tests']['data_sensitivity'] = sensitivity_test
        
        # 生成综合评分
        overall_score = self._calculate_overall_score(report['detailed_tests'])
        
        report['summary'] = {
            'overall_quality_score': overall_score,
            'quality_level': self._get_quality_level(overall_score),
            'key_findings': self._extract_key_findings(report['detailed_tests']),
            'priority_recommendations': self._extract_priority_recommendations(report['detailed_tests'])
        }
        
        # 保存报告
        self._save_report(report)
        
        return report
    
    def _calculate_quality_score(self, number_variability: float, 
                               confidence_variability: float, 
                               fixed_pattern: bool) -> float:
        """计算质量评分"""
        score = 100
        
        # 号码变化性评分
        if number_variability < 0.1:
            score -= 50  # 严重扣分
        elif number_variability < 0.3:
            score -= 20
        
        # 置信度变化性评分
        if confidence_variability < 0.01:
            score -= 20
        elif confidence_variability > 0.5:
            score -= 10  # 变化过大也不好
        
        # 固定模式惩罚
        if fixed_pattern:
            score -= 30
        
        return max(0, score)
    
    def _assess_sensitivity_level(self, numbers_changed: bool, 
                                number_change_ratio: float, 
                                confidence_change: float) -> str:
        """评估敏感性级别"""
        if numbers_changed and number_change_ratio > 0.3:
            return "high"
        elif numbers_changed or confidence_change > 0.1:
            return "medium"
        elif confidence_change > 0.01:
            return "low"
        else:
            return "none"
    
    def _calculate_sensitivity_score(self, numbers_changed: bool, 
                                   number_change_ratio: float, 
                                   confidence_change: float) -> float:
        """计算敏感性评分"""
        score = 0
        
        if numbers_changed:
            score += 50
        
        score += min(30, number_change_ratio * 100)
        score += min(20, confidence_change * 100)
        
        return min(100, score)
    
    def _calculate_overall_score(self, detailed_tests: Dict[str, Any]) -> float:
        """计算综合评分"""
        scores = []
        
        if 'variability' in detailed_tests:
            scores.append(detailed_tests['variability'].get('quality_score', 0))
        
        if 'anomaly_detection' in detailed_tests:
            scores.append(detailed_tests['anomaly_detection'].get('overall_health_score', 0))
        
        if 'data_sensitivity' in detailed_tests:
            scores.append(detailed_tests['data_sensitivity'].get('sensitivity_score', 0))
        
        return statistics.mean(scores) if scores else 0
    
    def _get_quality_level(self, score: float) -> str:
        """获取质量级别"""
        if score >= 80:
            return "excellent"
        elif score >= 60:
            return "good"
        elif score >= 40:
            return "fair"
        else:
            return "poor"
    
    def _extract_key_findings(self, detailed_tests: Dict[str, Any]) -> List[str]:
        """提取关键发现"""
        findings = []
        
        # 从各个测试中提取关键信息
        for test_name, test_result in detailed_tests.items():
            if test_name == 'variability':
                if test_result.get('fixed_pattern_detected'):
                    findings.append("检测到异常固定预测模式")
                if test_result.get('number_variability_ratio', 0) < 0.2:
                    findings.append("预测号码变化性不足")
            
            elif test_name == 'anomaly_detection':
                anomalies = test_result.get('anomalies', [])
                if anomalies:
                    findings.append(f"检测到{len(anomalies)}个异常模式")
            
            elif test_name == 'data_sensitivity':
                sensitivity_level = test_result.get('sensitivity_level', 'none')
                if sensitivity_level == 'none':
                    findings.append("数据敏感性不足")
                elif sensitivity_level == 'high':
                    findings.append("数据敏感性良好")
        
        return findings
    
    def _extract_priority_recommendations(self, detailed_tests: Dict[str, Any]) -> List[str]:
        """提取优先建议"""
        recommendations = []
        
        for test_result in detailed_tests.values():
            if 'recommendations' in test_result:
                if isinstance(test_result['recommendations'], list):
                    recommendations.extend(test_result['recommendations'][:2])  # 取前2个
                else:
                    recommendations.append(test_result['recommendations'])
        
        return list(set(recommendations))  # 去重
    
    def _generate_recommendations(self, number_variability: float, 
                                confidence_variability: float, 
                                fixed_pattern: bool) -> List[str]:
        """生成建议"""
        recommendations = []
        
        if fixed_pattern:
            recommendations.append("检查预测算法是否存在固定逻辑或缓存问题")
        
        if number_variability < 0.2:
            recommendations.append("增加预测算法的动态性和随机性")
        
        if confidence_variability < 0.01:
            recommendations.append("优化置信度计算逻辑，增加合理变化性")
        
        return recommendations
    
    def _generate_sensitivity_recommendations(self, sensitivity_level: str) -> List[str]:
        """生成敏感性建议"""
        if sensitivity_level == 'none':
            return ["增强数据变化检测机制", "优化模型对新数据的敏感性"]
        elif sensitivity_level == 'low':
            return ["适度增加数据敏感性", "检查数据变化是否正确传播到预测结果"]
        else:
            return ["数据敏感性表现良好"]
    
    def _generate_anomaly_recommendations(self, anomalies: List[Dict[str, Any]]) -> List[str]:
        """生成异常处理建议"""
        if not anomalies:
            return ["预测模式正常，继续监控"]
        
        recommendations = []
        for anomaly in anomalies:
            if anomaly.get('recommendation'):
                recommendations.append(anomaly['recommendation'])
        
        return list(set(recommendations))
    
    def _save_report(self, report: Dict[str, Any]) -> str:
        """保存报告"""
        report_file = os.path.join(self.validation_dir, f"{report['report_id']}.json")
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            return report_file
        except Exception as e:
            print(f"保存验证报告失败: {e}")
            return None

    def validate_prediction_diversity(self, predictions: List[str],
                                    test_name: str = "diversity_test") -> Dict[str, Any]:
        """
        验证预测结果多样性

        Args:
            predictions: 预测号码列表
            test_name: 测试名称

        Returns:
            多样性验证报告
        """
        if not predictions:
            return {'error': '没有预测结果可验证'}

        # 1. 计算辛普森多样性指数 (Simpson Diversity Index)
        counts = Counter(predictions)
        total = len(predictions)
        sdi = 1 - sum((count/total)**2 for count in counts.values())

        # 2. 计算形态探索率 (Exploration Rate)
        unique_patterns = len(set(predictions))
        exploration_rate = unique_patterns / total

        # 3. 计算多样性评分
        diversity_score = 0.6 * sdi + 0.4 * exploration_rate

        # 4. 确定质量级别
        if diversity_score >= 0.7:
            quality_level = 'excellent'
        elif diversity_score >= 0.5:
            quality_level = 'good'
        elif diversity_score >= 0.3:
            quality_level = 'fair'
        else:
            quality_level = 'poor'

        # 5. 生成改进建议
        recommendations = self._generate_diversity_recommendations(sdi, exploration_rate)

        # 6. 检测固定模式
        fixed_pattern_detected = self._detect_fixed_patterns(predictions)

        # 7. 计算质量分数 (0-100)
        quality_score = min(diversity_score * 100, 100)

        # 8. 生成验证报告
        report = {
            'test_name': test_name,
            'timestamp': datetime.now().isoformat(),
            'total_predictions': total,
            'unique_predictions': unique_patterns,
            'simpson_diversity_index': round(sdi, 4),
            'exploration_rate': round(exploration_rate, 4),
            'diversity_score': round(diversity_score, 4),
            'quality_level': quality_level,
            'quality_score': round(quality_score, 1),
            'fixed_pattern_detected': fixed_pattern_detected,
            'recommendations': recommendations,
            'detailed_analysis': {
                'number_frequency': dict(counts),
                'most_common': counts.most_common(5),
                'number_variability_ratio': exploration_rate
            }
        }

        # 9. 保存验证报告
        self._save_diversity_report(report)

        return report

    def _generate_diversity_recommendations(self, sdi: float, exploration_rate: float) -> List[str]:
        """
        生成多样性改进建议

        Args:
            sdi: 辛普森多样性指数
            exploration_rate: 探索率

        Returns:
            建议列表
        """
        recommendations = []

        if sdi < 0.6:
            recommendations.append("辛普森多样性指数偏低，建议增加预测算法的随机性")
            recommendations.append("考虑引入温度参数控制预测的随机程度")

        if exploration_rate < 0.15:
            recommendations.append("形态探索率不足，建议增加候选号码的多样性")
            recommendations.append("可以添加随机候选或使用概率采样方法")

        if sdi < 0.4 and exploration_rate < 0.1:
            recommendations.append("预测多样性严重不足，建议重构核心预测算法")
            recommendations.append("考虑使用马尔可夫链或其他概率性方法")

        if sdi > 0.8 and exploration_rate > 0.8:
            recommendations.append("多样性过高，可能缺乏预测的科学性")
            recommendations.append("建议平衡随机性和基于数据的预测逻辑")

        if not recommendations:
            recommendations.append("多样性表现良好，继续保持当前预测策略")

        return recommendations

    def _detect_fixed_patterns(self, predictions: List[str]) -> bool:
        """
        检测是否存在固定预测模式

        Args:
            predictions: 预测号码列表

        Returns:
            是否检测到固定模式
        """
        if len(predictions) < 3:
            return False

        # 检查是否有超过50%的预测是相同的
        counts = Counter(predictions)
        most_common_count = counts.most_common(1)[0][1]

        if most_common_count / len(predictions) > 0.5:
            return True

        # 检查是否存在明显的重复模式
        if len(set(predictions)) <= 2:
            return True

        return False

    def _save_diversity_report(self, report: Dict[str, Any]) -> None:
        """
        保存多样性验证报告

        Args:
            report: 验证报告
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"diversity_report_{report['test_name']}_{timestamp}.json"
            filepath = os.path.join(self.validation_dir, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            print(f"多样性验证报告已保存: {filepath}")

        except Exception as e:
            print(f"保存多样性报告失败: {e}")

    def validate_prediction_quality(self, predictions: List[Dict[str, Any]],
                                  test_name: str = "quality_test") -> Dict[str, Any]:
        """
        综合预测质量验证

        Args:
            predictions: 预测结果列表
            test_name: 测试名称

        Returns:
            综合质量报告
        """
        if not predictions:
            return {'error': '没有预测结果可验证'}

        # 提取预测号码
        numbers = [p.get('numbers', 'N/A') for p in predictions if 'numbers' in p]

        if not numbers:
            return {'error': '预测结果中没有有效的号码'}

        # 1. 多样性验证
        diversity_report = self.validate_prediction_diversity(numbers, f"{test_name}_diversity")

        # 2. 变化性验证
        variability_report = self.validate_prediction_variability(predictions, f"{test_name}_variability")

        # 3. 综合评分
        diversity_score = diversity_report.get('quality_score', 0)
        variability_score = variability_report.get('quality_score', 0)

        overall_score = (diversity_score + variability_score) / 2

        # 4. 综合质量级别
        if overall_score >= 80:
            overall_quality = 'excellent'
        elif overall_score >= 60:
            overall_quality = 'good'
        elif overall_score >= 40:
            overall_quality = 'fair'
        else:
            overall_quality = 'poor'

        # 5. 生成综合报告
        comprehensive_report = {
            'test_name': test_name,
            'timestamp': datetime.now().isoformat(),
            'overall_score': round(overall_score, 1),
            'overall_quality': overall_quality,
            'diversity_analysis': diversity_report,
            'variability_analysis': variability_report,
            'summary': {
                'total_predictions': len(predictions),
                'unique_numbers': len(set(numbers)),
                'key_issues': self._identify_key_issues(diversity_report, variability_report),
                'recommendations': self._generate_comprehensive_recommendations(diversity_report, variability_report)
            }
        }

        return comprehensive_report

    def _identify_key_issues(self, diversity_report: Dict[str, Any],
                           variability_report: Dict[str, Any]) -> List[str]:
        """识别关键问题"""
        issues = []

        if diversity_report.get('fixed_pattern_detected', False):
            issues.append("检测到固定预测模式")

        if diversity_report.get('simpson_diversity_index', 0) < 0.5:
            issues.append("预测多样性不足")

        if variability_report.get('number_variability_ratio', 0) < 0.3:
            issues.append("号码变化性偏低")

        if not issues:
            issues.append("未发现明显质量问题")

        return issues

    def _generate_comprehensive_recommendations(self, diversity_report: Dict[str, Any],
                                              variability_report: Dict[str, Any]) -> List[str]:
        """生成综合改进建议"""
        recommendations = []

        # 合并多样性和变化性建议
        diversity_recs = diversity_report.get('recommendations', [])
        variability_recs = variability_report.get('recommendations', [])

        all_recs = diversity_recs + variability_recs

        # 去重并返回
        return list(set(all_recs))
