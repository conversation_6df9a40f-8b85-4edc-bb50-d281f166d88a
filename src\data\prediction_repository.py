"""
预测数据访问层重构
实现PredictionRepository类，开发新的数据访问接口
"""

import json
import logging
import sqlite3
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple


@dataclass
class WinningRecord:
    """开奖记录数据结构"""
    period_number: str
    date: str
    numbers: str
    trial_numbers: str
    machine_number: str
    trial_machine_number: str
    sales_amount: float
    direct_bonus: float
    group3_bonus: float
    group6_bonus: float

@dataclass
class ModelPredictionRecord:
    """模型预测记录"""
    id: Optional[int] = None
    period_number: str = ""
    model_name: str = ""
    predicted_number: str = ""
    confidence: float = 0.0
    actual_number: Optional[str] = None
    is_hit: Optional[bool] = None
    prediction_date: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class FusionPredictionRecord:
    """融合预测记录"""
    id: Optional[int] = None
    period_number: str = ""
    best_number: str = ""
    confidence: float = 0.0
    fusion_method: str = ""
    model_weights: Dict[str, float] = None
    fusion_details: Dict[str, Any] = None
    prediction_date: Optional[datetime] = None
    actual_number: Optional[str] = None
    is_hit: Optional[bool] = None

class PredictionRepository:
    """预测数据访问仓库"""
    
    def __init__(self, db_path: str = "data/model_library.db", lottery_db_path: str = "data/lottery.db"):
        """
        初始化数据访问仓库
        
        Args:
            db_path: 模型库数据库路径
            lottery_db_path: 彩票数据库路径
        """
        self.db_path = db_path
        self.lottery_db_path = lottery_db_path
        self.logger = logging.getLogger(__name__)
    
    def save_model_prediction(self, prediction: ModelPredictionRecord) -> int:
        """
        保存模型预测结果

        Args:
            prediction: 模型预测记录

        Returns:
            预测记录ID
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 确保表存在
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS model_predictions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        period_number TEXT NOT NULL,
                        model_name TEXT NOT NULL,
                        predicted_number TEXT NOT NULL,
                        confidence REAL NOT NULL,
                        prediction_date TEXT NOT NULL,
                        metadata TEXT,
                        actual_number TEXT,
                        is_hit BOOLEAN,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # 检查并添加metadata列（如果不存在）
                try:
                    cursor.execute("ALTER TABLE model_predictions ADD COLUMN metadata TEXT")
                except sqlite3.OperationalError:
                    # 列已存在，忽略错误
                    pass

                # 插入记录（暂时不使用metadata字段）
                cursor.execute("""
                    INSERT INTO model_predictions
                    (period_number, model_name, predicted_number, confidence,
                     prediction_date, actual_number, is_hit)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    prediction.period_number,
                    prediction.model_name,
                    prediction.predicted_number,
                    prediction.confidence,
                    prediction.prediction_date.isoformat() if prediction.prediction_date else datetime.now().isoformat(),
                    prediction.actual_number,
                    prediction.is_hit
                ))

                prediction_id = cursor.lastrowid
                conn.commit()
                self.logger.info(f"保存模型预测成功: {prediction.model_name} - {prediction.period_number}, ID: {prediction_id}")
                return prediction_id or 0

        except Exception as e:
            self.logger.error(f"保存模型预测失败: {e}")
            raise
    
    def save_fusion_prediction(self, prediction: FusionPredictionRecord) -> bool:
        """
        保存融合预测结果
        
        Args:
            prediction: 融合预测记录
            
        Returns:
            是否保存成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 将字典转换为JSON字符串
                model_weights_json = json.dumps(prediction.model_weights or {})
                fusion_details_json = json.dumps(prediction.fusion_details or {})
                
                cursor.execute("""
                    INSERT OR REPLACE INTO fusion_predictions 
                    (period_number, best_number, confidence, fusion_method, 
                     model_weights, fusion_details, prediction_date, actual_number, is_hit)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    prediction.period_number,
                    prediction.best_number,
                    prediction.confidence,
                    prediction.fusion_method,
                    model_weights_json,
                    fusion_details_json,
                    prediction.prediction_date or datetime.now(),
                    prediction.actual_number,
                    prediction.is_hit
                ))
                
                conn.commit()
                self.logger.info(f"保存融合预测成功: {prediction.period_number}")
                return True
                
        except Exception as e:
            self.logger.error(f"保存融合预测失败: {e}")
            return False
    
    def save_prediction_ranking(self, period_number: str, ranking_data: List[Dict[str, Any]]) -> bool:
        """
        保存预测排行榜
        
        Args:
            period_number: 期号
            ranking_data: 排行榜数据列表
            
        Returns:
            是否保存成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 删除该期号的旧记录
                cursor.execute("DELETE FROM prediction_rankings WHERE period_number = ?", (period_number,))
                
                # 插入新记录
                for item in ranking_data:
                    cursor.execute("""
                        INSERT INTO prediction_rankings 
                        (period_number, number, rank, confidence, composite_score, 
                         model_support_count, historical_hit_rate, recommendation_level, prediction_method)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        period_number,
                        item['number'],
                        item['rank'],
                        item['confidence'],
                        item['composite_score'],
                        item['model_support_count'],
                        item['historical_hit_rate'],
                        item['recommendation_level'],
                        item['prediction_method']
                    ))
                
                conn.commit()
                self.logger.info(f"保存排行榜成功: {period_number}, 记录数: {len(ranking_data)}")
                return True
                
        except Exception as e:
            self.logger.error(f"保存排行榜失败: {e}")
            return False
    
    def get_model_predictions(self, model_name: str, window_size: int = 50) -> List[ModelPredictionRecord]:
        """
        获取模型历史预测
        
        Args:
            model_name: 模型名称
            window_size: 获取记录数量
            
        Returns:
            模型预测记录列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT id, period_number, model_name, predicted_number, confidence, 
                           actual_number, is_hit, prediction_date
                    FROM model_predictions 
                    WHERE model_name = ?
                    ORDER BY prediction_date DESC 
                    LIMIT ?
                """, (model_name, window_size))
                
                records = []
                for row in cursor.fetchall():
                    record = ModelPredictionRecord(
                        id=row[0],
                        period_number=row[1],
                        model_name=row[2],
                        predicted_number=row[3],
                        confidence=row[4],
                        actual_number=row[5],
                        is_hit=row[6],
                        prediction_date=datetime.fromisoformat(row[7]) if row[7] else None
                    )
                    records.append(record)
                
                return records
                
        except Exception as e:
            self.logger.error(f"获取模型预测失败: {e}")
            return []
    
    def get_recent_winning_numbers(self, count: int = 100) -> List[WinningRecord]:
        """
        获取最近的开奖号码
        
        Args:
            count: 获取记录数量
            
        Returns:
            开奖记录列表
        """
        try:
            with sqlite3.connect(self.lottery_db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT period, date, numbers, trial_numbers, machine_number, 
                           trial_machine_number, sales_amount, direct_bonus, group3_bonus, group6_bonus
                    FROM lottery_records 
                    ORDER BY date DESC 
                    LIMIT ?
                """, (count,))
                
                records = []
                for row in cursor.fetchall():
                    record = WinningRecord(
                        period_number=row[0],
                        date=row[1],
                        numbers=row[2],
                        trial_numbers=row[3],
                        machine_number=row[4],
                        trial_machine_number=row[5],
                        sales_amount=row[6],
                        direct_bonus=row[7],
                        group3_bonus=row[8],
                        group6_bonus=row[9]
                    )
                    records.append(record)
                
                return records
                
        except Exception as e:
            self.logger.error(f"获取开奖记录失败: {e}")
            return []
    
    def get_fusion_predictions(self, period_number: Optional[str] = None, limit: int = 10) -> List[FusionPredictionRecord]:
        """
        获取融合预测记录
        
        Args:
            period_number: 期号（可选）
            limit: 限制数量
            
        Returns:
            融合预测记录列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if period_number:
                    cursor.execute("""
                        SELECT id, period_number, best_number, confidence, fusion_method, 
                               model_weights, fusion_details, prediction_date, actual_number, is_hit
                        FROM fusion_predictions 
                        WHERE period_number = ?
                        ORDER BY prediction_date DESC
                    """, (period_number,))
                else:
                    cursor.execute("""
                        SELECT id, period_number, best_number, confidence, fusion_method, 
                               model_weights, fusion_details, prediction_date, actual_number, is_hit
                        FROM fusion_predictions 
                        ORDER BY prediction_date DESC 
                        LIMIT ?
                    """, (limit,))
                
                records = []
                for row in cursor.fetchall():
                    # 解析JSON字段
                    model_weights = json.loads(row[5]) if row[5] else {}
                    fusion_details = json.loads(row[6]) if row[6] else {}
                    
                    record = FusionPredictionRecord(
                        id=row[0],
                        period_number=row[1],
                        best_number=row[2],
                        confidence=row[3],
                        fusion_method=row[4],
                        model_weights=model_weights,
                        fusion_details=fusion_details,
                        prediction_date=datetime.fromisoformat(row[7]) if row[7] else None,
                        actual_number=row[8],
                        is_hit=row[9]
                    )
                    records.append(record)
                
                return records
                
        except Exception as e:
            self.logger.error(f"获取融合预测失败: {e}")
            return []
    
    def get_prediction_ranking(self, period_number: str) -> List[Dict[str, Any]]:
        """
        获取预测排行榜
        
        Args:
            period_number: 期号
            
        Returns:
            排行榜数据列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT number, rank, confidence, composite_score, model_support_count,
                           historical_hit_rate, recommendation_level, prediction_method, created_date
                    FROM prediction_rankings 
                    WHERE period_number = ?
                    ORDER BY rank
                """, (period_number,))
                
                rankings = []
                for row in cursor.fetchall():
                    ranking = {
                        'number': row[0],
                        'rank': row[1],
                        'confidence': row[2],
                        'composite_score': row[3],
                        'model_support_count': row[4],
                        'historical_hit_rate': row[5],
                        'recommendation_level': row[6],
                        'prediction_method': row[7],
                        'created_date': row[8]
                    }
                    rankings.append(ranking)
                
                return rankings
                
        except Exception as e:
            self.logger.error(f"获取排行榜失败: {e}")
            return []
    
    def update_prediction_results(self, period_number: str, actual_number: str) -> bool:
        """
        更新预测结果（当开奖号码公布时）
        
        Args:
            period_number: 期号
            actual_number: 实际开奖号码
            
        Returns:
            是否更新成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 更新模型预测结果
                cursor.execute("""
                    UPDATE model_predictions 
                    SET actual_number = ?, is_hit = (predicted_number = ?)
                    WHERE period_number = ? AND actual_number IS NULL
                """, (actual_number, actual_number, period_number))
                
                model_updated = cursor.rowcount
                
                # 更新融合预测结果
                cursor.execute("""
                    UPDATE fusion_predictions 
                    SET actual_number = ?, is_hit = (best_number = ?)
                    WHERE period_number = ? AND actual_number IS NULL
                """, (actual_number, actual_number, period_number))
                
                fusion_updated = cursor.rowcount
                
                conn.commit()
                
                self.logger.info(f"更新预测结果成功: {period_number}, 模型记录: {model_updated}, 融合记录: {fusion_updated}")
                return True
                
        except Exception as e:
            self.logger.error(f"更新预测结果失败: {e}")
            return False
    
    def get_accuracy_statistics(self, model_name: Optional[str] = None, days: int = 30) -> Dict[str, Any]:
        """
        获取准确率统计
        
        Args:
            model_name: 模型名称（可选）
            days: 统计天数
            
        Returns:
            统计结果
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if model_name:
                    # 单个模型统计
                    cursor.execute("""
                        SELECT COUNT(*) as total, SUM(CASE WHEN is_hit = 1 THEN 1 ELSE 0 END) as hits
                        FROM model_predictions 
                        WHERE model_name = ? AND actual_number IS NOT NULL 
                        AND prediction_date >= ?
                    """, (model_name, cutoff_date))
                    
                    result = cursor.fetchone()
                    total, hits = result[0], result[1]
                    
                    return {
                        'model_name': model_name,
                        'total_predictions': total,
                        'correct_predictions': hits,
                        'accuracy_rate': hits / total if total > 0 else 0,
                        'period_days': days
                    }
                else:
                    # 所有模型统计
                    cursor.execute("""
                        SELECT model_name, COUNT(*) as total, SUM(CASE WHEN is_hit = 1 THEN 1 ELSE 0 END) as hits
                        FROM model_predictions 
                        WHERE actual_number IS NOT NULL AND prediction_date >= ?
                        GROUP BY model_name
                    """, (cutoff_date,))
                    
                    results = {}
                    for row in cursor.fetchall():
                        model, total, hits = row[0], row[1], row[2]
                        results[model] = {
                            'total_predictions': total,
                            'correct_predictions': hits,
                            'accuracy_rate': hits / total if total > 0 else 0
                        }
                    
                    return {
                        'models': results,
                        'period_days': days,
                        'total_models': len(results)
                    }
                
        except Exception as e:
            self.logger.error(f"获取准确率统计失败: {e}")
            return {}
    
    def cleanup_old_data(self, days_to_keep: int = 365) -> bool:
        """
        清理旧数据
        
        Args:
            days_to_keep: 保留天数
            
        Returns:
            是否清理成功
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 清理旧的模型预测记录
                cursor.execute("""
                    DELETE FROM model_predictions 
                    WHERE prediction_date < ?
                """, (cutoff_date,))
                
                model_deleted = cursor.rowcount
                
                # 清理旧的排行榜记录
                cursor.execute("""
                    DELETE FROM prediction_rankings 
                    WHERE created_date < ?
                """, (cutoff_date,))
                
                ranking_deleted = cursor.rowcount
                
                # 清理旧的性能日志
                cursor.execute("""
                    DELETE FROM prediction_performance_log 
                    WHERE log_date < ?
                """, (cutoff_date,))
                
                log_deleted = cursor.rowcount
                
                conn.commit()
                
                self.logger.info(f"数据清理完成: 模型记录 {model_deleted}, 排行榜 {ranking_deleted}, 日志 {log_deleted}")
                return True
                
        except Exception as e:
            self.logger.error(f"数据清理失败: {e}")
            return False
