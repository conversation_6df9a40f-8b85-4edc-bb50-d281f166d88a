#!/usr/bin/env python3
"""
调试模型状态问题
"""

import sqlite3
import sys
import os
from pathlib import Path

def debug_model_status():
    """调试模型状态"""
    print("🔍 开始调试模型状态问题...")
    print("=" * 60)
    
    # 检查当前工作目录
    current_dir = os.getcwd()
    print(f"📁 当前工作目录: {current_dir}")
    
    # 检查数据库文件路径
    db_paths = [
        "data/model_library.db",
        "./data/model_library.db",
        os.path.join(current_dir, "data", "model_library.db")
    ]
    
    print("\n🔍 检查数据库文件路径:")
    for db_path in db_paths:
        abs_path = os.path.abspath(db_path)
        exists = Path(db_path).exists()
        print(f"  {db_path} -> {abs_path} [{'存在' if exists else '不存在'}]")
    
    # 使用存在的数据库文件
    db_path = None
    for path in db_paths:
        if Path(path).exists():
            db_path = path
            break
    
    if not db_path:
        print("❌ 未找到数据库文件")
        return
    
    print(f"\n✅ 使用数据库文件: {db_path}")
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='model_states'")
            table_schema = cursor.fetchone()
            if table_schema:
                print(f"\n📋 model_states表结构:")
                print(table_schema[0])
            else:
                print("❌ model_states表不存在")
                return
            
            print("\n" + "=" * 60)
            
            # 查询所有模型状态
            cursor.execute('''
                SELECT model_id, status, data_ready, features_ready, trained, 
                       training_data_size, last_training_time, last_check_time, error_message
                FROM model_states
                ORDER BY model_id
            ''')
            
            all_results = cursor.fetchall()
            print("📋 所有模型状态详情:")
            
            if not all_results:
                print("❌ 数据库中没有模型状态记录")
                return
            
            for row in all_results:
                model_id, status, data_ready, features_ready, trained, training_data_size, last_training_time, last_check_time, error_message = row
                
                print(f"\n🤖 模型: {model_id}")
                print(f"  状态: {status}")
                print(f"  数据就绪: {data_ready}")
                print(f"  特征就绪: {features_ready}")
                print(f"  已训练: {trained}")
                print(f"  训练数据量: {training_data_size}")
                print(f"  最后训练时间: {last_training_time}")
                print(f"  最后检查时间: {last_check_time}")
                print(f"  错误信息: {error_message}")
            
            print("\n" + "=" * 60)
            
            # 特别检查我们修复的两个模型
            target_models = ['markov_enhanced', 'deep_learning_cnn_lstm']
            for model_id in target_models:
                cursor.execute('''
                    SELECT trained, last_training_time, status
                    FROM model_states 
                    WHERE model_id = ?
                ''', (model_id,))
                
                result = cursor.fetchone()
                if result:
                    trained, last_training_time, status = result
                    print(f"🎯 {model_id}:")
                    print(f"  数据库中trained字段: {trained} (类型: {type(trained)})")
                    print(f"  数据库中status字段: {status}")
                    print(f"  最后训练时间: {last_training_time}")
                    
                    # 检查是否有最近的训练记录
                    if last_training_time:
                        from datetime import datetime
                        try:
                            training_time = datetime.fromisoformat(last_training_time)
                            now = datetime.now()
                            time_diff = now - training_time
                            print(f"  训练时间距现在: {time_diff}")
                        except:
                            print(f"  训练时间格式异常: {last_training_time}")
                else:
                    print(f"❌ {model_id}: 数据库中无记录")
                    
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_model_status()
