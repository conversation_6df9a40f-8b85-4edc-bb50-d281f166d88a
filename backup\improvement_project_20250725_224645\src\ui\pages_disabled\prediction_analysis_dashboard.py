#!/usr/bin/env python3
"""
预测分析仪表板
Prediction Analysis Dashboard

创建预测分析可视化界面
"""

import asyncio
import json
import os
# 导入系统组件
import sys
from datetime import datetime, timedelta

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import streamlit as st

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from analysis.model_weakness_identifier import ModelWeaknessIdentifier
from analysis.prediction_deviation_analyzer import PredictionDeviationAnalyzer
from analysis.success_factor_extractor import SuccessFactorExtractor
from core.draw_trigger_system import DrawTriggerSystem
from core.unified_prediction_storage import UnifiedPredictionStorage


def show_prediction_analysis_dashboard():
    """显示预测分析仪表板"""
    st.header("🔍 预测分析仪表板")
    st.markdown("---")
    
    # 初始化系统组件
    if 'storage' not in st.session_state:
        st.session_state.storage = UnifiedPredictionStorage()
        st.session_state.deviation_analyzer = PredictionDeviationAnalyzer()
        st.session_state.weakness_identifier = ModelWeaknessIdentifier()
        st.session_state.success_extractor = SuccessFactorExtractor()
    
    # 侧边栏控制
    with st.sidebar:
        st.subheader("🎛️ 控制面板")
        
        # 期号选择
        available_periods = get_available_periods()
        selected_period = st.selectbox(
            "选择分析期号",
            available_periods,
            index=0 if available_periods else None
        )
        
        # 刷新按钮
        if st.button("🔄 刷新分析", type="primary"):
            st.rerun()
        
        # 手动触发分析
        st.subheader("🎯 手动触发分析")
        manual_period = st.text_input("期号", placeholder="例如：2025194")
        manual_result = st.text_input("开奖号码", placeholder="例如：123")
        
        if st.button("▶️ 执行分析"):
            if manual_period and manual_result:
                trigger_manual_analysis(manual_period, manual_result)
            else:
                st.error("请输入期号和开奖号码")
    
    # 主要内容区域
    if selected_period:
        show_period_analysis(selected_period)
    else:
        show_system_overview()


def get_available_periods():
    """获取可用的期号列表"""
    try:
        storage = st.session_state.storage
        
        # 获取最近的期号
        import sqlite3
        with sqlite3.connect(storage.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT DISTINCT period_number 
                    FROM unified_predictions 
                    WHERE is_verified = TRUE
                    ORDER BY period_number DESC 
                    LIMIT 20
                """)
                periods = [row[0] for row in cursor.fetchall()]
        
        return periods
    except Exception as e:
        st.error(f"获取期号列表失败: {e}")
        return []


def show_system_overview():
    """显示系统概览"""
    st.subheader("📊 系统概览")
    
    try:
        storage = st.session_state.storage
        stats = storage.get_statistics()
        
        # 显示统计指标
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                "总预测数",
                stats.get('total_predictions', 0),
                help="系统中的预测记录总数"
            )
        
        with col2:
            st.metric(
                "已验证预测",
                stats.get('verified_predictions', 0),
                help="已经验证的预测记录数"
            )
        
        with col3:
            verified = stats.get('verified_predictions', 0)
            total = stats.get('total_predictions', 1)
            verification_rate = (verified / total * 100) if total > 0 else 0
            st.metric(
                "验证率",
                f"{verification_rate:.1f}%",
                help="已验证预测占总预测的比例"
            )
        
        with col4:
            st.metric(
                "平均准确率",
                f"{stats.get('average_accuracy', 0):.3f}",
                help="所有已验证预测的平均准确率"
            )
        
        # 模型分布图表
        if stats.get('model_counts'):
            st.subheader("📈 模型预测分布")
            
            model_df = pd.DataFrame(
                list(stats['model_counts'].items()),
                columns=['模型名称', '预测数量']
            )
            
            fig = px.pie(
                model_df,
                values='预测数量',
                names='模型名称',
                title="各模型预测数量分布"
            )
            st.plotly_chart(fig, use_container_width=True)
        
        # 系统状态
        st.subheader("🔧 系统状态")
        
        status_col1, status_col2 = st.columns(2)
        
        with status_col1:
            st.success("✅ 统一存储系统：正常")
            st.success("✅ 分析引擎：正常")
        
        with status_col2:
            st.success("✅ 优化系统：正常")
            st.success("✅ 触发系统：正常")
        
    except Exception as e:
        st.error(f"获取系统概览失败: {e}")


def show_period_analysis(period_number):
    """显示指定期号的分析结果"""
    st.subheader(f"📋 期号 {period_number} 分析结果")
    
    try:
        storage = st.session_state.storage
        predictions = storage.get_period_predictions(period_number)
        
        if not predictions:
            st.warning(f"期号 {period_number} 没有找到预测记录")
            return
        
        # 显示分析概览
        show_analysis_overview(predictions)
        
        # 显示详细分析
        show_detailed_analysis(predictions)
        
        # 显示优化建议
        show_optimization_suggestions(predictions)
        
    except Exception as e:
        st.error(f"获取期号分析失败: {e}")


def show_analysis_overview(predictions):
    """显示分析概览"""
    st.subheader("📊 分析概览")
    
    # 计算统计信息
    total_models = len(predictions)
    verified_predictions = [p for p in predictions if p.is_verified]
    successful_predictions = [p for p in verified_predictions if p.accuracy_score == 1.0]
    partial_matches = [p for p in verified_predictions if 0.5 <= p.accuracy_score < 1.0]
    
    # 显示指标
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "预测模型数量",
            total_models,
            help="参与预测的模型总数"
        )
    
    with col2:
        success_count = len(successful_predictions)
        success_rate = (success_count / len(verified_predictions) * 100) if verified_predictions else 0
        st.metric(
            "成功预测数",
            success_count,
            delta=f"{success_rate:.1f}%",
            help="完全正确的预测数量"
        )
    
    with col3:
        st.metric(
            "部分匹配数",
            len(partial_matches),
            help="部分位置正确的预测数量"
        )
    
    with col4:
        if verified_predictions:
            avg_deviation = sum(abs(p.accuracy_score - 1.0) for p in verified_predictions) / len(verified_predictions)
            st.metric(
                "平均偏差",
                f"{avg_deviation:.3f}",
                help="所有预测的平均准确率偏差"
            )
        else:
            st.metric("平均偏差", "N/A")
    
    # 预测结果分布图
    if verified_predictions:
        st.subheader("📈 预测结果分布")
        
        # 准确率分布
        accuracy_data = [p.accuracy_score for p in verified_predictions]
        model_names = [p.model_name for p in verified_predictions]
        
        fig = go.Figure()
        fig.add_trace(go.Bar(
            x=model_names,
            y=accuracy_data,
            name='准确率',
            marker_color='lightblue'
        ))
        
        fig.update_layout(
            title="各模型预测准确率",
            xaxis_title="模型名称",
            yaxis_title="准确率",
            yaxis=dict(range=[0, 1])
        )
        
        st.plotly_chart(fig, use_container_width=True)


def show_detailed_analysis(predictions):
    """显示详细分析"""
    st.subheader("🔍 详细分析")
    
    verified_predictions = [p for p in predictions if p.is_verified and p.actual_numbers]
    
    if not verified_predictions:
        st.warning("没有已验证的预测记录可供分析")
        return
    
    # 为每个模型显示详细分析
    for prediction in verified_predictions:
        with st.expander(f"📈 {prediction.model_name} 模型分析", expanded=False):
            
            # 预测结果对比
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.write("**预测结果**")
                st.code(prediction.predicted_numbers, language=None)
            
            with col2:
                st.write("**实际结果**")
                st.code(prediction.actual_numbers, language=None)
            
            with col3:
                st.write("**准确率**")
                accuracy_color = "green" if prediction.accuracy_score >= 0.8 else "orange" if prediction.accuracy_score >= 0.5 else "red"
                st.markdown(f"<span style='color: {accuracy_color}; font-size: 24px; font-weight: bold'>{prediction.accuracy_score:.3f}</span>", unsafe_allow_html=True)
            
            # 偏差分析
            try:
                deviation_analyzer = st.session_state.deviation_analyzer
                deviation_result = deviation_analyzer.analyze_deviation(prediction, prediction.actual_numbers)
                
                st.write("**偏差分析**")
                
                # 数值偏差
                numerical_dev = deviation_result.numerical_deviation
                if 'position_accuracy' in numerical_dev:
                    pos_acc_df = pd.DataFrame([
                        {
                            '位置': pos,
                            '预测': prediction.predicted_numbers[i],
                            '实际': prediction.actual_numbers[i],
                            '偏差': data['deviation'],
                            '接近度': f"{data['proximity_score']:.3f}"
                        }
                        for i, (pos, data) in enumerate(numerical_dev['position_accuracy'].items())
                    ])
                    st.dataframe(pos_acc_df, use_container_width=True)
                
                # 综合评分
                st.write(f"**综合评分**: {deviation_result.overall_score:.3f}")
                
            except Exception as e:
                st.error(f"偏差分析失败: {e}")


def show_optimization_suggestions(predictions):
    """显示优化建议"""
    st.subheader("💡 优化建议")
    
    try:
        # 模拟优化建议生成
        suggestions = generate_mock_suggestions(predictions)
        
        if not suggestions:
            st.info("当前没有优化建议")
            return
        
        for suggestion in suggestions:
            with st.expander(f"🎯 {suggestion['title']}", expanded=False):
                st.write(f"**描述**: {suggestion['description']}")
                st.write(f"**优先级**: {suggestion['priority']}")
                st.write(f"**预期改进**: {suggestion['expected_improvement']}")
                
                st.write("**具体行动**:")
                for action in suggestion['actions']:
                    st.write(f"- {action}")
                
                # 实施按钮
                if st.button(f"实施建议: {suggestion['title']}", key=f"implement_{suggestion['id']}"):
                    st.success(f"建议 '{suggestion['title']}' 已加入实施队列")
    
    except Exception as e:
        st.error(f"生成优化建议失败: {e}")


def generate_mock_suggestions(predictions):
    """生成模拟优化建议"""
    suggestions = []
    
    verified_predictions = [p for p in predictions if p.is_verified]
    if not verified_predictions:
        return suggestions
    
    avg_accuracy = sum(p.accuracy_score for p in verified_predictions) / len(verified_predictions)
    
    if avg_accuracy < 0.6:
        suggestions.append({
            'id': 'accuracy_improvement',
            'title': '提升整体准确率',
            'description': f'当前平均准确率为 {avg_accuracy:.3f}，建议进行参数优化',
            'priority': '高',
            'expected_improvement': '准确率提升15-25%',
            'actions': [
                '调整模型权重参数',
                '优化特征工程',
                '增加训练数据量',
                '实施集成学习策略'
            ]
        })
    
    # 检查置信度校准
    confidence_issues = [p for p in verified_predictions if abs(p.confidence - p.accuracy_score) > 0.3]
    if len(confidence_issues) > len(verified_predictions) * 0.3:
        suggestions.append({
            'id': 'confidence_calibration',
            'title': '改善置信度校准',
            'description': f'发现 {len(confidence_issues)} 个预测存在置信度校准问题',
            'priority': '中',
            'expected_improvement': '置信度校准误差减少40-60%',
            'actions': [
                '实施Platt缩放校准',
                '使用温度缩放技术',
                '调整置信度计算方法',
                '建立校准数据集'
            ]
        })
    
    return suggestions


def trigger_manual_analysis(period_number, actual_result):
    """触发手动分析"""
    try:
        with st.spinner("正在执行分析..."):
            # 这里应该调用实际的分析系统
            # 为了演示，我们显示一个成功消息
            st.success(f"✅ 已成功触发期号 {period_number} (开奖号码: {actual_result}) 的分析")
            
            # 模拟分析过程
            import time
            time.sleep(2)
            
            st.info("📊 分析完成，请刷新页面查看结果")
    
    except Exception as e:
        st.error(f"触发分析失败: {e}")


if __name__ == "__main__":
    # 如果直接运行此文件，显示仪表板
    st.set_page_config(
        page_title="预测分析仪表板",
        page_icon="🔍",
        layout="wide"
    )
    
    show_prediction_analysis_dashboard()
