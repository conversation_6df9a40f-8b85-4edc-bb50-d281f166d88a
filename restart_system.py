#!/usr/bin/env python3
"""
重启整个福彩3D预测系统
解决环境和依赖问题
"""

import os
import signal
import subprocess
import sys
import time
from pathlib import Path


def kill_existing_processes():
    """终止现有的相关进程"""
    print("🔄 终止现有进程...")
    
    try:
        # 在Windows上终止相关进程
        subprocess.run(["taskkill", "/f", "/im", "python.exe"], 
                      capture_output=True, check=False)
        subprocess.run(["taskkill", "/f", "/im", "streamlit.exe"], 
                      capture_output=True, check=False)
        time.sleep(2)
        print("✅ 现有进程已终止")
    except Exception as e:
        print(f"⚠️ 终止进程时出现警告: {e}")

def check_and_install_dependencies():
    """检查和安装依赖"""
    print("📦 检查依赖...")
    
    dependencies = [
        "apscheduler",
        "streamlit", 
        "fastapi",
        "uvicorn",
        "requests",
        "pandas",
        "numpy",
        "plotly"
    ]
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep}: 已安装")
        except ImportError:
            print(f"📥 安装 {dep}...")
            subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                          check=True)

def start_api_service():
    """启动API服务"""
    print("🚀 启动API服务...")
    
    try:
        # 启动API服务
        api_process = subprocess.Popen(
            [sys.executable, "start_api.py"],
            cwd=os.getcwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8'
        )
        
        # 等待API服务启动
        time.sleep(5)
        
        # 检查API服务状态
        try:
            import requests
            response = requests.get("http://127.0.0.1:8888/docs", timeout=5)
            if response.status_code == 200:
                print("✅ API服务启动成功")
                return api_process
            else:
                print(f"⚠️ API服务响应异常: {response.status_code}")
        except Exception as e:
            print(f"⚠️ API服务检查失败: {e}")
        
        return api_process
        
    except Exception as e:
        print(f"❌ API服务启动失败: {e}")
        return None

def start_streamlit():
    """启动Streamlit服务"""
    print("🌐 启动Streamlit服务...")
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = 'src'
        
        # 启动Streamlit
        streamlit_process = subprocess.Popen(
            [sys.executable, "-m", "streamlit", "run",
             "src/ui/main.py",
             "--server.port=8501",
             "--server.address=127.0.0.1",
             "--browser.gatherUsageStats=false"],
            env=env,
            cwd=os.getcwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8'
        )
        
        print("✅ Streamlit服务启动成功")
        print("🌐 访问地址: http://127.0.0.1:8501")
        
        return streamlit_process
        
    except Exception as e:
        print(f"❌ Streamlit服务启动失败: {e}")
        return None

def test_scheduler():
    """测试调度器功能"""
    print("🧪 测试调度器功能...")
    
    try:
        result = subprocess.run(
            [sys.executable, "scripts/start_scheduler.py", "--test"],
            capture_output=True,
            text=True,
            timeout=30,
            cwd=os.getcwd()
        )
        
        if result.returncode == 0:
            print("✅ 调度器测试通过")
            return True
        else:
            print(f"❌ 调度器测试失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 调度器测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🔧 福彩3D预测系统重启工具")
    print("=" * 50)
    
    # 1. 终止现有进程
    kill_existing_processes()
    
    # 2. 检查依赖
    check_and_install_dependencies()
    
    # 3. 测试调度器
    if not test_scheduler():
        print("⚠️ 调度器测试失败，但继续启动服务...")
    
    # 4. 启动API服务
    api_process = start_api_service()
    
    # 5. 启动Streamlit服务
    streamlit_process = start_streamlit()
    
    if streamlit_process:
        print("\n" + "=" * 50)
        print("🎉 系统启动完成！")
        print("📱 Streamlit: http://127.0.0.1:8501")
        print("🔗 API文档: http://127.0.0.1:8888/docs")
        print("\n💡 使用说明:")
        print("1. 打开浏览器访问 http://127.0.0.1:8501")
        print("2. 进入'数据更新'页面")
        print("3. 在'自动更新'选项卡中启动调度器")
        print("4. 按 Ctrl+C 停止所有服务")
        
        try:
            # 等待用户中断
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务...")
            
            if streamlit_process:
                streamlit_process.terminate()
            if api_process:
                api_process.terminate()
                
            print("✅ 所有服务已停止")
    else:
        print("❌ 系统启动失败")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
