# 🚀 API服务启动指南

## ✅ 正确的启动方式

### API服务启动
```bash
cd D:\github\3dyuce
python start_production_api.py
```

### Streamlit界面启动
```bash
cd D:\github\3dyuce
python start_streamlit.py
```

## 📋 服务信息

### API服务
- **启动脚本**: `start_production_api.py`
- **绑定地址**: 127.0.0.1:8888
- **健康检查**: http://127.0.0.1:8888/health
- **API文档**: http://127.0.0.1:8888/docs

### Streamlit界面
- **启动脚本**: `start_streamlit.py`
- **绑定地址**: 127.0.0.1:8501
- **访问地址**: http://127.0.0.1:8501

## ❌ 错误的启动方式（已禁用）

**不要直接运行以下文件**：
- ~~`python src/api/production_main.py`~~ (端口错误，路径问题)
- ~~`python src/api/main.py`~~ (旧版API文件，已弃用)
- ~~`python src/ui/main.py`~~ (Streamlit应用文件，需通过启动脚本运行)

## 🔍 验证服务状态

启动API服务后，可以通过以下方式验证：

1. **健康检查**:
   ```bash
   curl http://127.0.0.1:8888/health
   ```

2. **浏览器访问**:
   - API文档: http://127.0.0.1:8888/docs
   - 健康检查: http://127.0.0.1:8888/health

3. **测试预测接口**:
   ```bash
   curl "http://127.0.0.1:8888/api/v1/prediction/intelligent-fusion/predict?prediction_mode=智能融合&auto_train=true"
   ```

## 🛠️ 故障排除

### 如果API启动失败：
1. 检查端口8888是否被占用
2. 确认在正确的目录 (`D:\github\3dyuce`)
3. 检查Python环境和依赖

### 如果Streamlit启动失败：
1. 检查端口8501是否被占用
2. 确认API服务已启动
3. 检查配置文件

## 📝 重要提醒

- **始终使用** `start_production_api.py` 启动API服务
- **不要直接运行** `src/api/production_main.py`
- **确保先启动API服务，再启动Streamlit界面**
- **API绑定到127.0.0.1:8888，不是8000**
