#!/usr/bin/env python3
"""
全自动Bug检测与反馈系统 - 实际效果演示
创建日期: 2025年7月24日
用途: 演示Bug检测系统的实际使用效果和深度检查功能
"""

import json
import os
import sys
import time
from datetime import datetime

import pandas as pd
import streamlit as st

# 添加项目路径
sys.path.insert(0, '.')

# 导入Bug检测组件
try:
    from src.bug_detection.core.database_manager import DatabaseManager
    from src.bug_detection.feedback.bug_reporter import IntelligentBugReporter
    from src.bug_detection.monitoring.api_monitor import APIPerformanceMonitor
    from src.bug_detection.monitoring.js_monitor import (JavaScriptMonitor,
                                                         inject_js_monitor,
                                                         show_js_monitor_debug)
except ImportError as e:
    st.error(f"导入Bug检测组件失败: {e}")
    st.stop()

def main():
    st.set_page_config(
        page_title="Bug检测系统演示",
        page_icon="🔍",
        layout="wide"
    )
    
    st.title("🔍 全自动Bug检测与反馈系统 - 实际效果演示")
    st.markdown("---")
    
    # 侧边栏导航
    demo_mode = st.sidebar.selectbox(
        "选择演示模式",
        [
            "🏠 系统概览",
            "🔧 JavaScript错误监控",
            "📊 Bug报告生成",
            "📈 性能监控",
            "🧪 实际Bug测试",
            "📋 深度检查功能",
            "🎯 使用指南"
        ]
    )
    
    if demo_mode == "🏠 系统概览":
        show_system_overview()
    elif demo_mode == "🔧 JavaScript错误监控":
        show_js_monitoring_demo()
    elif demo_mode == "📊 Bug报告生成":
        show_bug_reporting_demo()
    elif demo_mode == "📈 性能监控":
        show_performance_monitoring_demo()
    elif demo_mode == "🧪 实际Bug测试":
        show_actual_bug_testing()
    elif demo_mode == "📋 深度检查功能":
        show_deep_inspection_features()
    elif demo_mode == "🎯 使用指南":
        show_usage_guide()

def show_system_overview():
    """显示系统概览"""
    st.header("🏠 系统概览")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("监控组件", "5个", "✅ 全部就绪")
    
    with col2:
        st.metric("API端点", "8个", "✅ 正常运行")
    
    with col3:
        st.metric("数据表", "5个", "✅ 已创建")
    
    with col4:
        st.metric("测试覆盖", "17页面", "✅ 全覆盖")
    
    st.subheader("🏗️ 系统架构")
    
    architecture_diagram = """
    ```
    全自动Bug检测与反馈系统
    ├── 🔍 JavaScript错误监控
    │   ├── 实时错误捕获
    │   ├── 错误分类分析
    │   └── 自动上报机制
    ├── 📊 API性能监控
    │   ├── 响应时间监控
    │   ├── 错误率统计
    │   └── 性能瓶颈识别
    ├── 🧠 智能Bug报告
    │   ├── 自动错误分类
    │   ├── 严重程度评估
    │   ├── 修复建议生成
    │   └── 相似Bug识别
    ├── 🧪 自动化测试
    │   ├── E2E页面测试
    │   ├── 单元测试
    │   ├── 集成测试
    │   └── 视觉回归测试
    └── 📈 实时监控仪表板
        ├── Bug统计分析
        ├── 性能指标展示
        └── 告警通知系统
    ```
    """
    st.code(architecture_diagram)
    
    st.subheader("🎯 核心功能状态")
    
    # 检查各组件状态
    status_data = []
    
    try:
        # 检查数据库
        db_manager = DatabaseManager()
        status_data.append({"组件": "数据库管理器", "状态": "✅ 正常", "描述": "SQLite数据库连接正常"})
    except Exception as e:
        status_data.append({"组件": "数据库管理器", "状态": "❌ 异常", "描述": f"错误: {str(e)}"})
    
    try:
        # 检查JavaScript监控
        js_monitor = JavaScriptMonitor()
        status_data.append({"组件": "JavaScript监控", "状态": "✅ 正常", "描述": "错误监控脚本就绪"})
    except Exception as e:
        status_data.append({"组件": "JavaScript监控", "状态": "❌ 异常", "描述": f"错误: {str(e)}"})
    
    try:
        # 检查Bug报告生成器
        bug_reporter = IntelligentBugReporter()
        status_data.append({"组件": "Bug报告生成器", "状态": "✅ 正常", "描述": "智能分析引擎就绪"})
    except Exception as e:
        status_data.append({"组件": "Bug报告生成器", "状态": "❌ 异常", "描述": f"错误: {str(e)}"})
    
    status_df = pd.DataFrame(status_data)
    st.dataframe(status_df, use_container_width=True)

def show_js_monitoring_demo():
    """演示JavaScript错误监控"""
    st.header("🔧 JavaScript错误监控演示")
    
    # 注入JavaScript错误监控
    inject_js_monitor("bug_detection_demo")
    
    st.subheader("📡 实时错误监控")
    st.info("JavaScript错误监控已激活，正在实时监控页面错误...")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("🎮 错误测试控制台")
        
        if st.button("🧪 触发JavaScript错误", key="js_error_test"):
            # 注入一个测试错误
            error_script = """
            <script>
            setTimeout(() => {
                console.log('准备触发测试错误...');
                throw new Error('这是一个演示用的JavaScript错误 - Bug检测系统测试');
            }, 500);
            </script>
            """
            st.components.v1.html(error_script, height=0)
            st.success("✅ 测试错误已触发！请查看右侧监控状态。")
        
        if st.button("🔗 触发Promise错误", key="promise_error_test"):
            promise_error_script = """
            <script>
            setTimeout(() => {
                Promise.reject(new Error('Promise rejection 测试错误'));
            }, 500);
            </script>
            """
            st.components.v1.html(promise_error_script, height=0)
            st.success("✅ Promise错误已触发！")
        
        if st.button("📦 触发资源加载错误", key="resource_error_test"):
            resource_error_script = """
            <script>
            setTimeout(() => {
                const img = new Image();
                img.src = 'https://nonexistent-domain-12345.com/image.jpg';
                document.body.appendChild(img);
            }, 500);
            </script>
            """
            st.components.v1.html(resource_error_script, height=0)
            st.success("✅ 资源加载错误已触发！")
    
    with col2:
        st.subheader("📊 监控状态")
        
        # 显示JavaScript监控调试信息
        show_js_monitor_debug()
        
        # 实时错误统计
        error_stats_script = """
        <script>
        // 安全的DOM操作函数
        function safeSetText(elementId, text) {
            const element = document.getElementById(elementId);
            if (element) {
                element.innerText = text;
                return true;
            } else {
                console.warn('元素不存在:', elementId);
                return false;
            }
        }

        function updateErrorStats() {
            if (window.bugDetector) {
                const stats = window.bugDetector.getErrorStats();
                const errorCount = Object.values(stats).reduce((a, b) => a + b, 0);

                safeSetText('total-errors', errorCount);
                safeSetText('error-types', JSON.stringify(stats, null, 2));
                safeSetText('session-id', window.bugDetector.sessionId);
            } else {
                safeSetText('total-errors', '监控未初始化');
                safeSetText('error-types', 'N/A');
                safeSetText('session-id', 'N/A');
            }
        }
        
        // 每2秒更新一次
        setInterval(updateErrorStats, 2000);
        updateErrorStats();
        </script>
        
        <div style="background: #f0f2f6; padding: 15px; border-radius: 5px; margin: 10px 0;">
            <h4>📈 实时统计</h4>
            <p><strong>检测到的错误总数:</strong> <span id="total-errors">加载中...</span></p>
            <p><strong>会话ID:</strong> <span id="session-id">加载中...</span></p>
            <h5>错误类型分布:</h5>
            <pre id="error-types" style="background: white; padding: 10px; border-radius: 3px;">加载中...</pre>
        </div>
        """
        st.components.v1.html(error_stats_script, height=300)

def show_bug_reporting_demo():
    """演示Bug报告生成"""
    st.header("📊 Bug报告生成演示")
    
    st.subheader("🎯 智能Bug报告生成器")
    
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.subheader("📝 创建测试Bug报告")
        
        # Bug类型选择
        error_type = st.selectbox(
            "错误类型",
            ["javascript", "api_error", "database_error", "network_error", "memory_error"]
        )
        
        # 错误消息
        error_message = st.text_area(
            "错误消息",
            value="TypeError: Cannot read property 'data' of undefined",
            height=100
        )
        
        # 严重程度
        severity = st.selectbox(
            "严重程度",
            ["low", "medium", "high", "critical"]
        )
        
        # 页面信息
        page_name = st.selectbox(
            "发生页面",
            ["data_overview", "prediction_analysis", "intelligent_fusion", "real_time_monitoring"]
        )
        
        if st.button("🚀 生成Bug报告", key="generate_bug_report"):
            try:
                # 创建Bug报告生成器
                bug_reporter = IntelligentBugReporter()
                
                # 构造错误数据
                error_data = {
                    'type': error_type,
                    'message': error_message,
                    'severity': severity,
                    'page_name': page_name,
                    'source': 'demo_test.js',
                    'line_number': 42,
                    'page_url': f'http://127.0.0.1:8501/?page={page_name}',
                    'session_id': f'demo_session_{int(time.time())}'
                }
                
                # 生成增强Bug报告
                with st.spinner("正在生成智能Bug报告..."):
                    bug_report = bug_reporter.generate_enhanced_report(error_data)
                
                st.session_state.current_bug_report = bug_report
                st.success("✅ Bug报告生成成功！请查看右侧详细信息。")
                
            except Exception as e:
                st.error(f"❌ Bug报告生成失败: {e}")
    
    with col2:
        st.subheader("📋 生成的Bug报告")
        
        if 'current_bug_report' in st.session_state:
            bug_report = st.session_state.current_bug_report
            
            # 基本信息
            st.markdown("### 🆔 基本信息")
            st.write(f"**Bug ID**: {bug_report['id']}")
            st.write(f"**时间戳**: {bug_report['timestamp']}")
            st.write(f"**严重程度**: {bug_report['error']['severity']}")
            st.write(f"**优先级**: {bug_report['priority']}")
            st.write(f"**分类**: {bug_report['category']}")
            
            # 错误详情
            st.markdown("### 🐛 错误详情")
            st.write(f"**类型**: {bug_report['error']['type']}")
            st.write(f"**消息**: {bug_report['error']['message']}")
            
            # 影响分析
            if 'impact_analysis' in bug_report:
                st.markdown("### 📊 影响分析")
                impact = bug_report['impact_analysis']
                st.write(f"**用户影响**: {impact['user_impact']}")
                st.write(f"**系统影响**: {impact['system_impact']}")
                st.write(f"**业务影响**: {impact['business_impact']}")
            
            # 修复建议
            st.markdown("### 🔧 修复建议")
            for i, suggestion in enumerate(bug_report['suggested_fixes'], 1):
                st.write(f"{i}. {suggestion}")
            
            # 相似Bug
            if 'similar_bugs' in bug_report and bug_report['similar_bugs']:
                st.markdown("### 🔍 相似Bug")
                for similar in bug_report['similar_bugs'][:3]:
                    st.write(f"- Bug ID: {similar['bug_id']} (相似度: {similar['similarity']:.2%})")
            
            # 完整报告JSON
            with st.expander("📄 查看完整JSON报告"):
                st.json(bug_report)
        else:
            st.info("👆 请在左侧生成一个Bug报告来查看详细信息")

def show_performance_monitoring_demo():
    """演示性能监控"""
    st.header("📈 性能监控演示")
    
    st.subheader("🚀 API性能监控")
    
    # 模拟性能数据
    performance_data = {
        '/api/v1/prediction': {'avg_time': 0.15, 'max_time': 0.8, 'count': 156, 'errors': 2},
        '/api/v1/data/update': {'avg_time': 0.05, 'max_time': 0.2, 'count': 89, 'errors': 0},
        '/api/v1/analysis': {'avg_time': 0.25, 'max_time': 1.2, 'count': 234, 'errors': 5},
        '/api/v1/monitoring': {'avg_time': 0.03, 'max_time': 0.1, 'count': 445, 'errors': 1}
    }
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📊 API性能统计")
        
        perf_df = pd.DataFrame([
            {
                'API端点': endpoint,
                '平均响应时间(s)': data['avg_time'],
                '最大响应时间(s)': data['max_time'],
                '请求次数': data['count'],
                '错误次数': data['errors'],
                '错误率(%)': round(data['errors'] / data['count'] * 100, 2)
            }
            for endpoint, data in performance_data.items()
        ])
        
        st.dataframe(perf_df, use_container_width=True)
        
        # 性能图表
        st.subheader("📈 响应时间趋势")
        chart_data = pd.DataFrame({
            'API端点': list(performance_data.keys()),
            '平均响应时间': [data['avg_time'] for data in performance_data.values()]
        })
        st.bar_chart(chart_data.set_index('API端点'))
    
    with col2:
        st.subheader("⚠️ 性能告警")
        
        # 检查性能问题
        alerts = []
        for endpoint, data in performance_data.items():
            if data['avg_time'] > 0.2:
                alerts.append(f"🟡 {endpoint}: 平均响应时间过长 ({data['avg_time']}s)")
            if data['max_time'] > 1.0:
                alerts.append(f"🔴 {endpoint}: 最大响应时间过长 ({data['max_time']}s)")
            if data['errors'] / data['count'] > 0.02:
                alerts.append(f"🟠 {endpoint}: 错误率过高 ({data['errors']}/{data['count']})")
        
        if alerts:
            for alert in alerts:
                st.warning(alert)
        else:
            st.success("✅ 所有API性能正常")
        
        st.subheader("🎯 性能优化建议")
        st.info("""
        **当前建议**:
        1. 优化 `/api/v1/analysis` 端点的查询逻辑
        2. 为慢查询添加缓存机制
        3. 考虑对高频API进行负载均衡
        4. 监控数据库连接池状态
        """)

def show_actual_bug_testing():
    """实际Bug测试"""
    st.header("🧪 实际Bug测试")
    
    st.warning("⚠️ 注意：以下测试会故意触发真实的错误来演示Bug检测系统的工作效果")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("🎮 Bug测试控制台")
        
        test_type = st.selectbox(
            "选择测试类型",
            [
                "JavaScript运行时错误",
                "API调用失败",
                "数据处理异常",
                "UI渲染错误",
                "内存泄漏模拟"
            ]
        )
        
        if st.button("🚀 执行Bug测试", key="execute_bug_test"):
            if test_type == "JavaScript运行时错误":
                js_runtime_error = """
                <script>
                setTimeout(() => {
                    // 故意访问未定义的对象属性
                    const undefinedObj = null;
                    console.log(undefinedObj.someProperty); // 这会触发TypeError
                }, 1000);
                </script>
                """
                st.components.v1.html(js_runtime_error, height=0)
                st.error("💥 JavaScript运行时错误已触发！")
                
            elif test_type == "API调用失败":
                # 模拟API调用失败
                try:
                    import requests
                    response = requests.get("http://nonexistent-api.com/data", timeout=1)
                except Exception as e:
                    st.error(f"💥 API调用失败: {str(e)}")
                    
                    # 生成Bug报告
                    try:
                        bug_reporter = IntelligentBugReporter()
                        error_data = {
                            'type': 'api_error',
                            'message': str(e),
                            'severity': 'high',
                            'page_name': 'bug_testing',
                            'source': 'api_call_test'
                        }
                        bug_report = bug_reporter.generate_bug_report(error_data)
                        st.session_state.test_bug_report = bug_report
                        st.success("✅ Bug报告已自动生成")
                    except Exception as report_error:
                        st.error(f"Bug报告生成失败: {report_error}")
                        
            elif test_type == "数据处理异常":
                try:
                    # 故意触发数据处理错误
                    data = [1, 2, "invalid", 4, 5]
                    result = sum(data)  # 这会触发TypeError
                except Exception as e:
                    st.error(f"💥 数据处理异常: {str(e)}")
                    
            elif test_type == "UI渲染错误":
                ui_error_script = """
                <script>
                setTimeout(() => {
                    // 尝试操作不存在的DOM元素
                    const element = document.getElementById('nonexistent-element');
                    element.innerHTML = 'This will fail'; // 这会触发错误
                }, 1000);
                </script>
                """
                st.components.v1.html(ui_error_script, height=0)
                st.error("💥 UI渲染错误已触发！")
                
            elif test_type == "内存泄漏模拟":
                memory_leak_script = """
                <script>
                // 模拟内存泄漏
                let leakyArray = [];
                function createMemoryLeak() {
                    for (let i = 0; i < 10000; i++) {
                        leakyArray.push(new Array(1000).fill('memory leak test'));
                    }
                    console.warn('内存泄漏模拟：创建了大量数组对象');
                }
                setTimeout(createMemoryLeak, 1000);
                </script>
                """
                st.components.v1.html(memory_leak_script, height=0)
                st.warning("⚠️ 内存泄漏模拟已执行！")
    
    with col2:
        st.subheader("📊 Bug检测结果")
        
        if 'test_bug_report' in st.session_state:
            bug_report = st.session_state.test_bug_report
            
            st.success("🎯 Bug检测系统成功捕获并分析了错误！")
            
            st.markdown("### 📋 自动生成的Bug报告")
            st.write(f"**Bug ID**: {bug_report['id']}")
            st.write(f"**错误类型**: {bug_report['error']['type']}")
            st.write(f"**严重程度**: {bug_report['error']['severity']}")
            st.write(f"**错误消息**: {bug_report['error']['message']}")
            
            st.markdown("### 🔧 自动修复建议")
            for suggestion in bug_report['suggested_fixes']:
                st.write(f"• {suggestion}")
                
            with st.expander("查看完整报告"):
                st.json(bug_report)
        else:
            st.info("👆 请执行Bug测试来查看检测结果")
        
        # 实时错误监控状态
        st.markdown("### 🔍 实时监控状态")
        monitoring_status = """
        <div style="background: #f0f2f6; padding: 15px; border-radius: 5px;">
            <h5>📡 监控组件状态</h5>
            <p>✅ JavaScript错误监控: 活跃</p>
            <p>✅ API性能监控: 活跃</p>
            <p>✅ Bug报告生成器: 就绪</p>
            <p>✅ 数据库连接: 正常</p>
        </div>
        """
        st.markdown(monitoring_status, unsafe_allow_html=True)

def show_deep_inspection_features():
    """深度检查功能演示"""
    st.header("📋 深度检查功能")
    
    st.subheader("🔍 系统深度诊断")
    
    tab1, tab2, tab3, tab4 = st.tabs(["🏥 健康检查", "📊 性能分析", "🔬 错误分析", "🎯 智能建议"])
    
    with tab1:
        st.markdown("### 🏥 系统健康检查")
        
        if st.button("🚀 执行全面健康检查", key="health_check"):
            with st.spinner("正在执行系统健康检查..."):
                time.sleep(2)  # 模拟检查过程
                
                health_results = {
                    "数据库连接": {"状态": "✅ 正常", "响应时间": "5ms", "详情": "SQLite连接正常"},
                    "JavaScript监控": {"状态": "✅ 正常", "覆盖率": "100%", "详情": "错误捕获机制正常"},
                    "API端点": {"状态": "✅ 正常", "可用性": "100%", "详情": "所有8个端点响应正常"},
                    "Bug报告生成": {"状态": "✅ 正常", "处理能力": "1000/min", "详情": "智能分析引擎正常"},
                    "测试框架": {"状态": "✅ 正常", "覆盖率": "85%", "详情": "E2E测试框架就绪"}
                }
                
                for component, result in health_results.items():
                    col1, col2, col3 = st.columns([2, 1, 3])
                    with col1:
                        st.write(f"**{component}**")
                    with col2:
                        st.write(result["状态"])
                    with col3:
                        st.write(result["详情"])
                
                st.success("🎉 系统健康检查完成！所有组件运行正常。")
    
    with tab2:
        st.markdown("### 📊 深度性能分析")
        
        if st.button("📈 执行性能分析", key="performance_analysis"):
            with st.spinner("正在分析系统性能..."):
                time.sleep(3)
                
                # 模拟性能分析结果
                perf_metrics = {
                    "响应时间分析": {
                        "平均响应时间": "45ms",
                        "95%分位数": "120ms",
                        "99%分位数": "250ms",
                        "最大响应时间": "800ms"
                    },
                    "资源使用分析": {
                        "CPU使用率": "15%",
                        "内存使用率": "32%",
                        "磁盘I/O": "低",
                        "网络带宽": "正常"
                    },
                    "错误率分析": {
                        "总错误率": "0.05%",
                        "JavaScript错误": "0.02%",
                        "API错误": "0.03%",
                        "系统错误": "0.00%"
                    }
                }
                
                for category, metrics in perf_metrics.items():
                    st.markdown(f"#### {category}")
                    cols = st.columns(len(metrics))
                    for i, (metric, value) in enumerate(metrics.items()):
                        with cols[i]:
                            st.metric(metric, value)
                
                st.success("📊 性能分析完成！系统性能良好。")
    
    with tab3:
        st.markdown("### 🔬 错误深度分析")
        
        if st.button("🔍 执行错误分析", key="error_analysis"):
            with st.spinner("正在分析错误模式..."):
                time.sleep(2)
                
                # 模拟错误分析结果
                error_analysis = {
                    "错误趋势": "过去24小时错误数量下降15%",
                    "高频错误": [
                        {"类型": "TypeError", "次数": 12, "趋势": "↓"},
                        {"类型": "NetworkError", "次数": 8, "趋势": "→"},
                        {"类型": "ReferenceError", "次数": 3, "趋势": "↑"}
                    ],
                    "错误分布": {
                        "前端错误": "65%",
                        "后端错误": "25%",
                        "网络错误": "10%"
                    },
                    "影响页面": [
                        {"页面": "prediction_analysis", "错误数": 8},
                        {"页面": "data_overview", "错误数": 5},
                        {"页面": "intelligent_fusion", "错误数": 3}
                    ]
                }
                
                st.markdown("#### 📈 错误趋势")
                st.info(error_analysis["错误趋势"])
                
                st.markdown("#### 🔥 高频错误类型")
                for error in error_analysis["高频错误"]:
                    st.write(f"• **{error['类型']}**: {error['次数']}次 {error['趋势']}")
                
                st.markdown("#### 📊 错误分布")
                dist_data = pd.DataFrame([
                    {"类型": k, "占比": v} for k, v in error_analysis["错误分布"].items()
                ])
                st.dataframe(dist_data, use_container_width=True)
                
                st.success("🔬 错误分析完成！发现了一些需要关注的模式。")
    
    with tab4:
        st.markdown("### 🎯 智能优化建议")
        
        if st.button("🧠 生成智能建议", key="smart_suggestions"):
            with st.spinner("AI正在分析系统状态并生成建议..."):
                time.sleep(3)
                
                suggestions = [
                    {
                        "类别": "🚀 性能优化",
                        "建议": "为prediction_analysis页面添加结果缓存机制",
                        "优先级": "高",
                        "预期收益": "响应时间减少40%"
                    },
                    {
                        "类别": "🛡️ 错误预防",
                        "建议": "在数据处理前添加类型检查",
                        "优先级": "中",
                        "预期收益": "减少TypeError 60%"
                    },
                    {
                        "类别": "📊 监控增强",
                        "建议": "增加用户行为热力图分析",
                        "优先级": "中",
                        "预期收益": "提升用户体验洞察"
                    },
                    {
                        "类别": "🔧 代码质量",
                        "建议": "为高频API添加限流机制",
                        "优先级": "低",
                        "预期收益": "提升系统稳定性"
                    }
                ]
                
                for suggestion in suggestions:
                    with st.expander(f"{suggestion['类别']}: {suggestion['建议']}"):
                        st.write(f"**优先级**: {suggestion['优先级']}")
                        st.write(f"**预期收益**: {suggestion['预期收益']}")
                        
                        if st.button(f"实施建议", key=f"implement_{suggestion['类别']}"):
                            st.success(f"✅ 建议已标记为待实施: {suggestion['建议']}")
                
                st.success("🧠 智能建议生成完成！请根据优先级逐步实施。")

def show_usage_guide():
    """使用指南"""
    st.header("🎯 Bug检测系统使用指南")
    
    st.markdown("""
    ## 🚀 快速开始
    
    ### 1. 系统集成
    ```python
    # 在你的Streamlit应用中集成Bug检测
    from src.bug_detection.monitoring.js_monitor import inject_js_monitor
    from src.bug_detection.core.database_manager import DatabaseManager
    
    # 注入JavaScript错误监控
    inject_js_monitor("your_page_name")
    
    # 初始化数据库管理器
    db_manager = DatabaseManager()
    ```
    
    ### 2. API监控集成
    ```python
    # 在FastAPI应用中添加性能监控
    from src.bug_detection.monitoring.api_monitor import add_monitoring_to_app
    
    app = FastAPI()
    add_monitoring_to_app(app, database_manager, "your_app_name")
    ```
    
    ### 3. Bug报告生成
    ```python
    # 生成智能Bug报告
    from src.bug_detection.feedback.bug_reporter import IntelligentBugReporter
    
    bug_reporter = IntelligentBugReporter(db_manager)
    bug_report = bug_reporter.generate_enhanced_report(error_data)
    ```
    
    ## 📋 深度检查功能
    
    ### 🔍 JavaScript错误监控
    - **实时错误捕获**: 自动捕获页面中的JavaScript错误
    - **错误分类**: 按类型、严重程度自动分类
    - **会话追踪**: 跟踪用户会话中的错误模式
    - **自动上报**: 错误信息自动发送到后端分析
    
    ### 📊 性能监控
    - **API响应时间**: 监控所有API端点的响应时间
    - **错误率统计**: 实时统计API调用错误率
    - **性能瓶颈识别**: 自动识别慢查询和性能问题
    - **资源使用监控**: 监控CPU、内存、网络使用情况
    
    ### 🧠 智能分析
    - **错误模式识别**: 识别重复出现的错误模式
    - **影响评估**: 评估错误对用户和业务的影响
    - **修复建议**: 基于错误类型自动生成修复建议
    - **相似Bug识别**: 查找历史上的相似问题
    
    ### 🧪 自动化测试
    - **E2E页面测试**: 自动测试所有17个功能页面
    - **视觉回归测试**: 检测UI变化和回归问题
    - **性能基准测试**: 建立性能基线并监控变化
    - **集成测试**: 验证系统各组件间的集成
    
    ## 🎮 实际使用场景
    
    ### 场景1: 发现JavaScript错误
    1. 用户在使用预测分析页面时遇到错误
    2. JavaScript监控自动捕获错误信息
    3. 系统自动生成Bug报告并分类
    4. 开发团队收到告警通知
    5. 根据修复建议快速解决问题
    
    ### 场景2: API性能问题
    1. 系统检测到某个API响应时间过长
    2. 性能监控自动记录详细指标
    3. 系统分析性能瓶颈原因
    4. 生成优化建议报告
    5. 开发团队根据建议进行优化
    
    ### 场景3: 用户体验问题
    1. 用户行为分析发现异常操作模式
    2. 系统识别可能的用户体验问题
    3. 生成用户体验优化建议
    4. 产品团队根据数据改进界面
    5. 持续监控改进效果
    
    ## 📈 最佳实践
    
    ### ✅ 推荐做法
    - 在所有页面都集成JavaScript错误监控
    - 定期查看Bug报告和性能指标
    - 根据智能建议优先处理高影响问题
    - 建立错误处理的标准流程
    - 定期执行自动化测试
    
    ### ❌ 避免事项
    - 不要忽略低优先级的警告
    - 不要在生产环境中禁用监控
    - 不要手动处理可以自动化的任务
    - 不要忽略用户行为数据的价值
    
    ## 🔧 高级配置
    
    ### 自定义错误处理
    ```python
    # 自定义错误分类规则
    def custom_error_classifier(error_data):
        if 'payment' in error_data.get('message', '').lower():
            return 'critical'
        return 'medium'
    
    bug_reporter.set_custom_classifier(custom_error_classifier)
    ```
    
    ### 性能阈值配置
    ```python
    # 设置自定义性能阈值
    monitor_config = {
        'slow_request_threshold': 1.0,  # 1秒
        'error_rate_threshold': 0.05,   # 5%
        'memory_usage_threshold': 0.8   # 80%
    }
    ```
    
    ## 📞 技术支持
    
    如果在使用过程中遇到问题，请：
    1. 查看系统健康检查结果
    2. 检查错误日志和监控数据
    3. 参考本使用指南的故障排除部分
    4. 联系技术支持团队
    
    ---
    
    **系统版本**: v1.0.0  
    **最后更新**: 2025年7月24日  
    **技术支持**: Bug检测系统开发团队
    """)

if __name__ == "__main__":
    main()
