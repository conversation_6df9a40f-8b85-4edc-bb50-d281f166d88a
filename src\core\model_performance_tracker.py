"""
模型性能跟踪系统
实现ModelPerformanceTracker类，建立模型历史预测记录和准确率统计系统
"""

import json
import logging
import sqlite3
from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple


@dataclass
class PredictionRecord:
    """预测记录数据结构"""
    period_number: str
    model_name: str
    predicted_number: str
    confidence: float
    actual_number: Optional[str] = None
    is_hit: Optional[bool] = None
    prediction_date: Optional[datetime] = None

@dataclass
class ModelAccuracy:
    """模型准确率统计数据结构"""
    model_name: str
    window_size: int
    accuracy_rate: float
    total_predictions: int
    correct_predictions: int
    last_updated: datetime

class ModelPerformanceTracker:
    """模型性能跟踪器"""
    
    def __init__(self, db_path: str = "data/model_library.db", window_size: int = 50):
        """
        初始化模型性能跟踪器
        
        Args:
            db_path: 数据库路径
            window_size: 滑动窗口大小，用于计算准确率
        """
        self.db_path = db_path
        self.window_size = window_size
        self.models = ['markov_enhanced', 'deep_learning_cnn_lstm', 'trend_analyzer', 'intelligent_fusion']
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据库
        self._init_database()
    
    def _init_database(self):
        """初始化数据库表结构"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建模型预测历史表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS model_predictions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        period_number TEXT NOT NULL,
                        model_name TEXT NOT NULL,
                        predicted_number TEXT NOT NULL,
                        confidence REAL NOT NULL,
                        actual_number TEXT,
                        is_hit BOOLEAN,
                        prediction_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(period_number, model_name)
                    )
                """)
                
                # 创建模型准确率统计表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS model_accuracy (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        model_name TEXT NOT NULL,
                        window_size INTEGER NOT NULL,
                        accuracy_rate REAL NOT NULL,
                        total_predictions INTEGER NOT NULL,
                        correct_predictions INTEGER NOT NULL,
                        last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(model_name, window_size)
                    )
                """)
                
                # 创建索引优化查询性能
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_model_predictions_period ON model_predictions(period_number)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_model_predictions_model ON model_predictions(model_name)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_model_predictions_date ON model_predictions(prediction_date)")
                
                conn.commit()
                self.logger.info("数据库初始化完成")
                
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    def track_prediction(self, model_name: str, predicted: str, actual: str, period: str, confidence: float = 0.5) -> None:
        """
        记录模型预测结果并更新准确率统计
        
        Args:
            model_name: 模型名称
            predicted: 预测号码
            actual: 实际开奖号码
            period: 期号
            confidence: 预测置信度
        """
        try:
            is_hit = predicted == actual
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 插入或更新预测记录
                cursor.execute("""
                    INSERT OR REPLACE INTO model_predictions 
                    (period_number, model_name, predicted_number, confidence, actual_number, is_hit, prediction_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (period, model_name, predicted, confidence, actual, is_hit, datetime.now()))
                
                conn.commit()
            
            # 更新准确率统计
            self._update_accuracy_stats(model_name)
            
            self.logger.info(f"记录预测结果: {model_name} - {period} - {predicted} -> {actual} ({'命中' if is_hit else '未命中'})")
            
        except Exception as e:
            self.logger.error(f"记录预测结果失败: {e}")
            raise
    
    def get_model_accuracy(self, model_name: str, window_size: int = None) -> float:
        """
        获取模型在滑动窗口内的准确率
        
        Args:
            model_name: 模型名称
            window_size: 窗口大小，默认使用初始化时的窗口大小
            
        Returns:
            准确率 (0.0-1.0)
        """
        if window_size is None:
            window_size = self.window_size
            
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取最近的预测记录
                cursor.execute("""
                    SELECT is_hit FROM model_predictions 
                    WHERE model_name = ? AND actual_number IS NOT NULL
                    ORDER BY prediction_date DESC 
                    LIMIT ?
                """, (model_name, window_size))
                
                results = cursor.fetchall()
                
                if not results:
                    return 0.25  # 默认权重，如果没有历史数据
                
                correct_count = sum(1 for (is_hit,) in results if is_hit)
                total_count = len(results)
                
                return correct_count / total_count if total_count > 0 else 0.25
                
        except Exception as e:
            self.logger.error(f"获取模型准确率失败: {e}")
            return 0.25
    
    def calculate_dynamic_weights(self) -> Dict[str, float]:
        """
        基于历史准确率计算动态权重，使用指数加权提升高性能模型权重

        Returns:
            模型权重字典
        """
        try:
            accuracies = {}
            for model in self.models:
                accuracies[model] = self.get_model_accuracy(model)

            # 使用指数加权提升高性能模型的影响
            total_accuracy = sum(accuracies.values())
            if total_accuracy == 0:
                # 如果所有模型准确率都为0，使用均等权重
                return {model: 0.25 for model in self.models}

            # 指数加权：accuracy^2 / total_accuracy^2 * model_count
            exponential_weights = {}
            for model, accuracy in accuracies.items():
                exponential_weight = (accuracy ** 2) / (total_accuracy ** 2) * len(self.models)
                exponential_weights[model] = max(exponential_weight, 0.1)  # 最小权重0.1

            # 归一化权重
            total_weight = sum(exponential_weights.values())
            normalized_weights = {model: weight/total_weight for model, weight in exponential_weights.items()}

            self.logger.info(f"计算动态权重: {normalized_weights}")
            return normalized_weights

        except Exception as e:
            self.logger.error(f"计算动态权重失败: {e}")
            return {model: 0.25 for model in self.models}
    
    def get_model_performance_summary(self) -> Dict[str, Any]:
        """
        获取所有模型的性能摘要
        
        Returns:
            性能摘要字典
        """
        try:
            summary = {}
            
            for model in self.models:
                accuracy = self.get_model_accuracy(model)
                
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    # 获取总预测次数
                    cursor.execute("""
                        SELECT COUNT(*) FROM model_predictions 
                        WHERE model_name = ? AND actual_number IS NOT NULL
                    """, (model,))
                    total_predictions = cursor.fetchone()[0]
                    
                    # 获取命中次数
                    cursor.execute("""
                        SELECT COUNT(*) FROM model_predictions 
                        WHERE model_name = ? AND is_hit = 1
                    """, (model,))
                    correct_predictions = cursor.fetchone()[0]
                    
                    # 获取最近预测时间
                    cursor.execute("""
                        SELECT MAX(prediction_date) FROM model_predictions 
                        WHERE model_name = ?
                    """, (model,))
                    last_prediction = cursor.fetchone()[0]
                
                summary[model] = {
                    'accuracy_rate': accuracy,
                    'total_predictions': total_predictions,
                    'correct_predictions': correct_predictions,
                    'last_prediction': last_prediction,
                    'window_size': self.window_size
                }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"获取性能摘要失败: {e}")
            return {}
    
    def _update_accuracy_stats(self, model_name: str) -> None:
        """更新模型准确率统计"""
        try:
            accuracy = self.get_model_accuracy(model_name)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取统计数据
                cursor.execute("""
                    SELECT COUNT(*), SUM(CASE WHEN is_hit = 1 THEN 1 ELSE 0 END)
                    FROM model_predictions 
                    WHERE model_name = ? AND actual_number IS NOT NULL
                    ORDER BY prediction_date DESC 
                    LIMIT ?
                """, (model_name, self.window_size))
                
                result = cursor.fetchone()
                total_predictions = result[0] if result else 0
                correct_predictions = result[1] if result else 0
                
                # 插入或更新准确率统计
                cursor.execute("""
                    INSERT OR REPLACE INTO model_accuracy 
                    (model_name, window_size, accuracy_rate, total_predictions, correct_predictions, last_updated)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (model_name, self.window_size, accuracy, total_predictions, correct_predictions, datetime.now()))
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"更新准确率统计失败: {e}")
    
    def get_recent_predictions(self, model_name: str, count: int = 10) -> List[PredictionRecord]:
        """
        获取模型最近的预测记录
        
        Args:
            model_name: 模型名称
            count: 获取记录数量
            
        Returns:
            预测记录列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT period_number, model_name, predicted_number, confidence, 
                           actual_number, is_hit, prediction_date
                    FROM model_predictions 
                    WHERE model_name = ?
                    ORDER BY prediction_date DESC 
                    LIMIT ?
                """, (model_name, count))
                
                records = []
                for row in cursor.fetchall():
                    record = PredictionRecord(
                        period_number=row[0],
                        model_name=row[1],
                        predicted_number=row[2],
                        confidence=row[3],
                        actual_number=row[4],
                        is_hit=row[5],
                        prediction_date=datetime.fromisoformat(row[6]) if row[6] else None
                    )
                    records.append(record)
                
                return records
                
        except Exception as e:
            self.logger.error(f"获取最近预测记录失败: {e}")
            return []
    
    def clear_old_records(self, days_to_keep: int = 365) -> None:
        """
        清理旧的预测记录
        
        Args:
            days_to_keep: 保留天数
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    DELETE FROM model_predictions 
                    WHERE prediction_date < ?
                """, (cutoff_date,))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                self.logger.info(f"清理了 {deleted_count} 条旧预测记录")
                
        except Exception as e:
            self.logger.error(f"清理旧记录失败: {e}")
