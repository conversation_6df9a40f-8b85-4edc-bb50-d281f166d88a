#!/usr/bin/env python3
"""
集成测试
Integration Tests

验证整个闭环系统的协调工作
"""

import pytest
import asyncio
import tempfile
import os
from datetime import datetime
from pathlib import Path

# 导入系统组件
import sys
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from src.core.unified_prediction_storage import UnifiedPredictionStorage, PredictionRecord
from src.core.draw_trigger_system import DrawTriggerSystem
from src.analysis.prediction_deviation_analyzer import PredictionDeviationAnalyzer
from src.analysis.model_weakness_identifier import ModelWeaknessIdentifier
from src.analysis.success_factor_extractor import SuccessFactorExtractor
from src.optimization.optimization_advisor import OptimizationAdvisor
from src.optimization.parameter_backtesting_engine import ParameterBacktestingEngine
from src.optimization.auto_parameter_applier import AutoParameterApplier


class TestIntegration:
    """集成测试类"""
    
    @pytest.fixture
    def temp_db_path(self):
        """创建临时数据库路径"""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            temp_path = f.name
        yield temp_path
        # 清理
        if os.path.exists(temp_path):
            os.unlink(temp_path)
    
    @pytest.fixture
    def storage_system(self, temp_db_path):
        """创建存储系统"""
        return UnifiedPredictionStorage(temp_db_path)
    
    @pytest.fixture
    def sample_predictions(self):
        """创建示例预测数据"""
        predictions = []
        for i in range(10):
            prediction = PredictionRecord(
                period_number=f"202519{i:02d}",
                model_name=f"model_{i % 3}",
                predicted_numbers=f"{i%10}{(i+1)%10}{(i+2)%10}",
                confidence=0.5 + (i % 5) * 0.1,
                prediction_time=datetime.now(),
                actual_numbers=f"{(i+1)%10}{(i+2)%10}{(i+3)%10}",
                is_verified=True,
                accuracy_score=0.3 + (i % 4) * 0.2
            )
            predictions.append(prediction)
        return predictions
    
    def test_storage_system_basic_operations(self, storage_system, sample_predictions):
        """测试存储系统基本操作"""
        # 测试保存预测记录
        for prediction in sample_predictions:
            record_id = storage_system.save_prediction_record(prediction)
            assert record_id > 0
        
        # 测试获取期号预测
        period_predictions = storage_system.get_period_predictions("2025190")
        assert len(period_predictions) > 0
        
        # 测试更新预测结果
        storage_system.update_prediction_result("2025190", "123")
        
        # 测试获取统计信息
        stats = storage_system.get_statistics()
        assert stats['total_predictions'] == len(sample_predictions)
        assert 'model_counts' in stats
    
    @pytest.mark.asyncio
    async def test_draw_trigger_system(self, storage_system, sample_predictions):
        """测试开奖触发系统"""
        # 保存示例预测
        for prediction in sample_predictions:
            storage_system.save_prediction_record(prediction)
        
        # 创建触发系统
        trigger_system = DrawTriggerSystem(storage_system)
        
        # 注册示例分析器
        async def mock_analyzer(predictions, actual_numbers):
            return {
                'analyzer_type': 'mock_analysis',
                'predictions_count': len(predictions),
                'actual_numbers': actual_numbers
            }
        
        trigger_system.register_analyzer('mock_analyzer', mock_analyzer)
        
        # 触发分析
        result = await trigger_system.on_draw_announced("2025190", "123")
        
        assert result.period_number == "2025190"
        assert result.total_predictions > 0
        assert 'mock_analyzer' in result.analysis_details
    
    def test_deviation_analyzer(self, sample_predictions):
        """测试偏差分析器"""
        analyzer = PredictionDeviationAnalyzer()
        
        # 测试单个预测的偏差分析
        prediction = sample_predictions[0]
        prediction.actual_numbers = "124"  # 设置实际结果
        
        result = analyzer.analyze_deviation(prediction, prediction.actual_numbers)
        
        assert result.numerical_deviation is not None
        assert result.pattern_deviation is not None
        assert result.confidence_calibration is not None
        assert 0 <= result.overall_score <= 1
    
    def test_weakness_identifier(self, sample_predictions):
        """测试弱点识别器"""
        identifier = ModelWeaknessIdentifier()
        
        # 测试模型弱点识别
        result = identifier.identify_weaknesses("model_0", sample_predictions[:5])
        
        assert result.model_name == "model_0"
        assert isinstance(result.identified_weaknesses, dict)
        assert isinstance(result.severity_assessment, dict)
        assert isinstance(result.improvement_suggestions, dict)
        assert 0 <= result.confidence_score <= 1
    
    def test_success_factor_extractor(self, sample_predictions):
        """测试成功因子提取器"""
        extractor = SuccessFactorExtractor()
        
        # 创建一些成功的预测
        successful_predictions = []
        for i, prediction in enumerate(sample_predictions[:3]):
            prediction.accuracy_score = 1.0  # 设置为完全正确
            prediction.actual_numbers = prediction.predicted_numbers
            successful_predictions.append(prediction)
        
        result = extractor.extract_success_factors("model_0", sample_predictions)
        
        assert result.model_name == "model_0"
        assert isinstance(result.top_factors, list)
        assert isinstance(result.detailed_analysis, dict)
        assert 0 <= result.confidence_score <= 1
    
    def test_optimization_advisor(self):
        """测试优化建议生成器"""
        advisor = OptimizationAdvisor()
        
        # 模拟分析结果
        mock_analysis = {
            'deviation_analysis': {
                'overall_score': 0.4,
                'numerical_deviation': {
                    'sum_deviation': {'absolute_deviation': 5}
                }
            },
            'weakness_identification': {
                'identified_weaknesses': {
                    'overfitting': {'severity_score': 0.7}
                }
            }
        }
        
        result = advisor.generate_suggestions("test_model", mock_analysis)
        
        assert result.model_name == "test_model"
        assert len(result.optimization_strategies) > 0
        assert isinstance(result.expected_improvements, dict)
        assert 0 <= result.confidence_score <= 1
    
    @pytest.mark.asyncio
    async def test_parameter_backtesting_engine(self):
        """测试参数回测引擎"""
        import pandas as pd
        
        engine = ParameterBacktestingEngine()
        
        # 创建模拟历史数据
        historical_data = pd.DataFrame({
            'period': [f"202519{i:02d}" for i in range(1, 11)],
            'numbers': [f"{i%10}{(i+1)%10}{(i+2)%10}" for i in range(1, 11)]
        })
        
        result = await engine.find_optimal_parameters(
            "test_model", "2025194", "123", historical_data
        )
        
        assert result.model_name == "test_model"
        assert result.target_period == "2025194"
        assert result.actual_result == "123"
        assert isinstance(result.optimal_parameters, dict)
        assert 0 <= result.confidence_score <= 1
    
    @pytest.mark.asyncio
    async def test_parameter_applier(self):
        """测试参数应用系统"""
        from src.optimization.parameter_backtesting_engine import BacktestingResult
        
        applier = AutoParameterApplier()
        
        # 创建模拟优化结果
        mock_result = BacktestingResult(
            model_name="test_model",
            target_period="2025194",
            actual_result="123",
            optimal_parameters={
                'test_param': 0.5,
                'another_param': 10
            },
            optimization_process={},
            validation_results={},
            improvement_metrics={},
            parameter_sensitivity={},
            confidence_score=0.8,
            analysis_timestamp=datetime.now()
        )
        
        optimization_results = {"test_model": mock_result}
        
        result = await applier.apply_optimizations(optimization_results)
        
        assert "test_model" in result
        assert isinstance(result["test_model"].success, bool)
        assert result["test_model"].rollback_available
    
    @pytest.mark.asyncio
    async def test_complete_workflow(self, storage_system, sample_predictions):
        """测试完整工作流程"""
        # 1. 保存预测记录
        for prediction in sample_predictions:
            storage_system.save_prediction_record(prediction)
        
        # 2. 创建系统组件
        trigger_system = DrawTriggerSystem(storage_system)
        deviation_analyzer = PredictionDeviationAnalyzer()
        weakness_identifier = ModelWeaknessIdentifier()
        success_extractor = SuccessFactorExtractor()
        optimization_advisor = OptimizationAdvisor()
        
        # 3. 注册分析器
        async def comprehensive_analyzer(predictions, actual_numbers):
            analysis_results = {}
            
            # 偏差分析
            for prediction in predictions:
                if prediction.is_verified:
                    deviation_result = deviation_analyzer.analyze_deviation(prediction, actual_numbers)
                    analysis_results[f'{prediction.model_name}_deviation'] = {
                        'overall_score': deviation_result.overall_score
                    }
            
            return analysis_results
        
        trigger_system.register_analyzer('comprehensive_analyzer', comprehensive_analyzer)
        
        # 4. 触发完整分析流程
        result = await trigger_system.on_draw_announced("2025190", "123")
        
        # 5. 验证结果
        assert result.period_number == "2025190"
        assert result.total_predictions > 0
        assert 'comprehensive_analyzer' in result.analysis_details
        
        # 6. 生成优化建议
        optimization_result = optimization_advisor.generate_suggestions(
            "model_0", 
            result.analysis_details
        )
        
        assert optimization_result.model_name == "model_0"
        assert len(optimization_result.optimization_strategies) > 0
    
    def test_system_resilience(self, storage_system):
        """测试系统韧性"""
        # 测试空数据处理
        empty_predictions = storage_system.get_period_predictions("nonexistent")
        assert len(empty_predictions) == 0
        
        # 测试错误输入处理
        analyzer = PredictionDeviationAnalyzer()
        
        # 创建无效预测记录
        invalid_prediction = PredictionRecord(
            period_number="",
            model_name="",
            predicted_numbers="",
            confidence=0.0
        )
        
        # 系统应该能够处理无效输入而不崩溃
        try:
            result = analyzer.analyze_deviation(invalid_prediction, "123")
            # 如果没有抛出异常，检查结果是否合理
            assert result is not None
        except Exception as e:
            # 如果抛出异常，应该是可预期的异常
            assert isinstance(e, (ValueError, TypeError))
    
    def test_performance_benchmarks(self, storage_system, sample_predictions):
        """测试性能基准"""
        import time
        
        # 测试存储性能
        start_time = time.time()
        for prediction in sample_predictions:
            storage_system.save_prediction_record(prediction)
        storage_time = time.time() - start_time
        
        # 存储操作应该在合理时间内完成
        assert storage_time < 5.0  # 5秒内完成
        
        # 测试查询性能
        start_time = time.time()
        for i in range(5):
            storage_system.get_period_predictions(f"202519{i:02d}")
        query_time = time.time() - start_time
        
        # 查询操作应该很快
        assert query_time < 1.0  # 1秒内完成


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
