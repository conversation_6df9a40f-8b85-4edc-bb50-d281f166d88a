"""
模型库API路由

提供模型库的RESTful API接口
"""

import json
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Body, HTTPException, Query

from src.model_library.base_model import (ModelInfo, ModelStatusInfo,
                                          PredictionResult)
from src.model_library.database_manager import DatabaseManager
from src.model_library.exceptions import ModelLibraryError, ModelNotFoundError
from src.model_library.model_registry import ModelRegistry

# 创建路由器
router = APIRouter(
    prefix="/api/model-library",
    tags=["model_library"],
    responses={404: {"description": "Not found"}}
)

# 全局实例
model_registry = ModelRegistry()
db_manager = DatabaseManager()


@router.get("/models", response_model=List[Dict[str, Any]])
async def get_models(
    active_only: bool = Query(True, description="是否只返回活跃模型"),
    model_type: Optional[str] = Query(None, description="按模型类型筛选")
):
    """获取模型列表"""
    try:
        models = model_registry.list_models(active_only=active_only)
        
        # 转换为字典格式
        model_list = []
        for model_info in models:
            if model_type and model_info.model_type.value != model_type:
                continue
                
            model_dict = {
                "model_id": model_info.model_id,
                "name": model_info.name,
                "description": model_info.description,
                "model_type": model_info.model_type.value,
                "version": model_info.version,
                "author": model_info.author,
                "created_at": model_info.created_at.isoformat(),
                "updated_at": model_info.updated_at.isoformat(),
                "is_active": model_info.is_active
            }
            
            # 获取模型状态
            status_info = model_registry.get_model_status(model_info.model_id)
            if status_info:
                model_dict["status"] = {
                    "status": status_info.status.value,
                    "data_ready": status_info.data_ready,
                    "features_ready": status_info.features_ready,
                    "trained": status_info.trained,
                    "up_to_date": status_info.up_to_date,
                    "training_data_size": status_info.training_data_size,
                    "last_check_time": status_info.last_check_time.isoformat()
                }
            
            model_list.append(model_dict)
        
        return model_list
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模型列表失败: {str(e)}")


@router.get("/models/{model_id}", response_model=Dict[str, Any])
async def get_model_details(model_id: str):
    """获取模型详细信息"""
    try:
        # 获取模型信息
        model_info = model_registry.get_model_info(model_id)
        if not model_info:
            raise HTTPException(status_code=404, detail=f"模型 '{model_id}' 未找到")
        
        # 获取模型状态
        status_info = model_registry.get_model_status(model_id)
        
        # 获取性能统计
        performance = db_manager.get_model_performance(model_id)
        
        # 构建响应
        response = {
            "model_id": model_info.model_id,
            "name": model_info.name,
            "description": model_info.description,
            "model_type": model_info.model_type.value,
            "version": model_info.version,
            "author": model_info.author,
            "created_at": model_info.created_at.isoformat(),
            "updated_at": model_info.updated_at.isoformat(),
            "data_requirements": model_info.data_requirements,
            "feature_engineering": model_info.feature_engineering,
            "parameters": model_info.parameters,
            "is_active": model_info.is_active
        }
        
        if status_info:
            response["status"] = {
                "status": status_info.status.value,
                "data_ready": status_info.data_ready,
                "features_ready": status_info.features_ready,
                "trained": status_info.trained,
                "up_to_date": status_info.up_to_date,
                "training_data_size": status_info.training_data_size,
                "last_training_time": status_info.last_training_time.isoformat() if status_info.last_training_time else None,
                "last_check_time": status_info.last_check_time.isoformat(),
                "error_message": status_info.error_message
            }
        
        if performance:
            response["performance"] = performance
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模型详情失败: {str(e)}")


@router.get("/models/{model_id}/status", response_model=Dict[str, Any])
async def get_model_status(model_id: str):
    """获取模型状态"""
    try:
        status_info = model_registry.get_model_status(model_id)
        if not status_info:
            raise HTTPException(status_code=404, detail=f"模型 '{model_id}' 未找到")
        
        return {
            "model_id": status_info.model_id,
            "status": status_info.status.value,
            "data_ready": status_info.data_ready,
            "features_ready": status_info.features_ready,
            "trained": status_info.trained,
            "up_to_date": status_info.up_to_date,
            "training_data_size": status_info.training_data_size,
            "last_training_time": status_info.last_training_time.isoformat() if status_info.last_training_time else None,
            "last_check_time": status_info.last_check_time.isoformat(),
            "error_message": status_info.error_message
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模型状态失败: {str(e)}")


@router.post("/models/{model_id}/predict", response_model=Dict[str, Any])
async def predict_single_model(
    model_id: str,
    request_data: Dict[str, Any] = Body(...)
):
    """单模型预测"""
    try:
        # 获取模型实例
        model = model_registry.get_model(model_id)
        if not model:
            raise HTTPException(status_code=404, detail=f"模型 '{model_id}' 未找到或未加载")
        
        # 检查模型是否就绪
        if not model.is_ready():
            raise HTTPException(status_code=400, detail=f"模型 '{model_id}' 尚未就绪")
        
        # 获取历史数据
        history = request_data.get("history", [])
        top_n = request_data.get("top_n", 3)
        
        # 执行预测
        prediction_result = model.predict(history, top_n)
        
        # 保存预测记录
        prediction_data = {
            "model_id": model_id,
            "prediction_time": prediction_result.prediction_time.isoformat(),
            "target_period": prediction_result.target_period,
            "prediction_result": {
                "百位": prediction_result.百位,
                "十位": prediction_result.十位,
                "个位": prediction_result.个位,
                "和值": prediction_result.和值,
                "跨度": prediction_result.跨度
            },
            "confidence_score": prediction_result.confidence,
            "metadata": prediction_result.metadata
        }
        
        prediction_id = db_manager.save_prediction_record(prediction_data)
        
        # 构建响应
        response = {
            "prediction_id": prediction_id,
            "model_id": model_id,
            "prediction_time": prediction_result.prediction_time.isoformat(),
            "target_period": prediction_result.target_period,
            "prediction": {
                "百位": prediction_result.百位,
                "十位": prediction_result.十位,
                "个位": prediction_result.个位,
                "和值": prediction_result.和值,
                "跨度": prediction_result.跨度
            },
            "confidence": prediction_result.confidence,
            "metadata": prediction_result.metadata
        }
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"预测失败: {str(e)}")


@router.post("/predict/combined", response_model=Dict[str, Any])
async def predict_combined_models(request_data: Dict[str, Any] = Body(...)):
    """组合模型预测"""
    try:
        model_ids = request_data.get("model_ids", [])
        strategy = request_data.get("strategy", "voting")  # voting, weighted, intersection
        weights = request_data.get("weights", {})
        history = request_data.get("history", [])
        top_n = request_data.get("top_n", 3)
        
        if not model_ids:
            raise HTTPException(status_code=400, detail="必须指定至少一个模型ID")
        
        # 获取所有模型的预测结果
        predictions = []
        for model_id in model_ids:
            model = model_registry.get_model(model_id)
            if not model:
                continue
                
            if not model.is_ready():
                continue
                
            try:
                prediction = model.predict(history, top_n)
                predictions.append({
                    "model_id": model_id,
                    "prediction": prediction,
                    "weight": weights.get(model_id, 1.0)
                })
            except Exception:
                continue
        
        if not predictions:
            raise HTTPException(status_code=400, detail="没有可用的模型进行预测")
        
        # 组合预测结果（简化实现）
        combined_result = _combine_predictions(predictions, strategy)
        
        # 保存组合预测记录
        prediction_data = {
            "model_id": "combined",
            "prediction_time": datetime.now().isoformat(),
            "target_period": combined_result["target_period"],
            "prediction_result": combined_result["prediction"],
            "confidence_score": combined_result["confidence"],
            "metadata": {
                "strategy": strategy,
                "model_ids": model_ids,
                "weights": weights
            }
        }
        
        prediction_id = db_manager.save_prediction_record(prediction_data)
        
        response = {
            "prediction_id": prediction_id,
            "strategy": strategy,
            "model_ids": model_ids,
            "prediction_time": combined_result["prediction_time"],
            "target_period": combined_result["target_period"],
            "prediction": combined_result["prediction"],
            "confidence": combined_result["confidence"],
            "individual_predictions": [
                {
                    "model_id": p["model_id"],
                    "weight": p["weight"],
                    "prediction": {
                        "百位": p["prediction"].百位,
                        "十位": p["prediction"].十位,
                        "个位": p["prediction"].个位
                    },
                    "confidence": p["prediction"].confidence
                }
                for p in predictions
            ]
        }
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"组合预测失败: {str(e)}")


@router.get("/models/{model_id}/performance", response_model=Dict[str, Any])
async def get_model_performance(model_id: str):
    """获取模型性能统计"""
    try:
        performance = db_manager.get_model_performance(model_id)
        if not performance:
            # 如果没有性能数据，计算一下
            accuracy_data = db_manager.calculate_model_accuracy(model_id)
            if accuracy_data['total_predictions'] > 0:
                db_manager.update_model_performance(model_id, accuracy_data)
                performance = db_manager.get_model_performance(model_id)
        
        if not performance:
            return {
                "model_id": model_id,
                "total_predictions": 0,
                "direct_accuracy": 0.0,
                "position_accuracy": 0.0,
                "group_accuracy": 0.0,
                "average_confidence": 0.0,
                "last_updated": None
            }
        
        return performance
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取性能统计失败: {str(e)}")


@router.get("/models/{model_id}/history", response_model=List[Dict[str, Any]])
async def get_model_prediction_history(
    model_id: str,
    limit: int = Query(50, description="返回记录数量限制")
):
    """获取模型预测历史"""
    try:
        predictions = db_manager.get_model_predictions(model_id, limit)
        return predictions
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取预测历史失败: {str(e)}")


@router.get("/performance/ranking", response_model=List[Dict[str, Any]])
async def get_performance_ranking(
    metric: str = Query("direct_accuracy", description="排序指标"),
    limit: int = Query(10, description="返回数量限制")
):
    """获取性能排行榜"""
    try:
        ranking = db_manager.get_performance_ranking(metric, limit)
        return ranking
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取排行榜失败: {str(e)}")


def _combine_predictions(predictions: List[Dict], strategy: str) -> Dict[str, Any]:
    """组合预测结果的简化实现"""
    if not predictions:
        raise ValueError("没有预测结果可组合")
    
    # 获取第一个预测作为基础
    first_prediction = predictions[0]["prediction"]
    
    if strategy == "voting":
        # 投票策略：选择出现频率最高的数字
        combined_prediction = {
            "百位": first_prediction.百位,
            "十位": first_prediction.十位,
            "个位": first_prediction.个位,
            "和值": first_prediction.和值,
            "跨度": first_prediction.跨度
        }
    elif strategy == "weighted":
        # 加权策略：根据权重计算
        combined_prediction = {
            "百位": first_prediction.百位,
            "十位": first_prediction.十位,
            "个位": first_prediction.个位,
            "和值": first_prediction.和值,
            "跨度": first_prediction.跨度
        }
    else:  # intersection
        # 交集策略：取交集
        combined_prediction = {
            "百位": first_prediction.百位,
            "十位": first_prediction.十位,
            "个位": first_prediction.个位,
            "和值": first_prediction.和值,
            "跨度": first_prediction.跨度
        }
    
    # 计算平均置信度
    avg_confidence = sum(p["prediction"].confidence * p["weight"] for p in predictions) / sum(p["weight"] for p in predictions)
    
    return {
        "prediction_time": datetime.now().isoformat(),
        "target_period": first_prediction.target_period,
        "prediction": combined_prediction,
        "confidence": avg_confidence
    }


@router.post("/models/{model_id}/refresh-status")
async def refresh_model_status(model_id: str):
    """刷新模型状态"""
    try:
        registry = ModelRegistry()
        status = registry.get_model_status(model_id, force_refresh=True)

        if status:
            return {
                "success": True,
                "status": {
                    "model_id": status.model_id,
                    "status": status.status.value,
                    "data_ready": status.data_ready,
                    "features_ready": status.features_ready,
                    "trained": status.trained,
                    "up_to_date": status.up_to_date,
                    "training_data_size": status.training_data_size,
                    "last_training_time": status.last_training_time.isoformat() if status.last_training_time else None,
                    "last_check_time": status.last_check_time.isoformat(),
                    "error_message": status.error_message
                }
            }
        else:
            return {"success": False, "error": "模型未找到"}

    except Exception as e:
        return {"success": False, "error": str(e)}
