# 🏗️ 福彩3D预测系统技术架构详解

## 📊 架构概览

**架构模式**: 前后端分离 + 微服务架构  
**技术栈**: Python 3.11.9 + Streamlit + FastAPI  
**部署模式**: 本地部署 (127.0.0.1)  
**数据存储**: SQLite + 外部数据源  

## 🎯 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层 (Streamlit)                    │
│                     127.0.0.1:8501                        │
├─────────────────────────────────────────────────────────────┤
│                    组件层 (Components)                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Navigation  │ │ PageManager │ │ ErrorHandler│          │
│  │ Component   │ │             │ │             │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                    页面层 (Pages)                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ 数据分析类   │ │ 预测工具类   │ │ 系统管理类   │          │
│  │ (5个页面)   │ │ (4个页面)   │ │ (4个页面)   │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                    API层 (FastAPI)                         │
│                     127.0.0.1:8888                        │
├─────────────────────────────────────────────────────────────┤
│                    数据层 (Data Layer)                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ SQLite DB   │ │ 外部数据源   │ │ 缓存系统     │          │
│  │ (8351条)    │ │ (17500.cn)  │ │ (内存缓存)   │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## 🧩 核心组件详解

### 1. 导航组件 (NavigationComponent)

**文件**: `src/ui/components/navigation.py`  
**职责**: 统一导航管理，解决Streamlit selectbox限制  

```python
class NavigationComponent:
    def __init__(self):
        self.navigation_modes = {
            "快速访问": self._show_quick_access,
            "分类浏览": self._show_category_browse, 
            "我的收藏": self._show_favorites
        }
    
    def render(self) -> str:
        # 渲染导航界面，返回选中的页面
        pass
```

**核心特性**:
- 三种导航模式无缝切换
- 智能推荐基于使用频率
- 支持17个页面的完整导航
- 收藏功能持久化存储

### 2. 页面管理器 (PageManager)

**文件**: `src/ui/components/page_manager.py`  
**职责**: 统一页面路由和错误处理  

```python
class PageManager:
    def __init__(self):
        self.pages = {
            "📈 数据概览": "data_overview",
            "🎯 预测分析": "prediction_analysis",
            # ... 其他15个页面
        }
    
    def load_page(self, page_name: str):
        # 动态加载页面模块，统一错误处理
        pass
```

**核心特性**:
- 动态模块加载机制
- 统一错误处理和恢复
- 页面状态管理
- 性能优化和缓存

### 3. 错误处理系统

**核心文件**:
- `error_handler.py`: 错误处理核心
- `error_config.py`: 错误配置管理  
- `error_middleware.py`: 错误中间件

**架构设计**:
```python
# 错误处理层次结构
ErrorHandler (核心处理器)
├── ErrorRecovery (错误恢复)
├── SmartErrorHandler (智能处理)
├── ErrorLogger (日志记录)
└── ErrorMiddleware (中间件)
```

**特性**:
- 8种错误类型完整覆盖
- 12种智能恢复策略
- JSON格式结构化日志
- 装饰器模式全局保护

### 4. 用户偏好系统

**文件**: `src/ui/components/user_preferences.py`  
**职责**: 用户行为分析和偏好学习  

```python
class UserPreferenceManager:
    def track_page_visit(self, page_name: str):
        # 记录页面访问
        pass
    
    def get_recommendations(self) -> List[str]:
        # 基于使用频率生成推荐
        pass
    
    def manage_favorites(self, page_name: str, action: str):
        # 收藏管理
        pass
```

## 📊 数据架构设计

### 数据流向图

```
外部数据源 (17500.cn)
    ↓ (HTTP请求)
数据更新模块 (data_update.py)
    ↓ (数据清洗)
SQLite数据库 (8351条记录)
    ↓ (SQL查询)
FastAPI后端 (数据API)
    ↓ (HTTP API)
Streamlit前端 (数据展示)
    ↓ (用户交互)
用户界面 (17个功能页面)
```

### 数据模型设计

```python
# 核心数据模型
class LotteryRecord:
    period: str      # 期号
    date: str        # 开奖日期
    numbers: str     # 开奖号码 (3位)
    trial_numbers: str # 试机号码 (3位)
    machine_id: str  # 开奖机器号
    sales_amount: float # 销售额
    # ... 其他字段
```

### 数据更新机制

```python
# 自动更新流程
def auto_update_process():
    1. 定时触发 (21:30)
    2. 获取外部数据
    3. 数据格式验证
    4. 增量更新处理
    5. 完整性验证
    6. 缓存刷新
    7. 状态通知
```

## 🎯 预测算法架构

### 智能融合预测系统

```
历史数据 (8351条)
    ↓
特征工程模块
    ↓
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│ 趋势分析模型 │ │ LSTM序列模型│ │ 统计学模型   │
│ (trend)     │ │ (lstm)      │ │ (stats)     │
└─────────────┘ └─────────────┘ └─────────────┘
    ↓               ↓               ↓
        智能融合算法 (Adaptive Weighted)
                    ↓
            预测结果 + 置信度评分
                    ↓
            候选预测列表 (Top-19)
```

### 模型融合策略

```python
class IntelligentFusion:
    def __init__(self):
        self.models = {
            'trend_analysis': TrendModel(),
            'lstm_sequence': LSTMModel(),
            'statistical': StatModel()
        }
    
    def predict(self, data):
        # 1. 各模型独立预测
        predictions = {}
        for name, model in self.models.items():
            predictions[name] = model.predict(data)
        
        # 2. 自适应权重计算
        weights = self.calculate_adaptive_weights(predictions)
        
        # 3. 加权融合
        final_prediction = self.weighted_fusion(predictions, weights)
        
        return final_prediction
```

## 🔧 性能优化架构

### 缓存策略

```python
# 多层缓存架构
L1: 内存缓存 (Streamlit session_state)
    ├── 页面状态缓存
    ├── 用户偏好缓存
    └── 临时数据缓存

L2: 应用缓存 (FastAPI内存)
    ├── 数据查询结果缓存
    ├── 预测结果缓存
    └── 统计分析缓存

L3: 数据库缓存 (SQLite)
    ├── 查询结果缓存
    └── 索引优化
```

### 异步处理架构

```python
# 异步任务处理
async def async_data_processing():
    tasks = [
        fetch_external_data(),      # 外部数据获取
        update_database(),          # 数据库更新
        refresh_cache(),           # 缓存刷新
        generate_predictions()      # 预测生成
    ]
    
    results = await asyncio.gather(*tasks)
    return results
```

## 🛡️ 安全架构设计

### 安全层次

```
1. 网络安全层
   ├── 本地绑定 (127.0.0.1)
   ├── 端口限制 (8501, 8888)
   └── 防火墙配置

2. 应用安全层
   ├── 输入验证
   ├── SQL注入防护
   ├── XSS防护
   └── CSRF防护

3. 数据安全层
   ├── 数据加密存储
   ├── 访问权限控制
   ├── 数据备份机制
   └── 完整性验证
```

### 错误处理安全

```python
# 安全的错误处理
class SecureErrorHandler:
    def handle_error(self, error):
        # 1. 错误信息脱敏
        safe_message = self.sanitize_error(error)
        
        # 2. 详细信息记录到日志
        self.log_detailed_error(error)
        
        # 3. 用户友好提示
        return self.user_friendly_message(safe_message)
```

## 📈 监控架构设计

### 实时监控系统

```
系统监控层
├── 服务状态监控
│   ├── API服务健康检查
│   ├── 数据库连接状态
│   └── 外部数据源状态
├── 性能监控
│   ├── 响应时间监控
│   ├── 内存使用监控
│   └── CPU使用监控
└── 业务监控
    ├── 预测准确率监控
    ├── 用户行为监控
    └── 数据质量监控
```

### 日志架构

```python
# 结构化日志系统
{
    "timestamp": "2025-07-24T00:35:00",
    "level": "INFO|WARN|ERROR",
    "component": "navigation|prediction|data",
    "message": "具体日志信息",
    "context": {
        "user_id": "session_id",
        "page": "current_page",
        "action": "user_action"
    },
    "performance": {
        "response_time": "ms",
        "memory_usage": "MB"
    }
}
```

## 🔄 部署架构

### 本地部署架构

```
开发环境
├── Python 3.11.9 虚拟环境
├── 依赖包管理 (pip)
├── 配置文件管理
└── 日志目录结构

运行时环境
├── Streamlit进程 (8501端口)
├── FastAPI进程 (8888端口)
├── SQLite数据库文件
└── 日志文件系统

监控环境
├── 进程监控
├── 端口监控
├── 文件系统监控
└── 性能指标监控
```

### 扩展架构考虑

```
水平扩展
├── 负载均衡器
├── 多实例部署
├── 数据库集群
└── 缓存集群

垂直扩展
├── 服务器资源升级
├── 数据库性能优化
├── 缓存容量扩展
└── 网络带宽升级
```

## 📋 技术债务和改进点

### 当前技术债务
1. **数据库**: SQLite适合单机，大规模需要PostgreSQL
2. **缓存**: 内存缓存重启丢失，需要Redis
3. **监控**: 基础监控，需要专业APM工具
4. **部署**: 手动部署，需要容器化

### 未来改进方向
1. **微服务化**: 拆分为独立的微服务
2. **容器化**: Docker容器部署
3. **云原生**: Kubernetes编排
4. **大数据**: Spark/Hadoop处理大规模数据

---

**技术架构文档版本**: v1.0  
**创建时间**: 2025年7月24日 00:40  
**架构状态**: ✅ 生产就绪  

🏗️ **技术架构文档完成，为后续开发提供详细指导！**
