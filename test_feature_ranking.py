#!/usr/bin/env python3
"""测试特征重要性排序引擎"""

import sys
import os
sys.path.append('src')

import numpy as np
from model_library.features.feature_ranking import MultiAlgorithmFeatureRanking

def test_feature_ranking():
    """测试特征重要性排序功能"""
    print("🧪 测试多算法特征重要性排序引擎...")
    
    # 创建测试数据
    np.random.seed(42)
    n_samples = 100
    
    features = {
        'sum_feature': np.random.normal(13.5, 3, n_samples),
        'span_feature': np.random.normal(5, 2, n_samples),
        'frequency_feature': np.random.uniform(0, 1, n_samples),
        'trend_feature': np.random.normal(0, 1, n_samples),
        'noise_feature': np.random.normal(0, 1, n_samples)
    }
    
    # 创建目标值（模拟预测准确率）
    targets = (0.3 * features['sum_feature'] + 
              0.2 * features['span_feature'] + 
              0.1 * features['frequency_feature'] + 
              np.random.normal(0, 0.1, n_samples))
    
    # 测试特征重要性计算
    ranker = MultiAlgorithmFeatureRanking()
    
    for model_id in ["markov_enhanced", "deep_learning_cnn_lstm", "trend_analyzer", "intelligent_fusion"]:
        print(f"\n📊 测试模型: {model_id}")
        importance = ranker.calculate_ensemble_feature_importance(model_id, features, targets)
        
        # 按重要性排序
        sorted_features = sorted(importance.items(), key=lambda x: x[1], reverse=True)
        
        for feature_name, score in sorted_features:
            print(f"  {feature_name}: {score:.4f}")
    
    print("\n✅ 特征重要性排序引擎测试完成！")
    return True

if __name__ == "__main__":
    try:
        success = test_feature_ranking()
        if success:
            print("🎉 所有测试通过！")
        else:
            print("❌ 测试失败！")
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()
