#!/usr/bin/env python3
"""
变量作用域修复测试用例
验证intelligentFusionSystem变量作用域错误的修复效果
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock
import traceback

# 添加src路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

class TestVariableScopeFix(unittest.TestCase):
    """变量作用域修复测试类"""
    
    def setUp(self):
        """测试前置设置"""
        self.test_results = []
    
    def test_safe_initialization_functions(self):
        """测试安全初始化函数"""
        print("\n=== 测试安全初始化函数 ===")
        
        try:
            from ui.intelligent_fusion_components import (
                safe_initialize_intelligent_system,
                safe_initialize_adaptive_fusion,
                safe_initialize_database_manager
            )
            
            # 测试智能融合系统初始化
            with patch('streamlit.error') as mock_error:
                system = safe_initialize_intelligent_system()
                # 在测试环境中可能返回None，这是正常的
                print(f"✓ safe_initialize_intelligent_system 执行成功")
            
            # 测试自适应融合系统初始化
            with patch('streamlit.error') as mock_error:
                fusion_system = safe_initialize_adaptive_fusion(100)
                print(f"✓ safe_initialize_adaptive_fusion 执行成功")
            
            # 测试数据库管理器初始化
            with patch('streamlit.error') as mock_error:
                db_manager = safe_initialize_database_manager("test.db")
                print(f"✓ safe_initialize_database_manager 执行成功")
            
            self.test_results.append(("安全初始化函数", True, "所有函数执行成功"))
            
        except Exception as e:
            print(f"✗ 安全初始化函数测试失败: {e}")
            self.test_results.append(("安全初始化函数", False, str(e)))
    
    def test_variable_validation_functions(self):
        """测试变量验证函数"""
        print("\n=== 测试变量验证函数 ===")
        
        try:
            from ui.intelligent_fusion_components import (
                validate_variable_initialization,
                safe_variable_access
            )
            
            # 测试变量初始化验证
            uninitialized = validate_variable_initialization(None, "test", None)
            self.assertEqual(len(uninitialized), 2)
            print(f"✓ validate_variable_initialization 正确识别未初始化变量")
            
            # 测试安全变量访问
            try:
                result = safe_variable_access(None, "test_var")
                self.fail("应该抛出ValueError")
            except ValueError:
                print(f"✓ safe_variable_access 正确处理None值")
            
            # 测试带默认值的安全访问
            result = safe_variable_access(None, "test_var", "default")
            self.assertEqual(result, "default")
            print(f"✓ safe_variable_access 正确返回默认值")
            
            self.test_results.append(("变量验证函数", True, "所有验证函数工作正常"))
            
        except Exception as e:
            print(f"✗ 变量验证函数测试失败: {e}")
            self.test_results.append(("变量验证函数", False, str(e)))
    
    def test_runtime_state_check(self):
        """测试运行时状态检查"""
        print("\n=== 测试运行时状态检查 ===")
        
        try:
            from ui.intelligent_fusion_components import check_runtime_state
            
            state = check_runtime_state()
            
            # 验证状态信息结构
            required_keys = ['intelligent_fusion_available', 'streamlit_available', 
                           'database_accessible', 'modules_loaded']
            
            for key in required_keys:
                self.assertIn(key, state)
            
            print(f"✓ 运行时状态检查返回完整信息")
            print(f"  智能融合可用: {state['intelligent_fusion_available']}")
            print(f"  数据库可访问: {state['database_accessible']}")
            print(f"  模块加载状态: {len(state['modules_loaded'])} 个模块")
            
            self.test_results.append(("运行时状态检查", True, "状态检查功能正常"))
            
        except Exception as e:
            print(f"✗ 运行时状态检查测试失败: {e}")
            self.test_results.append(("运行时状态检查", False, str(e)))
    
    def test_safe_execution_context(self):
        """测试安全执行上下文"""
        print("\n=== 测试安全执行上下文 ===")
        
        try:
            from ui.intelligent_fusion_components import create_safe_execution_context
            
            context = create_safe_execution_context()
            
            # 验证上下文结构
            required_keys = ['fusion_system', 'temp_system', 'db_manager', 
                           'initialized', 'error_count', 'warnings']
            
            for key in required_keys:
                self.assertIn(key, context)
            
            # 验证初始状态
            self.assertFalse(context['initialized'])
            self.assertEqual(context['error_count'], 0)
            self.assertEqual(len(context['warnings']), 0)
            
            print(f"✓ 安全执行上下文创建成功")
            print(f"  包含 {len(context)} 个字段")
            
            self.test_results.append(("安全执行上下文", True, "上下文创建和验证成功"))
            
        except Exception as e:
            print(f"✗ 安全执行上下文测试失败: {e}")
            self.test_results.append(("安全执行上下文", False, str(e)))
    
    def test_error_handling_integration(self):
        """测试错误处理集成"""
        print("\n=== 测试错误处理集成 ===")
        
        try:
            from ui.intelligent_fusion_components import handle_fusion_error
            
            # 模拟不同类型的错误
            test_errors = [
                (ImportError("模块不存在"), "导入错误"),
                (FileNotFoundError("文件不存在"), "文件错误"),
                (ValueError("值错误"), "值错误"),
                (Exception("通用错误"), "通用错误")
            ]
            
            with patch('streamlit.error') as mock_error, \
                 patch('streamlit.info') as mock_info:
                
                for error, operation in test_errors:
                    handle_fusion_error(error, operation)
                    print(f"✓ 正确处理 {operation}")
                
                # 验证调用次数
                self.assertEqual(mock_error.call_count, len(test_errors))
                self.assertEqual(mock_info.call_count, len(test_errors))
            
            self.test_results.append(("错误处理集成", True, "所有错误类型处理正常"))
            
        except Exception as e:
            print(f"✗ 错误处理集成测试失败: {e}")
            self.test_results.append(("错误处理集成", False, str(e)))
    
    def test_ui_component_import(self):
        """测试UI组件导入"""
        print("\n=== 测试UI组件导入 ===")
        
        try:
            # 测试关键函数导入
            from ui.intelligent_fusion_components import (
                show_adaptive_fusion_tab,
                INTELLIGENT_FUSION_AVAILABLE
            )
            
            print(f"✓ UI组件导入成功")
            print(f"  INTELLIGENT_FUSION_AVAILABLE: {INTELLIGENT_FUSION_AVAILABLE}")
            
            # 验证函数可调用性
            self.assertTrue(callable(show_adaptive_fusion_tab))
            
            self.test_results.append(("UI组件导入", True, "所有组件导入成功"))
            
        except Exception as e:
            print(f"✗ UI组件导入测试失败: {e}")
            self.test_results.append(("UI组件导入", False, str(e)))
    
    def test_original_error_prevention(self):
        """测试原始错误预防"""
        print("\n=== 测试原始错误预防 ===")
        
        try:
            # 模拟原始错误场景
            def simulate_original_error():
                # 这是修复前可能出现的错误模式
                if False:  # 条件不满足
                    intelligentFusionSystem = "defined"
                
                # 尝试访问变量（这在修复前会出错）
                try:
                    return intelligentFusionSystem
                except NameError:
                    return "NameError caught"
                except UnboundLocalError:
                    return "UnboundLocalError caught"
            
            result = simulate_original_error()
            self.assertIn("Error caught", result)
            print(f"✓ 原始错误模式被正确捕获: {result}")
            
            # 测试修复后的模式
            from ui.intelligent_fusion_components import create_safe_execution_context
            
            context = create_safe_execution_context()
            # 在修复后的代码中，变量都在context中安全管理
            self.assertIsNotNone(context)
            print(f"✓ 修复后的安全模式工作正常")
            
            self.test_results.append(("原始错误预防", True, "错误预防机制有效"))
            
        except Exception as e:
            print(f"✗ 原始错误预防测试失败: {e}")
            self.test_results.append(("原始错误预防", False, str(e)))
    
    def test_comprehensive_scope_validation(self):
        """综合作用域验证测试"""
        print("\n=== 综合作用域验证测试 ===")
        
        try:
            # 测试所有修复的函数是否存在
            from ui.intelligent_fusion_components import (
                safe_initialize_intelligent_system,
                safe_initialize_adaptive_fusion,
                safe_initialize_database_manager,
                validate_variable_initialization,
                safe_variable_access,
                check_runtime_state,
                create_safe_execution_context,
                handle_fusion_error,
                validate_fusion_prerequisites
            )
            
            functions_tested = [
                'safe_initialize_intelligent_system',
                'safe_initialize_adaptive_fusion', 
                'safe_initialize_database_manager',
                'validate_variable_initialization',
                'safe_variable_access',
                'check_runtime_state',
                'create_safe_execution_context',
                'handle_fusion_error',
                'validate_fusion_prerequisites'
            ]
            
            print(f"✓ 所有 {len(functions_tested)} 个修复函数都可正常导入")
            
            # 验证函数可调用性
            for func_name in functions_tested:
                func = locals()[func_name]
                self.assertTrue(callable(func), f"{func_name} 不可调用")
            
            print(f"✓ 所有修复函数都可正常调用")
            
            self.test_results.append(("综合作用域验证", True, f"{len(functions_tested)}个函数全部验证通过"))
            
        except Exception as e:
            print(f"✗ 综合作用域验证测试失败: {e}")
            self.test_results.append(("综合作用域验证", False, str(e)))

def run_all_tests():
    """运行所有测试"""
    print("🔍 开始变量作用域修复测试...")
    print("="*70)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestVariableScopeFix)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=0, stream=open(os.devnull, 'w'))
    result = runner.run(suite)
    
    # 创建测试实例来获取结果
    test_instance = TestVariableScopeFix()
    test_instance.setUp()
    
    # 手动运行每个测试方法
    test_methods = [
        'test_safe_initialization_functions',
        'test_variable_validation_functions', 
        'test_runtime_state_check',
        'test_safe_execution_context',
        'test_error_handling_integration',
        'test_ui_component_import',
        'test_original_error_prevention',
        'test_comprehensive_scope_validation'
    ]
    
    for method_name in test_methods:
        try:
            method = getattr(test_instance, method_name)
            method()
        except Exception as e:
            print(f"测试方法 {method_name} 执行失败: {e}")
    
    # 汇总结果
    print("\n" + "="*70)
    print("🎯 变量作用域修复测试结果:")
    print("="*70)
    
    passed = 0
    total = len(test_instance.test_results)
    
    for test_name, success, details in test_instance.test_results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
        if not success:
            print(f"  详情: {details}")
        if success:
            passed += 1
    
    print(f"\n📊 测试统计: {passed}/{total} 项通过")
    
    if passed == total:
        print("\n🎉 所有变量作用域修复测试通过！")
        print("✅ 修复效果验证成功")
        print("✅ 防护机制工作正常")
        print("✅ 错误处理完善")
        return True
    else:
        print(f"\n⚠️ 部分测试失败 ({total-passed}项)")
        print("❌ 需要进一步检查修复效果")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
