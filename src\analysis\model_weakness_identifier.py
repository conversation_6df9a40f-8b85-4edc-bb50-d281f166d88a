#!/usr/bin/env python3
"""
模型弱点识别器
Model Weakness Identifier

智能识别模型的具体弱点和问题
"""

import logging
import os
import statistics
import sys
from collections import Counter, defaultdict
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple

import numpy as np

sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from core.unified_prediction_storage import PredictionRecord


@dataclass
class WeaknessIdentificationResult:
    """弱点识别结果"""
    model_name: str
    identified_weaknesses: Dict[str, Any]
    severity_assessment: Dict[str, str]
    improvement_suggestions: Dict[str, List[str]]
    confidence_score: float
    analysis_timestamp: datetime


class ModelWeaknessIdentifier:
    """模型弱点识别器"""
    
    def __init__(self):
        """初始化模型弱点识别器"""
        self.logger = logging.getLogger(__name__)
        
        # 弱点检测器配置
        self.weakness_detectors = {
            'overfitting': self._detect_overfitting,
            'bias': self._detect_prediction_bias,
            'variance': self._detect_high_variance,
            'temporal_drift': self._detect_temporal_drift,
            'confidence_miscalibration': self._detect_confidence_issues,
            'pattern_blindness': self._detect_pattern_blindness
        }
        
        # 严重程度阈值
        self.severity_thresholds = {
            'overfitting': {'low': 0.1, 'medium': 0.3, 'high': 0.5},
            'bias': {'low': 0.15, 'medium': 0.3, 'high': 0.5},
            'variance': {'low': 0.2, 'medium': 0.4, 'high': 0.6},
            'temporal_drift': {'low': 0.1, 'medium': 0.25, 'high': 0.4},
            'confidence_miscalibration': {'low': 0.1, 'medium': 0.2, 'high': 0.3},
            'pattern_blindness': {'low': 0.2, 'medium': 0.4, 'high': 0.6}
        }
    
    def identify_weaknesses(self, model_name: str, prediction_history: List[PredictionRecord]) -> WeaknessIdentificationResult:
        """
        识别模型弱点
        
        Args:
            model_name: 模型名称
            prediction_history: 预测历史记录
            
        Returns:
            弱点识别结果
        """
        try:
            self.logger.info(f"开始识别模型弱点: {model_name}")
            
            if not prediction_history:
                self.logger.warning(f"模型 {model_name} 没有历史预测记录")
                return WeaknessIdentificationResult(
                    model_name=model_name,
                    identified_weaknesses={},
                    severity_assessment={},
                    improvement_suggestions={},
                    confidence_score=0.0,
                    analysis_timestamp=datetime.now()
                )
            
            # 执行各种弱点检测
            identified_weaknesses = {}
            severity_assessment = {}
            improvement_suggestions = {}
            
            for weakness_type, detector_func in self.weakness_detectors.items():
                try:
                    weakness_result = detector_func(prediction_history)
                    
                    if weakness_result['has_weakness']:
                        identified_weaknesses[weakness_type] = weakness_result
                        severity_assessment[weakness_type] = self._assess_severity(
                            weakness_type, weakness_result['severity_score']
                        )
                        improvement_suggestions[weakness_type] = self._generate_improvement_suggestions(
                            weakness_type, weakness_result
                        )
                        
                except Exception as e:
                    self.logger.error(f"弱点检测失败 {weakness_type}: {e}")
            
            # 计算整体置信度
            confidence_score = self._calculate_identification_confidence(
                identified_weaknesses, prediction_history
            )
            
            return WeaknessIdentificationResult(
                model_name=model_name,
                identified_weaknesses=identified_weaknesses,
                severity_assessment=severity_assessment,
                improvement_suggestions=improvement_suggestions,
                confidence_score=confidence_score,
                analysis_timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"模型弱点识别失败: {e}")
            raise
    
    def _detect_overfitting(self, prediction_history: List[PredictionRecord]) -> Dict[str, Any]:
        """检测过拟合"""
        result = {
            'has_weakness': False,
            'severity_score': 0.0,
            'indicators': [],
            'details': {}
        }
        
        # 分析准确率变化趋势
        verified_predictions = [p for p in prediction_history if p.is_verified and p.accuracy_score is not None]
        
        if len(verified_predictions) < 10:
            return result
        
        # 按时间排序
        verified_predictions.sort(key=lambda x: x.prediction_time or datetime.min)
        
        # 计算滑动窗口准确率
        window_size = min(10, len(verified_predictions) // 3)
        early_accuracy = np.mean([p.accuracy_score for p in verified_predictions[:window_size]])
        recent_accuracy = np.mean([p.accuracy_score for p in verified_predictions[-window_size:]])
        
        # 检测准确率下降
        accuracy_drop = early_accuracy - recent_accuracy
        
        if accuracy_drop > 0.15:  # 准确率下降超过15%
            result['has_weakness'] = True
            result['severity_score'] = min(accuracy_drop / 0.5, 1.0)
            result['indicators'].append('accuracy_degradation')
            result['details']['accuracy_drop'] = accuracy_drop
            result['details']['early_accuracy'] = early_accuracy
            result['details']['recent_accuracy'] = recent_accuracy
        
        # 检测置信度与准确率的背离
        confidence_accuracy_correlation = self._calculate_confidence_accuracy_correlation(verified_predictions)
        
        if confidence_accuracy_correlation < 0.3:  # 相关性过低
            result['has_weakness'] = True
            result['severity_score'] = max(result['severity_score'], 1 - confidence_accuracy_correlation)
            result['indicators'].append('confidence_accuracy_divergence')
            result['details']['correlation'] = confidence_accuracy_correlation
        
        return result
    
    def _detect_prediction_bias(self, prediction_history: List[PredictionRecord]) -> Dict[str, Any]:
        """检测预测偏差"""
        result = {
            'has_weakness': False,
            'severity_score': 0.0,
            'indicators': [],
            'details': {}
        }
        
        verified_predictions = [p for p in prediction_history if p.is_verified and p.actual_numbers]
        
        if len(verified_predictions) < 5:
            return result
        
        # 分析位置偏差
        position_biases = []
        for pos in range(3):  # 百位、十位、个位
            predicted_digits = []
            actual_digits = []
            
            for p in verified_predictions:
                if len(p.predicted_numbers) > pos and len(p.actual_numbers) > pos:
                    predicted_digits.append(int(p.predicted_numbers[pos]))
                    actual_digits.append(int(p.actual_numbers[pos]))
            
            if predicted_digits and actual_digits:
                pred_mean = np.mean(predicted_digits)
                actual_mean = np.mean(actual_digits)
                bias = abs(pred_mean - actual_mean)
                position_biases.append(bias)
        
        # 检测系统性偏差
        avg_bias = np.mean(position_biases) if position_biases else 0
        
        if avg_bias > 1.5:  # 平均偏差超过1.5
            result['has_weakness'] = True
            result['severity_score'] = min(avg_bias / 3.0, 1.0)
            result['indicators'].append('systematic_bias')
            result['details']['position_biases'] = position_biases
            result['details']['average_bias'] = avg_bias
        
        # 检测数字偏好
        all_predicted_digits = []
        for p in verified_predictions:
            all_predicted_digits.extend([int(d) for d in p.predicted_numbers])
        
        digit_distribution = Counter(all_predicted_digits)
        expected_frequency = len(all_predicted_digits) / 10
        
        # 计算卡方统计量
        chi_square = sum((count - expected_frequency) ** 2 / expected_frequency 
                        for count in digit_distribution.values())
        
        if chi_square > 16.92:  # 95%置信度的临界值
            result['has_weakness'] = True
            result['severity_score'] = max(result['severity_score'], min(chi_square / 30, 1.0))
            result['indicators'].append('digit_preference_bias')
            result['details']['digit_distribution'] = dict(digit_distribution)
            result['details']['chi_square'] = chi_square
        
        return result
    
    def _detect_high_variance(self, prediction_history: List[PredictionRecord]) -> Dict[str, Any]:
        """检测高方差"""
        result = {
            'has_weakness': False,
            'severity_score': 0.0,
            'indicators': [],
            'details': {}
        }
        
        if len(prediction_history) < 10:
            return result
        
        # 分析置信度方差
        confidences = [p.confidence for p in prediction_history]
        confidence_variance = np.var(confidences)
        
        if confidence_variance > 0.15:  # 置信度方差过高
            result['has_weakness'] = True
            result['severity_score'] = min(confidence_variance / 0.3, 1.0)
            result['indicators'].append('high_confidence_variance')
            result['details']['confidence_variance'] = confidence_variance
        
        # 分析预测结果的一致性
        predicted_numbers = [p.predicted_numbers for p in prediction_history]
        unique_predictions = len(set(predicted_numbers))
        prediction_diversity = unique_predictions / len(predicted_numbers)
        
        if prediction_diversity > 0.8:  # 预测过于分散
            result['has_weakness'] = True
            result['severity_score'] = max(result['severity_score'], prediction_diversity)
            result['indicators'].append('high_prediction_diversity')
            result['details']['prediction_diversity'] = prediction_diversity
        
        return result
    
    def _detect_temporal_drift(self, prediction_history: List[PredictionRecord]) -> Dict[str, Any]:
        """检测时间漂移"""
        result = {
            'has_weakness': False,
            'severity_score': 0.0,
            'indicators': [],
            'details': {}
        }
        
        verified_predictions = [p for p in prediction_history if p.is_verified and p.accuracy_score is not None]
        
        if len(verified_predictions) < 15:
            return result
        
        # 按时间排序
        verified_predictions.sort(key=lambda x: x.prediction_time or datetime.min)
        
        # 分析准确率趋势
        window_size = len(verified_predictions) // 3
        early_period = verified_predictions[:window_size]
        middle_period = verified_predictions[window_size:2*window_size]
        recent_period = verified_predictions[-window_size:]
        
        early_acc = np.mean([p.accuracy_score for p in early_period])
        middle_acc = np.mean([p.accuracy_score for p in middle_period])
        recent_acc = np.mean([p.accuracy_score for p in recent_period])
        
        # 检测单调下降趋势
        if early_acc > middle_acc > recent_acc:
            drift_magnitude = early_acc - recent_acc
            if drift_magnitude > 0.1:
                result['has_weakness'] = True
                result['severity_score'] = min(drift_magnitude / 0.4, 1.0)
                result['indicators'].append('monotonic_degradation')
                result['details']['drift_magnitude'] = drift_magnitude
        
        # 检测波动性增加
        early_std = np.std([p.accuracy_score for p in early_period])
        recent_std = np.std([p.accuracy_score for p in recent_period])
        
        if recent_std > early_std * 1.5:
            result['has_weakness'] = True
            result['severity_score'] = max(result['severity_score'], min(recent_std / early_std / 3, 1.0))
            result['indicators'].append('increased_volatility')
            result['details']['volatility_increase'] = recent_std / early_std
        
        return result
    
    def _detect_confidence_issues(self, prediction_history: List[PredictionRecord]) -> Dict[str, Any]:
        """检测置信度问题"""
        result = {
            'has_weakness': False,
            'severity_score': 0.0,
            'indicators': [],
            'details': {}
        }
        
        verified_predictions = [p for p in prediction_history if p.is_verified and p.accuracy_score is not None]
        
        if len(verified_predictions) < 5:
            return result
        
        # 计算校准误差
        calibration_errors = []
        for p in verified_predictions:
            error = abs(p.confidence - p.accuracy_score)
            calibration_errors.append(error)
        
        avg_calibration_error = np.mean(calibration_errors)
        
        if avg_calibration_error > 0.2:
            result['has_weakness'] = True
            result['severity_score'] = min(avg_calibration_error / 0.5, 1.0)
            result['indicators'].append('poor_calibration')
            result['details']['avg_calibration_error'] = avg_calibration_error
        
        # 检测过度自信
        overconfident_count = sum(1 for p in verified_predictions 
                                 if p.confidence > p.accuracy_score + 0.2)
        overconfidence_rate = overconfident_count / len(verified_predictions)
        
        if overconfidence_rate > 0.3:
            result['has_weakness'] = True
            result['severity_score'] = max(result['severity_score'], overconfidence_rate)
            result['indicators'].append('overconfidence')
            result['details']['overconfidence_rate'] = overconfidence_rate
        
        return result
    
    def _detect_pattern_blindness(self, prediction_history: List[PredictionRecord]) -> Dict[str, Any]:
        """检测模式盲点"""
        result = {
            'has_weakness': False,
            'severity_score': 0.0,
            'indicators': [],
            'details': {}
        }
        
        verified_predictions = [p for p in prediction_history if p.is_verified and p.actual_numbers]
        
        if len(verified_predictions) < 10:
            return result
        
        # 分析对特定模式的识别能力
        pattern_performance = {
            'consecutive': [],
            'repeated': [],
            'ascending': [],
            'descending': []
        }
        
        for p in verified_predictions:
            actual = p.actual_numbers
            predicted = p.predicted_numbers
            
            # 检测连续数字模式
            if self._has_consecutive_pattern(actual):
                pattern_performance['consecutive'].append(p.accuracy_score)
            
            # 检测重复数字模式
            if self._has_repeated_pattern(actual):
                pattern_performance['repeated'].append(p.accuracy_score)
            
            # 检测递增模式
            if self._has_ascending_pattern(actual):
                pattern_performance['ascending'].append(p.accuracy_score)
            
            # 检测递减模式
            if self._has_descending_pattern(actual):
                pattern_performance['descending'].append(p.accuracy_score)
        
        # 检测模式识别弱点
        overall_accuracy = np.mean([p.accuracy_score for p in verified_predictions])
        
        for pattern_type, scores in pattern_performance.items():
            if len(scores) >= 3:  # 至少有3个样本
                pattern_accuracy = np.mean(scores)
                if pattern_accuracy < overall_accuracy - 0.15:  # 特定模式表现差
                    result['has_weakness'] = True
                    performance_gap = overall_accuracy - pattern_accuracy
                    result['severity_score'] = max(result['severity_score'], 
                                                 min(performance_gap / 0.3, 1.0))
                    result['indicators'].append(f'{pattern_type}_pattern_blindness')
                    result['details'][f'{pattern_type}_performance'] = pattern_accuracy
        
        return result
    
    def _assess_severity(self, weakness_type: str, severity_score: float) -> str:
        """评估弱点严重程度"""
        thresholds = self.severity_thresholds.get(weakness_type, {'low': 0.3, 'medium': 0.6, 'high': 0.8})
        
        if severity_score <= thresholds['low']:
            return 'low'
        elif severity_score <= thresholds['medium']:
            return 'medium'
        else:
            return 'high'
    
    def _generate_improvement_suggestions(self, weakness_type: str, weakness_result: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        if weakness_type == 'overfitting':
            suggestions.extend([
                '增加正则化强度（L1/L2正则化）',
                '减少模型复杂度（减少参数数量）',
                '增加训练数据量',
                '使用Dropout技术防止过拟合',
                '实施早停策略',
                '使用交叉验证评估模型性能'
            ])
        
        elif weakness_type == 'bias':
            suggestions.extend([
                '重新平衡训练数据分布',
                '调整损失函数权重',
                '引入对抗训练技术',
                '使用数据增强技术',
                '检查特征工程中的偏差',
                '实施偏差校正算法'
            ])
        
        elif weakness_type == 'variance':
            suggestions.extend([
                '使用集成学习方法',
                '增加训练数据量',
                '简化模型结构',
                '使用Bagging技术',
                '实施模型平均策略',
                '调整学习率和优化器参数'
            ])
        
        elif weakness_type == 'temporal_drift':
            suggestions.extend([
                '实施在线学习机制',
                '使用滑动窗口训练',
                '建立概念漂移检测系统',
                '定期重新训练模型',
                '使用自适应学习率',
                '实施模型版本管理'
            ])
        
        elif weakness_type == 'confidence_miscalibration':
            suggestions.extend([
                '使用Platt缩放校准置信度',
                '实施温度缩放技术',
                '使用保序回归校准',
                '调整决策阈值',
                '实施不确定性量化',
                '使用贝叶斯方法'
            ])
        
        elif weakness_type == 'pattern_blindness':
            suggestions.extend([
                '增强特征工程以捕获模式',
                '使用专门的模式识别模块',
                '实施注意力机制',
                '增加模式相关的训练数据',
                '使用序列建模技术',
                '实施多尺度特征提取'
            ])
        
        return suggestions
    
    def _calculate_identification_confidence(self, identified_weaknesses: Dict[str, Any], 
                                           prediction_history: List[PredictionRecord]) -> float:
        """计算识别置信度"""
        # 基于样本数量和弱点一致性计算置信度
        sample_size = len(prediction_history)
        
        # 样本数量置信度
        size_confidence = min(sample_size / 50, 1.0)  # 50个样本达到满分
        
        # 弱点一致性置信度
        if not identified_weaknesses:
            consistency_confidence = 1.0  # 没有发现弱点，置信度高
        else:
            # 基于弱点严重程度的一致性
            severity_scores = [w['severity_score'] for w in identified_weaknesses.values()]
            consistency_confidence = 1.0 - np.std(severity_scores)  # 严重程度越一致，置信度越高
        
        # 综合置信度
        overall_confidence = (size_confidence * 0.6 + consistency_confidence * 0.4)
        return max(0.0, min(1.0, overall_confidence))
    
    def _calculate_confidence_accuracy_correlation(self, predictions: List[PredictionRecord]) -> float:
        """计算置信度与准确率的相关性"""
        if len(predictions) < 3:
            return 0.0
        
        confidences = [p.confidence for p in predictions]
        accuracies = [p.accuracy_score for p in predictions]
        
        return np.corrcoef(confidences, accuracies)[0, 1] if len(set(confidences)) > 1 else 0.0
    
    def _has_consecutive_pattern(self, numbers: str) -> bool:
        """检测连续数字模式"""
        digits = [int(d) for d in numbers]
        for i in range(len(digits) - 1):
            if abs(digits[i+1] - digits[i]) == 1:
                return True
        return False
    
    def _has_repeated_pattern(self, numbers: str) -> bool:
        """检测重复数字模式"""
        return len(set(numbers)) < len(numbers)
    
    def _has_ascending_pattern(self, numbers: str) -> bool:
        """检测递增模式"""
        digits = [int(d) for d in numbers]
        return all(digits[i] <= digits[i+1] for i in range(len(digits)-1))
    
    def _has_descending_pattern(self, numbers: str) -> bool:
        """检测递减模式"""
        digits = [int(d) for d in numbers]
        return all(digits[i] >= digits[i+1] for i in range(len(digits)-1))


if __name__ == "__main__":
    # 测试代码
    from datetime import datetime, timedelta

    from ..core.unified_prediction_storage import PredictionRecord

    # 创建测试预测历史
    test_history = []
    for i in range(20):
        record = PredictionRecord(
            period_number=f"202519{i:02d}",
            model_name="test_model",
            predicted_numbers=f"{i%10}{(i+1)%10}{(i+2)%10}",
            confidence=0.5 + (i % 5) * 0.1,
            prediction_time=datetime.now() - timedelta(days=20-i),
            actual_numbers=f"{(i+1)%10}{(i+2)%10}{(i+3)%10}",
            is_verified=True,
            accuracy_score=0.3 + (i % 3) * 0.2
        )
        test_history.append(record)
    
    # 创建弱点识别器
    identifier = ModelWeaknessIdentifier()
    
    # 执行弱点识别
    result = identifier.identify_weaknesses("test_model", test_history)
    
    print("弱点识别结果:")
    print(f"识别的弱点: {list(result.identified_weaknesses.keys())}")
    print(f"严重程度评估: {result.severity_assessment}")
    print(f"置信度: {result.confidence_score:.3f}")
    
    for weakness_type, suggestions in result.improvement_suggestions.items():
        print(f"\n{weakness_type} 改进建议:")
        for suggestion in suggestions[:3]:  # 显示前3个建议
            print(f"  - {suggestion}")
