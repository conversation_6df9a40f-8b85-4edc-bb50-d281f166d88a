"""
错误处理测试页面
用于测试和演示各种错误处理功能
"""

import streamlit as st
import requests
import time
from typing import Optional

# 导入错误处理组件
try:
    from ..components.error_handler import ErrorHandler, ErrorRecovery, SmartErrorHandler
    from ..components.error_middleware import handle_page_errors, handle_api_errors, handle_data_errors
    from ..components.error_config import ErrorType, ErrorSeverity
except ImportError:
    st.error("无法导入错误处理组件")


@handle_page_errors("错误处理测试")
def show_error_handling_test():
    """错误处理测试页面"""
    st.header("🧪 错误处理测试")
    st.info("此页面用于测试和演示各种错误处理功能")
    
    # 创建测试选项卡
    tab1, tab2, tab3, tab4 = st.tabs([
        "🎨 错误样式测试", 
        "🌐 网络错误测试", 
        "📊 数据错误测试", 
        "🔧 恢复功能测试"
    ])
    
    with tab1:
        _show_error_style_tests()
    
    with tab2:
        _show_network_error_tests()
    
    with tab3:
        _show_data_error_tests()
    
    with tab4:
        _show_recovery_tests()


def _show_error_style_tests():
    """显示错误样式测试"""
    st.subheader("🎨 错误样式测试")
    st.write("测试不同类型和严重程度的错误显示样式")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**错误类型测试:**")
        
        if st.button("显示普通错误"):
            ErrorHandler.show_error(
                title="普通错误示例",
                message="这是一个普通的错误消息示例",
                error_type="error",
                suggestions=["建议1", "建议2", "建议3"]
            )
        
        if st.button("显示警告信息"):
            ErrorHandler.show_warning(
                message="这是一个警告信息示例",
                suggestions=["检查配置", "重试操作"]
            )
        
        if st.button("显示信息提示"):
            ErrorHandler.show_info(
                message="这是一个信息提示示例",
                details="包含一些详细信息"
            )
    
    with col2:
        st.write("**专用错误测试:**")
        
        if st.button("显示API错误"):
            ErrorHandler.show_api_error(
                endpoint="/api/test",
                status_code=500,
                message="服务器内部错误"
            )
        
        if st.button("显示数据错误"):
            ErrorHandler.show_data_error(
                operation="数据加载",
                details="数据格式不正确"
            )
        
        if st.button("显示模型错误"):
            ErrorHandler.show_model_error(
                model_name="test_model",
                error_msg="模型文件损坏"
            )


def _show_network_error_tests():
    """显示网络错误测试"""
    st.subheader("🌐 网络错误测试")
    st.write("测试各种网络连接和API调用错误")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**连接错误测试:**")
        
        if st.button("测试连接超时"):
            _test_connection_timeout()
        
        if st.button("测试连接拒绝"):
            _test_connection_refused()
        
        if st.button("测试无效URL"):
            _test_invalid_url()
    
    with col2:
        st.write("**HTTP错误测试:**")
        
        if st.button("测试404错误"):
            _test_http_404()
        
        if st.button("测试500错误"):
            _test_http_500()
        
        if st.button("测试认证错误"):
            _test_http_401()


@handle_api_errors("http://invalid-url.test", retry_on_failure=True)
def _test_connection_timeout():
    """测试连接超时"""
    response = requests.get("http://httpbin.org/delay/10", timeout=2)
    return response.json()


@handle_api_errors("http://localhost:9999", retry_on_failure=True)
def _test_connection_refused():
    """测试连接拒绝"""
    response = requests.get("http://localhost:9999/test", timeout=5)
    return response.json()


@handle_api_errors("http://invalid-domain-name-12345.com", retry_on_failure=False)
def _test_invalid_url():
    """测试无效URL"""
    response = requests.get("http://invalid-domain-name-12345.com/test", timeout=5)
    return response.json()


@handle_api_errors("http://httpbin.org/status/404", retry_on_failure=False)
def _test_http_404():
    """测试404错误"""
    response = requests.get("http://httpbin.org/status/404")
    response.raise_for_status()
    return response.json()


@handle_api_errors("http://httpbin.org/status/500", retry_on_failure=False)
def _test_http_500():
    """测试500错误"""
    response = requests.get("http://httpbin.org/status/500")
    response.raise_for_status()
    return response.json()


@handle_api_errors("http://httpbin.org/status/401", retry_on_failure=False)
def _test_http_401():
    """测试401错误"""
    response = requests.get("http://httpbin.org/status/401")
    response.raise_for_status()
    return response.json()


def _show_data_error_tests():
    """显示数据错误测试"""
    st.subheader("📊 数据错误测试")
    st.write("测试各种数据处理错误")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**文件操作错误:**")
        
        if st.button("测试文件不存在"):
            _test_file_not_found()
        
        if st.button("测试权限错误"):
            _test_permission_error()
        
        if st.button("测试数据格式错误"):
            _test_data_format_error()
    
    with col2:
        st.write("**数据验证错误:**")
        
        if st.button("测试数据类型错误"):
            _test_data_type_error()
        
        if st.button("测试数据范围错误"):
            _test_data_range_error()
        
        if st.button("测试数据完整性错误"):
            _test_data_integrity_error()


@handle_data_errors("文件读取")
def _test_file_not_found():
    """测试文件不存在错误"""
    with open("non_existent_file.txt", "r") as f:
        return f.read()


@handle_data_errors("文件写入")
def _test_permission_error():
    """测试权限错误"""
    with open("/root/test.txt", "w") as f:
        f.write("test")


@handle_data_errors("数据解析")
def _test_data_format_error():
    """测试数据格式错误"""
    import json
    return json.loads("invalid json data")


@handle_data_errors("数据类型验证")
def _test_data_type_error():
    """测试数据类型错误"""
    value = "not a number"
    return int(value)


@handle_data_errors("数据范围验证")
def _test_data_range_error():
    """测试数据范围错误"""
    value = 150
    if value > 100:
        raise ValueError(f"数值超出允许范围: {value} > 100")
    return value


@handle_data_errors("数据完整性检查")
def _test_data_integrity_error():
    """测试数据完整性错误"""
    data = {"name": "test"}
    required_fields = ["name", "age", "email"]
    
    missing_fields = [field for field in required_fields if field not in data]
    if missing_fields:
        raise ValueError(f"缺少必需字段: {missing_fields}")
    
    return data


def _show_recovery_tests():
    """显示恢复功能测试"""
    st.subheader("🔧 恢复功能测试")
    st.write("测试各种错误恢复机制")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**网络恢复测试:**")
        
        if st.button("显示网络恢复选项"):
            ErrorRecovery.show_network_error_recovery()
        
        if st.button("显示数据恢复选项"):
            ErrorRecovery.show_data_error_recovery("测试操作")
        
        if st.button("显示模型恢复选项"):
            ErrorRecovery.show_model_error_recovery("test_model")
    
    with col2:
        st.write("**智能错误处理测试:**")
        
        if st.button("测试智能API调用"):
            result = SmartErrorHandler.safe_api_call(
                "http://127.0.0.1:8888/health",
                timeout=5
            )
            if result:
                st.success("API调用成功")
                st.json(result)
        
        if st.button("测试智能错误处理装饰器"):
            _test_smart_error_handler()


@SmartErrorHandler.handle_exception
def _test_smart_error_handler():
    """测试智能错误处理装饰器"""
    # 故意引发一个错误来测试装饰器
    raise ConnectionError("这是一个测试连接错误")


if __name__ == "__main__":
    show_error_handling_test()
