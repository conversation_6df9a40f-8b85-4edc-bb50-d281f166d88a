#!/usr/bin/env python3
"""
Polars高性能数据处理引擎

提供基于Polars的高性能数据查询、聚合和分析功能
"""

import polars as pl
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import logging
from datetime import datetime, date
import sys
sys.path.append('src')

from data.models import LotteryRecord

logger = logging.getLogger(__name__)

class PolarsEngine:
    """Polars高性能数据处理引擎"""
    
    def __init__(self):
        """初始化Polars引擎"""
        self.df: Optional[pl.DataFrame] = None
        self.logger = logger
        
    def load_from_records(self, records: List[LotteryRecord]) -> None:
        """
        从LotteryRecord列表加载数据到Polars DataFrame
        
        Args:
            records: 彩票记录列表
        """
        if not records:
            self.logger.warning("记录列表为空")
            return
            
        # 转换为字典列表
        data_list = []
        for record in records:
            data_dict = record.to_dict()
            # 转换日期为字符串（Polars兼容）
            data_dict['date'] = data_dict['date']
            data_list.append(data_dict)
        
        # 创建Polars DataFrame
        self.df = pl.DataFrame(data_list)
        
        # 优化数据类型
        self.df = self.df.with_columns([
            pl.col("period").cast(pl.Utf8),
            pl.col("date").str.strptime(pl.Date, "%Y-%m-%d"),
            pl.col("numbers").cast(pl.Utf8),
            pl.col("trial_numbers").cast(pl.Utf8),
            pl.col("draw_machine").cast(pl.Int32),
            pl.col("trial_machine").cast(pl.Int32),
            pl.col("sales_amount").cast(pl.Int64),
            pl.col("direct_prize").cast(pl.Int32),
            pl.col("group3_prize").cast(pl.Int32),
            pl.col("group6_prize").cast(pl.Int32),
            pl.col("sum_value").cast(pl.Int32),
            pl.col("trial_sum_value").cast(pl.Int32),
            pl.col("span_value").cast(pl.Int32),
            pl.col("trial_span_value").cast(pl.Int32)
        ])
        
        self.logger.info(f"成功加载 {len(records)} 条记录到Polars引擎")
        
    def load_from_csv(self, csv_path: str) -> None:
        """
        从CSV文件加载数据
        
        Args:
            csv_path: CSV文件路径
        """
        try:
            self.df = pl.read_csv(csv_path)
            # 转换日期列
            if "date" in self.df.columns:
                self.df = self.df.with_columns(
                    pl.col("date").str.strptime(pl.Date, "%Y-%m-%d")
                )
            self.logger.info(f"从CSV文件加载数据成功: {csv_path}")
        except Exception as e:
            self.logger.error(f"从CSV加载数据失败: {e}")
            raise
            
    def get_basic_stats(self) -> Dict[str, Any]:
        """
        获取基础统计信息
        
        Returns:
            基础统计信息字典
        """
        if self.df is None:
            return {}
            
        stats = {
            "total_records": len(self.df),
            "date_range": {
                "start": str(self.df["date"].min()),
                "end": str(self.df["date"].max())
            },
            "sum_value_stats": {
                "min": self.df["sum_value"].min(),
                "max": self.df["sum_value"].max(),
                "mean": round(self.df["sum_value"].mean(), 2),
                "median": self.df["sum_value"].median()
            },
            "span_value_stats": {
                "min": self.df["span_value"].min(),
                "max": self.df["span_value"].max(),
                "mean": round(self.df["span_value"].mean(), 2)
            },
            "sales_amount_stats": {
                "min": self.df["sales_amount"].min(),
                "max": self.df["sales_amount"].max(),
                "mean": round(self.df["sales_amount"].mean(), 2),
                "total": self.df["sales_amount"].sum()
            }
        }
        
        return stats
        
    def get_frequency_analysis(self, digit_position: str = "all") -> Dict[str, Any]:
        """
        获取号码频率分析
        
        Args:
            digit_position: 分析位置 ("all", "hundreds", "tens", "units")
            
        Returns:
            频率分析结果
        """
        if self.df is None:
            return {}
            
        result = {}
        
        if digit_position in ["all", "hundreds"]:
            # 百位数字频率
            hundreds_freq = (
                self.df.with_columns(
                    pl.col("number_list").list.get(0).alias("hundreds")
                )
                .group_by("hundreds")
                .agg(pl.count().alias("count"))
                .sort("count", descending=True)
            )
            result["hundreds"] = hundreds_freq.to_dicts()
            
        if digit_position in ["all", "tens"]:
            # 十位数字频率
            tens_freq = (
                self.df.with_columns(
                    pl.col("number_list").list.get(1).alias("tens")
                )
                .group_by("tens")
                .agg(pl.count().alias("count"))
                .sort("count", descending=True)
            )
            result["tens"] = tens_freq.to_dicts()
            
        if digit_position in ["all", "units"]:
            # 个位数字频率
            units_freq = (
                self.df.with_columns(
                    pl.col("number_list").list.get(2).alias("units")
                )
                .group_by("units")
                .agg(pl.count().alias("count"))
                .sort("count", descending=True)
            )
            result["units"] = units_freq.to_dicts()
            
        return result
        
    def get_sum_value_distribution(self) -> Dict[str, Any]:
        """
        获取和值分布分析
        
        Returns:
            和值分布统计
        """
        if self.df is None:
            return {}
            
        # 和值分布
        sum_dist = (
            self.df.group_by("sum_value")
            .agg(pl.count().alias("count"))
            .sort("sum_value")
        )
        
        # 试机号和值分布
        trial_sum_dist = (
            self.df.group_by("trial_sum_value")
            .agg(pl.count().alias("count"))
            .sort("trial_sum_value")
        )
        
        return {
            "sum_value_distribution": sum_dist.to_dicts(),
            "trial_sum_distribution": trial_sum_dist.to_dicts(),
            "correlation": self.df.select(
                pl.corr("sum_value", "trial_sum_value").alias("correlation")
            ).item()
        }
        
    def get_span_value_analysis(self) -> Dict[str, Any]:
        """
        获取跨度值分析
        
        Returns:
            跨度值分析结果
        """
        if self.df is None:
            return {}
            
        span_dist = (
            self.df.group_by("span_value")
            .agg(pl.count().alias("count"))
            .sort("span_value")
        )
        
        trial_span_dist = (
            self.df.group_by("trial_span_value")
            .agg(pl.count().alias("count"))
            .sort("trial_span_value")
        )
        
        return {
            "span_distribution": span_dist.to_dicts(),
            "trial_span_distribution": trial_span_dist.to_dicts(),
            "span_correlation": self.df.select(
                pl.corr("span_value", "trial_span_value").alias("correlation")
            ).item()
        }
        
    def get_machine_analysis(self) -> Dict[str, Any]:
        """
        获取机器号分析
        
        Returns:
            机器号使用统计
        """
        if self.df is None:
            return {}
            
        draw_machine_stats = (
            self.df.group_by("draw_machine")
            .agg([
                pl.count().alias("count"),
                pl.col("sum_value").mean().alias("avg_sum"),
                pl.col("span_value").mean().alias("avg_span")
            ])
            .sort("count", descending=True)
        )
        
        trial_machine_stats = (
            self.df.group_by("trial_machine")
            .agg([
                pl.count().alias("count"),
                pl.col("trial_sum_value").mean().alias("avg_sum"),
                pl.col("trial_span_value").mean().alias("avg_span")
            ])
            .sort("count", descending=True)
        )
        
        return {
            "draw_machine_stats": draw_machine_stats.to_dicts(),
            "trial_machine_stats": trial_machine_stats.to_dicts()
        }
        
    def get_sales_analysis(self) -> Dict[str, Any]:
        """
        获取销售额分析
        
        Returns:
            销售额分析结果
        """
        if self.df is None:
            return {}
            
        # 按年份统计销售额
        yearly_sales = (
            self.df.with_columns(
                pl.col("date").dt.year().alias("year")
            )
            .group_by("year")
            .agg([
                pl.col("sales_amount").sum().alias("total_sales"),
                pl.col("sales_amount").mean().alias("avg_sales"),
                pl.count().alias("draw_count")
            ])
            .sort("year")
        )
        
        # 销售额与和值的关系
        sales_sum_corr = self.df.select(
            pl.corr("sales_amount", "sum_value").alias("correlation")
        ).item()
        
        return {
            "yearly_sales": yearly_sales.to_dicts(),
            "sales_sum_correlation": sales_sum_corr,
            "total_sales": self.df["sales_amount"].sum(),
            "avg_daily_sales": round(self.df["sales_amount"].mean(), 2)
        }
        
    def query_by_date_range(self, start_date: str, end_date: str) -> pl.DataFrame:
        """
        按日期范围查询数据

        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)

        Returns:
            查询结果DataFrame
        """
        if self.df is None:
            return pl.DataFrame()

        # 将字符串日期转换为Date类型进行比较
        start_date_parsed = pl.lit(start_date).str.strptime(pl.Date, "%Y-%m-%d")
        end_date_parsed = pl.lit(end_date).str.strptime(pl.Date, "%Y-%m-%d")

        return self.df.filter(
            (pl.col("date") >= start_date_parsed) & (pl.col("date") <= end_date_parsed)
        )
        
    def query_by_sum_range(self, min_sum: int, max_sum: int) -> pl.DataFrame:
        """
        按和值范围查询数据
        
        Args:
            min_sum: 最小和值
            max_sum: 最大和值
            
        Returns:
            查询结果DataFrame
        """
        if self.df is None:
            return pl.DataFrame()
            
        return self.df.filter(
            (pl.col("sum_value") >= min_sum) & (pl.col("sum_value") <= max_sum)
        )
        
    def get_recent_trends(self, days: int = 30) -> Dict[str, Any]:
        """
        获取最近趋势分析
        
        Args:
            days: 分析天数
            
        Returns:
            趋势分析结果
        """
        if self.df is None:
            return {}
            
        # 获取最近N天的数据
        recent_data = self.df.tail(days)
        
        if len(recent_data) == 0:
            return {}
            
        return {
            "period": f"最近{len(recent_data)}期",
            "sum_value_trend": {
                "mean": round(recent_data["sum_value"].mean(), 2),
                "std": round(recent_data["sum_value"].std(), 2)
            },
            "span_value_trend": {
                "mean": round(recent_data["span_value"].mean(), 2),
                "std": round(recent_data["span_value"].std(), 2)
            },
            "sales_trend": {
                "mean": round(recent_data["sales_amount"].mean(), 2),
                "total": recent_data["sales_amount"].sum()
            }
        }
        
    def export_analysis_results(self, output_dir: str) -> Dict[str, str]:
        """
        导出分析结果
        
        Args:
            output_dir: 输出目录
            
        Returns:
            导出文件路径字典
        """
        if self.df is None:
            return {}
            
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        results = {}
        
        # 导出基础统计
        basic_stats = self.get_basic_stats()
        stats_file = output_path / "polars_basic_stats.json"
        import json
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(basic_stats, f, ensure_ascii=False, indent=2)
        results['basic_stats'] = str(stats_file)
        
        # 导出频率分析
        freq_analysis = self.get_frequency_analysis()
        freq_file = output_path / "polars_frequency_analysis.json"
        with open(freq_file, 'w', encoding='utf-8') as f:
            json.dump(freq_analysis, f, ensure_ascii=False, indent=2)
        results['frequency_analysis'] = str(freq_file)
        
        # 导出完整数据为CSV
        csv_file = output_path / "polars_complete_data.csv"
        self.df.write_csv(csv_file)
        results['complete_data'] = str(csv_file)
        
        self.logger.info(f"分析结果已导出到: {output_dir}")
        return results
