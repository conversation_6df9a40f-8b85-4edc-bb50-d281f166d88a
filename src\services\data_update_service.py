#!/usr/bin/env python3
"""
数据更新服务

提供数据更新相关的业务逻辑封装
"""

import asyncio
import logging
import os
import sys
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.core.database import DatabaseManager
from src.data.collector import LotteryDataCollector
from src.data.incremental_updater import IncrementalUpdater

logger = logging.getLogger(__name__)

class DataUpdateService:
    """数据更新服务类"""
    
    def __init__(self, data_engine=None, data_dir: str = "data"):
        """
        初始化数据更新服务
        
        Args:
            data_engine: 数据引擎实例
            data_dir: 数据目录路径
        """
        self.data_engine = data_engine
        self.data_dir = data_dir
        
        # 初始化组件
        self.updater = IncrementalUpdater(data_dir)
        self.collector = LotteryDataCollector()
        self.db_manager = DatabaseManager() if not data_engine else data_engine.db_manager
        
        # 更新状态跟踪
        self.active_updates: Dict[str, Dict[str, Any]] = {}
        
        logger.info("DataUpdateService初始化完成")
    
    async def check_update_status(self) -> Dict[str, Any]:
        """
        检查数据更新状态
        
        Returns:
            数据更新状态信息
        """
        try:
            # 获取当前数据库状态
            record_count = self.db_manager.get_records_count()
            date_range = self.db_manager.get_date_range()
            
            # 获取更新器状态
            updater_status = self.updater.get_update_status()
            
            # 检查数据源可访问性
            data_source_status = await self._check_data_source_accessibility()
            
            # 检查是否有新数据
            has_new_data = await self._check_for_new_data()
            
            # 计算下一个期号
            next_period = self._calculate_next_period(updater_status.get('last_period'))
            
            return {
                "last_update_time": updater_status.get('last_update'),
                "last_period": updater_status.get('last_period'),
                "last_date": updater_status.get('last_date'),
                "current_record_count": record_count,
                "date_range": {
                    "start": date_range[0] if date_range and date_range[0] else None,
                    "end": date_range[1] if date_range and date_range[1] else None
                },
                "has_new_data": has_new_data,
                "data_source_status": data_source_status,
                "next_expected_period": next_period,
                "update_available": has_new_data and data_source_status == "accessible",
                "update_count": updater_status.get('update_count', 0),
                "raw_files": updater_status.get('raw_files', 0),
                "processed_files": updater_status.get('processed_files', 0)
            }
            
        except Exception as e:
            logger.error(f"检查更新状态失败: {e}")
            return {
                "error": str(e),
                "last_update_time": None,
                "current_record_count": 0,
                "has_new_data": False,
                "data_source_status": "error",
                "update_available": False
            }
    
    async def trigger_update(self, force_update: bool = False) -> Dict[str, Any]:
        """
        触发数据更新
        
        Args:
            force_update: 是否强制更新
            
        Returns:
            更新结果信息
        """
        update_id = str(uuid.uuid4())
        start_time = datetime.now()
        
        try:
            # 记录更新开始
            self.active_updates[update_id] = {
                "status": "in_progress",
                "progress": 0,
                "message": "开始数据更新...",
                "start_time": start_time,
                "records_added": 0,
                "total_records": 0
            }
            
            logger.info(f"开始数据更新，ID: {update_id}, 强制更新: {force_update}")
            
            # 更新进度
            await self._update_progress(update_id, 10, "检查数据源...")
            
            # 执行增量更新
            result = self.updater.perform_incremental_update(force_update=force_update)
            
            if result["success"]:
                await self._update_progress(update_id, 80, "更新数据库...")
                
                # 如果有数据引擎，重新加载数据
                if self.data_engine and "update_info" in result:
                    await self._update_progress(update_id, 90, "重新加载数据...")
                    self.data_engine.load_data_from_database()

                    # 清理缓存
                    await self._update_progress(update_id, 95, "清理缓存...")
                    self.data_engine.optimize_performance()

                # 如果有新数据添加，触发预测模型重训练
                records_added = result.get("update_info", {}).get("records_added", 0)
                if records_added > 0:
                    await self._update_progress(update_id, 97, "触发预测模型重训练...")
                    retrain_result = await self._trigger_model_retrain()
                    logger.info(f"预测模型重训练结果: {retrain_result}")

                # 完成更新
                duration_ms = (datetime.now() - start_time).total_seconds() * 1000
                
                final_result = {
                    "update_id": update_id,
                    "status": "completed",
                    "progress": 100,
                    "message": result["message"],
                    "records_added": result.get("update_info", {}).get("records_added", 0),
                    "total_records": result.get("update_info", {}).get("total_records", 0),
                    "quality_score": result.get("quality_score", 1.0),
                    "duration_ms": round(duration_ms, 2),
                    "timestamp": datetime.now().isoformat()
                }
                
                self.active_updates[update_id] = final_result
                logger.info(f"数据更新完成: {update_id}")
                
                return final_result
                
            else:
                # 更新失败
                error_result = {
                    "update_id": update_id,
                    "status": "failed",
                    "progress": 0,
                    "message": result["message"],
                    "error": result.get("error", "未知错误"),
                    "duration_ms": (datetime.now() - start_time).total_seconds() * 1000
                }
                
                self.active_updates[update_id] = error_result
                logger.error(f"数据更新失败: {update_id}, 原因: {result['message']}")
                
                return error_result
                
        except Exception as e:
            # 异常处理
            error_result = {
                "update_id": update_id,
                "status": "failed",
                "progress": 0,
                "message": f"更新过程中发生异常: {str(e)}",
                "error": str(e),
                "duration_ms": (datetime.now() - start_time).total_seconds() * 1000
            }
            
            self.active_updates[update_id] = error_result
            logger.error(f"数据更新异常: {update_id}, 错误: {e}")
            
            return error_result
    
    def get_update_history(self, limit: int = 10) -> Dict[str, Any]:
        """
        获取更新历史记录
        
        Args:
            limit: 返回记录数限制
            
        Returns:
            更新历史记录
        """
        try:
            # 从活跃更新中获取已完成的更新
            completed_updates = [
                update for update in self.active_updates.values()
                if update.get("status") in ["completed", "failed"]
            ]
            
            # 按时间排序
            completed_updates.sort(
                key=lambda x: x.get("timestamp", x.get("start_time", "")),
                reverse=True
            )
            
            # 限制返回数量
            limited_updates = completed_updates[:limit]
            
            return {
                "updates": limited_updates,
                "total_count": len(completed_updates),
                "active_updates": len([
                    u for u in self.active_updates.values()
                    if u.get("status") == "in_progress"
                ])
            }
            
        except Exception as e:
            logger.error(f"获取更新历史失败: {e}")
            return {
                "updates": [],
                "total_count": 0,
                "active_updates": 0,
                "error": str(e)
            }
    
    def get_update_progress(self, update_id: str) -> Dict[str, Any]:
        """
        获取特定更新的进度
        
        Args:
            update_id: 更新ID
            
        Returns:
            更新进度信息
        """
        return self.active_updates.get(update_id, {
            "error": "更新ID不存在",
            "status": "not_found"
        })
    
    async def cancel_update(self, update_id: str) -> bool:
        """
        取消正在进行的更新
        
        Args:
            update_id: 更新ID
            
        Returns:
            是否成功取消
        """
        if update_id in self.active_updates:
            update = self.active_updates[update_id]
            if update.get("status") == "in_progress":
                update["status"] = "cancelled"
                update["message"] = "更新已被用户取消"
                logger.info(f"更新已取消: {update_id}")
                return True
        
        return False
    
    async def _check_data_source_accessibility(self) -> str:
        """检查数据源可访问性"""
        try:
            # 尝试获取数据源信息
            test_data = await self.collector.fetch_data_async()
            if test_data and len(test_data) > 100:
                return "accessible"
            else:
                return "limited"
        except Exception as e:
            logger.warning(f"数据源访问检查失败: {e}")
            return "inaccessible"
    
    async def _trigger_model_retrain(self) -> Dict[str, Any]:
        """触发预测模型重训练"""
        try:
            # 尝试导入并调用智能融合系统重训练
            try:
                from prediction.intelligent_fusion import \
                    IntelligentFusionSystem
                intelligent_system = IntelligentFusionSystem()
                retrain_result = intelligent_system.train_all_models(force_retrain=True)

                return {
                    "success": True,
                    "message": "智能融合模型重训练完成",
                    "result": retrain_result
                }
            except Exception as e:
                logger.warning(f"智能融合模型重训练失败: {e}")

            # 尝试调用传统预测服务重训练
            try:
                import requests
                response = requests.post("http://127.0.0.1:8888/api/v1/prediction/train",
                                       json={"force_retrain": True}, timeout=30)
                if response.status_code == 200:
                    return {
                        "success": True,
                        "message": "预测服务重训练完成",
                        "result": response.json()
                    }
                else:
                    return {
                        "success": False,
                        "message": f"预测服务重训练失败: {response.status_code}",
                        "error": response.text
                    }
            except Exception as e:
                logger.warning(f"预测服务重训练失败: {e}")

            return {
                "success": False,
                "message": "所有重训练方法都失败",
                "error": "无法触发模型重训练"
            }

        except Exception as e:
            logger.error(f"触发模型重训练异常: {e}")
            return {
                "success": False,
                "message": f"重训练触发异常: {str(e)}",
                "error": str(e)
            }

    async def _check_for_new_data(self) -> bool:
        """检查是否有新数据"""
        try:
            # 获取当前数据状态
            current_status = self.updater.get_update_status()
            current_period = current_status.get('last_period')

            if not current_period:
                return True  # 如果没有数据，认为有新数据

            # 获取最新数据
            latest_data = await self.collector.fetch_data_async()
            if not latest_data:
                return False

            # 简单检查：比较数据长度或最后几行
            lines = latest_data.strip().split('\n')
            if len(lines) > current_status.get('record_count', 0):
                return True

            return False

        except Exception as e:
            logger.warning(f"检查新数据失败: {e}")
            return False
    
    def _calculate_next_period(self, last_period: Optional[str]) -> Optional[str]:
        """计算下一个期号"""
        if not last_period:
            return None
        
        try:
            # 福彩3D期号格式：YYYYNNN (如2025184)
            year = int(last_period[:4])
            number = int(last_period[4:])
            
            # 简单递增
            next_number = number + 1
            
            # 检查是否需要跨年
            if next_number > 365:  # 大概的年度期数
                year += 1
                next_number = 1
            
            return f"{year}{next_number:03d}"
            
        except Exception:
            return None
    
    async def _update_progress(self, update_id: str, progress: int, message: str):
        """更新进度信息"""
        if update_id in self.active_updates:
            self.active_updates[update_id].update({
                "progress": progress,
                "message": message
            })
            
            # 添加小延迟以便UI更新
            await asyncio.sleep(0.1)
