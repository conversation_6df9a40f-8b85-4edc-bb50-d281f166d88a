#!/usr/bin/env python3
"""
Bug分类字段迁移脚本
添加环境分类、优先级、标签等字段到bug_reports表
"""

import sqlite3
import logging
from pathlib import Path
from src.bug_detection.core.database_manager import DatabaseManager

logger = logging.getLogger(__name__)

def migrate_bug_classification():
    """添加Bug分类相关字段"""
    try:
        # 创建数据库管理器
        db_manager = DatabaseManager()
        
        # 连接数据库
        conn = sqlite3.connect(db_manager.db_path)
        cursor = conn.cursor()
        
        print("🔧 开始Bug分类字段迁移...")
        
        # 检查现有表结构
        cursor.execute("PRAGMA table_info(bug_reports)")
        existing_columns = [col[1] for col in cursor.fetchall()]
        print(f"📋 现有字段: {existing_columns}")
        
        # 需要添加的字段
        new_fields = [
            ('environment', 'TEXT DEFAULT "production"', '环境类型'),
            ('category', 'TEXT DEFAULT "general"', 'Bug分类'),
            ('priority', 'TEXT DEFAULT "medium"', '优先级'),
            ('tags', 'TEXT', '标签'),
            ('source', 'TEXT DEFAULT "user"', '来源'),
            ('component_name', 'TEXT', '组件名称'),
            ('reproduction_steps', 'TEXT', '重现步骤'),
            ('system_context', 'TEXT', '系统上下文'),
            ('user_journey', 'TEXT', '用户路径'),
            ('screenshots', 'TEXT', '截图路径'),
            ('assigned_to', 'TEXT', '分配给'),
            ('resolution_notes', 'TEXT', '解决说明'),
            ('updated_at', 'TIMESTAMP', '更新时间'),
            ('updated_by', 'TEXT', '更新人')
        ]
        
        # 添加缺少的字段
        added_fields = []
        for field_name, field_type, description in new_fields:
            if field_name not in existing_columns:
                try:
                    sql = f"ALTER TABLE bug_reports ADD COLUMN {field_name} {field_type}"
                    cursor.execute(sql)
                    added_fields.append(f"{field_name} ({description})")
                    print(f"✅ 已添加字段: {field_name} - {description}")
                except Exception as e:
                    print(f"❌ 添加字段 {field_name} 失败: {e}")
        
        # 创建Bug分类枚举值
        classification_data = {
            'environment': ['production', 'development', 'testing', 'staging'],
            'category': ['ui', 'api', 'database', 'performance', 'security', 'integration', 'general'],
            'priority': ['critical', 'high', 'medium', 'low'],
            'source': ['user', 'automated_test', 'monitoring', 'manual_test', 'integration_test']
        }
        
        print("\n📊 Bug分类枚举值:")
        for field, values in classification_data.items():
            print(f"  {field}: {', '.join(values)}")
        
        # 更新现有数据的分类
        print("\n🔄 更新现有Bug数据分类...")
        
        # 根据error_type自动分类现有Bug
        classification_rules = [
            ("test_error", "testing", "automated_test", "low"),
            ("integration_test", "testing", "integration_test", "low"),
            ("TypeError", "production", "user", "medium"),
            ("ReferenceError", "production", "user", "medium"),
            ("SyntaxError", "production", "user", "high"),
            ("NetworkError", "production", "user", "high"),
            ("unknown", "production", "user", "medium")
        ]
        
        for error_type, env, source, priority in classification_rules:
            cursor.execute("""
                UPDATE bug_reports 
                SET environment = ?, source = ?, priority = ?
                WHERE error_type LIKE ? AND environment IS NULL
            """, (env, source, priority, f"%{error_type}%"))
            
            updated_count = cursor.rowcount
            if updated_count > 0:
                print(f"  ✅ 更新了 {updated_count} 条 {error_type} 类型的Bug")
        
        # 提交更改
        conn.commit()
        
        # 验证迁移结果
        cursor.execute("SELECT COUNT(*) FROM bug_reports")
        total_bugs = cursor.fetchone()[0]
        
        cursor.execute("SELECT environment, COUNT(*) FROM bug_reports GROUP BY environment")
        env_stats = cursor.fetchall()
        
        print(f"\n📊 迁移完成统计:")
        print(f"  总Bug数: {total_bugs}")
        print(f"  环境分布:")
        for env, count in env_stats:
            print(f"    {env}: {count}")
        
        conn.close()
        
        if added_fields:
            print(f"\n✅ 成功添加字段: {', '.join(added_fields)}")
        else:
            print("\n✅ 所有字段已存在，无需添加")
        
        print("🎉 Bug分类字段迁移完成！")
        return True
        
    except Exception as e:
        print(f"❌ Bug分类字段迁移失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_bug_classification_views():
    """创建Bug分类相关的视图"""
    try:
        db_manager = DatabaseManager()
        conn = sqlite3.connect(db_manager.db_path)
        cursor = conn.cursor()
        
        print("🔧 创建Bug分类视图...")
        
        # 生产环境Bug视图
        cursor.execute("""
            CREATE VIEW IF NOT EXISTS production_bugs AS
            SELECT * FROM bug_reports 
            WHERE environment = 'production'
            ORDER BY created_at DESC
        """)
        
        # 测试环境Bug视图
        cursor.execute("""
            CREATE VIEW IF NOT EXISTS test_bugs AS
            SELECT * FROM bug_reports 
            WHERE environment IN ('testing', 'development')
            ORDER BY created_at DESC
        """)
        
        # 高优先级Bug视图
        cursor.execute("""
            CREATE VIEW IF NOT EXISTS high_priority_bugs AS
            SELECT * FROM bug_reports 
            WHERE priority IN ('critical', 'high')
            AND status IN ('open', 'in_progress')
            ORDER BY 
                CASE priority 
                    WHEN 'critical' THEN 1 
                    WHEN 'high' THEN 2 
                    ELSE 3 
                END,
                created_at DESC
        """)
        
        # Bug统计视图
        cursor.execute("""
            CREATE VIEW IF NOT EXISTS bug_statistics AS
            SELECT 
                environment,
                category,
                priority,
                status,
                COUNT(*) as count,
                COUNT(CASE WHEN status = 'resolved' THEN 1 END) as resolved_count,
                ROUND(
                    COUNT(CASE WHEN status = 'resolved' THEN 1 END) * 100.0 / COUNT(*), 
                    2
                ) as resolution_rate
            FROM bug_reports
            GROUP BY environment, category, priority, status
        """)
        
        conn.commit()
        conn.close()
        
        print("✅ Bug分类视图创建完成")
        return True
        
    except Exception as e:
        print(f"❌ 创建Bug分类视图失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始Bug分类系统迁移...")
    
    # 执行字段迁移
    if migrate_bug_classification():
        # 创建分类视图
        create_bug_classification_views()
        print("\n🎉 Bug分类系统迁移完成！")
    else:
        print("\n❌ Bug分类系统迁移失败！")
