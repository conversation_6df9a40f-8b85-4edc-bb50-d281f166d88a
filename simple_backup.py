import os
import shutil
from datetime import datetime

# 创建备份目录
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
backup_dir = f"backup/improvement_project_{timestamp}"
os.makedirs(backup_dir, exist_ok=True)
print(f"✅ 创建备份目录: {backup_dir}")

# 备份数据库
if os.path.exists("data/lottery_data.db"):
    shutil.copy2("data/lottery_data.db", f"{backup_dir}/lottery_data.db")
    print("✅ 备份主数据库")

# 备份关键配置
if os.path.exists("src/ui/main.py"):
    os.makedirs(f"{backup_dir}/src/ui", exist_ok=True)
    shutil.copy2("src/ui/main.py", f"{backup_dir}/src/ui/main.py")
    print("✅ 备份UI主文件")

print("🎉 基础备份完成!")
