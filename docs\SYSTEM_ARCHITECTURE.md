# 福彩3D预测系统 - 系统架构文档

## 概述

福彩3D预测系统是一个基于机器学习的彩票号码预测平台，采用现代化的微服务架构，提供数据分析、模型训练、预测生成和实时监控等功能。

## 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    福彩3D预测系统                              │
├─────────────────────────────────────────────────────────────┤
│  前端层 (Frontend Layer)                                     │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │   Streamlit UI  │  │   Web Dashboard │                   │
│  │   (用户界面)     │  │   (管理界面)     │                   │
│  └─────────────────┘  └─────────────────┘                   │
├─────────────────────────────────────────────────────────────┤
│  API层 (API Layer)                                          │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │   FastAPI       │  │   WebSocket     │                   │
│  │   (REST API)    │  │   (实时通信)     │                   │
│  └─────────────────┘  └─────────────────┘                   │
├─────────────────────────────────────────────────────────────┤
│  业务逻辑层 (Business Logic Layer)                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │   预测引擎       │  │   特征工程       │  │   模型管理       ││
│  │   (Prediction)  │  │   (Features)    │  │   (Models)      ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  数据层 (Data Layer)                                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │   SQLite DB     │  │   数据采集器     │  │   缓存系统       ││
│  │   (主数据库)     │  │   (Collector)   │  │   (Cache)       ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  监控层 (Monitoring Layer)                                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │   性能监控       │  │   健康检查       │  │   Bug检测        ││
│  │   (Performance) │  │   (Health)      │  │   (Bug Track)   ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

## 核心组件

### 1. 前端层 (Frontend Layer)

#### Streamlit UI
- **位置**: `src/ui/main.py`
- **功能**: 用户交互界面，提供数据查看、预测分析、模型训练等功能
- **特性**: 
  - 响应式设计
  - 实时数据更新
  - 交互式图表
  - 错误处理和用户反馈

#### 页面组件
- **数据概览**: `src/ui/pages/overview.py`
- **特征工程**: `src/ui/pages/feature_engineering_deep.py`
- **模型训练**: `src/ui/pages/training_monitoring_deep.py`
- **预测分析**: `src/ui/pages/prediction_analysis.py`
- **智能融合**: `src/ui/pages/intelligent_fusion_optimization.py`

### 2. API层 (API Layer)

#### FastAPI服务
- **位置**: `src/api/production_main.py`
- **端口**: 8000
- **功能**: 提供RESTful API接口
- **端点**:
  - `/api/v1/health/*` - 健康检查
  - `/api/v1/data/*` - 数据查询
  - `/api/v1/prediction/*` - 预测服务
  - `/api/v1/bug-detection/*` - Bug检测

#### WebSocket服务
- **功能**: 实时通信和事件推送
- **端点**:
  - `/ws/bug-detection` - Bug检测实时通信
  - `/ws/realtime-stats` - 实时统计数据

### 3. 业务逻辑层 (Business Logic Layer)

#### 预测引擎
- **位置**: `src/core/prediction/`
- **功能**: 核心预测算法和模型推理
- **组件**:
  - 模型加载器
  - 预测处理器
  - 结果后处理

#### 特征工程
- **位置**: `src/core/features/`
- **功能**: 数据特征提取和处理
- **特性**:
  - 165+维特征提取
  - 时间序列特征
  - 统计特征
  - 组合特征

#### 模型管理
- **位置**: `src/core/models/`
- **功能**: 机器学习模型管理
- **支持模型**:
  - 随机森林
  - XGBoost
  - LightGBM
  - 神经网络

### 4. 数据层 (Data Layer)

#### 数据库管理
- **位置**: `src/core/database_manager.py`
- **数据库**: SQLite (lottery_data.db)
- **特性**:
  - 连接池管理
  - WAL模式
  - 事务处理
  - 健康检查

#### 数据采集
- **位置**: `src/data/collector.py`
- **数据源**: https://data.17500.cn/3d_asc.txt
- **特性**:
  - 自动更新
  - 重试机制
  - 反爬虫对策
  - 数据验证

#### 缓存系统
- **位置**: `data/cache/`
- **功能**: 数据缓存和临时存储
- **特性**:
  - 文件缓存
  - 过期管理
  - 自动清理

### 5. 监控层 (Monitoring Layer)

#### 性能监控
- **位置**: `src/monitoring/performance_monitor.py`
- **功能**: 系统性能监控和告警
- **监控指标**:
  - API响应时间
  - 数据库查询时间
  - WebSocket延迟
  - 系统资源使用

#### 健康检查
- **位置**: `src/monitoring/health_checker.py`
- **功能**: 系统健康状态检查
- **检查项目**:
  - 数据库连接
  - 数据源状态
  - WebSocket状态
  - 系统资源

#### Bug检测
- **位置**: `src/bug_detection/`
- **功能**: 实时错误检测和报告
- **特性**:
  - JavaScript错误捕获
  - 实时错误推送
  - 错误统计分析

## 数据流

### 数据采集流程
```
外部数据源 → 数据采集器 → 数据验证 → 数据库存储 → 缓存更新
```

### 预测流程
```
历史数据 → 特征提取 → 模型推理 → 结果后处理 → 预测输出
```

### 监控流程
```
系统事件 → 性能监控 → 告警规则 → 通知推送 → 问题处理
```

## 技术栈

### 后端技术
- **Python 3.11+**: 主要编程语言
- **FastAPI**: Web框架
- **SQLite**: 数据库
- **Pandas**: 数据处理
- **Scikit-learn**: 机器学习
- **XGBoost/LightGBM**: 梯度提升算法

### 前端技术
- **Streamlit**: Web界面框架
- **Plotly**: 数据可视化
- **JavaScript**: 客户端脚本

### 监控技术
- **WebSocket**: 实时通信
- **Asyncio**: 异步处理
- **Logging**: 日志记录

## 部署架构

### 开发环境
```
本地开发 → Git版本控制 → 测试验证 → 文档更新
```

### 生产环境
```
代码部署 → 服务启动 → 健康检查 → 监控告警
```

## 安全考虑

### 数据安全
- 数据库备份机制
- 数据访问控制
- 敏感信息加密

### 系统安全
- API访问限制
- 错误信息过滤
- 资源使用限制

## 性能优化

### 数据库优化
- 索引优化
- 查询优化
- 连接池管理

### 应用优化
- 缓存策略
- 异步处理
- 资源复用

### 监控优化
- 实时监控
- 性能告警
- 自动恢复

## 扩展性设计

### 水平扩展
- 微服务架构
- 负载均衡
- 分布式缓存

### 垂直扩展
- 资源配置
- 性能调优
- 容量规划

## 维护指南

### 日常维护
- 数据备份
- 日志清理
- 性能监控

### 故障处理
- 错误诊断
- 快速恢复
- 根因分析

### 版本更新
- 代码部署
- 数据迁移
- 功能验证
