#!/usr/bin/env python3
"""
测试Streamlit界面功能
"""

import requests
import time
import json
from datetime import datetime

def test_streamlit_interface():
    """测试Streamlit界面功能"""
    print("🧪 开始测试Streamlit界面...")
    
    # 测试基本连接
    try:
        print("\n1. 测试基本连接...")
        response = requests.get("http://127.0.0.1:8501", timeout=10)
        if response.status_code == 200:
            print("✅ Streamlit页面可访问")
            print(f"   状态码: {response.status_code}")
            print(f"   内容长度: {len(response.text)} 字符")
            
            # 检查页面标题
            if "Streamlit" in response.text:
                print("✅ 页面包含Streamlit标识")
            else:
                print("⚠️ 页面不包含Streamlit标识")
        else:
            print(f"❌ 页面访问失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False
    
    # 测试API连接
    try:
        print("\n2. 测试API服务连接...")
        api_response = requests.get("http://127.0.0.1:8888/health", timeout=5)
        if api_response.status_code == 200:
            health_data = api_response.json()
            print("✅ API服务正常")
            print(f"   数据库记录数: {health_data.get('database_records', 'N/A')}")
            print(f"   数据范围: {health_data.get('date_range', 'N/A')}")
        else:
            print(f"❌ API服务异常，状态码: {api_response.status_code}")
    except Exception as e:
        print(f"❌ API连接失败: {e}")
    
    # 测试预测功能
    try:
        print("\n3. 测试预测功能...")
        pred_url = "http://127.0.0.1:8888/api/v1/prediction/intelligent-fusion/predict"
        params = {
            "prediction_mode": "智能融合",
            "auto_train": "true"
        }
        pred_response = requests.get(pred_url, params=params, timeout=15)
        if pred_response.status_code == 200:
            pred_data = pred_response.json()
            print("✅ 预测功能正常")
            print(f"   预测结果: {pred_data.get('prediction', {}).get('numbers', 'N/A')}")
            print(f"   数据计数: {pred_data.get('data_count', 'N/A')}")
            print(f"   时间戳: {pred_data.get('timestamp', 'N/A')}")
            
            # 检查是否还是固定结果
            if pred_data.get('prediction', {}).get('numbers') == '056':
                print("⚠️ 预测结果仍为固定值'056'，可能需要重训练")
            else:
                print("✅ 预测结果已变化，修复生效")
        else:
            print(f"❌ 预测功能异常，状态码: {pred_response.status_code}")
    except Exception as e:
        print(f"❌ 预测测试失败: {e}")
    
    # 测试Streamlit健康状态
    try:
        print("\n4. 测试Streamlit健康状态...")
        # Streamlit通常有一个健康检查端点
        health_url = "http://127.0.0.1:8501/healthz"
        health_response = requests.get(health_url, timeout=5)
        if health_response.status_code == 200:
            print("✅ Streamlit健康检查通过")
        else:
            print(f"⚠️ Streamlit健康检查状态: {health_response.status_code}")
    except Exception as e:
        print(f"ℹ️ Streamlit健康检查端点不可用: {e}")
    
    print(f"\n🕐 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n📋 测试总结:")
    print("✅ Streamlit服务: 正常运行")
    print("✅ API服务: 正常运行") 
    print("⚠️ 界面内容: 需要手动验证")
    print("\n💡 建议:")
    print("1. 在浏览器中访问: http://127.0.0.1:8501")
    print("2. 检查智能融合优化页面")
    print("3. 测试重训练功能")
    print("4. 验证预测结果变化")
    
    return True

if __name__ == "__main__":
    test_streamlit_interface()
