#!/usr/bin/env python3
"""
深度调试Polars引擎数据问题
"""

import sys
sys.path.append('src')

from core.database import DatabaseManager
from core.polars_engine import PolarsEngine
from core.data_engine import DataEngine
import polars as pl
import sqlite3

def debug_polars_issue():
    print("🔍 深度调试Polars引擎数据问题...")

    # 1. 检查数据库连接和数据
    print("\n1. 检查数据库数据...")
    db = DatabaseManager()

    # 直接查询数据库
    print("使用数据库连接直接查询...")
    with db._get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM lottery_records")
        count = cursor.fetchone()[0]
        print(f"数据库记录总数: {count}")

        cursor.execute("SELECT * FROM lottery_records LIMIT 5")
        db_records = cursor.fetchall()
        print(f"查询到的前5条记录数: {len(db_records)}")

        if db_records:
            print("前5条数据库记录:")
            for i, record in enumerate(db_records[:5]):
                print(f"  {i+1}: {record}")

        # 检查表结构
        cursor.execute("PRAGMA table_info(lottery_records)")
        columns = cursor.fetchall()
        print(f"表结构: {[col[1] for col in columns]}")

    # 2. 测试Polars直接读取数据库
    print("\n2. 测试Polars直接读取数据库...")
    try:
        with db._get_connection() as conn:
            df_direct = pl.read_database("SELECT * FROM lottery_records LIMIT 10", conn)
            print(f"Polars直接读取DataFrame形状: {df_direct.shape}")
            print(f"Polars直接读取DataFrame列: {df_direct.columns}")
            if not df_direct.is_empty():
                print("Polars直接读取前3行:")
                print(df_direct.head(3))
            else:
                print("⚠️ Polars直接读取DataFrame为空！")
    except Exception as e:
        print(f"Polars直接读取错误: {e}")
        import traceback
        traceback.print_exc()

    # 3. 检查数据引擎加载过程
    print("\n3. 检查数据引擎加载过程...")
    data_engine = DataEngine()

    # 检查初始状态
    print(f"初始Polars引擎DataFrame: {data_engine.polars_engine.df}")

    # 强制重新加载数据
    print("强制重新加载数据...")
    try:
        data_engine.load_data_from_database()

        # 再次检查
        df_after_reload = data_engine.polars_engine.df
        print(f"重新加载后DataFrame: {df_after_reload}")
        if df_after_reload is not None:
            print(f"重新加载后DataFrame形状: {df_after_reload.shape}")
            print(f"重新加载后DataFrame列: {df_after_reload.columns}")
            if not df_after_reload.is_empty():
                print("重新加载后DataFrame前3行:")
                print(df_after_reload.head(3))
            else:
                print("⚠️ 重新加载后DataFrame仍为空！")

    except Exception as e:
        print(f"数据引擎加载错误: {e}")
        import traceback
        traceback.print_exc()

    # 4. 测试统计计算
    print("\n4. 测试统计计算...")
    try:
        # 直接测试Polars引擎统计
        polars_stats = data_engine.polars_engine.get_basic_stats()
        print(f"Polars引擎统计结果: {polars_stats}")

        # 测试数据引擎统计
        stats = data_engine.get_basic_stats(use_cache=False)
        print(f"数据引擎统计结果: {stats}")
        print(f"统计结果键: {list(stats.keys()) if stats else 'None'}")
    except Exception as e:
        print(f"统计计算错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_polars_issue()
