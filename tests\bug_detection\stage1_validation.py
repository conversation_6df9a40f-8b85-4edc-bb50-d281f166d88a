"""
阶段1验收测试脚本
创建日期: 2025年7月24日
用途: 验证基础监控系统的所有组件功能
"""

import sys
import os
import tempfile
import traceback
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

def test_database_manager():
    """测试数据库管理器"""
    print("🔍 测试数据库管理器...")
    
    try:
        from src.bug_detection.core.database_manager import DatabaseManager
        
        # 创建临时数据库
        temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_db.close()
        
        # 初始化数据库管理器
        db_manager = DatabaseManager(temp_db.name)
        
        # 测试保存Bug报告
        bug_data = {
            'error_type': 'javascript',
            'severity': 'high',
            'page_name': 'test_page',
            'error_message': 'Test error message',
            'stack_trace': 'Test stack trace'
        }
        
        bug_id = db_manager.save_bug_report(bug_data)
        assert bug_id != "", "Bug ID不应为空"
        assert bug_id.startswith("BUG_"), "Bug ID格式不正确"
        
        # 测试获取Bug报告
        bug_reports = db_manager.get_bug_reports(limit=1)
        assert len(bug_reports) == 1, "应该有1个Bug报告"
        assert bug_reports[0]['error_type'] == 'javascript', "错误类型不匹配"
        
        # 测试保存性能指标
        db_manager.save_performance_metric('/api/test', 0.5, 200)
        summary = db_manager.get_performance_summary()
        assert '/api/test' in summary, "性能指标未保存"
        
        # 测试保存JavaScript错误
        db_manager.save_js_error('test_session', 'test error', 'http://test.com')
        
        # 清理
        os.unlink(temp_db.name)
        
        print("✅ 数据库管理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据库管理器测试失败: {e}")
        traceback.print_exc()
        return False

def test_javascript_monitor():
    """测试JavaScript监控器"""
    print("🔍 测试JavaScript监控器...")
    
    try:
        from src.bug_detection.monitoring.js_monitor import JavaScriptMonitor
        
        # 创建监控器实例
        js_monitor = JavaScriptMonitor()
        
        # 测试会话ID生成
        session_id = js_monitor._get_session_id()
        assert isinstance(session_id, str), "会话ID应该是字符串"
        assert len(session_id) > 0, "会话ID不应为空"
        
        # 测试会话ID一致性
        session_id2 = js_monitor._get_session_id()
        assert session_id == session_id2, "会话ID应该保持一致"
        
        print("✅ JavaScript监控器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ JavaScript监控器测试失败: {e}")
        traceback.print_exc()
        return False

def test_api_monitor():
    """测试API监控器"""
    print("🔍 测试API监控器...")
    
    try:
        from src.bug_detection.monitoring.api_monitor import APIPerformanceMonitor
        
        # 创建监控器实例
        api_monitor = APIPerformanceMonitor(app=None)
        
        # 测试路径排除逻辑
        assert api_monitor._should_exclude_path('/docs'), "应该排除/docs路径"
        assert api_monitor._should_exclude_path('/redoc'), "应该排除/redoc路径"
        assert not api_monitor._should_exclude_path('/api/test'), "不应该排除/api/test路径"
        
        # 测试性能摘要（空数据）
        summary = api_monitor.get_performance_summary()
        assert 'total_requests' in summary, "摘要应包含总请求数"
        assert 'error_count' in summary, "摘要应包含错误数"
        assert 'error_rate' in summary, "摘要应包含错误率"
        
        print("✅ API监控器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ API监控器测试失败: {e}")
        traceback.print_exc()
        return False

def test_bug_reporter():
    """测试Bug报告生成器"""
    print("🔍 测试Bug报告生成器...")
    
    try:
        from src.bug_detection.feedback.bug_reporter import BugReporter, IntelligentBugReporter
        
        # 创建Bug报告生成器
        bug_reporter = BugReporter()
        
        # 测试严重程度评估
        assert bug_reporter._assess_severity({'message': 'critical error'}) == 'critical'
        assert bug_reporter._assess_severity({'message': 'database error'}) == 'high'
        assert bug_reporter._assess_severity({'message': 'warning message'}) == 'medium'
        
        # 测试错误分类
        assert bug_reporter._categorize_error({'type': 'javascript'}) == 'Frontend'
        assert bug_reporter._categorize_error({'type': 'api_error'}) == 'Backend'
        
        # 测试Bug报告生成
        error_data = {
            'type': 'javascript',
            'message': 'Test error message',
            'source': 'test.js',
            'line_number': 10
        }
        
        report = bug_reporter.generate_bug_report(error_data)
        
        # 验证报告结构
        required_fields = ['id', 'timestamp', 'error', 'environment', 'context']
        for field in required_fields:
            assert field in report, f"报告应包含{field}字段"
        
        assert report['error']['type'] == 'javascript', "错误类型不匹配"
        assert report['error']['message'] == 'Test error message', "错误消息不匹配"
        
        # 测试智能Bug报告生成器
        intelligent_reporter = IntelligentBugReporter()
        enhanced_report = intelligent_reporter.generate_enhanced_report(error_data)
        
        # 验证增强功能
        enhanced_fields = ['similar_bugs', 'impact_analysis', 'root_cause_analysis']
        for field in enhanced_fields:
            assert field in enhanced_report, f"增强报告应包含{field}字段"
        
        print("✅ Bug报告生成器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ Bug报告生成器测试失败: {e}")
        traceback.print_exc()
        return False

def test_api_endpoints():
    """测试API端点（模拟测试）"""
    print("🔍 测试API端点...")
    
    try:
        from src.api.bug_detection.monitoring import router
        from src.api.bug_detection.reporting import router as reporting_router
        
        # 验证路由器存在
        assert router is not None, "监控路由器应该存在"
        assert reporting_router is not None, "报告路由器应该存在"
        
        # 验证路由器配置
        assert router.prefix == "/api/v1/bug-detection", "监控路由器前缀不正确"
        assert reporting_router.prefix == "/api/v1/bug-detection/reports", "报告路由器前缀不正确"
        
        print("✅ API端点测试通过")
        return True
        
    except Exception as e:
        print(f"❌ API端点测试失败: {e}")
        traceback.print_exc()
        return False

def test_integration():
    """集成测试"""
    print("🔍 执行集成测试...")
    
    try:
        from src.bug_detection.core.database_manager import DatabaseManager
        from src.bug_detection.feedback.bug_reporter import IntelligentBugReporter
        
        # 创建临时数据库
        temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_db.close()
        
        # 初始化组件
        db_manager = DatabaseManager(temp_db.name)
        bug_reporter = IntelligentBugReporter(db_manager)
        
        # 端到端测试：JavaScript错误 -> Bug报告 -> 数据库存储
        error_data = {
            'type': 'javascript',
            'message': 'TypeError: Cannot read property of undefined',
            'source': 'main.js',
            'line_number': 42,
            'page_url': 'http://localhost:8501/test_page',
            'session_id': 'test_session_123'
        }
        
        # 1. 保存JavaScript错误
        db_manager.save_js_error(
            error_data['session_id'],
            error_data['message'],
            error_data['page_url']
        )
        
        # 2. 生成Bug报告
        bug_report = bug_reporter.generate_enhanced_report(error_data)
        
        # 3. 验证完整流程
        assert bug_report['id'].startswith('BUG_'), "Bug ID格式不正确"
        assert bug_report['error']['type'] == 'javascript', "错误类型不匹配"
        
        # 4. 验证数据库中的数据
        bug_reports = db_manager.get_bug_reports(limit=1)
        assert len(bug_reports) >= 1, "数据库中应该有Bug报告"
        
        # 清理
        os.unlink(temp_db.name)
        
        print("✅ 集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        traceback.print_exc()
        return False

def run_all_tests():
    """运行所有阶段1测试"""
    print("🚀 开始阶段1验收测试")
    print("=" * 50)
    
    tests = [
        ("数据库管理器", test_database_manager),
        ("JavaScript监控器", test_javascript_monitor),
        ("API监控器", test_api_monitor),
        ("Bug报告生成器", test_bug_reporter),
        ("API端点", test_api_endpoints),
        ("集成测试", test_integration)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print("🎯 阶段1验收测试结果")
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"📊 成功率: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 阶段1验收测试全部通过！")
        print("✅ 基础监控系统已准备就绪")
        return True
    else:
        print(f"\n⚠️  有{failed}个测试失败，需要修复")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
