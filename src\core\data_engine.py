#!/usr/bin/env python3
"""
数据引擎集成模块

结合Polars高性能处理和SQLite持久化存储
"""

import logging
import sqlite3
import sys
import time
from datetime import date, datetime
from typing import Any, Dict, List, Optional, Tuple

import polars as pl

sys.path.append('src')

from core.database import DatabaseManager
from core.polars_engine import PolarsEngine
from data.models import LotteryRecord

logger = logging.getLogger(__name__)

class DataEngine:
    """数据引擎 - 集成Polars和SQLite"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        """
        初始化数据引擎
        
        Args:
            db_path: 数据库文件路径
        """
        self.polars_engine = PolarsEngine()
        self.db_manager = DatabaseManager(db_path)
        self.logger = logger
        
        # 性能统计
        self.performance_stats = {
            "queries_count": 0,
            "cache_hits": 0,
            "total_query_time": 0.0,
            "avg_query_time": 0.0
        }
        
    def load_data_from_records(self, records: List[LotteryRecord], save_to_db: bool = True) -> None:
        """
        从记录列表加载数据
        
        Args:
            records: 彩票记录列表
            save_to_db: 是否保存到数据库
        """
        start_time = time.time()
        
        # 加载到Polars引擎
        self.polars_engine.load_from_records(records)
        
        # 保存到数据库
        if save_to_db:
            self.db_manager.insert_records(records)
        
        load_time = time.time() - start_time
        self.logger.info(f"数据加载完成: {len(records)} 条记录，耗时 {load_time:.3f}秒")
    
    def load_data_from_database(self) -> None:
        """从数据库加载数据到Polars引擎"""
        start_time = time.time()

        # 从数据库查询所有数据
        with self.db_manager._get_connection() as conn:
            df = pl.read_database("SELECT * FROM lottery_records ORDER BY date", conn)

        # 转换为LotteryRecord对象
        records = []
        for row in df.iter_rows(named=True):
            record = LotteryRecord(
                period=row["period"],
                date=datetime.strptime(row["date"], "%Y-%m-%d").date(),
                numbers=row["numbers"],
                trial_numbers=row["trial_numbers"],
                draw_machine=row["draw_machine"],
                trial_machine=row["trial_machine"],
                sales_amount=row["sales_amount"],
                direct_prize=row["direct_prize"],
                group3_prize=row["group3_prize"],
                group6_prize=row["group6_prize"],
                unknown_field1=row["unknown_field1"],
                unknown_field2=row["unknown_field2"],
                unknown_field3=row["unknown_field3"]
            )
            records.append(record)

        # 加载到Polars引擎
        self.polars_engine.load_from_records(records)

        load_time = time.time() - start_time
        self.logger.info(f"从数据库加载数据完成: {len(records)} 条记录，耗时 {load_time:.3f}秒")
    
    def get_basic_stats(self, use_cache: bool = True, auto_refresh: bool = True) -> Dict[str, Any]:
        """
        获取基础统计信息

        Args:
            use_cache: 是否使用缓存
            auto_refresh: 是否自动检查并刷新数据

        Returns:
            基础统计信息
        """
        # 自动检查数据版本并刷新（如果需要）
        if auto_refresh:
            version_check = self.check_data_version()
            if version_check.get("needs_refresh", False):
                self.logger.info("检测到数据不一致，自动刷新数据")
                refresh_result = self.refresh_data()
                if not refresh_result["success"]:
                    self.logger.warning(f"自动数据刷新失败: {refresh_result['message']}")

        cache_key = "basic_stats"

        if use_cache:
            cached_result = self.db_manager.get_cached_statistics(cache_key)
            if cached_result:
                self.performance_stats["cache_hits"] += 1
                # 添加数据新鲜度信息
                cached_result["data_freshness"] = {
                    "auto_refresh_enabled": auto_refresh,
                    "cache_used": True,
                    "last_refresh_check": datetime.now().isoformat()
                }
                return cached_result

        start_time = time.time()

        # 使用Polars引擎计算统计
        polars_stats = self.polars_engine.get_basic_stats()

        # 补充数据库统计
        db_info = self.db_manager.get_database_info()

        result = {
            **polars_stats,
            "database_info": db_info,
            "data_freshness": {
                "auto_refresh_enabled": auto_refresh,
                "cache_used": False,
                "last_refresh_check": datetime.now().isoformat()
            },
            "computed_at": datetime.now().isoformat()
        }

        # 缓存结果
        if use_cache:
            self.db_manager.cache_statistics(cache_key, result, expires_hours=6)

        query_time = time.time() - start_time
        self._update_performance_stats(query_time)

        return result
    
    def get_frequency_analysis(self, position: str = "all", use_cache: bool = True) -> Dict[str, Any]:
        """
        获取频率分析
        
        Args:
            position: 分析位置
            use_cache: 是否使用缓存
            
        Returns:
            频率分析结果
        """
        cache_key = f"frequency_analysis_{position}"
        
        if use_cache:
            cached_result = self.db_manager.get_cached_statistics(cache_key)
            if cached_result:
                self.performance_stats["cache_hits"] += 1
                return cached_result
        
        start_time = time.time()
        
        # 优先使用数据库查询（对于频率统计更高效）
        if self.db_manager.get_records_count() > 0:
            result = self.db_manager.get_frequency_stats(position)
        else:
            # 回退到Polars引擎
            result = self.polars_engine.get_frequency_analysis(position)
        
        result["computed_at"] = datetime.now().isoformat()
        
        # 缓存结果
        if use_cache:
            self.db_manager.cache_statistics(cache_key, result, expires_hours=12)
        
        query_time = time.time() - start_time
        self._update_performance_stats(query_time)
        
        return result
    
    def get_sum_distribution(self, use_cache: bool = True) -> Dict[str, Any]:
        """
        获取和值分布分析
        
        Args:
            use_cache: 是否使用缓存
            
        Returns:
            和值分布结果
        """
        cache_key = "sum_distribution"
        
        if use_cache:
            cached_result = self.db_manager.get_cached_statistics(cache_key)
            if cached_result:
                self.performance_stats["cache_hits"] += 1
                return cached_result
        
        start_time = time.time()
        
        # 使用数据库查询
        if self.db_manager.get_records_count() > 0:
            result = self.db_manager.get_sum_distribution()
        else:
            # 回退到Polars引擎
            result = self.polars_engine.get_sum_value_distribution()
        
        result["computed_at"] = datetime.now().isoformat()
        
        # 缓存结果
        if use_cache:
            self.db_manager.cache_statistics(cache_key, result, expires_hours=12)
        
        query_time = time.time() - start_time
        self._update_performance_stats(query_time)
        
        return result
    
    def get_sales_analysis(self, use_cache: bool = True) -> Dict[str, Any]:
        """
        获取销售额分析
        
        Args:
            use_cache: 是否使用缓存
            
        Returns:
            销售额分析结果
        """
        cache_key = "sales_analysis"
        
        if use_cache:
            cached_result = self.db_manager.get_cached_statistics(cache_key)
            if cached_result:
                self.performance_stats["cache_hits"] += 1
                return cached_result
        
        start_time = time.time()
        
        # 使用数据库查询
        if self.db_manager.get_records_count() > 0:
            result = self.db_manager.get_sales_stats()
        else:
            # 回退到Polars引擎
            result = self.polars_engine.get_sales_analysis()
        
        result["computed_at"] = datetime.now().isoformat()
        
        # 缓存结果
        if use_cache:
            self.db_manager.cache_statistics(cache_key, result, expires_hours=6)
        
        query_time = time.time() - start_time
        self._update_performance_stats(query_time)
        
        return result
    
    def query_by_date_range(self, start_date: str, end_date: str, use_polars: bool = False) -> pl.DataFrame:
        """
        按日期范围查询数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            use_polars: 是否强制使用Polars引擎
            
        Returns:
            查询结果DataFrame
        """
        start_time = time.time()
        
        if use_polars or self.db_manager.get_records_count() == 0:
            # 使用Polars引擎
            result = self.polars_engine.query_by_date_range(start_date, end_date)
        else:
            # 使用数据库查询
            result = self.db_manager.query_by_date_range(start_date, end_date)
        
        query_time = time.time() - start_time
        self._update_performance_stats(query_time)
        
        return result
    
    def query_by_sum_range(self, min_sum: int, max_sum: int, use_polars: bool = False) -> pl.DataFrame:
        """
        按和值范围查询数据
        
        Args:
            min_sum: 最小和值
            max_sum: 最大和值
            use_polars: 是否强制使用Polars引擎
            
        Returns:
            查询结果DataFrame
        """
        start_time = time.time()
        
        if use_polars or self.db_manager.get_records_count() == 0:
            # 使用Polars引擎
            result = self.polars_engine.query_by_sum_range(min_sum, max_sum)
        else:
            # 使用数据库查询
            result = self.db_manager.query_by_sum_range(min_sum, max_sum)
        
        query_time = time.time() - start_time
        self._update_performance_stats(query_time)
        
        return result
    
    def get_recent_trends(self, days: int = 30, use_cache: bool = True) -> Dict[str, Any]:
        """
        获取最近趋势分析
        
        Args:
            days: 分析天数
            use_cache: 是否使用缓存
            
        Returns:
            趋势分析结果
        """
        cache_key = f"recent_trends_{days}"
        
        if use_cache:
            cached_result = self.db_manager.get_cached_statistics(cache_key)
            if cached_result:
                self.performance_stats["cache_hits"] += 1
                return cached_result
        
        start_time = time.time()
        
        # 使用Polars引擎进行趋势分析
        result = self.polars_engine.get_recent_trends(days)
        result["computed_at"] = datetime.now().isoformat()
        
        # 缓存结果
        if use_cache:
            self.db_manager.cache_statistics(cache_key, result, expires_hours=1)
        
        query_time = time.time() - start_time
        self._update_performance_stats(query_time)
        
        return result
    
    def optimize_performance(self) -> Dict[str, Any]:
        """
        性能优化操作
        
        Returns:
            优化结果
        """
        start_time = time.time()
        
        # 清理过期缓存
        expired_count = self.db_manager.clear_expired_cache()
        
        # 数据库优化（VACUUM）
        with self.db_manager._get_connection() as conn:
            conn.execute("VACUUM")
            conn.execute("ANALYZE")
        
        optimization_time = time.time() - start_time
        
        return {
            "expired_cache_cleared": expired_count,
            "optimization_time": optimization_time,
            "performance_stats": self.get_performance_stats()
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return {
            **self.performance_stats,
            "cache_hit_rate": (
                self.performance_stats["cache_hits"] / max(self.performance_stats["queries_count"], 1)
            ) * 100
        }
    
    def _update_performance_stats(self, query_time: float) -> None:
        """更新性能统计"""
        self.performance_stats["queries_count"] += 1
        self.performance_stats["total_query_time"] += query_time
        self.performance_stats["avg_query_time"] = (
            self.performance_stats["total_query_time"] / self.performance_stats["queries_count"]
        )
    
    def refresh_data(self) -> Dict[str, Any]:
        """
        刷新数据：从数据库重新加载数据到Polars引擎

        Returns:
            刷新结果信息
        """
        start_time = time.time()

        # 获取刷新前的状态
        old_count = len(self.polars_engine.df) if self.polars_engine.df is not None else 0
        old_latest_period = None
        if self.polars_engine.df is not None and len(self.polars_engine.df) > 0:
            old_latest_period = str(self.polars_engine.df.select("period").max().item())

        self.logger.info(f"开始刷新数据，当前Polars引擎记录数: {old_count}")

        try:
            # 重新从数据库加载数据
            self.load_data_from_database()

            # 获取刷新后的状态
            new_count = len(self.polars_engine.df) if self.polars_engine.df is not None else 0
            new_latest_period = None
            if self.polars_engine.df is not None and len(self.polars_engine.df) > 0:
                new_latest_period = str(self.polars_engine.df.select("period").max().item())

            # 清除相关缓存
            self._clear_related_cache()

            refresh_time = time.time() - start_time

            result = {
                "success": True,
                "message": "数据刷新成功",
                "old_count": old_count,
                "new_count": new_count,
                "old_latest_period": old_latest_period,
                "new_latest_period": new_latest_period,
                "records_added": new_count - old_count,
                "refresh_time": refresh_time,
                "timestamp": datetime.now().isoformat()
            }

            self.logger.info(f"数据刷新完成: {old_count} -> {new_count} 条记录，耗时 {refresh_time:.3f}秒")
            return result

        except Exception as e:
            refresh_time = time.time() - start_time
            error_msg = f"数据刷新失败: {str(e)}"
            self.logger.error(error_msg)

            return {
                "success": False,
                "message": error_msg,
                "old_count": old_count,
                "new_count": old_count,
                "records_added": 0,
                "refresh_time": refresh_time,
                "timestamp": datetime.now().isoformat()
            }

    def check_data_version(self) -> Dict[str, Any]:
        """
        检查数据版本：比较数据库与Polars引擎的数据是否一致

        Returns:
            版本检查结果
        """
        try:
            # 获取数据库状态
            db_count = self.db_manager.get_records_count()
            db_latest_records = self.db_manager.get_recent_records(1)
            db_latest_period = db_latest_records[0].period if db_latest_records else None

            # 获取Polars引擎状态
            polars_count = len(self.polars_engine.df) if self.polars_engine.df is not None else 0
            polars_latest_period = None
            if self.polars_engine.df is not None and len(self.polars_engine.df) > 0:
                polars_latest_period = str(self.polars_engine.df.select("period").max().item())

            # 检查是否需要刷新
            needs_refresh = (
                db_count != polars_count or
                db_latest_period != polars_latest_period
            )

            result = {
                "needs_refresh": needs_refresh,
                "database": {
                    "count": db_count,
                    "latest_period": db_latest_period
                },
                "polars_engine": {
                    "count": polars_count,
                    "latest_period": polars_latest_period
                },
                "differences": {
                    "count_diff": db_count - polars_count,
                    "period_match": db_latest_period == polars_latest_period
                },
                "timestamp": datetime.now().isoformat()
            }

            if needs_refresh:
                self.logger.info(f"检测到数据不一致: DB({db_count}, {db_latest_period}) vs Polars({polars_count}, {polars_latest_period})")
            else:
                self.logger.debug("数据版本检查通过，无需刷新")

            return result

        except Exception as e:
            error_msg = f"数据版本检查失败: {str(e)}"
            self.logger.error(error_msg)

            return {
                "needs_refresh": False,
                "error": error_msg,
                "timestamp": datetime.now().isoformat()
            }

    def _clear_related_cache(self) -> None:
        """清除相关缓存"""
        try:
            # 清除统计缓存
            cache_keys = [
                "basic_stats",
                "frequency_analysis_all",
                "sum_distribution",
                "sales_analysis"
            ]

            for cache_key in cache_keys:
                try:
                    # 删除缓存记录
                    with sqlite3.connect(self.db_manager.db_path) as conn:
                        cursor = conn.cursor()
                        cursor.execute("DELETE FROM statistics_cache WHERE cache_key = ?", (cache_key,))
                        conn.commit()
                except Exception as e:
                    self.logger.warning(f"清除缓存 {cache_key} 失败: {e}")

            self.logger.info("相关缓存已清除")

        except Exception as e:
            self.logger.warning(f"清除缓存时出错: {e}")

    def export_analysis_results(self, output_dir: str) -> Dict[str, str]:
        """
        导出分析结果

        Args:
            output_dir: 输出目录

        Returns:
            导出文件路径字典
        """
        # 使用Polars引擎的导出功能
        return self.polars_engine.export_analysis_results(output_dir)
