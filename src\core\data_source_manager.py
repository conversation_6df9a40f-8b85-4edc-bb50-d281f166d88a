#!/usr/bin/env python3
"""
数据源管理器

提供数据获取、缓存、更新等功能，支持多种数据源
"""

import asyncio
import logging
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

try:
    import requests
    from requests.adapters import HTTPAdapter
    from urllib3.util.retry import Retry
    HAS_REQUESTS = True
except ImportError:
    HAS_REQUESTS = False
    requests = None

sys.path.append('src')

from data.models import LotteryRecord
from data.parser import DataParser

logger = logging.getLogger(__name__)

class DataSourceManager:
    """数据源管理器"""
    
    def __init__(self, cache_dir: str = "data/cache", timeout: int = 30):
        """
        初始化数据源管理器
        
        Args:
            cache_dir: 缓存目录
            timeout: 请求超时时间（秒）
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.timeout = timeout
        self.logger = logger
        
        # 数据源配置
        self.data_sources = {
            "primary": {
                "url": "https://data.17500.cn/3d_asc.txt",
                "name": "17500彩票网",
                "priority": 1,
                "retry_count": 3,
                "retry_delay": 2
            },
            "backup": {
                "url": "https://backup.example.com/3d_data.txt",  # 备用数据源
                "name": "备用数据源",
                "priority": 2,
                "retry_count": 2,
                "retry_delay": 5
            }
        }
        
        # 创建HTTP会话
        self.session = self._create_session()
        
        # 数据解析器
        self.parser = DataParser()
        
        # 缓存状态
        self._cache_status = {
            "last_update": None,
            "cache_size": 0,
            "hit_count": 0,
            "miss_count": 0
        }
    
    def _create_session(self):
        """创建HTTP会话"""
        if not HAS_REQUESTS:
            return None

        session = requests.Session()

        # 配置重试策略
        try:
            retry_strategy = Retry(
                total=3,
                backoff_factor=1,
                status_forcelist=[429, 500, 502, 503, 504],
                allowed_methods=["HEAD", "GET", "OPTIONS"]
            )

            adapter = HTTPAdapter(max_retries=retry_strategy)
            session.mount("http://", adapter)
            session.mount("https://", adapter)
        except Exception as e:
            self.logger.warning(f"配置重试策略失败: {e}")

        # 设置请求头
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache'
        })

        return session
    
    def fetch_data(self, source_name: str = "primary", use_cache: bool = True) -> Optional[str]:
        """
        从指定数据源获取数据
        
        Args:
            source_name: 数据源名称
            use_cache: 是否使用缓存
            
        Returns:
            原始数据文本，失败返回None
        """
        if source_name not in self.data_sources:
            self.logger.error(f"未知数据源: {source_name}")
            return None
        
        source_config = self.data_sources[source_name]
        cache_file = self.cache_dir / f"{source_name}_data.txt"
        
        # 检查缓存
        if use_cache and cache_file.exists():
            cache_age = time.time() - cache_file.stat().st_mtime
            if cache_age < 3600:  # 缓存1小时内有效
                self.logger.info(f"使用缓存数据: {source_name}")
                self._cache_status["hit_count"] += 1
                return cache_file.read_text(encoding='utf-8')
        
        self._cache_status["miss_count"] += 1
        
        # 从网络获取数据
        if not HAS_REQUESTS or not self.session:
            self.logger.error("requests库不可用，无法获取网络数据")
            return None

        for attempt in range(source_config["retry_count"]):
            try:
                self.logger.info(f"从 {source_config['name']} 获取数据 (尝试 {attempt + 1}/{source_config['retry_count']})")

                response = self.session.get(
                    source_config["url"],
                    timeout=self.timeout,
                    stream=True
                )
                
                if response.status_code == 200:
                    data = response.text
                    
                    # 验证数据格式
                    if self._validate_data(data):
                        # 保存到缓存
                        cache_file.write_text(data, encoding='utf-8')
                        self._cache_status["last_update"] = datetime.now()
                        self._cache_status["cache_size"] = len(data)
                        
                        self.logger.info(f"成功获取数据: {len(data)} 字符")
                        return data
                    else:
                        self.logger.warning(f"数据格式验证失败: {source_name}")
                
                elif response.status_code == 429:
                    # 遇到限流，增加延迟
                    delay = source_config["retry_delay"] * (2 ** attempt)
                    self.logger.warning(f"遇到限流 (429)，等待 {delay} 秒后重试")
                    time.sleep(delay)
                    continue
                
                else:
                    self.logger.warning(f"HTTP错误: {response.status_code}")
                    
            except Exception as e:
                error_msg = str(e)
                if "timeout" in error_msg.lower():
                    self.logger.warning(f"请求超时: {source_name}")
                elif "connection" in error_msg.lower():
                    self.logger.warning(f"连接错误: {source_name}")
                else:
                    self.logger.error(f"获取数据失败: {e}")
            
            # 重试前等待
            if attempt < source_config["retry_count"] - 1:
                time.sleep(source_config["retry_delay"])
        
        self.logger.error(f"所有尝试失败: {source_name}")
        return None
    
    def _validate_data(self, data: str) -> bool:
        """
        验证数据格式
        
        Args:
            data: 原始数据
            
        Returns:
            是否有效
        """
        if not data or len(data) < 100:
            return False
        
        lines = data.strip().split('\n')
        if len(lines) < 10:  # 至少应该有10行数据
            return False
        
        # 检查第一行是否符合期号格式
        first_line = lines[0].strip()
        if not first_line or len(first_line.split()) < 3:
            return False
        
        return True
    
    def fetch_with_fallback(self, use_cache: bool = True) -> Optional[str]:
        """
        使用备用机制获取数据
        
        Args:
            use_cache: 是否使用缓存
            
        Returns:
            原始数据文本，失败返回None
        """
        # 按优先级排序数据源
        sorted_sources = sorted(
            self.data_sources.items(),
            key=lambda x: x[1]["priority"]
        )
        
        for source_name, source_config in sorted_sources:
            self.logger.info(f"尝试数据源: {source_config['name']}")
            
            data = self.fetch_data(source_name, use_cache)
            if data:
                return data
            
            self.logger.warning(f"数据源失败: {source_config['name']}")
        
        self.logger.error("所有数据源都失败了")
        return None
    
    def parse_data(self, raw_data: str) -> List[LotteryRecord]:
        """
        解析原始数据

        Args:
            raw_data: 原始数据文本

        Returns:
            解析后的记录列表
        """
        try:
            result = self.parser.parse_data(raw_data)
            # 如果返回的是元组，取第一个元素（记录列表）
            if isinstance(result, tuple):
                return result[0]
            return result
        except Exception as e:
            self.logger.error(f"数据解析失败: {e}")
            return []
    
    def get_latest_data(self, use_cache: bool = True, parse: bool = True) -> Union[str, List[LotteryRecord], None]:
        """
        获取最新数据
        
        Args:
            use_cache: 是否使用缓存
            parse: 是否解析数据
            
        Returns:
            原始数据或解析后的记录列表
        """
        raw_data = self.fetch_with_fallback(use_cache)
        
        if not raw_data:
            return None
        
        if parse:
            return self.parse_data(raw_data)
        else:
            return raw_data
    
    def clear_cache(self) -> int:
        """
        清理缓存
        
        Returns:
            清理的文件数量
        """
        cleared_count = 0
        
        for cache_file in self.cache_dir.glob("*_data.txt"):
            try:
                cache_file.unlink()
                cleared_count += 1
            except Exception as e:
                self.logger.warning(f"删除缓存文件失败: {cache_file}, {e}")
        
        # 重置缓存状态
        self._cache_status = {
            "last_update": None,
            "cache_size": 0,
            "hit_count": 0,
            "miss_count": 0
        }
        
        self.logger.info(f"清理了 {cleared_count} 个缓存文件")
        return cleared_count
    
    def get_cache_status(self) -> Dict[str, Any]:
        """获取缓存状态"""
        cache_files = list(self.cache_dir.glob("*_data.txt"))
        total_size = sum(f.stat().st_size for f in cache_files if f.exists())
        
        return {
            **self._cache_status,
            "cache_files": len(cache_files),
            "total_cache_size_mb": round(total_size / 1024 / 1024, 2),
            "cache_hit_rate": (
                self._cache_status["hit_count"] / 
                (self._cache_status["hit_count"] + self._cache_status["miss_count"])
                if (self._cache_status["hit_count"] + self._cache_status["miss_count"]) > 0 else 0
            )
        }
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        status = {"healthy": True, "checks": []}
        
        # 检查缓存目录
        if self.cache_dir.exists() and self.cache_dir.is_dir():
            status["checks"].append({"name": "cache_directory", "status": "ok"})
        else:
            status["healthy"] = False
            status["checks"].append({"name": "cache_directory", "status": "error", "error": "Cache directory not accessible"})
        
        # 检查网络连接
        if HAS_REQUESTS and self.session:
            try:
                test_response = self.session.get("https://www.baidu.com", timeout=5)
                if test_response.status_code == 200:
                    status["checks"].append({"name": "network_connectivity", "status": "ok"})
                else:
                    status["checks"].append({"name": "network_connectivity", "status": "warning", "message": f"HTTP {test_response.status_code}"})
            except Exception as e:
                status["checks"].append({"name": "network_connectivity", "status": "error", "error": str(e)})
        else:
            status["checks"].append({"name": "network_connectivity", "status": "error", "error": "requests库不可用"})

        # 检查数据源可用性
        if HAS_REQUESTS and self.session:
            for source_name, source_config in self.data_sources.items():
                try:
                    response = self.session.head(source_config["url"], timeout=10)
                    if response.status_code == 200:
                        status["checks"].append({"name": f"data_source_{source_name}", "status": "ok"})
                    else:
                        status["checks"].append({
                            "name": f"data_source_{source_name}",
                            "status": "warning",
                            "message": f"HTTP {response.status_code}"
                        })
                except Exception as e:
                    status["checks"].append({
                        "name": f"data_source_{source_name}",
                        "status": "error",
                        "error": str(e)
                    })
        else:
            for source_name, source_config in self.data_sources.items():
                status["checks"].append({
                    "name": f"data_source_{source_name}",
                    "status": "error",
                    "error": "requests库不可用"
                })
        
        return status
    
    def close(self):
        """关闭数据源管理器"""
        if self.session:
            self.session.close()
        self.logger.info("数据源管理器已关闭")
