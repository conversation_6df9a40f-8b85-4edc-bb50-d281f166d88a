#!/usr/bin/env python3
"""
测试完整的数据处理流程
"""

import sys
sys.path.append('src')

from data.collector import LotteryDataCollector
from data.parser import DataParser
from data.cleaner import DataCleaner
from data.formatter import DataFormatter, export_lottery_data
from pathlib import Path

def test_complete_workflow():
    """测试完整的数据处理工作流程"""
    print("开始测试完整数据处理工作流程...")
    
    try:
        # 1. 读取已采集的数据
        print("\n1. 读取原始数据...")
        data_file = Path('data/raw/3d_data_20250714_144231.txt')
        
        if not data_file.exists():
            print("❌ 原始数据文件不存在，请先运行数据采集")
            return False
        
        with open(data_file, 'r', encoding='utf-8') as f:
            raw_data = f.read()
        
        print(f"✅ 原始数据读取成功，长度: {len(raw_data)} 字符")
        
        # 2. 数据清洗
        print("\n2. 数据清洗...")
        cleaner = DataCleaner()
        cleaned_lines = cleaner.clean_raw_data(raw_data)
        cleaning_stats = cleaner.get_cleaning_statistics()
        
        print(f"✅ 数据清洗完成")
        print(f"   清洗行数: {cleaning_stats['cleaned_lines']}")
        print(f"   错误行数: {cleaning_stats['error_lines']}")
        print(f"   成功率: {cleaning_stats['success_rate']:.1f}%")
        
        # 3. 数据解析
        print("\n3. 数据解析...")
        parser = DataParser()
        records, quality_report = parser.parse_data(raw_data)
        
        print(f"✅ 数据解析完成")
        print(f"   有效记录: {len(records)}")
        print(f"   质量评分: {quality_report.quality_score}")
        
        if len(records) == 0:
            print("❌ 没有有效记录，停止处理")
            return False
        
        # 4. 数据统计
        print("\n4. 数据统计...")
        stats = parser.get_statistics(records)
        
        print(f"✅ 统计信息:")
        print(f"   记录总数: {stats['total_records']}")
        print(f"   日期范围: {stats['date_range']['start']} 到 {stats['date_range']['end']}")
        print(f"   和值范围: {stats['sum_value_stats']['min']}-{stats['sum_value_stats']['max']}")
        print(f"   最常见数字: {stats['most_frequent_digit']}")
        
        # 5. 数据格式化和导出
        print("\n5. 数据格式化和导出...")
        formatter = DataFormatter()
        
        # 创建摘要报告
        summary = formatter.create_summary_report(records, quality_report)
        
        # 导出多种格式
        export_results = export_lottery_data(
            records,
            output_dir="data/processed",
            formats=['json', 'csv', 'txt']
        )
        
        print(f"✅ 数据导出完成:")
        for fmt, success in export_results.items():
            status = "✅" if success else "❌"
            print(f"   {fmt.upper()}: {status}")
        
        # 6. 显示最新数据
        print(f"\n6. 最新数据预览:")
        print(f"   最新5条记录:")
        for record in records[-5:]:
            print(f"     {record.period} {record.date} {record.numbers}")
        
        # 7. 保存摘要报告
        print(f"\n7. 保存摘要报告...")
        import json
        summary_file = Path('data/processed/summary_report.json')
        summary_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 摘要报告已保存到: {summary_file}")
        
        print(f"\n🎉 完整数据处理工作流程测试成功！")
        print(f"📊 处理结果摘要:")
        print(f"   - 原始数据: {len(raw_data)} 字符")
        print(f"   - 有效记录: {len(records)} 条")
        print(f"   - 数据质量: {quality_report.quality_score}")
        print(f"   - 导出格式: {len([f for f, s in export_results.items() if s])} 种")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_complete_workflow()
