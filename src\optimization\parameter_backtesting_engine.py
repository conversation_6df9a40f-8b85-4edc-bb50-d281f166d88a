#!/usr/bin/env python3
"""
参数回测引擎
Parameter Backtesting Engine

寻找能预测到实际结果的最优参数
"""

import asyncio
import json
import logging
import os
import sys
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd

sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from core.unified_prediction_storage import PredictionRecord


@dataclass
class OptimizationResult:
    """优化结果"""
    optimal_parameters: Dict[str, Any]
    best_score: float
    optimization_history: List[Dict[str, Any]]
    convergence_info: Dict[str, Any]
    validation_metrics: Dict[str, float]


@dataclass
class BacktestingResult:
    """回测结果"""
    model_name: str
    target_period: str
    actual_result: str
    optimal_parameters: Dict[str, Any]
    optimization_process: Dict[str, OptimizationResult]
    validation_results: Dict[str, Any]
    improvement_metrics: Dict[str, float]
    parameter_sensitivity: Dict[str, float]
    confidence_score: float
    analysis_timestamp: datetime


class BaseOptimizer(ABC):
    """基础优化器抽象类"""
    
    @abstractmethod
    async def optimize(self, model_name: str, target_period: str, actual_result: str,
                      historical_data: pd.DataFrame, search_space: Dict[str, Any]) -> OptimizationResult:
        """执行参数优化"""
        pass


class GridSearchOptimizer(BaseOptimizer):
    """网格搜索优化器"""
    
    def __init__(self, max_iterations: int = 100):
        self.max_iterations = max_iterations
        self.logger = logging.getLogger(__name__)
    
    async def optimize(self, model_name: str, target_period: str, actual_result: str,
                      historical_data: pd.DataFrame, search_space: Dict[str, Any]) -> OptimizationResult:
        """网格搜索优化"""
        self.logger.info(f"开始网格搜索优化: {model_name}")
        
        best_score = -np.inf
        best_params = {}
        optimization_history = []
        
        # 生成参数网格
        param_grid = self._generate_parameter_grid(search_space)
        
        for i, params in enumerate(param_grid[:self.max_iterations]):
            try:
                # 模拟预测并评估
                score = await self._evaluate_parameters(
                    model_name, params, target_period, actual_result, historical_data
                )
                
                optimization_history.append({
                    'iteration': i,
                    'parameters': params.copy(),
                    'score': score,
                    'timestamp': datetime.now().isoformat()
                })
                
                if score > best_score:
                    best_score = score
                    best_params = params.copy()
                
            except Exception as e:
                self.logger.error(f"参数评估失败: {e}")
        
        return OptimizationResult(
            optimal_parameters=best_params,
            best_score=best_score,
            optimization_history=optimization_history,
            convergence_info={'method': 'grid_search', 'iterations': len(optimization_history)},
            validation_metrics={'best_accuracy': best_score}
        )
    
    def _generate_parameter_grid(self, search_space: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成参数网格"""
        import itertools
        
        param_names = list(search_space.keys())
        param_values = []
        
        for param_name, param_range in search_space.items():
            if isinstance(param_range, (list, tuple)):
                param_values.append(param_range)
            elif isinstance(param_range, range):
                param_values.append(list(param_range))
            else:
                param_values.append([param_range])
        
        # 生成所有参数组合
        param_combinations = list(itertools.product(*param_values))
        
        return [dict(zip(param_names, combo)) for combo in param_combinations]
    
    async def _evaluate_parameters(self, model_name: str, parameters: Dict[str, Any],
                                  target_period: str, actual_result: str,
                                  historical_data: pd.DataFrame) -> float:
        """评估参数组合"""
        # 这里应该实际运行模型并计算准确率
        # 为了演示，我们使用模拟评估
        
        # 模拟准确率计算（基于参数的合理性）
        base_accuracy = 0.5
        
        # 根据参数调整准确率
        for param_name, param_value in parameters.items():
            if 'weight' in param_name.lower():
                # 权重参数，期望在0.3-0.7之间
                if 0.3 <= param_value <= 0.7:
                    base_accuracy += 0.1
                else:
                    base_accuracy -= 0.05
            elif 'window' in param_name.lower():
                # 窗口参数，期望在10-50之间
                if 10 <= param_value <= 50:
                    base_accuracy += 0.05
                else:
                    base_accuracy -= 0.02
        
        # 添加随机噪声
        noise = np.random.normal(0, 0.1)
        return max(0.0, min(1.0, base_accuracy + noise))


class BayesianOptimizer(BaseOptimizer):
    """贝叶斯优化器"""
    
    def __init__(self, max_iterations: int = 50):
        self.max_iterations = max_iterations
        self.logger = logging.getLogger(__name__)
    
    async def optimize(self, model_name: str, target_period: str, actual_result: str,
                      historical_data: pd.DataFrame, search_space: Dict[str, Any]) -> OptimizationResult:
        """贝叶斯优化"""
        self.logger.info(f"开始贝叶斯优化: {model_name}")
        
        # 简化的贝叶斯优化实现
        best_score = -np.inf
        best_params = {}
        optimization_history = []
        
        for i in range(self.max_iterations):
            # 采样参数（简化实现）
            params = self._sample_parameters(search_space, i)
            
            try:
                score = await self._evaluate_parameters(
                    model_name, params, target_period, actual_result, historical_data
                )
                
                optimization_history.append({
                    'iteration': i,
                    'parameters': params.copy(),
                    'score': score,
                    'timestamp': datetime.now().isoformat()
                })
                
                if score > best_score:
                    best_score = score
                    best_params = params.copy()
                
            except Exception as e:
                self.logger.error(f"贝叶斯优化评估失败: {e}")
        
        return OptimizationResult(
            optimal_parameters=best_params,
            best_score=best_score,
            optimization_history=optimization_history,
            convergence_info={'method': 'bayesian', 'iterations': len(optimization_history)},
            validation_metrics={'best_accuracy': best_score}
        )
    
    def _sample_parameters(self, search_space: Dict[str, Any], iteration: int) -> Dict[str, Any]:
        """采样参数"""
        params = {}
        
        for param_name, param_range in search_space.items():
            if isinstance(param_range, tuple) and len(param_range) == 2:
                # 连续参数
                low, high = param_range
                params[param_name] = np.random.uniform(low, high)
            elif isinstance(param_range, list):
                # 离散参数
                params[param_name] = np.random.choice(param_range)
            elif isinstance(param_range, range):
                # 整数范围
                params[param_name] = np.random.choice(list(param_range))
            else:
                params[param_name] = param_range
        
        return params
    
    async def _evaluate_parameters(self, model_name: str, parameters: Dict[str, Any],
                                  target_period: str, actual_result: str,
                                  historical_data: pd.DataFrame) -> float:
        """评估参数组合"""
        # 与网格搜索相同的评估逻辑
        base_accuracy = 0.5
        
        for param_name, param_value in parameters.items():
            if 'weight' in param_name.lower():
                if 0.3 <= param_value <= 0.7:
                    base_accuracy += 0.1
                else:
                    base_accuracy -= 0.05
            elif 'window' in param_name.lower():
                if 10 <= param_value <= 50:
                    base_accuracy += 0.05
                else:
                    base_accuracy -= 0.02
        
        noise = np.random.normal(0, 0.1)
        return max(0.0, min(1.0, base_accuracy + noise))


class GeneticAlgorithmOptimizer(BaseOptimizer):
    """遗传算法优化器"""
    
    def __init__(self, population_size: int = 20, generations: int = 30):
        self.population_size = population_size
        self.generations = generations
        self.logger = logging.getLogger(__name__)
    
    async def optimize(self, model_name: str, target_period: str, actual_result: str,
                      historical_data: pd.DataFrame, search_space: Dict[str, Any]) -> OptimizationResult:
        """遗传算法优化"""
        self.logger.info(f"开始遗传算法优化: {model_name}")
        
        # 简化的遗传算法实现
        best_score = -np.inf
        best_params = {}
        optimization_history = []
        
        # 初始化种群
        population = [self._random_individual(search_space) for _ in range(self.population_size)]
        
        for generation in range(self.generations):
            # 评估种群
            fitness_scores = []
            for individual in population:
                try:
                    score = await self._evaluate_parameters(
                        model_name, individual, target_period, actual_result, historical_data
                    )
                    fitness_scores.append(score)
                    
                    if score > best_score:
                        best_score = score
                        best_params = individual.copy()
                        
                except Exception as e:
                    fitness_scores.append(0.0)
                    self.logger.error(f"遗传算法评估失败: {e}")
            
            optimization_history.append({
                'generation': generation,
                'best_score': max(fitness_scores),
                'avg_score': np.mean(fitness_scores),
                'timestamp': datetime.now().isoformat()
            })
            
            # 选择、交叉、变异（简化实现）
            population = self._evolve_population(population, fitness_scores, search_space)
        
        return OptimizationResult(
            optimal_parameters=best_params,
            best_score=best_score,
            optimization_history=optimization_history,
            convergence_info={'method': 'genetic_algorithm', 'generations': self.generations},
            validation_metrics={'best_accuracy': best_score}
        )
    
    def _random_individual(self, search_space: Dict[str, Any]) -> Dict[str, Any]:
        """生成随机个体"""
        individual = {}
        
        for param_name, param_range in search_space.items():
            if isinstance(param_range, tuple) and len(param_range) == 2:
                low, high = param_range
                individual[param_name] = np.random.uniform(low, high)
            elif isinstance(param_range, list):
                individual[param_name] = np.random.choice(param_range)
            elif isinstance(param_range, range):
                individual[param_name] = np.random.choice(list(param_range))
            else:
                individual[param_name] = param_range
        
        return individual
    
    def _evolve_population(self, population: List[Dict[str, Any]], 
                          fitness_scores: List[float], 
                          search_space: Dict[str, Any]) -> List[Dict[str, Any]]:
        """进化种群"""
        # 简化的进化操作
        new_population = []
        
        # 保留最好的个体
        best_indices = np.argsort(fitness_scores)[-self.population_size//4:]
        for idx in best_indices:
            new_population.append(population[idx].copy())
        
        # 生成新个体
        while len(new_population) < self.population_size:
            new_population.append(self._random_individual(search_space))
        
        return new_population
    
    async def _evaluate_parameters(self, model_name: str, parameters: Dict[str, Any],
                                  target_period: str, actual_result: str,
                                  historical_data: pd.DataFrame) -> float:
        """评估参数组合"""
        # 与其他优化器相同的评估逻辑
        base_accuracy = 0.5
        
        for param_name, param_value in parameters.items():
            if 'weight' in param_name.lower():
                if 0.3 <= param_value <= 0.7:
                    base_accuracy += 0.1
                else:
                    base_accuracy -= 0.05
            elif 'window' in param_name.lower():
                if 10 <= param_value <= 50:
                    base_accuracy += 0.05
                else:
                    base_accuracy -= 0.02
        
        noise = np.random.normal(0, 0.1)
        return max(0.0, min(1.0, base_accuracy + noise))


class ParameterBacktestingEngine:
    """参数回测引擎"""
    
    def __init__(self):
        """初始化参数回测引擎"""
        self.logger = logging.getLogger(__name__)
        
        # 优化器注册表
        self.optimizers = {
            'grid_search': GridSearchOptimizer(),
            'bayesian_optimization': BayesianOptimizer(),
            'genetic_algorithm': GeneticAlgorithmOptimizer()
        }
    
    async def find_optimal_parameters(self, model_name: str, target_period: str, 
                                    actual_result: str, historical_data: pd.DataFrame) -> BacktestingResult:
        """
        寻找最优参数配置
        
        Args:
            model_name: 模型名称
            target_period: 目标期号
            actual_result: 实际结果
            historical_data: 历史数据
            
        Returns:
            回测结果
        """
        try:
            self.logger.info(f"开始参数回测: {model_name} - {target_period}")
            
            # 定义搜索空间
            search_space = self._define_search_space(model_name)
            
            # 执行多种优化策略
            optimization_results = {}
            for method_name, optimizer in self.optimizers.items():
                try:
                    result = await optimizer.optimize(
                        model_name, target_period, actual_result, historical_data, search_space
                    )
                    optimization_results[method_name] = result
                except Exception as e:
                    self.logger.error(f"优化方法 {method_name} 失败: {e}")
            
            # 选择最佳参数
            best_parameters = self._select_best_parameters(optimization_results)
            
            # 验证参数有效性
            validation_results = await self._validate_parameters(
                model_name, best_parameters, target_period, actual_result, historical_data
            )
            
            # 计算改进指标
            improvement_metrics = self._calculate_improvement_metrics(validation_results)
            
            # 参数敏感性分析
            parameter_sensitivity = self._analyze_parameter_sensitivity(optimization_results)
            
            # 计算置信度
            confidence_score = self._calculate_confidence_score(optimization_results, validation_results)
            
            return BacktestingResult(
                model_name=model_name,
                target_period=target_period,
                actual_result=actual_result,
                optimal_parameters=best_parameters,
                optimization_process=optimization_results,
                validation_results=validation_results,
                improvement_metrics=improvement_metrics,
                parameter_sensitivity=parameter_sensitivity,
                confidence_score=confidence_score,
                analysis_timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"参数回测失败: {e}")
            raise
    
    def _define_search_space(self, model_name: str) -> Dict[str, Any]:
        """定义参数搜索空间"""
        base_spaces = {
            'markov_enhanced': {
                'order': range(1, 6),
                'smoothing_factor': (0.01, 0.5),
                'window_size': range(10, 100),
                'weight_decay': (0.1, 0.9)
            },
            'deep_learning_cnn_lstm': {
                'learning_rate': (0.0001, 0.01),
                'batch_size': [16, 32, 64, 128],
                'hidden_units': range(50, 300),
                'dropout_rate': (0.1, 0.5),
                'sequence_length': range(10, 50)
            },
            'trend_analyzer': {
                'trend_window': range(5, 30),
                'trend_weight': (0.1, 0.8),
                'momentum_factor': (0.1, 0.9),
                'volatility_threshold': (0.1, 0.5)
            },
            'intelligent_fusion': {
                'fusion_method': ['weighted_average', 'voting', 'stacking'],
                'confidence_threshold': (0.1, 0.9),
                'diversity_weight': (0.1, 0.5),
                'performance_weight': (0.3, 0.8)
            }
        }
        
        return base_spaces.get(model_name, {
            'default_weight': (0.1, 0.9),
            'default_threshold': (0.1, 0.9)
        })
    
    def _select_best_parameters(self, optimization_results: Dict[str, OptimizationResult]) -> Dict[str, Any]:
        """选择最佳参数"""
        if not optimization_results:
            return {}
        
        # 找到最佳结果
        best_method = max(optimization_results.keys(), 
                         key=lambda k: optimization_results[k].best_score)
        
        return optimization_results[best_method].optimal_parameters
    
    async def _validate_parameters(self, model_name: str, parameters: Dict[str, Any],
                                  target_period: str, actual_result: str,
                                  historical_data: pd.DataFrame) -> Dict[str, Any]:
        """验证参数有效性"""
        validation_results = {
            'parameter_validation': True,
            'cross_validation_score': 0.75,  # 模拟交叉验证分数
            'stability_score': 0.8,  # 模拟稳定性分数
            'generalization_score': 0.7  # 模拟泛化分数
        }
        
        return validation_results
    
    def _calculate_improvement_metrics(self, validation_results: Dict[str, Any]) -> Dict[str, float]:
        """计算改进指标"""
        return {
            'accuracy_improvement': validation_results.get('cross_validation_score', 0.0) - 0.5,
            'stability_improvement': validation_results.get('stability_score', 0.0) - 0.6,
            'generalization_improvement': validation_results.get('generalization_score', 0.0) - 0.5
        }
    
    def _analyze_parameter_sensitivity(self, optimization_results: Dict[str, OptimizationResult]) -> Dict[str, float]:
        """分析参数敏感性"""
        sensitivity = {}
        
        # 基于优化历史分析参数敏感性
        for method_name, result in optimization_results.items():
            if result.optimization_history:
                # 简化的敏感性分析
                for param_name in result.optimal_parameters.keys():
                    sensitivity[param_name] = np.random.uniform(0.1, 0.9)  # 模拟敏感性分数
        
        return sensitivity
    
    def _calculate_confidence_score(self, optimization_results: Dict[str, OptimizationResult],
                                   validation_results: Dict[str, Any]) -> float:
        """计算置信度分数"""
        if not optimization_results:
            return 0.0
        
        # 基于优化结果的一致性
        best_scores = [result.best_score for result in optimization_results.values()]
        consistency = 1.0 - np.std(best_scores) if len(best_scores) > 1 else 1.0
        
        # 基于验证结果
        validation_score = validation_results.get('cross_validation_score', 0.0)
        
        # 综合置信度
        confidence = (consistency * 0.6 + validation_score * 0.4)
        return max(0.0, min(1.0, confidence))


if __name__ == "__main__":
    # 测试代码
    async def test_backtesting():
        engine = ParameterBacktestingEngine()
        
        # 创建模拟历史数据
        historical_data = pd.DataFrame({
            'period': [f"202519{i:02d}" for i in range(1, 21)],
            'numbers': [f"{i%10}{(i+1)%10}{(i+2)%10}" for i in range(1, 21)]
        })
        
        # 执行回测
        result = await engine.find_optimal_parameters(
            "test_model", "2025194", "123", historical_data
        )
        
        print("参数回测结果:")
        print(f"最优参数: {result.optimal_parameters}")
        print(f"置信度: {result.confidence_score:.3f}")
        print(f"改进指标: {result.improvement_metrics}")
    
    # 运行测试
    asyncio.run(test_backtesting())
