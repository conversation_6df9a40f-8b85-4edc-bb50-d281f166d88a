"""
A/B测试深度管理页面
提供实验设计向导、实时结果监控、统计显著性分析
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# 导入A/B测试模块
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

try:
    from model_library.optimization.ab_testing import AdaptiveABTestingFramework, AllocationStrategy
    from model_library.optimization.experiment_config import ExperimentConfigManager, ExperimentConfig, ExperimentArm
except ImportError as e:
    st.error(f"导入A/B测试模块失败: {e}")
    st.stop()


def initialize_session_state():
    """初始化会话状态"""
    if 'ab_framework' not in st.session_state:
        st.session_state.ab_framework = AdaptiveABTestingFramework()
    
    if 'config_manager' not in st.session_state:
        st.session_state.config_manager = ExperimentConfigManager()
    
    if 'current_experiment_id' not in st.session_state:
        st.session_state.current_experiment_id = None
    
    if 'experiment_arms' not in st.session_state:
        st.session_state.experiment_arms = []


def show_experiment_design_wizard():
    """显示实验设计向导"""
    st.subheader("🧙‍♂️ 实验设计向导")
    
    with st.form("experiment_design_form"):
        # 基本信息
        st.markdown("#### 📋 基本信息")
        col1, col2 = st.columns(2)
        
        with col1:
            exp_name = st.text_input("实验名称", placeholder="例如：超参数优化实验")
            exp_description = st.text_area("实验描述", placeholder="描述实验目的和预期效果")
        
        with col2:
            target_metric = st.selectbox(
                "主要指标",
                options=["accuracy", "precision", "recall", "f1_score", "loss"],
                index=0
            )
            
            allocation_strategy = st.selectbox(
                "分配策略",
                options=["thompson_sampling", "ucb", "epsilon_greedy", "equal"],
                format_func=lambda x: {
                    "thompson_sampling": "汤普森采样",
                    "ucb": "置信上界",
                    "epsilon_greedy": "ε-贪心",
                    "equal": "均等分配"
                }[x]
            )
        
        # 实验分支设计
        st.markdown("#### 🔀 实验分支设计")
        
        # 控制组
        st.markdown("**控制组（基线）**")
        col1, col2 = st.columns(2)
        
        with col1:
            control_name = st.text_input("控制组名称", value="控制组")
            control_desc = st.text_input("控制组描述", value="当前配置")
        
        with col2:
            control_lr = st.number_input("学习率", value=0.001, step=0.0001, format="%.4f", key="control_lr")
            control_batch = st.selectbox("批次大小", options=[32, 64, 128], index=1, key="control_batch")
        
        # 实验组
        st.markdown("**实验组**")
        num_variants = st.slider("实验组数量", min_value=1, max_value=5, value=2)
        
        variants = []
        for i in range(num_variants):
            st.markdown(f"**实验组 {i+1}**")
            col1, col2 = st.columns(2)
            
            with col1:
                var_name = st.text_input(f"名称", value=f"实验组{i+1}", key=f"var_name_{i}")
                var_desc = st.text_input(f"描述", value=f"变体配置{i+1}", key=f"var_desc_{i}")
            
            with col2:
                var_lr = st.number_input(f"学习率", value=0.001*(i+2), step=0.0001, format="%.4f", key=f"var_lr_{i}")
                var_batch = st.selectbox(f"批次大小", options=[32, 64, 128], index=(i+1)%3, key=f"var_batch_{i}")
            
            variants.append({
                "name": var_name,
                "description": var_desc,
                "learning_rate": var_lr,
                "batch_size": var_batch
            })
        
        # 实验参数
        st.markdown("#### ⚙️ 实验参数")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            min_sample_size = st.number_input("最小样本量", min_value=50, value=200, step=50)
            significance_level = st.slider("显著性水平", 0.01, 0.10, 0.05, step=0.01)
        
        with col2:
            max_duration = st.number_input("最大持续天数", min_value=1, value=14, step=1)
            statistical_power = st.slider("统计功效", 0.70, 0.95, 0.80, step=0.05)
        
        with col3:
            traffic_allocation = st.slider("流量分配比例", 0.1, 1.0, 1.0, step=0.1)
            min_effect_size = st.slider("最小可检测效应", 0.01, 0.20, 0.05, step=0.01)
        
        # 提交按钮
        submitted = st.form_submit_button("🚀 创建实验", type="primary")
        
        if submitted:
            if exp_name and control_name:
                # 创建实验分支
                arms = [
                    {
                        "arm_id": "control",
                        "name": control_name,
                        "description": control_desc,
                        "configuration": {
                            "learning_rate": control_lr,
                            "batch_size": control_batch
                        }
                    }
                ]
                
                for i, variant in enumerate(variants):
                    arms.append({
                        "arm_id": f"variant_{i+1}",
                        "name": variant["name"],
                        "description": variant["description"],
                        "configuration": {
                            "learning_rate": variant["learning_rate"],
                            "batch_size": variant["batch_size"]
                        }
                    })
                
                # 创建实验
                success = st.session_state.ab_framework.create_experiment(
                    experiment_id=f"exp_{int(datetime.now().timestamp())}",
                    name=exp_name,
                    description=exp_description,
                    arms=arms,
                    allocation_strategy=AllocationStrategy(allocation_strategy),
                    target_metric=target_metric,
                    min_sample_size=min_sample_size,
                    max_duration_days=max_duration,
                    significance_level=significance_level
                )
                
                if success:
                    st.success("✅ 实验创建成功！")
                    st.rerun()
                else:
                    st.error("❌ 实验创建失败")
            else:
                st.error("请填写必要的实验信息")


def show_experiment_monitoring():
    """显示实验监控"""
    st.subheader("📊 实验监控")
    
    # 获取实验列表
    experiments = st.session_state.ab_framework.list_experiments()
    
    if not experiments:
        st.info("暂无实验，请先创建实验")
        return
    
    # 实验选择
    exp_options = {exp["experiment_id"]: f"{exp['name']} ({exp['status']})" 
                   for exp in experiments}
    
    selected_exp_id = st.selectbox(
        "选择实验",
        options=list(exp_options.keys()),
        format_func=lambda x: exp_options[x]
    )
    
    if not selected_exp_id:
        return
    
    st.session_state.current_experiment_id = selected_exp_id
    
    # 获取实验状态
    exp_status = st.session_state.ab_framework.get_experiment_status(selected_exp_id)
    
    if not exp_status:
        st.error("无法获取实验状态")
        return
    
    # 实验控制
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if exp_status["status"] == "draft":
            if st.button("🚀 启动实验", type="primary"):
                success = st.session_state.ab_framework.start_experiment(selected_exp_id)
                if success:
                    st.success("实验已启动")
                    st.rerun()
    
    with col2:
        if exp_status["status"] == "running":
            if st.button("⏸️ 暂停实验"):
                st.info("实验已暂停")
    
    with col3:
        if exp_status["status"] == "running":
            if st.button("🛑 停止实验"):
                st.info("实验已停止")
    
    with col4:
        if st.button("📊 分析结果"):
            st.session_state.show_analysis = True
    
    # 实验概览
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("实验状态", exp_status["status"].upper())
    
    with col2:
        st.metric("总试验次数", exp_status["total_trials"])
    
    with col3:
        st.metric("分支数量", exp_status["arms_count"])
    
    with col4:
        duration = "运行中" if exp_status["status"] == "running" else "已结束"
        st.metric("运行状态", duration)
    
    # 分支性能对比
    st.markdown("#### 🔀 分支性能对比")
    
    arms_data = []
    for arm_id, arm_info in exp_status["arms"].items():
        arms_data.append({
            "分支": arm_info["name"],
            "试验次数": arm_info["total_trials"],
            "转化率": f"{arm_info['conversion_rate']:.3f}",
            "置信区间": f"[{arm_info['confidence_interval'][0]:.3f}, {arm_info['confidence_interval'][1]:.3f}]"
        })
    
    df_arms = pd.DataFrame(arms_data)
    st.dataframe(df_arms, use_container_width=True)
    
    # 性能趋势图
    if exp_status["total_trials"] > 0:
        show_performance_trends(selected_exp_id, exp_status)


def show_performance_trends(experiment_id: str, exp_status: Dict[str, Any]):
    """显示性能趋势"""
    st.markdown("#### 📈 性能趋势")
    
    # 生成模拟的趋势数据
    trend_data = generate_mock_trend_data(exp_status)
    
    # 创建趋势图
    fig = go.Figure()
    
    colors = ['blue', 'red', 'green', 'orange', 'purple']
    
    for i, (arm_id, arm_info) in enumerate(exp_status["arms"].items()):
        arm_name = arm_info["name"]
        
        # 模拟该分支的趋势数据
        x_data = list(range(1, len(trend_data) + 1))
        y_data = [d.get(arm_id, 0) for d in trend_data]
        
        fig.add_trace(go.Scatter(
            x=x_data,
            y=y_data,
            mode='lines+markers',
            name=arm_name,
            line=dict(color=colors[i % len(colors)], width=2)
        ))
    
    fig.update_layout(
        title="分支转化率趋势",
        xaxis_title="时间点",
        yaxis_title="转化率",
        height=400
    )
    
    st.plotly_chart(fig, use_container_width=True)


def show_statistical_analysis():
    """显示统计显著性分析"""
    st.subheader("📊 统计显著性分析")
    
    if not st.session_state.current_experiment_id:
        st.warning("请先选择一个实验")
        return
    
    # 分析实验结果
    analysis = st.session_state.ab_framework.analyze_experiment(
        st.session_state.current_experiment_id
    )
    
    if not analysis:
        st.info("暂无分析结果")
        return
    
    # 显示分析结果
    col1, col2, col3 = st.columns(3)
    
    with col1:
        significance_color = "green" if analysis.statistical_significance else "red"
        st.metric(
            "统计显著性",
            "显著" if analysis.statistical_significance else "不显著",
            delta_color=significance_color
        )
    
    with col2:
        st.metric("P值", f"{analysis.p_value:.4f}")
    
    with col3:
        st.metric("置信度", f"{analysis.confidence:.3f}")
    
    # 获胜分支
    if analysis.winner_arm_id:
        st.success(f"🏆 获胜分支: {analysis.winner_arm_id}")
    else:
        st.info("暂无明显获胜分支")
    
    # 效应大小
    st.metric("效应大小", f"{analysis.effect_size:.4f}")
    
    # 建议
    st.markdown("#### 💡 建议")
    for recommendation in analysis.recommendations:
        st.write(f"• {recommendation}")
    
    # 详细结果
    with st.expander("📋 详细分析结果"):
        st.json(analysis.detailed_results)


def show_experiment_history():
    """显示实验历史"""
    st.subheader("📚 实验历史")
    
    experiments = st.session_state.ab_framework.list_experiments()
    
    if not experiments:
        st.info("暂无实验历史")
        return
    
    # 实验列表
    exp_data = []
    for exp in experiments:
        exp_data.append({
            "实验ID": exp["experiment_id"][:8] + "...",
            "名称": exp["name"],
            "状态": exp["status"],
            "分支数": exp["arms_count"],
            "试验次数": exp["total_trials"],
            "创建时间": exp["created_at"][:19] if exp["created_at"] else "N/A",
            "分配策略": exp["allocation_strategy"]
        })
    
    df_experiments = pd.DataFrame(exp_data)
    st.dataframe(df_experiments, use_container_width=True)
    
    # 实验统计
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("总实验数", len(experiments))
    
    with col2:
        running_count = sum(1 for exp in experiments if exp["status"] == "running")
        st.metric("运行中", running_count)
    
    with col3:
        completed_count = sum(1 for exp in experiments if exp["status"] == "completed")
        st.metric("已完成", completed_count)
    
    with col4:
        total_trials = sum(exp["total_trials"] for exp in experiments)
        st.metric("总试验次数", total_trials)


def generate_mock_trend_data(exp_status: Dict[str, Any]) -> List[Dict[str, float]]:
    """生成模拟趋势数据"""
    np.random.seed(42)
    
    trend_data = []
    arms = list(exp_status["arms"].keys())
    
    # 生成20个时间点的数据
    for i in range(20):
        data_point = {}
        
        for j, arm_id in enumerate(arms):
            # 模拟不同分支的性能趋势
            base_rate = 0.6 + j * 0.05  # 不同分支有不同的基础转化率
            noise = np.random.normal(0, 0.02)
            trend = i * 0.001  # 轻微上升趋势
            
            rate = max(0, min(1, base_rate + noise + trend))
            data_point[arm_id] = rate
        
        trend_data.append(data_point)
    
    return trend_data


def main():
    """主函数"""
    st.set_page_config(
        page_title="A/B测试深度管理",
        page_icon="🧪",
        layout="wide"
    )
    
    st.title("🧪 A/B测试深度管理")
    st.markdown("---")
    
    # 初始化会话状态
    initialize_session_state()
    
    # 创建标签页
    tab1, tab2, tab3, tab4 = st.tabs([
        "🧙‍♂️ 实验设计", 
        "📊 实验监控", 
        "📈 统计分析", 
        "📚 实验历史"
    ])
    
    with tab1:
        show_experiment_design_wizard()
    
    with tab2:
        show_experiment_monitoring()
    
    with tab3:
        show_statistical_analysis()
    
    with tab4:
        show_experiment_history()


if __name__ == "__main__":
    main()
