# 阶段2：依赖安装和配置

**阶段状态**: 未开始  
**预计耗时**: 30分钟  
**依赖关系**: 阶段1完成  

## 📋 阶段目标

安装WebSocket相关依赖包，更新项目配置文件，为WebSocket功能提供必要的库支持。

## 🔧 具体任务

### 2.1 更新pyproject.toml

**文件路径**: `pyproject.toml`  
**涉及部分**: `[project] dependencies` 部分  
**修改行数**: 约5-10行  

**具体修改**:
在 `dependencies` 列表中添加以下依赖：

```toml
# WebSocket支持 (添加到dependencies部分)
"websockets>=11.0.3,<12.0.0",
"redis>=5.0.1,<6.0.0",
"aioredis>=2.0.1,<3.0.0",
"ujson>=5.8.0,<6.0.0",
"orjson>=3.9.0,<4.0.0",
```

**预期结果**: 
- pyproject.toml包含所有WebSocket依赖
- 版本约束符合Python 3.11.9兼容性
- 依赖声明格式正确

**依赖库**: 无

### 2.2 安装核心WebSocket依赖

**操作内容**: 安装websockets、fastapi[websockets]、uvicorn[standard]

**具体步骤**:
```bash
# 安装核心WebSocket库
pip install "websockets>=11.0.3,<12.0.0"

# 安装FastAPI WebSocket支持
pip install "fastapi[websockets]>=0.104.0,<0.111.0"

# 安装ASGI服务器WebSocket支持
pip install "uvicorn[standard]>=0.24.0,<0.28.0"

# 验证安装
pip show websockets fastapi uvicorn
```

**预期结果**: 
- websockets库版本 >=11.0.3
- fastapi包含websockets支持
- uvicorn包含standard扩展
- 所有包成功安装

**依赖库**: 
- Python 3.11.9
- pip

### 2.3 安装事件总线依赖

**操作内容**: 安装Redis和aioredis支持

**具体步骤**:
```bash
# 安装Redis客户端
pip install "redis>=5.0.1,<6.0.0"

# 安装异步Redis客户端
pip install "aioredis>=2.0.1,<3.0.0"

# 验证安装
pip show redis aioredis
```

**预期结果**: 
- redis库版本 >=5.0.1
- aioredis库版本 >=2.0.1
- 支持异步Redis操作

**依赖库**: 
- Python 3.11.9
- pip

### 2.4 安装性能优化依赖

**操作内容**: 安装ujson、orjson等性能优化包

**具体步骤**:
```bash
# 安装快速JSON库
pip install "ujson>=5.8.0,<6.0.0"

# 安装更快的JSON处理库
pip install "orjson>=3.9.0,<4.0.0"

# 验证安装
pip show ujson orjson
```

**预期结果**: 
- ujson库版本 >=5.8.0
- orjson库版本 >=3.9.0
- JSON处理性能提升

**依赖库**: 
- Python 3.11.9
- pip
- C编译器 (orjson需要)

### 2.5 验证依赖安装

**操作内容**: 检查所有依赖是否正确安装

**具体步骤**:
```bash
# 检查所有新安装的包
pip show websockets fastapi uvicorn redis aioredis ujson orjson

# 测试导入
python -c "
import websockets
import fastapi
import uvicorn
import redis
import aioredis
import ujson
import orjson
print('所有依赖导入成功')
"

# 生成新的依赖快照
pip freeze > packages_after_websocket_$(date +%Y%m%d_%H%M%S).txt
```

**预期结果**: 
- 所有包信息正确显示
- 所有包可以正常导入
- 生成新的依赖快照文件

**依赖库**: 
- 前面安装的所有包

## ✅ 验收标准

- [ ] pyproject.toml已更新WebSocket依赖
- [ ] websockets>=11.0.3安装成功
- [ ] fastapi[websockets]安装成功
- [ ] uvicorn[standard]安装成功
- [ ] redis>=5.0.1安装成功
- [ ] aioredis>=2.0.1安装成功
- [ ] ujson>=5.8.0安装成功
- [ ] orjson>=3.9.0安装成功
- [ ] 所有包可以正常导入
- [ ] 生成新的依赖快照

## 🚨 注意事项

1. **版本兼容性**: 确保所有包版本与Python 3.11.9兼容
2. **网络连接**: 安装过程需要稳定的网络连接
3. **编译环境**: orjson可能需要C编译器
4. **虚拟环境**: 确保在正确的虚拟环境中安装
5. **权限问题**: 确保有足够权限安装包

## 🔧 故障排除

### 常见问题

1. **编译错误**: 如果orjson安装失败，可以跳过或使用预编译版本
2. **网络超时**: 使用国内镜像源加速下载
3. **版本冲突**: 检查现有包版本，必要时升级

### 解决方案

```bash
# 使用国内镜像源
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ websockets

# 跳过编译，使用预编译版本
pip install --only-binary=all orjson
```

## 📝 执行记录

### 执行时间
- 开始时间: ___________
- 结束时间: ___________
- 实际耗时: ___________

### 执行结果
- [ ] 任务2.1完成
- [ ] 任务2.2完成
- [ ] 任务2.3完成
- [ ] 任务2.4完成
- [ ] 任务2.5完成
- [ ] 阶段验收通过

### 问题记录
- 遇到的问题: ___________
- 解决方案: ___________
- 经验教训: ___________

## 🔄 下一阶段

完成本阶段后，继续执行 [阶段3：代码修复和配置](./阶段3-代码修复和配置.md)
