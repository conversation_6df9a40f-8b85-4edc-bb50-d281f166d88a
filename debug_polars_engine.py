#!/usr/bin/env python3
"""
调试Polars引擎
"""

import sys
sys.path.append('src')

from core.data_engine import DataEngine

def debug_polars_engine():
    print("🔍 调试Polars引擎...")
    
    try:
        # 初始化数据引擎
        engine = DataEngine("data/lottery.db")
        
        # 检查数据库记录数
        db_count = engine.db_manager.get_records_count()
        print(f"数据库记录数: {db_count}")
        
        # 检查Polars引擎DataFrame
        polars_df = engine.polars_engine.df
        if polars_df is None:
            print("❌ Polars DataFrame为空，需要加载数据")
            
            # 从数据库加载数据
            engine.load_data_from_database()
            polars_df = engine.polars_engine.df
            
            if polars_df is not None:
                print(f"✅ 数据加载成功，Polars DataFrame记录数: {len(polars_df)}")
            else:
                print("❌ 数据加载失败，Polars DataFrame仍为空")
                return
        else:
            print(f"✅ Polars DataFrame已有数据，记录数: {len(polars_df)}")
        
        # 测试get_basic_stats
        print("\n测试get_basic_stats方法...")
        stats = engine.polars_engine.get_basic_stats()
        
        print("返回的统计信息:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
            
        # 测试数据引擎的get_basic_stats
        print("\n测试数据引擎的get_basic_stats方法...")
        engine_stats = engine.get_basic_stats()
        
        print("数据引擎返回的统计信息:")
        for key, value in engine_stats.items():
            print(f"  {key}: {value}")
            
    except Exception as e:
        print(f"❌ 调试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_polars_engine()
