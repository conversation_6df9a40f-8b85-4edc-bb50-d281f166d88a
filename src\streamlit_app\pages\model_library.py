"""
模型库管理页面

Streamlit界面用于管理和使用模型库
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.model_library.model_registry import ModelRegistry
    from src.model_library.prediction_engine import ModelPredictor
    from src.model_library.performance_tracker import PerformanceTracker
    from src.model_library.utils.data_utils import LotteryDataLoader
except ImportError as e:
    st.error(f"导入模型库模块失败: {e}")
    st.stop()


def main():
    """主页面"""
    st.set_page_config(
        page_title="福彩3D模型库",
        page_icon="🎯",
        layout="wide"
    )
    
    st.title("🎯 福彩3D预测模型库")
    st.markdown("---")
    
    # 侧边栏导航
    with st.sidebar:
        st.header("📋 功能导航")
        page = st.selectbox(
            "选择功能",
            ["模型总览", "模型预测", "性能监控", "模型管理"]
        )
    
    # 根据选择显示不同页面
    if page == "模型总览":
        show_model_overview()
    elif page == "模型预测":
        show_prediction_page()
    elif page == "性能监控":
        show_performance_page()
    elif page == "模型管理":
        show_management_page()


def show_model_overview():
    """模型总览页面"""
    st.header("📊 模型总览")
    
    try:
        registry = ModelRegistry()
        models = registry.list_models()
        
        if not models:
            st.warning("暂无已注册的模型")
            return
        
        # 模型统计
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("总模型数", len(models))
        
        with col2:
            active_models = sum(1 for m in models if m.is_active)
            st.metric("活跃模型", active_models)
        
        with col3:
            model_types = set(m.model_type.value for m in models)
            st.metric("模型类型", len(model_types))
        
        with col4:
            st.metric("最新版本", "3.0.0")
        
        # 模型列表
        st.subheader("📋 模型列表")
        
        model_data = []
        for model in models:
            # 获取模型状态
            try:
                status_info = registry.get_model_status(model.model_id)
                status = status_info.status.value if status_info else "unknown"
            except:
                status = "unknown"
            
            model_data.append({
                "模型ID": model.model_id,
                "模型名称": model.name,
                "类型": model.model_type.value,
                "版本": model.version,
                "状态": status,
                "作者": model.author,
                "创建时间": model.created_at.strftime("%Y-%m-%d")
            })
        
        df = pd.DataFrame(model_data)
        st.dataframe(df, use_container_width=True)
        
        # 模型类型分布图
        st.subheader("📈 模型类型分布")
        type_counts = df['类型'].value_counts()
        
        fig = px.pie(
            values=type_counts.values,
            names=type_counts.index,
            title="模型类型分布"
        )
        st.plotly_chart(fig, use_container_width=True)
        
    except Exception as e:
        st.error(f"加载模型总览失败: {e}")


def show_prediction_page():
    """模型预测页面"""
    st.header("🔮 模型预测")
    
    try:
        registry = ModelRegistry()
        predictor = ModelPredictor()
        
        # 获取可用模型
        models = registry.list_models()
        if not models:
            st.warning("暂无可用模型")
            return
        
        model_options = {f"{m.name} ({m.model_id})": m.model_id for m in models}
        
        # 预测配置
        col1, col2 = st.columns([2, 1])
        
        with col1:
            st.subheader("⚙️ 预测配置")
            
            # 选择预测模式
            prediction_mode = st.radio(
                "预测模式",
                ["单模型预测", "组合预测"]
            )
            
            if prediction_mode == "单模型预测":
                selected_model = st.selectbox(
                    "选择模型",
                    options=list(model_options.keys())
                )
                model_ids = [model_options[selected_model]]
            else:
                selected_models = st.multiselect(
                    "选择模型（可多选）",
                    options=list(model_options.keys()),
                    default=list(model_options.keys())[:2]
                )
                model_ids = [model_options[m] for m in selected_models]
                
                if len(model_ids) > 1:
                    strategy = st.selectbox(
                        "组合策略",
                        ["voting", "weighted", "intersection"]
                    )
        
        with col2:
            st.subheader("📊 预测参数")
            top_n = st.slider("返回预测数量", 1, 5, 3)
            
            if st.button("🚀 执行预测", type="primary"):
                with st.spinner("正在预测..."):
                    try:
                        # 加载历史数据
                        data_loader = LotteryDataLoader()
                        history = data_loader.load_recent_records(50)
                        
                        if not history:
                            st.error("无法加载历史数据")
                            return
                        
                        # 执行预测
                        if prediction_mode == "单模型预测":
                            prediction = predictor.predict_single(model_ids[0], history, top_n)
                        else:
                            prediction = predictor.predict_combined(
                                model_ids, history, strategy, top_n=top_n
                            )
                        
                        # 显示预测结果
                        show_prediction_result(prediction)
                        
                    except Exception as e:
                        st.error(f"预测失败: {e}")
        
    except Exception as e:
        st.error(f"加载预测页面失败: {e}")


def show_prediction_result(prediction):
    """显示预测结果"""
    st.subheader("🎯 预测结果")
    
    col1, col2, col3 = st.columns(3)
    
    # 显示各位预测
    positions = [("百位", prediction.百位), ("十位", prediction.十位), ("个位", prediction.个位)]
    
    for i, (pos_name, pos_probs) in enumerate(positions):
        with [col1, col2, col3][i]:
            st.write(f"**{pos_name}**")
            
            if pos_probs:
                # 排序并显示top3
                sorted_probs = sorted(pos_probs.items(), key=lambda x: x[1], reverse=True)[:3]
                
                for j, (digit, prob) in enumerate(sorted_probs):
                    st.write(f"{j+1}. 数字 {digit}: {prob:.3f}")
    
    # 预测信息
    st.subheader("📋 预测信息")
    info_col1, info_col2 = st.columns(2)
    
    with info_col1:
        st.write(f"**目标期号**: {prediction.target_period}")
        st.write(f"**预测时间**: {prediction.prediction_time.strftime('%Y-%m-%d %H:%M:%S')}")
        st.write(f"**置信度**: {prediction.confidence:.3f}")
    
    with info_col2:
        st.write(f"**模型ID**: {prediction.model_id}")
        if prediction.metadata:
            for key, value in prediction.metadata.items():
                st.write(f"**{key}**: {value}")


def show_performance_page():
    """性能监控页面"""
    st.header("📈 性能监控")
    
    try:
        tracker = PerformanceTracker()
        
        # 性能排行榜
        st.subheader("🏆 性能排行榜")
        
        ranking = tracker.get_performance_ranking("direct_accuracy", 10)
        
        if ranking:
            ranking_data = []
            for item in ranking:
                ranking_data.append({
                    "排名": item.get("rank", 0),
                    "模型名称": item.get("name", "未知"),
                    "模型ID": item.get("model_id", ""),
                    "准确率": f"{item.get('direct_accuracy', 0):.3f}",
                    "预测次数": item.get("total_predictions", 0),
                    "平均置信度": f"{item.get('average_confidence', 0):.3f}"
                })
            
            df = pd.DataFrame(ranking_data)
            st.dataframe(df, use_container_width=True)
        else:
            st.info("暂无性能数据")
        
        # 性能趋势图
        st.subheader("📊 性能趋势")
        
        if ranking:
            # 创建准确率对比图
            model_names = [item.get("name", "未知") for item in ranking[:5]]
            accuracies = [item.get("direct_accuracy", 0) for item in ranking[:5]]
            
            fig = go.Figure(data=[
                go.Bar(x=model_names, y=accuracies, name="准确率")
            ])
            
            fig.update_layout(
                title="模型准确率对比",
                xaxis_title="模型",
                yaxis_title="准确率",
                yaxis=dict(range=[0, 1])
            )
            
            st.plotly_chart(fig, use_container_width=True)
        
    except Exception as e:
        st.error(f"加载性能监控失败: {e}")


def show_management_page():
    """模型管理页面"""
    st.header("⚙️ 模型管理")
    
    try:
        registry = ModelRegistry()
        
        # 模型状态管理
        st.subheader("📊 模型状态")
        
        models = registry.list_models()
        
        if models:
            for model in models:
                with st.expander(f"{model.name} ({model.model_id})"):
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        st.write("**基本信息**")
                        st.write(f"类型: {model.model_type.value}")
                        st.write(f"版本: {model.version}")
                        st.write(f"作者: {model.author}")
                        st.write(f"描述: {model.description}")
                    
                    with col2:
                        st.write("**状态信息**")
                        try:
                            status_info = registry.get_model_status(model.model_id)
                            if status_info:
                                st.write(f"状态: {status_info.status.value}")
                                st.write(f"数据就绪: {'是' if status_info.data_ready else '否'}")
                                st.write(f"特征就绪: {'是' if status_info.features_ready else '否'}")
                                st.write(f"已训练: {'是' if status_info.trained else '否'}")
                                st.write(f"训练数据量: {status_info.training_data_size}")
                            else:
                                st.write("状态: 未知")
                        except Exception as e:
                            st.write(f"状态获取失败: {e}")
        else:
            st.info("暂无已注册的模型")
        
        # 系统信息
        st.subheader("🔧 系统信息")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("**模型库版本**: 1.0.0")
            st.write("**数据库状态**: 正常")
            st.write("**最后更新**: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        
        with col2:
            if st.button("🔄 刷新模型状态"):
                st.rerun()
            
            if st.button("🧹 清除缓存"):
                # 这里可以添加清除缓存的逻辑
                st.success("缓存已清除")
        
    except Exception as e:
        st.error(f"加载管理页面失败: {e}")


if __name__ == "__main__":
    main()
