"""
马尔可夫模型交叉验证实现

使用k折交叉验证评估马尔可夫模型性能
"""

import os
import json
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime
from collections import defaultdict

from .model_validation import MarkovCrossValidator
from .pattern_prediction import PatternPredictor


class MarkovModelValidator:
    """马尔可夫模型验证器"""
    
    def __init__(self, db_path: str = None):
        """
        初始化验证器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path or os.path.join('data', 'lottery.db')
        self.cross_validator = MarkovCrossValidator(db_path)
        self.validation_results = {}
        
    def validate_markov_model(self, 
                             transition_window_size: int = 1000,
                             probability_window_size: int = 500,
                             smoothing_alpha: float = 1.0,
                             k_folds: int = 5,
                             data_limit: int = 2000) -> Dict[str, Any]:
        """
        验证马尔可夫模型
        
        Args:
            transition_window_size: 转移矩阵窗口大小
            probability_window_size: 概率计算窗口大小
            smoothing_alpha: 平滑参数
            k_folds: 交叉验证折数
            data_limit: 数据限制
            
        Returns:
            验证结果
        """
        print(f"开始马尔可夫模型交叉验证 (k={k_folds})...")
        
        # 加载验证数据
        data = self.cross_validator.load_validation_data(limit=data_limit)
        if not data:
            return {'error': '无法加载验证数据'}
        
        print(f"加载了 {len(data)} 条验证数据")
        
        # 创建k折交叉验证分割
        folds = self.cross_validator.k_fold_time_series_split(data, k=k_folds)
        if not folds:
            return {'error': '无法创建交叉验证分割'}
        
        print(f"创建了 {len(folds)} 个交叉验证折")
        
        # 存储每折的预测结果
        all_predictions = []
        all_actuals = []
        fold_results = []
        
        # 对每个折进行验证
        for fold_idx, (train_data, val_data) in enumerate(folds):
            print(f"\n验证折 {fold_idx+1}/{len(folds)}:")
            print(f"  训练集: {len(train_data)} 条")
            print(f"  验证集: {len(val_data)} 条")
            
            # 创建并训练模型
            predictor = PatternPredictor(
                db_path=self.db_path,
                transition_window_size=transition_window_size,
                probability_window_size=probability_window_size,
                smoothing_alpha=smoothing_alpha
            )
            
            # 使用训练数据训练模型
            train_result = predictor.train_model()
            if not train_result.get('success', False):
                print(f"  ❌ 模型训练失败: {train_result}")
                continue
            
            # 在验证集上进行预测
            predictions = []
            actuals = []
            
            for record in val_data:
                # 获取实际结果
                actual = record.get('numbers', '')
                if not actual or len(actual) != 3:
                    continue
                
                # 获取历史数据（不包括当前记录）
                history_data = [r for r in train_data if r['period'] < record['period']]
                
                # 预测下一期
                try:
                    # 生成形态预测
                    pattern_predictions = predictor.predict_next_patterns(history_data)
                    
                    # 生成候选号码
                    candidates = predictor.generate_candidate_numbers(pattern_predictions, top_k=5)
                    
                    if candidates:
                        # 使用第一个候选作为预测结果
                        prediction = candidates[0]['numbers']
                        predictions.append(prediction)
                        actuals.append(actual)
                except Exception as e:
                    print(f"  预测失败: {e}")
            
            # 计算当前折的性能指标
            if predictions and actuals:
                accuracy_metrics = self.cross_validator.calculate_prediction_accuracy(predictions, actuals)
                diversity_metrics = self.cross_validator.calculate_diversity_metrics(predictions)
                stability_metrics = self.cross_validator.calculate_stability_metrics(predictions)
                
                # 估计模型似然
                log_likelihood = self.cross_validator.estimate_model_likelihood(predictions, actuals)
                
                # 计算AIC和BIC
                # 马尔可夫链参数数量：转移矩阵(10x10)和初始概率(10)
                num_params = 10 * 10 + 10
                aic_bic = self.cross_validator.calculate_aic_bic(
                    log_likelihood, 
                    num_params, 
                    len(predictions)
                )
                
                fold_result = {
                    'fold_idx': fold_idx,
                    'train_size': len(train_data),
                    'val_size': len(val_data),
                    'predictions_count': len(predictions),
                    'accuracy_metrics': accuracy_metrics,
                    'diversity_metrics': diversity_metrics,
                    'stability_metrics': stability_metrics,
                    'aic_bic': aic_bic
                }
                
                fold_results.append(fold_result)
                
                # 累积所有预测结果
                all_predictions.extend(predictions)
                all_actuals.extend(actuals)
                
                print(f"  ✅ 折 {fold_idx+1} 验证完成:")
                print(f"    预测数量: {len(predictions)}")
                print(f"    准确率: {accuracy_metrics['exact_match']:.4f}")
                print(f"    多样性: {diversity_metrics['simpson_diversity']:.4f}")
                print(f"    AIC: {aic_bic['aic']:.2f}, BIC: {aic_bic['bic']:.2f}")
            else:
                print(f"  ❌ 折 {fold_idx+1} 无有效预测")
        
        # 计算整体性能指标
        overall_results = {}
        
        if all_predictions and all_actuals:
            overall_accuracy = self.cross_validator.calculate_prediction_accuracy(all_predictions, all_actuals)
            overall_diversity = self.cross_validator.calculate_diversity_metrics(all_predictions)
            overall_stability = self.cross_validator.calculate_stability_metrics(all_predictions)
            
            # 整体AIC/BIC
            overall_log_likelihood = self.cross_validator.estimate_model_likelihood(all_predictions, all_actuals)
            overall_aic_bic = self.cross_validator.calculate_aic_bic(
                overall_log_likelihood, 
                num_params, 
                len(all_predictions)
            )
            
            overall_results = {
                'accuracy_metrics': overall_accuracy,
                'diversity_metrics': overall_diversity,
                'stability_metrics': overall_stability,
                'aic_bic': overall_aic_bic,
                'total_predictions': len(all_predictions)
            }
        
        # 汇总结果
        validation_results = {
            'model_params': {
                'transition_window_size': transition_window_size,
                'probability_window_size': probability_window_size,
                'smoothing_alpha': smoothing_alpha
            },
            'validation_params': {
                'k_folds': k_folds,
                'data_limit': data_limit
            },
            'fold_results': fold_results,
            'overall_results': overall_results
        }
        
        # 保存验证结果
        self.validation_results = validation_results
        
        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"markov_cross_validation_{timestamp}.json"
        self.cross_validator.save_validation_report(validation_results, report_filename)
        
        print("\n✅ 马尔可夫模型交叉验证完成")
        if overall_results:
            print(f"总体准确率: {overall_results['accuracy_metrics']['exact_match']:.4f}")
            print(f"总体多样性: {overall_results['diversity_metrics']['simpson_diversity']:.4f}")
            print(f"总体AIC: {overall_results['aic_bic']['aic']:.2f}, BIC: {overall_results['aic_bic']['bic']:.2f}")
        
        return validation_results
    
    def compare_markov_models(self, 
                             window_sizes: List[int] = [100, 500, 1000],
                             alphas: List[float] = [0.5, 1.0, 2.0],
                             k_folds: int = 3) -> Dict[str, Any]:
        """
        比较不同参数的马尔可夫模型
        
        Args:
            window_sizes: 窗口大小列表
            alphas: 平滑参数列表
            k_folds: 交叉验证折数
            
        Returns:
            比较结果
        """
        print(f"开始马尔可夫模型参数比较...")
        
        comparison_results = {}
        best_model = {'aic': float('inf'), 'params': None, 'accuracy': 0}
        
        for window_size in window_sizes:
            for alpha in alphas:
                print(f"\n评估参数: 窗口大小={window_size}, 平滑参数={alpha}")
                
                # 验证当前参数组合
                result = self.validate_markov_model(
                    transition_window_size=window_size,
                    probability_window_size=window_size // 2,  # 概率窗口为转移窗口的一半
                    smoothing_alpha=alpha,
                    k_folds=k_folds,
                    data_limit=2000
                )
                
                # 保存结果
                key = f"window_{window_size}_alpha_{alpha}"
                comparison_results[key] = result
                
                # 更新最佳模型
                if 'overall_results' in result and 'aic_bic' in result['overall_results']:
                    current_aic = result['overall_results']['aic_bic']['aic']
                    current_accuracy = result['overall_results']['accuracy_metrics']['exact_match']
                    
                    if current_aic < best_model['aic']:
                        best_model['aic'] = current_aic
                        best_model['params'] = {
                            'window_size': window_size,
                            'alpha': alpha
                        }
                        best_model['accuracy'] = current_accuracy
        
        # 汇总比较结果
        summary = {
            'models_compared': len(window_sizes) * len(alphas),
            'best_model': best_model,
            'comparison_results': comparison_results
        }
        
        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"markov_model_comparison_{timestamp}.json"
        self.cross_validator.save_validation_report(summary, report_filename)
        
        print("\n✅ 马尔可夫模型参数比较完成")
        if best_model['params']:
            print(f"最佳参数: 窗口大小={best_model['params']['window_size']}, 平滑参数={best_model['params']['alpha']}")
            print(f"最佳AIC: {best_model['aic']:.2f}")
            print(f"最佳准确率: {best_model['accuracy']:.4f}")
        
        return summary


if __name__ == "__main__":
    # 测试代码
    validator = MarkovModelValidator()
    
    # 验证单个模型
    result = validator.validate_markov_model(
        transition_window_size=1000,
        probability_window_size=500,
        smoothing_alpha=1.0,
        k_folds=3
    )
    
    # 比较不同参数的模型
    # comparison = validator.compare_markov_models(
    #     window_sizes=[100, 500, 1000],
    #     alphas=[0.5, 1.0, 2.0],
    #     k_folds=2
    # )
