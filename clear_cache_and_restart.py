#!/usr/bin/env python3
"""
清除缓存并重启服务
"""

import sys
sys.path.append('src')

from core.database import DatabaseManager
import sqlite3

def clear_all_cache():
    """清除所有缓存数据"""
    print("🧹 清除所有缓存数据...")
    
    db = DatabaseManager()
    
    try:
        with db._get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查缓存表中的数据
            cursor.execute("SELECT COUNT(*) FROM statistics_cache")
            cache_count = cursor.fetchone()[0]
            print(f"当前缓存记录数: {cache_count}")
            
            if cache_count > 0:
                # 查看缓存内容
                cursor.execute("SELECT cache_key, created_at FROM statistics_cache")
                cache_records = cursor.fetchall()
                print("缓存记录:")
                for record in cache_records:
                    print(f"  - {record[0]}: {record[1]}")
                
                # 清除所有缓存
                cursor.execute("DELETE FROM statistics_cache")
                deleted_count = cursor.rowcount
                conn.commit()
                
                print(f"✅ 已清除 {deleted_count} 条缓存记录")
            else:
                print("ℹ️ 缓存表为空，无需清除")
                
    except Exception as e:
        print(f"❌ 清除缓存失败: {e}")
        return False
    
    return True

def test_api_without_cache():
    """测试API不使用缓存"""
    print("\n🧪 测试API不使用缓存...")
    
    import requests
    try:
        response = requests.get("http://127.0.0.1:8888/api/v1/stats/basic?use_cache=false", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ API响应成功")
            print(f"📊 总记录数: {data.get('total_records', 'N/A')}")
            print(f"📅 数据范围: {data.get('date_range', {}).get('start', 'N/A')} 到 {data.get('date_range', {}).get('end', 'N/A')}")
            
            sum_stats = data.get('sum_value_stats', {})
            if sum_stats:
                print(f"📈 和值统计: 平均值 {sum_stats.get('mean', 'N/A')}, 范围 {sum_stats.get('min', 'N/A')}-{sum_stats.get('max', 'N/A')}")
            else:
                print("⚠️ 和值统计数据为空")
                
            return True
        else:
            print(f"❌ API响应失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def test_api_with_cache():
    """测试API使用缓存"""
    print("\n🧪 测试API使用缓存...")
    
    import requests
    try:
        response = requests.get("http://127.0.0.1:8888/api/v1/stats/basic?use_cache=true", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ API响应成功")
            print(f"📊 总记录数: {data.get('total_records', 'N/A')}")
            
            sum_stats = data.get('sum_value_stats', {})
            if sum_stats:
                print(f"📈 和值统计: 平均值 {sum_stats.get('mean', 'N/A')}")
                print("✅ 缓存数据正常")
                return True
            else:
                print("⚠️ 缓存数据仍为空，需要重新生成")
                return False
        else:
            print(f"❌ API响应失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def main():
    print("=" * 60)
    print("🔧 福彩3D预测分析工具 - 缓存清理和修复")
    print("=" * 60)
    
    # 1. 清除缓存
    if not clear_all_cache():
        print("❌ 缓存清理失败，退出")
        return
    
    # 2. 测试API不使用缓存
    if not test_api_without_cache():
        print("❌ API基础功能异常，请检查服务状态")
        return
    
    # 3. 测试API使用缓存（这会重新生成缓存）
    print("\n🔄 重新生成缓存...")
    if test_api_with_cache():
        print("✅ 缓存重新生成成功")
    else:
        print("⚠️ 缓存重新生成可能需要多次调用")
    
    print("\n" + "=" * 60)
    print("🎉 缓存清理和修复完成！")
    print("📱 请刷新Streamlit页面: http://127.0.0.1:8501")
    print("📖 API文档: http://127.0.0.1:8888/docs")
    print("=" * 60)

if __name__ == "__main__":
    main()
