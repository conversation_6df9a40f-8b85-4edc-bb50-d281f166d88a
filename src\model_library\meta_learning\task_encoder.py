"""
任务编码器
实现任务特征向量化、相似度计算、特征降维聚类、任务分类器训练
"""

import numpy as np
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import logging

try:
    from sklearn.preprocessing import StandardScaler, LabelEncoder
    from sklearn.decomposition import PCA
    from sklearn.cluster import KMeans
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.metrics.pairwise import cosine_similarity, euclidean_distances
    from sklearn.manifold import TSNE
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False


@dataclass
class TaskVector:
    """任务向量"""
    task_id: str
    feature_vector: np.ndarray
    metadata: Dict[str, Any]
    created_at: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "task_id": self.task_id,
            "feature_vector": self.feature_vector.tolist(),
            "metadata": self.metadata,
            "created_at": self.created_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TaskVector':
        return cls(
            task_id=data["task_id"],
            feature_vector=np.array(data["feature_vector"]),
            metadata=data["metadata"],
            created_at=datetime.fromisoformat(data["created_at"])
        )


class TaskEncoder:
    """任务编码器"""
    
    def __init__(self, encoding_dim: int = 64):
        self.encoding_dim = encoding_dim
        self.feature_extractors = self._initialize_feature_extractors()
        
        # 编码组件
        self.scaler = StandardScaler() if SKLEARN_AVAILABLE else None
        self.pca = PCA(n_components=encoding_dim) if SKLEARN_AVAILABLE else None
        self.label_encoder = LabelEncoder() if SKLEARN_AVAILABLE else None
        
        # 聚类和分类
        self.kmeans = KMeans(n_clusters=5, random_state=42) if SKLEARN_AVAILABLE else None
        self.task_classifier = RandomForestClassifier(random_state=42) if SKLEARN_AVAILABLE else None
        
        # 存储
        self.task_vectors: Dict[str, TaskVector] = {}
        self.cluster_centers: Optional[np.ndarray] = None
        self.task_clusters: Dict[str, int] = {}
        
        # 训练状态
        self.is_fitted = False
        
        self.logger = logging.getLogger("TaskEncoder")
    
    def _initialize_feature_extractors(self) -> Dict[str, callable]:
        """初始化特征提取器"""
        return {
            "basic_stats": self._extract_basic_statistics,
            "data_characteristics": self._extract_data_characteristics,
            "complexity_metrics": self._extract_complexity_metrics,
            "domain_features": self._extract_domain_features,
            "performance_features": self._extract_performance_features
        }
    
    def encode_task(self, task_data: Dict[str, Any]) -> TaskVector:
        """编码任务为向量"""
        try:
            # 提取所有特征
            all_features = []
            feature_metadata = {}
            
            for extractor_name, extractor_func in self.feature_extractors.items():
                try:
                    features = extractor_func(task_data)
                    all_features.extend(features)
                    feature_metadata[extractor_name] = len(features)
                except Exception as e:
                    self.logger.warning(f"特征提取失败 {extractor_name}: {e}")
                    # 添加零特征作为占位符
                    placeholder_features = [0.0] * 10
                    all_features.extend(placeholder_features)
                    feature_metadata[extractor_name] = len(placeholder_features)
            
            # 转换为numpy数组
            feature_vector = np.array(all_features, dtype=np.float32)
            
            # 处理NaN和无穷值
            feature_vector = np.nan_to_num(feature_vector, nan=0.0, posinf=1.0, neginf=-1.0)
            
            # 如果有训练好的缩放器，应用缩放
            if self.scaler and self.is_fitted:
                feature_vector = self.scaler.transform([feature_vector])[0]
            
            # 如果有训练好的PCA，应用降维
            if self.pca and self.is_fitted:
                feature_vector = self.pca.transform([feature_vector])[0]
            
            # 创建任务向量
            task_vector = TaskVector(
                task_id=task_data.get("task_id", f"task_{int(datetime.now().timestamp())}"),
                feature_vector=feature_vector,
                metadata={
                    "feature_metadata": feature_metadata,
                    "original_dim": len(all_features),
                    "encoded_dim": len(feature_vector),
                    "task_type": task_data.get("task_type", "unknown"),
                    "data_size": task_data.get("data_size", 0),
                    "feature_count": task_data.get("feature_count", 0)
                },
                created_at=datetime.now()
            )
            
            # 存储任务向量
            self.task_vectors[task_vector.task_id] = task_vector
            
            return task_vector
            
        except Exception as e:
            self.logger.error(f"任务编码失败: {e}")
            # 返回零向量作为fallback
            return TaskVector(
                task_id=task_data.get("task_id", "error_task"),
                feature_vector=np.zeros(self.encoding_dim),
                metadata={"error": str(e)},
                created_at=datetime.now()
            )
    
    def _extract_basic_statistics(self, task_data: Dict[str, Any]) -> List[float]:
        """提取基础统计特征"""
        features = []
        
        # 数据规模特征
        data_size = task_data.get("data_size", 0)
        feature_count = task_data.get("feature_count", 0)
        
        features.extend([
            np.log10(max(1, data_size)),  # 数据量对数
            np.log10(max(1, feature_count)),  # 特征数对数
            data_size / max(1, feature_count),  # 数据特征比
            min(data_size, 10000) / 10000,  # 标准化数据量
            min(feature_count, 1000) / 1000  # 标准化特征数
        ])
        
        # 复杂度特征
        complexity = task_data.get("complexity_score", 0.5)
        features.extend([
            complexity,
            complexity ** 2,  # 复杂度平方
            1 / (1 + complexity),  # 复杂度倒数
        ])
        
        # 任务类型编码
        task_type = task_data.get("task_type", "unknown")
        type_encoding = {
            "classification": [1, 0, 0],
            "regression": [0, 1, 0], 
            "time_series": [0, 0, 1],
            "unknown": [0, 0, 0]
        }
        features.extend(type_encoding.get(task_type, [0, 0, 0]))
        
        return features
    
    def _extract_data_characteristics(self, task_data: Dict[str, Any]) -> List[float]:
        """提取数据特征"""
        features = []
        
        # 数据样本分析
        data_sample = task_data.get("data_sample")
        if data_sample is not None and len(data_sample) > 0:
            try:
                data_array = np.array(data_sample)
                
                # 统计特征
                features.extend([
                    np.mean(data_array),
                    np.std(data_array),
                    np.min(data_array),
                    np.max(data_array),
                    np.median(data_array)
                ])
                
                # 分布特征
                features.extend([
                    self._calculate_skewness(data_array.flatten()),
                    self._calculate_kurtosis(data_array.flatten()),
                    np.count_nonzero(data_array) / data_array.size,  # 非零比例
                ])
                
            except Exception:
                # 如果数据处理失败，使用默认值
                features.extend([0.0] * 8)
        else:
            # 无数据样本，使用默认值
            features.extend([0.0] * 8)
        
        # 目标类型特征
        target_type = task_data.get("target_type", "unknown")
        target_encoding = {
            "binary": [1, 0, 0],
            "multiclass": [0, 1, 0],
            "continuous": [0, 0, 1],
            "unknown": [0, 0, 0]
        }
        features.extend(target_encoding.get(target_type, [0, 0, 0]))
        
        return features
    
    def _extract_complexity_metrics(self, task_data: Dict[str, Any]) -> List[float]:
        """提取复杂度指标"""
        features = []
        
        data_size = task_data.get("data_size", 0)
        feature_count = task_data.get("feature_count", 0)
        
        # 维度复杂度
        features.extend([
            feature_count / max(1, data_size),  # 特征密度
            min(feature_count, data_size) / max(feature_count, data_size, 1),  # 维度比例
            np.sqrt(feature_count),  # 特征数平方根
        ])
        
        # 计算复杂度
        computational_complexity = data_size * feature_count
        features.extend([
            np.log10(max(1, computational_complexity)),
            min(computational_complexity, 1e6) / 1e6,  # 标准化计算复杂度
        ])
        
        # 学习复杂度估计
        learning_complexity = task_data.get("complexity_score", 0.5)
        features.extend([
            learning_complexity,
            learning_complexity * np.log10(max(1, data_size)),
            learning_complexity * np.log10(max(1, feature_count))
        ])
        
        return features
    
    def _extract_domain_features(self, task_data: Dict[str, Any]) -> List[float]:
        """提取领域特征"""
        features = []
        
        # 福彩3D特定特征
        domain = task_data.get("domain", "general")
        
        if domain == "lottery" or "lottery" in task_data.get("description", "").lower():
            # 福彩3D特定特征
            features.extend([
                1.0,  # 是福彩3D任务
                0.8,  # 序列性强度
                0.9,  # 模式重要性
                0.7,  # 历史依赖性
                0.6   # 随机性程度
            ])
        else:
            # 通用任务特征
            features.extend([0.0, 0.5, 0.5, 0.5, 0.5])
        
        # 时间序列特征
        is_time_series = task_data.get("task_type") == "time_series"
        features.extend([
            1.0 if is_time_series else 0.0,
            0.8 if is_time_series else 0.2,  # 时间依赖性
            0.9 if is_time_series else 0.1   # 序列重要性
        ])
        
        return features
    
    def _extract_performance_features(self, task_data: Dict[str, Any]) -> List[float]:
        """提取性能特征"""
        features = []
        
        # 历史性能
        best_accuracy = task_data.get("best_accuracy", 0.0)
        features.extend([
            best_accuracy,
            best_accuracy ** 2,
            1 - best_accuracy,  # 改进空间
        ])
        
        # 训练历史
        training_history = task_data.get("training_history", [])
        if training_history:
            performances = [record.get("performance", 0) for record in training_history]
            features.extend([
                np.mean(performances),
                np.std(performances),
                len(performances) / 100,  # 标准化训练次数
                max(performances) - min(performances) if performances else 0  # 性能范围
            ])
        else:
            features.extend([0.0, 0.0, 0.0, 0.0])
        
        return features
    
    def _calculate_skewness(self, data: np.ndarray) -> float:
        """计算偏度"""
        try:
            mean = np.mean(data)
            std = np.std(data)
            if std == 0:
                return 0.0
            return np.mean(((data - mean) / std) ** 3)
        except:
            return 0.0
    
    def _calculate_kurtosis(self, data: np.ndarray) -> float:
        """计算峰度"""
        try:
            mean = np.mean(data)
            std = np.std(data)
            if std == 0:
                return 0.0
            return np.mean(((data - mean) / std) ** 4) - 3
        except:
            return 0.0
    
    def fit_encoder(self, task_data_list: List[Dict[str, Any]]):
        """训练编码器"""
        if not SKLEARN_AVAILABLE:
            self.logger.warning("sklearn不可用，跳过编码器训练")
            return
        
        try:
            self.logger.info(f"开始训练任务编码器，任务数量: {len(task_data_list)}")
            
            # 编码所有任务
            all_vectors = []
            task_types = []
            
            for task_data in task_data_list:
                task_vector = self.encode_task(task_data)
                all_vectors.append(task_vector.feature_vector)
                task_types.append(task_data.get("task_type", "unknown"))
            
            if not all_vectors:
                self.logger.warning("无任务向量可用于训练")
                return
            
            # 转换为numpy数组
            X = np.array(all_vectors)
            
            # 训练缩放器
            self.scaler.fit(X)
            X_scaled = self.scaler.transform(X)
            
            # 训练PCA
            self.pca.fit(X_scaled)
            X_pca = self.pca.transform(X_scaled)
            
            # 更新所有任务向量
            for i, (task_id, task_vector) in enumerate(self.task_vectors.items()):
                task_vector.feature_vector = X_pca[i]
            
            # 训练聚类
            self.kmeans.fit(X_pca)
            self.cluster_centers = self.kmeans.cluster_centers_
            
            # 分配聚类标签
            cluster_labels = self.kmeans.labels_
            for i, task_id in enumerate(self.task_vectors.keys()):
                self.task_clusters[task_id] = int(cluster_labels[i])
            
            # 训练任务分类器
            if len(set(task_types)) > 1:  # 需要多个类别
                self.label_encoder.fit(task_types)
                y_encoded = self.label_encoder.transform(task_types)
                self.task_classifier.fit(X_pca, y_encoded)
            
            self.is_fitted = True
            self.logger.info("任务编码器训练完成")
            
        except Exception as e:
            self.logger.error(f"训练编码器失败: {e}")
    
    def find_similar_tasks(self, target_task_id: str, top_k: int = 5) -> List[Tuple[str, float]]:
        """寻找相似任务"""
        if target_task_id not in self.task_vectors:
            return []
        
        target_vector = self.task_vectors[target_task_id].feature_vector
        similarities = []
        
        for task_id, task_vector in self.task_vectors.items():
            if task_id == target_task_id:
                continue
            
            # 计算余弦相似度
            if SKLEARN_AVAILABLE:
                similarity = cosine_similarity([target_vector], [task_vector.feature_vector])[0, 0]
            else:
                # 简化的相似度计算
                similarity = 1 / (1 + np.linalg.norm(target_vector - task_vector.feature_vector))
            
            similarities.append((task_id, similarity))
        
        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        return similarities[:top_k]
    
    def predict_task_type(self, task_vector: np.ndarray) -> str:
        """预测任务类型"""
        if not self.is_fitted or not self.task_classifier:
            return "unknown"
        
        try:
            # 应用相同的预处理
            if self.scaler:
                task_vector = self.scaler.transform([task_vector])[0]
            if self.pca:
                task_vector = self.pca.transform([task_vector])[0]
            
            # 预测
            prediction = self.task_classifier.predict([task_vector])[0]
            task_type = self.label_encoder.inverse_transform([prediction])[0]
            
            return task_type
            
        except Exception as e:
            self.logger.error(f"预测任务类型失败: {e}")
            return "unknown"
    
    def get_task_cluster(self, task_id: str) -> Optional[int]:
        """获取任务聚类"""
        return self.task_clusters.get(task_id)
    
    def visualize_task_space(self) -> Optional[np.ndarray]:
        """可视化任务空间"""
        if not self.task_vectors or not SKLEARN_AVAILABLE:
            return None
        
        try:
            # 获取所有任务向量
            vectors = np.array([tv.feature_vector for tv in self.task_vectors.values()])
            
            # 使用t-SNE降维到2D
            tsne = TSNE(n_components=2, random_state=42)
            vectors_2d = tsne.fit_transform(vectors)
            
            return vectors_2d
            
        except Exception as e:
            self.logger.error(f"任务空间可视化失败: {e}")
            return None
    
    def export_encoder_state(self) -> Dict[str, Any]:
        """导出编码器状态"""
        state = {
            "encoding_dim": self.encoding_dim,
            "is_fitted": self.is_fitted,
            "task_count": len(self.task_vectors),
            "cluster_count": len(set(self.task_clusters.values())) if self.task_clusters else 0,
            "task_vectors": {tid: tv.to_dict() for tid, tv in self.task_vectors.items()},
            "task_clusters": self.task_clusters
        }
        
        return state


def test_task_encoder():
    """测试任务编码器"""
    print("🧪 测试任务编码器...")
    
    # 创建编码器
    encoder = TaskEncoder(encoding_dim=32)
    
    # 创建测试任务数据
    task_data_list = [
        {
            "task_id": "task_1",
            "task_type": "time_series",
            "data_size": 5000,
            "feature_count": 10,
            "complexity_score": 0.7,
            "best_accuracy": 0.85,
            "domain": "lottery"
        },
        {
            "task_id": "task_2",
            "task_type": "classification",
            "data_size": 3000,
            "feature_count": 15,
            "complexity_score": 0.6,
            "best_accuracy": 0.82,
            "domain": "general"
        },
        {
            "task_id": "task_3",
            "task_type": "time_series",
            "data_size": 8000,
            "feature_count": 12,
            "complexity_score": 0.8,
            "best_accuracy": 0.87,
            "domain": "lottery"
        }
    ]
    
    # 编码任务
    for task_data in task_data_list:
        task_vector = encoder.encode_task(task_data)
        print(f"📊 任务 {task_vector.task_id} 编码维度: {len(task_vector.feature_vector)}")
    
    # 训练编码器
    encoder.fit_encoder(task_data_list)
    print(f"✅ 编码器训练完成，拟合状态: {encoder.is_fitted}")
    
    # 寻找相似任务
    similar_tasks = encoder.find_similar_tasks("task_1", top_k=2)
    print(f"🔍 与task_1相似的任务: {similar_tasks}")
    
    # 获取聚类信息
    for task_id in encoder.task_vectors.keys():
        cluster = encoder.get_task_cluster(task_id)
        print(f"📊 任务 {task_id} 聚类: {cluster}")
    
    # 导出状态
    state = encoder.export_encoder_state()
    print(f"📋 编码器状态: 任务数量={state['task_count']}, 聚类数量={state['cluster_count']}")
    
    print("✅ 任务编码器测试完成！")


if __name__ == "__main__":
    test_task_encoder()
