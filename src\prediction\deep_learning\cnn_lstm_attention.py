"""
CNN-LSTM+注意力网络模型
基于参考文档的成功架构实现，目标复现并超越75.6%准确率
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional
import math


class PositionalEncoding(nn.Module):
    """位置编码模块"""
    
    def __init__(self, d_model: int, max_len: int = 5000):
        super().__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        return x + self.pe[:x.size(0), :]


class MultiScaleCNN(nn.Module):
    """多尺度CNN特征提取层"""
    
    def __init__(self, input_dim: int, output_dim: int = 64):
        super().__init__()
        
        # 多尺度卷积核
        self.conv_layers = nn.ModuleList([
            nn.Sequential(
                nn.Conv1d(input_dim, 32, kernel_size=k, padding=k//2),
                nn.BatchNorm1d(32),
                nn.ReLU(),
                nn.Dropout(0.1)
            ) for k in [3, 5, 7, 9]  # 多尺度卷积核
        ])
        
        # 特征融合层
        self.feature_fusion = nn.Sequential(
            nn.Conv1d(128, output_dim, kernel_size=1),  # 32*4=128
            nn.BatchNorm1d(output_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 全局平均池化
        self.global_avg_pool = nn.AdaptiveAvgPool1d(1)
        
    def forward(self, x):
        """
        Args:
            x: (batch_size, input_dim, seq_len)
        Returns:
            (batch_size, output_dim, seq_len)
        """
        # 多尺度特征提取
        conv_features = []
        for conv in self.conv_layers:
            feat = conv(x)
            conv_features.append(feat)
        
        # 特征融合
        fused_features = torch.cat(conv_features, dim=1)
        output = self.feature_fusion(fused_features)
        
        return output


class CNNLSTMAttentionPredictor(nn.Module):
    """
    CNN-LSTM+多头注意力预测网络
    基于参考文档的成功架构，目标达到75.6%基准准确率
    """
    
    def __init__(
        self,
        input_dim: int = 50,
        cnn_output_dim: int = 64,
        lstm_hidden_dim: int = 128,
        num_attention_heads: int = 8,
        num_classes: int = 1000,
        dropout_rate: float = 0.2,
        num_lstm_layers: int = 2
    ):
        super().__init__()
        
        self.input_dim = input_dim
        self.cnn_output_dim = cnn_output_dim
        self.lstm_hidden_dim = lstm_hidden_dim
        self.num_classes = num_classes
        
        # 多尺度CNN特征提取
        self.multi_scale_cnn = MultiScaleCNN(input_dim, cnn_output_dim)
        
        # 双向LSTM
        self.lstm = nn.LSTM(
            input_size=cnn_output_dim,
            hidden_size=lstm_hidden_dim,
            num_layers=num_lstm_layers,
            batch_first=True,
            bidirectional=True,
            dropout=dropout_rate if num_lstm_layers > 1 else 0
        )
        
        # 位置编码
        self.pos_encoding = PositionalEncoding(lstm_hidden_dim * 2)
        
        # 多头自注意力机制
        self.self_attention = nn.MultiheadAttention(
            embed_dim=lstm_hidden_dim * 2,
            num_heads=num_attention_heads,
            dropout=dropout_rate,
            batch_first=True
        )
        
        # 注意力权重计算
        self.attention_weights = nn.Sequential(
            nn.Linear(lstm_hidden_dim * 2, lstm_hidden_dim),
            nn.Tanh(),
            nn.Linear(lstm_hidden_dim, 1),
            nn.Softmax(dim=1)
        )
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.Linear(lstm_hidden_dim * 2, 256),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(128, num_classes)
        )
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化模型权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
            elif isinstance(module, nn.Conv1d):
                nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
            elif isinstance(module, nn.BatchNorm1d):
                nn.init.constant_(module.weight, 1)
                nn.init.constant_(module.bias, 0)
    
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: (batch_size, seq_len, input_dim)
            
        Returns:
            (batch_size, num_classes)
        """
        batch_size, seq_len, input_dim = x.shape
        
        # CNN特征提取 (batch_size, seq_len, input_dim) -> (batch_size, input_dim, seq_len)
        x_cnn = x.transpose(1, 2)
        cnn_features = self.multi_scale_cnn(x_cnn)  # (batch_size, cnn_output_dim, seq_len)
        
        # 转换回LSTM输入格式 (batch_size, seq_len, cnn_output_dim)
        cnn_features = cnn_features.transpose(1, 2)
        
        # 双向LSTM
        lstm_out, (hidden, cell) = self.lstm(cnn_features)  # (batch_size, seq_len, lstm_hidden_dim * 2)
        
        # 位置编码
        lstm_out = lstm_out.transpose(0, 1)  # (seq_len, batch_size, lstm_hidden_dim * 2)
        lstm_out = self.pos_encoding(lstm_out)
        lstm_out = lstm_out.transpose(0, 1)  # (batch_size, seq_len, lstm_hidden_dim * 2)
        
        # 多头自注意力
        attn_out, attn_weights = self.self_attention(lstm_out, lstm_out, lstm_out)
        
        # 残差连接
        attn_out = attn_out + lstm_out
        
        # 全局注意力池化
        attention_weights = self.attention_weights(attn_out)  # (batch_size, seq_len, 1)
        pooled = torch.sum(attn_out * attention_weights, dim=1)  # (batch_size, lstm_hidden_dim * 2)
        
        # 分类预测
        output = self.classifier(pooled)  # (batch_size, num_classes)
        
        return output
    
    def predict_probabilities(self, x):
        """预测概率分布"""
        logits = self.forward(x)
        probabilities = F.softmax(logits, dim=1)
        return probabilities
    
    def predict_top_k(self, x, k: int = 10):
        """预测Top-K结果"""
        probabilities = self.predict_probabilities(x)
        top_k_probs, top_k_indices = torch.topk(probabilities, k, dim=1)
        return top_k_indices, top_k_probs
    
    def get_feature_importance(self, x):
        """获取特征重要性"""
        # 启用梯度计算
        x.requires_grad_(True)
        
        # 前向传播
        output = self.forward(x)
        
        # 计算梯度
        output.sum().backward()
        
        # 特征重要性 = 梯度的绝对值
        importance = torch.abs(x.grad).mean(dim=0)  # (seq_len, input_dim)
        
        return importance.detach()


class ModelConfig:
    """模型配置类"""
    
    def __init__(self):
        # 模型架构参数
        self.input_dim = 50  # 特征维度
        self.cnn_output_dim = 64
        self.lstm_hidden_dim = 128
        self.num_attention_heads = 8
        self.num_classes = 1000  # 000-999
        self.dropout_rate = 0.2
        self.num_lstm_layers = 2
        
        # 训练参数
        self.learning_rate = 0.001
        self.batch_size = 32
        self.num_epochs = 100
        self.early_stopping_patience = 10
        self.weight_decay = 1e-4
        
        # 数据参数
        self.sequence_length = 20  # 输入序列长度
        self.train_ratio = 0.7
        self.val_ratio = 0.2
        self.test_ratio = 0.1
        
        # 设备配置
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 模型保存路径
        self.model_save_path = 'models/cnn_lstm_attention.pth'
        self.checkpoint_path = 'models/checkpoints/'


def test_model():
    """测试模型架构"""
    print("=== 测试CNN-LSTM+注意力模型 ===")
    
    config = ModelConfig()
    model = CNNLSTMAttentionPredictor(
        input_dim=config.input_dim,
        cnn_output_dim=config.cnn_output_dim,
        lstm_hidden_dim=config.lstm_hidden_dim,
        num_attention_heads=config.num_attention_heads,
        num_classes=config.num_classes,
        dropout_rate=config.dropout_rate,
        num_lstm_layers=config.num_lstm_layers
    )
    
    # 创建测试数据
    batch_size = 4
    seq_len = 20
    input_dim = 50
    
    test_input = torch.randn(batch_size, seq_len, input_dim)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    print(f"输入形状: {test_input.shape}")
    
    # 前向传播测试
    with torch.no_grad():
        output = model(test_input)
        print(f"输出形状: {output.shape}")
        
        # 预测概率
        probs = model.predict_probabilities(test_input)
        print(f"概率分布形状: {probs.shape}")
        print(f"概率和: {probs.sum(dim=1)}")
        
        # Top-K预测
        top_k_indices, top_k_probs = model.predict_top_k(test_input, k=10)
        print(f"Top-10预测形状: {top_k_indices.shape}")
        print(f"Top-10概率形状: {top_k_probs.shape}")
    
    print("✓ 模型架构测试成功!")
    return model


if __name__ == "__main__":
    test_model()
