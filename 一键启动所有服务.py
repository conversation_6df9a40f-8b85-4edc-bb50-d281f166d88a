#!/usr/bin/env python3
"""
一键启动福彩3D预测系统所有服务
按正确顺序启动API、调度器和界面
"""

import subprocess
import sys
import time
import os
from pathlib import Path

def check_port_available(port):
    """检查端口是否可用"""
    import socket
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('127.0.0.1', port))
            return True
    except:
        return False

def start_service(command, service_name, wait_time=3):
    """启动服务"""
    print(f"🚀 启动{service_name}...")
    
    try:
        # 启动服务
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待启动
        time.sleep(wait_time)
        
        # 检查进程状态
        if process.poll() is None:
            print(f"✅ {service_name}启动成功")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"❌ {service_name}启动失败")
            if stdout:
                print(f"输出: {stdout}")
            if stderr:
                print(f"错误: {stderr}")
            return None
            
    except Exception as e:
        print(f"❌ {service_name}启动异常: {e}")
        return None

def main():
    """主函数"""
    print("🎯 福彩3D预测系统 - 一键启动所有服务")
    print("=" * 60)
    
    # 检查工作目录
    project_dir = Path(__file__).parent
    os.chdir(project_dir)
    print(f"📁 工作目录: {project_dir}")
    
    # 检查端口可用性
    print("\n🔍 检查端口可用性...")
    if not check_port_available(8888):
        print("❌ 端口8888被占用，请先停止相关服务")
        return
    
    if not check_port_available(8501):
        print("❌ 端口8501被占用，请先停止相关服务")
        return
    
    print("✅ 端口检查通过")
    
    processes = []
    
    try:
        # 1. 启动API服务
        print("\n" + "="*60)
        api_process = start_service(
            "python start_production_api.py",
            "API服务",
            wait_time=5
        )
        if api_process:
            processes.append(("API服务", api_process))
        else:
            print("❌ API服务启动失败，停止后续启动")
            return
        
        # 2. 启动APScheduler调度器
        print("\n" + "="*60)
        scheduler_process = start_service(
            "python scripts/start_scheduler.py --daemon",
            "APScheduler调度器",
            wait_time=3
        )
        if scheduler_process:
            processes.append(("APScheduler调度器", scheduler_process))
        else:
            print("⚠️ APScheduler调度器启动失败，但继续启动界面")
        
        # 3. 启动Streamlit界面
        print("\n" + "="*60)
        streamlit_process = start_service(
            "python start_streamlit.py",
            "Streamlit界面",
            wait_time=5
        )
        if streamlit_process:
            processes.append(("Streamlit界面", streamlit_process))
        
        # 显示启动结果
        print("\n" + "="*60)
        print("📊 服务启动状态:")
        for name, process in processes:
            status = "运行中" if process.poll() is None else "已停止"
            print(f"   {name}: {status}")
        
        print("\n🌐 访问地址:")
        print("   API服务: http://127.0.0.1:8888")
        print("   API文档: http://127.0.0.1:8888/docs")
        print("   Streamlit界面: http://127.0.0.1:8501")
        
        print("\n🕐 APScheduler定时任务:")
        print("   数据更新: 每天21:30")
        print("   文件清理: 每周日02:00")
        print("   日志清理: 每天03:00")
        
        print("\n📋 管理命令:")
        print("   查看调度器状态: python scripts/start_scheduler.py --status")
        print("   停止所有服务: 按Ctrl+C")
        
        print("\n✅ 所有服务启动完成！")
        print("💡 提示: 保持此窗口打开以维持服务运行")
        
        # 等待用户中断
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n⚠️ 收到停止信号，正在关闭所有服务...")
            
            # 停止所有进程
            for name, process in processes:
                if process.poll() is None:
                    print(f"🛑 停止{name}...")
                    process.terminate()
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        process.kill()
            
            print("✅ 所有服务已停止")
    
    except Exception as e:
        print(f"❌ 启动过程出错: {e}")
        
        # 清理进程
        for name, process in processes:
            if process.poll() is None:
                process.terminate()

if __name__ == "__main__":
    main()
