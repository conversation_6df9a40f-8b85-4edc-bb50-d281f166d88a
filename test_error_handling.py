#!/usr/bin/env python3
"""
错误处理功能测试脚本
验证错误处理机制是否正常工作
"""

import sys
import os
import traceback

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_error_handler_import():
    """测试错误处理组件导入"""
    try:
        from src.ui.components.error_handler import ErrorHandler, <PERSON>rror<PERSON>ogger, ErrorRecovery, SmartErrorHandler
        print("✅ ErrorHandler组件导入成功")
        return True
    except Exception as e:
        print(f"❌ ErrorHandler组件导入失败: {e}")
        traceback.print_exc()
        return False

def test_error_config_import():
    """测试错误配置组件导入"""
    try:
        from src.ui.components.error_config import ErrorType, ErrorSeverity, get_error_config
        print("✅ ErrorConfig组件导入成功")
        
        # 测试配置获取
        config = get_error_config(ErrorType.NETWORK_ERROR)
        print(f"✅ 网络错误配置获取成功: {config['title']}")
        return True
    except Exception as e:
        print(f"❌ ErrorConfig组件导入失败: {e}")
        traceback.print_exc()
        return False

def test_error_middleware_import():
    """测试错误中间件组件导入"""
    try:
        from src.ui.components.error_middleware import handle_page_errors, handle_api_errors, handle_data_errors
        print("✅ ErrorMiddleware组件导入成功")
        return True
    except Exception as e:
        print(f"❌ ErrorMiddleware组件导入失败: {e}")
        traceback.print_exc()
        return False

def test_error_logger():
    """测试错误日志功能"""
    try:
        from src.ui.components.error_handler import ErrorLogger
        
        # 创建测试错误
        test_error = ValueError("这是一个测试错误")
        
        # 记录错误
        error_info = ErrorLogger.log_error(test_error, {
            "test_context": "错误处理测试",
            "function": "test_error_logger"
        })
        
        print("✅ 错误日志记录成功")
        print(f"   错误类型: {error_info['error_type']}")
        print(f"   错误消息: {error_info['error_message']}")
        return True
    except Exception as e:
        print(f"❌ 错误日志测试失败: {e}")
        traceback.print_exc()
        return False

def test_decorator_functionality():
    """测试装饰器功能"""
    try:
        from src.ui.components.error_middleware import handle_page_errors, handle_api_errors
        
        # 测试页面错误装饰器
        @handle_page_errors("测试页面")
        def test_page_function():
            return "页面函数正常执行"
        
        result = test_page_function()
        if result:
            print("✅ 页面错误装饰器测试成功")
        
        # 测试API错误装饰器
        @handle_api_errors("http://test.api", retry_on_failure=False)
        def test_api_function():
            return "API函数正常执行"
        
        result = test_api_function()
        if result:
            print("✅ API错误装饰器测试成功")
        
        return True
    except Exception as e:
        print(f"❌ 装饰器功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_smart_error_handler():
    """测试智能错误处理"""
    try:
        from src.ui.components.error_handler import SmartErrorHandler
        
        # 测试装饰器
        @SmartErrorHandler.handle_exception
        def test_function_with_error():
            # 这个函数会正常执行，不会抛出异常
            return "正常执行"
        
        result = test_function_with_error()
        if result:
            print("✅ 智能错误处理装饰器测试成功")
        
        return True
    except Exception as e:
        print(f"❌ 智能错误处理测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 开始错误处理功能测试")
    print("=" * 50)
    
    tests = [
        ("错误处理组件导入", test_error_handler_import),
        ("错误配置组件导入", test_error_config_import),
        ("错误中间件组件导入", test_error_middleware_import),
        ("错误日志功能", test_error_logger),
        ("装饰器功能", test_decorator_functionality),
        ("智能错误处理", test_smart_error_handler),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 测试: {test_name}")
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有错误处理功能测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
