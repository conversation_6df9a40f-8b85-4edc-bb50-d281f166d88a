#!/usr/bin/env python3
"""
测试修复后的TrendModelWrapper
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_trend_wrapper():
    """测试TrendModelWrapper的修复"""
    try:
        print("🚀 开始测试TrendModelWrapper修复...")
        
        # 导入修复后的TrendModelWrapper
        from src.model_library.wrappers.trend_wrapper import TrendModelWrapper
        
        print("✅ 成功导入TrendModelWrapper")
        
        # 创建实例
        model = TrendModelWrapper()
        print("✅ 成功创建TrendModelWrapper实例")
        
        # 检查模型信息
        info = model.get_info()
        print(f"📋 模型信息: {info.name} (ID: {info.model_id})")
        
        # 检查状态
        status = model.get_status()
        print(f"📊 模型状态:")
        print(f"  🆔 model_id: {status.model_id}")
        print(f"  📈 status: {status.status}")
        print(f"  💾 data_ready: {status.data_ready}")
        print(f"  🔧 features_ready: {status.features_ready}")
        print(f"  🎯 trained: {status.trained}")
        print(f"  📏 training_data_size: {status.training_data_size}")
        
        # 测试训练（使用模拟数据）
        print("\n🧪 测试训练功能...")
        mock_data = []
        for i in range(100):  # 创建100条模拟数据
            mock_data.append({
                'period': f'2025{i:03d}',
                'date': f'2025-01-{(i%30)+1:02d}',
                'numbers': f'{i%10}{(i+1)%10}{(i+2)%10}'
            })
        
        print(f"📊 使用{len(mock_data)}条模拟数据进行训练...")
        result = model.train(mock_data)
        
        print(f"🎯 训练结果:")
        print(f"  ✅ success: {result.success}")
        print(f"  📏 training_data_size: {result.training_data_size}")
        print(f"  ⏱️ training_duration: {result.training_duration:.2f}s")
        if result.error_message:
            print(f"  ❗ error_message: {result.error_message}")
        
        # 再次检查状态
        print("\n📊 训练后状态:")
        status_after = model.get_status()
        print(f"  📈 status: {status_after.status}")
        print(f"  🎯 trained: {status_after.trained}")
        print(f"  🔧 features_ready: {status_after.features_ready}")
        
        return result.success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_trend_wrapper()
    if success:
        print("\n🎉 TrendModelWrapper修复测试成功！")
    else:
        print("\n💥 TrendModelWrapper修复测试失败！")
    sys.exit(0 if success else 1)
