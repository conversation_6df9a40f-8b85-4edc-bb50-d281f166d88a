"""
训练-测试分割功能测试

验证时间序列分割的正确性和无未来信息泄露
"""

import sys
import os
sys.path.append('src')

from prediction.model_validation import MarkovCrossValidator
from prediction.markov_validator import MarkovModelValidator
import json
from datetime import datetime


def test_time_series_split():
    """测试时间序列分割功能"""
    print("🔄 测试时间序列分割功能...")
    
    validator = MarkovCrossValidator()
    
    # 加载测试数据
    data = validator.load_validation_data(limit=1000)
    if not data:
        print("❌ 无法加载测试数据")
        return False
    
    print(f"✅ 加载了 {len(data)} 条测试数据")
    
    # 测试不同的分割比例
    test_ratios = [0.1, 0.2, 0.3]
    
    for ratio in test_ratios:
        print(f"\n📊 测试分割比例: {ratio}")
        
        train_data, test_data = validator.time_series_split(data, test_ratio=ratio)
        
        # 验证分割比例
        expected_test_size = int(len(data) * ratio)
        expected_train_size = len(data) - expected_test_size
        
        print(f"   预期训练集大小: {expected_train_size}")
        print(f"   实际训练集大小: {len(train_data)}")
        print(f"   预期测试集大小: {expected_test_size}")
        print(f"   实际测试集大小: {len(test_data)}")
        
        # 验证时间顺序
        if train_data and test_data:
            last_train_date = train_data[-1]['date']
            first_test_date = test_data[0]['date']
            
            print(f"   最后训练日期: {last_train_date}")
            print(f"   首个测试日期: {first_test_date}")
            
            # 验证无未来信息泄露
            if last_train_date <= first_test_date:
                print("   ✅ 时间顺序正确，无未来信息泄露")
            else:
                print("   ❌ 时间顺序错误，存在未来信息泄露")
                return False
        
        # 验证数据完整性
        total_recovered = len(train_data) + len(test_data)
        if total_recovered == len(data):
            print("   ✅ 数据完整性验证通过")
        else:
            print(f"   ❌ 数据丢失: 原始{len(data)}条，恢复{total_recovered}条")
            return False
    
    return True


def test_train_test_validation():
    """测试基于训练-测试分割的模型验证"""
    print("\n🧠 测试基于训练-测试分割的模型验证...")
    
    validator = MarkovModelValidator()
    
    # 加载数据
    cross_validator = validator.cross_validator
    data = cross_validator.load_validation_data(limit=800)
    
    if not data:
        print("❌ 无法加载验证数据")
        return False
    
    print(f"✅ 加载了 {len(data)} 条验证数据")
    
    # 执行训练-测试分割
    train_data, test_data = cross_validator.time_series_split(data, test_ratio=0.2)
    
    print(f"📊 分割结果:")
    print(f"   训练集: {len(train_data)} 条")
    print(f"   测试集: {len(test_data)} 条")
    
    # 验证分割的时间一致性
    if train_data and test_data:
        train_periods = [int(record['period']) for record in train_data if record['period'].isdigit()]
        test_periods = [int(record['period']) for record in test_data if record['period'].isdigit()]
        
        if train_periods and test_periods:
            max_train_period = max(train_periods)
            min_test_period = min(test_periods)
            
            print(f"   最大训练期号: {max_train_period}")
            print(f"   最小测试期号: {min_test_period}")
            
            if max_train_period < min_test_period:
                print("   ✅ 期号顺序正确")
            else:
                print("   ❌ 期号顺序错误")
                return False
    
    # 模拟模型训练和测试过程
    print("\n🔄 模拟模型训练和测试...")
    
    try:
        from prediction.pattern_prediction import PatternPredictor
        
        # 创建预测器
        predictor = PatternPredictor(
            transition_window_size=500,
            probability_window_size=250,
            smoothing_alpha=1.0
        )
        
        # 训练模型（使用训练集）
        print("   🔧 训练模型...")
        train_result = predictor.train_model()
        
        if train_result.get('success'):
            print("   ✅ 模型训练成功")
        else:
            print(f"   ❌ 模型训练失败: {train_result}")
            return False
        
        # 在测试集上进行预测
        print("   🎯 在测试集上预测...")
        predictions = []
        actuals = []
        
        # 只预测前10个测试样本（节省时间）
        test_sample = test_data[:10]
        
        for i, record in enumerate(test_sample):
            actual = record.get('numbers', '')
            if not actual or len(actual) != 3:
                continue
            
            try:
                # 使用训练数据进行预测
                pattern_predictions = predictor.predict_next_patterns(train_data)
                candidates = predictor.generate_candidate_numbers(pattern_predictions, top_k=3)
                
                if candidates:
                    prediction = candidates[0]['numbers']
                    predictions.append(prediction)
                    actuals.append(actual)
                    print(f"     样本{i+1}: 预测={prediction}, 实际={actual}")
                
            except Exception as e:
                print(f"     样本{i+1}: 预测失败 - {e}")
        
        # 计算测试集性能
        if predictions and actuals:
            accuracy_metrics = cross_validator.calculate_prediction_accuracy(predictions, actuals)
            diversity_metrics = cross_validator.calculate_diversity_metrics(predictions)
            
            print(f"\n📈 测试集性能:")
            print(f"   预测数量: {len(predictions)}")
            print(f"   准确率: {accuracy_metrics['exact_match']:.4f}")
            print(f"   数字准确率: {accuracy_metrics['digit_accuracy']:.4f}")
            print(f"   多样性指数: {diversity_metrics['simpson_diversity']:.4f}")
            
            return True
        else:
            print("   ❌ 无有效预测结果")
            return False
            
    except Exception as e:
        print(f"   ❌ 模型验证失败: {e}")
        return False


def test_data_leakage_prevention():
    """测试数据泄露防护"""
    print("\n🔒 测试数据泄露防护...")
    
    validator = MarkovCrossValidator()
    
    # 创建有明确时间顺序的测试数据
    test_data = []
    for i in range(100):
        test_data.append({
            'period': f"2024{i:03d}",
            'date': f"2024-01-{(i%30)+1:02d}",
            'numbers': f"{i%10}{(i+1)%10}{(i+2)%10}"
        })
    
    # 执行分割
    train_data, test_data_split = validator.time_series_split(test_data, test_ratio=0.2)
    
    # 验证训练集中没有测试集的数据
    train_periods = set(record['period'] for record in train_data)
    test_periods = set(record['period'] for record in test_data_split)
    
    overlap = train_periods & test_periods
    
    if not overlap:
        print("   ✅ 无数据重叠，防泄露机制有效")
    else:
        print(f"   ❌ 发现数据重叠: {overlap}")
        return False
    
    # 验证时间顺序
    train_periods_int = sorted([int(p[4:]) for p in train_periods])
    test_periods_int = sorted([int(p[4:]) for p in test_periods])
    
    if train_periods_int and test_periods_int:
        max_train = max(train_periods_int)
        min_test = min(test_periods_int)
        
        if max_train < min_test:
            print("   ✅ 时间顺序正确")
            return True
        else:
            print(f"   ❌ 时间顺序错误: 最大训练期号{max_train} >= 最小测试期号{min_test}")
            return False
    
    return True


def main():
    """主测试函数"""
    print("🚀 训练-测试分割功能测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("时间序列分割", test_time_series_split()))
    test_results.append(("训练-测试验证", test_train_test_validation()))
    test_results.append(("数据泄露防护", test_data_leakage_prevention()))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总:")
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed_tests += 1
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！训练-测试分割功能正常！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步调试")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
