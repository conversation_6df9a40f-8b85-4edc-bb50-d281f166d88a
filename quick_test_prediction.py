#!/usr/bin/env python3
"""
快速测试预测功能
"""

import requests
import json

def test_prediction():
    """测试预测功能"""
    base_url = "http://127.0.0.1:8888"
    
    try:
        print("🧪 测试预测功能...")
        
        # 1. 获取预测结果
        print("\n1. 获取预测结果...")
        response = requests.get(f"{base_url}/api/v1/prediction/predict", timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功")
            print(f"   时间戳: {data.get('timestamp', 'N/A')}")
            
            predictions = data.get('predictions', {})
            for name, pred in predictions.items():
                print(f"\n   预测器: {name}")
                print(f"   成功: {pred.get('success', False)}")
                if pred.get('success'):
                    print(f"   预测号码: {pred.get('predictions', [])}")
                    print(f"   置信度: {pred.get('confidence', 0.0):.3f}")
                    print(f"   方法: {pred.get('method', 'N/A')}")
                    
                    analysis = pred.get('analysis', {})
                    if analysis:
                        print(f"   热号: {analysis.get('hot_numbers', [])}")
                        print(f"   冷号: {analysis.get('cold_numbers', [])}")
                        print(f"   推荐和值: {analysis.get('recommended_sums', [])}")
                else:
                    print(f"   错误: {pred.get('error', 'N/A')}")
            
            summary = data.get('summary', {})
            if summary:
                print(f"\n   摘要:")
                print(f"   成功预测器: {summary.get('successful_predictors', 0)}")
                print(f"   平均置信度: {summary.get('average_confidence', 0.0):.3f}")
                print(f"   总预测数: {summary.get('total_predictions', 0)}")
        else:
            print(f"   ❌ 失败 - 状态码: {response.status_code}")
            print(f"   响应: {response.text[:200]}")
        
        # 2. 获取预测器信息
        print("\n2. 获取预测器信息...")
        response = requests.get(f"{base_url}/api/v1/prediction/info", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功")
            print(f"   预测器总数: {data.get('total_predictors', 'N/A')}")
            predictors = data.get('predictors', {})
            for name, info in predictors.items():
                print(f"   - {name}: 已训练={info.get('is_trained', False)}")
        
        print("\n📋 预测功能测试完成")
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    test_prediction()
