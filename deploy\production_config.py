#!/usr/bin/env python3
"""
生产环境配置

生产环境的配置参数和环境变量设置
"""

import os
from pathlib import Path

class ProductionConfig:
    """生产环境配置类"""
    
    # 基础配置
    DEBUG = False
    TESTING = False
    ENVIRONMENT = "production"
    
    # 服务配置
    API_HOST = os.getenv("API_HOST", "127.0.0.1")
    API_PORT = int(os.getenv("API_PORT", "8000"))
    STREAMLIT_HOST = os.getenv("STREAMLIT_HOST", "127.0.0.1")
    STREAMLIT_PORT = int(os.getenv("STREAMLIT_PORT", "8501"))
    
    # 数据库配置
    DATABASE_PATH = os.getenv("DATABASE_PATH", "lottery_data.db")
    DATABASE_TIMEOUT = int(os.getenv("DATABASE_TIMEOUT", "30"))
    DATABASE_WAL_MODE = os.getenv("DATABASE_WAL_MODE", "true").lower() == "true"
    DATABASE_CACHE_SIZE = int(os.getenv("DATABASE_CACHE_SIZE", "10000"))
    
    # 数据源配置
    DATA_SOURCE_URL = os.getenv("DATA_SOURCE_URL", "https://data.17500.cn/3d_asc.txt")
    DATA_UPDATE_INTERVAL = int(os.getenv("DATA_UPDATE_INTERVAL", "3600"))  # 1小时
    DATA_RETRY_ATTEMPTS = int(os.getenv("DATA_RETRY_ATTEMPTS", "3"))
    DATA_RETRY_DELAY = int(os.getenv("DATA_RETRY_DELAY", "5"))
    
    # 缓存配置
    CACHE_DIR = os.getenv("CACHE_DIR", "data/cache")
    CACHE_MAX_SIZE = int(os.getenv("CACHE_MAX_SIZE", "1000"))  # MB
    CACHE_TTL = int(os.getenv("CACHE_TTL", "3600"))  # 1小时
    
    # 日志配置
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    LOG_DIR = os.getenv("LOG_DIR", "logs")
    LOG_MAX_SIZE = int(os.getenv("LOG_MAX_SIZE", "100"))  # MB
    LOG_BACKUP_COUNT = int(os.getenv("LOG_BACKUP_COUNT", "5"))
    
    # 监控配置
    MONITORING_ENABLED = os.getenv("MONITORING_ENABLED", "true").lower() == "true"
    HEALTH_CHECK_INTERVAL = int(os.getenv("HEALTH_CHECK_INTERVAL", "60"))  # 秒
    PERFORMANCE_MONITORING = os.getenv("PERFORMANCE_MONITORING", "true").lower() == "true"
    
    # WebSocket配置
    WEBSOCKET_HEARTBEAT_INTERVAL = int(os.getenv("WEBSOCKET_HEARTBEAT_INTERVAL", "30"))
    WEBSOCKET_TIMEOUT = int(os.getenv("WEBSOCKET_TIMEOUT", "300"))
    WEBSOCKET_MAX_CONNECTIONS = int(os.getenv("WEBSOCKET_MAX_CONNECTIONS", "100"))
    
    # 安全配置
    ALLOWED_HOSTS = os.getenv("ALLOWED_HOSTS", "localhost,127.0.0.1").split(",")
    CORS_ORIGINS = os.getenv("CORS_ORIGINS", "http://localhost:8501").split(",")
    API_RATE_LIMIT = int(os.getenv("API_RATE_LIMIT", "100"))  # 每分钟请求数
    
    # 性能配置
    MAX_WORKERS = int(os.getenv("MAX_WORKERS", "4"))
    WORKER_TIMEOUT = int(os.getenv("WORKER_TIMEOUT", "30"))
    MAX_CONNECTIONS = int(os.getenv("MAX_CONNECTIONS", "1000"))
    
    # 备份配置
    BACKUP_ENABLED = os.getenv("BACKUP_ENABLED", "true").lower() == "true"
    BACKUP_DIR = os.getenv("BACKUP_DIR", "backup")
    BACKUP_INTERVAL = int(os.getenv("BACKUP_INTERVAL", "86400"))  # 24小时
    BACKUP_RETENTION_DAYS = int(os.getenv("BACKUP_RETENTION_DAYS", "30"))
    
    @classmethod
    def validate_config(cls):
        """验证配置参数"""
        errors = []
        
        # 检查必需的目录
        required_dirs = [cls.CACHE_DIR, cls.LOG_DIR]
        if cls.BACKUP_ENABLED:
            required_dirs.append(cls.BACKUP_DIR)
        
        for dir_path in required_dirs:
            if not Path(dir_path).exists():
                try:
                    Path(dir_path).mkdir(parents=True, exist_ok=True)
                except Exception as e:
                    errors.append(f"无法创建目录 {dir_path}: {e}")
        
        # 检查数据库文件
        if not Path(cls.DATABASE_PATH).exists():
            errors.append(f"数据库文件不存在: {cls.DATABASE_PATH}")
        
        # 检查端口范围
        if not (1024 <= cls.API_PORT <= 65535):
            errors.append(f"API端口超出范围: {cls.API_PORT}")
        
        if not (1024 <= cls.STREAMLIT_PORT <= 65535):
            errors.append(f"Streamlit端口超出范围: {cls.STREAMLIT_PORT}")
        
        # 检查数值范围
        if cls.DATABASE_TIMEOUT <= 0:
            errors.append("数据库超时时间必须大于0")
        
        if cls.MAX_WORKERS <= 0:
            errors.append("工作进程数必须大于0")
        
        return errors
    
    @classmethod
    def get_environment_template(cls):
        """获取环境变量模板"""
        return """
# 福彩3D预测系统 - 生产环境配置

# 服务配置
API_HOST=127.0.0.1
API_PORT=8000
STREAMLIT_HOST=127.0.0.1
STREAMLIT_PORT=8501

# 数据库配置
DATABASE_PATH=lottery_data.db
DATABASE_TIMEOUT=30
DATABASE_WAL_MODE=true
DATABASE_CACHE_SIZE=10000

# 数据源配置
DATA_SOURCE_URL=https://data.17500.cn/3d_asc.txt
DATA_UPDATE_INTERVAL=3600
DATA_RETRY_ATTEMPTS=3
DATA_RETRY_DELAY=5

# 缓存配置
CACHE_DIR=data/cache
CACHE_MAX_SIZE=1000
CACHE_TTL=3600

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=logs
LOG_MAX_SIZE=100
LOG_BACKUP_COUNT=5

# 监控配置
MONITORING_ENABLED=true
HEALTH_CHECK_INTERVAL=60
PERFORMANCE_MONITORING=true

# WebSocket配置
WEBSOCKET_HEARTBEAT_INTERVAL=30
WEBSOCKET_TIMEOUT=300
WEBSOCKET_MAX_CONNECTIONS=100

# 安全配置
ALLOWED_HOSTS=localhost,127.0.0.1
CORS_ORIGINS=http://localhost:8501
API_RATE_LIMIT=100

# 性能配置
MAX_WORKERS=4
WORKER_TIMEOUT=30
MAX_CONNECTIONS=1000

# 备份配置
BACKUP_ENABLED=true
BACKUP_DIR=backup
BACKUP_INTERVAL=86400
BACKUP_RETENTION_DAYS=30
"""

def load_production_config():
    """加载生产环境配置"""
    # 验证配置
    errors = ProductionConfig.validate_config()
    if errors:
        raise ValueError(f"配置验证失败: {'; '.join(errors)}")
    
    return ProductionConfig

def create_env_file():
    """创建环境变量文件"""
    env_content = ProductionConfig.get_environment_template()
    
    with open('.env.production', 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print("✅ 生产环境配置文件已创建: .env.production")

if __name__ == "__main__":
    # 创建环境变量文件
    create_env_file()
    
    # 验证配置
    try:
        config = load_production_config()
        print("✅ 生产环境配置验证通过")
    except Exception as e:
        print(f"❌ 生产环境配置验证失败: {e}")
