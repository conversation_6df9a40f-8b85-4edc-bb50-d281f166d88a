#!/usr/bin/env python3
"""
快速测试Polars引擎修复
"""

import sys
sys.path.append('src')

from core.polars_engine import PolarsEngine
from data.parser import DataParser

def test_polars_quick():
    """快速测试Polars引擎"""
    print("🚀 快速测试Polars引擎...")
    
    try:
        # 1. 初始化引擎
        engine = PolarsEngine()
        print("✅ Polars引擎初始化成功")
        
        # 2. 加载数据
        with open('data/raw/3d_data_20250714_144231.txt', 'r', encoding='utf-8') as f:
            raw_data = f.read()
        
        parser = DataParser()
        records, quality_report = parser.parse_data(raw_data)
        
        engine.load_from_records(records)
        print(f"✅ 数据加载成功: {len(records)} 条记录")
        
        # 3. 测试基础统计
        stats = engine.get_basic_stats()
        print(f"✅ 基础统计: 总记录数 {stats['total_records']}")
        
        # 4. 测试修复后的查询功能
        recent_data = engine.query_by_date_range("2025-01-01", "2025-07-13")
        print(f"✅ 日期查询修复成功: 2025年数据 {len(recent_data)} 条")
        
        # 5. 测试和值查询
        sum_range_data = engine.query_by_sum_range(10, 15)
        print(f"✅ 和值查询成功: 和值10-15范围 {len(sum_range_data)} 条")
        
        # 6. 测试频率分析
        freq_analysis = engine.get_frequency_analysis("hundreds")
        if "hundreds" in freq_analysis:
            print(f"✅ 频率分析成功: 百位数字频率统计完成")
        
        # 7. 测试性能
        import time
        start_time = time.time()
        for i in range(10):
            engine.get_basic_stats()
        elapsed = time.time() - start_time
        print(f"✅ 性能测试: 10次基础统计耗时 {elapsed:.3f}秒 (平均 {elapsed/10*1000:.1f}ms)")
        
        print(f"\n🎉 Polars引擎快速测试全部通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_polars_quick()
    if success:
        print("\n✅ Polars引擎准备就绪，可以进入下一阶段！")
    else:
        print("\n❌ 需要修复问题后再继续")
        sys.exit(1)
