#!/usr/bin/env python3
"""测试数据库路径解析"""

import os
import sys
import sqlite3

# 添加src目录到路径
sys.path.append('src')

def test_db_paths():
    """测试不同wrapper的数据库路径"""
    print("🔍 测试数据库路径解析...")
    print(f"📁 当前工作目录: {os.getcwd()}")
    
    # 测试不同的数据库路径
    paths_to_test = [
        "data/model_library.db",
        "./data/model_library.db", 
        os.path.join(os.getcwd(), "data", "model_library.db"),
        os.path.abspath("data/model_library.db")
    ]
    
    for path in paths_to_test:
        abs_path = os.path.abspath(path)
        exists = os.path.exists(abs_path)
        print(f"📊 路径: {path}")
        print(f"   绝对路径: {abs_path}")
        print(f"   存在: {exists}")
        
        if exists:
            try:
                with sqlite3.connect(abs_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = cursor.fetchall()
                    print(f"   表: {[t[0] for t in tables]}")
                    
                    if 'model_states' in [t[0] for t in tables]:
                        cursor.execute("SELECT COUNT(*) FROM model_states")
                        count = cursor.fetchone()[0]
                        print(f"   model_states记录数: {count}")
                        
                        cursor.execute("SELECT model_id, trained FROM model_states")
                        records = cursor.fetchall()
                        for model_id, trained in records:
                            print(f"     {model_id}: trained={trained}")
            except Exception as e:
                print(f"   数据库错误: {e}")
        print()

def test_wrapper_paths():
    """测试wrapper的路径解析"""
    print("🔍 测试wrapper路径解析...")
    
    try:
        from model_library.wrappers.fusion_wrapper import FusionModelWrapper
        fusion = FusionModelWrapper()
        fusion_path = fusion._resolve_db_path()
        print(f"📊 FusionWrapper路径: {fusion_path}")
        print(f"   存在: {os.path.exists(fusion_path)}")
    except Exception as e:
        print(f"❌ FusionWrapper测试失败: {e}")
    
    try:
        from model_library.wrappers.markov_wrapper import MarkovModelWrapper
        markov = MarkovModelWrapper()
        markov_path = markov._resolve_db_path()
        print(f"📊 MarkovWrapper路径: {markov_path}")
        print(f"   存在: {os.path.exists(markov_path)}")
    except Exception as e:
        print(f"❌ MarkovWrapper测试失败: {e}")
    
    try:
        from model_library.wrappers.trend_wrapper import TrendModelWrapper
        trend = TrendModelWrapper()
        trend_path = trend._resolve_db_path()
        print(f"📊 TrendWrapper路径: {trend_path}")
        print(f"   存在: {os.path.exists(trend_path)}")
    except Exception as e:
        print(f"❌ TrendWrapper测试失败: {e}")

if __name__ == "__main__":
    test_db_paths()
    print("=" * 50)
    test_wrapper_paths()
