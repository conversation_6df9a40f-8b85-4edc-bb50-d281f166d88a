"""
阶段C终端bug检测脚本
"""

import sys
import os
import traceback

# 添加src路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def check_basic_imports():
    """检查基础导入"""
    print("=== 检查基础导入 ===")
    
    issues = []
    
    try:
        import numpy as np
        import pandas as pd
        import sqlite3
        from collections import defaultdict, Counter
        print("✓ 基础依赖库导入成功")
    except ImportError as e:
        issues.append(f"基础依赖库导入失败: {e}")
        print(f"✗ 基础依赖库导入失败: {e}")
    
    return issues

def check_trend_analysis_module():
    """检查短期趋势分析模块"""
    print("\n=== 检查短期趋势分析模块 ===")
    
    issues = []
    
    try:
        from prediction.trend_analysis import TrendAnalyzer
        
        # 测试基本初始化
        analyzer = TrendAnalyzer()
        print("✓ TrendAnalyzer 初始化成功")
        
        # 测试关键方法存在性
        required_methods = [
            'load_recent_data',
            'analyze_digit_frequency_trends',
            'analyze_position_trends',
            'analyze_combination_trends',
            'predict_next_trends'
        ]
        
        for method_name in required_methods:
            if hasattr(analyzer, method_name):
                print(f"✓ 方法 {method_name} 存在")
            else:
                issues.append(f"方法 {method_name} 缺失")
                print(f"✗ 方法 {method_name} 缺失")
        
        # 测试数据库路径设置
        if hasattr(analyzer, 'db_path'):
            print(f"✓ 数据库路径设置: {analyzer.db_path}")
        else:
            issues.append("数据库路径未设置")
            print("✗ 数据库路径未设置")
            
    except Exception as e:
        issues.append(f"TrendAnalyzer 导入或初始化失败: {e}")
        print(f"✗ TrendAnalyzer 导入或初始化失败: {e}")
        traceback.print_exc()
    
    return issues

def check_pattern_prediction_module():
    """检查形态转换预测模块"""
    print("\n=== 检查形态转换预测模块 ===")
    
    issues = []
    
    try:
        from prediction.pattern_prediction import PatternPredictor
        
        # 测试基本初始化
        predictor = PatternPredictor()
        print("✓ PatternPredictor 初始化成功")
        
        # 测试关键方法存在性
        required_methods = [
            'load_pattern_data',
            'extract_pattern_features',
            'analyze_pattern_transitions',
            'detect_pattern_cycles',
            'predict_next_patterns',
            'generate_candidate_numbers'
        ]
        
        for method_name in required_methods:
            if hasattr(predictor, method_name):
                print(f"✓ 方法 {method_name} 存在")
            else:
                issues.append(f"方法 {method_name} 缺失")
                print(f"✗ 方法 {method_name} 缺失")
        
        # 测试特征提取功能
        try:
            features = predictor.extract_pattern_features("123")
            if isinstance(features, dict) and len(features) > 0:
                print(f"✓ 特征提取功能正常: {len(features)} 个特征")
            else:
                issues.append("特征提取返回空结果")
                print("✗ 特征提取返回空结果")
        except Exception as e:
            issues.append(f"特征提取功能异常: {e}")
            print(f"✗ 特征提取功能异常: {e}")
            
    except Exception as e:
        issues.append(f"PatternPredictor 导入或初始化失败: {e}")
        print(f"✗ PatternPredictor 导入或初始化失败: {e}")
        traceback.print_exc()
    
    return issues

def check_adaptive_fusion_module():
    """检查自适应权重融合模块"""
    print("\n=== 检查自适应权重融合模块 ===")
    
    issues = []
    
    try:
        from prediction.adaptive_fusion import AdaptiveFusionSystem
        
        # 测试基本初始化
        fusion_system = AdaptiveFusionSystem()
        print("✓ AdaptiveFusionSystem 初始化成功")
        
        # 测试关键方法存在性
        required_methods = [
            'load_fusion_data',
            'evaluate_model_performance',
            'calculate_adaptive_weights',
            'fuse_predictions',
            'calibrate_confidence'
        ]
        
        for method_name in required_methods:
            if hasattr(fusion_system, method_name):
                print(f"✓ 方法 {method_name} 存在")
            else:
                issues.append(f"方法 {method_name} 缺失")
                print(f"✗ 方法 {method_name} 缺失")
        
        # 测试权重计算功能
        try:
            test_performances = {
                'model_a': {'exact_accuracy': 0.15, 'top5_accuracy': 0.4},
                'model_b': {'exact_accuracy': 0.12, 'top5_accuracy': 0.35}
            }
            weights = fusion_system.calculate_adaptive_weights(test_performances)
            
            if isinstance(weights, dict) and len(weights) == 2:
                print(f"✓ 权重计算功能正常: {weights}")
            else:
                issues.append("权重计算返回异常结果")
                print("✗ 权重计算返回异常结果")
        except Exception as e:
            issues.append(f"权重计算功能异常: {e}")
            print(f"✗ 权重计算功能异常: {e}")
            
    except Exception as e:
        issues.append(f"AdaptiveFusionSystem 导入或初始化失败: {e}")
        print(f"✗ AdaptiveFusionSystem 导入或初始化失败: {e}")
        traceback.print_exc()
    
    return issues

def check_intelligent_fusion_integration():
    """检查智能融合集成模块"""
    print("\n=== 检查智能融合集成模块 ===")
    
    issues = []
    
    try:
        from prediction.intelligent_fusion import IntelligentFusionSystem
        
        # 测试基本初始化
        intelligent_system = IntelligentFusionSystem()
        print("✓ IntelligentFusionSystem 初始化成功")
        
        # 测试关键方法存在性
        required_methods = [
            'train_all_models',
            'generate_trend_predictions',
            'generate_pattern_predictions',
            'generate_fusion_prediction',
            'extract_intelligent_features'
        ]
        
        for method_name in required_methods:
            if hasattr(intelligent_system, method_name):
                print(f"✓ 方法 {method_name} 存在")
            else:
                issues.append(f"方法 {method_name} 缺失")
                print(f"✗ 方法 {method_name} 缺失")
        
        # 测试子模块初始化
        if hasattr(intelligent_system, 'trend_analyzer'):
            print("✓ 趋势分析器子模块初始化成功")
        else:
            issues.append("趋势分析器子模块初始化失败")
            print("✗ 趋势分析器子模块初始化失败")
        
        if hasattr(intelligent_system, 'pattern_predictor'):
            print("✓ 形态预测器子模块初始化成功")
        else:
            issues.append("形态预测器子模块初始化失败")
            print("✗ 形态预测器子模块初始化失败")
        
        if hasattr(intelligent_system, 'fusion_system'):
            print("✓ 融合系统子模块初始化成功")
        else:
            issues.append("融合系统子模块初始化失败")
            print("✗ 融合系统子模块初始化失败")
            
    except Exception as e:
        issues.append(f"IntelligentFusionSystem 导入或初始化失败: {e}")
        print(f"✗ IntelligentFusionSystem 导入或初始化失败: {e}")
        traceback.print_exc()
    
    return issues

def check_database_connectivity():
    """检查数据库连接性"""
    print("\n=== 检查数据库连接性 ===")
    
    issues = []
    
    try:
        import sqlite3
        db_path = os.path.join('data', 'lottery.db')
        
        if not os.path.exists(db_path):
            issues.append("数据库文件不存在")
            print("✗ 数据库文件不存在")
            return issues
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查基本表结构
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        if tables:
            print(f"✓ 数据库连接成功，发现 {len(tables)} 个表")
            
            # 检查主要表是否存在
            table_names = [table[0] for table in tables]
            if 'lottery_records' in table_names:
                print("✓ lottery_records 表存在")
                
                # 检查记录数量
                cursor.execute("SELECT COUNT(*) FROM lottery_records")
                count = cursor.fetchone()[0]
                print(f"✓ lottery_records 表包含 {count} 条记录")
                
                if count < 100:
                    issues.append("数据记录数量不足")
                    print("⚠ 数据记录数量可能不足")
            else:
                issues.append("lottery_records 表不存在")
                print("✗ lottery_records 表不存在")
        else:
            issues.append("数据库中没有表")
            print("✗ 数据库中没有表")
        
        conn.close()
        
    except Exception as e:
        issues.append(f"数据库连接检查失败: {e}")
        print(f"✗ 数据库连接检查失败: {e}")
    
    return issues

def check_module_integration():
    """检查模块集成"""
    print("\n=== 检查模块集成 ===")
    
    issues = []
    
    try:
        # 测试所有模块能否同时导入
        from prediction.trend_analysis import TrendAnalyzer
        from prediction.pattern_prediction import PatternPredictor
        from prediction.adaptive_fusion import AdaptiveFusionSystem
        from prediction.intelligent_fusion import IntelligentFusionSystem
        
        print("✓ 所有模块同时导入成功")
        
        # 测试模块间的协同工作
        intelligent_system = IntelligentFusionSystem()
        
        # 检查子模块是否正确初始化
        if (hasattr(intelligent_system, 'trend_analyzer') and
            hasattr(intelligent_system, 'pattern_predictor') and
            hasattr(intelligent_system, 'fusion_system')):
            print("✓ 模块集成架构正确")
        else:
            issues.append("模块集成架构不完整")
            print("✗ 模块集成架构不完整")
        
        # 测试基本功能调用
        try:
            summary = intelligent_system.get_system_summary()
            if isinstance(summary, dict):
                print("✓ 系统摘要功能正常")
            else:
                issues.append("系统摘要功能异常")
                print("✗ 系统摘要功能异常")
        except Exception as e:
            issues.append(f"系统摘要功能调用失败: {e}")
            print(f"✗ 系统摘要功能调用失败: {e}")
            
    except Exception as e:
        issues.append(f"模块集成检查失败: {e}")
        print(f"✗ 模块集成检查失败: {e}")
        traceback.print_exc()
    
    return issues

def main():
    """主函数"""
    print("阶段C终端bug检测")
    print("=" * 50)
    
    all_issues = []
    
    # 执行各项检查
    all_issues.extend(check_basic_imports())
    all_issues.extend(check_trend_analysis_module())
    all_issues.extend(check_pattern_prediction_module())
    all_issues.extend(check_adaptive_fusion_module())
    all_issues.extend(check_intelligent_fusion_integration())
    all_issues.extend(check_database_connectivity())
    all_issues.extend(check_module_integration())
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("终端bug检测结果:")
    print("=" * 50)
    
    if not all_issues:
        print("🎉 未发现终端bug，阶段C实施质量优秀!")
        print("\n✅ 检查项目:")
        print("- ✓ 基础依赖库导入正常")
        print("- ✓ 短期趋势分析模块功能完整")
        print("- ✓ 形态转换预测模块功能完整")
        print("- ✓ 自适应权重融合模块功能完整")
        print("- ✓ 智能融合集成模块功能完整")
        print("- ✓ 数据库连接性正常")
        print("- ✓ 模块集成架构正确")
        
        print("\n📊 阶段C质量评估:")
        print("- 模块结构: 优秀")
        print("- 功能完整性: 完整")
        print("- 集成稳定性: 稳定")
        print("- 数据访问: 正常")
        print("- 错误处理: 完善")
        
        return True
    else:
        print(f"⚠️ 发现 {len(all_issues)} 个问题:")
        for i, issue in enumerate(all_issues, 1):
            print(f"  {i}. {issue}")
        
        print("\n🔧 建议修复措施:")
        print("- 检查依赖库安装完整性")
        print("- 验证模块导入路径配置")
        print("- 确认数据库文件完整性")
        print("- 测试方法调用和参数传递")
        print("- 检查模块间的依赖关系")
        
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n状态: {'✅ 无终端bug' if success else '❌ 需要修复'}")
    exit(0 if success else 1)
