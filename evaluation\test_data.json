{"historical_data": [{"period_number": "2025001", "draw_date": "2025-04-12", "winning_number": "704"}, {"period_number": "2025002", "draw_date": "2025-04-13", "winning_number": "494"}, {"period_number": "2025003", "draw_date": "2025-04-14", "winning_number": "772"}, {"period_number": "2025004", "draw_date": "2025-04-15", "winning_number": "758"}, {"period_number": "2025005", "draw_date": "2025-04-16", "winning_number": "369"}, {"period_number": "2025006", "draw_date": "2025-04-17", "winning_number": "905"}, {"period_number": "2025007", "draw_date": "2025-04-18", "winning_number": "583"}, {"period_number": "2025008", "draw_date": "2025-04-19", "winning_number": "202"}, {"period_number": "2025009", "draw_date": "2025-04-20", "winning_number": "646"}, {"period_number": "2025010", "draw_date": "2025-04-21", "winning_number": "314"}, {"period_number": "2025011", "draw_date": "2025-04-22", "winning_number": "763"}, {"period_number": "2025012", "draw_date": "2025-04-23", "winning_number": "972"}, {"period_number": "2025013", "draw_date": "2025-04-24", "winning_number": "948"}, {"period_number": "2025014", "draw_date": "2025-04-25", "winning_number": "931"}, {"period_number": "2025015", "draw_date": "2025-04-26", "winning_number": "160"}, {"period_number": "2025016", "draw_date": "2025-04-27", "winning_number": "399"}, {"period_number": "2025017", "draw_date": "2025-04-28", "winning_number": "660"}, {"period_number": "2025018", "draw_date": "2025-04-29", "winning_number": "015"}, {"period_number": "2025019", "draw_date": "2025-04-30", "winning_number": "805"}, {"period_number": "2025020", "draw_date": "2025-05-01", "winning_number": "407"}, {"period_number": "2025021", "draw_date": "2025-05-02", "winning_number": "448"}, {"period_number": "2025022", "draw_date": "2025-05-03", "winning_number": "356"}, {"period_number": "2025023", "draw_date": "2025-05-04", "winning_number": "263"}, {"period_number": "2025024", "draw_date": "2025-05-05", "winning_number": "222"}, {"period_number": "2025025", "draw_date": "2025-05-06", "winning_number": "523"}, {"period_number": "2025026", "draw_date": "2025-05-07", "winning_number": "214"}, {"period_number": "2025027", "draw_date": "2025-05-08", "winning_number": "183"}, {"period_number": "2025028", "draw_date": "2025-05-09", "winning_number": "808"}, {"period_number": "2025029", "draw_date": "2025-05-10", "winning_number": "853"}, {"period_number": "2025030", "draw_date": "2025-05-11", "winning_number": "180"}, {"period_number": "2025031", "draw_date": "2025-05-12", "winning_number": "047"}, {"period_number": "2025032", "draw_date": "2025-05-13", "winning_number": "565"}, {"period_number": "2025033", "draw_date": "2025-05-14", "winning_number": "777"}, {"period_number": "2025034", "draw_date": "2025-05-15", "winning_number": "216"}, {"period_number": "2025035", "draw_date": "2025-05-16", "winning_number": "925"}, {"period_number": "2025036", "draw_date": "2025-05-17", "winning_number": "153"}, {"period_number": "2025037", "draw_date": "2025-05-18", "winning_number": "698"}, {"period_number": "2025038", "draw_date": "2025-05-19", "winning_number": "202"}, {"period_number": "2025039", "draw_date": "2025-05-20", "winning_number": "024"}, {"period_number": "2025040", "draw_date": "2025-05-21", "winning_number": "563"}, {"period_number": "2025041", "draw_date": "2025-05-22", "winning_number": "286"}, {"period_number": "2025042", "draw_date": "2025-05-23", "winning_number": "699"}, {"period_number": "2025043", "draw_date": "2025-05-24", "winning_number": "329"}, {"period_number": "2025044", "draw_date": "2025-05-25", "winning_number": "409"}, {"period_number": "2025045", "draw_date": "2025-05-26", "winning_number": "841"}, {"period_number": "2025046", "draw_date": "2025-05-27", "winning_number": "846"}, {"period_number": "2025047", "draw_date": "2025-05-28", "winning_number": "532"}, {"period_number": "2025048", "draw_date": "2025-05-29", "winning_number": "841"}, {"period_number": "2025049", "draw_date": "2025-05-30", "winning_number": "053"}, {"period_number": "2025050", "draw_date": "2025-05-31", "winning_number": "175"}, {"period_number": "2025051", "draw_date": "2025-06-01", "winning_number": "837"}, {"period_number": "2025052", "draw_date": "2025-06-02", "winning_number": "212"}, {"period_number": "2025053", "draw_date": "2025-06-03", "winning_number": "964"}, {"period_number": "2025054", "draw_date": "2025-06-04", "winning_number": "995"}, {"period_number": "2025055", "draw_date": "2025-06-05", "winning_number": "738"}, {"period_number": "2025056", "draw_date": "2025-06-06", "winning_number": "049"}, {"period_number": "2025057", "draw_date": "2025-06-07", "winning_number": "936"}, {"period_number": "2025058", "draw_date": "2025-06-08", "winning_number": "236"}, {"period_number": "2025059", "draw_date": "2025-06-09", "winning_number": "073"}, {"period_number": "2025060", "draw_date": "2025-06-10", "winning_number": "299"}, {"period_number": "2025061", "draw_date": "2025-06-11", "winning_number": "483"}, {"period_number": "2025062", "draw_date": "2025-06-12", "winning_number": "663"}, {"period_number": "2025063", "draw_date": "2025-06-13", "winning_number": "295"}, {"period_number": "2025064", "draw_date": "2025-06-14", "winning_number": "806"}, {"period_number": "2025065", "draw_date": "2025-06-15", "winning_number": "829"}, {"period_number": "2025066", "draw_date": "2025-06-16", "winning_number": "829"}, {"period_number": "2025067", "draw_date": "2025-06-17", "winning_number": "873"}, {"period_number": "2025068", "draw_date": "2025-06-18", "winning_number": "700"}, {"period_number": "2025069", "draw_date": "2025-06-19", "winning_number": "245"}, {"period_number": "2025070", "draw_date": "2025-06-20", "winning_number": "216"}, {"period_number": "2025071", "draw_date": "2025-06-21", "winning_number": "640"}, {"period_number": "2025072", "draw_date": "2025-06-22", "winning_number": "186"}, {"period_number": "2025073", "draw_date": "2025-06-23", "winning_number": "163"}, {"period_number": "2025074", "draw_date": "2025-06-24", "winning_number": "670"}, {"period_number": "2025075", "draw_date": "2025-06-25", "winning_number": "865"}, {"period_number": "2025076", "draw_date": "2025-06-26", "winning_number": "168"}, {"period_number": "2025077", "draw_date": "2025-06-27", "winning_number": "589"}, {"period_number": "2025078", "draw_date": "2025-06-28", "winning_number": "755"}, {"period_number": "2025079", "draw_date": "2025-06-29", "winning_number": "645"}, {"period_number": "2025080", "draw_date": "2025-06-30", "winning_number": "599"}, {"period_number": "2025081", "draw_date": "2025-07-01", "winning_number": "584"}, {"period_number": "2025082", "draw_date": "2025-07-02", "winning_number": "886"}, {"period_number": "2025083", "draw_date": "2025-07-03", "winning_number": "922"}, {"period_number": "2025084", "draw_date": "2025-07-04", "winning_number": "749"}, {"period_number": "2025085", "draw_date": "2025-07-05", "winning_number": "337"}, {"period_number": "2025086", "draw_date": "2025-07-06", "winning_number": "539"}, {"period_number": "2025087", "draw_date": "2025-07-07", "winning_number": "601"}, {"period_number": "2025088", "draw_date": "2025-07-08", "winning_number": "006"}, {"period_number": "2025089", "draw_date": "2025-07-09", "winning_number": "709"}, {"period_number": "2025090", "draw_date": "2025-07-10", "winning_number": "514"}, {"period_number": "2025091", "draw_date": "2025-07-11", "winning_number": "970"}, {"period_number": "2025092", "draw_date": "2025-07-12", "winning_number": "573"}, {"period_number": "2025093", "draw_date": "2025-07-13", "winning_number": "382"}, {"period_number": "2025094", "draw_date": "2025-07-14", "winning_number": "928"}, {"period_number": "2025095", "draw_date": "2025-07-15", "winning_number": "603"}, {"period_number": "2025096", "draw_date": "2025-07-16", "winning_number": "296"}, {"period_number": "2025097", "draw_date": "2025-07-17", "winning_number": "462"}, {"period_number": "2025098", "draw_date": "2025-07-18", "winning_number": "985"}, {"period_number": "2025099", "draw_date": "2025-07-19", "winning_number": "790"}, {"period_number": "2025100", "draw_date": "2025-07-20", "winning_number": "831"}], "prediction_scenarios": [{"name": "标准预测场景", "description": "使用默认参数进行预测", "parameters": {"candidate_count": 10, "confidence_threshold": 0.3, "window_size": 50}}, {"name": "高置信度场景", "description": "使用高置信度阈值进行预测", "parameters": {"candidate_count": 5, "confidence_threshold": 0.6, "window_size": 30}}, {"name": "大候选集场景", "description": "生成更多候选号码", "parameters": {"candidate_count": 20, "confidence_threshold": 0.2, "window_size": 100}}, {"name": "快速预测场景", "description": "使用较小窗口快速预测", "parameters": {"candidate_count": 8, "confidence_threshold": 0.4, "window_size": 20}}], "performance_benchmarks": {"response_time_targets": {"api_health_check": 0.5, "single_best_prediction": 5.0, "prediction_ranking": 3.0, "model_performance": 1.0, "page_load_time": 3.0}, "accuracy_targets": {"single_prediction_accuracy": 0.05, "top_3_accuracy": 0.12, "top_5_accuracy": 0.18, "prediction_consistency": 0.9}, "user_experience_targets": {"interface_friendliness": 9.0, "operation_convenience": 9.0, "information_clarity": 9.0, "overall_satisfaction": 8.8}, "system_stability_targets": {"uptime": 0.999, "error_rate": 0.001, "memory_usage_mb": 512, "cpu_usage_percent": 80}}, "user_behavior_scripts": [{"name": "新用户首次使用流程", "steps": ["访问主页", "查看功能介绍", "点击预测分析", "查看预测结果", "查看排行榜", "查看技术详情"], "expected_time": 180}, {"name": "常规用户预测流程", "steps": ["直接访问预测页面", "调整预测参数", "执行预测", "查看单一最优推荐", "查看候选排行榜", "查看模型权重"], "expected_time": 60}, {"name": "高级用户深度分析流程", "steps": ["访问预测页面", "设置高级参数", "执行多次预测", "对比预测结果", "查看历史表现", "分析模型性能"], "expected_time": 300}, {"name": "移动端用户流程", "steps": ["移动端访问", "检查响应式布局", "触摸操作测试", "滑动查看排行榜", "缩放查看详情"], "expected_time": 120}], "api_test_cases": [{"name": "健康检查API测试", "method": "GET", "url": "/health", "expected_status": 200, "expected_fields": ["status", "timestamp", "database_records"]}, {"name": "单一最优预测API测试", "method": "POST", "url": "/api/v1/prediction/single-best", "payload": {"candidate_count": 10, "confidence_threshold": 0.3, "window_size": 50}, "expected_status": 200, "expected_fields": ["best_prediction", "ranking_list", "model_performance"]}, {"name": "预测排行榜API测试", "method": "GET", "url": "/api/v1/prediction/ranking/2025999", "expected_status": 200, "expected_fields": ["ranking_list", "metadata"]}, {"name": "模型性能API测试", "method": "GET", "url": "/api/v1/models/performance", "expected_status": 200, "expected_fields": ["models", "overall_stats", "last_updated"]}, {"name": "预测结果跟踪API测试", "method": "POST", "url": "/api/v1/prediction/track-result", "payload": {"period_number": "2025999", "predicted_number": "123", "actual_number": "123", "model_name": "test_model"}, "expected_status": 200, "expected_fields": ["success", "message"]}]}