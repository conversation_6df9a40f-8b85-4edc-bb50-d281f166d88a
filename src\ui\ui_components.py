"""
福彩3D系统UI组件库

提供统一的UI组件和用户体验优化功能
"""

import streamlit as st
from typing import Optional, Dict, Any, List
from datetime import datetime


def show_status_badge(status: str, text: str = None) -> None:
    """
    显示状态徽章
    
    Args:
        status: 状态类型 (success, error, warning, info, running, stopped)
        text: 显示文本
    """
    status_config = {
        "success": {"color": "green", "icon": "✅", "text": text or "成功"},
        "error": {"color": "red", "icon": "❌", "text": text or "错误"},
        "warning": {"color": "orange", "icon": "⚠️", "text": text or "警告"},
        "info": {"color": "blue", "icon": "ℹ️", "text": text or "信息"},
        "running": {"color": "green", "icon": "🟢", "text": text or "运行中"},
        "stopped": {"color": "red", "icon": "🔴", "text": text or "已停止"},
        "loading": {"color": "blue", "icon": "🔄", "text": text or "加载中"}
    }
    
    config = status_config.get(status, status_config["info"])
    
    st.markdown(
        f"""
        <div style="
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            background-color: {config['color']}20;
            border: 1px solid {config['color']}40;
            color: {config['color']};
            font-size: 0.8em;
            font-weight: bold;
        ">
            {config['icon']} {config['text']}
        </div>
        """,
        unsafe_allow_html=True
    )


def show_loading_spinner(text: str = "处理中...") -> None:
    """显示加载动画"""
    st.markdown(
        f"""
        <div style="text-align: center; padding: 20px;">
            <div style="
                display: inline-block;
                width: 20px;
                height: 20px;
                border: 3px solid #f3f3f3;
                border-top: 3px solid #3498db;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            "></div>
            <p style="margin-top: 10px; color: #666;">{text}</p>
        </div>
        <style>
        @keyframes spin {{
            0% {{ transform: rotate(0deg); }}
            100% {{ transform: rotate(360deg); }}
        }}
        </style>
        """,
        unsafe_allow_html=True
    )


def show_info_card(title: str, content: str, icon: str = "📋", 
                  color: str = "blue") -> None:
    """
    显示信息卡片
    
    Args:
        title: 卡片标题
        content: 卡片内容
        icon: 图标
        color: 颜色主题
    """
    st.markdown(
        f"""
        <div style="
            border: 1px solid {color}40;
            border-radius: 8px;
            padding: 16px;
            margin: 8px 0;
            background-color: {color}10;
        ">
            <h4 style="margin: 0 0 8px 0; color: {color};">
                {icon} {title}
            </h4>
            <p style="margin: 0; color: #333;">
                {content}
            </p>
        </div>
        """,
        unsafe_allow_html=True
    )


def show_help_tooltip(text: str, help_text: str) -> None:
    """
    显示带帮助提示的文本
    
    Args:
        text: 显示文本
        help_text: 帮助文本
    """
    st.markdown(
        f"""
        <div style="display: inline-block;">
            <span>{text}</span>
            <span style="
                margin-left: 4px;
                color: #666;
                cursor: help;
                font-size: 0.8em;
            " title="{help_text}">
                ❓
            </span>
        </div>
        """,
        unsafe_allow_html=True
    )


def show_progress_bar(current: int, total: int, text: str = "") -> None:
    """
    显示进度条
    
    Args:
        current: 当前进度
        total: 总数
        text: 进度文本
    """
    percentage = (current / total * 100) if total > 0 else 0
    
    st.markdown(
        f"""
        <div style="margin: 10px 0;">
            <div style="
                display: flex;
                justify-content: space-between;
                margin-bottom: 4px;
            ">
                <span>{text}</span>
                <span>{current}/{total} ({percentage:.1f}%)</span>
            </div>
            <div style="
                width: 100%;
                background-color: #f0f0f0;
                border-radius: 4px;
                overflow: hidden;
            ">
                <div style="
                    width: {percentage}%;
                    height: 8px;
                    background-color: #3498db;
                    transition: width 0.3s ease;
                "></div>
            </div>
        </div>
        """,
        unsafe_allow_html=True
    )


def show_action_buttons(actions: List[Dict[str, Any]], key_prefix: str = "") -> Dict[str, bool]:
    """
    显示操作按钮组
    
    Args:
        actions: 按钮配置列表，每个包含 label, key, type, help, disabled 等
        key_prefix: 按键前缀
        
    Returns:
        按钮点击状态字典
    """
    results = {}
    
    if not actions:
        return results
    
    # 根据按钮数量调整列数
    num_actions = len(actions)
    if num_actions <= 3:
        cols = st.columns(num_actions)
    else:
        cols = st.columns(3)
    
    for i, action in enumerate(actions):
        col_index = i % len(cols)
        
        with cols[col_index]:
            button_key = f"{key_prefix}{action.get('key', f'btn_{i}')}"
            button_type = action.get('type', 'secondary')
            button_disabled = action.get('disabled', False)
            button_help = action.get('help', '')
            
            clicked = st.button(
                action['label'],
                key=button_key,
                type=button_type,
                disabled=button_disabled,
                help=button_help,
                use_container_width=True
            )
            
            results[action.get('key', f'btn_{i}')] = clicked
    
    return results


def show_confirmation_dialog(message: str, key: str, 
                           confirm_text: str = "确认", 
                           cancel_text: str = "取消") -> Optional[bool]:
    """
    显示确认对话框
    
    Args:
        message: 确认消息
        key: 唯一键
        confirm_text: 确认按钮文本
        cancel_text: 取消按钮文本
        
    Returns:
        True: 确认, False: 取消, None: 未操作
    """
    if f"show_confirm_{key}" not in st.session_state:
        st.session_state[f"show_confirm_{key}"] = False
    
    if st.session_state[f"show_confirm_{key}"]:
        st.warning(f"⚠️ {message}")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button(confirm_text, key=f"confirm_{key}", type="primary"):
                st.session_state[f"show_confirm_{key}"] = False
                return True
        
        with col2:
            if st.button(cancel_text, key=f"cancel_{key}"):
                st.session_state[f"show_confirm_{key}"] = False
                return False
        
        return None
    
    return None


def show_feature_announcement(title: str, content: str, 
                            version: str = "", 
                            show_key: str = "feature_announcement") -> None:
    """
    显示功能公告
    
    Args:
        title: 公告标题
        content: 公告内容
        version: 版本信息
        show_key: 显示控制键
    """
    if f"hide_{show_key}" not in st.session_state:
        st.session_state[f"hide_{show_key}"] = False
    
    if not st.session_state[f"hide_{show_key}"]:
        with st.container():
            st.markdown(
                f"""
                <div style="
                    border: 2px solid #3498db;
                    border-radius: 8px;
                    padding: 16px;
                    margin: 16px 0;
                    background: linear-gradient(135deg, #3498db10, #2980b910);
                ">
                    <h3 style="margin: 0 0 8px 0; color: #3498db;">
                        🎉 {title} {version}
                    </h3>
                    <p style="margin: 0 0 12px 0;">
                        {content}
                    </p>
                </div>
                """,
                unsafe_allow_html=True
            )
            
            if st.button("✖️ 关闭公告", key=f"close_{show_key}"):
                st.session_state[f"hide_{show_key}"] = True
                st.rerun()


def show_quick_stats(stats: Dict[str, Any], title: str = "快速统计") -> None:
    """
    显示快速统计信息
    
    Args:
        stats: 统计数据字典
        title: 标题
    """
    st.markdown(f"#### 📊 {title}")
    
    # 计算列数
    num_stats = len(stats)
    if num_stats <= 4:
        cols = st.columns(num_stats)
    else:
        cols = st.columns(4)
    
    for i, (key, value) in enumerate(stats.items()):
        col_index = i % len(cols)
        
        with cols[col_index]:
            if isinstance(value, dict):
                # 如果值是字典，支持更多配置
                metric_value = value.get('value', 0)
                metric_delta = value.get('delta', None)
                metric_help = value.get('help', None)
                
                st.metric(
                    label=key,
                    value=metric_value,
                    delta=metric_delta,
                    help=metric_help
                )
            else:
                # 简单值
                st.metric(label=key, value=value)


def show_help_section(sections: List[Dict[str, str]]) -> None:
    """
    显示帮助文档部分
    
    Args:
        sections: 帮助部分列表，每个包含 title 和 content
    """
    st.markdown("### 📚 帮助文档")
    
    for section in sections:
        with st.expander(f"❓ {section['title']}"):
            st.markdown(section['content'])


def apply_custom_css() -> None:
    """应用自定义CSS样式"""
    st.markdown(
        """
        <style>
        /* 自定义按钮样式 */
        .stButton > button {
            border-radius: 6px;
            border: 1px solid #ddd;
            transition: all 0.3s ease;
        }
        
        .stButton > button:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        /* 自定义指标样式 */
        .metric-container {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        /* 自定义警告样式 */
        .stAlert {
            border-radius: 6px;
        }
        
        /* 自定义选项卡样式 */
        .stTabs [data-baseweb="tab-list"] {
            gap: 8px;
        }
        
        .stTabs [data-baseweb="tab"] {
            border-radius: 6px 6px 0 0;
        }
        
        /* 自定义侧边栏样式 */
        .css-1d391kg {
            padding-top: 1rem;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .stColumns {
                flex-direction: column;
            }
        }
        </style>
        """,
        unsafe_allow_html=True
    )
