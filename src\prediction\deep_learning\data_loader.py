"""
福彩3D数据加载器
实现时间序列数据划分、数据增强、批量加载等功能
"""

import torch
from torch.utils.data import Dataset, DataLoader
import numpy as np
import pandas as pd
from typing import List, Tuple, Dict, Optional
import warnings
warnings.filterwarnings('ignore')


class SimpleStandardScaler:
    """简化的标准化器"""

    def __init__(self):
        self.mean_ = None
        self.std_ = None

    def fit_transform(self, X):
        self.mean_ = np.mean(X, axis=0)
        self.std_ = np.std(X, axis=0)
        self.std_[self.std_ == 0] = 1  # 避免除零
        return (X - self.mean_) / self.std_

    def transform(self, X):
        if self.mean_ is None or self.std_ is None:
            raise ValueError("Scaler not fitted")
        return (X - self.mean_) / self.std_


class LotteryDataset(Dataset):
    """福彩3D数据集"""
    
    def __init__(self, sequences: np.ndarray, targets: np.ndarray, 
                 features: np.ndarray = None, transform=None):
        """
        Args:
            sequences: 序列数据 (num_samples, seq_len, num_features)
            targets: 目标标签 (num_samples,)
            features: 额外特征 (num_samples, feature_dim)
            transform: 数据变换
        """
        self.sequences = torch.FloatTensor(sequences)
        self.targets = torch.LongTensor(targets)
        self.features = torch.FloatTensor(features) if features is not None else None
        self.transform = transform
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        sequence = self.sequences[idx]
        target = self.targets[idx]
        
        if self.features is not None:
            # 将额外特征拼接到序列的最后一个时间步
            feature = self.features[idx]
            # 扩展特征维度以匹配序列长度
            feature_expanded = feature.unsqueeze(0).repeat(sequence.size(0), 1)
            sequence = torch.cat([sequence, feature_expanded], dim=1)
        
        if self.transform:
            sequence = self.transform(sequence)
        
        return sequence, target


class LotteryDataLoader:
    """福彩3D数据加载器"""
    
    def __init__(self, sequence_length: int = 20, feature_dim: int = 50):
        self.sequence_length = sequence_length
        self.feature_dim = feature_dim
        self.scaler = SimpleStandardScaler()
        
    def prepare_data(self, lottery_records: List, feature_extractor=None) -> Dict[str, np.ndarray]:
        """
        准备训练数据
        
        Args:
            lottery_records: 福彩3D记录列表
            feature_extractor: 特征提取器
            
        Returns:
            包含序列、目标和特征的字典
        """
        if len(lottery_records) < self.sequence_length + 1:
            raise ValueError(f"数据量不足，至少需要 {self.sequence_length + 1} 条记录")
        
        # 提取号码序列
        numbers = [record.numbers for record in lottery_records]
        
        # 转换为数值
        numeric_data = self._convert_to_numeric(numbers)
        
        # 提取特征（如果提供了特征提取器）
        features = None
        if feature_extractor:
            features = self._extract_features(numbers, feature_extractor)
        
        # 创建序列和目标
        sequences, targets = self._create_sequences(numeric_data)
        
        # 特征标准化
        sequences = self._normalize_sequences(sequences)
        
        if features is not None:
            features = self._normalize_features(features)
        
        return {
            'sequences': sequences,
            'targets': targets,
            'features': features,
            'raw_numbers': numbers
        }
    
    def _convert_to_numeric(self, numbers: List[str]) -> np.ndarray:
        """将号码转换为数值特征"""
        numeric_features = []
        
        for number in numbers:
            if len(number) == 3:
                # 基础数值特征
                h, t, u = int(number[0]), int(number[1]), int(number[2])
                
                features = [
                    h, t, u,  # 原始数字
                    h + t + u,  # 和值
                    max(h, t, u) - min(h, t, u),  # 跨度
                    h % 2, t % 2, u % 2,  # 奇偶性
                    int(h >= 5), int(t >= 5), int(u >= 5),  # 大小
                    h % 3, t % 3, u % 3,  # 012路
                ]

                # 质合性（2,3,5,7为质数）
                primes = {2, 3, 5, 7}
                features.extend([
                    int(h in primes), int(t in primes), int(u in primes)
                ])

                # AC值（算术复杂性）
                ac_value = self._calculate_ac_value([h, t, u])
                features.append(ac_value)

                # 添加额外特征确保维度一致
                features.append(abs(h - t))  # 百十位差值
                features.append(abs(t - u))  # 十个位差值

                numeric_features.append(features)
            else:
                # 如果号码格式不正确，使用默认值
                numeric_features.append([0] * 20)  # 更新为20个特征
        
        return np.array(numeric_features)
    
    def _calculate_ac_value(self, digits: List[int]) -> int:
        """计算AC值（算术复杂性）"""
        if len(digits) != 3:
            return 0
        
        differences = []
        for i in range(len(digits)):
            for j in range(i + 1, len(digits)):
                diff = abs(digits[i] - digits[j])
                if diff not in differences:
                    differences.append(diff)
        
        return len(differences) - 1
    
    def _extract_features(self, numbers: List[str], feature_extractor) -> np.ndarray:
        """提取高级特征"""
        try:
            # 使用特征提取器提取特征
            feature_dict = feature_extractor.extract_all_features(numbers)
            
            # 转换为数组
            feature_values = list(feature_dict.values())
            
            # 确保特征维度一致
            if len(feature_values) < self.feature_dim:
                # 填充零值
                feature_values.extend([0.0] * (self.feature_dim - len(feature_values)))
            elif len(feature_values) > self.feature_dim:
                # 截断
                feature_values = feature_values[:self.feature_dim]
            
            return np.array(feature_values).reshape(1, -1)
            
        except Exception as e:
            print(f"特征提取失败: {e}")
            return np.zeros((1, self.feature_dim))
    
    def _create_sequences(self, numeric_data: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """创建序列和目标"""
        sequences = []
        targets = []
        
        for i in range(len(numeric_data) - self.sequence_length):
            # 输入序列
            seq = numeric_data[i:i + self.sequence_length]
            sequences.append(seq)
            
            # 目标（下一个号码转换为类别索引）
            next_number_data = numeric_data[i + self.sequence_length]
            # 将三位数字转换为0-999的类别索引
            h, t, u = int(next_number_data[0]), int(next_number_data[1]), int(next_number_data[2])
            target = h * 100 + t * 10 + u  # 转换为0-999的索引
            targets.append(target)
        
        return np.array(sequences), np.array(targets)
    
    def _normalize_sequences(self, sequences: np.ndarray) -> np.ndarray:
        """标准化序列数据"""
        # 重塑为2D进行标准化
        original_shape = sequences.shape
        sequences_2d = sequences.reshape(-1, sequences.shape[-1])
        
        # 标准化
        sequences_normalized = self.scaler.fit_transform(sequences_2d)
        
        # 重塑回原始形状
        return sequences_normalized.reshape(original_shape)
    
    def _normalize_features(self, features: np.ndarray) -> np.ndarray:
        """标准化特征数据"""
        if features.ndim == 1:
            features = features.reshape(1, -1)
        
        # 使用相同的scaler进行标准化
        return self.scaler.transform(features)
    
    def split_data(self, data: Dict[str, np.ndarray], 
                   train_ratio: float = 0.7, val_ratio: float = 0.2, 
                   test_ratio: float = 0.1) -> Dict[str, Dict[str, np.ndarray]]:
        """
        时间序列数据划分
        
        Args:
            data: 数据字典
            train_ratio: 训练集比例
            val_ratio: 验证集比例
            test_ratio: 测试集比例
            
        Returns:
            划分后的数据
        """
        sequences = data['sequences']
        targets = data['targets']
        features = data.get('features')
        
        total_samples = len(sequences)
        
        # 计算划分点（时间序列数据按时间顺序划分）
        train_end = int(total_samples * train_ratio)
        val_end = int(total_samples * (train_ratio + val_ratio))
        
        # 划分数据
        train_data = {
            'sequences': sequences[:train_end],
            'targets': targets[:train_end]
        }
        
        val_data = {
            'sequences': sequences[train_end:val_end],
            'targets': targets[train_end:val_end]
        }
        
        test_data = {
            'sequences': sequences[val_end:],
            'targets': targets[val_end:]
        }
        
        # 添加特征（如果存在）
        if features is not None:
            train_data['features'] = features[:train_end] if features.ndim > 1 else features
            val_data['features'] = features[train_end:val_end] if features.ndim > 1 else features
            test_data['features'] = features[val_end:] if features.ndim > 1 else features
        
        return {
            'train': train_data,
            'val': val_data,
            'test': test_data
        }
    
    def create_data_loaders(self, split_data: Dict[str, Dict[str, np.ndarray]], 
                           batch_size: int = 32, shuffle_train: bool = True) -> Dict[str, DataLoader]:
        """
        创建数据加载器
        
        Args:
            split_data: 划分后的数据
            batch_size: 批量大小
            shuffle_train: 是否打乱训练数据
            
        Returns:
            数据加载器字典
        """
        data_loaders = {}
        
        for split_name, data in split_data.items():
            dataset = LotteryDataset(
                sequences=data['sequences'],
                targets=data['targets'],
                features=data.get('features')
            )
            
            shuffle = shuffle_train if split_name == 'train' else False
            
            data_loader = DataLoader(
                dataset,
                batch_size=batch_size,
                shuffle=shuffle,
                num_workers=0,  # Windows兼容性
                pin_memory=torch.cuda.is_available()
            )
            
            data_loaders[split_name] = data_loader
        
        return data_loaders
    
    def augment_data(self, sequences: np.ndarray, targets: np.ndarray, 
                     noise_factor: float = 0.01, num_augmented: int = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        数据增强
        
        Args:
            sequences: 原始序列
            targets: 原始目标
            noise_factor: 噪声因子
            num_augmented: 增强数据数量
            
        Returns:
            增强后的数据
        """
        if num_augmented is None:
            num_augmented = len(sequences) // 2
        
        # 随机选择要增强的样本
        indices = np.random.choice(len(sequences), num_augmented, replace=True)
        
        augmented_sequences = []
        augmented_targets = []
        
        for idx in indices:
            seq = sequences[idx].copy()
            target = targets[idx]
            
            # 添加高斯噪声
            noise = np.random.normal(0, noise_factor, seq.shape)
            augmented_seq = seq + noise
            
            augmented_sequences.append(augmented_seq)
            augmented_targets.append(target)
        
        # 合并原始数据和增强数据
        all_sequences = np.concatenate([sequences, np.array(augmented_sequences)], axis=0)
        all_targets = np.concatenate([targets, np.array(augmented_targets)], axis=0)
        
        return all_sequences, all_targets


def test_data_loader():
    """测试数据加载器"""
    print("=== 测试数据加载器 ===")
    
    # 创建模拟数据
    class MockRecord:
        def __init__(self, numbers):
            self.numbers = numbers
    
    # 生成测试数据
    test_records = []
    for i in range(100):
        numbers = f"{np.random.randint(0, 10)}{np.random.randint(0, 10)}{np.random.randint(0, 10)}"
        test_records.append(MockRecord(numbers))
    
    # 创建数据加载器
    data_loader = LotteryDataLoader(sequence_length=10, feature_dim=20)
    
    # 准备数据
    data = data_loader.prepare_data(test_records)
    print(f"序列形状: {data['sequences'].shape}")
    print(f"目标形状: {data['targets'].shape}")
    
    # 数据划分
    split_data = data_loader.split_data(data)
    print(f"训练集大小: {len(split_data['train']['sequences'])}")
    print(f"验证集大小: {len(split_data['val']['sequences'])}")
    print(f"测试集大小: {len(split_data['test']['sequences'])}")
    
    # 创建数据加载器
    data_loaders = data_loader.create_data_loaders(split_data, batch_size=8)
    
    # 测试批量加载
    train_loader = data_loaders['train']
    for batch_idx, (sequences, targets) in enumerate(train_loader):
        print(f"批次 {batch_idx}: 序列形状 {sequences.shape}, 目标形状 {targets.shape}")
        if batch_idx >= 2:  # 只测试前3个批次
            break
    
    print("✓ 数据加载器测试成功!")


if __name__ == "__main__":
    test_data_loader()
