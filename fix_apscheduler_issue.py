#!/usr/bin/env python3
"""
修复APScheduler导入问题
确保Streamlit能正确使用APScheduler
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_current_environment():
    """检查当前环境"""
    print("🔍 检查当前Python环境...")
    print(f"Python路径: {sys.executable}")
    print(f"工作目录: {os.getcwd()}")
    
    # 检查APScheduler
    try:
        import apscheduler
        print(f"✅ APScheduler版本: {apscheduler.__version__}")
        return True
    except ImportError as e:
        print(f"❌ APScheduler导入失败: {e}")
        return False

def install_apscheduler():
    """安装APScheduler"""
    print("📦 安装APScheduler...")
    
    try:
        # 使用当前Python环境安装
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "apscheduler", "--upgrade"
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ APScheduler安装成功")
            return True
        else:
            print(f"❌ APScheduler安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 安装过程异常: {e}")
        return False

def fix_streamlit_environment():
    """修复Streamlit环境"""
    print("🔧 修复Streamlit环境...")
    
    # 创建环境修复脚本
    fix_script = '''
import sys
import os
from pathlib import Path

# 确保使用正确的Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

# 设置环境变量
os.environ["PYTHONPATH"] = str(project_root / "src")

# 验证APScheduler
try:
    import apscheduler
    print(f"✅ Streamlit环境中APScheduler可用: {apscheduler.__version__}")
except ImportError as e:
    print(f"❌ Streamlit环境中APScheduler不可用: {e}")
    # 尝试安装
    import subprocess
    subprocess.run([sys.executable, "-m", "pip", "install", "apscheduler"])
'''
    
    # 保存修复脚本
    with open("streamlit_env_fix.py", "w", encoding="utf-8") as f:
        f.write(fix_script)
    
    print("✅ 环境修复脚本已创建")

def create_streamlit_wrapper():
    """创建Streamlit启动包装器"""
    print("📝 创建Streamlit启动包装器...")
    
    wrapper_content = f'''#!/usr/bin/env python3
"""
Streamlit启动包装器
确保正确的环境和依赖
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """主函数"""
    # 设置项目根目录
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    # 设置Python路径
    sys.path.insert(0, str(project_root / "src"))
    os.environ["PYTHONPATH"] = str(project_root / "src")
    
    # 验证APScheduler
    try:
        import apscheduler
        print(f"✅ APScheduler可用: {{apscheduler.__version__}}")
    except ImportError:
        print("❌ APScheduler不可用，尝试安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "apscheduler"])
    
    # 启动Streamlit
    print("🚀 启动Streamlit...")
    subprocess.run([
        sys.executable, "-m", "streamlit", "run",
        "src/ui/main.py",
        "--server.port=8501",
        "--server.address=127.0.0.1",
        "--browser.gatherUsageStats=false"
    ])

if __name__ == "__main__":
    main()
'''
    
    with open("start_streamlit_fixed.py", "w", encoding="utf-8") as f:
        f.write(wrapper_content)
    
    print("✅ Streamlit启动包装器已创建")

def test_scheduler_import():
    """测试调度器导入"""
    print("🧪 测试调度器模块导入...")
    
    try:
        # 添加src到路径
        sys.path.insert(0, "src")
        
        # 导入调度器模块
        from scheduler.task_scheduler import TaskScheduler, APSCHEDULER_AVAILABLE
        
        print(f"✅ 调度器模块导入成功")
        print(f"✅ APScheduler可用状态: {APSCHEDULER_AVAILABLE}")
        
        if APSCHEDULER_AVAILABLE:
            # 尝试创建调度器实例
            scheduler = TaskScheduler()
            print("✅ 调度器实例创建成功")
            return True
        else:
            print("❌ APScheduler在调度器模块中不可用")
            return False
            
    except Exception as e:
        print(f"❌ 调度器模块测试失败: {e}")
        return False

def main():
    """主修复函数"""
    print("🔧 APScheduler问题修复工具")
    print("=" * 50)
    
    # 1. 检查当前环境
    if not check_current_environment():
        # 2. 安装APScheduler
        if not install_apscheduler():
            print("❌ 无法安装APScheduler，请手动安装")
            return False
    
    # 3. 修复Streamlit环境
    fix_streamlit_environment()
    
    # 4. 创建启动包装器
    create_streamlit_wrapper()
    
    # 5. 测试调度器导入
    if test_scheduler_import():
        print("\n" + "=" * 50)
        print("🎉 APScheduler问题修复完成！")
        print("\n💡 使用说明:")
        print("1. 使用新的启动脚本: python start_streamlit_fixed.py")
        print("2. 或者重启现有的Streamlit服务")
        print("3. 在数据更新页面中测试调度器功能")
        
        # 询问是否立即启动
        response = input("\n是否立即启动修复后的Streamlit? (y/n): ")
        if response.lower() == 'y':
            print("🚀 启动修复后的Streamlit...")
            subprocess.run([sys.executable, "start_streamlit_fixed.py"])
        
        return True
    else:
        print("\n❌ 调度器测试仍然失败")
        print("💡 建议:")
        print("1. 重启计算机")
        print("2. 重新安装Python虚拟环境")
        print("3. 手动运行: pip install apscheduler")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        input("\\n按Enter键退出...")
    sys.exit(0 if success else 1)
