"""
预测API接口重构
更新预测相关的API接口，支持单一最优预测和排行榜功能
"""

import logging
import os
import sys
import traceback
from datetime import datetime
from typing import Any, Dict, List, Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from fastapi import Depends, FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

from src.core.accuracy_focused_fusion import (AccuracyFocusedFusion,
                                              ModelPrediction,
                                              PredictionResult)
# 导入核心模块
from src.core.model_performance_tracker import ModelPerformanceTracker
from src.core.number_ranking_system import NumberRankingSystem, RankingItem

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="福彩3D预测系统API",
    description="准确性导向的福彩3D预测API接口",
    version="2.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ============================================================================
# 数据模型定义
# ============================================================================

class PredictionRequest(BaseModel):
    """预测请求模型"""
    period_number: Optional[str] = Field(None, description="期号")
    candidate_count: int = Field(10, ge=5, le=50, description="候选数量")
    confidence_threshold: float = Field(0.3, ge=0.1, le=0.9, description="置信度阈值")
    window_size: int = Field(50, ge=10, le=200, description="历史数据窗口大小")

class BestPrediction(BaseModel):
    """最佳预测模型"""
    number: str = Field(..., description="推荐号码")
    confidence: float = Field(..., description="预测置信度")
    fusion_method: str = Field(..., description="融合方法")
    recommendation_level: str = Field(..., description="推荐等级")
    historical_hit_rate: float = Field(..., description="历史命中率")
    prediction_basis: str = Field(..., description="预测依据")
    model_support: List[str] = Field(..., description="支持模型列表")

class RankingItemResponse(BaseModel):
    """排行榜项目响应模型"""
    rank: int = Field(..., description="排名")
    number: str = Field(..., description="号码")
    confidence: float = Field(..., description="置信度")
    composite_score: float = Field(..., description="综合评分")
    model_support_count: int = Field(..., description="模型支持数")
    historical_hit_rate: float = Field(..., description="历史命中率")
    recommendation_level: str = Field(..., description="推荐等级")
    support_models: List[str] = Field(..., description="支持模型")

class ModelPerformanceSummary(BaseModel):
    """模型性能摘要"""
    model_name: str = Field(..., description="模型名称")
    accuracy_rate: float = Field(..., description="准确率")
    total_predictions: int = Field(..., description="总预测次数")
    correct_predictions: int = Field(..., description="正确预测次数")
    last_prediction: Optional[str] = Field(None, description="最后预测时间")
    current_weight: float = Field(..., description="当前权重")

class PredictionMetadata(BaseModel):
    """预测元数据"""
    prediction_time: datetime = Field(..., description="预测时间")
    data_window_size: int = Field(..., description="数据窗口大小")
    total_candidates: int = Field(..., description="候选总数")
    fusion_strategy: str = Field(..., description="融合策略")
    model_weights: Dict[str, float] = Field(..., description="模型权重")

class SingleBestPredictionResponse(BaseModel):
    """单一最优预测响应"""
    best_prediction: BestPrediction
    ranking_list: List[RankingItemResponse]
    model_performance: List[ModelPerformanceSummary]
    prediction_metadata: PredictionMetadata
    success: bool = True
    message: str = "预测成功"

class PredictionRankingResponse(BaseModel):
    """预测排行榜响应"""
    period_number: str = Field(..., description="期号")
    ranking_list: List[RankingItemResponse]
    statistics: Dict[str, Any] = Field(..., description="统计信息")
    generated_time: datetime = Field(..., description="生成时间")

class ModelPerformanceResponse(BaseModel):
    """模型性能响应"""
    models: List[ModelPerformanceSummary]
    overall_stats: Dict[str, Any] = Field(..., description="整体统计")
    last_updated: datetime = Field(..., description="最后更新时间")

class PredictionSaveRequest(BaseModel):
    """预测结果保存请求模型"""
    period_number: str = Field(..., description="期号")
    predicted_number: str = Field(..., description="预测号码")
    model_name: str = Field(..., description="模型名称")
    confidence: float = Field(..., ge=0.0, le=1.0, description="预测置信度")
    prediction_time: str = Field(..., description="预测时间")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")

class PredictionSaveResponse(BaseModel):
    """预测结果保存响应模型"""
    success: bool = Field(..., description="保存是否成功")
    prediction_id: int = Field(..., description="预测记录ID")
    message: str = Field(..., description="响应消息")
    saved_at: str = Field(..., description="保存时间")

class TrendPredictionResponse(BaseModel):
    """趋势分析预测响应模型"""
    success: bool = Field(..., description="预测是否成功")
    predicted_number: str = Field(..., description="预测号码")
    confidence: float = Field(..., ge=0.0, le=1.0, description="预测置信度")
    trend_analysis: Dict[str, Any] = Field(..., description="趋势分析详情")
    prediction_time: str = Field(..., description="预测时间")
    window_size: int = Field(..., description="分析窗口大小")
    data_points: int = Field(..., description="数据点数量")

class PredictionTrackingRequest(BaseModel):
    """预测结果跟踪请求"""
    period_number: str = Field(..., description="期号")
    actual_number: str = Field(..., description="实际开奖号码")
    predicted_numbers: Dict[str, str] = Field(..., description="各模型预测号码")
    confidences: Dict[str, float] = Field(..., description="各模型置信度")

class TrackingResponse(BaseModel):
    """跟踪响应"""
    success: bool = True
    message: str = "跟踪成功"
    updated_accuracies: Dict[str, float] = Field(..., description="更新后的准确率")

# ============================================================================
# 依赖注入
# ============================================================================

def get_performance_tracker() -> ModelPerformanceTracker:
    """获取性能跟踪器"""
    return ModelPerformanceTracker()

def get_fusion_engine(tracker: ModelPerformanceTracker = Depends(get_performance_tracker)) -> AccuracyFocusedFusion:
    """获取融合引擎"""
    return AccuracyFocusedFusion(tracker)

def get_ranking_system(
    fusion: AccuracyFocusedFusion = Depends(get_fusion_engine),
    tracker: ModelPerformanceTracker = Depends(get_performance_tracker)
) -> NumberRankingSystem:
    """获取排行榜系统"""
    return NumberRankingSystem(fusion, tracker)

# ============================================================================
# API端点
# ============================================================================

@app.post("/api/v1/prediction/single-best", response_model=SingleBestPredictionResponse)
async def get_single_best_prediction(
    request: PredictionRequest,
    fusion: AccuracyFocusedFusion = Depends(get_fusion_engine),
    ranking: NumberRankingSystem = Depends(get_ranking_system),
    tracker: ModelPerformanceTracker = Depends(get_performance_tracker)
):
    """获取单一最优预测"""
    try:
        logger.info(f"开始单一最优预测，期号: {request.period_number}")
        
        # 模拟获取各模型预测结果（实际应该从模型库获取）
        model_predictions = _get_mock_model_predictions()
        
        # 获取单一最优预测
        best_result = fusion.get_single_best_prediction(model_predictions)
        
        # 生成排行榜
        ranking_list = ranking.generate_ranking_list(model_predictions, request.candidate_count)
        
        # 获取模型性能
        performance_summary = tracker.get_model_performance_summary()
        model_weights = tracker.calculate_dynamic_weights()
        
        # 构建响应
        response = SingleBestPredictionResponse(
            best_prediction=BestPrediction(
                number=best_result.number,
                confidence=best_result.confidence,
                fusion_method=best_result.method,
                recommendation_level=ranking.get_recommendation_level(best_result.confidence),
                historical_hit_rate=ranking.get_historical_hit_rate(best_result.number),
                prediction_basis=fusion.get_fusion_explanation(best_result),
                model_support=best_result.model_support or []
            ),
            ranking_list=[
                RankingItemResponse(
                    rank=item.rank,
                    number=item.number,
                    confidence=item.confidence,
                    composite_score=item.composite_score,
                    model_support_count=item.model_support_count,
                    historical_hit_rate=item.historical_hit_rate,
                    recommendation_level=item.recommendation_level,
                    support_models=item.support_models or []
                ) for item in ranking_list
            ],
            model_performance=[
                ModelPerformanceSummary(
                    model_name=model,
                    accuracy_rate=stats['accuracy_rate'],
                    total_predictions=stats['total_predictions'],
                    correct_predictions=stats['correct_predictions'],
                    last_prediction=stats['last_prediction'],
                    current_weight=model_weights.get(model, 0.25)
                ) for model, stats in performance_summary.items()
            ],
            prediction_metadata=PredictionMetadata(
                prediction_time=datetime.now(),
                data_window_size=request.window_size,
                total_candidates=len(ranking_list),
                fusion_strategy=best_result.method,
                model_weights=model_weights
            )
        )
        
        logger.info(f"单一最优预测完成: {best_result.number}")
        return response
        
    except Exception as e:
        logger.error(f"单一最优预测失败: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"预测失败: {str(e)}")

@app.get("/api/v1/prediction/ranking/{period}", response_model=PredictionRankingResponse)
async def get_prediction_ranking(
    period: str,
    top_n: int = Query(10, ge=5, le=50, description="返回前N个候选"),
    ranking: NumberRankingSystem = Depends(get_ranking_system)
):
    """获取预测排行榜"""
    try:
        logger.info(f"获取预测排行榜，期号: {period}")
        
        # 从数据库获取排行榜
        ranking_list = ranking.get_ranking_from_database(period)
        
        if not ranking_list:
            # 如果数据库中没有，生成新的排行榜
            model_predictions = _get_mock_model_predictions()
            ranking_list = ranking.generate_ranking_list(model_predictions, top_n)
            ranking.save_ranking_to_database(ranking_list, period)
        
        # 获取统计信息
        statistics = ranking.get_ranking_statistics(ranking_list[:top_n])
        
        response = PredictionRankingResponse(
            period_number=period,
            ranking_list=[
                RankingItemResponse(
                    rank=item.rank,
                    number=item.number,
                    confidence=item.confidence,
                    composite_score=item.composite_score,
                    model_support_count=item.model_support_count,
                    historical_hit_rate=item.historical_hit_rate,
                    recommendation_level=item.recommendation_level,
                    support_models=item.support_models or []
                ) for item in ranking_list[:top_n]
            ],
            statistics=statistics,
            generated_time=datetime.now()
        )
        
        logger.info(f"排行榜获取完成，共 {len(response.ranking_list)} 个候选")
        return response
        
    except Exception as e:
        logger.error(f"获取排行榜失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取排行榜失败: {str(e)}")

@app.get("/api/v1/models/performance", response_model=ModelPerformanceResponse)
async def get_model_performance(
    tracker: ModelPerformanceTracker = Depends(get_performance_tracker)
):
    """获取模型性能统计"""
    try:
        logger.info("获取模型性能统计")
        
        performance_summary = tracker.get_model_performance_summary()
        model_weights = tracker.calculate_dynamic_weights()
        
        models = [
            ModelPerformanceSummary(
                model_name=model,
                accuracy_rate=stats['accuracy_rate'],
                total_predictions=stats['total_predictions'],
                correct_predictions=stats['correct_predictions'],
                last_prediction=stats['last_prediction'],
                current_weight=model_weights.get(model, 0.25)
            ) for model, stats in performance_summary.items()
        ]
        
        # 计算整体统计
        total_predictions = sum(m.total_predictions for m in models)
        total_correct = sum(m.correct_predictions for m in models)
        overall_accuracy = total_correct / total_predictions if total_predictions > 0 else 0
        
        overall_stats = {
            'total_predictions': total_predictions,
            'total_correct': total_correct,
            'overall_accuracy': overall_accuracy,
            'active_models': len([m for m in models if m.total_predictions > 0]),
            'best_model': max(models, key=lambda x: x.accuracy_rate).model_name if models else None
        }
        
        response = ModelPerformanceResponse(
            models=models,
            overall_stats=overall_stats,
            last_updated=datetime.now()
        )
        
        logger.info("模型性能统计获取完成")
        return response
        
    except Exception as e:
        logger.error(f"获取模型性能失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模型性能失败: {str(e)}")

@app.post("/api/v1/prediction/track-result", response_model=TrackingResponse)
async def track_prediction_result(
    request: PredictionTrackingRequest,
    tracker: ModelPerformanceTracker = Depends(get_performance_tracker)
):
    """记录预测结果用于性能跟踪"""
    try:
        logger.info(f"跟踪预测结果，期号: {request.period_number}")
        
        updated_accuracies = {}
        
        # 记录各模型的预测结果
        for model_name, predicted_number in request.predicted_numbers.items():
            confidence = request.confidences.get(model_name, 0.5)
            
            tracker.track_prediction(
                model_name=model_name,
                predicted=predicted_number,
                actual=request.actual_number,
                period=request.period_number,
                confidence=confidence
            )
            
            # 获取更新后的准确率
            updated_accuracies[model_name] = tracker.get_model_accuracy(model_name)
        
        response = TrackingResponse(
            updated_accuracies=updated_accuracies
        )
        
        logger.info(f"预测结果跟踪完成，期号: {request.period_number}")
        return response
        
    except Exception as e:
        logger.error(f"跟踪预测结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"跟踪失败: {str(e)}")

# ============================================================================
# 辅助函数
# ============================================================================

def _get_mock_model_predictions() -> List[ModelPrediction]:
    """获取模拟的模型预测结果（实际应该从模型库获取）"""
    import random
    
    models = ['markov_enhanced', 'deep_learning_cnn_lstm', 'trend_analyzer', 'intelligent_fusion']
    predictions = []
    
    for model in models:
        # 生成随机候选号码和置信度
        candidates = {}
        for _ in range(10):
            number = f"{random.randint(0, 9)}{random.randint(0, 9)}{random.randint(0, 9)}"
            confidence = random.uniform(0.1, 0.9)
            candidates[number] = confidence
        
        # 按置信度排序
        sorted_candidates = sorted(candidates.items(), key=lambda x: x[1], reverse=True)
        top_candidate = sorted_candidates[0][0]
        top_confidence = sorted_candidates[0][1]
        
        prediction = ModelPrediction(
            model_name=model,
            top_candidate=top_candidate,
            top_confidence=top_confidence,
            all_candidates=candidates
        )
        predictions.append(prediction)
    
    return predictions

# ============================================================================
# 预测结果保存API
# ============================================================================

@app.post("/api/v1/prediction/save", response_model=PredictionSaveResponse)
async def save_prediction_result(request: PredictionSaveRequest):
    """保存预测结果到数据库"""
    try:
        logger.info(f"保存预测结果，期号: {request.period_number}, 模型: {request.model_name}")

        # 导入数据访问层
        from src.data.prediction_repository import (ModelPredictionRecord,
                                                    PredictionRepository)

        # 创建数据访问仓库实例
        repository = PredictionRepository()

        # 创建预测记录
        prediction_record = ModelPredictionRecord(
            period_number=request.period_number,
            model_name=request.model_name,
            predicted_number=request.predicted_number,
            confidence=request.confidence,
            prediction_date=datetime.fromisoformat(request.prediction_time),
            metadata=request.metadata
        )

        # 保存到数据库
        prediction_id = repository.save_model_prediction(prediction_record)

        return PredictionSaveResponse(
            success=True,
            prediction_id=prediction_id,
            message="预测结果保存成功",
            saved_at=datetime.now().isoformat()
        )

    except Exception as e:
        logger.error(f"保存预测结果失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"保存失败: {str(e)}")

# ============================================================================
# 趋势分析独立预测API
# ============================================================================

@app.get("/api/v1/prediction/trend-analysis", response_model=TrendPredictionResponse)
async def get_trend_analysis_prediction(
    window_size: int = 30,
    confidence_threshold: float = 0.3
):
    """获取趋势分析模型的独立预测"""
    try:
        logger.info(f"开始趋势分析预测，窗口大小: {window_size}")

        # 导入趋势分析器
        from src.prediction.trend_analysis import TrendAnalyzer

        # 初始化趋势分析器
        analyzer = TrendAnalyzer(window_size=window_size)

        # 加载最近数据
        recent_data = analyzer.load_recent_data(limit=100)

        if len(recent_data) < window_size:
            raise HTTPException(
                status_code=400,
                detail=f"数据不足，需要至少 {window_size} 条记录，当前只有 {len(recent_data)} 条"
            )

        # 执行趋势预测
        predictions = analyzer.predict_next_trends(recent_data)

        # 获取最佳预测
        best_prediction = predictions.get('best_prediction', {})

        return TrendPredictionResponse(
            success=True,
            predicted_number=best_prediction.get('number', '000'),
            confidence=best_prediction.get('confidence', 0.0),
            trend_analysis=predictions.get('trend_details', {}),
            prediction_time=datetime.now().isoformat(),
            window_size=window_size,
            data_points=len(recent_data)
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"趋势分析预测失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"趋势分析预测失败: {str(e)}")

# ============================================================================
# 健康检查
# ============================================================================

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "timestamp": datetime.now(),
        "version": "2.0.0",
        "service": "福彩3D预测系统API"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8888)
