#!/usr/bin/env python3
"""
AI智能Bug检测系统 - 依赖库安装脚本
自动安装所有必需的AI和ML库
"""

import subprocess
import sys
import os
import logging
from typing import List, Tuple

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AIDepInstaller:
    """AI依赖库安装器"""
    
    def __init__(self):
        self.required_packages = [
            # 核心AI库
            ("transformers>=4.48.0", "🤗 Transformers - BERT/RoBERTa模型"),
            ("sentence-transformers", "📝 Sentence Transformers - 语义相似度"),
            ("scikit-learn>=1.3.0", "🔬 Scikit-learn - 机器学习"),
            
            # 深度学习框架
            ("torch>=2.1.0", "🔥 PyTorch - 深度学习框架"),
            ("torchvision", "👁️ TorchVision - 计算机视觉"),
            
            # 数据处理
            ("numpy>=1.21.0", "🔢 NumPy - 数值计算"),
            ("pandas>=1.5.0", "🐼 Pandas - 数据处理"),
            
            # NLP工具
            ("nltk", "📚 NLTK - 自然语言处理"),
            ("spacy", "🚀 spaCy - 高级NLP"),
            
            # 可选增强库
            ("accelerate", "⚡ Accelerate - 模型加速"),
            ("datasets", "📊 Datasets - 数据集管理"),
            ("tokenizers", "🔤 Tokenizers - 快速分词"),
        ]
        
        self.optional_packages = [
            # GPU加速（可选）
            ("bitsandbytes", "🎯 BitsAndBytes - 量化优化"),
            
            # 高级NLP模型
            ("openai", "🤖 OpenAI - GPT模型接口"),
            
            # 可视化
            ("matplotlib", "📈 Matplotlib - 数据可视化"),
            ("seaborn", "🎨 Seaborn - 统计可视化"),
        ]
    
    def check_python_version(self) -> bool:
        """检查Python版本"""
        version = sys.version_info
        logger.info(f"🐍 Python版本: {version.major}.{version.minor}.{version.micro}")
        
        if version.major < 3 or (version.major == 3 and version.minor < 9):
            logger.error("❌ Python版本过低，需要Python 3.9+")
            return False
        
        logger.info("✅ Python版本符合要求")
        return True
    
    def install_package(self, package: str, description: str) -> bool:
        """安装单个包"""
        try:
            logger.info(f"📦 安装 {description}")
            logger.info(f"   命令: pip install {package}")
            
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", package],
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            if result.returncode == 0:
                logger.info(f"✅ {description} 安装成功")
                return True
            else:
                logger.error(f"❌ {description} 安装失败")
                logger.error(f"   错误信息: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ {description} 安装超时")
            return False
        except Exception as e:
            logger.error(f"💥 {description} 安装异常: {e}")
            return False
    
    def verify_installation(self, package_name: str) -> bool:
        """验证包安装"""
        try:
            # 提取包名（去除版本号）
            clean_name = package_name.split('>=')[0].split('==')[0]
            
            # 特殊处理一些包名映射
            import_mapping = {
                'scikit-learn': 'sklearn',
                'sentence-transformers': 'sentence_transformers',
            }
            
            import_name = import_mapping.get(clean_name, clean_name)
            
            __import__(import_name)
            logger.info(f"✅ {clean_name} 验证成功")
            return True
            
        except ImportError:
            logger.warning(f"⚠️ {clean_name} 验证失败")
            return False
        except Exception as e:
            logger.warning(f"⚠️ {clean_name} 验证异常: {e}")
            return False
    
    def install_core_dependencies(self) -> Tuple[int, int]:
        """安装核心依赖"""
        logger.info("🚀 开始安装AI核心依赖库...")
        
        success_count = 0
        total_count = len(self.required_packages)
        
        for package, description in self.required_packages:
            if self.install_package(package, description):
                if self.verify_installation(package):
                    success_count += 1
            
            logger.info("-" * 60)
        
        return success_count, total_count
    
    def install_optional_dependencies(self) -> Tuple[int, int]:
        """安装可选依赖"""
        logger.info("🎁 开始安装AI可选依赖库...")
        
        success_count = 0
        total_count = len(self.optional_packages)
        
        for package, description in self.optional_packages:
            logger.info(f"🤔 是否安装 {description}? (y/n)")
            # 自动安装模式，跳过交互
            if self.install_package(package, description):
                if self.verify_installation(package):
                    success_count += 1
            
            logger.info("-" * 60)
        
        return success_count, total_count
    
    def download_models(self):
        """下载预训练模型"""
        logger.info("📥 开始下载预训练模型...")
        
        try:
            # 下载sentence-transformers模型
            logger.info("📝 下载Sentence Transformers模型...")
            from sentence_transformers import SentenceTransformer
            
            # 下载轻量级模型
            model = SentenceTransformer('all-MiniLM-L6-v2')
            logger.info("✅ Sentence Transformers模型下载完成")
            
        except Exception as e:
            logger.warning(f"⚠️ 模型下载失败: {e}")
            logger.info("💡 模型将在首次使用时自动下载")
    
    def create_test_script(self):
        """创建AI功能测试脚本"""
        test_script = '''#!/usr/bin/env python3
"""
AI智能Bug检测系统 - 功能测试脚本
"""

def test_ai_dependencies():
    """测试AI依赖库"""
    print("🧪 测试AI依赖库...")
    
    try:
        # 测试Transformers
        from transformers import pipeline
        print("✅ Transformers库正常")
        
        # 测试Sentence Transformers
        from sentence_transformers import SentenceTransformer
        print("✅ Sentence Transformers库正常")
        
        # 测试Scikit-learn
        from sklearn.feature_extraction.text import TfidfVectorizer
        print("✅ Scikit-learn库正常")
        
        # 测试PyTorch
        import torch
        print(f"✅ PyTorch库正常 (版本: {torch.__version__})")
        
        # 测试简单功能
        print("\\n🔬 测试AI功能...")
        
        # 测试TF-IDF
        vectorizer = TfidfVectorizer()
        texts = ["这是一个测试", "这是另一个测试"]
        tfidf_matrix = vectorizer.fit_transform(texts)
        print(f"✅ TF-IDF功能正常 (矩阵形状: {tfidf_matrix.shape})")
        
        # 测试语义相似度
        model = SentenceTransformer('all-MiniLM-L6-v2')
        embeddings = model.encode(texts)
        print(f"✅ 语义相似度功能正常 (嵌入维度: {embeddings.shape})")
        
        print("\\n🎉 所有AI功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ AI功能测试失败: {e}")
        return False

if __name__ == "__main__":
    test_ai_dependencies()
'''
        
        with open("test_ai_functionality.py", "w", encoding="utf-8") as f:
            f.write(test_script)
        
        logger.info("📝 AI功能测试脚本已创建: test_ai_functionality.py")
    
    def run_installation(self):
        """运行完整安装流程"""
        logger.info("🤖 AI智能Bug检测系统 - 依赖库安装器")
        logger.info("=" * 60)
        
        # 检查Python版本
        if not self.check_python_version():
            return False
        
        # 安装核心依赖
        core_success, core_total = self.install_core_dependencies()
        
        # 安装可选依赖
        opt_success, opt_total = self.install_optional_dependencies()
        
        # 下载模型
        self.download_models()
        
        # 创建测试脚本
        self.create_test_script()
        
        # 总结报告
        logger.info("=" * 60)
        logger.info("📊 安装总结报告")
        logger.info(f"核心依赖: {core_success}/{core_total} 成功")
        logger.info(f"可选依赖: {opt_success}/{opt_total} 成功")
        
        if core_success == core_total:
            logger.info("🎉 AI智能Bug检测系统依赖安装完成！")
            logger.info("💡 运行 'python test_ai_functionality.py' 测试AI功能")
            return True
        else:
            logger.warning("⚠️ 部分依赖安装失败，AI功能可能受限")
            return False

def main():
    """主函数"""
    installer = AIDepInstaller()
    success = installer.run_installation()
    
    if success:
        print("\n🚀 下一步:")
        print("1. 重启API和Streamlit服务")
        print("2. 运行 'python test_ai_functionality.py' 测试AI功能")
        print("3. 查看实时Bug检测仪表板的AI分析结果")
    else:
        print("\n❌ 安装未完全成功，请检查错误信息")
    
    return success

if __name__ == "__main__":
    main()
