#!/usr/bin/env python3
"""
AI智能Bug检测系统 - 相似度分析器
基于语义相似度的Bug聚合和去重分析
"""

import logging
import numpy as np
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import re
from collections import defaultdict

# AI库
try:
    from sentence_transformers import SentenceTransformer
    from sklearn.metrics.pairwise import cosine_similarity
    from sklearn.feature_extraction.text import TfidfVectorizer
    import torch
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False
    logging.warning("AI库未安装，将使用传统相似度算法")

logger = logging.getLogger(__name__)

class SimilarityAnalyzer:
    """智能相似度分析器"""
    
    def __init__(self, similarity_threshold: float = 0.8):
        self.similarity_threshold = similarity_threshold
        self.sentence_model = None
        self.tfidf_vectorizer = None
        
        # 初始化模型
        self._initialize_models()
        
        # 停用词列表
        self.stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 
            'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 
            'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did'
        }
        
        # 重要关键词权重
        self.important_keywords = {
            'error': 2.0, 'exception': 2.0, 'failed': 2.0, 'timeout': 2.0,
            'null': 1.5, 'undefined': 1.5, 'cannot': 1.5, 'unable': 1.5,
            'database': 1.8, 'api': 1.8, 'network': 1.8, 'security': 2.0
        }
    
    def _initialize_models(self):
        """初始化相似度模型"""
        try:
            if AI_AVAILABLE:
                # 加载句子嵌入模型
                self.sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
                logger.info("✅ 句子嵌入模型加载成功")
            
            # 初始化TF-IDF向量化器
            self.tfidf_vectorizer = TfidfVectorizer(
                max_features=1000,
                stop_words='english',
                ngram_range=(1, 2),
                lowercase=True
            )
            logger.info("✅ TF-IDF向量化器初始化成功")
            
        except Exception as e:
            logger.error(f"模型初始化失败: {e}")
            self.sentence_model = None
            self.tfidf_vectorizer = None
    
    def calculate_similarity(self, text1: str, text2: str, method: str = 'auto') -> float:
        """计算两个文本的相似度"""
        try:
            if method == 'auto':
                if self.sentence_model:
                    return self._semantic_similarity(text1, text2)
                elif self.tfidf_vectorizer:
                    return self._tfidf_similarity(text1, text2)
                else:
                    return self._jaccard_similarity(text1, text2)
            elif method == 'semantic':
                return self._semantic_similarity(text1, text2)
            elif method == 'tfidf':
                return self._tfidf_similarity(text1, text2)
            elif method == 'jaccard':
                return self._jaccard_similarity(text1, text2)
            elif method == 'weighted':
                return self._weighted_similarity(text1, text2)
            else:
                raise ValueError(f"不支持的相似度方法: {method}")
                
        except Exception as e:
            logger.error(f"相似度计算失败: {e}")
            return 0.0
    
    def _semantic_similarity(self, text1: str, text2: str) -> float:
        """语义相似度 (使用句子嵌入)"""
        try:
            if not self.sentence_model:
                return self._jaccard_similarity(text1, text2)
            
            # 生成句子嵌入
            embeddings = self.sentence_model.encode([text1, text2])
            
            # 计算余弦相似度
            similarity = cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]
            
            return float(similarity)
            
        except Exception as e:
            logger.error(f"语义相似度计算失败: {e}")
            return self._jaccard_similarity(text1, text2)
    
    def _tfidf_similarity(self, text1: str, text2: str) -> float:
        """TF-IDF相似度"""
        try:
            if not self.tfidf_vectorizer:
                return self._jaccard_similarity(text1, text2)
            
            # 拟合并转换文本
            tfidf_matrix = self.tfidf_vectorizer.fit_transform([text1, text2])
            
            # 计算余弦相似度
            similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
            
            return float(similarity)
            
        except Exception as e:
            logger.error(f"TF-IDF相似度计算失败: {e}")
            return self._jaccard_similarity(text1, text2)
    
    def _jaccard_similarity(self, text1: str, text2: str) -> float:
        """Jaccard相似度 (基于词汇重叠)"""
        try:
            # 文本预处理
            words1 = set(self._preprocess_text(text1))
            words2 = set(self._preprocess_text(text2))
            
            if not words1 or not words2:
                return 0.0
            
            # 计算Jaccard相似度
            intersection = len(words1 & words2)
            union = len(words1 | words2)
            
            return intersection / union if union > 0 else 0.0
            
        except Exception as e:
            logger.error(f"Jaccard相似度计算失败: {e}")
            return 0.0
    
    def _weighted_similarity(self, text1: str, text2: str) -> float:
        """加权相似度 (考虑关键词重要性)"""
        try:
            words1 = self._preprocess_text(text1)
            words2 = self._preprocess_text(text2)
            
            if not words1 or not words2:
                return 0.0
            
            # 计算加权交集
            common_words = set(words1) & set(words2)
            weighted_intersection = sum(
                self.important_keywords.get(word, 1.0) for word in common_words
            )
            
            # 计算加权并集
            all_words = set(words1) | set(words2)
            weighted_union = sum(
                self.important_keywords.get(word, 1.0) for word in all_words
            )
            
            return weighted_intersection / weighted_union if weighted_union > 0 else 0.0
            
        except Exception as e:
            logger.error(f"加权相似度计算失败: {e}")
            return 0.0
    
    def _preprocess_text(self, text: str) -> List[str]:
        """文本预处理"""
        # 转换为小写
        text = text.lower()
        
        # 移除特殊字符，保留字母数字和空格
        text = re.sub(r'[^a-zA-Z0-9\s]', ' ', text)
        
        # 分词并移除停用词
        words = [
            word for word in text.split() 
            if word not in self.stop_words and len(word) > 2
        ]
        
        return words
    
    def find_similar_errors(self, target_error: str, error_list: List[Dict], 
                          threshold: float = None) -> List[Dict]:
        """查找相似错误"""
        if threshold is None:
            threshold = self.similarity_threshold
        
        similar_errors = []
        
        for error in error_list:
            error_message = error.get('error_message', '')
            similarity = self.calculate_similarity(target_error, error_message)
            
            if similarity >= threshold:
                similar_errors.append({
                    **error,
                    'similarity_score': similarity
                })
        
        # 按相似度排序
        similar_errors.sort(key=lambda x: x['similarity_score'], reverse=True)
        
        return similar_errors
    
    def cluster_similar_errors(self, errors: List[Dict], threshold: float = None) -> List[List[Dict]]:
        """聚类相似错误"""
        if threshold is None:
            threshold = self.similarity_threshold
        
        clusters = []
        processed = set()
        
        for i, error in enumerate(errors):
            if i in processed:
                continue
            
            # 创建新聚类
            cluster = [error]
            processed.add(i)
            
            # 查找相似错误
            for j, other_error in enumerate(errors[i+1:], i+1):
                if j in processed:
                    continue
                
                similarity = self.calculate_similarity(
                    error.get('error_message', ''),
                    other_error.get('error_message', '')
                )
                
                if similarity >= threshold:
                    cluster.append(other_error)
                    processed.add(j)
            
            clusters.append(cluster)
        
        return clusters
    
    def analyze_error_patterns(self, errors: List[Dict]) -> Dict:
        """分析错误模式"""
        try:
            patterns = {
                'total_errors': len(errors),
                'unique_patterns': 0,
                'duplicate_groups': [],
                'common_keywords': {},
                'similarity_distribution': defaultdict(int)
            }
            
            # 聚类分析
            clusters = self.cluster_similar_errors(errors)
            patterns['unique_patterns'] = len(clusters)
            
            # 识别重复组
            for cluster in clusters:
                if len(cluster) > 1:
                    patterns['duplicate_groups'].append({
                        'count': len(cluster),
                        'representative': cluster[0].get('error_message', '')[:100],
                        'errors': cluster
                    })
            
            # 关键词频率分析
            all_text = ' '.join([error.get('error_message', '') for error in errors])
            words = self._preprocess_text(all_text)
            word_counts = defaultdict(int)
            
            for word in words:
                word_counts[word] += 1
            
            # 取前10个最常见的关键词
            patterns['common_keywords'] = dict(
                sorted(word_counts.items(), key=lambda x: x[1], reverse=True)[:10]
            )
            
            # 相似度分布统计
            for i in range(len(errors)):
                for j in range(i+1, len(errors)):
                    similarity = self.calculate_similarity(
                        errors[i].get('error_message', ''),
                        errors[j].get('error_message', '')
                    )
                    
                    # 分组统计
                    if similarity >= 0.9:
                        patterns['similarity_distribution']['very_high'] += 1
                    elif similarity >= 0.7:
                        patterns['similarity_distribution']['high'] += 1
                    elif similarity >= 0.5:
                        patterns['similarity_distribution']['medium'] += 1
                    elif similarity >= 0.3:
                        patterns['similarity_distribution']['low'] += 1
                    else:
                        patterns['similarity_distribution']['very_low'] += 1
            
            return patterns
            
        except Exception as e:
            logger.error(f"错误模式分析失败: {e}")
            return {'error': str(e)}
    
    def deduplicate_errors(self, errors: List[Dict], threshold: float = None) -> List[Dict]:
        """错误去重"""
        if threshold is None:
            threshold = self.similarity_threshold
        
        unique_errors = []
        processed = set()
        
        for i, error in enumerate(errors):
            if i in processed:
                continue
            
            # 查找重复错误
            duplicates = [error]
            processed.add(i)
            
            for j, other_error in enumerate(errors[i+1:], i+1):
                if j in processed:
                    continue
                
                similarity = self.calculate_similarity(
                    error.get('error_message', ''),
                    other_error.get('error_message', '')
                )
                
                if similarity >= threshold:
                    duplicates.append(other_error)
                    processed.add(j)
            
            # 合并重复错误信息
            merged_error = self._merge_duplicate_errors(duplicates)
            unique_errors.append(merged_error)
        
        return unique_errors
    
    def _merge_duplicate_errors(self, duplicates: List[Dict]) -> Dict:
        """合并重复错误"""
        if len(duplicates) == 1:
            return duplicates[0]
        
        # 选择最完整的错误作为基础
        base_error = max(duplicates, key=lambda x: len(str(x)))
        
        # 添加重复信息
        base_error['duplicate_count'] = len(duplicates)
        base_error['first_occurrence'] = min(
            error.get('created_at', '') for error in duplicates
        )
        base_error['last_occurrence'] = max(
            error.get('created_at', '') for error in duplicates
        )
        base_error['duplicate_ids'] = [
            error.get('id', '') for error in duplicates
        ]
        
        return base_error
    
    def get_similarity_report(self, errors: List[Dict]) -> Dict:
        """生成相似度分析报告"""
        try:
            report = {
                'analysis_time': datetime.now().isoformat(),
                'total_errors': len(errors),
                'model_info': {
                    'semantic_model_available': self.sentence_model is not None,
                    'tfidf_available': self.tfidf_vectorizer is not None,
                    'similarity_threshold': self.similarity_threshold
                }
            }
            
            if errors:
                # 模式分析
                patterns = self.analyze_error_patterns(errors)
                report['patterns'] = patterns
                
                # 去重分析
                unique_errors = self.deduplicate_errors(errors)
                report['deduplication'] = {
                    'original_count': len(errors),
                    'unique_count': len(unique_errors),
                    'duplicate_rate': (len(errors) - len(unique_errors)) / len(errors) * 100
                }
                
                # 聚类分析
                clusters = self.cluster_similar_errors(errors)
                report['clustering'] = {
                    'cluster_count': len(clusters),
                    'largest_cluster_size': max(len(cluster) for cluster in clusters),
                    'average_cluster_size': sum(len(cluster) for cluster in clusters) / len(clusters)
                }
            
            return report
            
        except Exception as e:
            logger.error(f"相似度报告生成失败: {e}")
            return {'error': str(e)}

# 使用示例
if __name__ == "__main__":
    # 初始化分析器
    analyzer = SimilarityAnalyzer(similarity_threshold=0.7)
    
    # 测试错误数据
    test_errors = [
        {'id': '1', 'error_message': 'Cannot read property "innerHTML" of null', 'created_at': '2025-07-24T10:00:00'},
        {'id': '2', 'error_message': 'Cannot read property "value" of null', 'created_at': '2025-07-24T10:05:00'},
        {'id': '3', 'error_message': 'HTTP 500 Internal Server Error', 'created_at': '2025-07-24T10:10:00'},
        {'id': '4', 'error_message': 'Cannot read property "textContent" of null', 'created_at': '2025-07-24T10:15:00'},
        {'id': '5', 'error_message': 'API endpoint returned 500 error', 'created_at': '2025-07-24T10:20:00'},
    ]
    
    print("🔍 相似度分析器测试")
    print("=" * 50)
    
    # 相似度计算测试
    print("\n📊 相似度计算:")
    for i in range(len(test_errors)):
        for j in range(i+1, len(test_errors)):
            similarity = analyzer.calculate_similarity(
                test_errors[i]['error_message'],
                test_errors[j]['error_message']
            )
            print(f"错误{i+1} vs 错误{j+1}: {similarity:.3f}")
    
    # 聚类测试
    print(f"\n🔗 错误聚类:")
    clusters = analyzer.cluster_similar_errors(test_errors)
    for i, cluster in enumerate(clusters):
        print(f"聚类 {i+1}: {len(cluster)} 个错误")
        for error in cluster:
            print(f"  - {error['error_message'][:50]}...")
    
    # 去重测试
    print(f"\n🧹 错误去重:")
    unique_errors = analyzer.deduplicate_errors(test_errors)
    print(f"原始错误数: {len(test_errors)}")
    print(f"去重后错误数: {len(unique_errors)}")
    
    # 生成报告
    print(f"\n📋 相似度分析报告:")
    report = analyzer.get_similarity_report(test_errors)
    print(f"总错误数: {report['total_errors']}")
    if 'deduplication' in report:
        print(f"重复率: {report['deduplication']['duplicate_rate']:.1f}%")
    if 'clustering' in report:
        print(f"聚类数: {report['clustering']['cluster_count']}")
        print(f"最大聚类大小: {report['clustering']['largest_cluster_size']}")
