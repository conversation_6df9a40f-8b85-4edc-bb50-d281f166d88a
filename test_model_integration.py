"""
模型验证集成测试

测试模型验证机制与IntelligentFusionSystem的集成
"""

import sys
import os
sys.path.append('src')

from prediction.intelligent_fusion import IntelligentFusionSystem
from prediction.model_validation import MarkovCrossValidator
from prediction.markov_validator import MarkovModelValidator


def test_fusion_system_initialization():
    """测试融合系统初始化"""
    print("🔧 测试融合系统初始化...")
    
    try:
        # 创建融合系统
        fusion_system = IntelligentFusionSystem()
        
        # 验证验证组件是否正确初始化
        has_cross_validator = hasattr(fusion_system, 'cross_validator')
        has_model_selector = hasattr(fusion_system, 'model_selector')
        
        print(f"   验证组件初始化: {'✅' if has_cross_validator and has_model_selector else '❌'}")
        print(f"   交叉验证器: {'✅' if has_cross_validator else '❌'}")
        print(f"   模型选择器: {'✅' if has_model_selector else '❌'}")
        
        # 验证验证状态
        has_validation_enabled = hasattr(fusion_system, 'validation_enabled')
        has_validation_results = hasattr(fusion_system, 'validation_results')
        
        print(f"   验证状态: {'✅' if has_validation_enabled and has_validation_results else '❌'}")
        
        return has_cross_validator and has_model_selector and has_validation_enabled and has_validation_results
        
    except Exception as e:
        print(f"   ❌ 融合系统初始化失败: {e}")
        return False


def test_validation_methods():
    """测试验证方法"""
    print("\n🧪 测试验证方法...")
    
    try:
        # 创建融合系统
        fusion_system = IntelligentFusionSystem()
        
        # 验证是否有验证方法
        has_validate_method = hasattr(fusion_system, 'validate_markov_model')
        has_status_method = hasattr(fusion_system, 'get_validation_status')
        
        print(f"   验证方法: {'✅' if has_validate_method and has_status_method else '❌'}")
        print(f"   validate_markov_model: {'✅' if has_validate_method else '❌'}")
        print(f"   get_validation_status: {'✅' if has_status_method else '❌'}")
        
        # 如果有验证方法，测试获取验证状态
        if has_status_method:
            try:
                status = fusion_system.get_validation_status()
                print(f"   验证状态: {status}")
                return True
            except Exception as e:
                print(f"   ❌ 获取验证状态失败: {e}")
                return False
        
        return has_validate_method and has_status_method
        
    except Exception as e:
        print(f"   ❌ 测试验证方法失败: {e}")
        return False


def test_direct_validation():
    """测试直接使用验证器"""
    print("\n📊 测试直接使用验证器...")
    
    try:
        # 创建验证器
        validator = MarkovModelValidator()
        
        # 执行简化的验证
        print("   执行简化验证...")
        result = validator.validate_markov_model(
            k_folds=2,
            data_limit=200
        )
        
        # 验证结果
        if 'overall_results' in result:
            overall = result['overall_results']
            accuracy = overall.get('accuracy_metrics', {}).get('exact_match', 0.0)
            diversity = overall.get('diversity_metrics', {}).get('simpson_diversity', 0.0)
            
            print(f"   验证结果:")
            print(f"     准确率: {accuracy:.4f}")
            print(f"     多样性: {diversity:.4f}")
            
            return True
        else:
            print(f"   ❌ 无有效验证结果")
            return False
        
    except Exception as e:
        print(f"   ❌ 直接验证失败: {e}")
        return False


def test_fusion_system_validation():
    """测试融合系统验证"""
    print("\n🔄 测试融合系统验证...")
    
    try:
        # 创建融合系统
        fusion_system = IntelligentFusionSystem()
        
        # 检查是否有验证方法
        if not hasattr(fusion_system, 'validate_markov_model'):
            print("   ❌ 融合系统没有验证方法")
            return False
        
        # 执行简化的验证
        print("   执行简化验证...")
        try:
            result = fusion_system.validate_markov_model(
                k_folds=2,
                data_limit=200
            )
            
            # 验证结果
            if isinstance(result, dict) and 'overall_results' in result:
                overall = result['overall_results']
                accuracy = overall.get('accuracy_metrics', {}).get('exact_match', 0.0)
                diversity = overall.get('diversity_metrics', {}).get('simpson_diversity', 0.0)
                
                print(f"   验证结果:")
                print(f"     准确率: {accuracy:.4f}")
                print(f"     多样性: {diversity:.4f}")
                
                # 检查验证状态是否更新
                status = fusion_system.get_validation_status()
                print(f"   验证状态已更新: {'✅' if status.get('last_validation_time') else '❌'}")
                
                return True
            else:
                print(f"   ❌ 无有效验证结果: {result}")
                return False
                
        except Exception as e:
            print(f"   ❌ 执行验证失败: {e}")
            return False
        
    except Exception as e:
        print(f"   ❌ 融合系统验证测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 模型验证集成测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("融合系统初始化", test_fusion_system_initialization()))
    test_results.append(("验证方法", test_validation_methods()))
    test_results.append(("直接验证", test_direct_validation()))
    test_results.append(("融合系统验证", test_fusion_system_validation()))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总:")
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed_tests += 1
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！模型验证集成成功！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步调试")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
