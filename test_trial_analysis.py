"""
测试试机号码关联分析
"""

import sys
import os

# 添加src路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_trial_analysis():
    print("=== 测试试机号码关联分析 ===")
    
    try:
        from prediction.trial_number_analysis import TrialNumberAnalyzer
        
        # 创建分析器
        analyzer = TrialNumberAnalyzer()
        
        # 测试数据加载
        print("1. 测试数据加载...")
        records = analyzer.load_trial_data()
        print(f"✓ 成功加载 {len(records)} 条试机号码记录")
        
        if len(records) > 0:
            print(f"  示例记录: {records[0]}")
            print(f"  日期范围: {records[0]['date']} 到 {records[-1]['date']}")
        
        # 测试位置差值分析
        if len(records) >= 10:
            print("\n2. 测试位置差值分析...")
            position_analysis = analyzer.analyze_position_differences(records[:100])  # 使用前100条
            print(f"✓ 位置差值分析完成")
            
            for pos, analysis in position_analysis.items():
                print(f"  {pos}: 平均最小差值 {analysis['avg_min_diff']:.2f}")
        
        # 测试预热效应分析
        if len(records) >= 20:
            print("\n3. 测试预热效应分析...")
            preheating_analysis = analyzer.analyze_preheating_effect(records[:50])  # 使用前50条
            print(f"✓ 预热效应分析完成")
            
            if 'immediate' in preheating_analysis:
                immediate = preheating_analysis['immediate']
                print(f"  当期预热命中率: {immediate.get('hit_probability', 0):.3f}")
        
        # 测试机器关联分析
        if len(records) >= 10:
            print("\n4. 测试机器关联分析...")
            machine_analysis = analyzer.analyze_machine_correlation(records[:30])  # 使用前30条
            print(f"✓ 机器关联分析完成")
            print(f"  机器配对组合数: {machine_analysis['total_combinations']}")
        
        # 测试预测模型构建
        if len(records) >= 20:
            print("\n5. 测试预测模型构建...")
            prediction_model = analyzer.build_trial_prediction_model(records[:50])
            print(f"✓ 预测模型构建完成")
            print(f"  样本数量: {prediction_model.get('sample_count', 0)}")
            
            if 'feature_importance' in prediction_model:
                print("  特征重要性:")
                for feature, importance in list(prediction_model['feature_importance'].items())[:3]:
                    print(f"    {feature}: {importance:.4f}")
        
        print("\n✅ 试机号码关联分析测试成功!")
        return True
        
    except Exception as e:
        print(f"✗ 试机号码关联分析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    success = test_trial_analysis()
    print(f"\n测试结果: {'成功' if success else '失败'}")
    return success

if __name__ == "__main__":
    main()
