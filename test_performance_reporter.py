"""
性能报告生成器测试

测试性能报告生成器的功能
"""

import sys
import os
sys.path.append('src')

from prediction.performance_reporter import PerformanceReporter
from prediction.markov_validator import MarkovModelValidator


def test_reporter_initialization():
    """测试报告生成器初始化"""
    print("🔧 测试报告生成器初始化...")
    
    try:
        reporter = PerformanceReporter()
        
        # 验证属性
        has_db_path = hasattr(reporter, 'db_path')
        has_validator = hasattr(reporter, 'validator')
        has_report_dir = hasattr(reporter, 'report_dir')
        
        print(f"   数据库路径: {'✅' if has_db_path else '❌'}")
        print(f"   验证器: {'✅' if has_validator else '❌'}")
        print(f"   报告目录: {'✅' if has_report_dir else '❌'}")
        
        # 验证报告目录是否创建
        if has_report_dir:
            dir_exists = os.path.exists(reporter.report_dir)
            print(f"   报告目录存在: {'✅' if dir_exists else '❌'}")
            return has_db_path and has_validator and has_report_dir and dir_exists
        
        return has_db_path and has_validator and has_report_dir
        
    except Exception as e:
        print(f"   ❌ 初始化失败: {e}")
        return False


def test_validation_and_report():
    """测试验证和报告生成"""
    print("\n📊 测试验证和报告生成...")
    
    try:
        # 创建验证器
        validator = MarkovModelValidator()
        
        # 执行简化验证
        print("   执行简化验证...")
        validation_result = validator.validate_markov_model(
            k_folds=2,
            data_limit=200
        )
        
        if 'overall_results' not in validation_result:
            print("   ❌ 验证失败，无法生成报告")
            return False
        
        print("   ✅ 验证完成")
        
        # 创建报告生成器
        reporter = PerformanceReporter()
        
        # 生成报告
        print("   生成性能报告...")
        report_path = reporter.generate_comprehensive_report(
            validation_result, 
            "test_validation_report"
        )
        
        if report_path and os.path.exists(report_path):
            print(f"   ✅ 报告生成成功: {report_path}")
            
            # 检查报告内容
            html_file = os.path.join(report_path, 'performance_report.html')
            json_file = os.path.join(report_path, 'validation_summary.json')
            
            html_exists = os.path.exists(html_file)
            json_exists = os.path.exists(json_file)
            
            print(f"   HTML报告: {'✅' if html_exists else '❌'}")
            print(f"   JSON摘要: {'✅' if json_exists else '❌'}")
            
            # 检查图表文件
            chart_files = [f for f in os.listdir(report_path) if f.endswith('.png')]
            print(f"   图表数量: {len(chart_files)}")
            
            return html_exists and json_exists
        else:
            print("   ❌ 报告生成失败")
            return False
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False


def test_report_content():
    """测试报告内容"""
    print("\n📋 测试报告内容...")
    
    try:
        # 创建测试数据
        test_validation_result = {
            'overall_results': {
                'total_predictions': 100,
                'accuracy_metrics': {
                    'exact_match': 0.02,
                    'digit_accuracy': 0.25,
                    'position_accuracy': [0.1, 0.15, 0.12]
                },
                'diversity_metrics': {
                    'simpson_diversity': 0.95,
                    'unique_ratio': 0.8,
                    'entropy': 2.5
                },
                'aic_bic': {
                    'aic': 1500.0,
                    'bic': 1600.0,
                    'log_likelihood': -750.0
                }
            },
            'model_params': {
                'transition_window_size': 1000,
                'probability_window_size': 500,
                'smoothing_alpha': 1.0
            },
            'validation_params': {
                'k_folds': 3,
                'data_limit': 1000
            },
            'fold_results': [
                {
                    'fold_idx': 0,
                    'train_size': 600,
                    'val_size': 200,
                    'predictions_count': 50,
                    'accuracy_metrics': {
                        'exact_match': 0.02,
                        'digit_accuracy': 0.24,
                        'position_accuracy': [0.1, 0.14, 0.12]
                    },
                    'diversity_metrics': {
                        'simpson_diversity': 0.94,
                        'unique_ratio': 0.8,
                        'entropy': 2.4
                    },
                    'aic_bic': {
                        'aic': 750.0,
                        'bic': 800.0
                    }
                },
                {
                    'fold_idx': 1,
                    'train_size': 600,
                    'val_size': 200,
                    'predictions_count': 50,
                    'accuracy_metrics': {
                        'exact_match': 0.02,
                        'digit_accuracy': 0.26,
                        'position_accuracy': [0.1, 0.16, 0.12]
                    },
                    'diversity_metrics': {
                        'simpson_diversity': 0.96,
                        'unique_ratio': 0.8,
                        'entropy': 2.6
                    },
                    'aic_bic': {
                        'aic': 750.0,
                        'bic': 800.0
                    }
                }
            ]
        }
        
        # 创建报告生成器
        reporter = PerformanceReporter()
        
        # 生成报告
        print("   生成测试报告...")
        report_path = reporter.generate_comprehensive_report(
            test_validation_result,
            "content_test_report"
        )
        
        if report_path and os.path.exists(report_path):
            print(f"   ✅ 测试报告生成成功: {report_path}")
            
            # 验证文件内容
            html_file = os.path.join(report_path, 'performance_report.html')
            json_file = os.path.join(report_path, 'validation_summary.json')
            
            # 检查HTML内容
            if os.path.exists(html_file):
                with open(html_file, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # 检查关键内容
                has_title = '马尔可夫模型性能报告' in html_content
                has_metrics = '总体性能指标' in html_content
                has_params = '模型参数' in html_content
                has_results = '详细验证结果' in html_content
                
                print(f"   HTML标题: {'✅' if has_title else '❌'}")
                print(f"   性能指标: {'✅' if has_metrics else '❌'}")
                print(f"   模型参数: {'✅' if has_params else '❌'}")
                print(f"   详细结果: {'✅' if has_results else '❌'}")
                
                html_valid = has_title and has_metrics and has_params and has_results
            else:
                html_valid = False
            
            # 检查JSON内容
            if os.path.exists(json_file):
                import json
                with open(json_file, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                
                has_metadata = 'report_metadata' in json_data
                has_performance = 'model_performance' in json_data
                has_recommendations = 'recommendations' in json_data
                
                print(f"   JSON元数据: {'✅' if has_metadata else '❌'}")
                print(f"   性能数据: {'✅' if has_performance else '❌'}")
                print(f"   优化建议: {'✅' if has_recommendations else '❌'}")
                
                json_valid = has_metadata and has_performance and has_recommendations
            else:
                json_valid = False
            
            return html_valid and json_valid
        else:
            print("   ❌ 测试报告生成失败")
            return False
        
    except Exception as e:
        print(f"   ❌ 内容测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 性能报告生成器测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("报告生成器初始化", test_reporter_initialization()))
    test_results.append(("验证和报告生成", test_validation_and_report()))
    test_results.append(("报告内容验证", test_report_content()))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总:")
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed_tests += 1
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！性能报告生成器功能正常！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步调试")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
