#!/usr/bin/env python3
"""
简单调试脚本
"""

import sys
sys.path.append('src')

def simple_debug():
    try:
        from core.polars_engine import PolarsEngine
        
        print("1. 创建Polars引擎...")
        engine = PolarsEngine()
        
        print(f"2. DataFrame状态: {engine.df is not None}")
        
        if engine.df is None:
            print("3. DataFrame为空，尝试加载数据...")
            
            # 尝试从数据库加载
            from core.database_manager import DatabaseManager
            db_manager = DatabaseManager("data/lottery.db")
            
            record_count = db_manager.get_records_count()
            print(f"4. 数据库记录数: {record_count}")
            
            if record_count > 0:
                print("5. 从数据库加载数据...")
                import polars as pl
                with db_manager._get_connection() as conn:
                    df = pl.read_database("SELECT * FROM lottery_records ORDER BY date LIMIT 5", conn)
                    print(f"6. 加载了 {len(df)} 条记录")
                    print("7. 列名:", df.columns)
                    
                    # 检查是否有必要的列
                    required_cols = ["sum_value", "span_value", "sales_amount", "date"]
                    for col in required_cols:
                        if col in df.columns:
                            print(f"✅ 有列: {col}")
                        else:
                            print(f"❌ 缺少列: {col}")
            else:
                print("❌ 数据库为空")
        else:
            print("3. DataFrame已存在")
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simple_debug()
