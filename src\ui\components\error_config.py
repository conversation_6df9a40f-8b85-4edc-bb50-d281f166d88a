"""
错误处理配置文件
定义错误类型、错误消息模板和恢复策略
"""

from typing import Dict, List, Any
from enum import Enum


class ErrorType(Enum):
    """错误类型枚举"""
    NETWORK_ERROR = "network_error"
    API_ERROR = "api_error"
    DATA_ERROR = "data_error"
    MODEL_ERROR = "model_error"
    FILE_ERROR = "file_error"
    IMPORT_ERROR = "import_error"
    VALIDATION_ERROR = "validation_error"
    SYSTEM_ERROR = "system_error"


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


# 错误消息模板
ERROR_MESSAGES = {
    ErrorType.NETWORK_ERROR: {
        "title": "网络连接错误",
        "icon": "🌐",
        "suggestions": [
            "检查网络连接状态",
            "确认服务是否正在运行",
            "检查防火墙设置",
            "重试连接"
        ]
    },
    ErrorType.API_ERROR: {
        "title": "API服务错误",
        "icon": "🔌",
        "suggestions": [
            "检查API服务是否正在运行",
            "确认API端点地址正确",
            "验证请求参数格式",
            "查看API服务日志"
        ]
    },
    ErrorType.DATA_ERROR: {
        "title": "数据处理错误",
        "icon": "📊",
        "suggestions": [
            "检查数据源连接状态",
            "确认数据格式是否正确",
            "验证数据完整性",
            "重新加载数据"
        ]
    },
    ErrorType.MODEL_ERROR: {
        "title": "模型处理错误",
        "icon": "🤖",
        "suggestions": [
            "检查模型文件是否存在",
            "确认模型格式是否正确",
            "验证输入数据格式",
            "重新加载模型"
        ]
    },
    ErrorType.FILE_ERROR: {
        "title": "文件操作错误",
        "icon": "📁",
        "suggestions": [
            "检查文件路径是否正确",
            "确认文件是否存在",
            "验证文件权限设置",
            "检查磁盘空间"
        ]
    },
    ErrorType.IMPORT_ERROR: {
        "title": "模块导入错误",
        "icon": "📦",
        "suggestions": [
            "检查模块文件是否存在",
            "确认文件路径是否正确",
            "验证模块依赖是否安装",
            "检查Python环境配置"
        ]
    },
    ErrorType.VALIDATION_ERROR: {
        "title": "数据验证错误",
        "icon": "✅",
        "suggestions": [
            "检查输入数据格式",
            "确认数据类型是否正确",
            "验证数据范围和约束",
            "查看数据验证规则"
        ]
    },
    ErrorType.SYSTEM_ERROR: {
        "title": "系统错误",
        "icon": "⚙️",
        "suggestions": [
            "重启相关服务",
            "检查系统资源使用情况",
            "查看系统日志",
            "联系技术支持"
        ]
    }
}

# 错误严重程度配置
ERROR_SEVERITY_CONFIG = {
    ErrorSeverity.LOW: {
        "color": "#ffa500",
        "background": "#fff8e1",
        "border": "#ffa500",
        "auto_recovery": True,
        "show_details": False
    },
    ErrorSeverity.MEDIUM: {
        "color": "#ff6b35",
        "background": "#fff2f0",
        "border": "#ff6b35",
        "auto_recovery": False,
        "show_details": True
    },
    ErrorSeverity.HIGH: {
        "color": "#ff4b4b",
        "background": "#fff2f2",
        "border": "#ff4b4b",
        "auto_recovery": False,
        "show_details": True
    },
    ErrorSeverity.CRITICAL: {
        "color": "#dc143c",
        "background": "#ffe6e6",
        "border": "#dc143c",
        "auto_recovery": False,
        "show_details": True
    }
}

# 错误恢复策略
RECOVERY_STRATEGIES = {
    ErrorType.NETWORK_ERROR: [
        {
            "name": "重试连接",
            "action": "retry_connection",
            "icon": "🔄",
            "description": "重新尝试建立网络连接"
        },
        {
            "name": "检查服务状态",
            "action": "check_service_status",
            "icon": "🔍",
            "description": "检查相关服务的运行状态"
        },
        {
            "name": "网络诊断",
            "action": "network_diagnostic",
            "icon": "🌐",
            "description": "执行网络连接诊断"
        }
    ],
    ErrorType.API_ERROR: [
        {
            "name": "重新调用API",
            "action": "retry_api_call",
            "icon": "🔄",
            "description": "重新执行API调用"
        },
        {
            "name": "检查API状态",
            "action": "check_api_status",
            "icon": "🔍",
            "description": "检查API服务状态"
        },
        {
            "name": "查看API文档",
            "action": "view_api_docs",
            "icon": "📚",
            "description": "查看API接口文档"
        }
    ],
    ErrorType.DATA_ERROR: [
        {
            "name": "重新加载数据",
            "action": "reload_data",
            "icon": "🔄",
            "description": "重新加载数据源"
        },
        {
            "name": "数据验证",
            "action": "validate_data",
            "icon": "✅",
            "description": "执行数据完整性验证"
        },
        {
            "name": "数据修复",
            "action": "repair_data",
            "icon": "🔧",
            "description": "尝试修复数据问题"
        }
    ],
    ErrorType.MODEL_ERROR: [
        {
            "name": "重新加载模型",
            "action": "reload_model",
            "icon": "🔄",
            "description": "重新加载预测模型"
        },
        {
            "name": "模型诊断",
            "action": "model_diagnostic",
            "icon": "🔍",
            "description": "执行模型状态诊断"
        },
        {
            "name": "使用备用模型",
            "action": "use_backup_model",
            "icon": "🔄",
            "description": "切换到备用预测模型"
        }
    ]
}

# 常见错误码映射
HTTP_ERROR_CODES = {
    400: {
        "title": "请求参数错误",
        "message": "请求参数格式不正确或缺少必需参数",
        "suggestions": ["检查请求参数格式", "确认必需参数是否提供", "查看API文档"]
    },
    401: {
        "title": "身份验证失败",
        "message": "访问被拒绝，需要有效的身份验证",
        "suggestions": ["检查访问凭证", "确认权限设置", "重新登录"]
    },
    403: {
        "title": "访问被禁止",
        "message": "没有权限访问此资源",
        "suggestions": ["检查用户权限", "确认资源访问策略", "联系管理员"]
    },
    404: {
        "title": "资源未找到",
        "message": "请求的资源不存在",
        "suggestions": ["检查URL地址", "确认资源是否存在", "查看路由配置"]
    },
    429: {
        "title": "请求频率限制",
        "message": "请求过于频繁，已被限制",
        "suggestions": ["降低请求频率", "等待一段时间后重试", "检查限流配置"]
    },
    500: {
        "title": "服务器内部错误",
        "message": "服务器处理请求时发生错误",
        "suggestions": ["重试请求", "检查服务器日志", "联系技术支持"]
    },
    502: {
        "title": "网关错误",
        "message": "网关或代理服务器错误",
        "suggestions": ["检查网关配置", "确认上游服务状态", "重试请求"]
    },
    503: {
        "title": "服务不可用",
        "message": "服务暂时不可用",
        "suggestions": ["等待服务恢复", "检查服务状态", "使用备用服务"]
    },
    504: {
        "title": "网关超时",
        "message": "网关或代理服务器超时",
        "suggestions": ["增加超时时间", "检查网络连接", "重试请求"]
    }
}

# 系统健康检查配置
HEALTH_CHECK_CONFIG = {
    "endpoints": [
        {
            "name": "API服务",
            "url": "http://127.0.0.1:8888/health",
            "timeout": 5,
            "critical": True
        },
        {
            "name": "数据状态",
            "url": "http://127.0.0.1:8888/api/v1/data/status",
            "timeout": 3,
            "critical": True
        },
        {
            "name": "模型状态",
            "url": "http://127.0.0.1:8888/api/v1/models/status",
            "timeout": 5,
            "critical": False
        }
    ],
    "external_sources": [
        {
            "name": "福彩3D数据源",
            "url": "https://data.17500.cn/3d_asc.txt",
            "timeout": 10,
            "method": "HEAD"
        }
    ]
}

# 错误处理配置
ERROR_HANDLER_CONFIG = {
    "log_level": "INFO",
    "log_file_max_size": 10 * 1024 * 1024,  # 10MB
    "log_file_backup_count": 5,
    "auto_recovery_enabled": True,
    "show_technical_details": False,
    "error_reporting_enabled": True,
    "max_retry_attempts": 3,
    "retry_delay": 1.0,
    "timeout_default": 10.0
}


def get_error_config(error_type: ErrorType) -> Dict[str, Any]:
    """获取错误类型配置"""
    return ERROR_MESSAGES.get(error_type, ERROR_MESSAGES[ErrorType.SYSTEM_ERROR])


def get_severity_config(severity: ErrorSeverity) -> Dict[str, Any]:
    """获取错误严重程度配置"""
    return ERROR_SEVERITY_CONFIG.get(severity, ERROR_SEVERITY_CONFIG[ErrorSeverity.MEDIUM])


def get_recovery_strategies(error_type: ErrorType) -> List[Dict[str, str]]:
    """获取错误恢复策略"""
    return RECOVERY_STRATEGIES.get(error_type, [])


def get_http_error_info(status_code: int) -> Dict[str, Any]:
    """获取HTTP错误信息"""
    return HTTP_ERROR_CODES.get(status_code, {
        "title": f"HTTP错误 {status_code}",
        "message": f"服务器返回错误状态码: {status_code}",
        "suggestions": ["检查请求格式", "重试请求", "联系技术支持"]
    })
