#!/usr/bin/env python3
"""
数据库连接池优化
减少连接创建和销毁的开销
"""

import sqlite3
import threading
import time
from queue import Queue, Empty
from contextlib import contextmanager
from typing import Optional

class ConnectionPool:
    """数据库连接池"""
    
    def __init__(self, db_path: str, max_connections: int = 10, timeout: float = 30.0):
        self.db_path = db_path
        self.max_connections = max_connections
        self.timeout = timeout
        self.pool = Queue(maxsize=max_connections)
        self.current_connections = 0
        self.lock = threading.Lock()
        
        # 预创建一些连接
        self._initialize_pool()
    
    def _initialize_pool(self):
        """初始化连接池"""
        initial_connections = min(3, self.max_connections)
        for _ in range(initial_connections):
            conn = self._create_connection()
            if conn:
                self.pool.put(conn)
    
    def _create_connection(self) -> Optional[sqlite3.Connection]:
        """创建新连接"""
        try:
            conn = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=self.timeout
            )
            # 优化SQLite设置
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=10000")
            conn.execute("PRAGMA temp_store=MEMORY")
            
            with self.lock:
                self.current_connections += 1
            
            return conn
        except Exception as e:
            print(f"创建数据库连接失败: {e}")
            return None
    
    @contextmanager
    def get_connection(self):
        """获取连接的上下文管理器"""
        conn = None
        try:
            # 尝试从池中获取连接
            try:
                conn = self.pool.get(timeout=5.0)
            except Empty:
                # 池中没有可用连接，创建新连接
                if self.current_connections < self.max_connections:
                    conn = self._create_connection()
                else:
                    # 等待连接可用
                    conn = self.pool.get(timeout=self.timeout)
            
            if conn is None:
                raise Exception("无法获取数据库连接")
            
            yield conn
            
        finally:
            if conn:
                # 将连接返回池中
                try:
                    self.pool.put_nowait(conn)
                except:
                    # 池已满，关闭连接
                    conn.close()
                    with self.lock:
                        self.current_connections -= 1
    
    def close_all(self):
        """关闭所有连接"""
        while not self.pool.empty():
            try:
                conn = self.pool.get_nowait()
                conn.close()
            except Empty:
                break
        
        with self.lock:
            self.current_connections = 0
    
    def get_stats(self):
        """获取连接池统计信息"""
        return {
            'current_connections': self.current_connections,
            'max_connections': self.max_connections,
            'available_connections': self.pool.qsize(),
            'pool_utilization': (self.current_connections / self.max_connections) * 100
        }

class OptimizedDatabaseManager:
    """优化的数据库管理器"""
    
    def __init__(self):
        self.pools = {}
        self.lock = threading.Lock()
    
    def get_pool(self, db_path: str) -> ConnectionPool:
        """获取指定数据库的连接池"""
        if db_path not in self.pools:
            with self.lock:
                if db_path not in self.pools:
                    self.pools[db_path] = ConnectionPool(db_path)
        return self.pools[db_path]
    
    def execute_query(self, db_path: str, query: str, params=None):
        """执行查询"""
        pool = self.get_pool(db_path)
        with pool.get_connection() as conn:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            if query.strip().upper().startswith('SELECT'):
                return cursor.fetchall()
            else:
                conn.commit()
                return cursor.rowcount
    
    def execute_many(self, db_path: str, query: str, params_list):
        """批量执行查询"""
        pool = self.get_pool(db_path)
        with pool.get_connection() as conn:
            cursor = conn.cursor()
            cursor.executemany(query, params_list)
            conn.commit()
            return cursor.rowcount
    
    def close_all_pools(self):
        """关闭所有连接池"""
        for pool in self.pools.values():
            pool.close_all()
        self.pools.clear()
    
    def get_all_stats(self):
        """获取所有连接池的统计信息"""
        stats = {}
        for db_path, pool in self.pools.items():
            stats[db_path] = pool.get_stats()
        return stats

# 全局连接池管理器
_db_manager = None
_manager_lock = threading.Lock()

def get_database_manager() -> OptimizedDatabaseManager:
    """获取全局数据库管理器实例"""
    global _db_manager
    if _db_manager is None:
        with _manager_lock:
            if _db_manager is None:
                _db_manager = OptimizedDatabaseManager()
    return _db_manager

# 便捷函数
def execute_query(db_path: str, query: str, params=None):
    """便捷的查询执行函数"""
    manager = get_database_manager()
    return manager.execute_query(db_path, query, params)

def execute_many(db_path: str, query: str, params_list):
    """便捷的批量执行函数"""
    manager = get_database_manager()
    return manager.execute_many(db_path, query, params_list)

def get_connection_stats():
    """获取连接池统计信息"""
    manager = get_database_manager()
    return manager.get_all_stats()
