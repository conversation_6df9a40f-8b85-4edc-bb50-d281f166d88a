#!/usr/bin/env python3
"""
最终系统验证
确保修复成功且无副作用
"""

import sys
import os
import requests
import time

def verify_api_service():
    """验证API服务状态"""
    print("=== 验证API服务状态 ===")
    
    try:
        response = requests.get("http://127.0.0.1:8888/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API服务正常运行")
            print(f"   状态: {data.get('status', 'N/A')}")
            print(f"   数据库记录: {data.get('database_records', 'N/A')}")
            print(f"   数据范围: {data.get('date_range', 'N/A')}")
            return True
        else:
            print(f"❌ API服务异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False

def verify_intelligent_fusion():
    """验证智能融合预测功能"""
    print("\n=== 验证智能融合预测功能 ===")
    
    try:
        response = requests.get(
            "http://127.0.0.1:8888/api/v1/prediction/intelligent-fusion/predict",
            params={
                "max_candidates": 15,
                "confidence_threshold": 0.6,
                "auto_train": True
            },
            timeout=90
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success', False):
                prediction = result.get('prediction', {})
                
                if 'error' not in prediction:
                    print("✅ 智能融合预测功能正常")
                    print(f"   推荐号码: {prediction.get('numbers', 'N/A')}")
                    print(f"   置信度: {prediction.get('confidence', 0):.1%}")
                    
                    # 验证候选数据结构（这是修复的关键点）
                    candidates = prediction.get('candidates', [])
                    print(f"   候选数量: {len(candidates)}")
                    
                    if candidates:
                        # 检查候选数据结构完整性
                        first_candidate = candidates[0]
                        required_fields = ['numbers', 'confidence']
                        optional_fields = ['fusion_score', 'strategy']
                        
                        missing_required = [f for f in required_fields if f not in first_candidate]
                        present_optional = [f for f in optional_fields if f in first_candidate]
                        
                        if not missing_required:
                            print(f"   ✅ 候选数据结构完整")
                            print(f"   必需字段: {required_fields}")
                            print(f"   可选字段: {present_optional}")
                            
                            # 验证数据类型
                            confidence_valid = isinstance(first_candidate.get('confidence'), (int, float))
                            fusion_score_valid = True
                            if 'fusion_score' in first_candidate:
                                fusion_score_valid = isinstance(first_candidate.get('fusion_score'), (int, float))
                            
                            if confidence_valid and fusion_score_valid:
                                print("   ✅ 候选数据类型正确")
                                return True
                            else:
                                print("   ❌ 候选数据类型错误")
                                return False
                        else:
                            print(f"   ❌ 缺少必需字段: {missing_required}")
                            return False
                    else:
                        print("   ⚠️ 没有候选数据（可能正常）")
                        return True
                else:
                    print(f"   ❌ 预测失败: {prediction['error']}")
                    return False
            else:
                print(f"   ❌ API调用失败: {result.get('error', '未知错误')}")
                return False
        else:
            print(f"   ❌ API请求失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 智能融合预测验证失败: {e}")
        return False

def verify_scientific_validation():
    """验证科学性测试"""
    print("\n=== 验证科学性测试 ===")
    
    try:
        # 运行科学性验证
        import subprocess
        result = subprocess.run(
            [sys.executable, "test_prediction_scientific.py"],
            capture_output=True,
            text=True,
            timeout=120
        )
        
        if result.returncode == 0:
            output = result.stdout
            if "科学性验证通过" in output:
                # 提取通过率
                lines = output.split('\n')
                for line in lines:
                    if "成功率:" in line:
                        success_rate = line.split("成功率:")[1].strip()
                        print(f"✅ 科学性验证通过，成功率: {success_rate}")
                        return True
                print("✅ 科学性验证通过")
                return True
            else:
                print("❌ 科学性验证失败")
                return False
        else:
            print(f"❌ 科学性验证执行失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 科学性验证异常: {e}")
        return False

def verify_ui_fix():
    """验证UI修复效果"""
    print("\n=== 验证UI修复效果 ===")
    
    try:
        # 运行UI修复测试
        import subprocess
        result = subprocess.run(
            [sys.executable, "test_ui_fusion_score_fix.py"],
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if result.returncode == 0:
            output = result.stdout
            if "所有测试通过" in output:
                # 提取测试结果
                lines = output.split('\n')
                for line in lines:
                    if "总计:" in line:
                        test_result = line.split("总计:")[1].strip()
                        print(f"✅ UI修复测试通过: {test_result}")
                        return True
                print("✅ UI修复测试通过")
                return True
            else:
                print("❌ UI修复测试失败")
                return False
        else:
            print(f"❌ UI修复测试执行失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ UI修复测试异常: {e}")
        return False

def main():
    """主验证函数"""
    print("🔍 开始最终系统验证...")
    print("="*60)
    
    verifications = []
    
    # 执行所有验证
    verifications.append(("API服务状态", verify_api_service()))
    verifications.append(("智能融合预测", verify_intelligent_fusion()))
    verifications.append(("科学性验证", verify_scientific_validation()))
    verifications.append(("UI修复效果", verify_ui_fix()))
    
    # 汇总结果
    print("\n" + "="*60)
    print("🎯 最终系统验证结果:")
    print("="*60)
    
    passed = 0
    total = len(verifications)
    
    for verification_name, result in verifications:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{verification_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 验证统计: {passed}/{total} 项通过")
    
    if passed == total:
        print("\n🎉 最终系统验证完全通过！")
        print("✅ 趋势分析预测错误修复成功")
        print("✅ 系统功能完全正常")
        print("✅ 无副作用影响")
        print("✅ 项目可以正常使用")
        return True
    else:
        print(f"\n⚠️ 系统验证部分失败 ({total-passed}项)")
        print("❌ 需要进一步检查和修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
