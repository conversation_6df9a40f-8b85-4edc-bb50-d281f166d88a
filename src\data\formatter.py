"""
福彩3D数据格式化器

负责数据格式转换和标准化输出
"""

import json
import csv
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime
import logging

from .models import LotteryRecord, DataQualityReport

logger = logging.getLogger(__name__)


class DataFormatter:
    """数据格式化器"""
    
    def __init__(self):
        """初始化格式化器"""
        self.supported_formats = ['json', 'csv', 'txt', 'excel']
        
    def to_json(self, records: List[LotteryRecord], indent: int = 2) -> str:
        """
        转换为JSON格式
        
        Args:
            records: 记录列表
            indent: JSON缩进
            
        Returns:
            JSON字符串
        """
        data = {
            "metadata": {
                "total_records": len(records),
                "export_time": datetime.now().isoformat(),
                "format_version": "1.0"
            },
            "records": [record.to_dict() for record in records]
        }
        
        return json.dumps(data, ensure_ascii=False, indent=indent)
    
    def to_csv(self, records: List[LotteryRecord]) -> str:
        """
        转换为CSV格式
        
        Args:
            records: 记录列表
            
        Returns:
            CSV字符串
        """
        import io
        
        output = io.StringIO()
        writer = csv.writer(output)
        
        # 写入表头（完整版）
        writer.writerow([
            'period', 'date', 'numbers', 'trial_numbers',
            'digit1', 'digit2', 'digit3', 'trial_digit1', 'trial_digit2', 'trial_digit3',
            'draw_machine', 'trial_machine', 'sales_amount',
            'direct_prize', 'group3_prize', 'group6_prize',
            'sum_value', 'trial_sum_value', 'span_value', 'trial_span_value',
            'unknown_field1', 'unknown_field2', 'unknown_field3'
        ])

        # 写入数据
        for record in records:
            nums = record.number_list
            trial_nums = record.trial_number_list
            writer.writerow([
                record.period,
                record.date.isoformat(),
                record.numbers,
                record.trial_numbers,
                nums[0], nums[1], nums[2],
                trial_nums[0], trial_nums[1], trial_nums[2],
                record.draw_machine,
                record.trial_machine,
                record.sales_amount,
                record.direct_prize,
                record.group3_prize,
                record.group6_prize,
                record.sum_value,
                record.trial_sum_value,
                record.span_value,
                record.trial_span_value,
                record.unknown_field1,
                record.unknown_field2,
                record.unknown_field3
            ])
        
        return output.getvalue()
    
    def to_txt(self, records: List[LotteryRecord], separator: str = '\t') -> str:
        """
        转换为文本格式
        
        Args:
            records: 记录列表
            separator: 字段分隔符
            
        Returns:
            文本字符串
        """
        lines = []
        
        # 添加表头
        lines.append(f"# 福彩3D历史数据")
        lines.append(f"# 导出时间: {datetime.now().isoformat()}")
        lines.append(f"# 记录总数: {len(records)}")
        lines.append(f"# 格式: 期号{separator}日期{separator}号码")
        lines.append("")
        
        # 添加数据
        for record in records:
            lines.append(f"{record.period}{separator}{record.date}{separator}{record.numbers}")
        
        return '\n'.join(lines)
    
    def to_excel_data(self, records: List[LotteryRecord]) -> List[Dict[str, Any]]:
        """
        转换为Excel数据格式
        
        Args:
            records: 记录列表
            
        Returns:
            Excel数据列表
        """
        excel_data = []
        
        for record in records:
            nums = record.number_list
            trial_nums = record.trial_number_list
            excel_data.append({
                '期号': record.period,
                '日期': record.date.isoformat(),
                '开奖号码': record.numbers,
                '试机号码': record.trial_numbers,
                '百位': nums[0],
                '十位': nums[1],
                '个位': nums[2],
                '试机百位': trial_nums[0],
                '试机十位': trial_nums[1],
                '试机个位': trial_nums[2],
                '开奖机器': record.draw_machine,
                '试机机器': record.trial_machine,
                '销售额': record.sales_amount,
                '直选奖金': record.direct_prize,
                '组三奖金': record.group3_prize,
                '组六奖金': record.group6_prize,
                '和值': record.sum_value,
                '试机和值': record.trial_sum_value,
                '跨度': record.span_value,
                '试机跨度': record.trial_span_value,
                '奇偶形态': self._get_odd_even_pattern(nums),
                '试机奇偶': self._get_odd_even_pattern(trial_nums),
                '大小形态': self._get_size_pattern(nums),
                '试机大小': self._get_size_pattern(trial_nums),
                '质合形态': self._get_prime_composite_pattern(nums),
                '试机质合': self._get_prime_composite_pattern(trial_nums)
            })
        
        return excel_data
    
    def _get_odd_even_pattern(self, numbers: List[int]) -> str:
        """获取奇偶形态"""
        pattern = []
        for num in numbers:
            pattern.append('奇' if num % 2 == 1 else '偶')
        return ''.join(pattern)
    
    def _get_size_pattern(self, numbers: List[int]) -> str:
        """获取大小形态"""
        pattern = []
        for num in numbers:
            pattern.append('大' if num >= 5 else '小')
        return ''.join(pattern)
    
    def _get_prime_composite_pattern(self, numbers: List[int]) -> str:
        """获取质合形态"""
        primes = {2, 3, 5, 7}
        pattern = []
        for num in numbers:
            if num in primes:
                pattern.append('质')
            elif num == 0 or num == 1:
                pattern.append('合')  # 0和1通常归为合数
            else:
                pattern.append('合')
        return ''.join(pattern)
    
    def save_to_file(
        self,
        records: List[LotteryRecord],
        filepath: Path,
        format_type: str = 'auto'
    ) -> bool:
        """
        保存数据到文件
        
        Args:
            records: 记录列表
            filepath: 文件路径
            format_type: 格式类型，'auto'表示根据文件扩展名自动判断
            
        Returns:
            保存是否成功
        """
        try:
            # 自动检测格式
            if format_type == 'auto':
                suffix = filepath.suffix.lower()
                if suffix == '.json':
                    format_type = 'json'
                elif suffix == '.csv':
                    format_type = 'csv'
                elif suffix in ['.txt', '.dat']:
                    format_type = 'txt'
                elif suffix in ['.xlsx', '.xls']:
                    format_type = 'excel'
                else:
                    format_type = 'txt'  # 默认格式
            
            # 确保目录存在
            filepath.parent.mkdir(parents=True, exist_ok=True)
            
            # 根据格式保存
            if format_type == 'json':
                content = self.to_json(records)
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(content)
                    
            elif format_type == 'csv':
                content = self.to_csv(records)
                with open(filepath, 'w', encoding='utf-8', newline='') as f:
                    f.write(content)
                    
            elif format_type == 'txt':
                content = self.to_txt(records)
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(content)
                    
            elif format_type == 'excel':
                try:
                    import pandas as pd
                    excel_data = self.to_excel_data(records)
                    df = pd.DataFrame(excel_data)
                    df.to_excel(filepath, index=False, engine='openpyxl')
                except ImportError:
                    logger.warning("pandas未安装，无法导出Excel格式，改用CSV格式")
                    csv_path = filepath.with_suffix('.csv')
                    content = self.to_csv(records)
                    with open(csv_path, 'w', encoding='utf-8', newline='') as f:
                        f.write(content)
                    filepath = csv_path
            
            logger.info(f"数据已保存到: {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"保存文件失败: {e}")
            return False
    
    def create_summary_report(
        self,
        records: List[LotteryRecord],
        quality_report: DataQualityReport
    ) -> Dict[str, Any]:
        """
        创建数据摘要报告
        
        Args:
            records: 记录列表
            quality_report: 质量报告
            
        Returns:
            摘要报告字典
        """
        if not records:
            return {"error": "没有数据记录"}
        
        # 基本统计
        dates = [r.date for r in records]
        sum_values = [r.sum_value for r in records]
        span_values = [r.span_value for r in records]
        
        # 数字频率统计
        all_digits = []
        for record in records:
            all_digits.extend(record.number_list)
        
        digit_freq = {}
        for digit in range(10):
            digit_freq[str(digit)] = all_digits.count(digit)
        
        # 形态统计
        odd_even_patterns = {}
        size_patterns = {}
        
        for record in records:
            nums = record.number_list
            
            # 奇偶形态
            oe_pattern = self._get_odd_even_pattern(nums)
            odd_even_patterns[oe_pattern] = odd_even_patterns.get(oe_pattern, 0) + 1
            
            # 大小形态
            size_pattern = self._get_size_pattern(nums)
            size_patterns[size_pattern] = size_patterns.get(size_pattern, 0) + 1
        
        return {
            "basic_info": {
                "total_records": len(records),
                "date_range": {
                    "start": min(dates).isoformat(),
                    "end": max(dates).isoformat(),
                    "span_days": (max(dates) - min(dates)).days
                },
                "period_range": {
                    "start": records[0].period,
                    "end": records[-1].period
                }
            },
            "statistics": {
                "sum_value": {
                    "min": min(sum_values),
                    "max": max(sum_values),
                    "avg": round(sum(sum_values) / len(sum_values), 2)
                },
                "span_value": {
                    "min": min(span_values),
                    "max": max(span_values),
                    "avg": round(sum(span_values) / len(span_values), 2)
                },
                "digit_frequency": digit_freq,
                "most_frequent_digit": max(digit_freq, key=digit_freq.get),
                "least_frequent_digit": min(digit_freq, key=digit_freq.get)
            },
            "patterns": {
                "odd_even_top5": sorted(odd_even_patterns.items(), key=lambda x: x[1], reverse=True)[:5],
                "size_top5": sorted(size_patterns.items(), key=lambda x: x[1], reverse=True)[:5]
            },
            "quality": quality_report.to_dict(),
            "export_info": {
                "export_time": datetime.now().isoformat(),
                "format_version": "1.0"
            }
        }


# 便捷函数
def export_lottery_data(
    records: List[LotteryRecord],
    output_dir: str = "data/processed",
    formats: List[str] = None
) -> Dict[str, bool]:
    """
    便捷的数据导出函数
    
    Args:
        records: 记录列表
        output_dir: 输出目录
        formats: 导出格式列表，默认为['json', 'csv', 'txt']
        
    Returns:
        各格式导出结果字典
    """
    if formats is None:
        formats = ['json', 'csv', 'txt']
    
    formatter = DataFormatter()
    output_path = Path(output_dir)
    results = {}
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    for fmt in formats:
        filename = f"lottery_data_{timestamp}.{fmt}"
        filepath = output_path / filename
        
        success = formatter.save_to_file(records, filepath, fmt)
        results[fmt] = success
    
    return results


if __name__ == "__main__":
    # 测试代码
    from datetime import date
    
    # 创建测试数据
    test_records = [
        LotteryRecord("2024001", date(2024, 1, 1), "123"),
        LotteryRecord("2024002", date(2024, 1, 2), "456"),
        LotteryRecord("2024003", date(2024, 1, 3), "789"),
    ]
    
    print("测试数据格式化器...")
    formatter = DataFormatter()
    
    # 测试JSON格式
    json_data = formatter.to_json(test_records)
    print(f"\nJSON格式 (前200字符):")
    print(json_data[:200] + "...")
    
    # 测试CSV格式
    csv_data = formatter.to_csv(test_records)
    print(f"\nCSV格式:")
    print(csv_data)
    
    # 测试TXT格式
    txt_data = formatter.to_txt(test_records)
    print(f"\nTXT格式:")
    print(txt_data)
    
    # 测试Excel数据
    excel_data = formatter.to_excel_data(test_records)
    print(f"\nExcel数据:")
    for record in excel_data:
        print(f"  {record}")
    
    print("\n格式化器测试完成！")
