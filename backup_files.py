#!/usr/bin/env python3
"""
备份关键文件脚本
"""

import os
import shutil
from datetime import datetime
from pathlib import Path

def backup_files():
    """备份关键文件"""
    # 创建备份目录
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    backup_dir = Path(f"backup/prediction_model_sync_fix_{timestamp}")
    backup_dir.mkdir(parents=True, exist_ok=True)
    
    # 需要备份的文件列表
    files_to_backup = [
        "src/prediction/intelligent_fusion.py",
        "src/services/data_update_service.py", 
        "src/api/production_main.py",
        "src/ui/intelligent_fusion_page.py"
    ]
    
    print(f"📁 创建备份目录: {backup_dir}")
    
    # 备份文件
    for file_path in files_to_backup:
        source = Path(file_path)
        if source.exists():
            # 保持目录结构
            dest = backup_dir / file_path
            dest.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(source, dest)
            print(f"✅ 备份完成: {file_path} -> {dest}")
        else:
            print(f"⚠️  文件不存在: {file_path}")
    
    print(f"\n🎉 备份完成！备份目录: {backup_dir}")
    return str(backup_dir)

if __name__ == "__main__":
    backup_files()
