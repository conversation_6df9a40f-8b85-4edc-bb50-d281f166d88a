#!/usr/bin/env python3
"""
❌ 此文件已被禁用 ❌

此API文件已被移动到 deprecated/ 目录，不应再被使用。

🚫 错误原因：
- 容易与正确的API服务混淆
- 可能导致功能不完整或错误

✅ 正确的API服务：
请使用：src/api/production_main.py
启动脚本：start_production_api.py

📋 正确启动方式：
cd D:\github\3dyuce
python start_production_api.py

🔍 如需查看原文件内容：
请查看：src/api/deprecated/main.py.deprecated

⚠️ 重要提醒：
不要直接运行此文件或任何src/api/目录下的文件
请始终使用项目根目录的启动脚本

禁用日期：2025-07-22
"""

# 防止意外导入
raise ImportError(
    "❌ 此API文件已被禁用！\n"
    "✅ 请使用：python start_production_api.py\n"
    "📖 详情请查看：正确启动方式.md"
)
