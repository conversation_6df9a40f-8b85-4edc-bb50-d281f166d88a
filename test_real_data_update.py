#!/usr/bin/env python3
"""
测试真实数据更新功能
"""

import os
import sys

sys.path.append('src')

import sqlite3

import requests

from src.ui.data_update_components import DataUpdateManager


def get_current_record_count():
    """获取当前记录数"""
    with sqlite3.connect('data/lottery.db') as conn:
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM lottery_records')
        return cursor.fetchone()[0]

def get_latest_records(limit=5):
    """获取最新记录"""
    with sqlite3.connect('data/lottery.db') as conn:
        cursor = conn.cursor()
        cursor.execute('''
            SELECT period, date, numbers, sum_value, span_value 
            FROM lottery_records 
            ORDER BY period DESC 
            LIMIT ?
        ''', (limit,))
        return cursor.fetchall()

def test_real_data_fetch():
    """测试真实数据获取"""
    print("测试真实数据获取...")
    manager = DataUpdateManager()
    
    try:
        # 获取当前记录数
        initial_count = get_current_record_count()
        print(f"当前数据库记录数: {initial_count}")
        
        # 获取最新记录
        latest_records = get_latest_records(3)
        print("最新3条记录:")
        for record in latest_records:
            print(f"  期号: {record[0]}, 日期: {record[1]}, 号码: {record[2]}, 和值: {record[3]}, 跨度: {record[4]}")
        
        # 尝试获取数据源
        print("\n尝试获取数据源...")
        result = manager.fetch_latest_data()
        
        if result['status'] == 'success':
            print(f"✅ 数据获取成功: {result['message']}")
            print(f"   获取记录数: {result.get('new_records', 0)}")
            
            # 如果有新数据，尝试更新
            if result.get('data') and len(result['data']) > initial_count:
                print(f"\n发现新数据，尝试增量更新...")
                
                # 只取最新的几条记录进行测试
                new_data = result['data'][-5:]  # 最新5条
                update_result = manager.update_database(new_data, 'incremental')
                
                if update_result['status'] == 'success':
                    print(f"✅ 数据更新成功: {update_result['message']}")
                    
                    # 验证更新结果
                    final_count = get_current_record_count()
                    print(f"更新后记录数: {final_count}")
                    
                    if final_count > initial_count:
                        print(f"✅ 记录数成功增加: {initial_count} -> {final_count}")
                        
                        # 显示最新记录
                        latest_records = get_latest_records(3)
                        print("更新后最新3条记录:")
                        for record in latest_records:
                            print(f"  期号: {record[0]}, 日期: {record[1]}, 号码: {record[2]}, 和值: {record[3]}, 跨度: {record[4]}")
                        
                        return True
                    else:
                        print("⚠️ 记录数未增加，可能是重复数据")
                        return True
                else:
                    print(f"❌ 数据更新失败: {update_result['message']}")
                    if 'technical_details' in update_result:
                        print(f"   技术详情: {update_result['technical_details']}")
                    return False
            else:
                print("ℹ️ 没有发现新数据")
                return True
        else:
            print(f"❌ 数据获取失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主函数"""
    print("开始测试真实数据更新功能...")
    print("=" * 50)
    
    success = test_real_data_fetch()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 真实数据更新测试成功！")
        print("✅ 数据不一致问题已解决")
        print("✅ 数据更新功能正常工作")
    else:
        print("⚠️ 真实数据更新测试失败")
        print("需要进一步检查网络连接或数据源状态")

if __name__ == "__main__":
    main()
