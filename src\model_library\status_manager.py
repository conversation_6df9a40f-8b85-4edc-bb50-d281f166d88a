"""
模型状态管理器

负责实时监控和管理模型状态
"""

import sqlite3
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional

from .base_model import BaseModel, ModelStatus, ModelStatusInfo
from .database_manager import DatabaseManager
from .exceptions import ModelStatusError


class ModelStatusManager:
    """模型状态管理器"""
    
    def __init__(self, db_path: str = "data/model_library.db"):
        self.db_path = Path(db_path)
        self.db_manager = DatabaseManager(db_path)
        self._status_cache: Dict[str, ModelStatusInfo] = {}
        self._cache_lock = threading.RLock()
        self._cache_ttl = 60  # 缓存TTL为60秒
        self._last_cache_update: Dict[str, datetime] = {}
        
    def check_data_ready(self, model: BaseModel) -> bool:
        """检查数据是否就绪
        
        Args:
            model: 模型实例
            
        Returns:
            bool: 数据是否就绪
        """
        try:
            # 检查数据库中是否有足够的历史数据
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT COUNT(*) FROM lottery_records')
                total_records = cursor.fetchone()[0]
                
                # 获取模型所需的最小数据量
                required_size = model.get_required_data_size()
                
                return total_records >= required_size
                
        except Exception:
            return False
    
    def check_features_ready(self, model: BaseModel) -> bool:
        """检查特征是否就绪

        Args:
            model: 模型实例

        Returns:
            bool: 特征是否就绪
        """
        try:
            # 检查模型的特征工程配置
            model_info = model.get_info()
            feature_config = model_info.feature_engineering

            # 如果没有特征工程配置，认为特征就绪
            if not feature_config:
                return True

            # 针对trend_analyzer模型的特定检查
            if model.model_id == "trend_analyzer":
                return self._check_trend_analyzer_features_ready(model)

            # 检查必要的特征是否可以计算
            # 这里可以根据具体的特征工程需求进行检查
            return True

        except Exception:
            return False

    def _check_trend_analyzer_features_ready(self, model) -> bool:
        """检查趋势分析器的特征是否就绪"""
        try:
            # 如果模型有自己的特征检查方法，优先使用
            if hasattr(model, '_check_features_ready'):
                # 获取数据进行检查
                from src.model_library.utils.data_utils import \
                    LotteryDataLoader
                data_loader = LotteryDataLoader()
                try:
                    records = data_loader.load_all_records()
                    return model._check_features_ready(records)
                except Exception:
                    return False

            # 基本检查：趋势分析器是否初始化
            if hasattr(model, 'trend_analyzer'):
                return model.trend_analyzer is not None

            return False

        except Exception:
            return False
    
    def check_model_trained(self, model_id: str) -> bool:
        """检查模型是否已训练
        
        Args:
            model_id: 模型ID
            
        Returns:
            bool: 模型是否已训练
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    'SELECT trained FROM model_states WHERE model_id = ?',
                    (model_id,)
                )
                result = cursor.fetchone()
                return bool(result[0]) if result else False
                
        except Exception:
            return False
    
    def check_model_updated(self, model_id: str) -> bool:
        """检查模型是否使用最新数据
        
        Args:
            model_id: 模型ID
            
        Returns:
            bool: 模型是否使用最新数据
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取最新的开奖数据时间
                cursor.execute(
                    'SELECT MAX(date) FROM lottery_records'
                )
                latest_data_date = cursor.fetchone()[0]
                
                if not latest_data_date:
                    return False
                
                # 获取模型最后训练时间
                cursor.execute(
                    'SELECT last_training_time FROM model_states WHERE model_id = ?',
                    (model_id,)
                )
                result = cursor.fetchone()
                
                if not result or not result[0]:
                    return False
                
                last_training_time = datetime.fromisoformat(result[0])
                latest_data_datetime = datetime.strptime(latest_data_date, '%Y-%m-%d')
                
                # 如果最后训练时间在最新数据之后，认为是最新的
                return last_training_time.date() >= latest_data_datetime.date()
                
        except Exception:
            return False
    
    def get_training_data_size(self, model: BaseModel) -> int:
        """获取训练数据大小
        
        Args:
            model: 模型实例
            
        Returns:
            int: 训练数据大小
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT COUNT(*) FROM lottery_records')
                return cursor.fetchone()[0]
                
        except Exception:
            return 0
    
    def update_model_status(self, model: BaseModel) -> ModelStatusInfo:
        """更新模型状态
        
        Args:
            model: 模型实例
            
        Returns:
            ModelStatusInfo: 更新后的状态信息
        """
        try:
            model_id = model.model_id
            
            # 检查各项状态
            data_ready = self.check_data_ready(model)
            features_ready = self.check_features_ready(model)
            trained = self.check_model_trained(model_id)
            up_to_date = self.check_model_updated(model_id)
            training_data_size = self.get_training_data_size(model)
            
            # 确定整体状态
            if not data_ready:
                status = ModelStatus.NOT_READY
                error_message = "训练数据不足"
            elif not features_ready:
                status = ModelStatus.NOT_READY
                error_message = "特征工程未就绪"
            elif not trained:
                status = ModelStatus.READY
                error_message = None
            elif not up_to_date:
                status = ModelStatus.READY
                error_message = "需要使用最新数据重新训练"
            else:
                status = ModelStatus.TRAINED
                error_message = None
            
            # 创建状态信息
            status_info = ModelStatusInfo(
                model_id=model_id,
                status=status,
                data_ready=data_ready,
                features_ready=features_ready,
                trained=trained,
                up_to_date=up_to_date,
                training_data_size=training_data_size,
                last_check_time=datetime.now(),
                error_message=error_message
            )
            
            # 保存到数据库
            self._save_status_to_db(status_info)
            
            # 更新缓存
            with self._cache_lock:
                self._status_cache[model_id] = status_info
                self._last_cache_update[model_id] = datetime.now()
            
            return status_info
            
        except Exception as e:
            # 创建错误状态
            error_status = ModelStatusInfo(
                model_id=model.model_id,
                status=ModelStatus.ERROR,
                error_message=str(e),
                last_check_time=datetime.now()
            )
            
            # 保存错误状态
            self._save_status_to_db(error_status)
            
            return error_status
    
    def get_model_status(self, model_id: str, use_cache: bool = True) -> Optional[ModelStatusInfo]:
        """获取模型状态
        
        Args:
            model_id: 模型ID
            use_cache: 是否使用缓存
            
        Returns:
            ModelStatusInfo: 状态信息，如果不存在返回None
        """
        # 检查缓存
        if use_cache:
            with self._cache_lock:
                if model_id in self._status_cache:
                    last_update = self._last_cache_update.get(model_id)
                    if last_update and (datetime.now() - last_update).seconds < self._cache_ttl:
                        return self._status_cache[model_id]
        
        # 从数据库加载
        return self._load_status_from_db(model_id)
    
    def update_status_cache(self, model_id: str, status_info: ModelStatusInfo):
        """更新状态缓存
        
        Args:
            model_id: 模型ID
            status_info: 状态信息
        """
        with self._cache_lock:
            self._status_cache[model_id] = status_info
            self._last_cache_update[model_id] = datetime.now()
    
    def clear_status_cache(self, model_id: Optional[str] = None):
        """清除状态缓存
        
        Args:
            model_id: 模型ID，如果为None则清除所有缓存
        """
        with self._cache_lock:
            if model_id:
                self._status_cache.pop(model_id, None)
                self._last_cache_update.pop(model_id, None)
            else:
                self._status_cache.clear()
                self._last_cache_update.clear()
    
    def batch_update_status(self, models: List[BaseModel]) -> Dict[str, ModelStatusInfo]:
        """批量更新模型状态
        
        Args:
            models: 模型列表
            
        Returns:
            Dict[str, ModelStatusInfo]: 模型ID到状态信息的映射
        """
        results = {}
        
        for model in models:
            try:
                status_info = self.update_model_status(model)
                results[model.model_id] = status_info
            except Exception as e:
                # 记录错误但继续处理其他模型
                error_status = ModelStatusInfo(
                    model_id=model.model_id,
                    status=ModelStatus.ERROR,
                    error_message=str(e),
                    last_check_time=datetime.now()
                )
                results[model.model_id] = error_status
        
        return results
    
    def get_models_by_status(self, status: ModelStatus) -> List[str]:
        """根据状态获取模型ID列表
        
        Args:
            status: 模型状态
            
        Returns:
            List[str]: 模型ID列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    'SELECT model_id FROM model_states WHERE status = ?',
                    (status.value,)
                )
                return [row[0] for row in cursor.fetchall()]
                
        except Exception:
            return []
    
    def _save_status_to_db(self, status_info: ModelStatusInfo):
        """保存状态到数据库"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO model_states
                (model_id, status, data_ready, features_ready, trained,
                 up_to_date, training_data_size, last_training_time,
                 last_check_time, error_message)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                status_info.model_id,
                status_info.status.value,
                status_info.data_ready,
                status_info.features_ready,
                status_info.trained,
                status_info.up_to_date,
                status_info.training_data_size,
                status_info.last_training_time.isoformat() if status_info.last_training_time else None,
                status_info.last_check_time.isoformat(),
                status_info.error_message
            ))
            conn.commit()
    
    def _load_status_from_db(self, model_id: str) -> Optional[ModelStatusInfo]:
        """从数据库加载状态"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    'SELECT * FROM model_states WHERE model_id = ?',
                    (model_id,)
                )
                row = cursor.fetchone()
                
                if row:
                    status_info = ModelStatusInfo(
                        model_id=row[0],
                        status=ModelStatus(row[1]),
                        data_ready=bool(row[2]),
                        features_ready=bool(row[3]),
                        trained=bool(row[4]),
                        up_to_date=bool(row[5]),
                        training_data_size=row[6],
                        last_training_time=datetime.fromisoformat(row[7]) if row[7] else None,
                        last_check_time=datetime.fromisoformat(row[8]),
                        error_message=row[9]
                    )
                    
                    # 更新缓存
                    self.update_status_cache(model_id, status_info)
                    
                    return status_info
                    
        except Exception:
            pass
        
        return None
