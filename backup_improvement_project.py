#!/usr/bin/env python3
"""
福彩3D预测系统改进项目备份脚本
创建完整的项目备份，确保改进过程的安全性
"""

import os
import shutil
import sqlite3
import json
from datetime import datetime
from pathlib import Path

class ProjectBackupManager:
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.backup_dir = f"backup/improvement_project_{self.timestamp}"
        self.backup_info = {
            "timestamp": self.timestamp,
            "backup_type": "improvement_project",
            "files_backed_up": [],
            "databases_backed_up": [],
            "status": "in_progress"
        }
        
    def create_backup_directory(self):
        """创建备份目录结构"""
        os.makedirs(self.backup_dir, exist_ok=True)
        os.makedirs(f"{self.backup_dir}/data", exist_ok=True)
        os.makedirs(f"{self.backup_dir}/src", exist_ok=True)
        os.makedirs(f"{self.backup_dir}/config", exist_ok=True)
        print(f"✅ 创建备份目录: {self.backup_dir}")
        
    def backup_databases(self):
        """备份所有数据库文件"""
        db_files = [
            "data/lottery_data.db",
            "data/bug_detection.db", 
            "data/model_library.db",
            "data/scheduler.db",
            "data/unified_predictions.db"
        ]
        
        for db_file in db_files:
            if os.path.exists(db_file):
                backup_path = f"{self.backup_dir}/{db_file}"
                os.makedirs(os.path.dirname(backup_path), exist_ok=True)
                shutil.copy2(db_file, backup_path)
                self.backup_info["databases_backed_up"].append(db_file)
                print(f"✅ 备份数据库: {db_file}")
                
    def backup_critical_files(self):
        """备份关键文件"""
        critical_files = [
            "src/ui/main.py",
            "src/core/database.py",
            "src/data/collector.py",
            "src/api/production_main.py",
            "src/bug_detection/realtime/websocket_manager.py"
        ]
        
        for file_path in critical_files:
            if os.path.exists(file_path):
                backup_path = f"{self.backup_dir}/{file_path}"
                os.makedirs(os.path.dirname(backup_path), exist_ok=True)
                shutil.copy2(file_path, backup_path)
                self.backup_info["files_backed_up"].append(file_path)
                print(f"✅ 备份关键文件: {file_path}")
                
    def backup_pages_disabled(self):
        """备份pages_disabled目录"""
        pages_disabled_dir = "src/ui/pages_disabled"
        if os.path.exists(pages_disabled_dir):
            backup_path = f"{self.backup_dir}/{pages_disabled_dir}"
            shutil.copytree(pages_disabled_dir, backup_path)
            self.backup_info["files_backed_up"].append(pages_disabled_dir)
            print(f"✅ 备份禁用页面目录: {pages_disabled_dir}")
            
    def create_rollback_script(self):
        """创建回滚脚本"""
        rollback_script = f"""#!/usr/bin/env python3
'''
福彩3D预测系统改进项目回滚脚本
自动生成于: {self.timestamp}
'''

import os
import shutil
import json
from pathlib import Path

def rollback_project():
    backup_dir = "{self.backup_dir}"
    
    print("🔄 开始回滚项目改进...")
    
    # 回滚数据库
    databases = {json.dumps(self.backup_info["databases_backed_up"], indent=8)}
    for db in databases:
        if os.path.exists(f"{{backup_dir}}/{{db}}"):
            shutil.copy2(f"{{backup_dir}}/{{db}}", db)
            print(f"✅ 回滚数据库: {{db}}")
    
    # 回滚关键文件
    files = {json.dumps(self.backup_info["files_backed_up"], indent=8)}
    for file_path in files:
        if os.path.exists(f"{{backup_dir}}/{{file_path}}"):
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            shutil.copy2(f"{{backup_dir}}/{{file_path}}", file_path)
            print(f"✅ 回滚文件: {{file_path}}")
    
    # 恢复pages_disabled目录
    pages_disabled_backup = f"{{backup_dir}}/src/ui/pages_disabled"
    if os.path.exists(pages_disabled_backup):
        if os.path.exists("src/ui/pages_disabled"):
            shutil.rmtree("src/ui/pages_disabled")
        shutil.copytree(pages_disabled_backup, "src/ui/pages_disabled")
        print("✅ 恢复pages_disabled目录")
    
    print("🎉 项目回滚完成!")

if __name__ == "__main__":
    rollback_project()
"""
        
        rollback_file = f"rollback_improvement_{self.timestamp}.py"
        with open(rollback_file, 'w', encoding='utf-8') as f:
            f.write(rollback_script)
        print(f"✅ 创建回滚脚本: {rollback_file}")
        
    def save_backup_info(self):
        """保存备份信息"""
        self.backup_info["status"] = "completed"
        info_file = f"{self.backup_dir}/backup_info.json"
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(self.backup_info, f, indent=2, ensure_ascii=False)
        print(f"✅ 保存备份信息: {info_file}")
        
    def run_backup(self):
        """执行完整备份"""
        print("🚀 开始项目改进备份...")
        self.create_backup_directory()
        self.backup_databases()
        self.backup_critical_files()
        self.backup_pages_disabled()
        self.create_rollback_script()
        self.save_backup_info()
        print(f"🎉 备份完成! 备份目录: {self.backup_dir}")
        return self.backup_dir

if __name__ == "__main__":
    backup_manager = ProjectBackupManager()
    backup_manager.run_backup()
