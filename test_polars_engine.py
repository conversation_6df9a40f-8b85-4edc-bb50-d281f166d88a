#!/usr/bin/env python3
"""
测试Polars数据处理引擎
"""

import sys
sys.path.append('src')

from core.polars_engine import PolarsEngine
from data.parser import DataParser
import json
import time

def test_polars_engine():
    """测试Polars引擎的各项功能"""
    print("🚀 开始测试Polars数据处理引擎...")
    
    # 1. 初始化引擎
    print("\n1. 初始化Polars引擎...")
    engine = PolarsEngine()
    print("✅ Polars引擎初始化成功")
    
    # 2. 加载数据
    print("\n2. 加载历史数据...")
    try:
        # 从现有数据文件加载
        with open('data/raw/3d_data_20250714_144231.txt', 'r', encoding='utf-8') as f:
            raw_data = f.read()
        
        parser = DataParser()
        records, quality_report = parser.parse_data(raw_data)
        
        print(f"✅ 数据解析成功: {len(records)} 条记录")
        
        # 加载到Polars引擎
        start_time = time.time()
        engine.load_from_records(records)
        load_time = time.time() - start_time
        
        print(f"✅ 数据加载到Polars成功，耗时: {load_time:.3f}秒")
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return False
    
    # 3. 基础统计测试
    print("\n3. 测试基础统计功能...")
    try:
        start_time = time.time()
        basic_stats = engine.get_basic_stats()
        stats_time = time.time() - start_time
        
        print(f"✅ 基础统计计算成功，耗时: {stats_time:.3f}秒")
        print(f"   总记录数: {basic_stats['total_records']}")
        print(f"   日期范围: {basic_stats['date_range']['start']} 到 {basic_stats['date_range']['end']}")
        print(f"   和值统计: 最小{basic_stats['sum_value_stats']['min']}, 最大{basic_stats['sum_value_stats']['max']}, 平均{basic_stats['sum_value_stats']['mean']}")
        print(f"   销售额统计: 总计{basic_stats['sales_amount_stats']['total']:,}元, 平均{basic_stats['sales_amount_stats']['mean']:,.0f}元")
        
    except Exception as e:
        print(f"❌ 基础统计测试失败: {e}")
        return False
    
    # 4. 频率分析测试
    print("\n4. 测试频率分析功能...")
    try:
        start_time = time.time()
        freq_analysis = engine.get_frequency_analysis("all")
        freq_time = time.time() - start_time
        
        print(f"✅ 频率分析计算成功，耗时: {freq_time:.3f}秒")
        
        # 显示百位数字频率前5名
        if "hundreds" in freq_analysis:
            print("   百位数字频率前5名:")
            for i, item in enumerate(freq_analysis["hundreds"][:5]):
                print(f"     {i+1}. 数字{item['hundreds']}: {item['count']}次")
        
    except Exception as e:
        print(f"❌ 频率分析测试失败: {e}")
        return False
    
    # 5. 和值分布测试
    print("\n5. 测试和值分布分析...")
    try:
        start_time = time.time()
        sum_analysis = engine.get_sum_value_distribution()
        sum_time = time.time() - start_time
        
        print(f"✅ 和值分布分析成功，耗时: {sum_time:.3f}秒")
        print(f"   和值与试机号和值相关性: {sum_analysis['correlation']:.4f}")
        
        # 显示最常见的和值
        top_sums = sorted(sum_analysis['sum_value_distribution'], key=lambda x: x['count'], reverse=True)[:5]
        print("   最常见和值前5名:")
        for i, item in enumerate(top_sums):
            print(f"     {i+1}. 和值{item['sum_value']}: {item['count']}次")
        
    except Exception as e:
        print(f"❌ 和值分布测试失败: {e}")
        return False
    
    # 6. 机器号分析测试
    print("\n6. 测试机器号分析...")
    try:
        start_time = time.time()
        machine_analysis = engine.get_machine_analysis()
        machine_time = time.time() - start_time
        
        print(f"✅ 机器号分析成功，耗时: {machine_time:.3f}秒")
        
        if machine_analysis['draw_machine_stats']:
            print("   开奖机器使用统计:")
            for item in machine_analysis['draw_machine_stats']:
                print(f"     机器{item['draw_machine']}: {item['count']}次, 平均和值{item['avg_sum']:.2f}")
        
    except Exception as e:
        print(f"❌ 机器号分析测试失败: {e}")
        return False
    
    # 7. 销售额分析测试
    print("\n7. 测试销售额分析...")
    try:
        start_time = time.time()
        sales_analysis = engine.get_sales_analysis()
        sales_time = time.time() - start_time
        
        print(f"✅ 销售额分析成功，耗时: {sales_time:.3f}秒")
        print(f"   总销售额: {sales_analysis['total_sales']:,}元")
        print(f"   平均每期销售额: {sales_analysis['avg_daily_sales']:,.0f}元")
        print(f"   销售额与和值相关性: {sales_analysis['sales_sum_correlation']:.4f}")
        
        # 显示年度销售额
        if sales_analysis['yearly_sales']:
            print("   年度销售额统计:")
            for item in sales_analysis['yearly_sales'][-3:]:  # 显示最近3年
                print(f"     {item['year']}年: {item['total_sales']:,}元 ({item['draw_count']}期)")
        
    except Exception as e:
        print(f"❌ 销售额分析测试失败: {e}")
        return False
    
    # 8. 查询功能测试
    print("\n8. 测试查询功能...")
    try:
        # 按日期范围查询
        start_time = time.time()
        recent_data = engine.query_by_date_range("2025-01-01", "2025-07-13")
        query_time = time.time() - start_time
        
        print(f"✅ 日期范围查询成功，耗时: {query_time:.3f}秒")
        print(f"   2025年数据: {len(recent_data)}条记录")
        
        # 按和值范围查询
        sum_range_data = engine.query_by_sum_range(10, 15)
        print(f"   和值10-15范围: {len(sum_range_data)}条记录")
        
    except Exception as e:
        print(f"❌ 查询功能测试失败: {e}")
        return False
    
    # 9. 趋势分析测试
    print("\n9. 测试趋势分析...")
    try:
        start_time = time.time()
        trends = engine.get_recent_trends(30)
        trend_time = time.time() - start_time
        
        print(f"✅ 趋势分析成功，耗时: {trend_time:.3f}秒")
        if trends:
            print(f"   {trends['period']}:")
            print(f"     平均和值: {trends['sum_value_trend']['mean']}")
            print(f"     平均跨度: {trends['span_value_trend']['mean']}")
            print(f"     平均销售额: {trends['sales_trend']['mean']:,.0f}元")
        
    except Exception as e:
        print(f"❌ 趋势分析测试失败: {e}")
        return False
    
    # 10. 性能测试
    print("\n10. 性能测试...")
    try:
        # 测试大量查询的性能
        start_time = time.time()
        for i in range(100):
            engine.get_basic_stats()
        batch_time = time.time() - start_time
        
        print(f"✅ 性能测试完成")
        print(f"   100次基础统计查询耗时: {batch_time:.3f}秒")
        print(f"   平均每次查询: {batch_time/100*1000:.2f}毫秒")
        
        # 性能评估
        if batch_time < 1.0:
            print("   🚀 性能优秀：平均查询时间 < 10ms")
        elif batch_time < 5.0:
            print("   ✅ 性能良好：平均查询时间 < 50ms")
        else:
            print("   ⚠️ 性能需要优化")
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False
    
    # 11. 导出测试
    print("\n11. 测试数据导出...")
    try:
        start_time = time.time()
        export_results = engine.export_analysis_results('data/processed')
        export_time = time.time() - start_time
        
        print(f"✅ 数据导出成功，耗时: {export_time:.3f}秒")
        print("   导出文件:")
        for key, path in export_results.items():
            print(f"     {key}: {path}")
        
    except Exception as e:
        print(f"❌ 数据导出测试失败: {e}")
        return False
    
    # 测试总结
    print(f"\n🎉 Polars数据处理引擎测试完成！")
    print(f"📊 测试摘要:")
    print(f"   - 数据加载: ✅")
    print(f"   - 基础统计: ✅")
    print(f"   - 频率分析: ✅")
    print(f"   - 和值分布: ✅")
    print(f"   - 机器号分析: ✅")
    print(f"   - 销售额分析: ✅")
    print(f"   - 查询功能: ✅")
    print(f"   - 趋势分析: ✅")
    print(f"   - 性能测试: ✅")
    print(f"   - 数据导出: ✅")
    
    return True

if __name__ == "__main__":
    success = test_polars_engine()
    if success:
        print("\n✅ 所有测试通过，Polars引擎准备就绪！")
    else:
        print("\n❌ 测试失败，请检查错误信息")
        sys.exit(1)
