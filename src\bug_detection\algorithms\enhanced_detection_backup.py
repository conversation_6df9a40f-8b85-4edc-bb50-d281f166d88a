#!/usr/bin/env python3
"""
增强的Bug检测算法
实现更智能的错误检测、分类和严重程度评估
"""

import json
import logging
import re
from collections import Counter, defaultdict
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple

import numpy as np

logger = logging.getLogger(__name__)

class EnhancedBugDetector:
    """增强的Bug检测器"""
    
    def __init__(self, database_manager=None):
        self.db_manager = database_manager
        self.error_patterns = self._load_error_patterns()
        self.severity_weights = self._load_severity_weights()
        self.classification_rules = self._load_classification_rules()
        self.historical_data = []
        
    def _load_error_patterns(self) -> Dict[str, List[str]]:
        """加载错误模式"""
        return {
            'critical': [
                r'fatal\s+error',
                r'system\s+crash',
                r'database\s+connection\s+failed',
                r'out\s+of\s+memory',
                r'stack\s+overflow',
                r'segmentation\s+fault',
                r'access\s+violation'
            ],
            'high': [
                r'uncaught\s+exception',
                r'null\s+pointer',
                r'timeout\s+error',
                r'authentication\s+failed',
                r'permission\s+denied',
                r'resource\s+not\s+found',
                r'connection\s+refused'
            ],
            'medium': [
                r'warning',
                r'deprecated',
                r'slow\s+query',
                r'cache\s+miss',
                r'retry\s+attempt',
                r'validation\s+error'
            ],
            'low': [
                r'info',
                r'debug',
                r'trace',
                r'notice'
            ]
        }
    
    def _load_severity_weights(self) -> Dict[str, float]:
        """加载严重程度权重"""
        return {
            'error_frequency': 0.3,      # 错误频率
            'user_impact': 0.25,         # 用户影响
            'system_impact': 0.2,        # 系统影响
            'business_impact': 0.15,     # 业务影响
            'recovery_difficulty': 0.1   # 恢复难度
        }
    
    def _load_classification_rules(self) -> Dict[str, Dict[str, Any]]:
        """加载分类规则"""
        return {
            'ui': {
                'keywords': ['dom', 'element', 'css', 'style', 'render', 'display'],
                'patterns': [r'element.*not.*found', r'css.*error', r'render.*failed'],
                'category': 'ui'
            },
            'api': {
                'keywords': ['api', 'endpoint', 'request', 'response', 'http', 'rest'],
                'patterns': [r'api.*error', r'http.*\d{3}', r'request.*failed'],
                'category': 'api'
            },
            'database': {
                'keywords': ['database', 'sql', 'query', 'connection', 'table', 'index'],
                'patterns': [r'sql.*error', r'database.*connection', r'query.*timeout'],
                'category': 'database'
            },
            'performance': {
                'keywords': ['slow', 'timeout', 'memory', 'cpu', 'performance', 'latency'],
                'patterns': [r'timeout.*exceeded', r'memory.*leak', r'slow.*response'],
                'category': 'performance'
            },
            'security': {
                'keywords': ['auth', 'permission', 'access', 'security', 'token', 'csrf'],
                'patterns': [r'access.*denied', r'authentication.*failed', r'unauthorized'],
                'category': 'security'
            },
            'integration': {
                'keywords': ['integration', 'external', 'third.*party', 'webhook', 'callback'],
                'patterns': [r'integration.*failed', r'external.*service', r'webhook.*error'],
                'category': 'integration'
            }
        }
    
    def detect_and_classify(self, error_data: Dict[str, Any]) -> Dict[str, Any]:
        """检测并分类错误"""
        try:
            # 基础信息提取
            error_message = error_data.get('message', '').lower()
            error_type = error_data.get('type', '').lower()
            stack_trace = error_data.get('stack_trace', '').lower()
            page_url = error_data.get('page_url', '')
            
            # 综合文本用于分析
            combined_text = f"{error_message} {error_type} {stack_trace}"
            
            # 1. 严重程度评估
            severity = self._assess_severity_enhanced(combined_text, error_data)
            
            # 2. 错误分类
            category = self._classify_error_enhanced(combined_text, error_data)
            
            # 3. 优先级计算
            priority = self._calculate_priority_enhanced(severity, category, error_data)
            
            # 4. 环境检测
            environment = self._detect_environment(error_data)
            
            # 5. 影响分析
            impact_analysis = self._analyze_impact(error_data, severity, category)
            
            # 6. 相似错误检测
            similar_errors = self._find_similar_errors(error_data)
            
            # 7. 修复建议生成
            fix_suggestions = self._generate_fix_suggestions_enhanced(
                combined_text, category, similar_errors
            )
            
            # 8. 标签生成
            tags = self._generate_tags_enhanced(error_data, category, severity)
            
            return {
                'severity': severity,
                'category': category,
                'priority': priority,
                'environment': environment,
                'impact_analysis': impact_analysis,
                'similar_errors': similar_errors,
                'fix_suggestions': fix_suggestions,
                'tags': tags,
                'confidence_score': self._calculate_confidence_score(error_data),
                'detection_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in detect_and_classify: {e}")
            return self._get_fallback_classification(error_data)
    
    def _assess_severity_enhanced(self, combined_text: str, error_data: Dict[str, Any]) -> str:
        """增强的严重程度评估"""
        severity_scores = {'critical': 0, 'high': 0, 'medium': 0, 'low': 0}
        
        # 1. 基于模式匹配的评分
        for severity, patterns in self.error_patterns.items():
            for pattern in patterns:
                if re.search(pattern, combined_text, re.IGNORECASE):
                    severity_scores[severity] += 1
        
        # 2. 基于频率的评分
        frequency_score = self._calculate_frequency_score(error_data)
        
        # 3. 基于用户影响的评分
        user_impact_score = self._calculate_user_impact_score(error_data)
        
        # 4. 基于系统影响的评分
        system_impact_score = self._calculate_system_impact_score(error_data)
        
        # 综合评分
        weights = self.severity_weights
        final_scores = {}
        
        for severity in severity_scores:
            pattern_score = severity_scores[severity] * 0.4
            final_scores[severity] = (
                pattern_score +
                frequency_score * weights['error_frequency'] +
                user_impact_score * weights['user_impact'] +
                system_impact_score * weights['system_impact']
            )
        
        # 返回得分最高的严重程度
        return max(final_scores, key=final_scores.get)
    
    def _classify_error_enhanced(self, combined_text: str, error_data: Dict[str, Any]) -> str:
        """增强的错误分类"""
        category_scores = defaultdict(float)
        
        # 1. 基于关键词的分类
        for category, rules in self.classification_rules.items():
            for keyword in rules['keywords']:
                if keyword in combined_text:
                    category_scores[category] += 1
        
        # 2. 基于正则模式的分类
        for category, rules in self.classification_rules.items():
            for pattern in rules['patterns']:
                if re.search(pattern, combined_text, re.IGNORECASE):
                    category_scores[category] += 2  # 模式匹配权重更高
        
        # 3. 基于页面URL的分类
        page_url = error_data.get('page_url', '').lower()
        if 'api' in page_url or '/api/' in page_url:
            category_scores['api'] += 1
        elif 'admin' in page_url:
            category_scores['ui'] += 1
        
        # 返回得分最高的分类，如果没有匹配则返回general
        if category_scores:
            return max(category_scores, key=category_scores.get)
        else:
            return 'general'
    
    def _calculate_priority_enhanced(self, severity: str, category: str, error_data: Dict[str, Any]) -> str:
        """增强的优先级计算"""
        # 基础优先级映射
        base_priority = {
            'critical': 'P1',
            'high': 'P2', 
            'medium': 'P3',
            'low': 'P4'
        }.get(severity, 'P4')
        
        # 根据分类调整优先级
        category_adjustments = {
            'security': -1,      # 安全问题提升优先级
            'database': -1,      # 数据库问题提升优先级
            'api': 0,           # API问题保持原优先级
            'ui': 1,            # UI问题降低优先级
            'performance': 0,    # 性能问题保持原优先级
            'integration': 1     # 集成问题降低优先级
        }
        
        adjustment = category_adjustments.get(category, 0)
        priority_num = int(base_priority[1:]) + adjustment
        priority_num = max(1, min(4, priority_num))  # 限制在P1-P4范围内
        
        return f'P{priority_num}'
    
    def _detect_environment(self, error_data: Dict[str, Any]) -> str:
        """检测运行环境"""
        page_url = error_data.get('page_url', '').lower()
        user_agent = error_data.get('user_agent', '').lower()
        
        # 基于URL判断
        if 'localhost' in page_url or '127.0.0.1' in page_url:
            return 'development'
        elif 'test' in page_url or 'staging' in page_url:
            return 'testing'
        elif 'dev' in page_url:
            return 'development'
        else:
            return 'production'
    
    def _analyze_impact(self, error_data: Dict[str, Any], severity: str, category: str) -> Dict[str, Any]:
        """分析错误影响"""
        return {
            'user_impact': self._assess_user_impact(error_data, severity),
            'system_impact': self._assess_system_impact(error_data, category),
            'business_impact': self._assess_business_impact(error_data, severity, category),
            'estimated_affected_users': self._estimate_affected_users(error_data)
        }
    
    def _find_similar_errors(self, error_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """查找相似错误"""
        if not self.db_manager:
            return []
        
        try:
            # 获取最近的错误记录
            recent_errors = self.db_manager.get_bug_reports(limit=100)
            similar_errors = []
            
            for error in recent_errors:
                similarity_score = self._calculate_similarity_score(error_data, error)
                if similarity_score > 0.6:  # 相似度阈值
                    similar_errors.append({
                        'bug_id': error.get('id'),
                        'similarity_score': similarity_score,
                        'status': error.get('status'),
                        'created_at': error.get('created_at'),
                        'resolution_notes': error.get('resolution_notes')
                    })
            
            # 按相似度排序
            similar_errors.sort(key=lambda x: x['similarity_score'], reverse=True)
            return similar_errors[:5]  # 返回最相似的5个
            
        except Exception as e:
            logger.error(f"Error finding similar errors: {e}")
            return []
    
    def _calculate_similarity_score(self, error1: Dict[str, Any], error2: Dict[str, Any]) -> float:
        """计算错误相似度得分"""
        score = 0.0
        
        # 错误类型相似度 (30%)
        if error1.get('type', '').lower() == error2.get('error_type', '').lower():
            score += 0.3
        
        # 错误消息相似度 (40%)
        msg1_words = set(error1.get('message', '').lower().split())
        msg2_words = set(error2.get('error_message', '').lower().split())
        
        if msg1_words and msg2_words:
            intersection = len(msg1_words & msg2_words)
            union = len(msg1_words | msg2_words)
            if union > 0:
                score += 0.4 * (intersection / union)
        
        # 页面相似度 (20%)
        if error1.get('page_url', '') == error2.get('page_name', ''):
            score += 0.2
        
        # 堆栈跟踪相似度 (10%)
        stack1 = error1.get('stack_trace', '').lower()
        stack2 = error2.get('stack_trace', '').lower()
        
        if stack1 and stack2:
            stack1_lines = set(stack1.split('\n')[:5])  # 只比较前5行
            stack2_lines = set(stack2.split('\n')[:5])
            
            if stack1_lines and stack2_lines:
                intersection = len(stack1_lines & stack2_lines)
                union = len(stack1_lines | stack2_lines)
                if union > 0:
                    score += 0.1 * (intersection / union)
        
        return score

    def _generate_fix_suggestions_enhanced(self, combined_text: str, category: str, similar_errors: List[Dict]) -> List[str]:
        """生成增强的修复建议"""
        suggestions = []

        # 基于分类的建议
        category_suggestions = {
            'ui': [
                "检查DOM元素是否正确加载",
                "验证CSS样式表引用",
                "检查JavaScript事件绑定",
                "确认浏览器兼容性"
            ],
            'api': [
                "验证API端点可访问性",
                "检查请求参数格式",
                "确认认证令牌有效性",
                "检查网络连接状态"
            ],
            'database': [
                "检查数据库连接配置",
                "优化SQL查询性能",
                "验证数据库权限",
                "检查数据完整性"
            ],
            'performance': [
                "优化代码执行效率",
                "增加缓存机制",
                "检查内存使用情况",
                "优化数据库查询"
            ],
            'security': [
                "更新安全配置",
                "检查权限设置",
                "验证输入数据",
                "加强认证机制"
            ],
            'integration': [
                "检查第三方服务状态",
                "验证API密钥配置",
                "检查网络连接",
                "更新集成配置"
            ]
        }

        suggestions.extend(category_suggestions.get(category, ["需要进一步分析错误原因"]))

        # 基于关键词的建议
        if 'timeout' in combined_text:
            suggestions.append("增加请求超时时间")
            suggestions.append("优化响应性能")

        if 'memory' in combined_text:
            suggestions.append("检查内存泄漏")
            suggestions.append("优化内存使用")

        if 'null' in combined_text or 'undefined' in combined_text:
            suggestions.append("添加空值检查")
            suggestions.append("初始化变量")

        # 基于相似错误的建议
        if similar_errors:
            resolved_errors = [e for e in similar_errors if e['status'] == 'resolved']
            if resolved_errors:
                suggestions.append("参考相似已解决问题的解决方案")
                for error in resolved_errors[:2]:  # 最多参考2个
                    if error.get('resolution_notes'):
                        suggestions.append(f"参考解决方案: {error['resolution_notes']}")

        return list(set(suggestions))  # 去重

    def _generate_tags_enhanced(self, error_data: Dict[str, Any], category: str, severity: str) -> List[str]:
        """生成增强的标签"""
        tags = []

        # 基础标签
        tags.append(f"category:{category}")
        tags.append(f"severity:{severity}")
        tags.append(f"environment:{self._detect_environment(error_data)}")

        # 基于错误内容的标签
        error_message = error_data.get('message', '').lower()
        error_type = error_data.get('type', '').lower()

        # 技术标签
        if 'javascript' in error_type:
            tags.append('tech:javascript')
        if 'python' in error_type:
            tags.append('tech:python')
        if 'sql' in error_message:
            tags.append('tech:sql')

        # 特征标签
        if 'timeout' in error_message:
            tags.append('feature:timeout')
        if 'memory' in error_message:
            tags.append('feature:memory')
        if 'network' in error_message:
            tags.append('feature:network')
        if 'auth' in error_message:
            tags.append('feature:authentication')

        # 浏览器标签
        user_agent = error_data.get('user_agent', '').lower()
        if 'chrome' in user_agent:
            tags.append('browser:chrome')
        elif 'firefox' in user_agent:
            tags.append('browser:firefox')
        elif 'safari' in user_agent:
            tags.append('browser:safari')
        elif 'edge' in user_agent:
            tags.append('browser:edge')

        # 时间标签
        now = datetime.now()
        tags.append(f"hour:{now.hour}")
        tags.append(f"weekday:{now.weekday()}")

        return tags

    def _calculate_frequency_score(self, error_data: Dict[str, Any]) -> float:
        """计算错误频率得分"""
        if not self.db_manager:
            return 0.0

        try:
            # 获取最近24小时内相似错误的数量
            error_type = error_data.get('type', '')
            recent_errors = self.db_manager.get_bug_reports(limit=1000)

            # 计算相似错误数量
            similar_count = 0
            for error in recent_errors:
                if error.get('error_type') == error_type:
                    # 检查时间是否在24小时内
                    created_at = error.get('created_at', '')
                    if created_at:
                        try:
                            error_time = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                            if (datetime.now() - error_time).total_seconds() < 86400:  # 24小时
                                similar_count += 1
                        except:
                            pass

            # 频率得分：0-1之间，频率越高得分越高
            return min(similar_count / 10.0, 1.0)

        except Exception as e:
            logger.error(f"Error calculating frequency score: {e}")
            return 0.0

    def _calculate_user_impact_score(self, error_data: Dict[str, Any]) -> float:
        """计算用户影响得分"""
        # 基于页面重要性和错误类型评估用户影响
        page_url = error_data.get('page_url', '').lower()
        error_type = error_data.get('type', '').lower()

        # 重要页面权重
        important_pages = {
            'login': 0.9,
            'payment': 1.0,
            'checkout': 1.0,
            'register': 0.8,
            'dashboard': 0.7,
            'profile': 0.6
        }

        page_weight = 0.5  # 默认权重
        for page, weight in important_pages.items():
            if page in page_url:
                page_weight = weight
                break

        # 错误类型权重
        error_weights = {
            'javascript': 0.7,
            'network': 0.8,
            'api': 0.9,
            'database': 1.0
        }

        error_weight = error_weights.get(error_type, 0.5)

        return (page_weight + error_weight) / 2

    def _calculate_system_impact_score(self, error_data: Dict[str, Any]) -> float:
        """计算系统影响得分"""
        error_message = error_data.get('message', '').lower()

        # 系统关键词权重
        system_keywords = {
            'database': 1.0,
            'memory': 0.8,
            'cpu': 0.7,
            'disk': 0.6,
            'network': 0.8,
            'security': 0.9
        }

        max_impact = 0.0
        for keyword, weight in system_keywords.items():
            if keyword in error_message:
                max_impact = max(max_impact, weight)

        return max_impact

    def _assess_user_impact(self, error_data: Dict[str, Any], severity: str) -> str:
        """评估用户影响"""
        user_impact_score = self._calculate_user_impact_score(error_data)

        if user_impact_score >= 0.8:
            return "High - Critical user functionality affected"
        elif user_impact_score >= 0.6:
            return "Medium - Some user features impacted"
        elif user_impact_score >= 0.4:
            return "Low - Minor user experience issues"
        else:
            return "Minimal - Limited user impact"

    def _assess_system_impact(self, error_data: Dict[str, Any], category: str) -> str:
        """评估系统影响"""
        system_impact_score = self._calculate_system_impact_score(error_data)

        if system_impact_score >= 0.8:
            return "High - Core system components affected"
        elif system_impact_score >= 0.6:
            return "Medium - Some system functions impacted"
        elif system_impact_score >= 0.4:
            return "Low - Minor system issues"
        else:
            return "Minimal - Isolated component issue"

    def _assess_business_impact(self, error_data: Dict[str, Any], severity: str, category: str) -> str:
        """评估业务影响"""
        # 基于严重程度和分类评估业务影响
        business_critical_categories = ['api', 'database', 'security', 'payment']

        if severity == 'critical' or category in business_critical_categories:
            return "High - Business operations significantly affected"
        elif severity == 'high':
            return "Medium - Some business processes impacted"
        elif severity == 'medium':
            return "Low - Minor business impact"
        else:
            return "Minimal - No significant business impact"

    def _estimate_affected_users(self, error_data: Dict[str, Any]) -> str:
        """估算受影响用户数"""
        # 基于页面重要性和错误严重程度估算
        page_url = error_data.get('page_url', '').lower()

        if any(page in page_url for page in ['login', 'payment', 'checkout']):
            return "High (>50% of users potentially affected)"
        elif any(page in page_url for page in ['dashboard', 'profile']):
            return "Medium (10-50% of users potentially affected)"
        else:
            return "Low (<10% of users potentially affected)"

    def _calculate_confidence_score(self, error_data: Dict[str, Any]) -> float:
        """计算检测置信度"""
        confidence = 0.5  # 基础置信度

        # 有完整错误消息
        if error_data.get('message'):
            confidence += 0.2

        # 有堆栈跟踪
        if error_data.get('stack_trace'):
            confidence += 0.2

        # 有页面信息
        if error_data.get('page_url'):
            confidence += 0.1

        return min(confidence, 1.0)

    def _get_fallback_classification(self, error_data: Dict[str, Any]) -> Dict[str, Any]:
        """获取后备分类结果"""
        return {
            'severity': 'medium',
            'category': 'general',
            'priority': 'P3',
            'environment': 'production',
            'impact_analysis': {
                'user_impact': 'Unknown',
                'system_impact': 'Unknown',
                'business_impact': 'Unknown',
                'estimated_affected_users': 'Unknown'
            },
            'similar_errors': [],
            'fix_suggestions': ['需要进一步分析错误原因'],
            'tags': ['category:general', 'severity:medium'],
            'confidence_score': 0.3,
            'detection_timestamp': datetime.now().isoformat()
        }
