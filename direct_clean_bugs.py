#!/usr/bin/env python3
"""
直接清理测试Bug数据
"""

import os
import sqlite3


def main():
    try:
        # 数据库路径
        db_path = "data/lottery_data.db"
        
        if not os.path.exists(db_path):
            print(f"❌ 数据库文件不存在: {db_path}")
            return
        
        print("🧹 开始清理测试Bug数据...")
        print(f"📁 数据库路径: {db_path}")
        
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查看清理前的数据
        print("\n📊 清理前的数据统计:")
        cursor.execute("SELECT COUNT(*) FROM bug_reports")
        total_before = cursor.fetchone()[0]
        print(f"   总Bug数: {total_before}")
        
        if total_before > 0:
            cursor.execute("SELECT error_type, COUNT(*) FROM bug_reports GROUP BY error_type")
            types_before = cursor.fetchall()
            print("   按类型分布:")
            for error_type, count in types_before:
                print(f"     {error_type}: {count}个")
        
        # 执行清理操作
        print("\n🗑️ 执行清理操作...")
        
        # 清理测试相关的Bug报告
        cursor.execute("""
            DELETE FROM bug_reports 
            WHERE error_type IN ('test_error', 'integration_test')
            OR error_message LIKE '%测试%' 
            OR error_message LIKE '%BatchTest%'
            OR error_message LIKE '%TestError%'
            OR page_name LIKE '%bug_test.js%'
            OR page_name LIKE '%batch_test.js%'
            OR page_name = 'test_page'
        """)
        
        deleted_count = cursor.rowcount
        print(f"✅ 已删除 {deleted_count} 条测试数据")
        
        # 提交更改
        conn.commit()
        
        # 查看清理后的数据
        print("\n📊 清理后的数据统计:")
        cursor.execute("SELECT COUNT(*) FROM bug_reports")
        total_after = cursor.fetchone()[0]
        print(f"   总Bug数: {total_after}")
        
        if total_after > 0:
            cursor.execute("SELECT error_type, COUNT(*) FROM bug_reports GROUP BY error_type")
            types_after = cursor.fetchall()
            print("   按类型分布:")
            for error_type, count in types_after:
                print(f"     {error_type}: {count}个")
        else:
            print("   ✨ 所有Bug数据已清理完毕")
        
        # 显示清理摘要
        print(f"\n📋 清理摘要:")
        print(f"   清理前: {total_before} 个Bug")
        print(f"   清理后: {total_after} 个Bug")
        print(f"   已清理: {deleted_count} 个测试Bug")
        if total_before > 0:
            print(f"   清理率: {(deleted_count/total_before*100):.1f}%")
        else:
            print("   清理率: 0%")
        
        conn.close()
        
        print("\n🎉 测试Bug数据清理完成!")
        return True
        
    except Exception as e:
        print(f"❌ 清理测试Bug数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    if success:
        print("\n✅ 清理操作成功完成")
    else:
        print("\n❌ 清理操作失败")
