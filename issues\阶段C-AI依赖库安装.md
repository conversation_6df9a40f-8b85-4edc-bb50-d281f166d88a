# 阶段C：AI依赖库安装

## 📋 任务概述

**任务ID**: dft59dHUx5Xb8FyTzhHYVN  
**阶段**: C  
**优先级**: 高  
**状态**: 未开始  
**预计工期**: 90分钟  
**前置任务**: 阶段B - 数据库结构升级  
**后续任务**: 阶段D - AI功能验证与测试  

## 🎯 任务目标

安装所有必需的AI和机器学习库，包括transformers、scikit-learn、sentence-transformers等，解决依赖库缺失问题，为AI功能提供完整的技术支持。

## 📊 子任务详情

### C1：依赖库环境检查

**任务ID**: mWk6D1xfKmcjttWKVLKcVT  
**预计时间**: 15分钟  

#### 🔧 执行步骤

1. **检查当前Python环境**
   ```python
   import sys
   import pkg_resources
   
   def check_current_environment():
       print(f"Python版本: {sys.version}")
       print(f"Python路径: {sys.executable}")
       
       # 获取已安装包列表
       installed_packages = [d.project_name for d in pkg_resources.working_set]
       print(f"已安装包数量: {len(installed_packages)}")
       
       return installed_packages
   ```

2. **检查AI相关库状态**
   ```python
   def check_ai_libraries():
       ai_libraries = [
           'transformers', 'sentence-transformers', 'scikit-learn',
           'torch', 'numpy', 'pandas', 'nltk', 'spacy'
       ]
       
       missing_libraries = []
       for lib in ai_libraries:
           try:
               __import__(lib)
               print(f"✅ {lib} - 已安装")
           except ImportError:
               print(f"❌ {lib} - 缺失")
               missing_libraries.append(lib)
       
       return missing_libraries
   ```

3. **检查版本兼容性**
   ```python
   def check_version_compatibility():
       version_requirements = {
           'transformers': '4.48.0',
           'scikit-learn': '1.3.0',
           'torch': '2.1.0',
           'numpy': '1.21.0',
           'pandas': '1.5.0'
       }
       
       # 检查每个库的版本是否满足要求
   ```

#### 📁 涉及文件
- 无特定文件
- Python环境检查

#### ✅ 成功标准
- Python环境信息完整
- 缺失库清单准确
- 版本兼容性分析完成

---

### C2：执行AI依赖安装脚本

**任务ID**: o2fQPiwahjaQAYEGC1DdRU  
**预计时间**: 45分钟  

#### 🔧 执行步骤

1. **运行安装脚本**
   ```bash
   python install_ai_dependencies.py
   ```

2. **核心依赖库安装**
   ```python
   # 脚本将安装以下核心库
   core_packages = [
       "transformers>=4.48.0",      # 🤗 Transformers
       "sentence-transformers",      # 📝 Sentence Transformers
       "scikit-learn>=1.3.0",       # 🔬 Scikit-learn
       "torch>=2.1.0",              # 🔥 PyTorch
       "torchvision",               # 👁️ TorchVision
       "numpy>=1.21.0",             # 🔢 NumPy
       "pandas>=1.5.0",             # 🐼 Pandas
       "nltk",                      # 📚 NLTK
       "spacy",                     # 🚀 spaCy
       "accelerate",                # ⚡ Accelerate
       "datasets",                  # 📊 Datasets
       "tokenizers",                # 🔤 Tokenizers
   ]
   ```

3. **监控安装过程**
   - 每个包的安装状态
   - 错误信息收集
   - 安装时间记录
   - 依赖冲突处理

#### 📁 涉及文件
- `install_ai_dependencies.py` (安装脚本)
- Python site-packages目录

#### 🔧 关键类和方法
- `AIDepInstaller.__init__()`
- `AIDepInstaller.install_package()`
- `AIDepInstaller.verify_installation()`
- `AIDepInstaller.install_core_dependencies()`

#### ✅ 成功标准
- 所有核心库安装成功
- 无依赖冲突
- 安装日志完整
- 验证测试通过

---

### C3：核心依赖库验证

**任务ID**: 8QnGCkob3by2Z8T4q1Fn46  
**预计时间**: 20分钟  

#### 🔧 执行步骤

1. **验证Transformers库**
   ```python
   def test_transformers():
       try:
           from transformers import pipeline, AutoTokenizer, AutoModel
           
           # 测试基本功能
           classifier = pipeline("sentiment-analysis")
           result = classifier("This is a test")
           print(f"✅ Transformers基本功能正常: {result}")
           
           return True
       except Exception as e:
           print(f"❌ Transformers测试失败: {e}")
           return False
   ```

2. **验证Scikit-learn库**
   ```python
   def test_scikit_learn():
       try:
           from sklearn.feature_extraction.text import TfidfVectorizer
           from sklearn.ensemble import RandomForestClassifier
           
           # 测试TF-IDF功能
           vectorizer = TfidfVectorizer()
           texts = ["这是测试文本", "另一个测试"]
           tfidf_matrix = vectorizer.fit_transform(texts)
           print(f"✅ TF-IDF功能正常: {tfidf_matrix.shape}")
           
           return True
       except Exception as e:
           print(f"❌ Scikit-learn测试失败: {e}")
           return False
   ```

3. **验证Sentence Transformers库**
   ```python
   def test_sentence_transformers():
       try:
           from sentence_transformers import SentenceTransformer
           
           # 测试语义相似度
           model = SentenceTransformer('all-MiniLM-L6-v2')
           sentences = ["这是一个测试", "这是另一个测试"]
           embeddings = model.encode(sentences)
           print(f"✅ Sentence Transformers功能正常: {embeddings.shape}")
           
           return True
       except Exception as e:
           print(f"❌ Sentence Transformers测试失败: {e}")
           return False
   ```

#### 📁 涉及文件
- 已安装的Python包
- 测试脚本

#### ✅ 成功标准
- Transformers库功能正常
- Scikit-learn库功能正常
- Sentence Transformers库功能正常
- 所有导入测试通过

---

### C4：预训练模型下载

**任务ID**: 2T5SbczB85h1k8nKkUPmkL  
**预计时间**: 10分钟  

#### 🔧 执行步骤

1. **下载Sentence Transformers模型**
   ```python
   def download_sentence_transformer_model():
       try:
           from sentence_transformers import SentenceTransformer
           
           # 下载轻量级模型
           model_name = 'all-MiniLM-L6-v2'
           model = SentenceTransformer(model_name)
           
           print(f"✅ 模型 {model_name} 下载完成")
           return True
       except Exception as e:
           print(f"❌ 模型下载失败: {e}")
           return False
   ```

2. **验证模型缓存**
   ```python
   def verify_model_cache():
       import os
       from pathlib import Path
       
       # 检查模型缓存目录
       cache_dir = Path.home() / '.cache' / 'torch' / 'sentence_transformers'
       if cache_dir.exists():
           models = list(cache_dir.glob('*'))
           print(f"✅ 缓存模型数量: {len(models)}")
           return True
       else:
           print("⚠️ 模型缓存目录不存在")
           return False
   ```

3. **测试模型加载速度**
   ```python
   def test_model_loading_speed():
       import time
       from sentence_transformers import SentenceTransformer
       
       start_time = time.time()
       model = SentenceTransformer('all-MiniLM-L6-v2')
       load_time = time.time() - start_time
       
       print(f"✅ 模型加载时间: {load_time:.2f}秒")
       return load_time < 30  # 期望加载时间小于30秒
   ```

#### 📁 涉及文件
- `~/.cache/torch/sentence_transformers/` (模型缓存)
- `~/.cache/huggingface/` (Transformers缓存)

#### ✅ 成功标准
- 预训练模型下载成功
- 模型缓存验证通过
- 模型加载速度可接受
- 模型功能测试正常

## 🔄 依赖关系

```mermaid
graph TD
    B[阶段B: 数据库升级] --> C1[C1: 依赖库环境检查]
    C1 --> C2[C2: 执行AI依赖安装脚本]
    C2 --> C3[C3: 核心依赖库验证]
    C3 --> C4[C4: 预训练模型下载]
    C4 --> D[阶段D: AI功能验证与测试]
```

## ⚠️ 风险点与缓解措施

### 🚨 高风险

1. **网络连接问题**
   - **风险**: 包下载失败，模型下载失败
   - **缓解**: 使用镜像源，离线安装包
   - **回滚**: 稍后重试，使用备用源

2. **依赖冲突**
   - **风险**: 版本不兼容，包冲突
   - **缓解**: 虚拟环境，版本锁定
   - **回滚**: 卸载冲突包，降级版本

3. **磁盘空间不足**
   - **风险**: 安装失败，模型下载失败
   - **缓解**: 清理缓存，扩展存储
   - **回滚**: 删除不必要文件

### ⚠️ 中风险

1. **编译错误**
   - **风险**: C扩展编译失败
   - **缓解**: 预编译包，编译工具
   - **回滚**: 使用纯Python版本

2. **内存不足**
   - **风险**: 大模型加载失败
   - **缓解**: 轻量级模型，模型量化
   - **回滚**: 使用更小的模型

## 📦 安装包详情

### 🔥 核心AI库

| 包名 | 版本要求 | 大小 | 用途 |
|------|----------|------|------|
| transformers | ≥4.48.0 | ~500MB | BERT/RoBERTa模型 |
| sentence-transformers | latest | ~200MB | 语义相似度 |
| scikit-learn | ≥1.3.0 | ~50MB | 机器学习 |
| torch | ≥2.1.0 | ~800MB | 深度学习框架 |
| numpy | ≥1.21.0 | ~20MB | 数值计算 |
| pandas | ≥1.5.0 | ~30MB | 数据处理 |

### 📚 NLP工具

| 包名 | 版本要求 | 大小 | 用途 |
|------|----------|------|------|
| nltk | latest | ~10MB | 自然语言处理 |
| spacy | latest | ~50MB | 高级NLP |
| tokenizers | latest | ~20MB | 快速分词 |

### ⚡ 增强库

| 包名 | 版本要求 | 大小 | 用途 |
|------|----------|------|------|
| accelerate | latest | ~30MB | 模型加速 |
| datasets | latest | ~100MB | 数据集管理 |
| bitsandbytes | latest | ~50MB | 量化优化 |

## 📋 验证清单

### ✅ 阶段C完成标准

- [ ] **C1**: 依赖库环境检查完成，缺失库清单确认
- [ ] **C2**: AI依赖安装脚本执行成功，所有核心库安装完成
- [ ] **C3**: 核心依赖库验证通过，功能测试正常
- [ ] **C4**: 预训练模型下载成功，加载速度可接受

### 🔍 质量检查

- [ ] 所有核心库可正常导入
- [ ] TF-IDF功能正常工作
- [ ] Transformers管道功能正常
- [ ] Sentence Transformers编码功能正常
- [ ] 模型缓存完整
- [ ] 无版本冲突

## 📊 预期结果

### 🎯 安装成功指标

1. **包安装成功率**: 100%
2. **功能测试通过率**: 100%
3. **模型下载成功率**: 100%
4. **总安装时间**: <90分钟
5. **磁盘使用增加**: ~2GB

### 🚀 功能增强

1. **智能错误分类**: 支持9种错误类型自动识别
2. **语义相似度分析**: 支持错误聚类和去重
3. **机器学习分类**: 支持TF-IDF + Random Forest
4. **深度学习支持**: 支持BERT等预训练模型
5. **高性能处理**: 支持GPU加速和模型量化

---

**创建时间**: 2025-07-25  
**最后更新**: 2025-07-25  
**版本**: v1.0
