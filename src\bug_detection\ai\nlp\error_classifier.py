#!/usr/bin/env python3
"""
AI智能Bug检测系统 - NLP错误分类器
基于BERT/RoBERTa模型的错误日志智能分析
"""

import os
import json
import pickle
import logging
from typing import Dict, List, Tuple, Optional
import numpy as np
import pandas as pd
from datetime import datetime

# NLP核心库
try:
    from transformers import AutoTokenizer, AutoModel, pipeline
    from sentence_transformers import SentenceTransformer
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.metrics.pairwise import cosine_similarity
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    logging.warning("Transformers库未安装，将使用传统NLP方法")

# 传统NLP库
import re
from collections import Counter, defaultdict

logger = logging.getLogger(__name__)

class ErrorClassifier:
    """AI错误分类器 - 智能分析错误日志"""
    
    def __init__(self, model_path: str = "models/nlp/"):
        self.model_path = model_path
        self.error_categories = {
            'ui': ['dom', 'element', 'css', 'style', 'render', 'display', 'component', 'widget'],
            'api': ['api', 'endpoint', 'request', 'response', 'http', 'rest', 'fetch', 'ajax'],
            'database': ['database', 'sql', 'query', 'connection', 'table', 'record', 'transaction'],
            'network': ['network', 'connection', 'socket', 'tcp', 'dns', 'proxy', 'timeout'],
            'performance': ['slow', 'timeout', 'memory', 'cpu', 'performance', 'lag', 'bottleneck'],
            'security': ['security', 'auth', 'permission', 'access', 'token', 'csrf', 'xss'],
            'data': ['data', 'validation', 'format', 'parse', 'serialize', 'json', 'xml'],
            'integration': ['integration', 'external', 'third-party', 'webhook', 'callback'],
            'general': ['error', 'exception', 'failed', 'undefined', 'null', 'unknown']
        }
        
        # 初始化模型
        self.bert_model = None
        self.sentence_transformer = None
        self.tfidf_vectorizer = None
        self.rf_classifier = None
        
        self._initialize_models()
    
    def _initialize_models(self):
        """初始化AI模型"""
        try:
            if TRANSFORMERS_AVAILABLE:
                self._load_transformer_models()
            else:
                self._load_traditional_models()
        except Exception as e:
            logger.error(f"模型初始化失败: {e}")
            self._fallback_to_rule_based()
    
    def _load_transformer_models(self):
        """加载Transformer模型"""
        try:
            # 加载预训练的BERT模型
            model_name = "bert-base-uncased"
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.bert_model = AutoModel.from_pretrained(model_name)
            
            # 加载句子嵌入模型
            self.sentence_transformer = SentenceTransformer('all-MiniLM-L6-v2')
            
            logger.info("✅ Transformer模型加载成功")
            
        except Exception as e:
            logger.warning(f"Transformer模型加载失败: {e}")
            self._load_traditional_models()
    
    def _load_traditional_models(self):
        """加载传统机器学习模型"""
        try:
            # 初始化TF-IDF向量化器
            self.tfidf_vectorizer = TfidfVectorizer(
                max_features=1000,
                stop_words='english',
                ngram_range=(1, 2)
            )
            
            # 初始化随机森林分类器
            self.rf_classifier = RandomForestClassifier(
                n_estimators=100,
                random_state=42
            )
            
            logger.info("✅ 传统ML模型初始化成功")
            
        except Exception as e:
            logger.error(f"传统ML模型初始化失败: {e}")
            self._fallback_to_rule_based()
    
    def _fallback_to_rule_based(self):
        """回退到基于规则的分类"""
        logger.info("🔄 回退到基于规则的错误分类")
        self.use_rule_based = True
    
    def classify_error(self, error_message: str, context: Dict = None) -> Dict:
        """智能错误分类"""
        try:
            if hasattr(self, 'sentence_transformer') and self.sentence_transformer:
                return self._classify_with_transformers(error_message, context)
            elif self.tfidf_vectorizer and self.rf_classifier:
                return self._classify_with_ml(error_message, context)
            else:
                return self._classify_with_rules(error_message, context)
        except Exception as e:
            logger.error(f"错误分类失败: {e}")
            return self._classify_with_rules(error_message, context)
    
    def _classify_with_transformers(self, error_message: str, context: Dict = None) -> Dict:
        """使用Transformer模型分类"""
        try:
            # 生成错误消息的嵌入向量
            error_embedding = self.sentence_transformer.encode([error_message])
            
            # 生成各类别的代表性嵌入
            category_embeddings = {}
            for category, keywords in self.error_categories.items():
                category_text = " ".join(keywords)
                category_embeddings[category] = self.sentence_transformer.encode([category_text])
            
            # 计算相似度
            similarities = {}
            for category, embedding in category_embeddings.items():
                similarity = cosine_similarity(error_embedding, embedding)[0][0]
                similarities[category] = similarity
            
            # 选择最相似的类别
            predicted_category = max(similarities, key=similarities.get)
            confidence = similarities[predicted_category]
            
            # 增强分析
            severity = self._analyze_severity_ai(error_message, predicted_category)
            priority = self._calculate_priority_ai(severity, predicted_category, context)
            
            return {
                'category': predicted_category,
                'confidence': float(confidence),
                'severity': severity,
                'priority': priority,
                'method': 'transformer',
                'similarities': similarities,
                'analysis_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Transformer分类失败: {e}")
            return self._classify_with_rules(error_message, context)
    
    def _classify_with_ml(self, error_message: str, context: Dict = None) -> Dict:
        """使用传统机器学习分类"""
        try:
            # 如果模型未训练，先进行训练
            if not hasattr(self.rf_classifier, 'classes_'):
                self._train_ml_model()
            
            # 特征提取
            features = self.tfidf_vectorizer.transform([error_message])
            
            # 预测
            prediction = self.rf_classifier.predict(features)[0]
            probabilities = self.rf_classifier.predict_proba(features)[0]
            confidence = max(probabilities)
            
            # 获取类别概率
            class_probabilities = dict(zip(self.rf_classifier.classes_, probabilities))
            
            severity = self._analyze_severity_ml(error_message, prediction)
            priority = self._calculate_priority_ml(severity, prediction, context)
            
            return {
                'category': prediction,
                'confidence': float(confidence),
                'severity': severity,
                'priority': priority,
                'method': 'machine_learning',
                'probabilities': class_probabilities,
                'analysis_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"ML分类失败: {e}")
            return self._classify_with_rules(error_message, context)
    
    def _classify_with_rules(self, error_message: str, context: Dict = None) -> Dict:
        """基于规则的分类 (回退方案)"""
        error_lower = error_message.lower()
        category_scores = defaultdict(float)
        
        # 关键词匹配
        for category, keywords in self.error_categories.items():
            for keyword in keywords:
                if keyword in error_lower:
                    category_scores[category] += 1
        
        # 正则模式匹配
        patterns = {
            'ui': [r'element.*not.*found', r'css.*error', r'render.*failed'],
            'api': [r'http.*\d{3}', r'api.*error', r'request.*failed'],
            'database': [r'sql.*error', r'database.*connection', r'query.*timeout'],
            'network': [r'network.*error', r'connection.*refused', r'timeout'],
            'performance': [r'timeout.*exceeded', r'memory.*leak', r'slow.*response'],
            'security': [r'access.*denied', r'authentication.*failed', r'unauthorized'],
            'data': [r'parse.*error', r'invalid.*format', r'validation.*failed'],
            'integration': [r'external.*service', r'webhook.*error', r'third.*party']
        }
        
        for category, pattern_list in patterns.items():
            for pattern in pattern_list:
                if re.search(pattern, error_lower):
                    category_scores[category] += 2
        
        # 选择得分最高的类别
        if category_scores:
            predicted_category = max(category_scores, key=category_scores.get)
            confidence = category_scores[predicted_category] / sum(category_scores.values())
        else:
            predicted_category = 'general'
            confidence = 0.5
        
        severity = self._analyze_severity_rules(error_message, predicted_category)
        priority = self._calculate_priority_rules(severity, predicted_category, context)
        
        return {
            'category': predicted_category,
            'confidence': float(confidence),
            'severity': severity,
            'priority': priority,
            'method': 'rule_based',
            'scores': dict(category_scores),
            'analysis_time': datetime.now().isoformat()
        }
    
    def _train_ml_model(self):
        """训练机器学习模型 (使用模拟数据)"""
        try:
            # 生成训练数据 (实际应用中应使用真实历史数据)
            training_data = self._generate_training_data()
            
            # 特征提取
            X = self.tfidf_vectorizer.fit_transform(training_data['messages'])
            y = training_data['categories']
            
            # 训练模型
            self.rf_classifier.fit(X, y)
            
            logger.info("✅ ML模型训练完成")
            
        except Exception as e:
            logger.error(f"ML模型训练失败: {e}")
    
    def _generate_training_data(self) -> Dict:
        """生成训练数据 (模拟数据)"""
        training_samples = {
            'ui': [
                "Cannot read property 'innerHTML' of null",
                "Element not found in DOM",
                "CSS rendering error",
                "Component failed to render"
            ],
            'api': [
                "HTTP 500 Internal Server Error",
                "API endpoint not found",
                "Request timeout",
                "JSON parse error in response"
            ],
            'database': [
                "Database connection failed",
                "SQL syntax error",
                "Query execution timeout",
                "Table does not exist"
            ],
            'network': [
                "Network connection refused",
                "DNS resolution failed",
                "Socket timeout error",
                "Proxy connection error"
            ],
            'performance': [
                "Memory leak detected",
                "CPU usage too high",
                "Response time exceeded",
                "Slow query performance"
            ],
            'security': [
                "Access denied",
                "Authentication failed",
                "Invalid security token",
                "CSRF token mismatch"
            ],
            'data': [
                "Data validation failed",
                "Invalid JSON format",
                "Parse error",
                "Encoding error"
            ],
            'integration': [
                "External service unavailable",
                "Webhook timeout",
                "Third-party API error",
                "Integration failed"
            ]
        }
        
        messages = []
        categories = []
        
        for category, samples in training_samples.items():
            messages.extend(samples)
            categories.extend([category] * len(samples))
        
        return {'messages': messages, 'categories': categories}
    
    def _analyze_severity_ai(self, error_message: str, category: str) -> str:
        """AI分析错误严重程度"""
        # 基于错误消息和类别的智能严重程度分析
        critical_keywords = ['critical', 'fatal', 'crash', 'security', 'data loss']
        high_keywords = ['error', 'failed', 'timeout', 'exception']
        medium_keywords = ['warning', 'deprecated', 'slow']
        
        error_lower = error_message.lower()
        
        if any(keyword in error_lower for keyword in critical_keywords):
            return 'critical'
        elif category in ['security', 'database'] or any(keyword in error_lower for keyword in high_keywords):
            return 'high'
        elif any(keyword in error_lower for keyword in medium_keywords):
            return 'medium'
        else:
            return 'low'
    
    def _analyze_severity_ml(self, error_message: str, category: str) -> str:
        """ML分析错误严重程度"""
        return self._analyze_severity_ai(error_message, category)
    
    def _analyze_severity_rules(self, error_message: str, category: str) -> str:
        """规则分析错误严重程度"""
        return self._analyze_severity_ai(error_message, category)
    
    def _calculate_priority_ai(self, severity: str, category: str, context: Dict = None) -> str:
        """AI计算优先级"""
        priority_map = {'critical': 'P1', 'high': 'P2', 'medium': 'P3', 'low': 'P4'}
        base_priority = priority_map.get(severity, 'P3')
        
        # 根据类别调整优先级
        if category in ['security', 'database'] and base_priority != 'P1':
            priority_levels = ['P4', 'P3', 'P2', 'P1']
            current_index = priority_levels.index(base_priority)
            if current_index < len(priority_levels) - 1:
                base_priority = priority_levels[current_index + 1]
        
        return base_priority
    
    def _calculate_priority_ml(self, severity: str, category: str, context: Dict = None) -> str:
        """ML计算优先级"""
        return self._calculate_priority_ai(severity, category, context)
    
    def _calculate_priority_rules(self, severity: str, category: str, context: Dict = None) -> str:
        """规则计算优先级"""
        return self._calculate_priority_ai(severity, category, context)
    
    def analyze_batch(self, error_messages: List[str], contexts: List[Dict] = None) -> List[Dict]:
        """批量分析错误"""
        if contexts is None:
            contexts = [None] * len(error_messages)
        
        results = []
        for i, message in enumerate(error_messages):
            context = contexts[i] if i < len(contexts) else None
            result = self.classify_error(message, context)
            results.append(result)
        
        return results
    
    def get_model_info(self) -> Dict:
        """获取模型信息"""
        return {
            'transformers_available': TRANSFORMERS_AVAILABLE,
            'bert_model_loaded': self.bert_model is not None,
            'sentence_transformer_loaded': self.sentence_transformer is not None,
            'ml_model_trained': hasattr(self.rf_classifier, 'classes_') if self.rf_classifier else False,
            'supported_categories': list(self.error_categories.keys()),
            'model_path': self.model_path
        }

# 使用示例
if __name__ == "__main__":
    # 初始化分类器
    classifier = ErrorClassifier()
    
    # 测试错误分类
    test_errors = [
        "Cannot read property 'innerHTML' of null",
        "HTTP 500 Internal Server Error",
        "Database connection timeout",
        "Memory leak detected in application"
    ]
    
    print("🤖 AI错误分类器测试")
    print("=" * 50)
    
    for error in test_errors:
        result = classifier.classify_error(error)
        print(f"\n错误: {error}")
        print(f"分类: {result['category']}")
        print(f"置信度: {result['confidence']:.3f}")
        print(f"严重程度: {result['severity']}")
        print(f"优先级: {result['priority']}")
        print(f"方法: {result['method']}")
    
    # 显示模型信息
    print(f"\n📊 模型信息:")
    model_info = classifier.get_model_info()
    for key, value in model_info.items():
        print(f"  {key}: {value}")
