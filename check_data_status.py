#!/usr/bin/env python3
"""
检查福彩3D数据状态
验证数据更新情况
"""

import sys
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path

def check_latest_data():
    """检查最新数据状态"""
    print("🔍 福彩3D数据状态检查")
    print("=" * 50)
    
    # 检查最新的数据文件
    processed_dir = Path("data/processed")
    if not processed_dir.exists():
        print("❌ 数据目录不存在")
        return
    
    # 找到最新的CSV文件
    csv_files = list(processed_dir.glob("lottery_data_*.csv"))
    if not csv_files:
        print("❌ 没有找到数据文件")
        return
    
    latest_file = max(csv_files, key=lambda x: x.stat().st_mtime)
    print(f"📁 最新数据文件: {latest_file.name}")
    
    # 读取数据
    try:
        df = pd.read_csv(latest_file)
        print(f"📊 总记录数: {len(df)}")
        
        # 显示最新5条记录
        print("\n📋 最新5条记录:")
        latest_records = df.tail(5)[['period', 'numbers', 'date']]
        for _, row in latest_records.iterrows():
            print(f"  期号: {row['period']}, 号码: {row['numbers']}, 日期: {row['date']}")
        
        # 分析数据状态
        latest_period = df['period'].max()
        latest_date = df['date'].max()
        
        print(f"\n🎯 数据分析:")
        print(f"  最新期号: {latest_period}")
        print(f"  最新日期: {latest_date}")
        
        # 检查是否有今天的数据
        today = datetime.now().strftime('%Y-%m-%d')
        print(f"  今天日期: {today}")
        
        if latest_date == today:
            print("  ✅ 数据已是最新（包含今天的数据）")
        else:
            # 计算日期差
            try:
                latest_dt = datetime.strptime(latest_date, '%Y-%m-%d')
                today_dt = datetime.strptime(today, '%Y-%m-%d')
                days_diff = (today_dt - latest_dt).days
                
                if days_diff == 1:
                    print("  ⏳ 数据落后1天（可能今天还未开奖）")
                elif days_diff > 1:
                    print(f"  ⚠️ 数据落后{days_diff}天")
                else:
                    print("  ✅ 数据是最新的")
                    
            except ValueError:
                print("  ⚠️ 日期格式解析错误")
        
        # 福彩3D开奖时间说明
        print(f"\n💡 福彩3D开奖说明:")
        print(f"  - 开奖时间: 每天晚上20:30左右")
        print(f"  - 数据更新: 通常在21:00-22:00之间")
        print(f"  - 当前时间: {datetime.now().strftime('%H:%M:%S')}")
        
        current_hour = datetime.now().hour
        if current_hour < 21:
            print(f"  - 状态: 今天的开奖可能还未进行")
        elif current_hour < 23:
            print(f"  - 状态: 今天的数据可能正在更新中")
        else:
            print(f"  - 状态: 今天的数据应该已经可用")
            
    except Exception as e:
        print(f"❌ 读取数据文件失败: {e}")

def check_update_metadata():
    """检查更新元数据"""
    print(f"\n🔄 更新状态检查:")
    
    metadata_file = Path("data/processed/update_metadata.json")
    if metadata_file.exists():
        try:
            import json
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            print(f"  上次更新时间: {metadata.get('last_update', '未知')}")
            print(f"  更新状态: {metadata.get('status', '未知')}")
            print(f"  数据记录数: {metadata.get('total_records', '未知')}")
            
        except Exception as e:
            print(f"  ⚠️ 读取元数据失败: {e}")
    else:
        print(f"  ⚠️ 更新元数据文件不存在")

def check_scheduler_status():
    """检查调度器状态"""
    print(f"\n⏰ 调度器状态:")
    
    # 检查最新的调度器日志
    log_dir = Path("data/logs")
    if log_dir.exists():
        log_files = list(log_dir.glob("scheduler_*.log"))
        if log_files:
            latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
            print(f"  最新日志: {latest_log.name}")
            
            # 读取最后几行日志
            try:
                with open(latest_log, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # 查找最近的更新记录
                recent_updates = []
                for line in reversed(lines[-20:]):  # 检查最后20行
                    if "数据更新" in line and ("成功" in line or "完成" in line):
                        recent_updates.append(line.strip())
                        if len(recent_updates) >= 3:
                            break
                
                if recent_updates:
                    print(f"  最近更新记录:")
                    for update in recent_updates:
                        timestamp = update.split(' - ')[0] if ' - ' in update else "未知时间"
                        message = update.split(' - ')[-1] if ' - ' in update else update
                        print(f"    {timestamp}: {message}")
                else:
                    print(f"  ⚠️ 未找到最近的更新记录")
                    
            except Exception as e:
                print(f"  ⚠️ 读取日志失败: {e}")
        else:
            print(f"  ⚠️ 没有找到调度器日志文件")
    else:
        print(f"  ⚠️ 日志目录不存在")

def main():
    """主函数"""
    check_latest_data()
    check_update_metadata()
    check_scheduler_status()
    
    print(f"\n" + "=" * 50)
    print(f"🎯 总结:")
    print(f"如果显示'数据更新执行成功'但没有新数据，通常是因为:")
    print(f"1. 数据源确实没有新数据（正常情况）")
    print(f"2. 福彩3D还未开奖或数据还未发布")
    print(f"3. 系统正确检测到无需更新")
    print(f"\n💡 建议:")
    print(f"- 在晚上21:30后再次尝试更新")
    print(f"- 查看调度器是否设置了自动更新")
    print(f"- 如果确认有新数据但未更新，请检查网络连接")

if __name__ == "__main__":
    main()
