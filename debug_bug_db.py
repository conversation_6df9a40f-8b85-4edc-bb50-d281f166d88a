#!/usr/bin/env python3
"""
调试Bug检测数据库问题
"""

import os
import sqlite3
import sys


def main():
    try:
        # 导入Bug检测数据库管理器
        from src.bug_detection.core.database_manager import DatabaseManager

        # 创建数据库管理器实例
        db_manager = DatabaseManager()
        
        print(f"📁 Bug检测数据库路径: {db_manager.db_path}")
        print(f"📁 文件是否存在: {os.path.exists(db_manager.db_path)}")
        
        if os.path.exists(db_manager.db_path):
            # 连接数据库
            conn = sqlite3.connect(db_manager.db_path)
            cursor = conn.cursor()
            
            # 查看所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            print(f"\n📋 数据库中的表:")
            for table in tables:
                print(f"  - {table[0]}")
            
            # 检查performance_metrics表
            if ('performance_metrics',) in tables:
                print(f"\n✅ performance_metrics表存在")
                
                # 查看表结构
                cursor.execute('PRAGMA table_info(performance_metrics)')
                columns = cursor.fetchall()
                print('\n📋 performance_metrics表结构:')
                for col in columns:
                    print(f'  - {col[1]} ({col[2]})')
                
                # 查看数据
                cursor.execute('SELECT COUNT(*) FROM performance_metrics')
                count = cursor.fetchone()[0]
                print(f'\n📊 performance_metrics记录数: {count}')

                if count > 0:
                    cursor.execute('SELECT endpoint, response_time, status_code, timestamp FROM performance_metrics ORDER BY timestamp DESC LIMIT 10')
                    records = cursor.fetchall()
                    print('\n📈 最新10条记录:')
                    for record in records:
                        print(f'  - {record[0]} | {record[1]}s | {record[2]} | {record[3]}')
                
            else:
                print(f"\n❌ performance_metrics表不存在")
                print("🔧 尝试手动创建表...")
                
                # 手动创建表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS performance_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        endpoint TEXT,
                        response_time REAL,
                        status_code INTEGER,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                conn.commit()
                print("✅ performance_metrics表创建成功")

            # 检查JavaScript错误表
            print("\n" + "="*50)
            print("🔍 检查JavaScript错误表")
            if ('js_errors',) in tables:
                print("✅ js_errors表存在")

                # 检查表结构
                cursor.execute("PRAGMA table_info(js_errors)")
                columns = cursor.fetchall()
                print(f"\n🔧 js_errors表结构:")
                for col in columns:
                    print(f"  - {col[1]} ({col[2]})")

                # 检查记录数
                cursor.execute("SELECT COUNT(*) FROM js_errors")
                count = cursor.fetchone()[0]
                print(f"\n📋 js_errors记录数: {count}")

                if count > 0:
                    # 显示最新记录
                    cursor.execute("SELECT * FROM js_errors ORDER BY timestamp DESC LIMIT 5")
                    records = cursor.fetchall()
                    print(f"\n📋 最新{min(5, len(records))}条JavaScript错误:")
                    for record in records:
                        session_id = record[1] if len(record) > 1 else 'unknown'
                        error_message = record[2] if len(record) > 2 else 'unknown'
                        page_url = record[3] if len(record) > 3 else 'unknown'
                        timestamp = record[4] if len(record) > 4 else 'unknown'
                        print(f"  - {error_message[:50]}... | {page_url} | {timestamp}")
                else:
                    print("📋 暂无JavaScript错误记录")
            else:
                print("❌ js_errors表不存在")

            conn.close()
        else:
            print(f"\n❌ 数据库文件不存在: {db_manager.db_path}")
            
    except Exception as e:
        print(f'❌ 调试Bug检测数据库时出错: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
