#!/usr/bin/env python3
"""
快速数据库修复
"""

import sqlite3
import os
from datetime import datetime

def fix_database():
    """修复数据库"""
    print("🚀 开始数据库修复...")
    
    # 确保数据目录存在
    os.makedirs("data", exist_ok=True)
    db_path = "data/bug_detection.db"
    
    # 备份数据库
    if os.path.exists(db_path):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"{db_path}.fix_backup_{timestamp}"
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"💾 数据库已备份到: {backup_path}")
    
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 检查当前表结构
    cursor.execute("PRAGMA table_info(bug_reports)")
    columns = cursor.fetchall()
    column_names = [col[1] for col in columns]
    print(f"📊 当前列数: {len(columns)}")
    
    # 需要添加的列
    columns_to_add = [
        ("environment", "TEXT DEFAULT 'production'"),
        ("category", "TEXT DEFAULT 'general'"),
        ("priority", "TEXT DEFAULT 'medium'"),
        ("tags", "TEXT"),
        ("source", "TEXT DEFAULT 'user'"),
        ("component_name", "TEXT"),
        ("reproduction_steps", "TEXT"),
        ("system_context", "TEXT"),
        ("user_journey", "TEXT"),
        ("screenshots", "TEXT")
    ]
    
    added_count = 0
    for column_name, column_def in columns_to_add:
        if column_name not in column_names:
            try:
                sql = f"ALTER TABLE bug_reports ADD COLUMN {column_name} {column_def}"
                cursor.execute(sql)
                print(f"✅ 添加列: {column_name}")
                added_count += 1
            except Exception as e:
                print(f"⚠️ 添加列失败 {column_name}: {e}")
        else:
            print(f"⏭️ 列已存在: {column_name}")
    
    conn.commit()
    
    # 验证结果
    cursor.execute("PRAGMA table_info(bug_reports)")
    final_columns = cursor.fetchall()
    print(f"📈 最终列数: {len(final_columns)}")
    print(f"🆕 新增列数: {added_count}")
    
    # 测试插入数据
    try:
        test_id = f"test_fix_{datetime.now().strftime('%H%M%S')}"
        cursor.execute("""
            INSERT INTO bug_reports 
            (id, error_type, severity, environment, category, priority, tags, source)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (test_id, 'test_error', 'low', 'test', 'fix_validation', 'low', 'test,fix', 'fix_script'))
        
        # 查询验证
        cursor.execute("SELECT environment, category, priority, tags, source FROM bug_reports WHERE id = ?", (test_id,))
        result = cursor.fetchone()
        
        if result:
            print("✅ 数据操作测试成功")
            print(f"   测试数据: {result}")
            
            # 清理测试数据
            cursor.execute("DELETE FROM bug_reports WHERE id = ?", (test_id,))
            conn.commit()
            print("🧹 测试数据已清理")
        else:
            print("❌ 数据操作测试失败")
            
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
    
    conn.close()
    print("🎉 数据库修复完成！")
    return True

if __name__ == "__main__":
    fix_database()
