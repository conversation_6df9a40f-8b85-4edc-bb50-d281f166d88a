"""
销售额影响因子建模模块
分析销售额对开奖号码的影响和预测价值
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
from collections import defaultdict, Counter
import sqlite3
import os
from datetime import datetime, timedelta

class SalesImpactAnalyzer:
    """销售额影响因子分析器"""
    
    def __init__(self, db_path: str = None):
        """
        初始化分析器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path or os.path.join('data', 'lottery.db')
        self.analysis_results = {}
        
    def load_sales_data(self) -> List[Dict[str, Any]]:
        """
        从数据库加载销售额数据
        
        Returns:
            包含销售额和开奖信息的记录列表
        """
        if not os.path.exists(self.db_path):
            raise FileNotFoundError(f"数据库文件不存在: {self.db_path}")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 查询销售额和开奖数据
        cursor.execute("""
            SELECT period, date, numbers, trial_numbers, 
                   sales_amount, direct_prize, group3_prize, group6_prize,
                   draw_machine, trial_machine
            FROM lottery_records 
            WHERE sales_amount IS NOT NULL 
            AND sales_amount > 0
            ORDER BY date ASC
        """)
        
        records = []
        for row in cursor.fetchall():
            record = {
                'period': row[0],
                'date': row[1],
                'numbers': row[2],
                'trial_numbers': row[3],
                'sales_amount': row[4],
                'direct_prize': row[5],
                'group3_prize': row[6],
                'group6_prize': row[7],
                'draw_machine': row[8],
                'trial_machine': row[9]
            }
            records.append(record)
        
        conn.close()
        return records
    
    def analyze_sales_distribution(self, records: List[Dict]) -> Dict[str, Any]:
        """
        分析销售额分布特征
        
        Args:
            records: 销售额记录列表
            
        Returns:
            销售额分布分析结果
        """
        sales_amounts = [record['sales_amount'] for record in records]
        
        if not sales_amounts:
            return {}
        
        # 基础统计
        sales_stats = {
            'count': len(sales_amounts),
            'mean': np.mean(sales_amounts),
            'median': np.median(sales_amounts),
            'std': np.std(sales_amounts),
            'min': min(sales_amounts),
            'max': max(sales_amounts),
            'q25': np.percentile(sales_amounts, 25),
            'q75': np.percentile(sales_amounts, 75)
        }
        
        # 销售额分级
        sales_levels = []
        for amount in sales_amounts:
            if amount < sales_stats['q25']:
                sales_levels.append('low')
            elif amount < sales_stats['q75']:
                sales_levels.append('medium')
            else:
                sales_levels.append('high')
        
        level_distribution = Counter(sales_levels)
        
        # 时间趋势分析
        time_trends = self._analyze_sales_time_trends(records)
        
        return {
            'basic_stats': sales_stats,
            'level_distribution': dict(level_distribution),
            'time_trends': time_trends
        }
    
    def _analyze_sales_time_trends(self, records: List[Dict]) -> Dict[str, Any]:
        """
        分析销售额的时间趋势
        
        Args:
            records: 销售额记录列表
            
        Returns:
            时间趋势分析结果
        """
        # 按年份分组
        yearly_sales = defaultdict(list)
        monthly_sales = defaultdict(list)
        weekday_sales = defaultdict(list)
        
        for record in records:
            try:
                date_obj = datetime.strptime(record['date'], '%Y-%m-%d')
                year = date_obj.year
                month = date_obj.month
                weekday = date_obj.weekday()  # 0=Monday, 6=Sunday
                
                yearly_sales[year].append(record['sales_amount'])
                monthly_sales[month].append(record['sales_amount'])
                weekday_sales[weekday].append(record['sales_amount'])
                
            except (ValueError, TypeError):
                continue
        
        # 计算趋势统计
        trends = {}
        
        # 年度趋势
        if yearly_sales:
            trends['yearly'] = {
                year: {
                    'count': len(amounts),
                    'mean': np.mean(amounts),
                    'total': sum(amounts)
                }
                for year, amounts in yearly_sales.items()
            }
        
        # 月度趋势
        if monthly_sales:
            trends['monthly'] = {
                month: {
                    'count': len(amounts),
                    'mean': np.mean(amounts),
                    'total': sum(amounts)
                }
                for month, amounts in monthly_sales.items()
            }
        
        # 星期趋势
        if weekday_sales:
            weekday_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 
                           'Friday', 'Saturday', 'Sunday']
            trends['weekday'] = {
                weekday_names[day]: {
                    'count': len(amounts),
                    'mean': np.mean(amounts),
                    'total': sum(amounts)
                }
                for day, amounts in weekday_sales.items()
            }
        
        return trends
    
    def analyze_sales_number_correlation(self, records: List[Dict]) -> Dict[str, Any]:
        """
        分析销售额与开奖号码的关联性
        
        Args:
            records: 销售额记录列表
            
        Returns:
            销售额与号码关联性分析结果
        """
        # 按销售额分级分析号码特征
        sales_amounts = [r['sales_amount'] for r in records]
        q25, q75 = np.percentile(sales_amounts, [25, 75])
        
        level_analysis = {
            'low': {'numbers': [], 'features': []},
            'medium': {'numbers': [], 'features': []},
            'high': {'numbers': [], 'features': []}
        }
        
        for record in records:
            amount = record['sales_amount']
            numbers = record['numbers']
            
            if len(numbers) == 3:
                # 确定销售额级别
                if amount < q25:
                    level = 'low'
                elif amount < q75:
                    level = 'medium'
                else:
                    level = 'high'
                
                # 计算号码特征
                digits = [int(d) for d in numbers]
                features = {
                    'sum': sum(digits),
                    'span': max(digits) - min(digits),
                    'unique_count': len(set(digits)),
                    'odd_count': sum(1 for d in digits if d % 2 == 1),
                    'big_count': sum(1 for d in digits if d >= 5),
                    'max_digit': max(digits),
                    'min_digit': min(digits)
                }
                
                level_analysis[level]['numbers'].append(numbers)
                level_analysis[level]['features'].append(features)
        
        # 统计各级别的号码特征分布
        correlation_results = {}
        for level, data in level_analysis.items():
            if data['features']:
                features_df = pd.DataFrame(data['features'])
                
                correlation_results[level] = {
                    'count': len(data['numbers']),
                    'feature_stats': {
                        col: {
                            'mean': features_df[col].mean(),
                            'std': features_df[col].std(),
                            'distribution': Counter(features_df[col]).most_common(10)
                        }
                        for col in features_df.columns
                    },
                    'most_common_numbers': Counter(data['numbers']).most_common(10)
                }
        
        # 计算特征差异显著性
        feature_significance = self._calculate_feature_significance(level_analysis)
        
        return {
            'level_analysis': correlation_results,
            'feature_significance': feature_significance,
            'sales_thresholds': {'q25': q25, 'q75': q75}
        }
    
    def _calculate_feature_significance(self, level_analysis: Dict) -> Dict[str, float]:
        """
        计算不同销售额级别间特征差异的显著性
        
        Args:
            level_analysis: 分级分析数据
            
        Returns:
            特征显著性分数
        """
        significance = {}
        
        # 提取各级别的特征数据
        level_features = {}
        for level, data in level_analysis.items():
            if data['features']:
                level_features[level] = pd.DataFrame(data['features'])
        
        if len(level_features) >= 2:
            # 计算特征在不同级别间的方差比
            feature_names = list(level_features.values())[0].columns
            
            for feature in feature_names:
                level_means = []
                level_vars = []
                
                for level, df in level_features.items():
                    if feature in df.columns and len(df) > 1:
                        level_means.append(df[feature].mean())
                        level_vars.append(df[feature].var())
                
                if len(level_means) >= 2 and any(v > 0 for v in level_vars):
                    # 简单的方差比作为显著性指标
                    between_var = np.var(level_means)
                    within_var = np.mean(level_vars)
                    
                    if within_var > 0:
                        significance[feature] = between_var / within_var
                    else:
                        significance[feature] = 0
        
        return significance
    
    def analyze_sales_prediction_value(self, records: List[Dict]) -> Dict[str, Any]:
        """
        分析销售额的预测价值
        
        Args:
            records: 销售额记录列表
            
        Returns:
            销售额预测价值分析结果
        """
        # 构建销售额预测特征
        prediction_features = []
        
        for i in range(len(records) - 1):
            current = records[i]
            next_record = records[i + 1]
            
            if (current['sales_amount'] and next_record['sales_amount'] and 
                len(current['numbers']) == 3 and len(next_record['numbers']) == 3):
                
                # 当前期特征
                current_digits = [int(d) for d in current['numbers']]
                current_features = {
                    'sales_amount': current['sales_amount'],
                    'sum': sum(current_digits),
                    'span': max(current_digits) - min(current_digits),
                    'unique_count': len(set(current_digits))
                }
                
                # 下期目标
                next_digits = [int(d) for d in next_record['numbers']]
                next_features = {
                    'next_sum': sum(next_digits),
                    'next_span': max(next_digits) - min(next_digits),
                    'next_unique_count': len(set(next_digits)),
                    'next_numbers': next_record['numbers']
                }
                
                prediction_features.append({**current_features, **next_features})
        
        if not prediction_features:
            return {}
        
        # 分析销售额对下期号码的预测能力
        df = pd.DataFrame(prediction_features)
        
        # 按销售额分组分析下期号码特征
        sales_quartiles = df['sales_amount'].quantile([0.25, 0.5, 0.75])
        
        prediction_analysis = {}
        for i, (q_name, threshold) in enumerate([
            ('Q1', sales_quartiles[0.25]),
            ('Q2', sales_quartiles[0.5]),
            ('Q3', sales_quartiles[0.75])
        ]):
            if i == 0:
                mask = df['sales_amount'] <= threshold
            elif i == 1:
                mask = (df['sales_amount'] > sales_quartiles[0.25]) & (df['sales_amount'] <= threshold)
            else:
                mask = df['sales_amount'] > sales_quartiles[0.5]
            
            subset = df[mask]
            if len(subset) > 0:
                prediction_analysis[q_name] = {
                    'count': len(subset),
                    'sales_range': (subset['sales_amount'].min(), subset['sales_amount'].max()),
                    'next_sum_stats': {
                        'mean': subset['next_sum'].mean(),
                        'std': subset['next_sum'].std(),
                        'distribution': Counter(subset['next_sum']).most_common(5)
                    },
                    'next_span_stats': {
                        'mean': subset['next_span'].mean(),
                        'std': subset['next_span'].std(),
                        'distribution': Counter(subset['next_span']).most_common(5)
                    },
                    'most_common_next_numbers': Counter(subset['next_numbers']).most_common(10)
                }
        
        # 计算预测准确性
        prediction_accuracy = self._calculate_prediction_accuracy(df)
        
        return {
            'prediction_analysis': prediction_analysis,
            'prediction_accuracy': prediction_accuracy,
            'sample_count': len(prediction_features),
            'sales_quartiles': dict(sales_quartiles)
        }
    
    def _calculate_prediction_accuracy(self, df: pd.DataFrame) -> Dict[str, float]:
        """
        计算基于销售额的预测准确性
        
        Args:
            df: 预测特征数据框
            
        Returns:
            预测准确性指标
        """
        accuracy = {}
        
        # 基于销售额级别预测和值的准确性
        sales_median = df['sales_amount'].median()
        
        # 高销售额组
        high_sales = df[df['sales_amount'] > sales_median]
        if len(high_sales) > 0:
            high_sum_mean = high_sales['next_sum'].mean()
            
            # 计算预测准确性（使用均值±1标准差作为预测区间）
            high_sum_std = high_sales['next_sum'].std()
            if high_sum_std > 0:
                prediction_range = (high_sum_mean - high_sum_std, high_sum_mean + high_sum_std)
                hits = sum(1 for s in high_sales['next_sum'] 
                          if prediction_range[0] <= s <= prediction_range[1])
                accuracy['high_sales_sum_accuracy'] = hits / len(high_sales)
        
        # 低销售额组
        low_sales = df[df['sales_amount'] <= sales_median]
        if len(low_sales) > 0:
            low_sum_mean = low_sales['next_sum'].mean()
            low_sum_std = low_sales['next_sum'].std()
            if low_sum_std > 0:
                prediction_range = (low_sum_mean - low_sum_std, low_sum_mean + low_sum_std)
                hits = sum(1 for s in low_sales['next_sum'] 
                          if prediction_range[0] <= s <= prediction_range[1])
                accuracy['low_sales_sum_accuracy'] = hits / len(low_sales)
        
        return accuracy
    
    def extract_sales_features(self, records: List[Dict]) -> Dict[str, List[float]]:
        """
        提取销售额相关特征用于机器学习
        
        Args:
            records: 销售额记录列表
            
        Returns:
            销售额特征字典
        """
        features = {
            'sales_amount_normalized': [],
            'sales_level': [],
            'sales_trend': [],
            'sales_volatility': [],
            'sales_rank_percentile': []
        }
        
        if len(records) < 2:
            return features
        
        sales_amounts = [r['sales_amount'] for r in records]
        
        # 归一化销售额
        min_sales, max_sales = min(sales_amounts), max(sales_amounts)
        if max_sales > min_sales:
            normalized = [(s - min_sales) / (max_sales - min_sales) for s in sales_amounts]
        else:
            normalized = [0.5] * len(sales_amounts)
        
        # 销售额级别（0=低，1=中，2=高）
        q25, q75 = np.percentile(sales_amounts, [25, 75])
        levels = []
        for amount in sales_amounts:
            if amount < q25:
                levels.append(0)
            elif amount < q75:
                levels.append(1)
            else:
                levels.append(2)
        
        # 销售额趋势（相对于前期的变化）
        trends = [0]  # 第一期无趋势
        for i in range(1, len(sales_amounts)):
            if sales_amounts[i-1] > 0:
                trend = (sales_amounts[i] - sales_amounts[i-1]) / sales_amounts[i-1]
                trends.append(trend)
            else:
                trends.append(0)
        
        # 销售额波动性（最近5期的标准差）
        volatilities = []
        for i in range(len(sales_amounts)):
            start_idx = max(0, i - 4)
            window_sales = sales_amounts[start_idx:i+1]
            if len(window_sales) > 1:
                volatilities.append(np.std(window_sales) / np.mean(window_sales))
            else:
                volatilities.append(0)
        
        # 销售额排名百分位
        sorted_sales = sorted(sales_amounts)
        percentiles = []
        for amount in sales_amounts:
            rank = sorted_sales.index(amount)
            percentile = rank / (len(sorted_sales) - 1) if len(sorted_sales) > 1 else 0.5
            percentiles.append(percentile)
        
        features['sales_amount_normalized'] = normalized
        features['sales_level'] = levels
        features['sales_trend'] = trends
        features['sales_volatility'] = volatilities
        features['sales_rank_percentile'] = percentiles
        
        return features
    
    def train_model(self) -> Dict[str, Any]:
        """
        训练销售额影响因子模型
        
        Returns:
            训练结果和模型性能
        """
        print("开始训练销售额影响因子模型...")
        
        # 加载数据
        records = self.load_sales_data()
        print(f"加载了 {len(records)} 条销售额记录")
        
        if len(records) < 10:
            raise ValueError("销售额数据不足，无法训练模型")
        
        # 执行各种分析
        print("分析销售额分布...")
        distribution_analysis = self.analyze_sales_distribution(records)
        
        print("分析销售额与号码关联性...")
        correlation_analysis = self.analyze_sales_number_correlation(records)
        
        print("分析销售额预测价值...")
        prediction_analysis = self.analyze_sales_prediction_value(records)
        
        print("提取销售额特征...")
        sales_features = self.extract_sales_features(records)
        
        # 保存分析结果
        self.analysis_results = {
            'distribution_analysis': distribution_analysis,
            'correlation_analysis': correlation_analysis,
            'prediction_analysis': prediction_analysis,
            'sales_features': sales_features,
            'data_summary': {
                'total_records': len(records),
                'date_range': (records[0]['date'], records[-1]['date']) if records else None,
                'sales_range': (min(r['sales_amount'] for r in records), 
                              max(r['sales_amount'] for r in records))
            }
        }
        
        print("销售额影响因子模型训练完成!")
        return {
            'success': True,
            'analysis_results': self.analysis_results
        }


if __name__ == "__main__":
    # 测试代码
    analyzer = SalesImpactAnalyzer()
    
    try:
        # 训练模型
        result = analyzer.train_model()
        print("训练结果:", result['success'])
        
        if result['success']:
            data_summary = result['analysis_results']['data_summary']
            print("数据摘要:", data_summary)
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
