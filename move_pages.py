import shutil
import os

# 移动特征工程深度页面
source = "src/ui/pages_disabled/feature_engineering_deep.py"
dest = "src/ui/pages/feature_engineering_deep.py"

if os.path.exists(source):
    shutil.move(source, dest)
    print(f"✅ 移动成功: {source} -> {dest}")
else:
    print(f"❌ 源文件不存在: {source}")

# 验证移动结果
if os.path.exists(dest):
    print(f"✅ 目标文件存在: {dest}")
else:
    print(f"❌ 目标文件不存在: {dest}")

print("🎉 特征工程深度页面恢复完成!")
