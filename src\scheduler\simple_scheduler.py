"""
简单定时任务调度器

不依赖APScheduler的简单实现，用于基本的定时任务功能
"""

import time
import threading
import json
import logging
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime, timedelta
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root / 'src'))

from data.incremental_updater import IncrementalUpdater

logger = logging.getLogger(__name__)


class SimpleScheduler:
    """简单定时任务调度器"""
    
    def __init__(self, data_dir: str = "data"):
        """
        初始化调度器
        
        Args:
            data_dir: 数据目录
        """
        self.data_dir = Path(data_dir)
        self.log_dir = self.data_dir / "logs"
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化组件
        self.updater = IncrementalUpdater(str(self.data_dir))
        self.running = False
        self.threads = []
        
        # 任务配置
        self.tasks = {
            "data_update": {
                "name": "数据更新任务",
                "func": self.data_update_job,
                "interval": 24 * 3600,  # 24小时
                "last_run": None,
                "enabled": True
            },
            "file_cleanup": {
                "name": "文件清理任务",
                "func": self.cleanup_job,
                "interval": 7 * 24 * 3600,  # 7天
                "last_run": None,
                "enabled": True
            },
            "log_cleanup": {
                "name": "日志清理任务",
                "func": self.log_cleanup_job,
                "interval": 24 * 3600,  # 24小时
                "last_run": None,
                "enabled": True
            }
        }
        
        # 设置日志
        self.setup_logging()
        logger.info("简单定时任务调度器初始化完成")
    
    def setup_logging(self):
        """设置日志系统"""
        # 创建日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 文件日志处理器
        log_file = self.log_dir / f"simple_scheduler_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.INFO)
        
        # 配置日志器
        logger.setLevel(logging.INFO)
        logger.addHandler(file_handler)
        
        logger.info(f"日志系统初始化完成，日志文件: {log_file}")
    
    def data_update_job(self):
        """数据更新任务"""
        logger.info("开始执行定时数据更新任务")
        
        try:
            result = self.updater.perform_incremental_update()
            
            if result["success"]:
                if "update_info" in result:
                    update_info = result["update_info"]
                    logger.info(f"数据更新成功: 新增 {update_info.get('records_added', 0)} 条记录")
                else:
                    logger.info(f"数据更新完成: {result['message']}")
            else:
                logger.error(f"数据更新失败: {result['message']}")
                
        except Exception as e:
            logger.error(f"数据更新任务执行异常: {e}")
    
    def cleanup_job(self):
        """文件清理任务"""
        logger.info("开始执行定时文件清理任务")
        
        try:
            result = self.updater.cleanup_old_files(keep_count=10)
            
            if result["success"]:
                logger.info(f"文件清理成功: 删除原始文件 {result['deleted_raw_files']} 个, "
                          f"处理文件 {result['deleted_processed_files']} 个")
            else:
                logger.error(f"文件清理失败: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            logger.error(f"文件清理任务执行异常: {e}")
    
    def log_cleanup_job(self):
        """日志清理任务"""
        logger.info("开始执行日志清理任务")
        
        try:
            log_files = sorted(
                self.log_dir.glob("simple_scheduler_*.log"),
                key=lambda f: f.stat().st_mtime,
                reverse=True
            )
            
            deleted_count = 0
            for log_file in log_files[30:]:  # 保留最新30个
                log_file.unlink()
                deleted_count += 1
            
            logger.info(f"日志清理完成: 删除 {deleted_count} 个旧日志文件")
            
        except Exception as e:
            logger.error(f"日志清理任务执行异常: {e}")
    
    def should_run_task(self, task_config: Dict[str, Any]) -> bool:
        """
        检查任务是否应该运行
        
        Args:
            task_config: 任务配置
            
        Returns:
            是否应该运行
        """
        if not task_config.get("enabled", True):
            return False
        
        last_run = task_config.get("last_run")
        if last_run is None:
            return True
        
        interval = task_config.get("interval", 3600)
        last_run_time = datetime.fromisoformat(last_run)
        next_run_time = last_run_time + timedelta(seconds=interval)
        
        return datetime.now() >= next_run_time
    
    def run_task(self, task_id: str, task_config: Dict[str, Any]):
        """
        运行单个任务
        
        Args:
            task_id: 任务ID
            task_config: 任务配置
        """
        try:
            logger.info(f"开始执行任务: {task_config['name']}")
            
            # 执行任务
            task_config["func"]()
            
            # 更新最后运行时间
            task_config["last_run"] = datetime.now().isoformat()
            
            logger.info(f"任务执行完成: {task_config['name']}")
            
        except Exception as e:
            logger.error(f"任务执行失败: {task_config['name']}, 错误: {e}")
    
    def scheduler_loop(self):
        """调度器主循环"""
        logger.info("调度器主循环启动")
        
        while self.running:
            try:
                # 检查每个任务
                for task_id, task_config in self.tasks.items():
                    if self.should_run_task(task_config):
                        # 在新线程中运行任务
                        task_thread = threading.Thread(
                            target=self.run_task,
                            args=(task_id, task_config),
                            name=f"Task-{task_id}"
                        )
                        task_thread.start()
                        self.threads.append(task_thread)
                
                # 清理已完成的线程
                self.threads = [t for t in self.threads if t.is_alive()]
                
                # 等待一分钟再检查
                time.sleep(60)
                
            except Exception as e:
                logger.error(f"调度器循环异常: {e}")
                time.sleep(60)
        
        logger.info("调度器主循环结束")
    
    def start(self):
        """启动调度器"""
        if self.running:
            logger.warning("调度器已在运行")
            return
        
        self.running = True
        
        # 启动调度器线程
        scheduler_thread = threading.Thread(
            target=self.scheduler_loop,
            name="SimpleScheduler"
        )
        scheduler_thread.daemon = True
        scheduler_thread.start()
        
        logger.info("简单定时任务调度器启动成功")
    
    def stop(self):
        """停止调度器"""
        if not self.running:
            return
        
        self.running = False
        
        # 等待所有任务线程完成
        for thread in self.threads:
            thread.join(timeout=30)
        
        logger.info("简单定时任务调度器已停止")
    
    def get_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        tasks_status = []
        
        for task_id, task_config in self.tasks.items():
            last_run = task_config.get("last_run")
            next_run = None
            
            if last_run:
                last_run_time = datetime.fromisoformat(last_run)
                interval = task_config.get("interval", 3600)
                next_run_time = last_run_time + timedelta(seconds=interval)
                next_run = next_run_time.isoformat()
            
            tasks_status.append({
                "id": task_id,
                "name": task_config["name"],
                "enabled": task_config.get("enabled", True),
                "last_run": last_run,
                "next_run": next_run,
                "interval": task_config.get("interval", 3600)
            })
        
        return {
            "running": self.running,
            "tasks": tasks_status,
            "active_threads": len([t for t in self.threads if t.is_alive()])
        }
    
    def run_task_now(self, task_id: str) -> bool:
        """立即运行指定任务"""
        if task_id not in self.tasks:
            logger.error(f"任务不存在: {task_id}")
            return False
        
        try:
            task_config = self.tasks[task_id]
            self.run_task(task_id, task_config)
            return True
        except Exception as e:
            logger.error(f"手动执行任务失败: {task_id}, 错误: {e}")
            return False
    
    def enable_task(self, task_id: str):
        """启用任务"""
        if task_id in self.tasks:
            self.tasks[task_id]["enabled"] = True
            logger.info(f"任务已启用: {task_id}")
    
    def disable_task(self, task_id: str):
        """禁用任务"""
        if task_id in self.tasks:
            self.tasks[task_id]["enabled"] = False
            logger.info(f"任务已禁用: {task_id}")
    
    def set_task_interval(self, task_id: str, interval: int):
        """设置任务间隔"""
        if task_id in self.tasks:
            self.tasks[task_id]["interval"] = interval
            logger.info(f"任务间隔已更新: {task_id} = {interval}秒")


# 便捷函数
def start_simple_scheduler(data_dir: str = "data") -> SimpleScheduler:
    """
    启动简单调度器的便捷函数
    
    Args:
        data_dir: 数据目录
        
    Returns:
        调度器实例
    """
    scheduler = SimpleScheduler(data_dir)
    scheduler.start()
    return scheduler


if __name__ == "__main__":
    # 测试代码
    print("测试简单定时任务调度器...")
    
    scheduler = SimpleScheduler()
    
    # 显示状态
    status = scheduler.get_status()
    print(f"调度器状态: {status}")
    
    # 手动执行任务
    print("手动执行数据更新任务...")
    success = scheduler.run_task_now("data_update")
    print(f"执行结果: {'成功' if success else '失败'}")
    
    # 启动调度器
    print("启动调度器...")
    scheduler.start()
    
    # 运行一段时间
    print("调度器运行中，5秒后停止...")
    time.sleep(5)
    
    # 停止调度器
    scheduler.stop()
    print("调度器已停止")
    
    print("简单调度器测试完成！")
