#!/usr/bin/env python3
"""
WebSocket监控脚本
实时监控WebSocket连接状态和性能指标
"""

import time
import json
import requests
import psutil
from datetime import datetime

class WebSocketMonitor:
    def __init__(self):
        self.api_base = "http://127.0.0.1:8888"
    
    def get_websocket_stats(self):
        """获取WebSocket统计信息"""
        try:
            response = requests.get(f"{self.api_base}/api/v1/health/websocket/connections", timeout=5)
            return response.json() if response.status_code == 200 else {}
        except:
            return {}
    
    def get_system_stats(self):
        """获取系统资源统计"""
        return {
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent,
            'timestamp': datetime.now().isoformat()
        }
    
    def generate_report(self):
        """生成监控报告"""
        websocket_stats = self.get_websocket_stats()
        system_stats = self.get_system_stats()
        
        report = {
            'timestamp': time.time(),
            'websocket_stats': websocket_stats,
            'system_stats': system_stats
        }
        
        return report
    
    def save_report(self, report):
        """保存监控报告"""
        filename = f"websocket_monitor_{int(time.time())}.json"
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        return filename

def main():
    monitor = WebSocketMonitor()
    report = monitor.generate_report()
    filename = monitor.save_report(report)
    
    print(f"监控报告已保存: {filename}")
    print(json.dumps(report, indent=2))

if __name__ == "__main__":
    main()
