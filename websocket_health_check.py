#!/usr/bin/env python3
"""
WebSocket健康检查脚本
用于监控WebSocket服务健康状态
"""

import json
import logging
import time
import requests
from typing import Dict, Any

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebSocketHealthChecker:
    """WebSocket健康检查器"""
    
    def __init__(self, api_base: str = "http://127.0.0.1:8888"):
        self.api_base = api_base
    
    def check_api_health(self) -> Dict[str, Any]:
        """检查API服务健康状态"""
        try:
            response = requests.get(f"{self.api_base}/health", timeout=5)
            return {
                'status': 'healthy' if response.status_code == 200 else 'unhealthy',
                'data': response.json() if response.status_code == 200 else None,
                'error': None
            }
        except Exception as e:
            return {'status': 'error', 'data': None, 'error': str(e)}
    
    def check_websocket_health(self) -> Dict[str, Any]:
        """检查WebSocket服务健康状态"""
        try:
            response = requests.get(f"{self.api_base}/api/v1/health/websocket", timeout=5)
            return {
                'status': 'healthy' if response.status_code == 200 else 'unhealthy',
                'data': response.json() if response.status_code == 200 else None,
                'error': None
            }
        except Exception as e:
            return {'status': 'error', 'data': None, 'error': str(e)}
    
    def generate_health_report(self) -> Dict[str, Any]:
        """生成健康检查报告"""
        api_health = self.check_api_health()
        websocket_health = self.check_websocket_health()
        
        overall_status = 'healthy'
        if api_health['status'] != 'healthy' or websocket_health['status'] != 'healthy':
            overall_status = 'unhealthy'
        
        return {
            'timestamp': time.time(),
            'overall_status': overall_status,
            'api_health': api_health,
            'websocket_health': websocket_health
        }

def main():
    """主函数"""
    checker = WebSocketHealthChecker()
    report = checker.generate_health_report()
    
    print(json.dumps(report, indent=2))
    
    # 保存报告
    with open(f'health_report_{int(time.time())}.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    return 0 if report['overall_status'] == 'healthy' else 1

if __name__ == "__main__":
    exit(main())
