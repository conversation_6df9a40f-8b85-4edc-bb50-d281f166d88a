"""
福彩3D数据质量检查器

专门负责数据完整性、准确性验证和异常数据检测
"""

from typing import List, Dict, Any, Optional, Tuple, Set
from datetime import datetime, date, timedelta
from collections import Counter
import logging

from .models import LotteryRecord, DataQualityReport

logger = logging.getLogger(__name__)


class QualityChecker:
    """数据质量检查器"""
    
    def __init__(self):
        """初始化质量检查器"""
        self.check_results = {}
        self.anomalies = []
        
    def check_data_completeness(self, records: List[LotteryRecord]) -> Dict[str, Any]:
        """
        检查数据完整性
        
        Args:
            records: 记录列表
            
        Returns:
            完整性检查结果
        """
        if not records:
            return {"status": "empty", "message": "没有数据记录"}
        
        # 按期号排序
        sorted_records = sorted(records, key=lambda x: x.period)
        
        # 检查期号连续性
        periods = [int(r.period) for r in sorted_records]
        missing_periods = []
        
        for i in range(len(periods) - 1):
            current = periods[i]
            next_period = periods[i + 1]
            
            # 检查期号间隔
            if next_period - current > 1:
                for missing in range(current + 1, next_period):
                    missing_periods.append(str(missing).zfill(7))
        
        # 检查日期连续性
        dates = [r.date for r in sorted_records]
        date_gaps = []
        
        for i in range(len(dates) - 1):
            current_date = dates[i]
            next_date = dates[i + 1]
            gap_days = (next_date - current_date).days
            
            # 福彩3D通常每天开奖，超过3天的间隔可能是异常
            if gap_days > 3:
                date_gaps.append({
                    "from": current_date.isoformat(),
                    "to": next_date.isoformat(),
                    "gap_days": gap_days,
                    "period_from": sorted_records[i].period,
                    "period_to": sorted_records[i + 1].period
                })
        
        # 检查重复记录
        period_counts = Counter(r.period for r in records)
        duplicates = {period: count for period, count in period_counts.items() if count > 1}
        
        result = {
            "status": "checked",
            "total_records": len(records),
            "period_range": {
                "start": sorted_records[0].period,
                "end": sorted_records[-1].period,
                "expected_count": periods[-1] - periods[0] + 1,
                "actual_count": len(periods)
            },
            "date_range": {
                "start": min(dates).isoformat(),
                "end": max(dates).isoformat(),
                "span_days": (max(dates) - min(dates)).days + 1
            },
            "missing_periods": {
                "count": len(missing_periods),
                "list": missing_periods[:20]  # 只显示前20个
            },
            "date_gaps": {
                "count": len(date_gaps),
                "list": date_gaps[:10]  # 只显示前10个
            },
            "duplicates": {
                "count": len(duplicates),
                "list": dict(list(duplicates.items())[:10])  # 只显示前10个
            },
            "completeness_score": self._calculate_completeness_score(
                len(periods), periods[-1] - periods[0] + 1, len(duplicates)
            )
        }
        
        self.check_results["completeness"] = result
        return result
    
    def check_data_accuracy(self, records: List[LotteryRecord]) -> Dict[str, Any]:
        """
        检查数据准确性
        
        Args:
            records: 记录列表
            
        Returns:
            准确性检查结果
        """
        accuracy_issues = []
        
        for i, record in enumerate(records):
            issues = []
            
            # 检查期号格式
            if not record.period.isdigit() or len(record.period) != 7:
                issues.append(f"期号格式错误: {record.period}")
            
            # 检查号码格式
            if not record.numbers.isdigit() or len(record.numbers) != 3:
                issues.append(f"号码格式错误: {record.numbers}")
            
            # 检查号码范围
            for j, digit in enumerate(record.numbers):
                if not (0 <= int(digit) <= 9):
                    issues.append(f"号码第{j+1}位超出范围: {digit}")
            
            # 检查日期合理性
            if record.date < date(2000, 1, 1) or record.date > date.today():
                issues.append(f"日期不合理: {record.date}")
            
            # 检查期号与日期的一致性
            try:
                year_from_period = int(record.period[:4])
                year_from_date = record.date.year
                if abs(year_from_period - year_from_date) > 1:
                    issues.append(f"期号年份({year_from_period})与日期年份({year_from_date})不一致")
            except ValueError:
                issues.append(f"期号年份解析失败: {record.period}")
            
            if issues:
                accuracy_issues.append({
                    "record_index": i,
                    "period": record.period,
                    "date": record.date.isoformat(),
                    "numbers": record.numbers,
                    "issues": issues
                })
        
        result = {
            "status": "checked",
            "total_records": len(records),
            "accuracy_issues": {
                "count": len(accuracy_issues),
                "list": accuracy_issues[:20]  # 只显示前20个
            },
            "accuracy_score": self._calculate_accuracy_score(len(records), len(accuracy_issues))
        }
        
        self.check_results["accuracy"] = result
        return result
    
    def detect_anomalies(self, records: List[LotteryRecord]) -> Dict[str, Any]:
        """
        检测异常数据
        
        Args:
            records: 记录列表
            
        Returns:
            异常检测结果
        """
        anomalies = []
        
        # 统计分析
        sum_values = [r.sum_value for r in records]
        span_values = [r.span_value for r in records]
        
        # 计算统计指标
        sum_mean = sum(sum_values) / len(sum_values)
        sum_std = (sum((x - sum_mean) ** 2 for x in sum_values) / len(sum_values)) ** 0.5
        
        span_mean = sum(span_values) / len(span_values)
        span_std = (sum((x - span_mean) ** 2 for x in span_values) / len(span_values)) ** 0.5
        
        # 检测异常值
        for i, record in enumerate(records):
            anomaly_flags = []
            
            # 和值异常检测（3倍标准差）
            if abs(record.sum_value - sum_mean) > 3 * sum_std:
                anomaly_flags.append(f"和值异常: {record.sum_value} (均值: {sum_mean:.1f})")
            
            # 跨度异常检测
            if abs(record.span_value - span_mean) > 3 * span_std:
                anomaly_flags.append(f"跨度异常: {record.span_value} (均值: {span_mean:.1f})")
            
            # 检测连号异常（如111, 222等）
            if len(set(record.numbers)) == 1:
                anomaly_flags.append(f"三连号: {record.numbers}")
            
            # 检测顺子异常（如123, 456等）
            nums = sorted([int(d) for d in record.numbers])
            if nums[1] - nums[0] == 1 and nums[2] - nums[1] == 1:
                anomaly_flags.append(f"顺子号码: {record.numbers}")
            
            # 检测极值异常
            if record.sum_value == 0:
                anomaly_flags.append("和值为0")
            elif record.sum_value == 27:
                anomaly_flags.append("和值为27")
            
            if record.span_value == 0:
                anomaly_flags.append("跨度为0")
            elif record.span_value == 9:
                anomaly_flags.append("跨度为9")
            
            if anomaly_flags:
                anomalies.append({
                    "record_index": i,
                    "period": record.period,
                    "date": record.date.isoformat(),
                    "numbers": record.numbers,
                    "sum_value": record.sum_value,
                    "span_value": record.span_value,
                    "anomaly_flags": anomaly_flags
                })
        
        # 检测频率异常
        frequency_anomalies = self._detect_frequency_anomalies(records)
        
        result = {
            "status": "checked",
            "total_records": len(records),
            "statistical_info": {
                "sum_value": {
                    "mean": round(sum_mean, 2),
                    "std": round(sum_std, 2),
                    "min": min(sum_values),
                    "max": max(sum_values)
                },
                "span_value": {
                    "mean": round(span_mean, 2),
                    "std": round(span_std, 2),
                    "min": min(span_values),
                    "max": max(span_values)
                }
            },
            "anomalies": {
                "count": len(anomalies),
                "list": anomalies[:20]  # 只显示前20个
            },
            "frequency_anomalies": frequency_anomalies,
            "anomaly_score": self._calculate_anomaly_score(len(records), len(anomalies))
        }
        
        self.check_results["anomalies"] = result
        self.anomalies = anomalies
        return result
    
    def _detect_frequency_anomalies(self, records: List[LotteryRecord]) -> Dict[str, Any]:
        """检测频率异常"""
        # 统计各数字出现频率
        digit_counts = Counter()
        for record in records:
            for digit in record.numbers:
                digit_counts[digit] += 1
        
        total_digits = len(records) * 3
        expected_freq = total_digits / 10  # 每个数字的期望频率
        
        frequency_anomalies = []
        for digit, count in digit_counts.items():
            deviation = abs(count - expected_freq) / expected_freq
            if deviation > 0.2:  # 偏差超过20%
                frequency_anomalies.append({
                    "digit": digit,
                    "count": count,
                    "expected": round(expected_freq, 1),
                    "deviation": round(deviation * 100, 1)
                })
        
        return {
            "total_digits": total_digits,
            "expected_frequency": round(expected_freq, 1),
            "anomalies": frequency_anomalies
        }
    
    def _calculate_completeness_score(self, actual_count: int, expected_count: int, duplicate_count: int) -> float:
        """计算完整性评分"""
        if expected_count == 0:
            return 0.0
        
        base_score = (actual_count / expected_count) * 100
        duplicate_penalty = (duplicate_count / actual_count) * 10 if actual_count > 0 else 0
        
        return max(0, min(100, base_score - duplicate_penalty))
    
    def _calculate_accuracy_score(self, total_records: int, issue_count: int) -> float:
        """计算准确性评分"""
        if total_records == 0:
            return 0.0
        
        return max(0, (1 - issue_count / total_records) * 100)
    
    def _calculate_anomaly_score(self, total_records: int, anomaly_count: int) -> float:
        """计算异常检测评分（异常越少评分越高）"""
        if total_records == 0:
            return 0.0
        
        return max(0, (1 - anomaly_count / total_records) * 100)
    
    def generate_quality_report(self, records: List[LotteryRecord]) -> Dict[str, Any]:
        """
        生成综合质量报告
        
        Args:
            records: 记录列表
            
        Returns:
            综合质量报告
        """
        # 执行所有检查
        completeness = self.check_data_completeness(records)
        accuracy = self.check_data_accuracy(records)
        anomalies = self.detect_anomalies(records)
        
        # 计算综合评分
        overall_score = (
            completeness.get("completeness_score", 0) * 0.4 +
            accuracy.get("accuracy_score", 0) * 0.4 +
            anomalies.get("anomaly_score", 0) * 0.2
        )
        
        return {
            "summary": {
                "total_records": len(records),
                "overall_score": round(overall_score, 2),
                "check_time": datetime.now().isoformat()
            },
            "completeness": completeness,
            "accuracy": accuracy,
            "anomalies": anomalies,
            "recommendations": self._generate_recommendations(completeness, accuracy, anomalies)
        }
    
    def _generate_recommendations(self, completeness: Dict, accuracy: Dict, anomalies: Dict) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if completeness.get("completeness_score", 100) < 95:
            recommendations.append("建议检查数据采集流程，确保数据完整性")
        
        if accuracy.get("accuracy_score", 100) < 98:
            recommendations.append("建议加强数据验证规则，提高数据准确性")
        
        if anomalies.get("anomaly_score", 100) < 90:
            recommendations.append("发现较多异常数据，建议人工审核异常记录")
        
        if len(completeness.get("missing_periods", {}).get("list", [])) > 0:
            recommendations.append("存在缺失期号，建议补充缺失数据")
        
        if len(accuracy.get("accuracy_issues", {}).get("list", [])) > 0:
            recommendations.append("存在格式错误，建议修正数据格式")
        
        if not recommendations:
            recommendations.append("数据质量良好，建议保持当前数据管理流程")
        
        return recommendations


# 便捷函数
def check_data_quality(records: List[LotteryRecord]) -> Dict[str, Any]:
    """
    便捷的数据质量检查函数
    
    Args:
        records: 记录列表
        
    Returns:
        质量检查报告
    """
    checker = QualityChecker()
    return checker.generate_quality_report(records)


if __name__ == "__main__":
    # 测试代码
    from datetime import date
    
    # 创建测试数据
    test_records = [
        LotteryRecord("2024001", date(2024, 1, 1), "123"),
        LotteryRecord("2024002", date(2024, 1, 2), "456"),
        LotteryRecord("2024003", date(2024, 1, 3), "789"),
        LotteryRecord("2024005", date(2024, 1, 5), "000"),  # 缺失2024004，异常号码
        LotteryRecord("2024006", date(2024, 1, 6), "999"),  # 异常号码
        LotteryRecord("2024002", date(2024, 1, 2), "456"),  # 重复记录
    ]
    
    print("测试数据质量检查器...")
    checker = QualityChecker()
    
    # 生成质量报告
    report = checker.generate_quality_report(test_records)
    
    print(f"\n质量报告摘要:")
    print(f"  总记录数: {report['summary']['total_records']}")
    print(f"  综合评分: {report['summary']['overall_score']}")
    
    print(f"\n完整性检查:")
    print(f"  缺失期号: {report['completeness']['missing_periods']['count']}")
    print(f"  重复记录: {report['completeness']['duplicates']['count']}")
    print(f"  完整性评分: {report['completeness']['completeness_score']}")
    
    print(f"\n准确性检查:")
    print(f"  准确性问题: {report['accuracy']['accuracy_issues']['count']}")
    print(f"  准确性评分: {report['accuracy']['accuracy_score']}")
    
    print(f"\n异常检测:")
    print(f"  异常记录: {report['anomalies']['anomalies']['count']}")
    print(f"  异常评分: {report['anomalies']['anomaly_score']}")
    
    print(f"\n改进建议:")
    for i, rec in enumerate(report['recommendations'], 1):
        print(f"  {i}. {rec}")
    
    print("\n质量检查器测试完成！")
