#!/usr/bin/env python3
"""
测试训练按钮功能
"""

import os
import sys

sys.path.append('src')

def test_model_training():
    """测试模型训练功能"""
    try:
        # 测试智能融合模型训练
        from prediction.intelligent_fusion import IntelligentFusionSystem
        
        print("🚀 开始测试智能融合模型训练...")
        intelligent_system = IntelligentFusionSystem()
        
        print("📊 检查当前模型状态...")
        print(f"模型已训练: {intelligent_system.models_trained}")
        print(f"融合就绪: {intelligent_system.fusion_ready}")
        
        if hasattr(intelligent_system, 'training_data_count'):
            print(f"训练数据量: {intelligent_system.training_data_count}")
        
        # 执行训练
        print("\n🔧 开始训练模型...")
        training_result = intelligent_system.train_all_models(force_retrain=True)
        
        print("\n📊 训练结果:")
        print(f"成功: {training_result.get('success', False)}")
        print(f"融合就绪: {training_result.get('fusion_ready', False)}")
        print(f"成功模型数: {training_result.get('successful_models', 0)}")
        print(f"总模型数: {training_result.get('total_models', 0)}")
        
        if training_result.get('success'):
            print("✅ 模型训练成功！")
            
            # 检查训练后状态
            print("\n📈 训练后状态:")
            print(f"模型已训练: {intelligent_system.models_trained}")
            print(f"融合就绪: {intelligent_system.fusion_ready}")
            print(f"训练数据量: {intelligent_system.training_data_count}")
        else:
            print("❌ 模型训练失败")
            print(f"错误信息: {training_result}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_model_status():
    """测试模型状态检查"""
    try:
        from model_library.model_registry import ModelRegistry
        from model_library.status_manager import ModelStatusManager

        print("\n🔍 测试模型状态检查...")
        registry = ModelRegistry()
        status_manager = ModelStatusManager()
        
        # 获取所有模型
        models = registry.list_models(active_only=False)
        print(f"已注册模型数: {len(models)}")

        for model_info in models:
            print(f"\n📋 模型: {model_info.name} ({model_info.model_id})")
            try:
                status_info = status_manager.get_model_status(model_info.model_id)
                if status_info:
                    print(f"  运行状态: {status_info.status.value}")
                    print(f"  数据就绪: {'✅' if status_info.data_ready else '❌'}")
                    print(f"  特征就绪: {'✅' if status_info.features_ready else '❌'}")
                    print(f"  训练完成: {'✅' if status_info.trained else '❌'}")
                    if status_info.error_message:
                        print(f"  错误信息: {status_info.error_message}")
                else:
                    print("  状态信息未找到")
            except Exception as e:
                print(f"  状态检查失败: {e}")
                
    except Exception as e:
        print(f"❌ 状态检查测试失败: {e}")

if __name__ == "__main__":
    print("🧪 开始测试训练按钮功能")
    print("=" * 50)
    
    test_model_status()
    print("\n" + "=" * 50)
    test_model_training()
    
    print("\n🎉 测试完成！")
