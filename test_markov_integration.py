"""
马尔可夫模型优化集成测试

测试修改后的系统与IntelligentFusionSystem的兼容性
"""

import os
import sys
import time

import numpy as np

# 添加项目路径
sys.path.append('src')

from prediction.intelligent_fusion import IntelligentFusionSystem
from prediction.pattern_prediction import PatternPredictor


def test_pattern_predictor_integration():
    """测试PatternPredictor的集成"""
    print("🔧 测试PatternPredictor集成...")
    
    try:
        # 创建PatternPredictor实例
        predictor = PatternPredictor(
            transition_window_size=1000,
            probability_window_size=500,
            smoothing_alpha=1.0
        )
        
        print(f"✅ PatternPredictor创建成功")
        print(f"   - 转移矩阵窗口: {predictor.transition_window_size}")
        print(f"   - 概率计算窗口: {predictor.probability_window_size}")
        print(f"   - 平滑参数: {predictor.smoothing_alpha}")
        
        return True
        
    except Exception as e:
        print(f"❌ PatternPredictor集成失败: {e}")
        return False


def test_intelligent_fusion_system():
    """测试IntelligentFusionSystem"""
    print("\n🧠 测试IntelligentFusionSystem...")
    
    try:
        # 创建IntelligentFusionSystem实例
        fusion_system = IntelligentFusionSystem()
        
        print("✅ IntelligentFusionSystem创建成功")
        
        # 测试模型训练
        print("🔄 开始模型训练...")
        start_time = time.time()
        
        result = fusion_system.train_all_models(force_retrain=True)
        
        training_time = time.time() - start_time
        print(f"⏱️ 训练耗时: {training_time:.2f}秒")
        
        if result.get('success'):
            print("✅ 模型训练成功")
            return True
        else:
            print(f"❌ 模型训练失败: {result}")
            return False
            
    except Exception as e:
        print(f"❌ IntelligentFusionSystem测试失败: {e}")
        return False


def test_prediction_diversity():
    """测试预测多样性"""
    print("\n📊 测试预测多样性...")
    
    try:
        fusion_system = IntelligentFusionSystem()
        
        # 生成多次预测
        predictions = []
        num_predictions = 10
        
        print(f"🎯 生成{num_predictions}次预测...")
        
        for i in range(num_predictions):
            try:
                result = fusion_system.generate_fusion_prediction(
                    data=[],  # 使用自动加载的历史数据
                    max_candidates=5,
                    confidence_threshold=0.1
                )
                
                if 'error' not in result:
                    predicted_number = result.get('numbers', '')
                    if predicted_number:
                        predictions.append(predicted_number)
                        print(f"   预测{i+1}: {predicted_number}")
                    else:
                        print(f"   预测{i+1}: 无有效预测")
                else:
                    print(f"   预测{i+1}: 预测失败 - {result.get('error', '未知错误')}")
                    
            except Exception as e:
                print(f"   预测{i+1}: 异常 - {e}")
        
        if len(predictions) == 0:
            print("❌ 没有生成任何有效预测")
            return False
        
        # 计算多样性指标
        unique_predictions = set(predictions)
        diversity_ratio = len(unique_predictions) / len(predictions)
        
        print(f"\n📈 多样性分析:")
        print(f"   - 总预测数: {len(predictions)}")
        print(f"   - 唯一预测数: {len(unique_predictions)}")
        print(f"   - 多样性比率: {diversity_ratio:.3f}")
        
        # 计算辛普森多样性指数
        if len(predictions) > 0:
            from collections import Counter
            counts = Counter(predictions)
            total = len(predictions)
            simpson_index = 1 - sum((count/total)**2 for count in counts.values())
            
            print(f"   - 辛普森多样性指数: {simpson_index:.3f}")
            
            # 验证多样性目标（应该≥0.9）
            if simpson_index >= 0.9:
                print("✅ 多样性目标达成 (≥0.9)")
                return True
            elif simpson_index >= 0.7:
                print("⚠️ 多样性良好但未达到目标 (0.7-0.9)")
                return True
            else:
                print("❌ 多样性不足 (<0.7)")
                return False
        else:
            print("❌ 无法计算多样性指数")
            return False
            
    except Exception as e:
        print(f"❌ 多样性测试失败: {e}")
        return False


def test_performance_impact():
    """测试性能影响"""
    print("\n⚡ 测试性能影响...")
    
    try:
        fusion_system = IntelligentFusionSystem()
        
        # 测试预测性能
        start_time = time.time()
        
        result = fusion_system.generate_fusion_prediction(
            data=[],  # 使用自动加载的历史数据
            max_candidates=10,
            confidence_threshold=0.1
        )
        
        prediction_time = time.time() - start_time
        
        print(f"⏱️ 预测耗时: {prediction_time:.3f}秒")
        
        # 性能标准：预测时间应该<5秒
        if prediction_time < 5.0:
            print("✅ 性能符合要求 (<5秒)")
            return True
        elif prediction_time < 10.0:
            print("⚠️ 性能可接受但较慢 (5-10秒)")
            return True
        else:
            print("❌ 性能不符合要求 (>10秒)")
            return False
            
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 马尔可夫模型优化集成测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("PatternPredictor集成", test_pattern_predictor_integration()))
    test_results.append(("IntelligentFusionSystem", test_intelligent_fusion_system()))
    test_results.append(("预测多样性", test_prediction_diversity()))
    test_results.append(("性能影响", test_performance_impact()))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总:")
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed_tests += 1
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！马尔可夫模型优化集成成功！")
        return True
    elif passed_tests >= total_tests * 0.75:
        print("⚠️ 大部分测试通过，系统基本可用")
        return True
    else:
        print("❌ 多项测试失败，需要进一步调试")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
