"""
自适应数据质量评估引擎
基于模型特性的数据质量评估标准，包含序列完整性、数据量充足性评估
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')


@dataclass
class DataQualityMetrics:
    """数据质量指标"""
    completeness: float  # 完整性
    consistency: float   # 一致性
    accuracy: float      # 准确性
    timeliness: float    # 时效性
    validity: float      # 有效性
    overall_score: float # 综合评分


@dataclass
class ModelCharacteristics:
    """模型特性配置"""
    data_dependency: str      # 数据依赖类型: "sequential", "volume", "quality"
    noise_tolerance: float    # 噪声容忍度 0-1
    min_pattern_length: int   # 最小模式长度
    optimal_data_ratio: float # 最优数据比例
    sequence_importance: float # 序列重要性权重
    volume_importance: float   # 数据量重要性权重


class AdaptiveDataQualityEngine:
    """自适应数据质量评估引擎"""
    
    def __init__(self):
        self.model_characteristics = self._initialize_model_characteristics()
        self.quality_thresholds = self._initialize_quality_thresholds()
        self.evaluation_cache = {}
        
    def _initialize_model_characteristics(self) -> Dict[str, ModelCharacteristics]:
        """初始化模型特性配置"""
        return {
            "markov_enhanced": ModelCharacteristics(
                data_dependency="sequential",
                noise_tolerance=0.3,
                min_pattern_length=50,
                optimal_data_ratio=0.8,
                sequence_importance=0.8,
                volume_importance=0.6
            ),
            "deep_learning_cnn_lstm": ModelCharacteristics(
                data_dependency="volume",
                noise_tolerance=0.1,
                min_pattern_length=200,
                optimal_data_ratio=0.9,
                sequence_importance=0.9,
                volume_importance=0.9
            ),
            "trend_analyzer": ModelCharacteristics(
                data_dependency="quality",
                noise_tolerance=0.2,
                min_pattern_length=100,
                optimal_data_ratio=0.85,
                sequence_importance=0.7,
                volume_importance=0.7
            ),
            "intelligent_fusion": ModelCharacteristics(
                data_dependency="balanced",
                noise_tolerance=0.25,
                min_pattern_length=80,
                optimal_data_ratio=0.85,
                sequence_importance=0.75,
                volume_importance=0.8
            )
        }
    
    def _initialize_quality_thresholds(self) -> Dict[str, Dict[str, float]]:
        """初始化质量阈值"""
        return {
            "excellent": {"min_score": 0.9, "color": "green"},
            "good": {"min_score": 0.75, "color": "blue"},
            "fair": {"min_score": 0.6, "color": "orange"},
            "poor": {"min_score": 0.0, "color": "red"}
        }
    
    def calculate_adaptive_quality_score(self, model_id: str, 
                                       data_range: Tuple[int, int],
                                       lottery_data: pd.DataFrame) -> DataQualityMetrics:
        """
        基于模型特性计算自适应质量评分
        
        Args:
            model_id: 模型ID
            data_range: 数据范围 (start_period, end_period)
            lottery_data: 福彩3D历史数据
            
        Returns:
            数据质量指标对象
        """
        # 获取模型特性
        characteristics = self.model_characteristics.get(model_id)
        if not characteristics:
            characteristics = self.model_characteristics["intelligent_fusion"]
        
        # 提取指定范围的数据
        filtered_data = self._filter_data_by_range(lottery_data, data_range)
        
        if filtered_data.empty:
            return DataQualityMetrics(0, 0, 0, 0, 0, 0)
        
        # 计算各项质量指标
        completeness = self._evaluate_completeness(filtered_data, characteristics)
        consistency = self._evaluate_consistency(filtered_data, characteristics)
        accuracy = self._evaluate_accuracy(filtered_data, characteristics)
        timeliness = self._evaluate_timeliness(filtered_data, characteristics)
        validity = self._evaluate_validity(filtered_data, characteristics)
        
        # 计算综合评分
        overall_score = self._calculate_overall_score(
            completeness, consistency, accuracy, timeliness, validity, characteristics
        )
        
        return DataQualityMetrics(
            completeness=completeness,
            consistency=consistency,
            accuracy=accuracy,
            timeliness=timeliness,
            validity=validity,
            overall_score=overall_score
        )
    
    def _filter_data_by_range(self, data: pd.DataFrame, 
                            data_range: Tuple[int, int]) -> pd.DataFrame:
        """根据期号范围过滤数据"""
        try:
            start_period, end_period = data_range
            
            if 'period' in data.columns:
                # 假设期号列名为 'period'
                filtered = data[
                    (data['period'] >= start_period) & 
                    (data['period'] <= end_period)
                ].copy()
            else:
                # 如果没有期号列，使用索引
                filtered = data.iloc[start_period:end_period+1].copy()
            
            return filtered
            
        except Exception as e:
            print(f"数据过滤失败: {e}")
            return pd.DataFrame()
    
    def _evaluate_completeness(self, data: pd.DataFrame, 
                             characteristics: ModelCharacteristics) -> float:
        """评估数据完整性"""
        try:
            if data.empty:
                return 0.0
            
            # 计算缺失值比例
            total_cells = data.size
            missing_cells = data.isnull().sum().sum()
            completeness_ratio = 1 - (missing_cells / total_cells)
            
            # 根据模型特性调整权重
            if characteristics.data_dependency == "sequential":
                # 序列模型对连续性要求更高
                sequence_completeness = self._evaluate_sequence_completeness(data)
                completeness_ratio = (completeness_ratio + sequence_completeness) / 2
            
            return min(completeness_ratio, 1.0)
            
        except Exception as e:
            print(f"完整性评估失败: {e}")
            return 0.0
    
    def _evaluate_sequence_completeness(self, data: pd.DataFrame) -> float:
        """评估序列完整性"""
        try:
            if 'period' not in data.columns or len(data) < 2:
                return 1.0
            
            # 检查期号连续性
            periods = sorted(data['period'].dropna().unique())
            if len(periods) < 2:
                return 1.0
            
            expected_count = periods[-1] - periods[0] + 1
            actual_count = len(periods)
            
            return actual_count / expected_count
            
        except Exception:
            return 1.0
    
    def _evaluate_consistency(self, data: pd.DataFrame, 
                            characteristics: ModelCharacteristics) -> float:
        """评估数据一致性"""
        try:
            if data.empty:
                return 0.0
            
            consistency_scores = []
            
            # 1. 数值范围一致性
            if 'winning_numbers' in data.columns:
                consistency_scores.append(self._check_number_range_consistency(data))
            
            # 2. 格式一致性
            consistency_scores.append(self._check_format_consistency(data))
            
            # 3. 逻辑一致性
            consistency_scores.append(self._check_logical_consistency(data))
            
            return np.mean(consistency_scores) if consistency_scores else 0.0
            
        except Exception as e:
            print(f"一致性评估失败: {e}")
            return 0.0
    
    def _check_number_range_consistency(self, data: pd.DataFrame) -> float:
        """检查号码范围一致性"""
        try:
            # 假设福彩3D号码应该在0-9范围内
            valid_count = 0
            total_count = 0
            
            for _, row in data.iterrows():
                if pd.notna(row.get('winning_numbers')):
                    numbers = str(row['winning_numbers'])
                    if len(numbers) == 3:
                        for digit in numbers:
                            total_count += 1
                            if digit.isdigit() and 0 <= int(digit) <= 9:
                                valid_count += 1
            
            return valid_count / total_count if total_count > 0 else 1.0
            
        except Exception:
            return 1.0
    
    def _check_format_consistency(self, data: pd.DataFrame) -> float:
        """检查格式一致性"""
        try:
            format_scores = []
            
            # 检查各列的格式一致性
            for column in data.columns:
                if data[column].dtype == 'object':
                    # 字符串列的长度一致性
                    lengths = data[column].dropna().astype(str).str.len()
                    if len(lengths) > 0:
                        length_consistency = 1 - (lengths.std() / (lengths.mean() + 1e-8))
                        format_scores.append(max(length_consistency, 0))
            
            return np.mean(format_scores) if format_scores else 1.0
            
        except Exception:
            return 1.0
    
    def _check_logical_consistency(self, data: pd.DataFrame) -> float:
        """检查逻辑一致性"""
        try:
            # 检查日期逻辑一致性
            if 'date' in data.columns:
                dates = pd.to_datetime(data['date'], errors='coerce')
                valid_dates = dates.dropna()
                
                if len(valid_dates) > 1:
                    # 检查日期是否递增
                    is_increasing = valid_dates.is_monotonic_increasing
                    return 1.0 if is_increasing else 0.8
            
            return 1.0
            
        except Exception:
            return 1.0
    
    def _evaluate_accuracy(self, data: pd.DataFrame, 
                         characteristics: ModelCharacteristics) -> float:
        """评估数据准确性"""
        try:
            if data.empty:
                return 0.0
            
            accuracy_scores = []
            
            # 1. 数值准确性检查
            accuracy_scores.append(self._check_numerical_accuracy(data))
            
            # 2. 业务规则准确性
            accuracy_scores.append(self._check_business_rule_accuracy(data))
            
            # 3. 统计异常检测
            accuracy_scores.append(self._detect_statistical_anomalies(data))
            
            return np.mean(accuracy_scores)
            
        except Exception as e:
            print(f"准确性评估失败: {e}")
            return 0.0
    
    def _check_numerical_accuracy(self, data: pd.DataFrame) -> float:
        """检查数值准确性"""
        try:
            # 检查数值列的合理性
            numerical_columns = data.select_dtypes(include=[np.number]).columns
            
            if len(numerical_columns) == 0:
                return 1.0
            
            accuracy_scores = []
            
            for col in numerical_columns:
                values = data[col].dropna()
                if len(values) > 0:
                    # 检查是否有异常值
                    q1, q3 = values.quantile([0.25, 0.75])
                    iqr = q3 - q1
                    lower_bound = q1 - 1.5 * iqr
                    upper_bound = q3 + 1.5 * iqr
                    
                    outliers = values[(values < lower_bound) | (values > upper_bound)]
                    accuracy = 1 - (len(outliers) / len(values))
                    accuracy_scores.append(accuracy)
            
            return np.mean(accuracy_scores) if accuracy_scores else 1.0
            
        except Exception:
            return 1.0
    
    def _check_business_rule_accuracy(self, data: pd.DataFrame) -> float:
        """检查业务规则准确性"""
        try:
            # 福彩3D特定的业务规则检查
            if 'winning_numbers' not in data.columns:
                return 1.0
            
            valid_count = 0
            total_count = 0
            
            for _, row in data.iterrows():
                if pd.notna(row['winning_numbers']):
                    total_count += 1
                    numbers = str(row['winning_numbers'])
                    
                    # 检查是否为3位数字
                    if len(numbers) == 3 and numbers.isdigit():
                        valid_count += 1
            
            return valid_count / total_count if total_count > 0 else 1.0
            
        except Exception:
            return 1.0
    
    def _detect_statistical_anomalies(self, data: pd.DataFrame) -> float:
        """检测统计异常"""
        try:
            if 'winning_numbers' not in data.columns:
                return 1.0
            
            # 分析号码分布的合理性
            all_digits = []
            for _, row in data.iterrows():
                if pd.notna(row['winning_numbers']):
                    numbers = str(row['winning_numbers'])
                    if len(numbers) == 3 and numbers.isdigit():
                        all_digits.extend([int(d) for d in numbers])
            
            if not all_digits:
                return 1.0
            
            # 检查数字分布是否合理（应该相对均匀）
            digit_counts = np.bincount(all_digits, minlength=10)
            expected_freq = len(all_digits) / 10
            
            # 计算卡方统计量
            chi_square = np.sum((digit_counts - expected_freq) ** 2 / expected_freq)
            
            # 转换为0-1分数（较小的卡方值表示更均匀的分布）
            anomaly_score = 1 / (1 + chi_square / 100)
            
            return anomaly_score
            
        except Exception:
            return 1.0
    
    def _evaluate_timeliness(self, data: pd.DataFrame, 
                           characteristics: ModelCharacteristics) -> float:
        """评估数据时效性"""
        try:
            if data.empty or 'date' not in data.columns:
                return 1.0
            
            # 转换日期列
            dates = pd.to_datetime(data['date'], errors='coerce')
            valid_dates = dates.dropna()
            
            if len(valid_dates) == 0:
                return 0.0
            
            # 计算数据的新鲜度
            latest_date = valid_dates.max()
            current_date = datetime.now()
            
            days_old = (current_date - latest_date).days
            
            # 时效性评分：越新的数据分数越高
            if days_old <= 1:
                return 1.0
            elif days_old <= 7:
                return 0.9
            elif days_old <= 30:
                return 0.7
            elif days_old <= 90:
                return 0.5
            else:
                return 0.3
                
        except Exception as e:
            print(f"时效性评估失败: {e}")
            return 1.0
    
    def _evaluate_validity(self, data: pd.DataFrame, 
                         characteristics: ModelCharacteristics) -> float:
        """评估数据有效性"""
        try:
            if data.empty:
                return 0.0
            
            validity_scores = []
            
            # 1. 数据类型有效性
            validity_scores.append(self._check_data_type_validity(data))
            
            # 2. 值域有效性
            validity_scores.append(self._check_value_range_validity(data))
            
            # 3. 关联性有效性
            validity_scores.append(self._check_relationship_validity(data))
            
            return np.mean(validity_scores)
            
        except Exception as e:
            print(f"有效性评估失败: {e}")
            return 0.0
    
    def _check_data_type_validity(self, data: pd.DataFrame) -> float:
        """检查数据类型有效性"""
        try:
            valid_columns = 0
            total_columns = len(data.columns)
            
            for column in data.columns:
                # 检查列是否包含预期的数据类型
                non_null_values = data[column].dropna()
                
                if len(non_null_values) > 0:
                    # 简单的类型一致性检查
                    first_type = type(non_null_values.iloc[0])
                    consistent_types = sum(1 for val in non_null_values if type(val) == first_type)
                    
                    if consistent_types / len(non_null_values) >= 0.9:
                        valid_columns += 1
            
            return valid_columns / total_columns if total_columns > 0 else 1.0
            
        except Exception:
            return 1.0
    
    def _check_value_range_validity(self, data: pd.DataFrame) -> float:
        """检查值域有效性"""
        try:
            # 福彩3D特定的值域检查
            if 'winning_numbers' not in data.columns:
                return 1.0
            
            valid_count = 0
            total_count = 0
            
            for _, row in data.iterrows():
                if pd.notna(row['winning_numbers']):
                    total_count += 1
                    numbers = str(row['winning_numbers'])
                    
                    # 检查每个数字是否在0-9范围内
                    if len(numbers) == 3 and all(c.isdigit() and 0 <= int(c) <= 9 for c in numbers):
                        valid_count += 1
            
            return valid_count / total_count if total_count > 0 else 1.0
            
        except Exception:
            return 1.0
    
    def _check_relationship_validity(self, data: pd.DataFrame) -> float:
        """检查关联性有效性"""
        try:
            # 检查相关字段之间的逻辑关系
            validity_score = 1.0
            
            # 如果有期号和日期，检查它们的对应关系
            if 'period' in data.columns and 'date' in data.columns:
                # 期号应该与日期有合理的对应关系
                periods = data['period'].dropna()
                dates = pd.to_datetime(data['date'], errors='coerce').dropna()
                
                if len(periods) > 1 and len(dates) > 1:
                    # 检查期号和日期是否都是递增的
                    period_increasing = periods.is_monotonic_increasing
                    date_increasing = dates.is_monotonic_increasing
                    
                    if period_increasing and date_increasing:
                        validity_score = 1.0
                    else:
                        validity_score = 0.8
            
            return validity_score
            
        except Exception:
            return 1.0
    
    def _calculate_overall_score(self, completeness: float, consistency: float,
                               accuracy: float, timeliness: float, validity: float,
                               characteristics: ModelCharacteristics) -> float:
        """计算综合质量评分"""
        try:
            # 根据模型特性调整权重
            if characteristics.data_dependency == "sequential":
                weights = {
                    'completeness': 0.25,
                    'consistency': 0.25,
                    'accuracy': 0.2,
                    'timeliness': 0.15,
                    'validity': 0.15
                }
            elif characteristics.data_dependency == "volume":
                weights = {
                    'completeness': 0.3,
                    'consistency': 0.2,
                    'accuracy': 0.25,
                    'timeliness': 0.1,
                    'validity': 0.15
                }
            elif characteristics.data_dependency == "quality":
                weights = {
                    'completeness': 0.15,
                    'consistency': 0.25,
                    'accuracy': 0.35,
                    'timeliness': 0.1,
                    'validity': 0.15
                }
            else:  # balanced
                weights = {
                    'completeness': 0.2,
                    'consistency': 0.2,
                    'accuracy': 0.25,
                    'timeliness': 0.15,
                    'validity': 0.2
                }
            
            overall_score = (
                completeness * weights['completeness'] +
                consistency * weights['consistency'] +
                accuracy * weights['accuracy'] +
                timeliness * weights['timeliness'] +
                validity * weights['validity']
            )
            
            return min(overall_score, 1.0)
            
        except Exception as e:
            print(f"综合评分计算失败: {e}")
            return 0.0
    
    def recommend_optimal_data_configuration(self, model_id: str,
                                           available_data: pd.DataFrame) -> Dict[str, Any]:
        """推荐最优数据配置"""
        try:
            characteristics = self.model_characteristics.get(model_id)
            if not characteristics:
                characteristics = self.model_characteristics["intelligent_fusion"]
            
            total_records = len(available_data)
            
            # 根据模型特性推荐数据量
            if characteristics.data_dependency == "volume":
                recommended_size = max(int(total_records * 0.9), characteristics.min_pattern_length)
            elif characteristics.data_dependency == "sequential":
                recommended_size = max(int(total_records * 0.8), characteristics.min_pattern_length)
            else:
                recommended_size = max(int(total_records * characteristics.optimal_data_ratio), 
                                     characteristics.min_pattern_length)
            
            # 推荐数据范围
            if total_records > recommended_size:
                start_idx = total_records - recommended_size
                recommended_range = (start_idx, total_records - 1)
            else:
                recommended_range = (0, total_records - 1)
            
            # 评估推荐配置的质量
            quality_metrics = self.calculate_adaptive_quality_score(
                model_id, recommended_range, available_data
            )
            
            return {
                "recommended_size": recommended_size,
                "recommended_range": recommended_range,
                "expected_quality": quality_metrics.overall_score,
                "quality_breakdown": {
                    "completeness": quality_metrics.completeness,
                    "consistency": quality_metrics.consistency,
                    "accuracy": quality_metrics.accuracy,
                    "timeliness": quality_metrics.timeliness,
                    "validity": quality_metrics.validity
                },
                "confidence": min(quality_metrics.overall_score * 1.1, 1.0),
                "reasoning": self._generate_recommendation_reasoning(characteristics, quality_metrics)
            }
            
        except Exception as e:
            print(f"推荐配置生成失败: {e}")
            return {
                "recommended_size": 1000,
                "recommended_range": (0, 999),
                "expected_quality": 0.5,
                "confidence": 0.5,
                "reasoning": "使用默认配置"
            }
    
    def _generate_recommendation_reasoning(self, characteristics: ModelCharacteristics,
                                         quality_metrics: DataQualityMetrics) -> str:
        """生成推荐理由"""
        reasoning_parts = []
        
        if characteristics.data_dependency == "sequential":
            reasoning_parts.append("该模型依赖序列数据，优先保证数据连续性")
        elif characteristics.data_dependency == "volume":
            reasoning_parts.append("该模型需要大量数据，推荐使用更多历史记录")
        elif characteristics.data_dependency == "quality":
            reasoning_parts.append("该模型对数据质量敏感，优先选择高质量数据")
        
        if quality_metrics.overall_score >= 0.9:
            reasoning_parts.append("当前数据质量优秀")
        elif quality_metrics.overall_score >= 0.75:
            reasoning_parts.append("当前数据质量良好")
        elif quality_metrics.overall_score >= 0.6:
            reasoning_parts.append("当前数据质量一般，建议优化")
        else:
            reasoning_parts.append("当前数据质量较差，需要清理")
        
        return "；".join(reasoning_parts)
    
    def get_quality_level(self, score: float) -> str:
        """获取质量等级"""
        for level, threshold in self.quality_thresholds.items():
            if score >= threshold["min_score"]:
                return level
        return "poor"


def test_adaptive_quality_engine():
    """测试自适应数据质量评估引擎"""
    print("🧪 测试自适应数据质量评估引擎...")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'period': range(2024001, 2024101),
        'date': pd.date_range('2024-01-01', periods=100),
        'winning_numbers': [f"{np.random.randint(0,10)}{np.random.randint(0,10)}{np.random.randint(0,10)}" 
                           for _ in range(100)],
        'sales_amount': np.random.uniform(1000000, 5000000, 100)
    })
    
    # 测试质量评估
    engine = AdaptiveDataQualityEngine()
    
    for model_id in ["markov_enhanced", "deep_learning_cnn_lstm", "trend_analyzer", "intelligent_fusion"]:
        print(f"\n📊 测试模型: {model_id}")
        
        # 评估数据质量
        quality_metrics = engine.calculate_adaptive_quality_score(
            model_id, (0, 99), test_data
        )
        
        print(f"  综合评分: {quality_metrics.overall_score:.3f}")
        print(f"  完整性: {quality_metrics.completeness:.3f}")
        print(f"  一致性: {quality_metrics.consistency:.3f}")
        print(f"  准确性: {quality_metrics.accuracy:.3f}")
        print(f"  时效性: {quality_metrics.timeliness:.3f}")
        print(f"  有效性: {quality_metrics.validity:.3f}")
        
        # 推荐配置
        recommendation = engine.recommend_optimal_data_configuration(model_id, test_data)
        print(f"  推荐数据量: {recommendation['recommended_size']}")
        print(f"  推荐理由: {recommendation['reasoning']}")
    
    print("\n✅ 自适应数据质量评估引擎测试完成！")


if __name__ == "__main__":
    test_adaptive_quality_engine()
