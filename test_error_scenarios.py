#!/usr/bin/env python3
"""
错误处理和异常场景测试

测试各种错误场景的处理机制，包括数据库连接失败、网络中断、WebSocket断开等情况
"""

import asyncio
import json
import logging
import random
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 尝试导入所需库
try:
    import requests
    HAS_REQUESTS = True
except ImportError:
    HAS_REQUESTS = False
    logger.warning("requests库未安装，将跳过HTTP错误测试")

try:
    import websockets
    HAS_WEBSOCKETS = True
except ImportError:
    HAS_WEBSOCKETS = False
    logger.warning("websockets库未安装，将跳过WebSocket错误测试")

class ErrorScenarioTester:
    """错误场景测试器"""
    
    def __init__(self):
        self.api_base_url = "http://127.0.0.1:8000"
        self.websocket_url = "ws://127.0.0.1:8000"
        self.invalid_api_url = "http://127.0.0.1:9999"  # 无效端口
        self.session = None
        
        if HAS_REQUESTS:
            self.session = requests.Session()
            self.session.timeout = 10
    
    async def test_api_error_responses(self) -> Dict[str, Any]:
        """测试API错误响应"""
        logger.info("🚫 测试API错误响应...")
        
        test_result = {
            "test_name": "api_error_responses",
            "tests": [],
            "passed": True
        }
        
        if not HAS_REQUESTS:
            test_result["note"] = "跳过测试 - requests库未安装"
            return test_result
        
        # 测试各种错误场景
        error_scenarios = [
            # (端点, 方法, 数据, 期望状态码, 描述)
            ("/api/v1/nonexistent", "GET", None, [404], "不存在的端点"),
            ("/api/v1/health/invalid", "GET", None, [404], "无效健康检查端点"),
            ("/api/v1/data/invalid", "GET", None, [404], "无效数据端点"),
            ("/api/v1/bug-detection/report-js-error", "POST", "invalid json", [400, 422], "无效JSON数据"),
            ("/api/v1/bug-detection/report-js-error", "POST", {}, [400, 422], "空数据"),
            ("/api/v1/data/by-date-range", "GET", None, [400, 422], "缺少必需参数")
        ]
        
        for endpoint, method, data, expected_codes, description in error_scenarios:
            try:
                url = f"{self.api_base_url}{endpoint}"
                
                if method == "GET":
                    response = self.session.get(url)
                elif method == "POST":
                    if isinstance(data, str):
                        response = self.session.post(url, data=data, headers={'Content-Type': 'application/json'})
                    else:
                        response = self.session.post(url, json=data)
                
                status_ok = response.status_code in expected_codes
                
                test_result["tests"].append({
                    "endpoint": endpoint,
                    "method": method,
                    "description": description,
                    "status_code": response.status_code,
                    "expected_codes": expected_codes,
                    "status_ok": status_ok,
                    "passed": status_ok
                })
                
                if not status_ok:
                    test_result["passed"] = False
                    
            except Exception as e:
                test_result["tests"].append({
                    "endpoint": endpoint,
                    "method": method,
                    "description": description,
                    "error": str(e),
                    "passed": True  # 网络错误也是可接受的
                })
        
        return test_result
    
    async def test_database_error_handling(self) -> Dict[str, Any]:
        """测试数据库错误处理"""
        logger.info("🗄️  测试数据库错误处理...")
        
        test_result = {
            "test_name": "database_error_handling",
            "tests": [],
            "passed": True
        }
        
        if not HAS_REQUESTS:
            test_result["note"] = "跳过测试 - requests库未安装"
            return test_result
        
        # 测试数据库相关的错误场景
        db_error_tests = [
            ("/api/v1/data/by-date-range?start_date=invalid&end_date=invalid", "无效日期格式"),
            ("/api/v1/data/by-date-range?start_date=2025-01-01&end_date=2020-01-01", "开始日期晚于结束日期"),
            ("/api/v1/data/by-date-range?start_date=2025-01-01&end_date=2025-12-31&limit=-1", "无效限制参数"),
            ("/api/v1/data/by-date-range?start_date=2025-01-01&end_date=2025-12-31&limit=999999", "过大限制参数")
        ]
        
        for endpoint, description in db_error_tests:
            try:
                response = self.session.get(f"{self.api_base_url}{endpoint}")
                
                # 期望返回错误状态码或者能够优雅处理
                handled_gracefully = response.status_code in [200, 400, 422, 500]
                
                test_result["tests"].append({
                    "endpoint": endpoint,
                    "description": description,
                    "status_code": response.status_code,
                    "handled_gracefully": handled_gracefully,
                    "passed": handled_gracefully
                })
                
                if not handled_gracefully:
                    test_result["passed"] = False
                    
            except Exception as e:
                test_result["tests"].append({
                    "endpoint": endpoint,
                    "description": description,
                    "error": str(e),
                    "passed": True
                })
        
        return test_result
    
    async def test_websocket_disconnection_handling(self) -> Dict[str, Any]:
        """测试WebSocket断开处理"""
        logger.info("🔌 测试WebSocket断开处理...")
        
        test_result = {
            "test_name": "websocket_disconnection_handling",
            "tests": [],
            "passed": True
        }
        
        if not HAS_WEBSOCKETS:
            test_result["note"] = "跳过测试 - websockets库未安装"
            return test_result
        
        # 测试正常连接然后突然断开
        try:
            uri = f"{self.websocket_url}/ws/bug-detection"
            
            async with websockets.connect(uri, timeout=10) as websocket:
                # 发送一些正常消息
                for i in range(3):
                    ping_msg = {"type": "ping", "sequence": i}
                    await websocket.send(json.dumps(ping_msg))
                    
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=3)
                        test_result["tests"].append({
                            "test_type": "normal_message",
                            "sequence": i,
                            "response_received": True,
                            "passed": True
                        })
                    except asyncio.TimeoutError:
                        test_result["tests"].append({
                            "test_type": "normal_message",
                            "sequence": i,
                            "response_received": False,
                            "passed": False
                        })
                        test_result["passed"] = False
                
                # 测试发送大量消息（可能导致断开）
                try:
                    for i in range(50):
                        large_msg = {"type": "test", "data": "x" * 1000, "sequence": i}
                        await websocket.send(json.dumps(large_msg))
                        await asyncio.sleep(0.01)  # 短暂延迟
                    
                    test_result["tests"].append({
                        "test_type": "message_flood",
                        "messages_sent": 50,
                        "connection_survived": True,
                        "passed": True
                    })
                    
                except Exception as e:
                    test_result["tests"].append({
                        "test_type": "message_flood",
                        "connection_error": str(e),
                        "passed": True  # 断开也是可以接受的
                    })
        
        except Exception as e:
            test_result["tests"].append({
                "test_type": "websocket_connection",
                "error": str(e),
                "passed": True  # 连接失败也是可以接受的
            })
        
        # 测试连接到无效端点
        invalid_endpoints = [
            f"{self.websocket_url}/ws/nonexistent",
            "ws://127.0.0.1:9999/ws/test"
        ]
        
        for endpoint in invalid_endpoints:
            try:
                async with websockets.connect(endpoint, timeout=5) as websocket:
                    test_result["tests"].append({
                        "test_type": "invalid_endpoint_connection",
                        "endpoint": endpoint,
                        "unexpected_success": True,
                        "passed": False
                    })
                    test_result["passed"] = False
                    
            except Exception as e:
                test_result["tests"].append({
                    "test_type": "invalid_endpoint_connection",
                    "endpoint": endpoint,
                    "expected_failure": True,
                    "error_type": type(e).__name__,
                    "passed": True
                })
        
        return test_result
    
    async def test_system_resource_limits(self) -> Dict[str, Any]:
        """测试系统资源限制"""
        logger.info("💾 测试系统资源限制...")
        
        test_result = {
            "test_name": "system_resource_limits",
            "tests": [],
            "passed": True
        }
        
        # 测试文件系统限制
        try:
            cache_dir = Path("data/cache")
            cache_dir.mkdir(parents=True, exist_ok=True)
            
            # 尝试创建大文件
            large_file = cache_dir / "large_test_file.tmp"
            try:
                # 创建10MB的文件
                with open(large_file, 'w') as f:
                    for i in range(100000):
                        f.write("x" * 100 + "\n")
                
                file_size = large_file.stat().st_size
                test_result["tests"].append({
                    "test_type": "large_file_creation",
                    "file_size_bytes": file_size,
                    "creation_successful": True,
                    "passed": True
                })
                
                # 清理大文件
                large_file.unlink()
                
            except Exception as e:
                test_result["tests"].append({
                    "test_type": "large_file_creation",
                    "error": str(e),
                    "passed": True  # 失败也是可以接受的
                })
        
        except Exception as e:
            test_result["tests"].append({
                "test_type": "filesystem_access",
                "error": str(e),
                "passed": False
            })
            test_result["passed"] = False
        
        # 测试内存使用
        try:
            # 创建大量小对象
            large_list = []
            for i in range(100000):
                large_list.append({"id": i, "data": "x" * 100})
            
            memory_test_passed = len(large_list) == 100000
            
            test_result["tests"].append({
                "test_type": "memory_allocation",
                "objects_created": len(large_list),
                "allocation_successful": memory_test_passed,
                "passed": memory_test_passed
            })
            
            if not memory_test_passed:
                test_result["passed"] = False
            
            # 清理内存
            del large_list
            
        except Exception as e:
            test_result["tests"].append({
                "test_type": "memory_allocation",
                "error": str(e),
                "passed": True  # 内存限制也是可以接受的
            })
        
        return test_result
    
    async def test_concurrent_error_scenarios(self) -> Dict[str, Any]:
        """测试并发错误场景"""
        logger.info("⚡ 测试并发错误场景...")
        
        test_result = {
            "test_name": "concurrent_error_scenarios",
            "tests": [],
            "passed": True
        }
        
        if not HAS_REQUESTS:
            test_result["note"] = "跳过测试 - requests库未安装"
            return test_result
        
        # 并发发送错误请求
        async def send_error_request(session, request_id):
            try:
                # 随机选择错误类型
                error_types = [
                    ("invalid_endpoint", f"{self.api_base_url}/api/v1/invalid_{request_id}"),
                    ("malformed_data", f"{self.api_base_url}/api/v1/bug-detection/report-js-error"),
                    ("timeout_test", f"{self.api_base_url}/api/v1/health/detailed")
                ]
                
                error_type, url = random.choice(error_types)
                
                if error_type == "malformed_data":
                    response = await asyncio.to_thread(
                        session.post, url, 
                        data="invalid json data",
                        headers={'Content-Type': 'application/json'},
                        timeout=5
                    )
                else:
                    response = await asyncio.to_thread(session.get, url, timeout=5)
                
                return {
                    "request_id": request_id,
                    "error_type": error_type,
                    "status_code": response.status_code,
                    "handled": response.status_code in [400, 404, 422, 500]
                }
                
            except Exception as e:
                return {
                    "request_id": request_id,
                    "error_type": "exception",
                    "exception": str(e),
                    "handled": True  # 异常也算是处理了
                }
        
        try:
            # 创建并发错误请求
            concurrent_count = 20
            sessions = [requests.Session() for _ in range(concurrent_count)]
            
            tasks = [send_error_request(sessions[i], i) for i in range(concurrent_count)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 分析结果
            handled_requests = sum(1 for r in results if isinstance(r, dict) and r.get("handled", False))
            success_rate = handled_requests / concurrent_count
            
            test_result["tests"].append({
                "test_type": "concurrent_error_requests",
                "total_requests": concurrent_count,
                "handled_requests": handled_requests,
                "success_rate": round(success_rate, 4),
                "passed": success_rate >= 0.8  # 80%处理成功率
            })
            
            if success_rate < 0.8:
                test_result["passed"] = False
            
            # 关闭会话
            for session in sessions:
                session.close()
                
        except Exception as e:
            test_result["tests"].append({
                "test_type": "concurrent_error_requests",
                "error": str(e),
                "passed": False
            })
            test_result["passed"] = False
        
        return test_result
    
    async def run_error_scenario_tests(self) -> Dict[str, Any]:
        """运行所有错误场景测试"""
        logger.info("🚀 开始错误场景测试套件")
        
        test_start_time = time.time()
        
        # 定义测试套件
        test_suite = [
            ("API错误响应", self.test_api_error_responses),
            ("数据库错误处理", self.test_database_error_handling),
            ("WebSocket断开处理", self.test_websocket_disconnection_handling),
            ("系统资源限制", self.test_system_resource_limits),
            ("并发错误场景", self.test_concurrent_error_scenarios)
        ]
        
        results = {}
        overall_passed = True
        
        for test_name, test_func in test_suite:
            try:
                logger.info(f"执行测试: {test_name}")
                result = await test_func()
                results[test_name] = result
                
                if not result.get("passed", False):
                    overall_passed = False
                
                # 测试间隔
                await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(f"测试 '{test_name}' 执行失败: {e}")
                results[test_name] = {
                    "test_name": test_name.lower().replace(" ", "_"),
                    "passed": False,
                    "error": str(e),
                    "tests": []
                }
                overall_passed = False
        
        total_execution_time = int((time.time() - test_start_time) * 1000)
        
        # 生成错误场景测试报告
        report = {
            "timestamp": datetime.now().isoformat(),
            "overall_passed": overall_passed,
            "total_execution_time_ms": total_execution_time,
            "test_results": results,
            "summary": self._generate_error_summary(results)
        }
        
        # 输出测试结果
        self._print_error_results(report)
        
        return report
    
    def _generate_error_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成错误场景测试摘要"""
        total_test_categories = len(results)
        passed_test_categories = sum(1 for r in results.values() if r.get("passed", False))
        
        total_individual_tests = 0
        passed_individual_tests = 0
        
        for result in results.values():
            tests = result.get("tests", [])
            total_individual_tests += len(tests)
            passed_individual_tests += sum(1 for t in tests if t.get("passed", False))
        
        return {
            "total_test_categories": total_test_categories,
            "passed_test_categories": passed_test_categories,
            "total_individual_tests": total_individual_tests,
            "passed_individual_tests": passed_individual_tests,
            "error_handling_robustness": passed_test_categories / total_test_categories if total_test_categories > 0 else 0
        }
    
    def _print_error_results(self, report: Dict[str, Any]):
        """打印错误场景测试结果"""
        logger.info("\n" + "="*60)
        logger.info("🚫 错误场景测试结果")
        logger.info("="*60)
        
        summary = report["summary"]
        logger.info(f"总体状态: {'✅ 通过' if report['overall_passed'] else '❌ 未通过'}")
        logger.info(f"执行时间: {report['total_execution_time_ms']}ms")
        logger.info(f"测试类别: {summary['passed_test_categories']}/{summary['total_test_categories']} 通过")
        logger.info(f"单项测试: {summary['passed_individual_tests']}/{summary['total_individual_tests']} 通过")
        logger.info(f"错误处理健壮性: {summary['error_handling_robustness']:.1%}")
        
        logger.info("\n详细结果:")
        for test_name, result in report["test_results"].items():
            status = "✅" if result.get("passed", False) else "❌"
            test_count = len(result.get("tests", []))
            logger.info(f"{status} {test_name}: {test_count} 个测试")
    
    def close(self):
        """关闭测试器"""
        if self.session:
            self.session.close()

async def main():
    """主函数"""
    tester = ErrorScenarioTester()
    
    try:
        report = await tester.run_error_scenario_tests()
        
        # 保存错误场景测试报告
        report_file = Path("error_scenario_test_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"\n📄 错误场景测试报告已保存到: {report_file}")
        
        # 返回退出码
        if report["overall_passed"]:
            return 0
        else:
            return 1
    finally:
        tester.close()

if __name__ == "__main__":
    if not HAS_REQUESTS:
        logger.warning("⚠️  requests库未安装，大部分错误场景测试将被跳过")
        logger.info("安装命令: pip install requests")
    
    if not HAS_WEBSOCKETS:
        logger.warning("⚠️  websockets库未安装，WebSocket错误测试将被跳过")
        logger.info("安装命令: pip install websockets")
    
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
