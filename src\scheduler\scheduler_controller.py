"""
福彩3D调度器控制接口

提供统一的调度器控制API，用于界面集成
"""

import json
import logging
import os
import subprocess
import threading
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

# 导入错误处理器
try:
    import sys
    sys.path.append('src')
    from utils.error_handler import ErrorType, get_scheduler_error_handler
    ERROR_HANDLER_AVAILABLE = True
except ImportError:
    ERROR_HANDLER_AVAILABLE = False

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SchedulerController:
    """调度器控制器 - 提供统一的调度器管理接口"""
    
    def __init__(self, config_file: str = "scheduler_config.json", data_dir: str = "data"):
        """
        初始化调度器控制器
        
        Args:
            config_file: 配置文件路径
            data_dir: 数据目录
        """
        self.config_file = Path(config_file)
        self.data_dir = Path(data_dir)
        self.scheduler_process = None
        self._lock = threading.Lock()
        
        # 确保配置文件存在
        if not self.config_file.exists():
            self._create_default_config()
    
    def _create_default_config(self):
        """创建默认配置文件"""
        default_config = {
            "update_schedule": {
                "enabled": True,
                "cron": "30 21 * * *",
                "timezone": "Asia/Shanghai",
                "description": "每天21:30执行数据更新"
            },
            "cleanup_schedule": {
                "enabled": True,
                "cron": "0 2 * * 0",
                "keep_files": 10,
                "description": "每周日02:00执行文件清理"
            },
            "monitoring": {
                "enabled": True,
                "max_failures": 3,
                "notification_email": None
            },
            "logging": {
                "level": "INFO",
                "max_log_files": 30,
                "log_rotation": "daily"
            }
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
        
        logger.info(f"创建默认配置文件: {self.config_file}")
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """
        保存配置文件
        
        Args:
            config: 配置字典
            
        Returns:
            是否保存成功
        """
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            logger.info("配置文件保存成功")
            return True
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            return False
    
    def update_schedule_config(self, cron_expression: str, description: Optional[str] = None) -> bool:
        """
        更新调度配置
        
        Args:
            cron_expression: Cron表达式
            description: 描述信息
            
        Returns:
            是否更新成功
        """
        try:
            config = self.load_config()
            config["update_schedule"]["cron"] = cron_expression
            if description:
                config["update_schedule"]["description"] = description
            
            return self.save_config(config)
        except Exception as e:
            logger.error(f"更新调度配置失败: {e}")
            return False
    
    def _check_scheduler_process(self) -> bool:
        """检查是否有调度器进程在运行"""
        try:
            import psutil
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = proc.info['cmdline']
                    if cmdline and len(cmdline) >= 2:
                        if 'python' in cmdline[0] and 'start_scheduler.py' in ' '.join(cmdline):
                            return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            return False
        except ImportError:
            # 如果没有psutil，使用原来的方法
            return False

    def get_scheduler_status(self) -> Dict[str, Any]:
        """
        获取调度器状态

        Returns:
            状态信息字典
        """
        try:
            # 首先检查进程
            process_running = self._check_scheduler_process()

            # 执行状态查询命令 - 增强编码处理
            try:
                result = subprocess.run(
                    ["python", "scripts/start_scheduler.py", "--status"],
                    capture_output=True,
                    text=True,
                    encoding='utf-8',
                    errors='replace',  # 替换无法解码的字符
                    timeout=10,
                    cwd=os.getcwd()
                )
                output = result.stdout or ""
                error_output = result.stderr or ""
            except (UnicodeDecodeError, subprocess.SubprocessError):
                # 如果UTF-8解码失败，尝试使用字节模式
                try:
                    result = subprocess.run(
                        ["python", "scripts/start_scheduler.py", "--status"],
                        capture_output=True,
                        timeout=10,
                        cwd=os.getcwd()
                    )
                    # 手动处理编码
                    output = ""
                    error_output = ""
                    if result.stdout:
                        try:
                            output = result.stdout.decode('utf-8', errors='replace')
                        except:
                            output = result.stdout.decode('gbk', errors='replace')
                    if result.stderr:
                        try:
                            error_output = result.stderr.decode('utf-8', errors='replace')
                        except:
                            error_output = result.stderr.decode('gbk', errors='replace')
                except Exception:
                    output = ""
                    error_output = "编码解析失败"
                    result = type('MockResult', (), {'returncode': 1})()  # 创建模拟结果

            status = {
                "running": process_running,  # 优先使用进程检测结果
                "job_count": 0,
                "jobs": [],
                "last_check": datetime.now().isoformat(),
                "error": None
            }

            if hasattr(result, 'returncode') and result.returncode == 0:
                # 如果进程检测失败，尝试从输出解析
                if not process_running and output:
                    if ("运行状态: 运行中" in output or "调度器启动成功" in output or "已配置" in output):
                        status["running"] = True

                # 提取任务数量
                if output and "任务数量:" in output:
                    try:
                        job_count_line = [line for line in output.split('\n') if "任务数量:" in line][0]
                        status["job_count"] = int(job_count_line.split(':')[1].strip())
                    except:
                        pass

                # 获取配置信息
                config = self.load_config()
                if config:
                    status["config"] = {
                        "update_time": config.get("update_schedule", {}).get("cron", "未配置"),
                        "description": config.get("update_schedule", {}).get("description", ""),
                        "enabled": config.get("update_schedule", {}).get("enabled", False)
                    }
            else:
                status["error"] = error_output or "调度器状态查询失败"

            return status
            
        except subprocess.TimeoutExpired:
            return {
                "running": False,
                "error": "状态查询超时",
                "last_check": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "running": False,
                "error": str(e),
                "last_check": datetime.now().isoformat()
            }
    
    def start_scheduler(self) -> Dict[str, Any]:
        """
        启动调度器

        Returns:
            启动结果
        """
        with self._lock:
            try:
                # 检查是否已经运行
                status = self.get_scheduler_status()
                if status.get("running"):
                    return {
                        "success": True,
                        "message": "调度器已在运行",
                        "already_running": True
                    }

                # 启动调度器
                self.scheduler_process = subprocess.Popen(
                    ["python", "scripts/start_scheduler.py", "--daemon"],
                    cwd=os.getcwd(),
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    encoding='utf-8',
                    errors='replace'  # 替换无法解码的字符
                )

                # 等待一段时间检查启动状态
                time.sleep(3)

                # 验证启动状态
                status = self.get_scheduler_status()
                if status.get("running"):
                    return {
                        "success": True,
                        "message": "调度器启动成功",
                        "process_id": self.scheduler_process.pid if self.scheduler_process else None
                    }
                else:
                    # 获取进程输出以提供更详细的错误信息
                    error_details = []
                    if self.scheduler_process:
                        try:
                            stdout, stderr = self.scheduler_process.communicate(timeout=1)
                            if stderr:
                                error_details.append(f"错误输出: {stderr.strip()}")
                            if stdout:
                                error_details.append(f"标准输出: {stdout.strip()}")
                        except subprocess.TimeoutExpired:
                            error_details.append("进程启动超时")
                        except Exception as e:
                            error_details.append(f"获取进程输出失败: {str(e)}")

                    error_msg = status.get("error") or "调度器进程启动后未能正常运行"
                    if error_details:
                        error_msg += f" ({'; '.join(error_details)})"

                    return {
                        "success": False,
                        "message": "调度器启动失败",
                        "error": error_msg
                    }

            except PermissionError as e:
                error_msg = "权限不足，无法启动调度器"
                logger.error(f"{error_msg}: {e}")
                if ERROR_HANDLER_AVAILABLE:
                    handler = get_scheduler_error_handler()
                    return handler.handle_permission_error(e, {"operation": "start_scheduler"})
                return {"success": False, "message": error_msg, "error": str(e)}
            except FileNotFoundError as e:
                error_msg = "找不到调度器启动脚本"
                logger.error(f"{error_msg}: {e}")
                return {"success": False, "message": error_msg, "error": str(e)}
            except Exception as e:
                error_msg = "启动调度器异常"
                logger.error(f"{error_msg}: {e}")
                if ERROR_HANDLER_AVAILABLE:
                    handler = get_scheduler_error_handler()
                    return handler.handle_scheduler_start_error(e, {"operation": "start_scheduler"})
                return {"success": False, "message": error_msg, "error": str(e)}
    
    def stop_scheduler(self) -> Dict[str, Any]:
        """
        停止调度器
        
        Returns:
            停止结果
        """
        with self._lock:
            try:
                # 如果有进程句柄，尝试终止
                if self.scheduler_process:
                    self.scheduler_process.terminate()
                    self.scheduler_process.wait(timeout=5)
                    self.scheduler_process = None
                
                # 验证停止状态
                time.sleep(2)
                status = self.get_scheduler_status()
                
                if not status.get("running"):
                    return {
                        "success": True,
                        "message": "调度器已停止"
                    }
                else:
                    return {
                        "success": False,
                        "message": "调度器停止失败",
                        "error": "进程仍在运行"
                    }
                    
            except Exception as e:
                logger.error(f"停止调度器失败: {e}")
                return {
                    "success": False,
                    "message": "停止调度器异常",
                    "error": str(e)
                }
    
    def restart_scheduler(self) -> Dict[str, Any]:
        """
        重启调度器
        
        Returns:
            重启结果
        """
        # 先停止
        stop_result = self.stop_scheduler()
        if not stop_result.get("success"):
            return {
                "success": False,
                "message": "重启失败：无法停止调度器",
                "error": stop_result.get("error")
            }
        
        # 等待一段时间
        time.sleep(2)
        
        # 再启动
        start_result = self.start_scheduler()
        if start_result.get("success"):
            return {
                "success": True,
                "message": "调度器重启成功"
            }
        else:
            return {
                "success": False,
                "message": "重启失败：无法启动调度器",
                "error": start_result.get("error")
            }
    
    def run_job_now(self, job_name: str = "data_update") -> Dict[str, Any]:
        """
        立即执行任务
        
        Args:
            job_name: 任务名称
            
        Returns:
            执行结果
        """
        try:
            result = subprocess.run(
                ["python", "scripts/start_scheduler.py", "--run-job", job_name],
                capture_output=True,
                text=True,
                timeout=60,
                cwd=os.getcwd()
            )
            
            if result.returncode == 0:
                return {
                    "success": True,
                    "message": f"任务 {job_name} 执行成功",
                    "output": result.stdout
                }
            else:
                return {
                    "success": False,
                    "message": f"任务 {job_name} 执行失败",
                    "error": result.stderr
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"执行任务异常",
                "error": str(e)
            }


# 全局控制器实例
_controller_instance = None

def get_scheduler_controller() -> SchedulerController:
    """获取调度器控制器单例"""
    global _controller_instance
    if _controller_instance is None:
        _controller_instance = SchedulerController()
    return _controller_instance
