#!/usr/bin/env python3
"""
SQLite数据库管理器

提供高性能的数据存储、查询和管理功能
"""

import json
import logging
import sqlite3
import sys
from datetime import date, datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

try:
    import polars as pl
    HAS_POLARS = True
except ImportError:
    HAS_POLARS = False
    pl = None

sys.path.append('src')

from core.database_manager import EnhancedDatabaseManager
from data.models import LotteryRecord

logger = logging.getLogger(__name__)

class DatabaseManager:
    """SQLite数据库管理器（兼容性包装器）"""

    def __init__(self, db_path: str = "data/lottery.db"):
        """
        初始化数据库管理器

        Args:
            db_path: 数据库文件路径
        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self.logger = logger

        # 使用增强的数据库管理器
        self._enhanced_manager = EnhancedDatabaseManager(str(db_path))

    def _get_connection(self):
        """获取数据库连接"""
        return sqlite3.connect(self.db_path)
        
    def _init_database(self) -> None:
        """初始化数据库结构"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建主数据表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS lottery_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    period TEXT UNIQUE NOT NULL,
                    date DATE NOT NULL,
                    numbers TEXT NOT NULL,
                    trial_numbers TEXT NOT NULL,
                    draw_machine INTEGER NOT NULL,
                    trial_machine INTEGER NOT NULL,
                    sales_amount INTEGER NOT NULL,
                    direct_prize INTEGER NOT NULL,
                    group3_prize INTEGER NOT NULL,
                    group6_prize INTEGER NOT NULL,
                    unknown_field1 INTEGER DEFAULT 0,
                    unknown_field2 INTEGER DEFAULT 0,
                    unknown_field3 INTEGER DEFAULT 0,
                    sum_value INTEGER NOT NULL,
                    trial_sum_value INTEGER NOT NULL,
                    span_value INTEGER NOT NULL,
                    trial_span_value INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_period ON lottery_records(period)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_date ON lottery_records(date)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_numbers ON lottery_records(numbers)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_sum_value ON lottery_records(sum_value)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_span_value ON lottery_records(span_value)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_sales_amount ON lottery_records(sales_amount)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_draw_machine ON lottery_records(draw_machine)")
            
            # 创建统计缓存表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS statistics_cache (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    cache_key TEXT UNIQUE NOT NULL,
                    cache_data TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP,
                    hit_count INTEGER DEFAULT 0
                )
            """)
            
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_cache_key ON statistics_cache(cache_key)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_expires_at ON statistics_cache(expires_at)")
            
            # 创建元数据表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS metadata (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
            self.logger.info("数据库初始化完成")
    
    def insert_records(self, records: List[LotteryRecord]) -> int:
        """
        批量插入记录

        Args:
            records: 彩票记录列表

        Returns:
            插入的记录数
        """
        # 使用增强管理器的方法
        return self._enhanced_manager.insert_records(records)
    
    def get_records_count(self) -> int:
        """获取记录总数"""
        # 使用增强管理器的方法
        return self._enhanced_manager.get_records_count()
    
    def get_date_range(self) -> Tuple[Optional[str], Optional[str]]:
        """获取数据日期范围"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT MIN(date), MAX(date) FROM lottery_records")
            result = cursor.fetchone()
            return result[0], result[1]

    def get_all_records(self) -> List[LotteryRecord]:
        """获取所有记录"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT period, date, numbers, trial_numbers, draw_machine,
                       trial_machine, sales_amount, direct_prize, group3_prize,
                       group6_prize, unknown_field1, unknown_field2, unknown_field3
                FROM lottery_records
                ORDER BY date ASC
            """)

            records = []
            for row in cursor.fetchall():
                record = LotteryRecord(
                    period=row[0],
                    date=datetime.strptime(row[1], "%Y-%m-%d").date() if row[1] else None,
                    numbers=row[2],
                    trial_numbers=row[3],
                    draw_machine=row[4],
                    trial_machine=row[5],
                    sales_amount=row[6],
                    direct_prize=row[7],
                    group3_prize=row[8],
                    group6_prize=row[9],
                    unknown_field1=row[10],
                    unknown_field2=row[11],
                    unknown_field3=row[12]
                )
                records.append(record)

            return records

    def get_recent_records(self, limit: int = 50) -> List[LotteryRecord]:
        """获取最近的记录"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT period, date, numbers, trial_numbers, draw_machine,
                       trial_machine, sales_amount, direct_prize, group3_prize,
                       group6_prize, unknown_field1, unknown_field2, unknown_field3
                FROM lottery_records
                ORDER BY date DESC
                LIMIT ?
            """, (limit,))

            records = []
            for row in cursor.fetchall():
                record = LotteryRecord(
                    period=row[0],
                    date=datetime.strptime(row[1], "%Y-%m-%d").date() if row[1] else None,
                    numbers=row[2],
                    trial_numbers=row[3],
                    draw_machine=row[4],
                    trial_machine=row[5],
                    sales_amount=row[6],
                    direct_prize=row[7],
                    group3_prize=row[8],
                    group6_prize=row[9],
                    unknown_field1=row[10],
                    unknown_field2=row[11],
                    unknown_field3=row[12]
                )
                records.append(record)

            return records

    def query_by_date_range(self, start_date: str, end_date: str):
        """
        按日期范围查询数据

        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)

        Returns:
            查询结果DataFrame或字典列表
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            query = f"""
                SELECT * FROM lottery_records
                WHERE date BETWEEN '{start_date}' AND '{end_date}'
                ORDER BY date DESC
            """
            cursor.execute(query)

            if HAS_POLARS:
                return pl.read_database(query, conn)
            else:
                # 返回字典列表作为备选
                columns = [description[0] for description in cursor.description]
                rows = cursor.fetchall()
                return [dict(zip(columns, row)) for row in rows]
    
    def query_by_sum_range(self, min_sum: int, max_sum: int):
        """
        按和值范围查询数据

        Args:
            min_sum: 最小和值
            max_sum: 最大和值

        Returns:
            查询结果DataFrame或字典列表
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            query = f"""
                SELECT * FROM lottery_records
                WHERE sum_value BETWEEN {min_sum} AND {max_sum}
                ORDER BY date DESC
            """
            cursor.execute(query)

            if HAS_POLARS:
                return pl.read_database(query, conn)
            else:
                # 返回字典列表作为备选
                columns = [description[0] for description in cursor.description]
                rows = cursor.fetchall()
                return [dict(zip(columns, row)) for row in rows]
    
    def get_frequency_stats(self, position: str = "all") -> Dict[str, Any]:
        """
        获取号码频率统计

        Args:
            position: 位置 ("all", "hundreds", "tens", "units")

        Returns:
            频率统计结果
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            result = {}

            # 获取总记录数
            cursor.execute("SELECT COUNT(*) FROM lottery_records")
            total_records = cursor.fetchone()[0]
            result["total_records"] = total_records

            if position in ["all", "hundreds"]:
                cursor.execute("""
                    SELECT SUBSTR(numbers, 1, 1) as digit, COUNT(*) as count
                    FROM lottery_records
                    WHERE numbers IS NOT NULL AND LENGTH(numbers) = 3
                    GROUP BY SUBSTR(numbers, 1, 1)
                    ORDER BY count DESC
                """)
                result["hundreds"] = [{"digit": row[0], "count": row[1]} for row in cursor.fetchall()]

            if position in ["all", "tens"]:
                cursor.execute("""
                    SELECT SUBSTR(numbers, 2, 1) as digit, COUNT(*) as count
                    FROM lottery_records
                    WHERE numbers IS NOT NULL AND LENGTH(numbers) = 3
                    GROUP BY SUBSTR(numbers, 2, 1)
                    ORDER BY count DESC
                """)
                result["tens"] = [{"digit": row[0], "count": row[1]} for row in cursor.fetchall()]

            if position in ["all", "units"]:
                cursor.execute("""
                    SELECT SUBSTR(numbers, 3, 1) as digit, COUNT(*) as count
                    FROM lottery_records
                    WHERE numbers IS NOT NULL AND LENGTH(numbers) = 3
                    GROUP BY SUBSTR(numbers, 3, 1)
                    ORDER BY count DESC
                """)
                result["units"] = [{"digit": row[0], "count": row[1]} for row in cursor.fetchall()]

            # 计算综合数字频率（前端期望的格式）
            if position == "all":
                digit_frequency = {}
                for digit in range(10):
                    digit_str = str(digit)
                    total_count = 0

                    # 统计该数字在所有位置的出现次数
                    for pos_data in [result.get("hundreds", []), result.get("tens", []), result.get("units", [])]:
                        for item in pos_data:
                            if item["digit"] == digit_str:
                                total_count += item["count"]
                                break

                    digit_frequency[digit_str] = total_count

                result["digit_frequency"] = digit_frequency

            # 添加位置频率分析（前端期望的格式）
            if position == "all":
                position_frequency = []
                for digit in range(10):
                    digit_str = str(digit)
                    for pos_name, pos_data in [("百位", result.get("hundreds", [])),
                                             ("十位", result.get("tens", [])),
                                             ("个位", result.get("units", []))]:
                        count = 0
                        for item in pos_data:
                            if item["digit"] == digit_str:
                                count = item["count"]
                                break
                        position_frequency.append({
                            "数字": digit_str,
                            "位置": pos_name,
                            "频率": count
                        })

                result["position_frequency"] = position_frequency

            # 添加日期范围查询
            cursor.execute("SELECT MIN(date) as min_date, MAX(date) as max_date FROM lottery_records")
            date_result = cursor.fetchone()
            if date_result and date_result[0] and date_result[1]:
                result["date_range"] = f"{date_result[0]} 至 {date_result[1]}"
            else:
                result["date_range"] = "N/A"

            # 添加最新期号查询
            cursor.execute("SELECT period FROM lottery_records ORDER BY date DESC, period DESC LIMIT 1")
            latest_result = cursor.fetchone()
            result["latest_period"] = latest_result[0] if latest_result else "N/A"

            # 计算热号冷号（基于现有的digit_frequency）
            if "digit_frequency" in result:
                digit_freq = result["digit_frequency"]
                sorted_digits = sorted(digit_freq.items(), key=lambda x: x[1], reverse=True)

                # 热号：频率最高的5个数字
                result["hot_numbers"] = [(digit, count) for digit, count in sorted_digits[:5]]
                # 冷号：频率最低的5个数字
                result["cold_numbers"] = [(digit, count) for digit, count in sorted_digits[-5:]]
            else:
                result["hot_numbers"] = []
                result["cold_numbers"] = []

            # 添加分析时间
            result["analysis_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            return result
    
    def get_sum_distribution(self) -> Dict[str, Any]:
        """获取和值分布统计"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 正式开奖号码和值分布
            cursor.execute("""
                SELECT sum_value, COUNT(*) as count
                FROM lottery_records 
                GROUP BY sum_value 
                ORDER BY sum_value
            """)
            sum_dist = [{"sum_value": row[0], "count": row[1]} for row in cursor.fetchall()]
            
            # 试机号码和值分布
            cursor.execute("""
                SELECT trial_sum_value, COUNT(*) as count
                FROM lottery_records 
                GROUP BY trial_sum_value 
                ORDER BY trial_sum_value
            """)
            trial_sum_dist = [{"sum_value": row[0], "count": row[1]} for row in cursor.fetchall()]
            
            return {
                "sum_distribution": sum_dist,
                "trial_sum_distribution": trial_sum_dist
            }
    
    def get_sales_stats(self) -> Dict[str, Any]:
        """获取销售额统计"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 总体统计
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_records,
                    SUM(sales_amount) as total_sales,
                    AVG(sales_amount) as avg_sales,
                    MIN(sales_amount) as min_sales,
                    MAX(sales_amount) as max_sales
                FROM lottery_records
            """)
            overall = cursor.fetchone()
            
            # 年度统计
            cursor.execute("""
                SELECT 
                    strftime('%Y', date) as year,
                    COUNT(*) as draw_count,
                    SUM(sales_amount) as total_sales,
                    AVG(sales_amount) as avg_sales
                FROM lottery_records 
                GROUP BY strftime('%Y', date)
                ORDER BY year
            """)
            yearly = [{"year": row[0], "draw_count": row[1], "total_sales": row[2], "avg_sales": row[3]} 
                     for row in cursor.fetchall()]
            
            return {
                "overall": {
                    "total_records": overall[0],
                    "total_sales": overall[1],
                    "avg_sales": overall[2],
                    "min_sales": overall[3],
                    "max_sales": overall[4]
                },
                "yearly": yearly
            }
    
    def cache_statistics(self, cache_key: str, data: Dict[str, Any], expires_hours: int = 24) -> None:
        """
        缓存统计结果
        
        Args:
            cache_key: 缓存键
            data: 要缓存的数据
            expires_hours: 过期时间（小时）
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            expires_at = datetime.now().timestamp() + (expires_hours * 3600)
            
            cursor.execute("""
                INSERT OR REPLACE INTO statistics_cache (cache_key, cache_data, expires_at)
                VALUES (?, ?, ?)
            """, (cache_key, json.dumps(data), expires_at))
            
            conn.commit()
    
    def get_cached_statistics(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """
        获取缓存的统计结果
        
        Args:
            cache_key: 缓存键
            
        Returns:
            缓存的数据，如果不存在或已过期则返回None
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT cache_data, expires_at FROM statistics_cache 
                WHERE cache_key = ? AND (expires_at IS NULL OR expires_at > ?)
            """, (cache_key, datetime.now().timestamp()))
            
            result = cursor.fetchone()
            if result:
                # 更新命中次数
                cursor.execute("""
                    UPDATE statistics_cache SET hit_count = hit_count + 1 
                    WHERE cache_key = ?
                """, (cache_key,))
                conn.commit()
                
                return json.loads(result[0])
            
            return None
    
    def clear_expired_cache(self) -> int:
        """清理过期缓存"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                DELETE FROM statistics_cache 
                WHERE expires_at IS NOT NULL AND expires_at <= ?
            """, (datetime.now().timestamp(),))
            
            conn.commit()
            return cursor.rowcount
    
    def get_database_info(self) -> Dict[str, Any]:
        """获取数据库信息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 表信息
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            # 数据库大小
            db_size = self.db_path.stat().st_size if self.db_path.exists() else 0
            
            # 记录统计
            stats = {}
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                stats[table] = cursor.fetchone()[0]
            
            return {
                "db_path": str(self.db_path),
                "db_size_mb": round(db_size / 1024 / 1024, 2),
                "tables": tables,
                "record_counts": stats
            }

    # 增强功能的包装方法
    def health_check(self) -> Dict[str, Any]:
        """执行数据库健康检查"""
        return self._enhanced_manager.health_check()

    def get_health_status(self) -> Dict[str, Any]:
        """获取当前健康状态"""
        return self._enhanced_manager.get_health_status()

    def get_connection_pool_status(self) -> Dict[str, Any]:
        """获取连接池详细状态"""
        return self._enhanced_manager.get_connection_pool_status()

    def optimize_database(self) -> Dict[str, Any]:
        """优化数据库性能"""
        return self._enhanced_manager.optimize_database()

    def close(self):
        """关闭数据库管理器"""
        if hasattr(self, '_enhanced_manager'):
            self._enhanced_manager.close()
