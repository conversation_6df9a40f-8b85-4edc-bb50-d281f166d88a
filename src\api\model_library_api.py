"""
模型库API扩展
提供RESTful API接口，支持特征工程、训练监控、A/B测试等功能的API访问
"""

from fastapi import FastAPI, HTTPException, BackgroundTasks, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
import json
import asyncio
from datetime import datetime
import logging

# 导入模型库模块
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from model_library.features.feature_ranking import MultiAlgorithmFeatureRanking
    from model_library.features.feature_selector import InteractiveFeatureSelector
    from model_library.data.adaptive_quality_engine import AdaptiveDataQualityEngine
    from model_library.training.websocket_monitor import WebSocketTrainingMonitor
    from model_library.training.bayesian_recommender import BayesianHyperparameterRecommender
    from model_library.optimization.ab_testing import AdaptiveABTestingFramework
    from model_library.meta_learning.meta_optimizer import Meta<PERSON>ear<PERSON>, Task
except ImportError as e:
    print(f"导入模块失败: {e}")


# Pydantic模型定义
class FeatureRankingRequest(BaseModel):
    model_id: str
    feature_names: List[str]
    data_sample: Optional[List[List[float]]] = None


class FeatureRankingResponse(BaseModel):
    rankings: Dict[str, float]
    algorithm_weights: Dict[str, float]
    confidence: float


class DataQualityRequest(BaseModel):
    model_id: str
    data_range: tuple
    data_sample: List[Dict[str, Any]]


class DataQualityResponse(BaseModel):
    overall_score: float
    completeness: float
    consistency: float
    accuracy: float
    timeliness: float
    validity: float


class HyperparameterRequest(BaseModel):
    model_id: str
    optimization_target: str = "accuracy"


class HyperparameterResponse(BaseModel):
    recommended_parameters: Dict[str, Any]
    confidence: float
    expected_improvement: float
    reasoning: str


class ABTestRequest(BaseModel):
    experiment_name: str
    description: str
    arms: List[Dict[str, Any]]
    allocation_strategy: str = "thompson_sampling"
    target_metric: str = "accuracy"


class ABTestResponse(BaseModel):
    experiment_id: str
    status: str
    message: str


class TrainingSessionRequest(BaseModel):
    model_id: str
    training_config: Dict[str, Any]


class TrainingSessionResponse(BaseModel):
    session_id: str
    status: str
    message: str


# 创建FastAPI应用
app = FastAPI(
    title="福彩3D预测系统模型库API",
    description="提供特征工程、训练监控、A/B测试等功能的API接口",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局实例
feature_ranking = MultiAlgorithmFeatureRanking()
feature_selector = InteractiveFeatureSelector()
quality_engine = AdaptiveDataQualityEngine()
ws_monitor = WebSocketTrainingMonitor()
bayesian_recommender = BayesianHyperparameterRecommender()
ab_framework = AdaptiveABTestingFramework()
meta_learner = MetaLearner()

# 日志配置
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "福彩3D预测系统模型库API",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "feature_ranking": "active",
            "quality_engine": "active",
            "websocket_monitor": "active",
            "ab_testing": "active",
            "meta_learning": "active"
        }
    }


# 特征工程API
@app.post("/api/features/ranking", response_model=FeatureRankingResponse)
async def rank_features(request: FeatureRankingRequest):
    """特征重要性排序"""
    try:
        # 模拟数据
        import numpy as np
        if request.data_sample:
            X = np.array(request.data_sample)
            y = np.random.randint(0, 1000, size=len(X))
        else:
            X = np.random.randn(100, len(request.feature_names))
            y = np.random.randint(0, 1000, size=100)
        
        # 计算特征重要性
        rankings = feature_ranking.calculate_comprehensive_ranking(
            X, y, request.feature_names, request.model_id
        )
        
        algorithm_weights = feature_ranking.get_algorithm_weights(request.model_id)
        confidence = feature_ranking.calculate_ranking_confidence(rankings)
        
        return FeatureRankingResponse(
            rankings=rankings,
            algorithm_weights=algorithm_weights,
            confidence=confidence
        )
    
    except Exception as e:
        logger.error(f"特征排序失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/features/categories")
async def get_feature_categories():
    """获取特征分类"""
    try:
        categories = feature_selector.get_available_feature_categories()
        return {"categories": categories}
    
    except Exception as e:
        logger.error(f"获取特征分类失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 数据质量API
@app.post("/api/data/quality", response_model=DataQualityResponse)
async def assess_data_quality(request: DataQualityRequest):
    """数据质量评估"""
    try:
        import pandas as pd
        
        # 转换数据格式
        df = pd.DataFrame(request.data_sample)
        
        # 评估数据质量
        quality_metrics = quality_engine.calculate_adaptive_quality_score(
            request.model_id, request.data_range, df
        )
        
        return DataQualityResponse(
            overall_score=quality_metrics.overall_score,
            completeness=quality_metrics.completeness,
            consistency=quality_metrics.consistency,
            accuracy=quality_metrics.accuracy,
            timeliness=quality_metrics.timeliness,
            validity=quality_metrics.validity
        )
    
    except Exception as e:
        logger.error(f"数据质量评估失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 超参数推荐API
@app.post("/api/hyperparameters/recommend", response_model=HyperparameterResponse)
async def recommend_hyperparameters(request: HyperparameterRequest):
    """超参数推荐"""
    try:
        recommendation = bayesian_recommender.recommend_next_hyperparameters(
            request.model_id, request.optimization_target
        )
        
        return HyperparameterResponse(
            recommended_parameters=recommendation["recommended_parameters"],
            confidence=recommendation["confidence"],
            expected_improvement=recommendation["expected_improvement"],
            reasoning=recommendation["reasoning"]
        )
    
    except Exception as e:
        logger.error(f"超参数推荐失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/hyperparameters/update")
async def update_optimization_history(model_id: str, parameters: Dict[str, Any], results: Dict[str, float]):
    """更新优化历史"""
    try:
        bayesian_recommender.update_optimization_history(model_id, parameters, results)
        return {"status": "success", "message": "优化历史已更新"}
    
    except Exception as e:
        logger.error(f"更新优化历史失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# A/B测试API
@app.post("/api/ab-test/create", response_model=ABTestResponse)
async def create_ab_test(request: ABTestRequest):
    """创建A/B测试"""
    try:
        from model_library.optimization.ab_testing import AllocationStrategy
        
        success = ab_framework.create_experiment(
            experiment_id=f"exp_{int(datetime.now().timestamp())}",
            name=request.experiment_name,
            description=request.description,
            arms=request.arms,
            allocation_strategy=AllocationStrategy(request.allocation_strategy),
            target_metric=request.target_metric
        )
        
        if success:
            experiments = ab_framework.list_experiments()
            latest_exp = experiments[-1] if experiments else None
            
            return ABTestResponse(
                experiment_id=latest_exp["experiment_id"] if latest_exp else "unknown",
                status="created",
                message="A/B测试创建成功"
            )
        else:
            raise HTTPException(status_code=400, detail="A/B测试创建失败")
    
    except Exception as e:
        logger.error(f"创建A/B测试失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/ab-test/{experiment_id}/start")
async def start_ab_test(experiment_id: str):
    """启动A/B测试"""
    try:
        success = ab_framework.start_experiment(experiment_id)
        
        if success:
            return {"status": "success", "message": "A/B测试已启动"}
        else:
            raise HTTPException(status_code=400, detail="A/B测试启动失败")
    
    except Exception as e:
        logger.error(f"启动A/B测试失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/ab-test/{experiment_id}/status")
async def get_ab_test_status(experiment_id: str):
    """获取A/B测试状态"""
    try:
        status = ab_framework.get_experiment_status(experiment_id)
        
        if status:
            return status
        else:
            raise HTTPException(status_code=404, detail="实验不存在")
    
    except Exception as e:
        logger.error(f"获取A/B测试状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/ab-test/list")
async def list_ab_tests():
    """列出所有A/B测试"""
    try:
        experiments = ab_framework.list_experiments()
        return {"experiments": experiments}
    
    except Exception as e:
        logger.error(f"列出A/B测试失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 训练监控API
@app.post("/api/training/session", response_model=TrainingSessionResponse)
async def create_training_session(request: TrainingSessionRequest):
    """创建训练会话"""
    try:
        # 这里需要WebSocket连接，简化实现
        session_id = f"session_{int(datetime.now().timestamp())}"
        
        return TrainingSessionResponse(
            session_id=session_id,
            status="created",
            message="训练会话创建成功"
        )
    
    except Exception as e:
        logger.error(f"创建训练会话失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/training/sessions")
async def list_training_sessions():
    """列出训练会话"""
    try:
        sessions = ws_monitor.get_active_sessions()
        return {"sessions": sessions}
    
    except Exception as e:
        logger.error(f"列出训练会话失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# WebSocket端点
@app.websocket("/ws/training/{model_id}")
async def websocket_training_endpoint(websocket: WebSocket, model_id: str):
    """训练监控WebSocket端点"""
    try:
        # 启动训练监控
        session_id = await ws_monitor.start_training_monitor(
            model_id, websocket, {"epochs": 100, "batch_size": 32}
        )
        
        logger.info(f"WebSocket训练监控已启动: {session_id}")
        
        # 保持连接
        try:
            while True:
                # 接收客户端消息
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # 处理消息
                if message.get("type") == "ping":
                    await websocket.send_text(json.dumps({"type": "pong"}))
                
                await asyncio.sleep(1)
                
        except WebSocketDisconnect:
            logger.info(f"WebSocket连接断开: {session_id}")
        
    except Exception as e:
        logger.error(f"WebSocket训练监控失败: {e}")
        await websocket.close()


# 元学习API
@app.post("/api/meta-learning/task")
async def add_meta_learning_task(task_data: Dict[str, Any]):
    """添加元学习任务"""
    try:
        task = Task(
            task_id=task_data["task_id"],
            name=task_data["name"],
            description=task_data.get("description", ""),
            task_type=task_data["task_type"],
            data_size=task_data["data_size"],
            feature_count=task_data["feature_count"],
            target_type=task_data["target_type"],
            complexity_score=task_data.get("complexity_score", 0.5)
        )
        
        meta_learner.add_task(task)
        
        return {"status": "success", "message": "元学习任务已添加"}
    
    except Exception as e:
        logger.error(f"添加元学习任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/meta-learning/recommend")
async def get_meta_learning_recommendation(task_data: Dict[str, Any]):
    """获取元学习推荐"""
    try:
        target_task = Task(
            task_id=task_data["task_id"],
            name=task_data["name"],
            description=task_data.get("description", ""),
            task_type=task_data["task_type"],
            data_size=task_data["data_size"],
            feature_count=task_data["feature_count"],
            target_type=task_data["target_type"],
            complexity_score=task_data.get("complexity_score", 0.5)
        )
        
        recommendations = meta_learner.recommend_hyperparameters(target_task)
        
        return {"recommendations": recommendations}
    
    except Exception as e:
        logger.error(f"获取元学习推荐失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/meta-learning/insights")
async def get_meta_learning_insights():
    """获取元学习洞察"""
    try:
        insights = meta_learner.get_meta_learning_insights()
        return insights
    
    except Exception as e:
        logger.error(f"获取元学习洞察失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 系统统计API
@app.get("/api/stats/overview")
async def get_system_overview():
    """获取系统概览"""
    try:
        return {
            "timestamp": datetime.now().isoformat(),
            "active_training_sessions": len(ws_monitor.get_active_sessions()),
            "active_experiments": len(ab_framework.list_experiments()),
            "meta_learning_tasks": len(meta_learner.tasks),
            "system_status": "healthy"
        }
    
    except Exception as e:
        logger.error(f"获取系统概览失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)
