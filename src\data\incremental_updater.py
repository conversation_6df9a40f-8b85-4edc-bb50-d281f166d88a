"""
福彩3D增量更新器

实现智能增量更新，避免重复下载全量数据
"""

import hashlib
import json
import logging
import os
from datetime import date, datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

try:
    from ..core.database import DatabaseManager
except ImportError:
    # 处理直接运行时的导入问题
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    from core.database import DatabaseManager

from .collector import LotteryDataCollector
from .consistency_checker import ConsistencyChecker
from .models import LotteryRecord
from .parser import DataParser
from .quality_checker import QualityChecker

logger = logging.getLogger(__name__)


class IncrementalUpdater:
    """增量更新器"""
    
    def __init__(self, data_dir: str = "data"):
        """
        初始化增量更新器
        
        Args:
            data_dir: 数据目录路径
        """
        self.data_dir = Path(data_dir)
        self.raw_dir = self.data_dir / "raw"
        self.processed_dir = self.data_dir / "processed"
        self.metadata_file = self.processed_dir / "update_metadata.json"
        
        # 确保目录存在
        self.raw_dir.mkdir(parents=True, exist_ok=True)
        self.processed_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化组件
        self.collector = LotteryDataCollector()
        self.parser = DataParser()
        self.quality_checker = QualityChecker()
        self.db_manager = DatabaseManager()
        self.consistency_checker = ConsistencyChecker(str(self.data_dir))

        logger.info(f"增量更新器初始化完成，数据目录: {self.data_dir}")
    
    def load_metadata(self) -> Dict[str, Any]:
        """
        加载更新元数据
        
        Returns:
            元数据字典
        """
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                logger.info(f"加载元数据成功，上次更新: {metadata.get('last_update_time', 'N/A')}")
                return metadata
            except Exception as e:
                logger.warning(f"加载元数据失败: {e}")
        
        # 返回默认元数据
        return {
            "last_update_time": None,
            "last_data_hash": None,
            "last_record_count": 0,
            "last_period": None,
            "last_date": None,
            "update_history": []
        }
    
    def save_metadata(self, metadata: Dict[str, Any]) -> bool:
        """
        保存更新元数据
        
        Args:
            metadata: 元数据字典
            
        Returns:
            保存是否成功
        """
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            logger.info("元数据保存成功")
            return True
        except Exception as e:
            logger.error(f"保存元数据失败: {e}")
            return False
    
    def calculate_data_hash(self, data: str) -> str:
        """
        计算数据哈希值
        
        Args:
            data: 数据字符串
            
        Returns:
            MD5哈希值
        """
        return hashlib.md5(data.encode('utf-8')).hexdigest()
    
    def detect_data_changes(self, new_data: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        检测数据变化
        
        Args:
            new_data: 新数据
            metadata: 当前元数据
            
        Returns:
            变化检测结果
        """
        new_hash = self.calculate_data_hash(new_data)
        old_hash = metadata.get('last_data_hash')
        
        # 解析新数据
        new_records, _ = self.parser.parse_data(new_data)
        new_count = len(new_records)
        old_count = metadata.get('last_record_count', 0)
        
        # 获取最新记录信息
        latest_record = new_records[-1] if new_records else None
        new_period = latest_record.period if latest_record else None
        new_date = latest_record.date.isoformat() if latest_record else None
        
        old_period = metadata.get('last_period')
        old_date = metadata.get('last_date')
        
        return {
            "data_changed": new_hash != old_hash,
            "hash_changed": new_hash != old_hash,
            "count_changed": new_count != old_count,
            "period_changed": new_period != old_period,
            "date_changed": new_date != old_date,
            "new_hash": new_hash,
            "old_hash": old_hash,
            "new_count": new_count,
            "old_count": old_count,
            "new_period": new_period,
            "old_period": old_period,
            "new_date": new_date,
            "old_date": old_date,
            "records_added": max(0, new_count - old_count)
        }
    
    def find_new_records(self, all_records: List[LotteryRecord], metadata: Dict[str, Any]) -> List[LotteryRecord]:
        """
        查找新增记录
        
        Args:
            all_records: 所有记录
            metadata: 元数据
            
        Returns:
            新增记录列表
        """
        last_period = metadata.get('last_period')
        last_date = metadata.get('last_date')
        
        if not last_period or not last_date:
            # 如果没有历史记录，返回最近的记录
            return all_records[-10:] if len(all_records) > 10 else all_records
        
        new_records = []
        last_date_obj = datetime.fromisoformat(last_date).date()
        
        for record in all_records:
            # 查找比上次更新更新的记录
            if (record.period > last_period or 
                (record.period == last_period and record.date > last_date_obj)):
                new_records.append(record)
        
        return new_records
    
    def backup_current_data(self) -> Optional[str]:
        """
        备份当前数据
        
        Returns:
            备份文件路径，失败时返回None
        """
        try:
            # 查找最新的原始数据文件
            raw_files = list(self.raw_dir.glob("3d_data_*.txt"))
            if not raw_files:
                logger.warning("没有找到原始数据文件")
                return None
            
            latest_file = max(raw_files, key=lambda f: f.stat().st_mtime)
            
            # 创建备份
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.raw_dir / f"backup_3d_data_{timestamp}.txt"
            
            import shutil
            shutil.copy2(latest_file, backup_file)
            
            logger.info(f"数据备份成功: {backup_file}")
            return str(backup_file)
            
        except Exception as e:
            logger.error(f"数据备份失败: {e}")
            return None
    
    def perform_incremental_update(self, force_update: bool = False) -> Dict[str, Any]:
        """
        执行增量更新
        
        Args:
            force_update: 是否强制更新
            
        Returns:
            更新结果
        """
        logger.info("开始执行增量更新...")
        
        # 1. 加载元数据
        metadata = self.load_metadata()
        
        # 2. 获取新数据
        logger.info("获取最新数据...")
        new_data = self.collector.fetch_latest_data_sync()
        
        if not new_data:
            return {
                "success": False,
                "message": "数据获取失败",
                "timestamp": datetime.now().isoformat()
            }
        
        # 3. 检测变化
        changes = self.detect_data_changes(new_data, metadata)
        
        if not changes["data_changed"] and not force_update:
            logger.info("数据未发生变化，跳过更新")
            return {
                "success": True,
                "message": "数据未发生变化",
                "changes": changes,
                "timestamp": datetime.now().isoformat()
            }
        
        logger.info(f"检测到数据变化: 新增 {changes['records_added']} 条记录")
        
        # 4. 备份当前数据
        backup_file = self.backup_current_data()
        
        # 5. 保存新数据
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        new_data_file = self.raw_dir / f"3d_data_{timestamp}.txt"
        
        try:
            with open(new_data_file, 'w', encoding='utf-8') as f:
                f.write(new_data)
            logger.info(f"新数据已保存: {new_data_file}")
        except Exception as e:
            logger.error(f"保存新数据失败: {e}")
            return {
                "success": False,
                "message": f"保存数据失败: {e}",
                "timestamp": datetime.now().isoformat()
            }
        
        # 6. 解析和处理数据
        all_records, quality_report = self.parser.parse_data(new_data)
        new_records = self.find_new_records(all_records, metadata)
        
        # 7. 写入数据库（如果有新记录）
        if new_records:
            try:
                logger.info(f"写入数据库: {len(new_records)} 条新记录")
                inserted_count = self.db_manager.insert_records(new_records)
                logger.info(f"数据库写入成功: {inserted_count} 条记录")
            except Exception as e:
                logger.error(f"数据库写入失败: {e}")
                return {
                    "success": False,
                    "message": f"数据库写入失败: {e}",
                    "timestamp": datetime.now().isoformat()
                }

        # 8. 质量检查
        quality_result = self.quality_checker.generate_quality_report(all_records)

        # 10. 更新元数据
        update_info = {
            "timestamp": datetime.now().isoformat(),
            "records_added": len(new_records),
            "total_records": len(all_records),
            "quality_score": quality_result["summary"]["overall_score"],
            "data_file": str(new_data_file),
            "backup_file": backup_file,
            "changes": changes
        }

        metadata.update({
            "last_update_time": update_info["timestamp"],
            "last_data_hash": changes["new_hash"],
            "last_record_count": changes["new_count"],
            "last_period": changes["new_period"],
            "last_date": changes["new_date"]
        })

        # 添加到更新历史
        if "update_history" not in metadata:
            metadata["update_history"] = []

        metadata["update_history"].append(update_info)

        # 只保留最近20次更新记录
        metadata["update_history"] = metadata["update_history"][-20:]

        # 11. 保存元数据
        self.save_metadata(metadata)

        # 12. 导出处理后的数据
        from .formatter import export_lottery_data
        export_results = export_lottery_data(
            all_records,
            output_dir=str(self.processed_dir),
            formats=['json', 'csv']
        )
        
        # 13. 通知API服务刷新数据
        if len(new_records) > 0:
            self._notify_api_refresh(len(new_records), changes["new_period"])

        # 14. 数据一致性检查
        logger.info("执行数据一致性检查...")
        consistency_result = self.consistency_checker.check_consistency()

        if not consistency_result.get("is_consistent", False):
            logger.warning("检测到数据不一致，尝试自动修复...")
            repair_result = self.consistency_checker.auto_repair()
            if repair_result["success"]:
                logger.info("数据一致性自动修复成功")
            else:
                logger.error(f"数据一致性自动修复失败: {repair_result.get('message', 'Unknown error')}")

        logger.info("增量更新完成")

        return {
            "success": True,
            "message": "增量更新成功",
            "update_info": update_info,
            "new_records": [r.to_dict() for r in new_records[-5:]],  # 只返回最新5条
            "quality_score": quality_result["summary"]["overall_score"],
            "export_results": export_results,
            "consistency_check": consistency_result,
            "timestamp": datetime.now().isoformat()
        }

    def _notify_api_refresh(self, records_added: int, latest_period: str) -> None:
        """
        通知API服务刷新数据

        Args:
            records_added: 新增记录数
            latest_period: 最新期号
        """
        try:
            import requests

            logger.info(f"通知API服务刷新数据: 新增{records_added}条记录，最新期号{latest_period}")

            # 调用API刷新接口
            response = requests.post(
                "http://127.0.0.1:8888/api/v1/data/refresh",
                timeout=10,
                json={
                    "source": "scheduler",
                    "records_added": records_added,
                    "latest_period": latest_period
                }
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    logger.info("✅ API服务数据刷新成功")
                else:
                    logger.warning(f"⚠️ API服务数据刷新失败: {result.get('message', 'Unknown error')}")
            else:
                logger.warning(f"⚠️ API服务响应异常: HTTP {response.status_code}")

        except requests.exceptions.ConnectionError:
            logger.warning("⚠️ 无法连接到API服务，跳过数据刷新通知")
        except requests.exceptions.Timeout:
            logger.warning("⚠️ API服务响应超时，跳过数据刷新通知")
        except Exception as e:
            logger.warning(f"⚠️ 通知API服务刷新数据失败: {e}")
    
    def get_update_status(self) -> Dict[str, Any]:
        """
        获取更新状态
        
        Returns:
            更新状态信息
        """
        metadata = self.load_metadata()
        
        # 检查数据文件
        raw_files = list(self.raw_dir.glob("3d_data_*.txt"))
        processed_files = list(self.processed_dir.glob("lottery_data_*.json"))
        
        return {
            "last_update": metadata.get("last_update_time"),
            "last_period": metadata.get("last_period"),
            "last_date": metadata.get("last_date"),
            "record_count": metadata.get("last_record_count", 0),
            "update_count": len(metadata.get("update_history", [])),
            "raw_files": len(raw_files),
            "processed_files": len(processed_files),
            "data_directory": str(self.data_dir),
            "metadata_exists": self.metadata_file.exists()
        }
    
    def cleanup_old_files(self, keep_count: int = 5) -> Dict[str, Any]:
        """
        清理旧文件
        
        Args:
            keep_count: 保留文件数量
            
        Returns:
            清理结果
        """
        try:
            # 清理原始数据文件
            raw_files = sorted(
                self.raw_dir.glob("3d_data_*.txt"),
                key=lambda f: f.stat().st_mtime,
                reverse=True
            )
            
            deleted_raw = 0
            for file in raw_files[keep_count:]:
                file.unlink()
                deleted_raw += 1
            
            # 清理处理后的数据文件
            processed_files = sorted(
                self.processed_dir.glob("lottery_data_*.json"),
                key=lambda f: f.stat().st_mtime,
                reverse=True
            )
            
            deleted_processed = 0
            for file in processed_files[keep_count:]:
                # 同时删除对应的csv和txt文件
                base_name = file.stem
                for ext in ['.json', '.csv', '.txt']:
                    related_file = file.parent / f"{base_name}{ext}"
                    if related_file.exists():
                        related_file.unlink()
                        deleted_processed += 1
            
            logger.info(f"文件清理完成，删除原始文件: {deleted_raw}, 处理文件: {deleted_processed}")
            
            return {
                "success": True,
                "deleted_raw_files": deleted_raw,
                "deleted_processed_files": deleted_processed,
                "kept_files": keep_count
            }
            
        except Exception as e:
            logger.error(f"文件清理失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }


# 便捷函数
def check_for_updates(data_dir: str = "data") -> Dict[str, Any]:
    """
    便捷的更新检查函数
    
    Args:
        data_dir: 数据目录
        
    Returns:
        更新检查结果
    """
    updater = IncrementalUpdater(data_dir)
    return updater.get_update_status()


def perform_update(data_dir: str = "data", force: bool = False) -> Dict[str, Any]:
    """
    便捷的更新执行函数
    
    Args:
        data_dir: 数据目录
        force: 是否强制更新
        
    Returns:
        更新结果
    """
    updater = IncrementalUpdater(data_dir)
    return updater.perform_incremental_update(force)


if __name__ == "__main__":
    # 测试代码
    print("测试增量更新器...")
    
    updater = IncrementalUpdater()
    
    # 检查更新状态
    status = updater.get_update_status()
    print(f"\n当前状态:")
    print(f"  上次更新: {status['last_update']}")
    print(f"  记录数量: {status['record_count']}")
    print(f"  更新次数: {status['update_count']}")
    
    # 执行更新
    print(f"\n执行增量更新...")
    result = updater.perform_incremental_update()
    
    if result["success"]:
        print(f"✅ 更新成功!")
        print(f"  新增记录: {result['update_info']['records_added']}")
        print(f"  总记录数: {result['update_info']['total_records']}")
        print(f"  质量评分: {result['quality_score']}")
    else:
        print(f"❌ 更新失败: {result['message']}")
    
    print("\n增量更新器测试完成！")
