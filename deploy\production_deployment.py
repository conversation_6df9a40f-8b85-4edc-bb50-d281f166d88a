#!/usr/bin/env python3
"""
生产环境部署和验证

执行完整的生产环境部署流程，包括部署验证、性能监控、健康检查
"""

import asyncio
import json
import logging
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ProductionDeploymentValidator:
    """生产环境部署验证器"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.deploy_dir = self.project_root / "deploy"
        self.api_url = "http://127.0.0.1:8000"
        self.ui_url = "http://127.0.0.1:8501"
        
        # 部署验证标准
        self.validation_criteria = {
            "startup_time_max": 30,        # 最大启动时间(秒)
            "api_response_time_max": 3000, # API最大响应时间(ms)
            "ui_load_time_max": 5000,      # UI最大加载时间(ms)
            "health_check_pass_rate": 0.95, # 健康检查通过率
            "stability_test_duration": 300,  # 稳定性测试时长(秒)
            "min_uptime": 600               # 最小运行时间(秒)
        }
        
        self.deployment_status = {
            "api_deployed": False,
            "ui_deployed": False,
            "health_verified": False,
            "performance_verified": False,
            "stability_verified": False
        }
    
    async def deploy_to_production(self) -> bool:
        """部署到生产环境"""
        logger.info("🚀 开始生产环境部署...")
        
        try:
            # 1. 运行依赖检查
            if not await self._check_dependencies():
                logger.error("❌ 依赖检查失败")
                return False
            
            # 2. 执行部署脚本
            if not await self._execute_deployment():
                logger.error("❌ 部署执行失败")
                return False
            
            # 3. 启动服务
            if not await self._start_services():
                logger.error("❌ 服务启动失败")
                return False
            
            # 4. 验证部署
            if not await self._verify_deployment():
                logger.error("❌ 部署验证失败")
                return False
            
            logger.info("✅ 生产环境部署成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 生产环境部署失败: {e}")
            return False
    
    async def _check_dependencies(self) -> bool:
        """检查依赖"""
        logger.info("🔍 检查部署依赖...")
        
        try:
            # 运行依赖检查脚本
            result = subprocess.run([
                sys.executable, str(self.deploy_dir / "check_dependencies.py")
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                logger.info("✅ 依赖检查通过")
                return True
            else:
                logger.error(f"❌ 依赖检查失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("❌ 依赖检查超时")
            return False
        except Exception as e:
            logger.error(f"❌ 依赖检查异常: {e}")
            return False
    
    async def _execute_deployment(self) -> bool:
        """执行部署脚本"""
        logger.info("📦 执行部署脚本...")
        
        try:
            # 运行部署脚本
            result = subprocess.run([
                sys.executable, str(self.deploy_dir / "deploy.py")
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info("✅ 部署脚本执行成功")
                return True
            else:
                logger.error(f"❌ 部署脚本执行失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("❌ 部署脚本执行超时")
            return False
        except Exception as e:
            logger.error(f"❌ 部署脚本执行异常: {e}")
            return False
    
    async def _start_services(self) -> bool:
        """启动服务"""
        logger.info("🔧 启动生产环境服务...")
        
        try:
            # 启动API服务
            logger.info("启动API服务...")
            api_process = subprocess.Popen([
                sys.executable, str(self.deploy_dir / "start_api_production.py")
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # 等待API服务启动
            await asyncio.sleep(10)
            
            # 检查API服务状态
            if not await self._check_api_status():
                logger.error("❌ API服务启动失败")
                api_process.terminate()
                return False
            
            self.deployment_status["api_deployed"] = True
            logger.info("✅ API服务启动成功")
            
            # 启动UI服务
            logger.info("启动UI服务...")
            ui_process = subprocess.Popen([
                sys.executable, str(self.deploy_dir / "start_ui_production.py")
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # 等待UI服务启动
            await asyncio.sleep(15)
            
            # 检查UI服务状态
            if not await self._check_ui_status():
                logger.error("❌ UI服务启动失败")
                ui_process.terminate()
                api_process.terminate()
                return False
            
            self.deployment_status["ui_deployed"] = True
            logger.info("✅ UI服务启动成功")
            
            # 保存进程引用
            self.api_process = api_process
            self.ui_process = ui_process
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 服务启动异常: {e}")
            return False
    
    async def _check_api_status(self) -> bool:
        """检查API服务状态"""
        try:
            import requests
            response = requests.get(f"{self.api_url}/api/v1/health/", timeout=10)
            return response.status_code == 200
        except:
            return False
    
    async def _check_ui_status(self) -> bool:
        """检查UI服务状态"""
        try:
            import requests
            response = requests.get(self.ui_url, timeout=10)
            return response.status_code == 200
        except:
            return False
    
    async def _verify_deployment(self) -> bool:
        """验证部署"""
        logger.info("🔍 验证生产环境部署...")
        
        verification_steps = [
            ("健康检查验证", self._verify_health_checks),
            ("性能验证", self._verify_performance),
            ("稳定性验证", self._verify_stability),
            ("功能验证", self._verify_functionality)
        ]
        
        for step_name, step_func in verification_steps:
            logger.info(f"执行验证: {step_name}")
            
            try:
                if not await step_func():
                    logger.error(f"❌ 验证失败: {step_name}")
                    return False
                
                logger.info(f"✅ 验证通过: {step_name}")
                
            except Exception as e:
                logger.error(f"❌ 验证异常 {step_name}: {e}")
                return False
        
        return True
    
    async def _verify_health_checks(self) -> bool:
        """验证健康检查"""
        logger.info("🏥 验证健康检查...")
        
        try:
            import requests
            
            health_endpoints = [
                f"{self.api_url}/api/v1/health/",
                f"{self.api_url}/api/v1/health/detailed",
                f"{self.api_url}/api/v1/health/components/database",
                f"{self.api_url}/api/v1/health/components/websocket"
            ]
            
            successful_checks = 0
            total_checks = len(health_endpoints)
            
            for endpoint in health_endpoints:
                try:
                    response = requests.get(endpoint, timeout=10)
                    if response.status_code in [200, 503]:  # 503可能是正常状态
                        successful_checks += 1
                except:
                    pass
            
            pass_rate = successful_checks / total_checks
            
            if pass_rate >= self.validation_criteria["health_check_pass_rate"]:
                self.deployment_status["health_verified"] = True
                logger.info(f"✅ 健康检查通过率: {pass_rate:.1%}")
                return True
            else:
                logger.error(f"❌ 健康检查通过率不足: {pass_rate:.1%}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 健康检查验证失败: {e}")
            return False
    
    async def _verify_performance(self) -> bool:
        """验证性能"""
        logger.info("⚡ 验证性能指标...")
        
        try:
            import requests
            
            # API响应时间测试
            api_times = []
            for i in range(5):
                start_time = time.time()
                response = requests.get(f"{self.api_url}/api/v1/health/ping", timeout=10)
                response_time = (time.time() - start_time) * 1000
                
                if response.status_code == 200:
                    api_times.append(response_time)
                
                await asyncio.sleep(0.5)
            
            if api_times:
                avg_api_time = sum(api_times) / len(api_times)
                if avg_api_time <= self.validation_criteria["api_response_time_max"]:
                    logger.info(f"✅ API平均响应时间: {avg_api_time:.2f}ms")
                else:
                    logger.error(f"❌ API响应时间过长: {avg_api_time:.2f}ms")
                    return False
            
            # UI加载时间测试
            ui_times = []
            for i in range(3):
                start_time = time.time()
                response = requests.get(self.ui_url, timeout=15)
                load_time = (time.time() - start_time) * 1000
                
                if response.status_code == 200:
                    ui_times.append(load_time)
                
                await asyncio.sleep(1)
            
            if ui_times:
                avg_ui_time = sum(ui_times) / len(ui_times)
                if avg_ui_time <= self.validation_criteria["ui_load_time_max"]:
                    logger.info(f"✅ UI平均加载时间: {avg_ui_time:.2f}ms")
                    self.deployment_status["performance_verified"] = True
                    return True
                else:
                    logger.error(f"❌ UI加载时间过长: {avg_ui_time:.2f}ms")
                    return False
            
            return False
            
        except Exception as e:
            logger.error(f"❌ 性能验证失败: {e}")
            return False
    
    async def _verify_stability(self) -> bool:
        """验证稳定性"""
        logger.info("🔒 验证系统稳定性...")
        
        try:
            import requests
            
            test_duration = self.validation_criteria["stability_test_duration"]
            start_time = time.time()
            successful_requests = 0
            total_requests = 0
            
            logger.info(f"开始{test_duration}秒稳定性测试...")
            
            while time.time() - start_time < test_duration:
                try:
                    response = requests.get(f"{self.api_url}/api/v1/health/ping", timeout=5)
                    total_requests += 1
                    
                    if response.status_code == 200:
                        successful_requests += 1
                    
                except:
                    total_requests += 1
                
                await asyncio.sleep(2)  # 每2秒测试一次
            
            if total_requests > 0:
                stability_rate = successful_requests / total_requests
                logger.info(f"稳定性测试结果: {successful_requests}/{total_requests} ({stability_rate:.1%})")
                
                if stability_rate >= 0.95:  # 95%稳定性要求
                    self.deployment_status["stability_verified"] = True
                    logger.info("✅ 系统稳定性验证通过")
                    return True
                else:
                    logger.error(f"❌ 系统稳定性不足: {stability_rate:.1%}")
                    return False
            
            return False
            
        except Exception as e:
            logger.error(f"❌ 稳定性验证失败: {e}")
            return False
    
    async def _verify_functionality(self) -> bool:
        """验证功能"""
        logger.info("🔧 验证核心功能...")
        
        try:
            import requests
            
            # 核心功能端点测试
            core_endpoints = [
                f"{self.api_url}/api/v1/data/basic-stats",
                f"{self.api_url}/api/v1/prediction/status",
                f"{self.api_url}/api/v1/health/performance/dashboard"
            ]
            
            successful_functions = 0
            
            for endpoint in core_endpoints:
                try:
                    response = requests.get(endpoint, timeout=10)
                    if response.status_code in [200, 404, 503]:  # 包容预期状态码
                        successful_functions += 1
                except:
                    pass
            
            functionality_rate = successful_functions / len(core_endpoints)
            
            if functionality_rate >= 0.8:  # 80%功能可用率
                logger.info(f"✅ 核心功能可用率: {functionality_rate:.1%}")
                return True
            else:
                logger.error(f"❌ 核心功能可用率不足: {functionality_rate:.1%}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 功能验证失败: {e}")
            return False
    
    async def monitor_production_health(self, duration_minutes: int = 10) -> Dict[str, Any]:
        """监控生产环境健康状态"""
        logger.info(f"📊 开始{duration_minutes}分钟生产环境健康监控...")
        
        monitoring_data = {
            "start_time": datetime.now().isoformat(),
            "duration_minutes": duration_minutes,
            "health_checks": [],
            "performance_metrics": [],
            "alerts": [],
            "summary": {}
        }
        
        try:
            import requests
            
            end_time = time.time() + (duration_minutes * 60)
            check_interval = 30  # 30秒检查一次
            
            while time.time() < end_time:
                # 健康检查
                try:
                    start_time = time.time()
                    response = requests.get(f"{self.api_url}/api/v1/health/detailed", timeout=10)
                    response_time = (time.time() - start_time) * 1000
                    
                    health_data = {
                        "timestamp": datetime.now().isoformat(),
                        "response_time_ms": round(response_time, 2),
                        "status_code": response.status_code,
                        "healthy": response.status_code in [200, 503]
                    }
                    
                    if response.status_code == 200:
                        try:
                            health_detail = response.json()
                            health_data["overall_healthy"] = health_detail.get("overall_healthy", False)
                            health_data["components"] = len(health_detail.get("components", []))
                        except:
                            pass
                    
                    monitoring_data["health_checks"].append(health_data)
                    
                except Exception as e:
                    monitoring_data["alerts"].append({
                        "timestamp": datetime.now().isoformat(),
                        "type": "health_check_failed",
                        "message": str(e)
                    })
                
                # 性能指标
                try:
                    start_time = time.time()
                    response = requests.get(f"{self.api_url}/api/v1/health/ping", timeout=5)
                    ping_time = (time.time() - start_time) * 1000
                    
                    monitoring_data["performance_metrics"].append({
                        "timestamp": datetime.now().isoformat(),
                        "ping_time_ms": round(ping_time, 2),
                        "status_code": response.status_code
                    })
                    
                except Exception as e:
                    monitoring_data["alerts"].append({
                        "timestamp": datetime.now().isoformat(),
                        "type": "performance_check_failed",
                        "message": str(e)
                    })
                
                await asyncio.sleep(check_interval)
            
            # 生成监控摘要
            monitoring_data["summary"] = self._generate_monitoring_summary(monitoring_data)
            monitoring_data["end_time"] = datetime.now().isoformat()
            
            logger.info("✅ 生产环境健康监控完成")
            return monitoring_data
            
        except Exception as e:
            logger.error(f"❌ 生产环境健康监控失败: {e}")
            monitoring_data["error"] = str(e)
            return monitoring_data
    
    def _generate_monitoring_summary(self, monitoring_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成监控摘要"""
        health_checks = monitoring_data.get("health_checks", [])
        performance_metrics = monitoring_data.get("performance_metrics", [])
        alerts = monitoring_data.get("alerts", [])
        
        summary = {
            "total_health_checks": len(health_checks),
            "successful_health_checks": sum(1 for h in health_checks if h.get("healthy", False)),
            "total_performance_checks": len(performance_metrics),
            "total_alerts": len(alerts),
            "health_check_success_rate": 0,
            "avg_response_time_ms": 0,
            "avg_ping_time_ms": 0
        }
        
        if health_checks:
            summary["health_check_success_rate"] = summary["successful_health_checks"] / summary["total_health_checks"]
            response_times = [h.get("response_time_ms", 0) for h in health_checks]
            summary["avg_response_time_ms"] = round(sum(response_times) / len(response_times), 2)
        
        if performance_metrics:
            ping_times = [p.get("ping_time_ms", 0) for p in performance_metrics]
            summary["avg_ping_time_ms"] = round(sum(ping_times) / len(ping_times), 2)
        
        return summary
    
    def generate_deployment_report(self, monitoring_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """生成部署报告"""
        report = {
            "deployment_timestamp": datetime.now().isoformat(),
            "deployment_status": self.deployment_status,
            "validation_criteria": self.validation_criteria,
            "deployment_successful": all(self.deployment_status.values()),
            "services": {
                "api_url": self.api_url,
                "ui_url": self.ui_url
            }
        }
        
        if monitoring_data:
            report["monitoring_data"] = monitoring_data
        
        # 部署建议
        if report["deployment_successful"]:
            report["recommendation"] = "生产环境部署成功，系统运行正常"
        else:
            failed_checks = [k for k, v in self.deployment_status.items() if not v]
            report["recommendation"] = f"部署存在问题，需要检查: {', '.join(failed_checks)}"
        
        return report

async def main():
    """主函数"""
    validator = ProductionDeploymentValidator()
    
    try:
        # 1. 执行生产环境部署
        deployment_success = await validator.deploy_to_production()
        
        if not deployment_success:
            logger.error("❌ 生产环境部署失败")
            return 1
        
        # 2. 监控生产环境健康状态
        monitoring_data = await validator.monitor_production_health(duration_minutes=5)
        
        # 3. 生成部署报告
        report = validator.generate_deployment_report(monitoring_data)
        
        # 4. 保存部署报告
        report_file = Path("production_deployment_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📄 生产环境部署报告已保存到: {report_file}")
        
        # 5. 输出部署结果
        if report["deployment_successful"]:
            logger.info("🎉 生产环境部署和验证完成！")
            logger.info(f"API服务: {validator.api_url}")
            logger.info(f"UI服务: {validator.ui_url}")
            return 0
        else:
            logger.error("❌ 生产环境部署验证失败")
            return 1
            
    except KeyboardInterrupt:
        logger.info("部署被用户中断")
        return 1
    except Exception as e:
        logger.error(f"生产环境部署失败: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
