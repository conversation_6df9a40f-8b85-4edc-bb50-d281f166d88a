"""
多算法特征重要性排序引擎

集成多种算法计算特征重要性，为福彩3D预测提供最优特征选择
"""

import warnings
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
from scipy.stats import pearsonr, spearmanr
from sklearn.ensemble import RandomForestRegressor
from sklearn.feature_selection import mutual_info_regression
from sklearn.preprocessing import StandardScaler

warnings.filterwarnings('ignore')


class MultiAlgorithmFeatureRanking:
    """多算法特征重要性排序引擎"""
    
    def __init__(self):
        self.ranking_algorithms = {
            "mutual_information": self._mutual_information_ranking,
            "random_forest": self._random_forest_ranking,
            "correlation_analysis": self._correlation_ranking,
            "lstm_attention": self._lstm_attention_ranking,
            "custom_lottery": self._lottery_specific_ranking
        }
        self.scaler = StandardScaler()
        self.feature_cache = {}
    
    def calculate_ensemble_feature_importance(self, model_id: str, features: Dict[str, np.ndarray], 
                                            targets: np.ndarray) -> Dict[str, float]:
        """
        集成多种算法计算特征重要性
        
        Args:
            model_id: 模型ID
            features: 特征字典 {特征名: 特征值数组}
            targets: 目标值数组
            
        Returns:
            特征重要性字典 {特征名: 重要性分数}
        """
        if not features or len(targets) == 0:
            return {}
        
        importance_scores = {}
        algorithm_weights = self._get_algorithm_weights(model_id)
        
        # 准备特征矩阵
        feature_matrix, feature_names = self._prepare_feature_matrix(features)
        
        if feature_matrix.shape[0] == 0:
            return {}
        
        for algo_name, algo_func in self.ranking_algorithms.items():
            try:
                scores = algo_func(feature_matrix, feature_names, targets)
                
                # 标准化分数
                normalized_scores = self._normalize_scores(scores)
                
                # 加权累加
                weight = algorithm_weights.get(algo_name, 0.2)
                for feature_name, score in normalized_scores.items():
                    importance_scores[feature_name] = importance_scores.get(feature_name, 0) + score * weight
                    
            except Exception as e:
                print(f"算法 {algo_name} 执行失败: {e}")
        
        # 福彩3D特定的重要性调整
        importance_scores = self._apply_lottery_specific_adjustments(importance_scores, model_id)
        
        return importance_scores
    
    def _prepare_feature_matrix(self, features: Dict[str, np.ndarray]) -> Tuple[np.ndarray, List[str]]:
        """准备特征矩阵"""
        feature_names = []
        feature_arrays = []
        
        for name, values in features.items():
            if isinstance(values, (list, np.ndarray)) and len(values) > 0:
                # 确保是数值类型
                try:
                    numeric_values = np.array(values, dtype=float)
                    if not np.isnan(numeric_values).all():
                        feature_names.append(name)
                        feature_arrays.append(numeric_values)
                except (ValueError, TypeError):
                    continue
        
        if not feature_arrays:
            return np.array([]), []
        
        # 确保所有特征数组长度一致
        min_length = min(len(arr) for arr in feature_arrays)
        feature_arrays = [arr[:min_length] for arr in feature_arrays]
        
        feature_matrix = np.column_stack(feature_arrays)
        
        # 处理缺失值
        feature_matrix = np.nan_to_num(feature_matrix, nan=0.0)
        
        return feature_matrix, feature_names
    
    def _mutual_information_ranking(self, feature_matrix: np.ndarray, 
                                  feature_names: List[str], targets: np.ndarray) -> Dict[str, float]:
        """互信息特征重要性"""
        try:
            # 确保目标值长度匹配
            min_length = min(len(feature_matrix), len(targets))
            feature_matrix = feature_matrix[:min_length]
            targets = targets[:min_length]
            
            mi_scores = mutual_info_regression(feature_matrix, targets, random_state=42)
            return dict(zip(feature_names, mi_scores))
        except Exception as e:
            print(f"互信息计算失败: {e}")
            return {name: 0.0 for name in feature_names}
    
    def _random_forest_ranking(self, feature_matrix: np.ndarray, 
                             feature_names: List[str], targets: np.ndarray) -> Dict[str, float]:
        """随机森林特征重要性"""
        try:
            # 确保目标值长度匹配
            min_length = min(len(feature_matrix), len(targets))
            feature_matrix = feature_matrix[:min_length]
            targets = targets[:min_length]
            
            rf = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
            rf.fit(feature_matrix, targets)
            return dict(zip(feature_names, rf.feature_importances_))
        except Exception as e:
            print(f"随机森林计算失败: {e}")
            return {name: 0.0 for name in feature_names}
    
    def _correlation_ranking(self, feature_matrix: np.ndarray, 
                           feature_names: List[str], targets: np.ndarray) -> Dict[str, float]:
        """相关性分析特征重要性"""
        correlations = {}
        
        # 确保目标值长度匹配
        min_length = min(len(feature_matrix), len(targets))
        feature_matrix = feature_matrix[:min_length]
        targets = targets[:min_length]
        
        for i, feature_name in enumerate(feature_names):
            try:
                feature_values = feature_matrix[:, i]
                
                # 计算皮尔逊相关系数
                pearson_corr, _ = pearsonr(feature_values, targets)

                # 计算斯皮尔曼相关系数
                spearman_corr, _ = spearmanr(feature_values, targets)

                # 取绝对值的平均作为重要性
                try:
                    p_val = float(pearson_corr) if isinstance(pearson_corr, (int, float)) and not np.isnan(pearson_corr) else 0.0
                    s_val = float(spearman_corr) if isinstance(spearman_corr, (int, float)) and not np.isnan(spearman_corr) else 0.0
                    correlations[feature_name] = (abs(p_val) + abs(s_val)) / 2
                except (TypeError, ValueError):
                    correlations[feature_name] = 0.0
                
            except Exception as e:
                correlations[feature_name] = 0.0
        
        return correlations
    
    def _lstm_attention_ranking(self, feature_matrix: np.ndarray, 
                              feature_names: List[str], targets: np.ndarray) -> Dict[str, float]:
        """LSTM注意力机制特征重要性（简化版本）"""
        # 简化实现：基于特征方差和目标相关性
        importance = {}
        
        # 确保目标值长度匹配
        min_length = min(len(feature_matrix), len(targets))
        feature_matrix = feature_matrix[:min_length]
        targets = targets[:min_length]
        
        for i, feature_name in enumerate(feature_names):
            try:
                feature_values = feature_matrix[:, i]
                
                # 计算特征方差（信息量）
                variance = np.var(feature_values)
                
                # 计算与目标的相关性
                correlation = abs(np.corrcoef(feature_values, targets)[0, 1])
                
                # 结合方差和相关性
                importance[feature_name] = variance * correlation if not np.isnan(correlation) else 0.0
                
            except Exception as e:
                importance[feature_name] = 0.0
        
        return importance

    def _lottery_specific_ranking(self, feature_matrix: np.ndarray,
                                feature_names: List[str], targets: np.ndarray) -> Dict[str, float]:
        """福彩3D特定的特征重要性算法"""
        scores = {}

        # 确保目标值长度匹配
        min_length = min(len(feature_matrix), len(targets))
        feature_matrix = feature_matrix[:min_length]
        targets = targets[:min_length]

        for i, feature_name in enumerate(feature_names):
            try:
                feature_values = feature_matrix[:, i]

                # 1. 预测稳定性评分
                stability_score = self._calculate_prediction_stability(feature_values, targets)

                # 2. 中奖模式识别能力
                pattern_score = self._calculate_pattern_recognition_ability(feature_values, targets)

                # 3. 号码分布预测能力
                distribution_score = self._calculate_distribution_prediction_ability(feature_values, targets)

                # 综合评分
                scores[feature_name] = (stability_score * 0.4 + pattern_score * 0.3 + distribution_score * 0.3)

            except Exception as e:
                scores[feature_name] = 0.0

        return scores

    def _calculate_prediction_stability(self, feature_values: np.ndarray, targets: np.ndarray) -> float:
        """计算预测稳定性"""
        try:
            # 计算特征值的变异系数
            cv = np.std(feature_values) / (np.mean(feature_values) + 1e-8)

            # 计算与目标的一致性
            correlation = abs(np.corrcoef(feature_values, targets)[0, 1])

            # 稳定性 = 低变异系数 + 高相关性
            stability = (1 / (1 + cv)) * correlation if not np.isnan(correlation) else 0.0

            return min(stability, 1.0)
        except:
            return 0.0

    def _calculate_pattern_recognition_ability(self, feature_values: np.ndarray, targets: np.ndarray) -> float:
        """计算模式识别能力"""
        try:
            # 简化的模式识别：计算特征值的周期性
            if len(feature_values) < 10:
                return 0.0

            # 计算自相关性
            autocorr = np.corrcoef(feature_values[:-1], feature_values[1:])[0, 1]

            # 计算与目标的非线性关系
            rank_corr_val, _ = spearmanr(feature_values, targets)
            rank_corr = abs(float(rank_corr_val)) if isinstance(rank_corr_val, (int, float)) and not np.isnan(rank_corr_val) else 0.0

            pattern_ability = (abs(autocorr) + rank_corr) / 2 if not np.isnan(autocorr) and not np.isnan(rank_corr) else 0.0

            return min(pattern_ability, 1.0)
        except:
            return 0.0

    def _calculate_distribution_prediction_ability(self, feature_values: np.ndarray, targets: np.ndarray) -> float:
        """计算分布预测能力"""
        try:
            # 计算特征值分布的熵
            hist, _ = np.histogram(feature_values, bins=10)
            hist = hist + 1e-8  # 避免log(0)
            entropy = -np.sum((hist / np.sum(hist)) * np.log(hist / np.sum(hist)))

            # 标准化熵值
            max_entropy = np.log(10)
            normalized_entropy = entropy / max_entropy

            # 计算与目标的相关性
            correlation = abs(np.corrcoef(feature_values, targets)[0, 1])

            # 分布预测能力 = 信息熵 * 相关性
            distribution_ability = normalized_entropy * correlation if not np.isnan(correlation) else 0.0

            return min(distribution_ability, 1.0)
        except:
            return 0.0

    def _normalize_scores(self, scores: Dict[str, float]) -> Dict[str, float]:
        """标准化分数到[0,1]范围"""
        if not scores:
            return {}

        values = list(scores.values())
        if not values or all(v == 0 for v in values):
            return scores

        min_val = min(values)
        max_val = max(values)

        if max_val == min_val:
            return {k: 1.0 for k in scores.keys()}

        normalized = {}
        for k, v in scores.items():
            normalized[k] = (v - min_val) / (max_val - min_val)

        return normalized

    def _get_algorithm_weights(self, model_id: str) -> Dict[str, float]:
        """获取不同模型的算法权重"""
        weights = {
            "markov_enhanced": {
                "mutual_information": 0.3,
                "random_forest": 0.2,
                "correlation_analysis": 0.2,
                "lstm_attention": 0.1,
                "custom_lottery": 0.2
            },
            "deep_learning_cnn_lstm": {
                "mutual_information": 0.2,
                "random_forest": 0.3,
                "correlation_analysis": 0.1,
                "lstm_attention": 0.3,
                "custom_lottery": 0.1
            },
            "trend_analyzer": {
                "mutual_information": 0.25,
                "random_forest": 0.25,
                "correlation_analysis": 0.3,
                "lstm_attention": 0.1,
                "custom_lottery": 0.1
            },
            "intelligent_fusion": {
                "mutual_information": 0.2,
                "random_forest": 0.2,
                "correlation_analysis": 0.2,
                "lstm_attention": 0.2,
                "custom_lottery": 0.2
            }
        }

        return weights.get(model_id, {
            "mutual_information": 0.2,
            "random_forest": 0.2,
            "correlation_analysis": 0.2,
            "lstm_attention": 0.2,
            "custom_lottery": 0.2
        })

    def _apply_lottery_specific_adjustments(self, importance_scores: Dict[str, float], model_id: str) -> Dict[str, float]:
        """应用福彩3D特定的重要性调整"""
        adjusted_scores = importance_scores.copy()

        # 福彩3D特定特征的权重调整
        lottery_feature_boosts = {
            "sum_": 1.2,      # 和值相关特征
            "span_": 1.1,     # 跨度相关特征
            "trial_": 1.15,   # 试机号相关特征
            "freq_": 1.1,     # 频率相关特征
            "pattern_": 1.05, # 模式相关特征
        }

        for feature_name, score in adjusted_scores.items():
            for prefix, boost in lottery_feature_boosts.items():
                if feature_name.startswith(prefix):
                    adjusted_scores[feature_name] = min(score * boost, 1.0)
                    break

        return adjusted_scores

    def get_top_features(self, importance_scores: Dict[str, float], top_k: int = 20) -> List[Tuple[str, float]]:
        """获取Top-K重要特征"""
        sorted_features = sorted(importance_scores.items(), key=lambda x: x[1], reverse=True)
        return sorted_features[:top_k]

    def analyze_feature_categories(self, importance_scores: Dict[str, float]) -> Dict[str, Dict[str, Any]]:
        """分析特征类别的重要性分布"""
        categories = {
            "basic_stats": [],      # 基础统计特征
            "time_series": [],      # 时间序列特征
            "combination": [],      # 组合特征
            "advanced": [],         # 高级特征
            "lottery_specific": []  # 福彩3D特定特征
        }

        for feature_name, score in importance_scores.items():
            if any(stat in feature_name for stat in ['mean', 'std', 'var', 'min', 'max']):
                categories["basic_stats"].append((feature_name, score))
            elif any(ts in feature_name for ts in ['lag', 'ma', 'volatility', 'momentum']):
                categories["time_series"].append((feature_name, score))
            elif any(comb in feature_name for comb in ['sum_', 'span_', 'odd_', 'big_']):
                categories["combination"].append((feature_name, score))
            elif any(lottery in feature_name for lottery in ['trial_', 'freq_', 'pattern_']):
                categories["lottery_specific"].append((feature_name, score))
            else:
                categories["advanced"].append((feature_name, score))

        # 计算每个类别的统计信息
        category_stats = {}
        for category, features in categories.items():
            if features:
                scores = [score for _, score in features]
                category_stats[category] = {
                    "count": len(features),
                    "avg_importance": np.mean(scores),
                    "max_importance": np.max(scores),
                    "top_features": sorted(features, key=lambda x: x[1], reverse=True)[:5]
                }
            else:
                category_stats[category] = {
                    "count": 0,
                    "avg_importance": 0.0,
                    "max_importance": 0.0,
                    "top_features": []
                }

        return category_stats
