"""
马尔可夫链集成适配器

将增强版马尔可夫链集成到现有预测系统
"""

import os
import sys
from datetime import datetime
from typing import Any, Dict, List, Optional

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

try:
    from .markov_enhanced import EnhancedMarkovChain
    from .pattern_prediction import PatternPredictor
except ImportError:
    from markov_enhanced import EnhancedMarkovChain

    from pattern_prediction import PatternPredictor


class MarkovIntegrationAdapter:
    """马尔可夫链集成适配器"""
    
    def __init__(self, db_path: str = None):
        """
        初始化集成适配器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path or os.path.join('data', 'lottery.db')
        
        # 初始化增强版马尔可夫链
        self.enhanced_markov = EnhancedMarkovChain(db_path=self.db_path, max_order=2)
        
        # 初始化原有模式预测器（用于对比）
        self.pattern_predictor = PatternPredictor()
        
        # 集成状态
        self.is_integrated = False
        self.integration_time = None
        
        # 性能对比数据
        self.performance_comparison = {
            'enhanced_markov': [],
            'pattern_predictor': []
        }
    
    def integrate_with_fusion_system(self):
        """与融合系统集成"""
        try:
            print("🔗 开始集成增强版马尔可夫链到融合系统...")
            
            # 训练增强版马尔可夫链
            print("   训练增强版马尔可夫链...")
            self.enhanced_markov.train(data_limit=1000)
            
            # 训练原有模式预测器
            print("   训练原有模式预测器...")
            self.pattern_predictor.train_pattern_model()
            
            self.is_integrated = True
            self.integration_time = datetime.now()
            
            print("✅ 集成完成！")
            return True
            
        except Exception as e:
            print(f"❌ 集成失败: {e}")
            return False
    
    def predict_enhanced(self, history: List[str], method: str = 'adaptive') -> Dict[str, Any]:
        """
        使用增强版马尔可夫链预测
        
        Args:
            history: 历史号码列表
            method: 预测方法 ('first_order', 'second_order', 'hybrid', 'adaptive', 'ensemble')
            
        Returns:
            预测结果
        """
        if not self.is_integrated:
            raise ValueError("系统未集成，请先调用integrate_with_fusion_system()")
        
        try:
            if method == 'first_order':
                return self.enhanced_markov.predict_first_order(history[-1])
            elif method == 'second_order' and len(history) >= 2:
                return self.enhanced_markov.predict_second_order(history[-2], history[-1])
            elif method == 'hybrid':
                return self.enhanced_markov.predict_hybrid(history)
            elif method == 'adaptive':
                return self.enhanced_markov.predict_adaptive(history)
            elif method == 'ensemble':
                return self.enhanced_markov.ensemble_predict(history)
            else:
                # 默认使用自适应预测
                return self.enhanced_markov.predict_adaptive(history)
                
        except Exception as e:
            print(f"增强预测失败: {e}")
            # 回退到一阶预测
            return self.enhanced_markov.predict_first_order(history[-1])
    
    def predict_original(self, history: List[str]) -> Dict[str, Any]:
        """
        使用原有模式预测器预测
        
        Args:
            history: 历史号码列表
            
        Returns:
            预测结果
        """
        try:
            # 使用原有的模式预测
            prediction = self.pattern_predictor.predict_next_numbers(history)
            
            return {
                'numbers': prediction.get('numbers', '000'),
                'method': 'original_pattern',
                'confidence': prediction.get('confidence', 0.3)
            }
            
        except Exception as e:
            print(f"原有预测失败: {e}")
            return {
                'numbers': '000',
                'method': 'original_pattern',
                'confidence': 0.1,
                'error': str(e)
            }
    
    def compare_predictions(self, history: List[str]) -> Dict[str, Any]:
        """
        对比不同预测方法的结果
        
        Args:
            history: 历史号码列表
            
        Returns:
            对比结果
        """
        results = {}
        
        # 增强版预测
        try:
            results['enhanced_adaptive'] = self.predict_enhanced(history, 'adaptive')
            results['enhanced_hybrid'] = self.predict_enhanced(history, 'hybrid')
            results['enhanced_ensemble'] = self.predict_enhanced(history, 'ensemble')
        except Exception as e:
            results['enhanced_error'] = str(e)
        
        # 原有预测
        try:
            results['original'] = self.predict_original(history)
        except Exception as e:
            results['original_error'] = str(e)
        
        # 生成推荐
        recommendations = self._generate_recommendations(results)
        
        return {
            'predictions': results,
            'recommendations': recommendations,
            'comparison_time': datetime.now().isoformat()
        }
    
    def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """生成预测推荐"""
        recommendations = []
        
        # 检查增强版预测
        if 'enhanced_adaptive' in results:
            adaptive_conf = results['enhanced_adaptive'].get('confidence', 0)
            if adaptive_conf > 0.5:
                recommendations.append(f"推荐使用自适应预测（置信度: {adaptive_conf:.3f}）")
        
        if 'enhanced_ensemble' in results:
            ensemble_conf = results['enhanced_ensemble'].get('confidence', 0)
            ensemble_div = results['enhanced_ensemble'].get('diversity', 0)
            if ensemble_div > 0.7:
                recommendations.append(f"集成预测多样性良好（多样性: {ensemble_div:.3f}）")
        
        # 检查原有预测
        if 'original' in results:
            original_conf = results['original'].get('confidence', 0)
            if original_conf > 0.4:
                recommendations.append(f"原有模式预测可作为参考（置信度: {original_conf:.3f}）")
        
        if not recommendations:
            recommendations.append("建议使用混合预测策略以提高稳定性")
        
        return recommendations
    
    def get_integration_status(self) -> Dict[str, Any]:
        """获取集成状态"""
        return {
            'is_integrated': self.is_integrated,
            'integration_time': self.integration_time.isoformat() if self.integration_time else None,
            'enhanced_markov_trained': self.enhanced_markov.is_trained,
            'enhanced_markov_info': self.enhanced_markov.get_model_info(),
            'performance_stats': self.enhanced_markov.get_performance_stats(),
            'sparse_matrix_stats': self.enhanced_markov.get_sparse_matrix_stats()
        }
    
    def benchmark_performance(self, test_data: List[str], num_tests: int = 10) -> Dict[str, Any]:
        """
        性能基准测试
        
        Args:
            test_data: 测试数据
            num_tests: 测试次数
            
        Returns:
            基准测试结果
        """
        if not self.is_integrated:
            raise ValueError("系统未集成")
        
        print(f"🏃 开始性能基准测试（{num_tests}次测试）...")
        
        results = {
            'enhanced_times': [],
            'original_times': [],
            'enhanced_predictions': [],
            'original_predictions': []
        }
        
        import time
        
        for i in range(num_tests):
            history = test_data[:-(num_tests-i)]
            
            # 测试增强版预测性能
            start_time = time.time()
            try:
                enhanced_pred = self.predict_enhanced(history, 'adaptive')
                enhanced_time = time.time() - start_time
                results['enhanced_times'].append(enhanced_time)
                results['enhanced_predictions'].append(enhanced_pred)
            except Exception as e:
                print(f"增强预测测试失败: {e}")
            
            # 测试原有预测性能
            start_time = time.time()
            try:
                original_pred = self.predict_original(history)
                original_time = time.time() - start_time
                results['original_times'].append(original_time)
                results['original_predictions'].append(original_pred)
            except Exception as e:
                print(f"原有预测测试失败: {e}")
        
        # 计算统计信息
        import numpy as np
        
        benchmark_stats = {
            'enhanced_avg_time': np.mean(results['enhanced_times']) if results['enhanced_times'] else 0,
            'original_avg_time': np.mean(results['original_times']) if results['original_times'] else 0,
            'enhanced_std_time': np.std(results['enhanced_times']) if results['enhanced_times'] else 0,
            'original_std_time': np.std(results['original_times']) if results['original_times'] else 0,
            'num_tests': num_tests,
            'test_completed': len(results['enhanced_times'])
        }
        
        # 计算性能提升
        if benchmark_stats['original_avg_time'] > 0:
            speedup = benchmark_stats['original_avg_time'] / benchmark_stats['enhanced_avg_time']
            benchmark_stats['speedup_ratio'] = speedup
        
        print(f"✅ 基准测试完成")
        print(f"   增强版平均耗时: {benchmark_stats['enhanced_avg_time']:.4f}秒")
        print(f"   原有版平均耗时: {benchmark_stats['original_avg_time']:.4f}秒")
        
        return {
            'benchmark_stats': benchmark_stats,
            'detailed_results': results
        }


if __name__ == "__main__":
    # 测试集成适配器
    adapter = MarkovIntegrationAdapter()
    
    try:
        # 集成系统
        success = adapter.integrate_with_fusion_system()
        
        if success:
            # 获取集成状态
            status = adapter.get_integration_status()
            print(f"\n集成状态: {status}")
            
            # 测试预测
            test_history = ['123', '456', '789', '012']
            
            # 对比预测
            comparison = adapter.compare_predictions(test_history)
            print(f"\n预测对比: {comparison}")
            
            # 性能基准测试
            benchmark = adapter.benchmark_performance(test_history, num_tests=5)
            print(f"\n性能基准: {benchmark['benchmark_stats']}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
