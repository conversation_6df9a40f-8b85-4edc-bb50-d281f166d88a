#!/usr/bin/env python3
"""
集成测试脚本：测试调度器更新后的自动通知机制
"""

import requests
import time
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from src.data.incremental_updater import IncrementalUpdater

def test_scheduler_notification():
    """测试调度器通知机制"""
    print("🔧 开始集成测试：调度器自动通知机制")
    
    # 1. 检查API服务状态
    print("\n1. 检查API服务状态...")
    try:
        response = requests.get("http://127.0.0.1:8888/api/v1/stats/basic", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API服务正常，当前记录数: {data.get('total_records', 0)}")
        else:
            print(f"❌ API服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接API服务: {e}")
        return False
    
    # 2. 获取更新前状态
    print("\n2. 获取更新前状态...")
    try:
        response = requests.get("http://127.0.0.1:8888/api/v1/data/status", timeout=5)
        if response.status_code == 200:
            before_status = response.json()
            print(f"✅ 更新前状态获取成功")
            print(f"   数据库记录数: {before_status['data']['database']['count']}")
            print(f"   Polars记录数: {before_status['data']['polars_engine']['count']}")
            print(f"   需要刷新: {before_status['data']['needs_refresh']}")
        else:
            print(f"❌ 获取状态失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取状态异常: {e}")
        return False
    
    # 3. 模拟调度器的_notify_api_refresh调用
    print("\n3. 模拟调度器通知API刷新...")
    try:
        # 创建增量更新器实例
        updater = IncrementalUpdater()
        
        # 直接调用通知方法（模拟调度器行为）
        updater._notify_api_refresh(0, "2025188")  # 模拟无新记录的通知
        print("✅ 调度器通知发送成功")
        
    except Exception as e:
        print(f"❌ 调度器通知失败: {e}")
        return False
    
    # 4. 验证API响应
    print("\n4. 验证API响应...")
    time.sleep(2)  # 等待处理完成
    
    try:
        response = requests.get("http://127.0.0.1:8888/api/v1/data/status", timeout=5)
        if response.status_code == 200:
            after_status = response.json()
            print(f"✅ 更新后状态获取成功")
            print(f"   数据库记录数: {after_status['data']['database']['count']}")
            print(f"   Polars记录数: {after_status['data']['polars_engine']['count']}")
            print(f"   需要刷新: {after_status['data']['needs_refresh']}")
        else:
            print(f"❌ 获取状态失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取状态异常: {e}")
        return False
    
    # 5. 测试UI界面响应
    print("\n5. 测试UI界面数据新鲜度...")
    try:
        response = requests.get("http://127.0.0.1:8888/api/v1/stats/basic", timeout=5)
        if response.status_code == 200:
            data = response.json()
            freshness = data.get('data_freshness', {})
            print(f"✅ UI数据新鲜度信息:")
            print(f"   自动刷新启用: {freshness.get('auto_refresh_enabled', False)}")
            print(f"   使用缓存: {freshness.get('cache_used', False)}")
            print(f"   最后检查时间: {freshness.get('last_refresh_check', 'N/A')}")
        else:
            print(f"❌ 获取UI数据失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取UI数据异常: {e}")
        return False
    
    print("\n🎉 集成测试完成！所有功能正常工作。")
    return True

def test_performance():
    """性能测试"""
    print("\n🚀 开始性能测试...")
    
    # 测试数据刷新性能
    print("\n1. 测试数据刷新性能...")
    times = []
    
    for i in range(3):
        print(f"   第{i+1}次测试...")
        start_time = time.time()
        
        try:
            response = requests.post("http://127.0.0.1:8888/api/v1/data/refresh", timeout=30)
            if response.status_code == 200:
                result = response.json()
                refresh_time = result['data']['refresh_time_ms']
                times.append(refresh_time)
                print(f"   ✅ 刷新时间: {refresh_time:.2f}ms")
            else:
                print(f"   ❌ 刷新失败: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 刷新异常: {e}")
    
    if times:
        avg_time = sum(times) / len(times)
        max_time = max(times)
        min_time = min(times)
        
        print(f"\n📊 性能测试结果:")
        print(f"   平均刷新时间: {avg_time:.2f}ms")
        print(f"   最大刷新时间: {max_time:.2f}ms")
        print(f"   最小刷新时间: {min_time:.2f}ms")
        
        # 性能标准检查
        if avg_time < 5000:  # 5秒内
            print("   ✅ 性能测试通过（< 5秒）")
            return True
        else:
            print("   ⚠️ 性能测试警告（> 5秒）")
            return False
    else:
        print("   ❌ 性能测试失败：无有效数据")
        return False

if __name__ == "__main__":
    print("🧪 开始数据引擎缓存问题修复项目的集成测试")
    
    # 运行集成测试
    integration_success = test_scheduler_notification()
    
    # 运行性能测试
    performance_success = test_performance()
    
    # 总结
    print("\n" + "="*50)
    print("📋 测试总结:")
    print(f"   集成测试: {'✅ 通过' if integration_success else '❌ 失败'}")
    print(f"   性能测试: {'✅ 通过' if performance_success else '❌ 失败'}")
    
    if integration_success and performance_success:
        print("\n🎉 所有测试通过！数据引擎缓存问题修复项目验收成功！")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，需要进一步调试。")
        sys.exit(1)
