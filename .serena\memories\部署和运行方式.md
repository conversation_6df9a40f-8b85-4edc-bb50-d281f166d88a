# 福彩3D预测系统部署和运行方式

## 系统环境要求
### 硬件要求
- **操作系统**: Windows 10 64位 (版本1903或更高)
- **处理器**: Intel i5或AMD Ryzen 5以上
- **内存**: 8GB RAM (推荐16GB)
- **存储**: 2GB可用空间
- **网络**: 稳定的互联网连接

### 软件环境
- **Python版本**: 3.11.9 (严格要求)
- **包管理器**: pip 或 uv
- **开发工具**: Cursor IDE (推荐)
- **浏览器**: Chrome/Edge/Firefox (支持WebSocket)

## 项目结构和配置
### 核心配置文件
```
d:/github/3dyuce/
├── pyproject.toml          # 项目配置和依赖
├── requirements-prod.txt   # 生产环境依赖
├── mcp.json               # MCP服务配置
├── start.bat              # Windows启动脚本
├── 一键启动.bat            # 一键启动脚本
└── 一键启动.py             # Python启动脚本
```

### 环境配置
```toml
# pyproject.toml
[project]
name = "福彩3d-predictor"
version = "2025.1.0"
requires-python = ">=3.11,<3.12"

[project.dependencies]
streamlit = ">=1.28.0,<1.32.0"
fastapi = ">=0.104.0,<0.111.0"
polars = ">=0.19.0,<0.21.0"
torch = ">=2.1.0,<2.3.0"
# ... 其他依赖
```

## 启动方式详解
### 1. 一键启动 (推荐)
```batch
# 一键启动.bat
@echo off
echo 启动福彩3D预测系统...
echo.

echo [1/2] 启动API服务...
start "API服务" cmd /k "python start_api.py"

echo [2/2] 等待5秒后启动界面...
timeout /t 5 /nobreak >nul

echo 启动Streamlit界面...
streamlit run src/ui/main.py --server.port 8501 --server.address 127.0.0.1

pause
```

### 2. Python一键启动
```python
# 一键启动.py
import subprocess
import time
import sys
import os

def start_system():
    print("🚀 启动福彩3D预测系统...")
    
    # 1. 启动API服务
    print("📡 启动API服务...")
    api_process = subprocess.Popen([
        sys.executable, "start_api.py"
    ], creationflags=subprocess.CREATE_NEW_CONSOLE)
    
    # 2. 等待API服务启动
    print("⏳ 等待API服务启动...")
    time.sleep(5)
    
    # 3. 启动Streamlit界面
    print("🖥️ 启动用户界面...")
    subprocess.run([
        "streamlit", "run", "src/ui/main.py",
        "--server.port", "8501",
        "--server.address", "127.0.0.1"
    ])

if __name__ == "__main__":
    start_system()
```

### 3. 分步启动
```bash
# 步骤1: 启动API服务
python start_api.py

# 步骤2: 启动Streamlit界面
streamlit run src/ui/main.py --server.port 8501
```

### 4. 开发模式启动
```bash
# API服务 (开发模式)
uvicorn src.api.production_main:app --host 127.0.0.1 --port 8888 --reload

# Streamlit界面 (开发模式)
streamlit run src/ui/main.py --server.port 8501 --server.address 127.0.0.1
```

## 服务配置详解
### API服务配置 (start_api.py)
```python
import uvicorn
from src.api.production_main import app

if __name__ == "__main__":
    uvicorn.run(
        app,
        host="127.0.0.1",      # 绑定到本地
        port=8888,             # API端口
        log_level="info",      # 日志级别
        access_log=True,       # 访问日志
        reload=False           # 生产模式不重载
    )
```

### Streamlit配置
```toml
# .streamlit/config.toml
[server]
port = 8501
address = "127.0.0.1"
maxUploadSize = 200
enableCORS = false
enableXsrfProtection = false

[browser]
gatherUsageStats = false
serverAddress = "127.0.0.1"
serverPort = 8501

[theme]
primaryColor = "#FF6B6B"
backgroundColor = "#FFFFFF"
secondaryBackgroundColor = "#F0F2F6"
textColor = "#262730"
```

## 端口配置和网络
### 默认端口分配
```python
PORTS = {
    "api_service": 8888,        # FastAPI服务
    "streamlit_ui": 8501,       # Streamlit界面
    "websocket": 8888,          # WebSocket (复用API端口)
    "redis_cache": 6379,        # Redis缓存 (可选)
    "serena_dashboard": 24282   # Serena仪表板
}
```

### 网络访问地址
```python
URLS = {
    "api_docs": "http://127.0.0.1:8888/docs",
    "api_health": "http://127.0.0.1:8888/health", 
    "streamlit_ui": "http://127.0.0.1:8501",
    "serena_dashboard": "http://127.0.0.1:24282/dashboard/index.html"
}
```

## 数据库和存储
### SQLite数据库
```python
DATABASE_CONFIG = {
    "main_db": "data/lottery.db",           # 主数据库
    "bug_db": "data/bug_detection.db",     # Bug检测数据库
    "model_db": "data/model_library.db",   # 模型库数据库
    "backup_dir": "data/backups/",         # 备份目录
    "cache_dir": "data/cache/"             # 缓存目录
}
```

### 数据文件结构
```
data/
├── lottery.db              # 主数据库
├── bug_detection.db        # Bug检测数据库
├── cache/                  # 缓存文件
│   ├── latest_prediction.json
│   ├── quality_history.json
│   └── fusion_state.json
├── backups/               # 数据备份
├── logs/                  # 日志文件
└── validation_reports/    # 验证报告
```

## 调度器和自动化
### 调度器配置 (scheduler_config.json)
```json
{
    "data_update": {
        "enabled": true,
        "schedule": "21:30",
        "timezone": "Asia/Shanghai",
        "retry_attempts": 3,
        "retry_delay": 300
    },
    "log_cleanup": {
        "enabled": true,
        "schedule": "02:00",
        "retention_days": 30
    },
    "performance_check": {
        "enabled": true,
        "interval": 3600
    }
}
```

### 自动任务
```python
# 定时任务列表
SCHEDULED_TASKS = {
    "data_update": {
        "function": "data_update_task",
        "trigger": "cron",
        "hour": 21,
        "minute": 30
    },
    "log_cleanup": {
        "function": "log_cleanup_task", 
        "trigger": "cron",
        "hour": 2,
        "minute": 0
    },
    "health_check": {
        "function": "health_check_task",
        "trigger": "interval",
        "seconds": 3600
    }
}
```

## 监控和日志
### 日志配置
```python
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "detailed": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        }
    },
    "handlers": {
        "file": {
            "class": "logging.handlers.RotatingFileHandler",
            "filename": "logs/system.log",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5
        }
    },
    "root": {
        "level": "INFO",
        "handlers": ["file"]
    }
}
```

### 性能监控
```python
# 监控指标
MONITORING_METRICS = {
    "api_response_time": "API响应时间",
    "database_query_time": "数据库查询时间", 
    "prediction_accuracy": "预测准确率",
    "system_memory_usage": "系统内存使用",
    "websocket_connections": "WebSocket连接数"
}
```

## 故障排除和维护
### 常见问题解决
```python
# 故障排除检查清单
TROUBLESHOOTING_CHECKLIST = {
    "port_conflict": "检查端口8888和8501是否被占用",
    "python_version": "确认Python版本为3.11.9",
    "dependencies": "检查依赖包是否正确安装",
    "database_access": "确认数据库文件权限",
    "network_connection": "检查网络连接和防火墙设置"
}
```

### 维护脚本
```python
# 系统维护工具
MAINTENANCE_SCRIPTS = {
    "backup_database.py": "数据库备份",
    "clear_cache.py": "清理缓存文件",
    "check_system_health.py": "系统健康检查",
    "update_dependencies.py": "更新依赖包",
    "restart_services.py": "重启服务"
}
```

## 部署最佳实践
### 1. 环境隔离
- 使用虚拟环境隔离Python依赖
- 配置专用的数据目录
- 设置适当的文件权限

### 2. 安全配置
- API服务绑定到127.0.0.1 (本地访问)
- 禁用不必要的网络服务
- 定期更新依赖包

### 3. 性能优化
- 配置适当的缓存策略
- 优化数据库查询
- 监控系统资源使用

### 4. 备份策略
- 定期备份数据库文件
- 保留配置文件副本
- 记录重要的系统变更