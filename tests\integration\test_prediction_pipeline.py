"""
集成测试和性能验证
执行端到端预测流程测试，API集成测试，性能基准测试
"""

import unittest
import asyncio
import time
import tempfile
import os
import json
import requests
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
import sys
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from src.core.model_performance_tracker import ModelPerformanceTracker
from src.core.accuracy_focused_fusion import AccuracyFocusedFusion, ModelPrediction
from src.core.number_ranking_system import NumberRankingSystem
from src.data.prediction_repository import PredictionRepository

# 配置测试日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestPredictionPipeline(unittest.TestCase):
    """预测流程集成测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时数据库
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db_path = self.temp_db.name
        
        # 初始化系统组件
        self.tracker = ModelPerformanceTracker(db_path=self.db_path)
        self.fusion = AccuracyFocusedFusion(self.tracker)
        self.ranking = NumberRankingSystem(self.fusion, self.tracker, db_path=self.db_path)
        self.repository = PredictionRepository(db_path=self.db_path)
        
        # 准备测试数据
        self._prepare_test_data()
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def _prepare_test_data(self):
        """准备测试数据"""
        # 添加历史预测数据
        models = ['markov_enhanced', 'deep_learning_cnn_lstm', 'trend_analyzer', 'intelligent_fusion']
        
        for i in range(50):
            period = f"2025{i+1:03d}"
            actual = f"{i%10}{(i+1)%10}{(i+2)%10}"
            
            for j, model in enumerate(models):
                # 模拟不同模型的准确率
                accuracy_rates = [0.6, 0.7, 0.5, 0.8]  # 不同模型的基础准确率
                is_correct = (i + j) % 10 < accuracy_rates[j] * 10
                
                predicted = actual if is_correct else f"{(i+j)%10}{(i+j+1)%10}{(i+j+2)%10}"
                confidence = 0.5 + (j * 0.1) + (i % 5) * 0.05
                
                self.tracker.track_prediction(model, predicted, actual, period, confidence)
    
    def create_test_predictions(self) -> list:
        """创建测试预测数据"""
        predictions = []
        
        # 模型预测数据
        models_data = [
            ("markov_enhanced", "123", 0.75, {"123": 0.75, "456": 0.6, "789": 0.4, "111": 0.3, "222": 0.2}),
            ("deep_learning_cnn_lstm", "123", 0.8, {"123": 0.8, "333": 0.7, "444": 0.5, "555": 0.3, "666": 0.1}),
            ("trend_analyzer", "456", 0.85, {"456": 0.85, "123": 0.6, "777": 0.4, "888": 0.2, "999": 0.1}),
            ("intelligent_fusion", "123", 0.7, {"123": 0.7, "000": 0.5, "111": 0.4, "222": 0.3, "333": 0.2})
        ]
        
        for model_name, top_candidate, top_confidence, all_candidates in models_data:
            pred = ModelPrediction(
                model_name=model_name,
                top_candidate=top_candidate,
                top_confidence=top_confidence,
                all_candidates=all_candidates
            )
            predictions.append(pred)
        
        return predictions
    
    def test_end_to_end_prediction_flow(self):
        """测试端到端预测流程"""
        logger.info("开始端到端预测流程测试")
        
        start_time = time.time()
        
        # 1. 创建预测数据
        predictions = self.create_test_predictions()
        self.assertEqual(len(predictions), 4)
        
        # 2. 执行融合预测
        best_result = self.fusion.get_single_best_prediction(predictions)
        self.assertIsNotNone(best_result)
        self.assertIsNotNone(best_result.number)
        self.assertGreater(best_result.confidence, 0)
        
        # 3. 生成排行榜
        ranking_list = self.ranking.generate_ranking_list(predictions, top_n=10)
        self.assertGreater(len(ranking_list), 0)
        self.assertLessEqual(len(ranking_list), 10)
        
        # 4. 验证排行榜排序
        for i in range(len(ranking_list) - 1):
            self.assertGreaterEqual(ranking_list[i].confidence, ranking_list[i+1].confidence)
        
        # 5. 保存预测结果
        period_number = "2025999"
        success = self.repository.save_fusion_prediction({
            'period_number': period_number,
            'best_number': best_result.number,
            'confidence': best_result.confidence,
            'fusion_method': best_result.method,
            'model_weights': self.tracker.calculate_dynamic_weights(),
            'fusion_details': best_result.fusion_details or {},
            'prediction_date': datetime.now()
        })
        self.assertTrue(success)
        
        # 6. 保存排行榜
        ranking_data = []
        for item in ranking_list:
            ranking_data.append({
                'number': item.number,
                'rank': item.rank,
                'confidence': item.confidence,
                'composite_score': item.composite_score,
                'model_support_count': item.model_support_count,
                'historical_hit_rate': item.historical_hit_rate,
                'recommendation_level': item.recommendation_level,
                'prediction_method': item.prediction_method
            })
        
        success = self.repository.save_prediction_ranking(period_number, ranking_data)
        self.assertTrue(success)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        logger.info(f"端到端预测流程完成，耗时: {execution_time:.3f}秒")
        
        # 性能验证：预测计算时间应小于5秒
        self.assertLess(execution_time, 5.0, "预测计算时间超过5秒")
    
    def test_prediction_consistency(self):
        """测试预测一致性"""
        logger.info("开始预测一致性测试")
        
        predictions = self.create_test_predictions()
        results = []
        
        # 执行10次相同条件的预测
        for i in range(10):
            result = self.fusion.get_single_best_prediction(predictions)
            results.append(result.number)
        
        # 计算一致性
        most_common = max(set(results), key=results.count)
        consistency_rate = results.count(most_common) / len(results)
        
        logger.info(f"预测一致性: {consistency_rate:.1%}")
        
        # 验证一致性≥90%
        self.assertGreaterEqual(consistency_rate, 0.9, f"预测一致性{consistency_rate:.1%}低于90%")
    
    def test_model_weight_adjustment(self):
        """测试模型权重动态调整"""
        logger.info("开始模型权重动态调整测试")
        
        # 获取初始权重
        initial_weights = self.tracker.calculate_dynamic_weights()
        logger.info(f"初始权重: {initial_weights}")
        
        # 添加新的预测数据（模拟某个模型表现更好）
        for i in range(10):
            period = f"2025{100+i:03d}"
            actual = f"{i%10}{(i+1)%10}{(i+2)%10}"
            
            # deep_learning_cnn_lstm模型100%准确
            self.tracker.track_prediction("deep_learning_cnn_lstm", actual, actual, period, 0.9)
            
            # 其他模型50%准确
            for model in ['markov_enhanced', 'trend_analyzer', 'intelligent_fusion']:
                predicted = actual if i % 2 == 0 else f"{(i+5)%10}{(i+6)%10}{(i+7)%10}"
                self.tracker.track_prediction(model, predicted, actual, period, 0.5)
        
        # 获取调整后的权重
        adjusted_weights = self.tracker.calculate_dynamic_weights()
        logger.info(f"调整后权重: {adjusted_weights}")
        
        # 验证deep_learning_cnn_lstm权重增加
        self.assertGreater(
            adjusted_weights['deep_learning_cnn_lstm'],
            initial_weights['deep_learning_cnn_lstm'],
            "表现更好的模型权重应该增加"
        )
    
    def test_ranking_quality_metrics(self):
        """测试排行榜质量指标"""
        logger.info("开始排行榜质量指标测试")
        
        predictions = self.create_test_predictions()
        ranking_list = self.ranking.generate_ranking_list(predictions, top_n=20)
        
        # 验证排行榜质量
        self.assertGreater(len(ranking_list), 0, "排行榜不能为空")
        
        # 验证置信度分布
        confidences = [item.confidence for item in ranking_list]
        self.assertGreater(max(confidences), 0.5, "最高置信度应大于50%")
        
        # 验证推荐等级分布
        levels = [item.recommendation_level for item in ranking_list]
        unique_levels = set(levels)
        self.assertGreater(len(unique_levels), 1, "应该有多种推荐等级")
        
        # 验证模型支持度
        support_counts = [item.model_support_count for item in ranking_list]
        max_support = max(support_counts)
        self.assertGreaterEqual(max_support, 2, "至少应有2个模型支持的候选")
        
        logger.info(f"排行榜质量指标 - 最高置信度: {max(confidences):.1%}, "
                   f"推荐等级种类: {len(unique_levels)}, 最大模型支持: {max_support}")

class TestAPIIntegration(unittest.TestCase):
    """API集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.api_base_url = "http://127.0.0.1:8888/api/v1"
        self.timeout = 10
    
    def test_api_health_check(self):
        """测试API健康检查"""
        try:
            response = requests.get(f"{self.api_base_url.replace('/api/v1', '')}/health", timeout=self.timeout)
            if response.status_code == 200:
                data = response.json()
                self.assertEqual(data['status'], 'healthy')
                logger.info("API健康检查通过")
            else:
                self.skipTest("API服务未运行")
        except requests.exceptions.RequestException:
            self.skipTest("API服务不可用")
    
    def test_single_best_prediction_api(self):
        """测试单一最优预测API"""
        try:
            request_data = {
                "candidate_count": 10,
                "confidence_threshold": 0.3,
                "window_size": 50
            }
            
            start_time = time.time()
            response = requests.post(
                f"{self.api_base_url}/prediction/single-best",
                json=request_data,
                timeout=self.timeout
            )
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                
                # 验证响应结构
                self.assertIn('best_prediction', data)
                self.assertIn('ranking_list', data)
                self.assertIn('model_performance', data)
                self.assertIn('prediction_metadata', data)
                
                # 验证最佳预测
                best_pred = data['best_prediction']
                self.assertIn('number', best_pred)
                self.assertIn('confidence', best_pred)
                self.assertIn('fusion_method', best_pred)
                
                # 验证排行榜
                ranking = data['ranking_list']
                self.assertIsInstance(ranking, list)
                self.assertLessEqual(len(ranking), 10)
                
                # 验证响应时间
                response_time = end_time - start_time
                self.assertLess(response_time, 5.0, f"API响应时间{response_time:.3f}秒超过5秒")
                
                logger.info(f"单一最优预测API测试通过，响应时间: {response_time:.3f}秒")
            else:
                self.skipTest(f"API返回错误状态码: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            self.skipTest(f"API请求失败: {e}")
    
    def test_model_performance_api(self):
        """测试模型性能API"""
        try:
            response = requests.get(f"{self.api_base_url}/models/performance", timeout=self.timeout)
            
            if response.status_code == 200:
                data = response.json()
                
                # 验证响应结构
                self.assertIn('models', data)
                self.assertIn('overall_stats', data)
                self.assertIn('last_updated', data)
                
                # 验证模型数据
                models = data['models']
                self.assertIsInstance(models, list)
                
                for model in models:
                    self.assertIn('model_name', model)
                    self.assertIn('accuracy_rate', model)
                    self.assertIn('current_weight', model)
                
                logger.info("模型性能API测试通过")
            else:
                self.skipTest(f"API返回错误状态码: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            self.skipTest(f"API请求失败: {e}")

class TestPerformanceBenchmarks(unittest.TestCase):
    """性能基准测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时数据库
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db_path = self.temp_db.name
        
        # 初始化系统组件
        self.tracker = ModelPerformanceTracker(db_path=self.db_path)
        self.fusion = AccuracyFocusedFusion(self.tracker)
        self.ranking = NumberRankingSystem(self.fusion, self.tracker)
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def test_concurrent_predictions(self):
        """测试并发预测性能"""
        logger.info("开始并发预测性能测试")
        
        def single_prediction():
            predictions = []
            for i in range(4):
                pred = ModelPrediction(
                    model_name=f"model_{i}",
                    top_candidate=f"{i}{i+1}{i+2}",
                    top_confidence=0.5 + i * 0.1,
                    all_candidates={f"{i}{j}{k}": 0.9 - j*0.1 - k*0.05 
                                  for j in range(3) for k in range(3)}
                )
                predictions.append(pred)
            
            return self.fusion.get_single_best_prediction(predictions)
        
        # 并发执行预测
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(single_prediction) for _ in range(10)]
            results = [future.result() for future in futures]
        end_time = time.time()
        
        # 验证结果
        self.assertEqual(len(results), 10)
        for result in results:
            self.assertIsNotNone(result)
            self.assertIsNotNone(result.number)
        
        total_time = end_time - start_time
        avg_time = total_time / 10
        
        logger.info(f"并发预测完成 - 总时间: {total_time:.3f}秒, 平均时间: {avg_time:.3f}秒")
        
        # 性能验证
        self.assertLess(avg_time, 1.0, f"平均预测时间{avg_time:.3f}秒超过1秒")
    
    def test_large_dataset_performance(self):
        """测试大数据集性能"""
        logger.info("开始大数据集性能测试")
        
        # 添加大量历史数据
        start_time = time.time()
        for i in range(1000):
            period = f"2024{i+1:04d}"
            for j, model in enumerate(['model_a', 'model_b', 'model_c', 'model_d']):
                predicted = f"{i%10}{(i+j)%10}{(i+j+1)%10}"
                actual = f"{i%10}{(i+1)%10}{(i+2)%10}"
                self.tracker.track_prediction(model, predicted, actual, period, 0.5 + j*0.1)
        
        data_load_time = time.time() - start_time
        
        # 测试权重计算性能
        start_time = time.time()
        weights = self.tracker.calculate_dynamic_weights()
        weight_calc_time = time.time() - start_time
        
        # 测试性能摘要生成
        start_time = time.time()
        summary = self.tracker.get_model_performance_summary()
        summary_time = time.time() - start_time
        
        logger.info(f"大数据集性能测试 - 数据加载: {data_load_time:.3f}秒, "
                   f"权重计算: {weight_calc_time:.3f}秒, 摘要生成: {summary_time:.3f}秒")
        
        # 性能验证
        self.assertLess(weight_calc_time, 1.0, "权重计算时间超过1秒")
        self.assertLess(summary_time, 2.0, "摘要生成时间超过2秒")
        
        # 验证结果正确性
        self.assertEqual(len(weights), 4)
        self.assertAlmostEqual(sum(weights.values()), 1.0, places=2)
        self.assertEqual(len(summary), 4)

class TestAccuracyValidation(unittest.TestCase):
    """准确性验证测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时数据库
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db_path = self.temp_db.name
        
        # 初始化系统
        self.tracker = ModelPerformanceTracker(db_path=self.db_path)
        self.fusion = AccuracyFocusedFusion(self.tracker)
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def test_accuracy_improvement_validation(self):
        """测试准确性提升验证"""
        logger.info("开始准确性提升验证测试")
        
        # 模拟历史数据：不同模型有不同的准确率
        models_accuracy = {
            'markov_enhanced': 0.4,      # 40%准确率
            'deep_learning_cnn_lstm': 0.6,  # 60%准确率
            'trend_analyzer': 0.5,       # 50%准确率
            'intelligent_fusion': 0.7    # 70%准确率
        }
        
        # 添加历史数据
        for i in range(100):
            period = f"2024{i+1:03d}"
            actual = f"{i%10}{(i+1)%10}{(i+2)%10}"
            
            for model, target_accuracy in models_accuracy.items():
                # 根据目标准确率生成预测
                is_correct = (i * hash(model)) % 100 < target_accuracy * 100
                predicted = actual if is_correct else f"{(i+3)%10}{(i+4)%10}{(i+5)%10}"
                confidence = 0.5 + (target_accuracy - 0.5)
                
                self.tracker.track_prediction(model, predicted, actual, period, confidence)
        
        # 验证各模型准确率
        for model, expected_accuracy in models_accuracy.items():
            actual_accuracy = self.tracker.get_model_accuracy(model)
            logger.info(f"{model} 准确率: {actual_accuracy:.1%} (期望: {expected_accuracy:.1%})")
            
            # 允许10%的误差范围
            self.assertAlmostEqual(actual_accuracy, expected_accuracy, delta=0.1)
        
        # 验证权重分配合理性
        weights = self.tracker.calculate_dynamic_weights()
        logger.info(f"动态权重分配: {weights}")
        
        # intelligent_fusion应该有最高权重
        max_weight_model = max(weights, key=weights.get)
        self.assertEqual(max_weight_model, 'intelligent_fusion', "最准确的模型应该有最高权重")

if __name__ == '__main__':
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestPredictionPipeline,
        TestAPIIntegration,
        TestPerformanceBenchmarks,
        TestAccuracyValidation
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果摘要
    print(f"\n{'='*60}")
    print(f"测试摘要:")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"跳过: {len(result.skipped)}")
    print(f"成功率: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    print(f"{'='*60}")
