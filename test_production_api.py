#!/usr/bin/env python3
"""
测试生产版API
"""

import requests
import json

def test_production_api():
    base_url = "http://127.0.0.1:8888"
    
    print("🧪 测试生产版FastAPI服务...")
    
    # 测试健康检查
    try:
        print("\n1. 测试健康检查...")
        response = requests.get(f"{base_url}/health", timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 健康状态: {data['status']}")
            print(f"   数据库记录: {data['database_records']}")
            print(f"   数据范围: {data['date_range']}")
        else:
            print(f"   ❌ 失败 - 状态码: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 测试基础统计
    try:
        print("\n2. 测试基础统计接口...")
        response = requests.get(f"{base_url}/api/v1/stats/basic", timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功 - 总记录数: {data['total_records']}")
            print(f"   查询时间: {data['query_time_ms']}ms")
            print(f"   数据范围: {data['date_range']['start']} 到 {data['date_range']['end']}")
        else:
            print(f"   ❌ 失败 - 状态码: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 测试频率分析
    try:
        print("\n3. 测试频率分析接口...")
        response = requests.get(f"{base_url}/api/v1/analysis/frequency", timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功 - 分析位置: {data['position']}")
            print(f"   查询时间: {data['query_time_ms']}ms")
        else:
            print(f"   ❌ 失败 - 状态码: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    print("\n📋 生产版API测试完成")
    print("🎯 现在可以启动Streamlit完整版应用！")

if __name__ == "__main__":
    test_production_api()
