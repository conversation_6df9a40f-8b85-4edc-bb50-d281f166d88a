#!/usr/bin/env python3
"""
位置频率数据完整性验证脚本
验证数据库中hundreds、tens、units数据的准确性
"""

import sys
import sqlite3
from pathlib import Path

def test_database_integrity():
    """测试数据库数据完整性"""
    
    db_path = Path("data/lottery.db")
    if not db_path.exists():
        print("❌ 数据库文件不存在")
        return False
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            print("🔍 开始验证数据库数据完整性...")
            
            # 1. 获取总记录数
            cursor.execute("SELECT COUNT(*) FROM lottery_records")
            total_records = cursor.fetchone()[0]
            print(f"📊 总记录数: {total_records}")
            
            # 2. 验证百位数据
            print("\n🔢 验证百位数据...")
            cursor.execute("""
                SELECT SUBSTR(numbers, 1, 1) as digit, COUNT(*) as count
                FROM lottery_records
                WHERE numbers IS NOT NULL AND LENGTH(numbers) = 3
                GROUP BY SUBSTR(numbers, 1, 1)
                ORDER BY digit
            """)
            hundreds_data = cursor.fetchall()
            hundreds_total = sum(count for _, count in hundreds_data)
            print(f"百位总计数: {hundreds_total}")
            
            for digit, count in hundreds_data:
                percentage = (count / hundreds_total * 100) if hundreds_total > 0 else 0
                print(f"  数字{digit}: {count}次 ({percentage:.2f}%)")
            
            # 3. 验证十位数据
            print("\n🔢 验证十位数据...")
            cursor.execute("""
                SELECT SUBSTR(numbers, 2, 1) as digit, COUNT(*) as count
                FROM lottery_records
                WHERE numbers IS NOT NULL AND LENGTH(numbers) = 3
                GROUP BY SUBSTR(numbers, 2, 1)
                ORDER BY digit
            """)
            tens_data = cursor.fetchall()
            tens_total = sum(count for _, count in tens_data)
            print(f"十位总计数: {tens_total}")
            
            for digit, count in tens_data:
                percentage = (count / tens_total * 100) if tens_total > 0 else 0
                print(f"  数字{digit}: {count}次 ({percentage:.2f}%)")
            
            # 4. 验证个位数据
            print("\n🔢 验证个位数据...")
            cursor.execute("""
                SELECT SUBSTR(numbers, 3, 1) as digit, COUNT(*) as count
                FROM lottery_records
                WHERE numbers IS NOT NULL AND LENGTH(numbers) = 3
                GROUP BY SUBSTR(numbers, 3, 1)
                ORDER BY digit
            """)
            units_data = cursor.fetchall()
            units_total = sum(count for _, count in units_data)
            print(f"个位总计数: {units_total}")
            
            for digit, count in units_data:
                percentage = (count / units_total * 100) if units_total > 0 else 0
                print(f"  数字{digit}: {count}次 ({percentage:.2f}%)")
            
            # 5. 验证数据一致性
            print("\n✅ 数据一致性验证:")
            print(f"总记录数: {total_records}")
            print(f"百位总计: {hundreds_total}")
            print(f"十位总计: {tens_total}")
            print(f"个位总计: {units_total}")
            
            # 检查是否一致
            if hundreds_total == tens_total == units_total == total_records:
                print("✅ 数据一致性验证通过！")
                integrity_passed = True
            else:
                print("❌ 数据一致性验证失败！")
                integrity_passed = False
            
            # 6. 验证数字分布均匀性
            print("\n📈 数字分布均匀性分析:")
            expected_freq = total_records / 10  # 每个数字的期望频率
            
            for position, data in [("百位", hundreds_data), ("十位", tens_data), ("个位", units_data)]:
                print(f"\n{position}分布分析:")
                print(f"期望频率: {expected_freq:.1f}")
                
                deviations = []
                for digit, count in data:
                    deviation = abs(count - expected_freq) / expected_freq * 100
                    deviations.append(deviation)
                    status = "正常" if deviation < 20 else "异常"
                    print(f"  数字{digit}: {count}次, 偏差{deviation:.1f}% ({status})")
                
                avg_deviation = sum(deviations) / len(deviations)
                print(f"平均偏差: {avg_deviation:.1f}%")
            
            return integrity_passed
            
    except Exception as e:
        print(f"❌ 验证过程中出错: {e}")
        return False

def test_api_response():
    """测试API响应数据格式"""
    import requests
    
    try:
        print("\n🌐 测试API响应...")
        response = requests.get("http://127.0.0.1:8888/api/v1/analysis/frequency", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API响应正常")
            
            # 检查position_frequency字段
            if 'position_frequency' in data:
                position_freq = data['position_frequency']
                print(f"📊 position_frequency类型: {type(position_freq)}")
                print(f"📊 position_frequency长度: {len(position_freq) if isinstance(position_freq, list) else 'N/A'}")
                
                if isinstance(position_freq, list) and len(position_freq) > 0:
                    print("✅ position_frequency格式正确（列表）")
                    
                    # 检查前几个项目的格式
                    for i, item in enumerate(position_freq[:3]):
                        if isinstance(item, dict) and all(key in item for key in ['位置', '数字', '频率']):
                            print(f"  项目{i+1}: {item}")
                        else:
                            print(f"❌ 项目{i+1}格式错误: {item}")
                            return False
                    
                    print("✅ position_frequency数据格式验证通过")
                    return True
                else:
                    print("❌ position_frequency格式错误")
                    return False
            else:
                print("❌ API响应中缺少position_frequency字段")
                return False
        else:
            print(f"❌ API响应错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 位置频率数据完整性验证")
    print("=" * 50)
    
    # 测试数据库完整性
    db_integrity = test_database_integrity()
    
    # 测试API响应
    api_integrity = test_api_response()
    
    print("\n" + "=" * 50)
    print("📋 验证结果总结:")
    print(f"数据库完整性: {'✅ 通过' if db_integrity else '❌ 失败'}")
    print(f"API响应格式: {'✅ 通过' if api_integrity else '❌ 失败'}")
    
    if db_integrity and api_integrity:
        print("\n🎉 所有验证通过！位置频率分析功能数据完整性良好。")
        sys.exit(0)
    else:
        print("\n⚠️ 存在问题，请检查相关组件。")
        sys.exit(1)
