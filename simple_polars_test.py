#!/usr/bin/env python3
"""
简单的Polars测试
"""

import polars as pl
import sys
sys.path.append('src')

def main():
    print("开始Polars测试...")
    
    # 测试基本功能
    df = pl.DataFrame({
        'period': ['2025001', '2025002', '2025003'],
        'numbers': ['123', '456', '789'],
        'sum_value': [6, 15, 24]
    })
    
    print("创建测试DataFrame:")
    print(df)
    
    # 测试统计
    stats = df.select([
        pl.col('sum_value').mean().alias('mean_sum'),
        pl.col('sum_value').max().alias('max_sum'),
        pl.count().alias('count')
    ])
    
    print("\n统计结果:")
    print(stats)
    
    print("\nPolars测试成功！")

if __name__ == "__main__":
    main()
