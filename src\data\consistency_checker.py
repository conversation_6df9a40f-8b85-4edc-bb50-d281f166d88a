#!/usr/bin/env python3
"""
数据一致性检查器
"""

import json
import logging
import sqlite3
import hashlib
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Tuple, Optional

# 配置日志
logger = logging.getLogger(__name__)

class ConsistencyChecker:
    """数据一致性检查器，用于验证元数据与数据库状态的一致性"""
    
    def __init__(self, data_dir: str = "data"):
        """初始化一致性检查器
        
        Args:
            data_dir: 数据目录路径
        """
        self.data_dir = Path(data_dir)
        self.db_path = self.data_dir / "lottery.db"
        self.metadata_path = self.data_dir / "processed" / "update_metadata.json"
        
        logger.info(f"一致性检查器初始化完成，数据目录: {self.data_dir}")
    
    def check_consistency(self) -> Dict[str, Any]:
        """检查元数据与数据库的一致性
        
        Returns:
            包含检查结果的字典
        """
        logger.info("开始检查数据一致性...")
        
        # 获取数据库状态
        db_state = self._get_database_state()
        if not db_state:
            return {
                "success": False,
                "message": "获取数据库状态失败",
                "timestamp": datetime.now().isoformat()
            }
        
        # 获取元数据状态
        metadata_state = self._get_metadata_state()
        if not metadata_state:
            return {
                "success": False,
                "message": "获取元数据状态失败",
                "timestamp": datetime.now().isoformat()
            }
        
        # 比较状态
        is_consistent, inconsistencies = self._compare_states(db_state, metadata_state)
        
        result = {
            "success": True,
            "is_consistent": is_consistent,
            "timestamp": datetime.now().isoformat(),
            "database_state": db_state,
            "metadata_state": metadata_state
        }
        
        if not is_consistent:
            result["inconsistencies"] = inconsistencies
            logger.warning(f"检测到数据不一致: {inconsistencies}")
        else:
            logger.info("数据一致性检查通过")
        
        return result
    
    def auto_repair(self) -> Dict[str, Any]:
        """自动修复元数据与数据库的不一致
        
        Returns:
            包含修复结果的字典
        """
        logger.info("开始自动修复数据不一致...")
        
        # 先检查一致性
        check_result = self.check_consistency()
        if not check_result["success"]:
            return {
                "success": False,
                "message": "检查一致性失败，无法修复",
                "timestamp": datetime.now().isoformat()
            }
        
        # 如果已经一致，不需要修复
        if check_result["is_consistent"]:
            return {
                "success": True,
                "message": "数据已经一致，无需修复",
                "timestamp": datetime.now().isoformat()
            }
        
        # 获取数据库状态（作为真实状态）
        db_state = check_result["database_state"]
        
        # 读取当前元数据
        try:
            with open(self.metadata_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
        except Exception as e:
            logger.error(f"读取元数据失败: {e}")
            return {
                "success": False,
                "message": f"读取元数据失败: {e}",
                "timestamp": datetime.now().isoformat()
            }
        
        # 备份原元数据
        backup_path = self.data_dir / "processed" / f"update_metadata_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            import shutil
            shutil.copy2(self.metadata_path, backup_path)
            logger.info(f"元数据已备份: {backup_path}")
        except Exception as e:
            logger.warning(f"元数据备份失败: {e}")
        
        # 更新元数据以匹配数据库
        metadata.update({
            "last_update_time": datetime.now().isoformat(),
            "last_data_hash": db_state["hash"],
            "last_record_count": db_state["count"],
            "last_period": db_state["latest_period"],
            "last_date": db_state["latest_date"]
        })
        
        # 添加修复记录到历史
        repair_record = {
            "timestamp": datetime.now().isoformat(),
            "records_added": 0,
            "total_records": db_state["count"],
            "quality_score": 99.0,
            "data_file": "auto_repair",
            "backup_file": str(backup_path),
            "changes": {
                "data_changed": True,
                "hash_changed": True,
                "count_changed": True,
                "period_changed": True,
                "date_changed": True,
                "new_hash": db_state["hash"],
                "old_hash": check_result["metadata_state"]["hash"],
                "new_count": db_state["count"],
                "old_count": check_result["metadata_state"]["count"],
                "new_period": db_state["latest_period"],
                "old_period": check_result["metadata_state"]["latest_period"],
                "new_date": db_state["latest_date"],
                "old_date": check_result["metadata_state"]["latest_date"],
                "records_added": 0
            },
            "note": "自动修复元数据不一致"
        }
        
        if "update_history" not in metadata:
            metadata["update_history"] = []
        
        metadata["update_history"].append(repair_record)
        
        # 只保留最近20次更新记录
        metadata["update_history"] = metadata["update_history"][-20:]
        
        # 保存更新后的元数据
        try:
            with open(self.metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            logger.info("元数据修复成功")
            
            return {
                "success": True,
                "message": "元数据修复成功",
                "timestamp": datetime.now().isoformat(),
                "backup_file": str(backup_path),
                "changes": repair_record["changes"]
            }
            
        except Exception as e:
            logger.error(f"保存元数据失败: {e}")
            return {
                "success": False,
                "message": f"保存元数据失败: {e}",
                "timestamp": datetime.now().isoformat()
            }
    
    def _get_database_state(self) -> Optional[Dict[str, Any]]:
        """获取数据库当前状态
        
        Returns:
            包含数据库状态的字典，如果失败则返回None
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取记录数
                cursor.execute("SELECT COUNT(*) FROM lottery_records")
                count = cursor.fetchone()[0]
                
                # 获取最新记录
                cursor.execute("SELECT period, date, numbers FROM lottery_records ORDER BY period DESC LIMIT 1")
                latest = cursor.fetchone()
                
                if not latest:
                    logger.warning("数据库中没有记录")
                    return None
                
                latest_period = latest[0]
                latest_date = latest[1]
                latest_numbers = latest[2]
                
                # 计算数据哈希
                hash_data = f"{count}_{latest_period}_{latest_date}_{latest_numbers}"
                data_hash = hashlib.md5(hash_data.encode()).hexdigest()
                
                return {
                    "count": count,
                    "latest_period": latest_period,
                    "latest_date": latest_date,
                    "latest_numbers": latest_numbers,
                    "hash": data_hash
                }
                
        except Exception as e:
            logger.error(f"获取数据库状态失败: {e}")
            return None
    
    def _get_metadata_state(self) -> Optional[Dict[str, Any]]:
        """获取元数据当前状态
        
        Returns:
            包含元数据状态的字典，如果失败则返回None
        """
        try:
            if not self.metadata_path.exists():
                logger.warning(f"元数据文件不存在: {self.metadata_path}")
                return None
            
            with open(self.metadata_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            count = metadata.get("last_record_count")
            period = metadata.get("last_period")
            date = metadata.get("last_date")
            data_hash = metadata.get("last_data_hash")
            
            if not all([count, period, date, data_hash]):
                logger.warning("元数据不完整")
                return None
            
            return {
                "count": count,
                "latest_period": period,
                "latest_date": date,
                "hash": data_hash
            }
            
        except Exception as e:
            logger.error(f"获取元数据状态失败: {e}")
            return None
    
    def _compare_states(self, db_state: Dict[str, Any], metadata_state: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """比较数据库状态和元数据状态
        
        Args:
            db_state: 数据库状态
            metadata_state: 元数据状态
            
        Returns:
            (是否一致, 不一致项)
        """
        inconsistencies = {}
        
        # 比较记录数
        if db_state["count"] != metadata_state["count"]:
            inconsistencies["count"] = {
                "database": db_state["count"],
                "metadata": metadata_state["count"]
            }
        
        # 比较最新期号
        if db_state["latest_period"] != metadata_state["latest_period"]:
            inconsistencies["latest_period"] = {
                "database": db_state["latest_period"],
                "metadata": metadata_state["latest_period"]
            }
        
        # 比较最新日期
        if db_state["latest_date"] != metadata_state["latest_date"]:
            inconsistencies["latest_date"] = {
                "database": db_state["latest_date"],
                "metadata": metadata_state["latest_date"]
            }
        
        # 比较数据哈希
        if db_state["hash"] != metadata_state["hash"]:
            inconsistencies["hash"] = {
                "database": db_state["hash"],
                "metadata": metadata_state["hash"]
            }
        
        is_consistent = len(inconsistencies) == 0
        
        return is_consistent, inconsistencies
