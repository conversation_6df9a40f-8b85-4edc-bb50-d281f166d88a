"""
训练记录数据模型
定义TrainingRecord、Knowledge数据结构，数据序列化反序列化，记录验证完整性检查
"""

import json
import pickle
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field, asdict
from enum import Enum
import hashlib
import uuid


class TrainingStatus(Enum):
    """训练状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class KnowledgeType(Enum):
    """知识类型"""
    PARAMETER_PATTERN = "parameter_pattern"
    ACCURACY_TREND = "accuracy_trend"
    FEATURE_IMPORTANCE = "feature_importance"
    CONVERGENCE_PATTERN = "convergence_pattern"
    MODEL_INSIGHT = "model_insight"
    OPTIMIZATION_RULE = "optimization_rule"


@dataclass
class TrainingMetrics:
    """训练指标"""
    accuracy: float = 0.0
    precision: float = 0.0
    recall: float = 0.0
    f1_score: float = 0.0
    loss: float = 0.0
    val_accuracy: float = 0.0
    val_loss: float = 0.0
    training_time: float = 0.0
    epochs: int = 0
    convergence_epoch: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TrainingMetrics':
        """从字典创建"""
        return cls(**data)


@dataclass
class HyperParameters:
    """超参数"""
    learning_rate: float = 0.001
    batch_size: int = 32
    epochs: int = 100
    optimizer: str = "adam"
    loss_function: str = "mse"
    regularization: Optional[Dict[str, Any]] = None
    model_specific: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'HyperParameters':
        """从字典创建"""
        return cls(**data)
    
    def get_hash(self) -> str:
        """获取参数哈希值"""
        param_str = json.dumps(self.to_dict(), sort_keys=True)
        return hashlib.md5(param_str.encode()).hexdigest()


@dataclass
class DataConfiguration:
    """数据配置"""
    data_range: tuple = (0, 1000)
    feature_set: List[str] = field(default_factory=list)
    preprocessing_steps: List[str] = field(default_factory=list)
    train_test_split: float = 0.8
    validation_split: float = 0.1
    data_quality_score: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DataConfiguration':
        """从字典创建"""
        return cls(**data)


@dataclass
class TrainingRecord:
    """训练记录"""
    # 基本信息
    record_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    model_id: str = ""
    model_version: str = "1.0"
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # 状态信息
    status: TrainingStatus = TrainingStatus.PENDING
    progress: float = 0.0
    
    # 配置信息
    hyperparameters: HyperParameters = field(default_factory=HyperParameters)
    data_config: DataConfiguration = field(default_factory=DataConfiguration)
    
    # 结果信息
    metrics: TrainingMetrics = field(default_factory=TrainingMetrics)
    model_path: Optional[str] = None
    
    # 元数据
    tags: List[str] = field(default_factory=list)
    notes: str = ""
    experiment_name: Optional[str] = None
    
    # 错误信息
    error_message: Optional[str] = None
    error_traceback: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 处理datetime对象
        data['created_at'] = self.created_at.isoformat()
        data['started_at'] = self.started_at.isoformat() if self.started_at else None
        data['completed_at'] = self.completed_at.isoformat() if self.completed_at else None
        data['status'] = self.status.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TrainingRecord':
        """从字典创建"""
        # 处理datetime字段
        if 'created_at' in data and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if 'started_at' in data and data['started_at']:
            data['started_at'] = datetime.fromisoformat(data['started_at'])
        if 'completed_at' in data and data['completed_at']:
            data['completed_at'] = datetime.fromisoformat(data['completed_at'])
        
        # 处理枚举字段
        if 'status' in data and isinstance(data['status'], str):
            data['status'] = TrainingStatus(data['status'])
        
        # 处理嵌套对象
        if 'hyperparameters' in data and isinstance(data['hyperparameters'], dict):
            data['hyperparameters'] = HyperParameters.from_dict(data['hyperparameters'])
        if 'data_config' in data and isinstance(data['data_config'], dict):
            data['data_config'] = DataConfiguration.from_dict(data['data_config'])
        if 'metrics' in data and isinstance(data['metrics'], dict):
            data['metrics'] = TrainingMetrics.from_dict(data['metrics'])
        
        return cls(**data)
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), indent=2, ensure_ascii=False)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'TrainingRecord':
        """从JSON字符串创建"""
        data = json.loads(json_str)
        return cls.from_dict(data)
    
    def to_pickle(self) -> bytes:
        """序列化为pickle格式"""
        return pickle.dumps(self)
    
    @classmethod
    def from_pickle(cls, pickle_data: bytes) -> 'TrainingRecord':
        """从pickle数据反序列化"""
        return pickle.loads(pickle_data)
    
    def get_signature(self) -> str:
        """获取训练记录的唯一签名"""
        signature_data = {
            'model_id': self.model_id,
            'hyperparameters': self.hyperparameters.to_dict(),
            'data_config': self.data_config.to_dict()
        }
        signature_str = json.dumps(signature_data, sort_keys=True)
        return hashlib.sha256(signature_str.encode()).hexdigest()
    
    def is_similar_to(self, other: 'TrainingRecord', threshold: float = 0.8) -> bool:
        """判断是否与另一个训练记录相似"""
        if self.model_id != other.model_id:
            return False
        
        # 比较超参数相似度
        param_similarity = self._calculate_parameter_similarity(other)
        
        # 比较数据配置相似度
        data_similarity = self._calculate_data_similarity(other)
        
        # 综合相似度
        overall_similarity = (param_similarity + data_similarity) / 2
        
        return overall_similarity >= threshold
    
    def _calculate_parameter_similarity(self, other: 'TrainingRecord') -> float:
        """计算参数相似度"""
        try:
            my_params = self.hyperparameters.to_dict()
            other_params = other.hyperparameters.to_dict()
            
            common_keys = set(my_params.keys()) & set(other_params.keys())
            if not common_keys:
                return 0.0
            
            similarities = []
            for key in common_keys:
                if isinstance(my_params[key], (int, float)) and isinstance(other_params[key], (int, float)):
                    # 数值参数相似度
                    if my_params[key] == 0 and other_params[key] == 0:
                        similarities.append(1.0)
                    else:
                        diff = abs(my_params[key] - other_params[key])
                        max_val = max(abs(my_params[key]), abs(other_params[key]))
                        similarity = 1 - (diff / (max_val + 1e-8))
                        similarities.append(max(0, similarity))
                else:
                    # 非数值参数相似度
                    similarities.append(1.0 if my_params[key] == other_params[key] else 0.0)
            
            return sum(similarities) / len(similarities)
            
        except Exception:
            return 0.0
    
    def _calculate_data_similarity(self, other: 'TrainingRecord') -> float:
        """计算数据配置相似度"""
        try:
            # 特征集相似度
            my_features = set(self.data_config.feature_set)
            other_features = set(other.data_config.feature_set)
            
            if not my_features and not other_features:
                feature_similarity = 1.0
            elif not my_features or not other_features:
                feature_similarity = 0.0
            else:
                intersection = len(my_features & other_features)
                union = len(my_features | other_features)
                feature_similarity = intersection / union
            
            # 数据范围相似度
            my_range = self.data_config.data_range
            other_range = other.data_config.data_range
            
            range_overlap = max(0, min(my_range[1], other_range[1]) - max(my_range[0], other_range[0]))
            range_union = max(my_range[1], other_range[1]) - min(my_range[0], other_range[0])
            range_similarity = range_overlap / (range_union + 1e-8)
            
            return (feature_similarity + range_similarity) / 2
            
        except Exception:
            return 0.0
    
    def validate(self) -> List[str]:
        """验证记录完整性"""
        errors = []
        
        # 必填字段检查
        if not self.model_id:
            errors.append("model_id不能为空")
        
        if not self.record_id:
            errors.append("record_id不能为空")
        
        # 状态一致性检查
        if self.status == TrainingStatus.COMPLETED:
            if not self.completed_at:
                errors.append("已完成的训练记录必须有完成时间")
            if self.metrics.accuracy == 0.0:
                errors.append("已完成的训练记录应该有准确率指标")
        
        if self.status == TrainingStatus.FAILED:
            if not self.error_message:
                errors.append("失败的训练记录应该有错误信息")
        
        # 时间逻辑检查
        if self.started_at and self.completed_at:
            if self.started_at > self.completed_at:
                errors.append("开始时间不能晚于完成时间")
        
        # 进度检查
        if not (0 <= self.progress <= 1):
            errors.append("进度值必须在0-1之间")
        
        return errors


@dataclass
class Knowledge:
    """知识条目"""
    knowledge_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    knowledge_type: KnowledgeType = KnowledgeType.MODEL_INSIGHT
    
    # 来源信息
    source_record_id: str = ""
    model_id: str = ""
    
    # 知识内容
    title: str = ""
    content: Dict[str, Any] = field(default_factory=dict)
    confidence: float = 0.0
    
    # 应用信息
    applicable_models: List[str] = field(default_factory=list)
    applicable_scenarios: List[str] = field(default_factory=list)
    
    # 元数据
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    tags: List[str] = field(default_factory=list)
    
    # 验证信息
    validation_count: int = 0
    success_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['knowledge_type'] = self.knowledge_type.value
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Knowledge':
        """从字典创建"""
        if 'knowledge_type' in data and isinstance(data['knowledge_type'], str):
            data['knowledge_type'] = KnowledgeType(data['knowledge_type'])
        if 'created_at' in data and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if 'updated_at' in data and isinstance(data['updated_at'], str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        return cls(**data)
    
    def get_effectiveness_score(self) -> float:
        """获取知识有效性评分"""
        if self.validation_count == 0:
            return self.confidence
        
        success_rate = self.success_count / self.validation_count
        return (self.confidence + success_rate) / 2
    
    def update_validation(self, success: bool):
        """更新验证结果"""
        self.validation_count += 1
        if success:
            self.success_count += 1
        self.updated_at = datetime.now()


def test_training_record():
    """测试训练记录功能"""
    print("🧪 测试训练记录数据模型...")
    
    # 创建训练记录
    record = TrainingRecord(
        model_id="intelligent_fusion",
        hyperparameters=HyperParameters(
            learning_rate=0.001,
            batch_size=64,
            epochs=100
        ),
        data_config=DataConfiguration(
            data_range=(0, 1000),
            feature_set=["sum_feature", "span_feature", "frequency_feature"]
        )
    )
    
    # 测试序列化
    json_str = record.to_json()
    print(f"📄 JSON序列化长度: {len(json_str)}")
    
    # 测试反序列化
    restored_record = TrainingRecord.from_json(json_str)
    print(f"✅ 反序列化成功: {restored_record.record_id}")
    
    # 测试验证
    errors = record.validate()
    print(f"🔍 验证结果: {len(errors)} 个错误")
    
    # 测试知识条目
    knowledge = Knowledge(
        knowledge_type=KnowledgeType.PARAMETER_PATTERN,
        source_record_id=record.record_id,
        model_id=record.model_id,
        title="最优学习率模式",
        content={"optimal_lr": 0.001, "pattern": "exponential_decay"},
        confidence=0.85
    )
    
    print(f"📚 知识条目创建: {knowledge.title}")
    print(f"🎯 有效性评分: {knowledge.get_effectiveness_score():.3f}")
    
    print("✅ 训练记录数据模型测试完成！")


if __name__ == "__main__":
    test_training_record()
