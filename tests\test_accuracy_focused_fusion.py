"""
准确性导向融合算法单元测试
测试AccuracyFocusedFusion、NumberRankingSystem、ModelPerformanceTracker类的核心功能
"""

import unittest
import tempfile
import os
import sqlite3
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
import sys
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.core.model_performance_tracker import ModelPerformanceTracker, PredictionRecord, ModelAccuracy
from src.core.accuracy_focused_fusion import AccuracyFocusedFusion, ModelPrediction, PredictionResult
from src.core.number_ranking_system import NumberRankingSystem, RankingItem

# 配置测试日志
logging.basicConfig(level=logging.DEBUG)

class TestModelPerformanceTracker(unittest.TestCase):
    """模型性能跟踪器测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时数据库
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db_path = self.temp_db.name
        
        # 初始化跟踪器
        self.tracker = ModelPerformanceTracker(db_path=self.db_path, window_size=10)
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def test_database_initialization(self):
        """测试数据库初始化"""
        # 验证数据库文件存在
        self.assertTrue(os.path.exists(self.db_path))
        
        # 验证表结构
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 检查model_predictions表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='model_predictions'")
            self.assertIsNotNone(cursor.fetchone())
            
            # 检查model_accuracy表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='model_accuracy'")
            self.assertIsNotNone(cursor.fetchone())
    
    def test_track_prediction(self):
        """测试预测结果记录"""
        # 记录预测结果
        self.tracker.track_prediction(
            model_name="test_model",
            predicted="123",
            actual="123",
            period="2025001",
            confidence=0.8
        )
        
        # 验证记录是否保存
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM model_predictions WHERE model_name='test_model'")
            result = cursor.fetchone()
            
            self.assertIsNotNone(result)
            self.assertEqual(result[1], "2025001")  # period_number
            self.assertEqual(result[2], "test_model")  # model_name
            self.assertEqual(result[3], "123")  # predicted_number
            self.assertEqual(result[5], "123")  # actual_number
            self.assertTrue(result[6])  # is_hit
    
    def test_get_model_accuracy(self):
        """测试模型准确率计算"""
        # 添加测试数据
        test_data = [
            ("2025001", "123", "123", True),
            ("2025002", "456", "789", False),
            ("2025003", "111", "111", True),
            ("2025004", "222", "333", False),
            ("2025005", "555", "555", True)
        ]
        
        for period, predicted, actual, is_hit in test_data:
            self.tracker.track_prediction("test_model", predicted, actual, period, 0.5)
        
        # 计算准确率
        accuracy = self.tracker.get_model_accuracy("test_model")
        
        # 验证准确率 (3/5 = 0.6)
        self.assertAlmostEqual(accuracy, 0.6, places=2)
    
    def test_calculate_dynamic_weights(self):
        """测试动态权重计算"""
        # 为多个模型添加测试数据
        models_data = {
            "model_a": [(True, True, True, False, False)],  # 60% 准确率
            "model_b": [(True, True, True, True, False)],   # 80% 准确率
            "model_c": [(False, False, True, True, True)],  # 60% 准确率
            "model_d": [(True, True, True, True, True)]     # 100% 准确率
        }
        
        for model_name, results_list in models_data.items():
            for i, is_hit in enumerate(results_list[0]):
                period = f"202500{i+1}"
                predicted = "123" if is_hit else "456"
                actual = "123"
                self.tracker.track_prediction(model_name, predicted, actual, period, 0.5)
        
        # 计算动态权重
        weights = self.tracker.calculate_dynamic_weights()
        
        # 验证权重
        self.assertEqual(len(weights), 4)
        self.assertAlmostEqual(sum(weights.values()), 1.0, places=2)
        
        # model_d应该有最高权重（100%准确率）
        self.assertGreater(weights["model_d"], weights["model_a"])
        self.assertGreater(weights["model_d"], weights["model_b"])
        self.assertGreater(weights["model_d"], weights["model_c"])
    
    def test_get_model_performance_summary(self):
        """测试模型性能摘要"""
        # 添加测试数据
        self.tracker.track_prediction("test_model", "123", "123", "2025001", 0.8)
        self.tracker.track_prediction("test_model", "456", "789", "2025002", 0.6)
        
        # 获取性能摘要
        summary = self.tracker.get_model_performance_summary()
        
        # 验证摘要内容
        self.assertIn("test_model", summary)
        model_stats = summary["test_model"]
        
        self.assertEqual(model_stats["total_predictions"], 2)
        self.assertEqual(model_stats["correct_predictions"], 1)
        self.assertAlmostEqual(model_stats["accuracy_rate"], 0.5, places=2)

class TestAccuracyFocusedFusion(unittest.TestCase):
    """准确性导向融合算法测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建模拟的性能跟踪器
        self.mock_tracker = Mock(spec=ModelPerformanceTracker)
        self.mock_tracker.calculate_dynamic_weights.return_value = {
            'markov_enhanced': 0.25,
            'deep_learning_cnn_lstm': 0.25,
            'trend_analyzer': 0.25,
            'intelligent_fusion': 0.25
        }
        self.mock_tracker.get_model_accuracy.return_value = 0.6
        
        # 初始化融合引擎
        self.fusion = AccuracyFocusedFusion(self.mock_tracker)
    
    def create_mock_predictions(self) -> list:
        """创建模拟预测数据"""
        predictions = []
        
        # 模型1：预测123，置信度0.8
        pred1 = ModelPrediction(
            model_name="markov_enhanced",
            top_candidate="123",
            top_confidence=0.8,
            all_candidates={"123": 0.8, "456": 0.6, "789": 0.4}
        )
        predictions.append(pred1)
        
        # 模型2：预测123，置信度0.7（交集）
        pred2 = ModelPrediction(
            model_name="deep_learning_cnn_lstm",
            top_candidate="123",
            top_confidence=0.7,
            all_candidates={"123": 0.7, "111": 0.5, "222": 0.3}
        )
        predictions.append(pred2)
        
        # 模型3：预测456，置信度0.9
        pred3 = ModelPrediction(
            model_name="trend_analyzer",
            top_candidate="456",
            top_confidence=0.9,
            all_candidates={"456": 0.9, "123": 0.5, "333": 0.2}
        )
        predictions.append(pred3)
        
        # 模型4：预测123，置信度0.6（交集）
        pred4 = ModelPrediction(
            model_name="intelligent_fusion",
            top_candidate="123",
            top_confidence=0.6,
            all_candidates={"123": 0.6, "789": 0.4, "000": 0.1}
        )
        predictions.append(pred4)
        
        return predictions
    
    def test_intersection_analysis(self):
        """测试交集分析"""
        predictions = self.create_mock_predictions()
        
        # 执行交集分析
        result = self.fusion._intersection_analysis(predictions)
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(result.number, "123")  # 123是交集
        self.assertEqual(result.method, "多模型交集分析")
        self.assertGreater(result.confidence, 0)
        self.assertIn("markov_enhanced", result.model_support)
        self.assertIn("deep_learning_cnn_lstm", result.model_support)
        self.assertIn("intelligent_fusion", result.model_support)
    
    def test_weighted_voting(self):
        """测试加权投票"""
        predictions = self.create_mock_predictions()
        
        # 执行加权投票
        result = self.fusion._weighted_voting(predictions)
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertIn(result.number, ["123", "456"])  # 应该是得票最高的号码
        self.assertEqual(result.method, "历史准确率加权投票")
        self.assertGreater(result.confidence, 0)
    
    def test_confidence_ranking(self):
        """测试置信度排序"""
        predictions = self.create_mock_predictions()
        
        # 执行置信度排序
        result = self.fusion._confidence_ranking(predictions)
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(result.method, "综合置信度排序")
        self.assertGreater(result.confidence, 0)
        self.assertIsNotNone(result.fusion_details)
    
    def test_best_model_fallback(self):
        """测试最佳模型回退"""
        predictions = self.create_mock_predictions()
        
        # 执行最佳模型回退
        result = self.fusion._best_model_fallback(predictions)
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertIn("回退", result.method)
        self.assertGreater(result.confidence, 0)
    
    def test_get_single_best_prediction(self):
        """测试单一最优预测"""
        predictions = self.create_mock_predictions()
        
        # 执行单一最优预测
        result = self.fusion.get_single_best_prediction(predictions)
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertIsInstance(result, PredictionResult)
        self.assertIsNotNone(result.number)
        self.assertGreater(result.confidence, 0)
        self.assertIsNotNone(result.method)
        self.assertEqual(result.rank, 1)
    
    def test_get_fusion_explanation(self):
        """测试融合解释生成"""
        result = PredictionResult(
            number="123",
            confidence=0.75,
            method="多模型交集分析",
            rank=1,
            model_support=["model_a", "model_b"],
            fusion_details={"intersection_size": 1, "total_models": 4}
        )
        
        # 生成解释
        explanation = self.fusion.get_fusion_explanation(result)
        
        # 验证解释内容
        self.assertIn("123", explanation)
        self.assertIn("75.0%", explanation)
        self.assertIn("多模型交集分析", explanation)
        self.assertIn("model_a", explanation)
        self.assertIn("model_b", explanation)

class TestNumberRankingSystem(unittest.TestCase):
    """号码排行榜系统测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建模拟对象
        self.mock_tracker = Mock(spec=ModelPerformanceTracker)
        self.mock_fusion = Mock(spec=AccuracyFocusedFusion)
        
        # 创建临时数据库
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db_path = self.temp_db.name
        
        # 初始化排行榜系统
        self.ranking = NumberRankingSystem(
            fusion_engine=self.mock_fusion,
            performance_tracker=self.mock_tracker,
            db_path=self.db_path
        )
        
        # 设置模拟返回值
        self.mock_tracker.calculate_dynamic_weights.return_value = {
            'model_a': 0.3, 'model_b': 0.3, 'model_c': 0.2, 'model_d': 0.2
        }
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def create_mock_predictions(self) -> list:
        """创建模拟预测数据"""
        predictions = []
        
        pred1 = ModelPrediction(
            model_name="model_a",
            top_candidate="123",
            top_confidence=0.8,
            all_candidates={"123": 0.8, "456": 0.6, "789": 0.4}
        )
        predictions.append(pred1)
        
        pred2 = ModelPrediction(
            model_name="model_b",
            top_candidate="456",
            top_confidence=0.7,
            all_candidates={"456": 0.7, "123": 0.5, "111": 0.3}
        )
        predictions.append(pred2)
        
        return predictions
    
    def test_get_recommendation_level(self):
        """测试推荐等级判断"""
        # 测试不同置信度的推荐等级
        self.assertEqual(self.ranking.get_recommendation_level(0.9), "强烈推荐")
        self.assertEqual(self.ranking.get_recommendation_level(0.7), "推荐")
        self.assertEqual(self.ranking.get_recommendation_level(0.5), "可考虑")
        self.assertEqual(self.ranking.get_recommendation_level(0.3), "谨慎")
        self.assertEqual(self.ranking.get_recommendation_level(0.1), "不推荐")
    
    def test_get_historical_hit_rate(self):
        """测试历史命中率计算"""
        # 由于需要彩票数据库，这里模拟返回值
        with patch.object(self.ranking, 'get_historical_hit_rate', return_value=0.05):
            hit_rate = self.ranking.get_historical_hit_rate("123")
            self.assertEqual(hit_rate, 0.05)
    
    def test_generate_ranking_list(self):
        """测试排行榜生成"""
        predictions = self.create_mock_predictions()
        
        # 模拟历史命中率
        with patch.object(self.ranking, 'get_historical_hit_rate', return_value=0.05):
            ranking_list = self.ranking.generate_ranking_list(predictions, top_n=5)
        
        # 验证排行榜
        self.assertIsInstance(ranking_list, list)
        self.assertGreater(len(ranking_list), 0)
        
        # 验证排行榜项目
        for item in ranking_list:
            self.assertIsInstance(item, RankingItem)
            self.assertGreater(item.rank, 0)
            self.assertIsNotNone(item.number)
            self.assertGreaterEqual(item.confidence, 0)
            self.assertIsNotNone(item.recommendation_level)
    
    def test_get_ranking_statistics(self):
        """测试排行榜统计"""
        # 创建模拟排行榜数据
        ranking_list = [
            RankingItem(
                rank=1, number="123", confidence=0.8, composite_score=0.85,
                model_support_count=3, historical_hit_rate=0.05,
                recommendation_level="强烈推荐", prediction_method="测试"
            ),
            RankingItem(
                rank=2, number="456", confidence=0.6, composite_score=0.65,
                model_support_count=2, historical_hit_rate=0.03,
                recommendation_level="推荐", prediction_method="测试"
            )
        ]
        
        # 获取统计信息
        stats = self.ranking.get_ranking_statistics(ranking_list)
        
        # 验证统计结果
        self.assertEqual(stats['total_candidates'], 2)
        self.assertIn('confidence_stats', stats)
        self.assertIn('recommendation_distribution', stats)
        self.assertIn('model_support_stats', stats)
        self.assertIn('top_3_numbers', stats)

class TestIntegration(unittest.TestCase):
    """集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时数据库
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db_path = self.temp_db.name
        
        # 初始化完整系统
        self.tracker = ModelPerformanceTracker(db_path=self.db_path)
        self.fusion = AccuracyFocusedFusion(self.tracker)
        self.ranking = NumberRankingSystem(self.fusion, self.tracker, db_path=self.db_path)
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def test_end_to_end_prediction_pipeline(self):
        """测试端到端预测流程"""
        # 1. 添加历史数据
        for i in range(10):
            period = f"202500{i+1:02d}"
            predicted = f"{i%10}{(i+1)%10}{(i+2)%10}"
            actual = f"{i%10}{(i+1)%10}{(i+2)%10}" if i % 2 == 0 else f"{(i+1)%10}{(i+2)%10}{(i+3)%10}"
            
            self.tracker.track_prediction("test_model", predicted, actual, period, 0.5 + i*0.05)
        
        # 2. 创建预测数据
        predictions = []
        for j, model in enumerate(['model_a', 'model_b', 'model_c', 'model_d']):
            candidates = {}
            for k in range(5):
                number = f"{j}{k}{(j+k)%10}"
                confidence = 0.9 - k*0.1 - j*0.05
                candidates[number] = confidence
            
            pred = ModelPrediction(
                model_name=model,
                top_candidate=list(candidates.keys())[0],
                top_confidence=list(candidates.values())[0],
                all_candidates=candidates
            )
            predictions.append(pred)
        
        # 3. 执行融合预测
        best_result = self.fusion.get_single_best_prediction(predictions)
        
        # 4. 生成排行榜
        with patch.object(self.ranking, 'get_historical_hit_rate', return_value=0.05):
            ranking_list = self.ranking.generate_ranking_list(predictions, top_n=10)
        
        # 5. 验证结果
        self.assertIsNotNone(best_result)
        self.assertIsInstance(best_result, PredictionResult)
        self.assertGreater(len(ranking_list), 0)
        
        # 验证排行榜是否按置信度排序
        for i in range(len(ranking_list) - 1):
            self.assertGreaterEqual(ranking_list[i].confidence, ranking_list[i+1].confidence)

if __name__ == '__main__':
    # 运行所有测试
    unittest.main(verbosity=2)
