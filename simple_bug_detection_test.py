#!/usr/bin/env python3
"""
简单Bug检测系统测试
创建日期: 2025年7月24日
用途: 快速验证Bug检测系统的核心功能
"""

import sys
import os
import traceback

def test_imports():
    """测试核心组件导入"""
    print("🔍 测试组件导入...")
    
    try:
        sys.path.insert(0, '.')
        
        # 测试数据库管理器
        from src.bug_detection.core.database_manager import DatabaseManager
        print("✅ DatabaseManager 导入成功")
        
        # 测试JavaScript监控
        from src.bug_detection.monitoring.js_monitor import JavaScriptMonitor
        print("✅ JavaScriptMonitor 导入成功")
        
        # 测试Bug报告生成器
        from src.bug_detection.feedback.bug_reporter import BugReporter, IntelligentBugReporter
        print("✅ BugReporter 导入成功")
        
        # 测试API监控
        from src.bug_detection.monitoring.api_monitor import APIPerformanceMonitor
        print("✅ APIPerformanceMonitor 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        traceback.print_exc()
        return False

def test_database():
    """测试数据库功能"""
    print("\n🗄️ 测试数据库功能...")
    
    try:
        from src.bug_detection.core.database_manager import DatabaseManager
        
        # 创建数据库管理器
        db_manager = DatabaseManager()
        print("✅ 数据库管理器创建成功")
        
        # 测试保存Bug报告
        test_bug_data = {
            'error_type': 'test_error',
            'severity': 'low',
            'page_name': 'test_page',
            'error_message': '这是一个测试错误',
            'stack_trace': 'Test stack trace'
        }
        
        bug_id = db_manager.save_bug_report(test_bug_data)
        print(f"✅ Bug报告保存成功: {bug_id}")
        
        # 测试获取Bug报告
        bug_reports = db_manager.get_bug_reports(limit=1)
        if bug_reports:
            print(f"✅ Bug报告获取成功: {len(bug_reports)}个")
        else:
            print("⚠️ 未获取到Bug报告")
        
        # 测试性能指标保存
        db_manager.save_performance_metric('/api/test', 0.1, 200)
        print("✅ 性能指标保存成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        traceback.print_exc()
        return False

def test_bug_reporter():
    """测试Bug报告生成器"""
    print("\n📊 测试Bug报告生成器...")
    
    try:
        from src.bug_detection.feedback.bug_reporter import IntelligentBugReporter
        
        # 创建Bug报告生成器
        bug_reporter = IntelligentBugReporter()
        print("✅ Bug报告生成器创建成功")
        
        # 测试错误数据
        error_data = {
            'type': 'javascript',
            'message': 'TypeError: Cannot read property of undefined',
            'source': 'test.js',
            'line_number': 42,
            'page_url': 'http://localhost:8501/test'
        }
        
        # 生成Bug报告
        bug_report = bug_reporter.generate_enhanced_report(error_data)
        print(f"✅ Bug报告生成成功: {bug_report['id']}")
        
        # 验证报告内容
        required_fields = ['id', 'timestamp', 'error', 'category', 'priority']
        for field in required_fields:
            if field in bug_report:
                print(f"✅ 报告包含字段: {field}")
            else:
                print(f"❌ 报告缺少字段: {field}")
                return False
        
        # 验证修复建议
        if 'suggested_fixes' in bug_report and bug_report['suggested_fixes']:
            print(f"✅ 修复建议生成成功: {len(bug_report['suggested_fixes'])}个")
        else:
            print("⚠️ 未生成修复建议")
        
        return True
        
    except Exception as e:
        print(f"❌ Bug报告生成器测试失败: {e}")
        traceback.print_exc()
        return False

def test_javascript_monitor():
    """测试JavaScript监控器"""
    print("\n🔧 测试JavaScript监控器...")
    
    try:
        from src.bug_detection.monitoring.js_monitor import JavaScriptMonitor
        
        # 创建JavaScript监控器
        js_monitor = JavaScriptMonitor()
        print("✅ JavaScript监控器创建成功")
        
        # 测试会话ID生成
        session_id = js_monitor._get_session_id()
        if session_id and isinstance(session_id, str):
            print(f"✅ 会话ID生成成功: {session_id[:12]}...")
        else:
            print("❌ 会话ID生成失败")
            return False
        
        # 测试会话ID一致性
        session_id2 = js_monitor._get_session_id()
        if session_id == session_id2:
            print("✅ 会话ID一致性验证通过")
        else:
            print("❌ 会话ID一致性验证失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ JavaScript监控器测试失败: {e}")
        traceback.print_exc()
        return False

def test_api_monitor():
    """测试API监控器"""
    print("\n📈 测试API监控器...")
    
    try:
        from src.bug_detection.monitoring.api_monitor import APIPerformanceMonitor
        
        # 创建API监控器
        api_monitor = APIPerformanceMonitor(app=None)
        print("✅ API监控器创建成功")
        
        # 测试路径排除逻辑
        test_cases = [
            ('/docs', True),
            ('/redoc', True),
            ('/openapi.json', True),
            ('/api/test', False),
            ('/health', False)
        ]
        
        for path, should_exclude in test_cases:
            result = api_monitor._should_exclude_path(path)
            if result == should_exclude:
                print(f"✅ 路径排除测试通过: {path}")
            else:
                print(f"❌ 路径排除测试失败: {path}")
                return False
        
        # 测试性能摘要
        summary = api_monitor.get_performance_summary()
        if isinstance(summary, dict):
            print("✅ 性能摘要生成成功")
        else:
            print("❌ 性能摘要生成失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ API监控器测试失败: {e}")
        traceback.print_exc()
        return False

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始Bug检测系统功能测试")
    print("="*50)
    
    tests = [
        ("组件导入", test_imports),
        ("数据库功能", test_database),
        ("Bug报告生成器", test_bug_reporter),
        ("JavaScript监控器", test_javascript_monitor),
        ("API监控器", test_api_monitor)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}测试通过")
            else:
                failed += 1
                print(f"❌ {test_name}测试失败")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name}测试异常: {e}")
    
    print("\n" + "="*50)
    print("🎯 测试结果总结")
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"📊 成功率: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 所有测试通过！Bug检测系统功能正常。")
        print("🚀 您可以运行以下命令查看实际效果:")
        print("   python start_bug_detection_demo.py")
        print("   streamlit run bug_detection_demo.py")
        print("   python deep_bug_inspection.py --mode=deep")
        return True
    else:
        print(f"\n⚠️ 有{failed}个测试失败，请检查系统配置。")
        return False

def show_demo_instructions():
    """显示演示说明"""
    print("\n" + "="*60)
    print("🎮 Bug检测系统实际效果演示指南")
    print("="*60)
    
    print("\n🌐 Web界面演示 (推荐)")
    print("   命令: streamlit run bug_detection_demo.py")
    print("   地址: http://localhost:8501")
    print("   功能: 完整的Web界面演示，包含实时错误触发和分析")
    
    print("\n🔧 集成示例演示")
    print("   命令: streamlit run integrate_bug_detection_example.py")
    print("   地址: http://localhost:8502")
    print("   功能: 展示在福彩3D系统中的实际集成效果")
    
    print("\n🔍 深度检查演示")
    print("   命令: python deep_bug_inspection.py --mode=deep")
    print("   功能: 命令行深度系统诊断和分析")
    
    print("\n⚡ 一键启动演示")
    print("   命令: python start_bug_detection_demo.py")
    print("   功能: 交互式菜单，选择不同的演示模式")
    
    print("\n📋 实际效果验证清单:")
    print("   ✅ JavaScript错误实时捕获")
    print("   ✅ 智能Bug报告自动生成")
    print("   ✅ 性能监控和告警")
    print("   ✅ 错误分类和修复建议")
    print("   ✅ 系统健康度评估")
    print("   ✅ 与现有系统无缝集成")
    
    print("\n🎯 推荐体验流程:")
    print("   1. 运行 streamlit run bug_detection_demo.py")
    print("   2. 在Web界面中点击'触发JavaScript错误'")
    print("   3. 观察错误被实时捕获和分析")
    print("   4. 查看自动生成的Bug报告和修复建议")
    print("   5. 体验性能监控和系统状态功能")

if __name__ == "__main__":
    success = run_all_tests()
    
    if success:
        show_demo_instructions()
    
    sys.exit(0 if success else 1)
