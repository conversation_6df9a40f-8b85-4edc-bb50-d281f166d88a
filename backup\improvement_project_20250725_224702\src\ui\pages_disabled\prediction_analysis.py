import json
from datetime import datetime

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import requests
import streamlit as st

# 导入错误处理组件
try:
    from ..components.error_handler import SmartErrorHandler
    from ..components.error_middleware import (handle_api_errors,
                                               handle_page_errors)
except ImportError:
    # 如果导入失败，使用简单的装饰器
    def handle_page_errors(page_name):
        def decorator(func):
            return func
        return decorator

    def handle_api_errors(endpoint, retry_on_failure=True):
        def decorator(func):
            return func
        return decorator

@handle_page_errors("预测分析")
def show_prediction_analysis():
    """预测分析页面 - 基于真实API和数据"""
    st.header("🎯 预测分析")
    
    # 调用真实的智能融合预测API
    try:
        api_url = "http://127.0.0.1:8888/api/v1/prediction/intelligent-fusion/predict"
        
        with st.spinner("正在生成预测结果..."):
            response = requests.get(api_url, timeout=10)
            response.raise_for_status()
            prediction_data = response.json()
        
        if prediction_data.get('success'):
            # 显示实际预测结果
            st.subheader("📊 预测结果")
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric(
                    "预测号码", 
                    prediction_data['prediction']['numbers'],
                    help="基于智能融合算法的预测结果"
                )
            with col2:
                confidence = prediction_data['prediction']['confidence']
                st.metric(
                    "置信度", 
                    f"{confidence:.3f}",
                    delta=f"{(confidence-0.5)*100:+.1f}%",
                    help="预测结果的可信度评分"
                )
            with col3:
                fusion_score = prediction_data['prediction']['fusion_score']
                st.metric(
                    "融合评分", 
                    f"{fusion_score:.3f}",
                    help="多模型融合后的综合评分"
                )
            
            # 候选预测列表（基于真实算法）
            st.subheader("🎯 候选预测列表")
            candidates = prediction_data['prediction']['candidates']
            
            # 过滤有效的候选预测
            valid_candidates = [c for c in candidates if c.get('numbers') and c['numbers'] != '']
            
            if valid_candidates:
                # 创建候选预测数据框
                candidates_df = pd.DataFrame(valid_candidates[:10])  # 显示前10个
                
                # 格式化显示
                display_df = candidates_df.copy()
                display_df['confidence'] = display_df['confidence'].apply(lambda x: f"{x:.3f}")
                display_df['fusion_score'] = display_df['fusion_score'].apply(lambda x: f"{x:.3f}")
                display_df['supporting_models'] = display_df['supporting_models'].astype(str)
                
                st.dataframe(
                    display_df,
                    column_config={
                        "numbers": st.column_config.TextColumn("预测号码", width="medium"),
                        "confidence": st.column_config.TextColumn("置信度", width="small"), 
                        "fusion_score": st.column_config.TextColumn("融合评分", width="small"),
                        "supporting_models": st.column_config.TextColumn("支持模型数", width="small")
                    },
                    hide_index=True,
                    use_container_width=True
                )
                
                # 候选预测置信度分布图
                if len(valid_candidates) > 1:
                    fig_candidates = px.bar(
                        x=[c['numbers'] for c in valid_candidates[:10]],
                        y=[c['confidence'] for c in valid_candidates[:10]],
                        title="候选预测置信度分布",
                        labels={'x': '预测号码', 'y': '置信度'},
                        color=[c['confidence'] for c in valid_candidates[:10]],
                        color_continuous_scale='viridis'
                    )
                    fig_candidates.update_layout(showlegend=False, height=400)
                    st.plotly_chart(fig_candidates, use_container_width=True)
            
            # 模型贡献度分析（真实数据）
            st.subheader("🤖 模型贡献度分析")
            contributions = prediction_data['prediction']['model_contributions']
            
            if contributions:
                # 模型贡献度表格
                contrib_data = []
                for contrib in contributions:
                    contrib_data.append({
                        '模型': contrib['model'],
                        '置信度': f"{contrib['confidence']:.3f}",
                        '权重': f"{contrib['weight']:.3f}",
                        '贡献度': f"{contrib['contribution']:.3f}"
                    })
                
                contrib_df = pd.DataFrame(contrib_data)
                st.dataframe(contrib_df, hide_index=True, use_container_width=True)
                
                # 可视化模型权重分布
                col1, col2 = st.columns(2)
                
                with col1:
                    # 权重饼图
                    fig_pie = px.pie(
                        values=[c['weight'] for c in contributions],
                        names=[c['model'] for c in contributions],
                        title="模型权重分布"
                    )
                    st.plotly_chart(fig_pie, use_container_width=True)
                
                with col2:
                    # 贡献度柱状图
                    fig_bar = px.bar(
                        x=[c['model'] for c in contributions],
                        y=[c['contribution'] for c in contributions],
                        title="模型贡献度对比",
                        labels={'x': '模型', 'y': '贡献度'},
                        color=[c['contribution'] for c in contributions],
                        color_continuous_scale='blues'
                    )
                    fig_bar.update_layout(showlegend=False)
                    st.plotly_chart(fig_bar, use_container_width=True)
            
            # 融合详情
            st.subheader("🔄 融合系统详情")
            fusion_details = prediction_data['prediction']['fusion_details']
            
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("候选数量", fusion_details['total_candidates'])
            with col2:
                st.metric("共识水平", f"{fusion_details['consensus_level']:.3f}")
            with col3:
                st.metric("数据样本", prediction_data['data_count'])
            with col4:
                st.metric("融合方法", prediction_data['prediction']['fusion_info']['fusion_method'])
            
            # 模型权重详情
            if 'model_weights' in fusion_details:
                st.subheader("⚖️ 模型权重详情")
                weights_data = []
                for model, weight in fusion_details['model_weights'].items():
                    weights_data.append({
                        '模型': model,
                        '权重': f"{weight:.3f}",
                        '权重百分比': f"{weight*100:.1f}%"
                    })
                
                weights_df = pd.DataFrame(weights_data)
                st.dataframe(weights_df, hide_index=True, use_container_width=True)
            
            # 预测信息
            st.subheader("ℹ️ 预测信息")
            info_col1, info_col2 = st.columns(2)
            
            with info_col1:
                st.write(f"**预测模式**: {prediction_data['prediction_mode']}")
                st.write(f"**生成时间**: {prediction_data['timestamp']}")
                st.write(f"**参与模型**: {', '.join(prediction_data['prediction']['fusion_info']['participating_models'])}")
            
            with info_col2:
                st.write(f"**融合方法**: {prediction_data['prediction']['fusion_info']['fusion_method']}")
                st.write(f"**最大候选数**: {prediction_data['prediction']['fusion_info']['max_candidates']}")
                st.write(f"**置信度阈值**: {prediction_data['prediction']['fusion_info']['confidence_threshold']}")
            
            # 预测历史记录
            st.subheader("📋 预测历史记录")
            try:
                history_url = "http://127.0.0.1:8888/api/v1/prediction/history"
                history_response = requests.get(history_url, timeout=5)
                
                if history_response.status_code == 200:
                    history_data = history_response.json()
                    
                    if history_data.get('success') and history_data.get('history'):
                        history_df = pd.DataFrame(history_data['history'][:10])  # 显示最近10条
                        
                        # 格式化历史记录
                        if not history_df.empty:
                            display_history = history_df.copy()
                            if 'timestamp' in display_history.columns:
                                display_history['时间'] = pd.to_datetime(display_history['timestamp']).dt.strftime('%Y-%m-%d %H:%M')
                            
                            column_mapping = {
                                'period': '期号',
                                'predicted_numbers': '预测号码',
                                'confidence': '置信度',
                                'actual_numbers': '实际开奖',
                                'is_correct': '是否正确'
                            }
                            
                            for old_col, new_col in column_mapping.items():
                                if old_col in display_history.columns:
                                    display_history[new_col] = display_history[old_col]
                            
                            # 选择要显示的列
                            display_cols = ['时间', '期号', '预测号码', '置信度', '实际开奖', '是否正确']
                            available_cols = [col for col in display_cols if col in display_history.columns]
                            
                            if available_cols:
                                st.dataframe(
                                    display_history[available_cols],
                                    hide_index=True,
                                    use_container_width=True
                                )
                            else:
                                st.dataframe(display_history, hide_index=True, use_container_width=True)
                    else:
                        st.info("暂无预测历史记录")
                else:
                    st.warning("无法获取预测历史记录")
            except Exception as e:
                st.warning(f"获取预测历史时出错: {str(e)}")
            
        else:
            st.error("预测API返回错误，请检查服务状态")
            if 'message' in prediction_data:
                st.error(f"错误信息: {prediction_data['message']}")
            
    except requests.exceptions.RequestException as e:
        st.error(f"无法连接到预测API服务: {str(e)}")
        st.info("请确保API服务(127.0.0.1:8888)正在运行")
        
        # 显示连接诊断信息
        with st.expander("🔍 连接诊断信息"):
            st.write("**API端点**: http://127.0.0.1:8888/api/v1/prediction/intelligent-fusion/predict")
            st.write("**错误类型**: 连接错误")
            st.write("**可能原因**:")
            st.write("- API服务未启动")
            st.write("- 端口8888被占用")
            st.write("- 网络连接问题")
            st.write("**解决建议**:")
            st.write("- 检查API服务状态")
            st.write("- 重启API服务")
            st.write("- 检查防火墙设置")
            
    except Exception as e:
        st.error(f"预测分析出现错误: {str(e)}")
        
        # 显示详细错误信息
        with st.expander("🔍 详细错误信息"):
            st.code(str(e))

if __name__ == "__main__":
    show_prediction_analysis()
