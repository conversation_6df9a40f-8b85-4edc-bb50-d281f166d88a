#!/usr/bin/env python3
"""
检查数据库状态
"""

import sqlite3
import os

def check_database():
    db_path = "data/test_lottery.db"
    
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return
    
    # 检查文件大小
    file_size = os.path.getsize(db_path)
    print(f"✅ 数据库文件大小: {file_size / 1024 / 1024:.2f} MB")
    
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 检查表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row[0] for row in cursor.fetchall()]
    print(f"✅ 数据表: {tables}")
    
    # 检查记录数
    cursor.execute("SELECT COUNT(*) FROM lottery_records")
    record_count = cursor.fetchone()[0]
    print(f"✅ 记录数: {record_count}")
    
    # 检查缓存表
    cursor.execute("SELECT COUNT(*) FROM statistics_cache")
    cache_count = cursor.fetchone()[0]
    print(f"✅ 缓存记录数: {cache_count}")
    
    # 检查最新记录
    cursor.execute("SELECT period, date, numbers FROM lottery_records ORDER BY date DESC LIMIT 3")
    latest_records = cursor.fetchall()
    print(f"✅ 最新3条记录:")
    for record in latest_records:
        print(f"   期号: {record[0]}, 日期: {record[1]}, 号码: {record[2]}")
    
    conn.close()
    print("✅ 数据库检查完成")

if __name__ == "__main__":
    check_database()
