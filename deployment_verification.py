#!/usr/bin/env python3
"""
部署验证脚本
创建日期: 2025年7月25日
用途: 验证WebSocket修复后的系统在生产环境中的稳定性和性能表现
"""

import json
import os
import sys
import time
from datetime import datetime
from typing import Dict, List

def verify_deployment():
    """验证部署状态"""
    print("🚀 WebSocket修复部署验证")
    print("=" * 50)
    
    verification_results = {
        'environment_check': {},
        'dependency_verification': {},
        'service_availability': {},
        'performance_validation': {},
        'stability_assessment': {}
    }
    
    # 1. 环境检查
    print("\n🔧 1. 环境检查")
    
    # Python版本检查
    python_version = sys.version_info
    expected_version = (3, 11, 9)
    
    if python_version[:3] == expected_version:
        print(f"  Python版本: ✅ {python_version.major}.{python_version.minor}.{python_version.micro}")
        verification_results['environment_check']['python_version'] = {
            'status': 'pass',
            'version': f"{python_version.major}.{python_version.minor}.{python_version.micro}"
        }
    else:
        print(f"  Python版本: ⚠️ {python_version.major}.{python_version.minor}.{python_version.micro} (期望 3.11.9)")
        verification_results['environment_check']['python_version'] = {
            'status': 'warning',
            'version': f"{python_version.major}.{python_version.minor}.{python_version.micro}",
            'expected': "3.11.9"
        }
    
    # 工作目录检查
    current_dir = os.getcwd()
    required_dirs = ['src', 'src/ui', 'src/api', 'src/bug_detection']
    
    missing_dirs = []
    for dir_name in required_dirs:
        if os.path.exists(dir_name):
            print(f"  目录结构: ✅ {dir_name}")
        else:
            print(f"  目录结构: ❌ {dir_name} 缺失")
            missing_dirs.append(dir_name)
    
    verification_results['environment_check']['directory_structure'] = {
        'status': 'pass' if not missing_dirs else 'fail',
        'missing_directories': missing_dirs
    }
    
    # 2. 依赖验证
    print("\n📦 2. 依赖验证")
    
    critical_dependencies = [
        'fastapi',
        'websockets', 
        'uvicorn',
        'streamlit',
        'requests',
        'asyncio'
    ]
    
    dependency_status = {}
    
    for dep in critical_dependencies:
        try:
            __import__(dep)
            print(f"  {dep}: ✅ 已安装")
            dependency_status[dep] = {'status': 'installed'}
        except ImportError:
            print(f"  {dep}: ❌ 未安装")
            dependency_status[dep] = {'status': 'missing'}
    
    verification_results['dependency_verification'] = dependency_status
    
    # 3. 服务可用性检查
    print("\n🌐 3. 服务可用性检查")
    
    # 检查关键文件
    critical_files = [
        'src/ui/main.py',
        'src/api/production_main.py',
        'src/bug_detection/realtime/event_bus.py',
        'src/bug_detection/realtime/websocket_manager.py',
        'src/ui/components/fallback_manager.py'
    ]
    
    file_status = {}
    
    for file_path in critical_files:
        if os.path.exists(file_path):
            print(f"  {file_path}: ✅ 存在")
            file_status[file_path] = {'status': 'exists'}
        else:
            print(f"  {file_path}: ❌ 缺失")
            file_status[file_path] = {'status': 'missing'}
    
    verification_results['service_availability'] = file_status
    
    # 4. 性能验证
    print("\n⚡ 4. 性能验证")
    
    sys.path.append('src')
    
    # 模块导入性能测试
    import_performance = {}
    
    modules_to_test = [
        ('事件总线', 'bug_detection.realtime.event_bus'),
        ('WebSocket管理器', 'bug_detection.realtime.websocket_manager'),
        ('降级管理器', 'ui.components.fallback_manager')
    ]
    
    for module_name, module_path in modules_to_test:
        try:
            start_time = time.time()
            __import__(module_path)
            end_time = time.time()
            
            import_time = (end_time - start_time) * 1000
            
            if import_time < 1000:
                status = "✅ 优秀"
            elif import_time < 2000:
                status = "✅ 良好"
            else:
                status = "⚠️ 需要优化"
            
            print(f"  {module_name}导入: {status} ({import_time:.2f}ms)")
            import_performance[module_name] = {
                'import_time_ms': import_time,
                'status': 'pass' if import_time < 2000 else 'warning'
            }
            
        except Exception as e:
            print(f"  {module_name}导入: ❌ 失败 - {e}")
            import_performance[module_name] = {
                'status': 'fail',
                'error': str(e)
            }
    
    verification_results['performance_validation'] = import_performance
    
    # 5. 稳定性评估
    print("\n🔒 5. 稳定性评估")
    
    stability_tests = {}
    
    # 测试降级管理器稳定性
    try:
        from ui.components.fallback_manager import FallbackManager
        
        # 创建多个实例测试
        managers = []
        for i in range(10):
            manager = FallbackManager()
            managers.append(manager)
        
        print("  降级管理器创建: ✅ 稳定")
        stability_tests['fallback_manager_creation'] = {'status': 'stable'}
        
        # 测试缓存操作稳定性
        test_manager = FallbackManager()
        for i in range(50):
            test_manager._update_cache(f"test_{i}", {"data": f"value_{i}"})
            test_manager.get_cached_data(f"test_{i}")
        
        print("  缓存操作稳定性: ✅ 稳定")
        stability_tests['cache_operations'] = {'status': 'stable'}
        
    except Exception as e:
        print(f"  稳定性测试: ❌ 失败 - {e}")
        stability_tests['stability_test'] = {'status': 'fail', 'error': str(e)}
    
    # 测试错误处理稳定性
    try:
        from ui.main import safe_get_nested, format_number
        
        # 测试各种边界情况
        test_cases = [
            (None, 'key1', 'key2'),
            ({}, 'missing_key'),
            ({'key': None}, 'key', 'subkey'),
            ({'key': {'subkey': 'value'}}, 'key', 'subkey')
        ]
        
        for test_case in test_cases:
            result = safe_get_nested(*test_case)
            # 只要不抛异常就算稳定
        
        print("  错误处理稳定性: ✅ 稳定")
        stability_tests['error_handling'] = {'status': 'stable'}
        
    except Exception as e:
        print(f"  错误处理测试: ❌ 失败 - {e}")
        stability_tests['error_handling'] = {'status': 'fail', 'error': str(e)}
    
    verification_results['stability_assessment'] = stability_tests
    
    # 6. 生成验证报告
    print("\n" + "=" * 50)
    print("📋 部署验证报告")
    print("=" * 50)
    
    # 计算总体得分
    total_score = 0
    max_score = 0
    
    # 环境检查得分 (20分)
    env_score = 0
    if verification_results['environment_check']['python_version']['status'] == 'pass':
        env_score += 10
    elif verification_results['environment_check']['python_version']['status'] == 'warning':
        env_score += 7
    
    if verification_results['environment_check']['directory_structure']['status'] == 'pass':
        env_score += 10
    
    total_score += env_score
    max_score += 20
    
    # 依赖验证得分 (25分)
    dep_success = sum(1 for dep in verification_results['dependency_verification'].values() 
                     if dep['status'] == 'installed')
    dep_total = len(verification_results['dependency_verification'])
    dep_score = (dep_success / dep_total) * 25 if dep_total > 0 else 0
    
    total_score += dep_score
    max_score += 25
    
    # 服务可用性得分 (25分)
    service_success = sum(1 for service in verification_results['service_availability'].values() 
                         if service['status'] == 'exists')
    service_total = len(verification_results['service_availability'])
    service_score = (service_success / service_total) * 25 if service_total > 0 else 0
    
    total_score += service_score
    max_score += 25
    
    # 性能验证得分 (15分)
    perf_success = sum(1 for perf in verification_results['performance_validation'].values() 
                      if perf['status'] in ['pass', 'warning'])
    perf_total = len(verification_results['performance_validation'])
    perf_score = (perf_success / perf_total) * 15 if perf_total > 0 else 0
    
    total_score += perf_score
    max_score += 15
    
    # 稳定性评估得分 (15分)
    stability_success = sum(1 for stability in verification_results['stability_assessment'].values() 
                           if stability['status'] == 'stable')
    stability_total = len(verification_results['stability_assessment'])
    stability_score = (stability_success / stability_total) * 15 if stability_total > 0 else 0
    
    total_score += stability_score
    max_score += 15
    
    # 总体评估
    final_score = (total_score / max_score * 100) if max_score > 0 else 0
    
    print(f"\n📊 验证结果汇总:")
    print(f"  🔧 环境检查: {env_score}/20分")
    print(f"  📦 依赖验证: {dep_score:.1f}/25分 ({dep_success}/{dep_total})")
    print(f"  🌐 服务可用性: {service_score:.1f}/25分 ({service_success}/{service_total})")
    print(f"  ⚡ 性能验证: {perf_score:.1f}/15分 ({perf_success}/{perf_total})")
    print(f"  🔒 稳定性评估: {stability_score:.1f}/15分 ({stability_success}/{stability_total})")
    
    print(f"\n📈 总体评估:")
    print(f"  总得分: {total_score:.1f}/{max_score} ({final_score:.1f}%)")
    
    if final_score >= 90:
        print("  🎉 部署状态: 优秀 - 可以投入生产使用")
        deployment_status = "优秀"
    elif final_score >= 80:
        print("  ✅ 部署状态: 良好 - 可以投入生产使用")
        deployment_status = "良好"
    elif final_score >= 70:
        print("  ⚠️ 部署状态: 一般 - 建议修复问题后部署")
        deployment_status = "一般"
    else:
        print("  ❌ 部署状态: 不合格 - 需要修复关键问题")
        deployment_status = "不合格"
    
    print(f"\n🎯 部署建议:")
    
    suggestions = []
    
    if verification_results['environment_check']['python_version']['status'] != 'pass':
        suggestions.append("- 建议使用Python 3.11.9以确保最佳兼容性")
    
    if any(dep['status'] == 'missing' for dep in verification_results['dependency_verification'].values()):
        suggestions.append("- 安装缺失的依赖库")
    
    if any(service['status'] == 'missing' for service in verification_results['service_availability'].values()):
        suggestions.append("- 确保所有关键文件存在")
    
    if any(perf['status'] == 'fail' for perf in verification_results['performance_validation'].values()):
        suggestions.append("- 修复模块导入问题")
    
    if not suggestions:
        suggestions.append("- 系统状态良好，可以正常部署")
    
    for suggestion in suggestions:
        print(suggestion)
    
    # 保存验证结果
    verification_report = {
        'timestamp': datetime.now().isoformat(),
        'overall_score': final_score,
        'deployment_status': deployment_status,
        'detailed_results': verification_results,
        'deployment_suggestions': suggestions
    }
    
    with open('deployment_verification_results.json', 'w', encoding='utf-8') as f:
        json.dump(verification_report, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 详细验证结果已保存到: deployment_verification_results.json")
    
    return deployment_status

if __name__ == "__main__":
    verify_deployment()
