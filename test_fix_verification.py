#!/usr/bin/env python3
"""
验证预测模型数据同步修复效果
"""

import sys
import os
sys.path.append('src')

def test_intelligent_fusion_fix():
    """测试智能融合系统修复"""
    print("🔍 测试智能融合系统修复效果")
    
    try:
        from prediction.intelligent_fusion import IntelligentFusionSystem
        
        # 初始化系统
        system = IntelligentFusionSystem()
        print(f"✅ 智能融合系统初始化成功")
        
        # 测试动态数据计数
        current_count = system._get_current_data_count()
        print(f"✅ 动态数据计数: {current_count}")
        
        # 测试数据变化检查
        data_changed = system._check_data_changed()
        print(f"✅ 数据变化检查: {'有变化' if data_changed else '无变化'}")
        
        # 测试训练状态
        print(f"✅ 模型训练状态: {'已训练' if system.models_trained else '未训练'}")
        print(f"✅ 融合就绪状态: {'就绪' if system.fusion_ready else '未就绪'}")
        print(f"✅ 训练数据计数: {system.training_data_count}")
        
        # 如果数据有变化或模型未训练，进行训练
        if data_changed or not system.models_trained:
            print("🚀 开始重新训练模型...")
            result = system.train_all_models(force_retrain=True)
            print(f"✅ 训练结果: {result.get('success', False)}")
            if result.get('success'):
                print(f"✅ 新的训练数据计数: {system.training_data_count}")
        else:
            print("ℹ️  模型已是最新状态，无需重训练")
        
        return True, current_count
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_database_count():
    """测试数据库记录数"""
    print("\n🔍 测试数据库记录数")
    
    try:
        from core.database import DatabaseManager
        
        db_manager = DatabaseManager()
        count = db_manager.get_records_count()
        print(f"✅ 数据库记录数: {count}")
        
        return count
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return None

def test_prediction_result():
    """测试预测结果"""
    print("\n🔍 测试预测结果")
    
    try:
        from prediction.intelligent_fusion import IntelligentFusionSystem
        from core.database import DatabaseManager
        
        # 初始化系统
        system = IntelligentFusionSystem()
        db_manager = DatabaseManager()
        
        # 获取测试数据
        recent_records = db_manager.get_recent_records(50)
        if not recent_records:
            print("❌ 无法获取测试数据")
            return None
        
        test_data = [record.numbers for record in recent_records]
        
        # 进行预测
        prediction = system.generate_fusion_prediction(
            test_data,
            max_candidates=10,
            confidence_threshold=0.5
        )
        
        if 'error' in prediction:
            print(f"❌ 预测失败: {prediction['error']}")
            return None
        
        predicted_number = prediction.get('predicted_number', 'N/A')
        confidence = prediction.get('confidence', 0)
        
        print(f"✅ 预测成功")
        print(f"   预测号码: {predicted_number}")
        print(f"   置信度: {confidence:.1%}")
        
        # 检查是否还是固定的056
        if predicted_number == "056":
            print("⚠️  警告: 预测结果仍为056，可能存在问题")
            return False
        else:
            print("✅ 预测结果已更新，不再固定为056")
            return True
        
    except Exception as e:
        print(f"❌ 预测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("🚀 开始验证修复效果")
    print("=" * 60)
    
    # 测试数据库
    db_count = test_database_count()
    
    # 测试智能融合系统
    fusion_success, system_count = test_intelligent_fusion_fix()
    
    # 测试预测结果
    prediction_success = test_prediction_result()
    
    print("\n" + "=" * 60)
    print("📊 验证结果总结")
    print("=" * 60)
    
    if db_count and system_count:
        if db_count == system_count:
            print("✅ 数据同步正常: 数据库和系统记录数一致")
        else:
            print(f"⚠️  数据同步异常: 数据库({db_count}) vs 系统({system_count})")
    
    if fusion_success:
        print("✅ 智能融合系统修复成功")
    else:
        print("❌ 智能融合系统修复失败")
    
    if prediction_success:
        print("✅ 预测结果已更新，修复成功")
    elif prediction_success is False:
        print("❌ 预测结果仍为056，修复未完全生效")
    else:
        print("❌ 预测功能异常")
    
    print("\n🎉 验证完成！")
    
    # 总体评估
    if db_count and system_count and db_count == system_count and fusion_success and prediction_success:
        print("🎊 所有修复都成功！预测模型数据同步问题已解决。")
    else:
        print("⚠️  部分修复可能需要进一步调整。")
