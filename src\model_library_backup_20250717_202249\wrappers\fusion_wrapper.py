"""
融合模型包装器

包装IntelligentFusionSystem智能融合系统
"""

import sys
import numpy as np
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# 添加项目路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.model_library.base_model import (
    BaseModel, ModelInfo, ModelStatusInfo, PredictionResult, 
    TrainingResult, ValidationResult, ModelType, ModelStatus
)
from src.model_library.utils.data_utils import LotteryDataLoader
from src.model_library.utils.validation_utils import ModelValidator

try:
    from src.prediction.intelligent_fusion import IntelligentFusionSystem
except ImportError:
    IntelligentFusionSystem = None


class FusionModelWrapper(BaseModel):
    """融合模型包装器"""
    
    def __init__(self):
        super().__init__(
            model_id="intelligent_fusion",
            name="智能融合预测系统",
            description="集成多种算法的自适应融合预测模型，支持动态权重调整和多策略组合",
            model_type=ModelType.FUSION
        )
        
        # 初始化内部模型
        self.fusion_system = None
        self._is_trained = False
        self._training_data_size = 0
        self._last_training_time = None
        
        # 模型参数
        self._parameters = {
            "fusion_strategy": "adaptive",  # "equal", "weighted", "adaptive"
            "weight_update_frequency": 10,  # 权重更新频率
            "performance_window": 50,  # 性能评估窗口
            "min_confidence_threshold": 0.3,  # 最小置信度阈值
            "enable_dynamic_weights": True,  # 启用动态权重
            "algorithms": ["markov", "trend", "statistical", "lstm"]  # 使用的算法
        }
        
        self._initialize_model()
    
    def _initialize_model(self):
        """初始化内部模型"""
        try:
            if IntelligentFusionSystem:
                self.fusion_system = IntelligentFusionSystem()
                
                # 设置参数（如果支持）
                if hasattr(self.fusion_system, 'set_fusion_strategy'):
                    self.fusion_system.set_fusion_strategy(self._parameters["fusion_strategy"])
                
        except Exception as e:
            print(f"警告：初始化融合系统失败: {e}")
    
    def get_info(self) -> ModelInfo:
        """获取模型基本信息"""
        return ModelInfo(
            model_id=self.model_id,
            name=self.name,
            description=self.description,
            model_type=self.model_type,
            version="3.0.0",
            author="Augment Agent",
            created_at=datetime.now(),
            updated_at=datetime.now(),
            data_requirements={
                "min_records": 500,
                "required_fields": ["period", "date", "number"],
                "data_format": "福彩3D历史开奖数据",
                "algorithms_data": "支持多种算法的数据格式"
            },
            feature_engineering={
                "features": ["多算法融合", "动态权重", "自适应策略", "性能监控"],
                "fusion_methods": ["加权平均", "投票机制", "置信度融合"],
                "algorithms": self._parameters["algorithms"]
            },
            parameters=self._parameters,
            is_active=True
        )
    
    def get_status(self) -> ModelStatusInfo:
        """获取模型状态信息"""
        # 检查数据是否就绪
        data_loader = LotteryDataLoader()
        try:
            records = data_loader.load_all_records()
            data_ready = len(records) >= self.get_required_data_size()
            training_data_size = len(records)
        except Exception:
            data_ready = False
            training_data_size = 0
        
        # 检查特征是否就绪
        features_ready = self.fusion_system is not None
        
        # 确定状态
        if not data_ready:
            status = ModelStatus.NOT_READY
            error_message = "训练数据不足"
        elif not features_ready:
            status = ModelStatus.NOT_READY
            error_message = "融合系统未就绪"
        elif not self._is_trained:
            status = ModelStatus.READY
            error_message = None
        else:
            status = ModelStatus.TRAINED
            error_message = None
        
        return ModelStatusInfo(
            model_id=self.model_id,
            status=status,
            data_ready=data_ready,
            features_ready=features_ready,
            trained=self._is_trained,
            up_to_date=True,
            training_data_size=training_data_size,
            last_training_time=self._last_training_time,
            last_check_time=datetime.now(),
            error_message=error_message
        )
    
    def train(self, data: List[Dict[str, Any]]) -> TrainingResult:
        """训练模型"""
        start_time = datetime.now()
        
        try:
            if not data:
                return TrainingResult(
                    model_id=self.model_id,
                    training_time=start_time,
                    success=False,
                    training_data_size=0,
                    training_duration=0.0,
                    error_message="训练数据为空"
                )
            
            if len(data) < self.get_required_data_size():
                return TrainingResult(
                    model_id=self.model_id,
                    training_time=start_time,
                    success=False,
                    training_data_size=len(data),
                    training_duration=0.0,
                    error_message=f"训练数据不足，需要至少{self.get_required_data_size()}条记录"
                )
            
            # 训练融合系统
            success = True
            error_message = None
            
            if self.fusion_system:
                try:
                    # 如果融合系统有train方法则调用
                    if hasattr(self.fusion_system, 'train'):
                        self.fusion_system.train(data)
                    elif hasattr(self.fusion_system, 'update_weights'):
                        # 或者更新权重
                        self.fusion_system.update_weights(data)
                except Exception as e:
                    error_message = f"融合系统训练失败: {e}"
                    success = False
            else:
                error_message = "融合系统未初始化"
                success = False
            
            # 更新训练状态
            self._is_trained = success
            self._training_data_size = len(data)
            self._last_training_time = start_time
            
            end_time = datetime.now()
            training_duration = (end_time - start_time).total_seconds()
            
            return TrainingResult(
                model_id=self.model_id,
                training_time=start_time,
                success=success,
                training_data_size=len(data),
                training_duration=training_duration,
                metrics={
                    "fusion_strategy": self._parameters["fusion_strategy"],
                    "algorithms_count": len(self._parameters["algorithms"]),
                    "performance_window": self._parameters["performance_window"]
                },
                error_message=error_message
            )
            
        except Exception as e:
            end_time = datetime.now()
            training_duration = (end_time - start_time).total_seconds()
            
            return TrainingResult(
                model_id=self.model_id,
                training_time=start_time,
                success=False,
                training_data_size=len(data),
                training_duration=training_duration,
                error_message=str(e)
            )
    
    def predict(self, history: List[Dict[str, Any]], top_n: int = 3) -> PredictionResult:
        """执行预测"""
        try:
            if not history:
                raise ValueError("历史数据为空")
            
            # 获取下一期期号
            latest_period = 0
            if history and 'period' in history[-1]:
                latest_period = int(history[-1]['period'])
            target_period = latest_period + 1
            
            # 执行融合预测
            prediction_result = self._execute_fusion_prediction(history, top_n)
            
            # 计算置信度
            confidence = self._calculate_fusion_confidence(prediction_result)
            
            return PredictionResult(
                model_id=self.model_id,
                prediction_time=datetime.now(),
                target_period=target_period,
                百位=prediction_result['百位'],
                十位=prediction_result['十位'],
                个位=prediction_result['个位'],
                和值=prediction_result.get('和值', {}),
                跨度=prediction_result.get('跨度', {}),
                confidence=confidence,
                metadata={
                    "fusion_strategy": self._parameters["fusion_strategy"],
                    "algorithms_used": self._parameters["algorithms"],
                    "history_size": len(history),
                    "model_version": "3.0.0"
                }
            )
            
        except Exception as e:
            # 返回默认预测结果
            return PredictionResult(
                model_id=self.model_id,
                prediction_time=datetime.now(),
                target_period=0,
                百位={str(i): 0.1 for i in range(10)},
                十位={str(i): 0.1 for i in range(10)},
                个位={str(i): 0.1 for i in range(10)},
                confidence=0.0,
                metadata={"error": str(e)}
            )
    
    def _execute_fusion_prediction(self, history: List[Dict[str, Any]], top_n: int) -> Dict[str, Dict[str, float]]:
        """执行融合预测逻辑"""
        # 默认预测结果
        default_result = {
            '百位': {str(i): 0.1 for i in range(10)},
            '十位': {str(i): 0.1 for i in range(10)},
            '个位': {str(i): 0.1 for i in range(10)}
        }
        
        try:
            if self.fusion_system and hasattr(self.fusion_system, 'predict'):
                # 使用融合系统进行预测
                result = self.fusion_system.predict(history[-100:])  # 使用最近100期
                if result:
                    return self._convert_fusion_result(result)
            
            elif self.fusion_system and hasattr(self.fusion_system, 'get_fusion_prediction'):
                # 或者使用其他预测方法
                result = self.fusion_system.get_fusion_prediction(history[-100:])
                if result:
                    return self._convert_fusion_result(result)
            
        except Exception as e:
            print(f"融合预测执行失败: {e}")
        
        return default_result
    
    def _convert_fusion_result(self, result) -> Dict[str, Dict[str, float]]:
        """转换融合预测结果格式"""
        # 这里需要根据实际的融合系统结果格式进行转换
        # 暂时返回默认格式
        if isinstance(result, dict):
            converted_result = {}
            for pos in ['百位', '十位', '个位']:
                if pos in result:
                    converted_result[pos] = result[pos]
                else:
                    converted_result[pos] = {str(i): 0.1 for i in range(10)}
            return converted_result
        
        return {
            '百位': {str(i): 0.1 for i in range(10)},
            '十位': {str(i): 0.1 for i in range(10)},
            '个位': {str(i): 0.1 for i in range(10)}
        }
    
    def _calculate_fusion_confidence(self, prediction_result: Dict[str, Dict[str, float]]) -> float:
        """计算融合预测的置信度"""
        try:
            # 基于多算法一致性计算置信度
            total_variance = 0.0
            
            for pos_probs in prediction_result.values():
                if pos_probs:
                    # 计算概率分布的方差
                    probs = list(pos_probs.values())
                    variance = np.var(probs)
                    total_variance += variance
            
            # 方差越小，一致性越高，置信度越高
            avg_variance = total_variance / 3.0
            max_variance = 0.09  # 均匀分布的方差
            
            # 置信度 = 1 - (方差 / 最大方差)
            confidence = max(0.0, 1.0 - (avg_variance / max_variance))
            
            # 考虑融合策略的影响
            if self._parameters["fusion_strategy"] == "adaptive":
                confidence *= 1.1  # 自适应策略置信度加成
            
            return min(1.0, confidence)
            
        except Exception:
            return 0.6  # 默认置信度
    
    def get_parameters(self) -> Dict[str, Any]:
        """获取模型参数"""
        return self._parameters.copy()
    
    def set_parameters(self, parameters: Dict[str, Any]) -> bool:
        """设置模型参数"""
        try:
            for key, value in parameters.items():
                if key in self._parameters:
                    self._parameters[key] = value
                    
                    # 更新融合系统参数
                    if self.fusion_system:
                        if key == "fusion_strategy" and hasattr(self.fusion_system, 'set_fusion_strategy'):
                            self.fusion_system.set_fusion_strategy(value)
                        elif hasattr(self.fusion_system, f'set_{key}'):
                            getattr(self.fusion_system, f'set_{key}')(value)
            
            return True
        except Exception:
            return False
    
    def validate(self, test_data: List[Dict[str, Any]]) -> ValidationResult:
        """验证模型"""
        try:
            if not test_data:
                return ValidationResult(
                    model_id=self.model_id,
                    validation_time=datetime.now(),
                    validation_type="simple_test",
                    metrics={},
                    success=False,
                    error_message="测试数据为空"
                )
            
            # 验证融合预测
            sample_history = test_data[:50] if len(test_data) > 50 else test_data
            prediction = self.predict(sample_history)
            
            # 验证预测格式
            validation_result = ModelValidator.validate_prediction_format({
                '百位': prediction.百位,
                '十位': prediction.十位,
                '个位': prediction.个位
            })
            
            metrics = {
                "format_valid": validation_result["valid"],
                "test_data_size": len(test_data),
                "prediction_confidence": prediction.confidence,
                "fusion_strategy": self._parameters["fusion_strategy"],
                "algorithms_count": len(self._parameters["algorithms"])
            }
            
            return ValidationResult(
                model_id=self.model_id,
                validation_time=datetime.now(),
                validation_type="fusion_validation",
                metrics=metrics,
                success=validation_result["valid"],
                error_message=validation_result.get("error")
            )
            
        except Exception as e:
            return ValidationResult(
                model_id=self.model_id,
                validation_time=datetime.now(),
                validation_type="error_test",
                metrics={},
                success=False,
                error_message=str(e)
            )
    
    def calculate_confidence(self, prediction: PredictionResult) -> float:
        """计算预测置信度"""
        return self._calculate_fusion_confidence({
            '百位': prediction.百位,
            '十位': prediction.十位,
            '个位': prediction.个位
        })
    
    def get_required_data_size(self) -> int:
        """获取模型所需的最小数据量"""
        return 500  # 融合系统需要较少的数据
