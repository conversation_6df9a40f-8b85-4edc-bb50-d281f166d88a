#!/usr/bin/env python3
"""
成功因子提取器
Success Factor Extractor

提取导致预测成功的关键因子
"""

import logging
import os
import sys
from collections import Counter, defaultdict
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple

import numpy as np

sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from core.unified_prediction_storage import PredictionRecord


@dataclass
class SuccessFactorResult:
    """成功因子提取结果"""
    model_name: str
    top_factors: List[Dict[str, Any]]
    detailed_analysis: Dict[str, Any]
    amplification_suggestions: List[Dict[str, Any]]
    replication_strategy: Dict[str, Any]
    confidence_score: float
    analysis_timestamp: datetime


class SuccessFactorExtractor:
    """成功因子提取器"""
    
    def __init__(self):
        """初始化成功因子提取器"""
        self.logger = logging.getLogger(__name__)
        
        # 分析方法配置
        self.analysis_methods = {
            'feature_importance': self._analyze_feature_importance,
            'temporal_patterns': self._analyze_temporal_patterns,
            'confidence_patterns': self._analyze_confidence_patterns,
            'numerical_patterns': self._analyze_numerical_patterns,
            'contextual_factors': self._analyze_contextual_factors
        }
        
        # 因子权重
        self.factor_weights = {
            'feature_importance': 0.25,
            'temporal_patterns': 0.20,
            'confidence_patterns': 0.20,
            'numerical_patterns': 0.20,
            'contextual_factors': 0.15
        }
    
    def extract_success_factors(self, model_name: str, prediction_history: List[PredictionRecord]) -> SuccessFactorResult:
        """
        提取成功预测的关键因子
        
        Args:
            model_name: 模型名称
            prediction_history: 预测历史记录
            
        Returns:
            成功因子提取结果
        """
        try:
            self.logger.info(f"开始提取成功因子: {model_name}")
            
            # 筛选成功的预测
            successful_predictions = self._filter_successful_predictions(prediction_history)
            
            if not successful_predictions:
                self.logger.warning(f"模型 {model_name} 没有成功的预测记录")
                return SuccessFactorResult(
                    model_name=model_name,
                    top_factors=[],
                    detailed_analysis={'analysis': 'no_successful_predictions'},
                    amplification_suggestions=[],
                    replication_strategy={},
                    confidence_score=0.0,
                    analysis_timestamp=datetime.now()
                )
            
            # 执行各种分析方法
            detailed_analysis = {}
            for method_name, method_func in self.analysis_methods.items():
                try:
                    analysis_result = method_func(successful_predictions, prediction_history)
                    detailed_analysis[method_name] = analysis_result
                except Exception as e:
                    self.logger.error(f"分析方法 {method_name} 执行失败: {e}")
                    detailed_analysis[method_name] = {'error': str(e)}
            
            # 综合排名成功因子
            top_factors = self._rank_success_factors(detailed_analysis)
            
            # 生成放大建议
            amplification_suggestions = self._generate_amplification_suggestions(top_factors, detailed_analysis)
            
            # 生成复制策略
            replication_strategy = self._generate_replication_strategy(detailed_analysis, successful_predictions)
            
            # 计算置信度
            confidence_score = self._calculate_extraction_confidence(
                successful_predictions, prediction_history, detailed_analysis
            )
            
            return SuccessFactorResult(
                model_name=model_name,
                top_factors=top_factors,
                detailed_analysis=detailed_analysis,
                amplification_suggestions=amplification_suggestions,
                replication_strategy=replication_strategy,
                confidence_score=confidence_score,
                analysis_timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"成功因子提取失败: {e}")
            raise
    
    def _filter_successful_predictions(self, prediction_history: List[PredictionRecord]) -> List[PredictionRecord]:
        """筛选成功的预测"""
        # 定义成功的标准
        successful_predictions = []
        
        for prediction in prediction_history:
            if prediction.is_verified and prediction.accuracy_score is not None:
                # 完全正确的预测
                if prediction.accuracy_score >= 1.0:
                    successful_predictions.append(prediction)
                # 高准确率的预测（>0.8）
                elif prediction.accuracy_score >= 0.8:
                    successful_predictions.append(prediction)
                # 高置信度且相对准确的预测
                elif prediction.confidence >= 0.7 and prediction.accuracy_score >= 0.6:
                    successful_predictions.append(prediction)
        
        return successful_predictions
    
    def _analyze_feature_importance(self, successful_predictions: List[PredictionRecord], 
                                   all_predictions: List[PredictionRecord]) -> Dict[str, Any]:
        """分析特征重要性"""
        analysis = {
            'metadata_features': {},
            'prediction_characteristics': {},
            'feature_correlations': {}
        }
        
        # 分析元数据特征
        metadata_features = defaultdict(list)
        for prediction in successful_predictions:
            if prediction.metadata:
                for key, value in prediction.metadata.items():
                    metadata_features[key].append(value)
        
        # 计算特征出现频率和重要性
        feature_importance = {}
        for feature_name, values in metadata_features.items():
            if len(values) > 0:
                # 计算该特征在成功预测中的分布
                if isinstance(values[0], (int, float)):
                    feature_importance[feature_name] = {
                        'type': 'numerical',
                        'mean': np.mean(values),
                        'std': np.std(values),
                        'importance_score': len(values) / len(successful_predictions)
                    }
                else:
                    value_counts = Counter(values)
                    feature_importance[feature_name] = {
                        'type': 'categorical',
                        'most_common': value_counts.most_common(3),
                        'importance_score': len(set(values)) / len(values)
                    }
        
        analysis['metadata_features'] = feature_importance
        
        # 分析预测特征
        confidence_levels = [p.confidence for p in successful_predictions]
        prediction_times = [p.prediction_time.hour if p.prediction_time else 12 
                           for p in successful_predictions]
        
        analysis['prediction_characteristics'] = {
            'confidence_distribution': {
                'mean': np.mean(confidence_levels),
                'std': np.std(confidence_levels),
                'min': np.min(confidence_levels),
                'max': np.max(confidence_levels)
            },
            'timing_patterns': {
                'hour_distribution': Counter(prediction_times),
                'preferred_hours': [h for h, count in Counter(prediction_times).most_common(3)]
            }
        }
        
        return analysis
    
    def _analyze_temporal_patterns(self, successful_predictions: List[PredictionRecord], 
                                  all_predictions: List[PredictionRecord]) -> Dict[str, Any]:
        """分析时间模式"""
        analysis = {
            'success_timing': {},
            'seasonal_patterns': {},
            'streak_analysis': {}
        }
        
        if not successful_predictions:
            return analysis
        
        # 按时间排序
        sorted_successful = sorted(successful_predictions, 
                                 key=lambda x: x.prediction_time or datetime.min)
        
        # 分析成功预测的时间分布
        success_hours = [p.prediction_time.hour if p.prediction_time else 12 
                        for p in sorted_successful]
        success_weekdays = [p.prediction_time.weekday() if p.prediction_time else 0 
                           for p in sorted_successful]
        
        analysis['success_timing'] = {
            'hour_distribution': dict(Counter(success_hours)),
            'weekday_distribution': dict(Counter(success_weekdays)),
            'peak_hours': [h for h, _ in Counter(success_hours).most_common(2)],
            'peak_weekdays': [w for w, _ in Counter(success_weekdays).most_common(2)]
        }
        
        # 分析连续成功模式
        if len(sorted_successful) >= 2:
            time_intervals = []
            for i in range(1, len(sorted_successful)):
                curr_time = sorted_successful[i].prediction_time
                prev_time = sorted_successful[i-1].prediction_time
                if curr_time and prev_time:
                    interval = (curr_time - prev_time).total_seconds() / 3600
                    time_intervals.append(interval)
            
            if time_intervals:
                analysis['streak_analysis'] = {
                    'avg_success_interval': np.mean(time_intervals),
                    'min_interval': np.min(time_intervals),
                    'max_interval': np.max(time_intervals),
                    'interval_consistency': 1.0 - (np.std(time_intervals) / np.mean(time_intervals))
                }
        
        return analysis
    
    def _analyze_confidence_patterns(self, successful_predictions: List[PredictionRecord], 
                                    all_predictions: List[PredictionRecord]) -> Dict[str, Any]:
        """分析置信度模式"""
        analysis = {
            'confidence_distribution': {},
            'confidence_accuracy_relationship': {},
            'optimal_confidence_range': {}
        }
        
        # 成功预测的置信度分析
        success_confidences = [p.confidence for p in successful_predictions]
        all_confidences = [p.confidence for p in all_predictions]
        
        analysis['confidence_distribution'] = {
            'success_mean': np.mean(success_confidences),
            'success_std': np.std(success_confidences),
            'overall_mean': np.mean(all_confidences),
            'overall_std': np.std(all_confidences),
            'confidence_advantage': np.mean(success_confidences) - np.mean(all_confidences)
        }
        
        # 寻找最优置信度范围
        confidence_ranges = [(0.0, 0.3), (0.3, 0.5), (0.5, 0.7), (0.7, 0.9), (0.9, 1.0)]
        range_performance = {}
        
        for low, high in confidence_ranges:
            range_predictions = [p for p in all_predictions 
                               if low <= p.confidence < high and p.is_verified]
            if range_predictions:
                success_rate = sum(1 for p in range_predictions if p.accuracy_score and p.accuracy_score >= 0.8) / len(range_predictions)
                range_performance[f"{low}-{high}"] = {
                    'success_rate': success_rate,
                    'prediction_count': len(range_predictions)
                }
        
        # 找到最佳置信度范围
        if range_performance:
            best_range = max(range_performance.items(), key=lambda x: x[1]['success_rate'])
            analysis['optimal_confidence_range'] = {
                'range': best_range[0],
                'success_rate': best_range[1]['success_rate'],
                'recommendation': f"保持置信度在 {best_range[0]} 范围内"
            }
        
        return analysis
    
    def _analyze_numerical_patterns(self, successful_predictions: List[PredictionRecord], 
                                   all_predictions: List[PredictionRecord]) -> Dict[str, Any]:
        """分析数值模式"""
        analysis = {
            'number_characteristics': {},
            'digit_preferences': {},
            'pattern_preferences': {}
        }
        
        # 分析成功预测的数字特征
        successful_numbers = [p.predicted_numbers for p in successful_predictions]
        
        # 数字特征统计
        sum_values = [sum(int(d) for d in numbers) for numbers in successful_numbers]
        span_values = [max(int(d) for d in numbers) - min(int(d) for d in numbers) 
                      for numbers in successful_numbers]
        
        analysis['number_characteristics'] = {
            'sum_distribution': {
                'mean': np.mean(sum_values),
                'std': np.std(sum_values),
                'preferred_range': (np.percentile(sum_values, 25), np.percentile(sum_values, 75))
            },
            'span_distribution': {
                'mean': np.mean(span_values),
                'std': np.std(span_values),
                'preferred_range': (np.percentile(span_values, 25), np.percentile(span_values, 75))
            }
        }
        
        # 数字偏好分析
        all_digits = []
        for numbers in successful_numbers:
            all_digits.extend([int(d) for d in numbers])
        
        digit_frequency = Counter(all_digits)
        analysis['digit_preferences'] = {
            'most_frequent': digit_frequency.most_common(5),
            'least_frequent': digit_frequency.most_common()[-3:],
            'distribution_evenness': len(set(all_digits)) / len(all_digits)
        }
        
        # 模式偏好分析
        patterns = {
            'consecutive': 0,
            'repeated': 0,
            'ascending': 0,
            'descending': 0
        }
        
        for numbers in successful_numbers:
            digits = [int(d) for d in numbers]
            
            # 连续数字
            if any(abs(digits[i+1] - digits[i]) == 1 for i in range(len(digits)-1)):
                patterns['consecutive'] += 1
            
            # 重复数字
            if len(set(digits)) < len(digits):
                patterns['repeated'] += 1
            
            # 递增
            if all(digits[i] <= digits[i+1] for i in range(len(digits)-1)):
                patterns['ascending'] += 1
            
            # 递减
            if all(digits[i] >= digits[i+1] for i in range(len(digits)-1)):
                patterns['descending'] += 1
        
        total_successful = len(successful_numbers)
        analysis['pattern_preferences'] = {
            pattern: count / total_successful 
            for pattern, count in patterns.items()
        }
        
        return analysis
    
    def _analyze_contextual_factors(self, successful_predictions: List[PredictionRecord], 
                                   all_predictions: List[PredictionRecord]) -> Dict[str, Any]:
        """分析上下文因子"""
        analysis = {
            'period_patterns': {},
            'sequence_context': {},
            'external_factors': {}
        }
        
        # 期号模式分析
        successful_periods = [p.period_number for p in successful_predictions if p.period_number]
        
        if successful_periods:
            # 分析期号的数字特征
            period_endings = [period[-2:] for period in successful_periods if len(period) >= 2]
            analysis['period_patterns'] = {
                'ending_distribution': dict(Counter(period_endings)),
                'preferred_endings': [ending for ending, _ in Counter(period_endings).most_common(3)]
            }
        
        return analysis
    
    def _rank_success_factors(self, detailed_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """综合排名成功因子"""
        factors = []
        
        # 从各个分析维度提取因子
        for analysis_type, analysis_result in detailed_analysis.items():
            if 'error' in analysis_result:
                continue
            
            weight = self.factor_weights.get(analysis_type, 0.1)
            
            if analysis_type == 'feature_importance':
                for feature_name, feature_data in analysis_result.get('metadata_features', {}).items():
                    factors.append({
                        'factor_name': f"metadata_{feature_name}",
                        'factor_type': 'feature',
                        'importance_score': feature_data.get('importance_score', 0) * weight,
                        'description': f"元数据特征: {feature_name}",
                        'details': feature_data
                    })
            
            elif analysis_type == 'confidence_patterns':
                confidence_advantage = analysis_result.get('confidence_distribution', {}).get('confidence_advantage', 0)
                if confidence_advantage > 0.05:
                    factors.append({
                        'factor_name': 'high_confidence',
                        'factor_type': 'confidence',
                        'importance_score': min(confidence_advantage * 2, 1.0) * weight,
                        'description': '高置信度预测',
                        'details': analysis_result['confidence_distribution']
                    })
            
            elif analysis_type == 'temporal_patterns':
                if 'peak_hours' in analysis_result.get('success_timing', {}):
                    factors.append({
                        'factor_name': 'optimal_timing',
                        'factor_type': 'temporal',
                        'importance_score': 0.7 * weight,
                        'description': '最佳预测时间',
                        'details': analysis_result['success_timing']
                    })
            
            elif analysis_type == 'numerical_patterns':
                for pattern, frequency in analysis_result.get('pattern_preferences', {}).items():
                    if frequency > 0.3:  # 频率超过30%
                        factors.append({
                            'factor_name': f"{pattern}_pattern",
                            'factor_type': 'numerical',
                            'importance_score': frequency * weight,
                            'description': f'{pattern}数字模式偏好',
                            'details': {'frequency': frequency}
                        })
        
        # 按重要性排序
        factors.sort(key=lambda x: x['importance_score'], reverse=True)
        
        return factors[:10]  # 返回前10个因子
    
    def _generate_amplification_suggestions(self, top_factors: List[Dict[str, Any]], 
                                          detailed_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成放大建议"""
        suggestions = []
        
        for factor in top_factors[:5]:  # 针对前5个因子
            factor_type = factor['factor_type']
            factor_name = factor['factor_name']
            
            if factor_type == 'confidence':
                suggestions.append({
                    'factor': factor_name,
                    'suggestion': '增强置信度计算机制',
                    'actions': [
                        '优化置信度校准算法',
                        '增加置信度相关特征权重',
                        '实施置信度阈值筛选'
                    ],
                    'expected_impact': 'high'
                })
            
            elif factor_type == 'temporal':
                suggestions.append({
                    'factor': factor_name,
                    'suggestion': '优化预测时机选择',
                    'actions': [
                        '在最佳时间窗口进行预测',
                        '建立时间相关的特征工程',
                        '实施时间敏感的模型调度'
                    ],
                    'expected_impact': 'medium'
                })
            
            elif factor_type == 'numerical':
                suggestions.append({
                    'factor': factor_name,
                    'suggestion': '强化数字模式识别',
                    'actions': [
                        '增加模式识别特征',
                        '调整模式相关参数权重',
                        '实施模式导向的预测策略'
                    ],
                    'expected_impact': 'medium'
                })
            
            elif factor_type == 'feature':
                suggestions.append({
                    'factor': factor_name,
                    'suggestion': '放大关键特征影响',
                    'actions': [
                        '增加该特征的权重',
                        '创建该特征的衍生特征',
                        '优化该特征的预处理方法'
                    ],
                    'expected_impact': 'high'
                })
        
        return suggestions
    
    def _generate_replication_strategy(self, detailed_analysis: Dict[str, Any], 
                                     successful_predictions: List[PredictionRecord]) -> Dict[str, Any]:
        """生成复制策略"""
        strategy = {
            'key_conditions': [],
            'replication_steps': [],
            'monitoring_metrics': [],
            'success_indicators': []
        }
        
        # 基于分析结果生成关键条件
        if 'confidence_patterns' in detailed_analysis:
            optimal_range = detailed_analysis['confidence_patterns'].get('optimal_confidence_range', {})
            if optimal_range:
                strategy['key_conditions'].append({
                    'condition': 'confidence_range',
                    'value': optimal_range.get('range', '0.7-0.9'),
                    'description': '保持置信度在最优范围内'
                })
        
        if 'temporal_patterns' in detailed_analysis:
            peak_hours = detailed_analysis['temporal_patterns'].get('success_timing', {}).get('peak_hours', [])
            if peak_hours:
                strategy['key_conditions'].append({
                    'condition': 'optimal_timing',
                    'value': peak_hours,
                    'description': '在最佳时间进行预测'
                })
        
        # 生成复制步骤
        strategy['replication_steps'] = [
            '识别符合成功条件的预测机会',
            '应用成功因子权重配置',
            '执行预测并监控关键指标',
            '根据反馈调整策略参数'
        ]
        
        # 监控指标
        strategy['monitoring_metrics'] = [
            'prediction_accuracy',
            'confidence_calibration',
            'success_factor_coverage',
            'replication_consistency'
        ]
        
        # 成功指标
        accuracy_scores = [p.accuracy_score for p in successful_predictions if p.accuracy_score is not None]
        avg_success_accuracy = np.mean(accuracy_scores) if accuracy_scores else 0.0
        strategy['success_indicators'] = [
            f'准确率达到 {avg_success_accuracy:.2f} 以上',
            '置信度校准误差小于 0.1',
            '成功因子覆盖率大于 80%'
        ]
        
        return strategy
    
    def _calculate_extraction_confidence(self, successful_predictions: List[PredictionRecord], 
                                       all_predictions: List[PredictionRecord], 
                                       detailed_analysis: Dict[str, Any]) -> float:
        """计算提取置信度"""
        # 基于成功样本数量
        success_count = len(successful_predictions)
        total_count = len(all_predictions)
        
        sample_confidence = min(success_count / 10, 1.0)  # 10个成功样本达到满分
        success_rate_confidence = success_count / total_count if total_count > 0 else 0.0
        
        # 基于分析完整性
        analysis_completeness = sum(1 for result in detailed_analysis.values() 
                                  if 'error' not in result) / len(detailed_analysis)
        
        # 综合置信度
        overall_confidence = (
            sample_confidence * 0.4 + 
            success_rate_confidence * 0.3 + 
            analysis_completeness * 0.3
        )
        
        return max(0.0, min(1.0, overall_confidence))


if __name__ == "__main__":
    # 测试代码
    print("成功因子提取器测试完成")
