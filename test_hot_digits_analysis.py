#!/usr/bin/env python3
"""
测试热号分析逻辑
"""

import sys
sys.path.append('src')
from prediction.trend_analysis import TrendAnalyzer

def test_hot_digits():
    """测试热号分析"""
    print("🔥 测试热号分析逻辑")
    print("=" * 50)
    
    analyzer = TrendAnalyzer()
    records = analyzer.load_recent_data(limit=100)
    
    if not records:
        print("❌ 无法加载数据")
        return
    
    print(f"✅ 加载了 {len(records)} 条记录")
    
    # 分析数字频率趋势
    digit_trends = analyzer.analyze_digit_frequency_trends(records)
    
    print("\n📊 数字频率趋势分析:")
    hot_digits_found = []
    for digit in range(10):
        trend = digit_trends.get(digit, {})
        heat_level = trend.get('heat_level', 'unknown')
        recent_freq = trend.get('recent_frequency', 0)
        trend_direction = trend.get('trend_direction', 'unknown')
        
        print(f"数字 {digit}: 热度={heat_level}, 频率={recent_freq:.3f}, 趋势={trend_direction}")
        
        if heat_level == 'hot':
            hot_digits_found.append(digit)
    
    print(f"\n🔥 发现的热号: {hot_digits_found}")
    
    # 测试趋势预测
    print("\n🎯 测试趋势预测...")
    trend_predictions = analyzer.predict_next_trends(records)
    hot_digits = trend_predictions.get('hot_digits', [])
    
    print(f"\n📋 热号推荐 (前5个):")
    for i, hot in enumerate(hot_digits[:5]):
        digit = hot.get('digit', 'N/A')
        confidence = hot.get('confidence', 0)
        reason = hot.get('reason', 'N/A')
        print(f"  {i+1}. 数字{digit}: 置信度={confidence:.3f}, 原因={reason}")
    
    # 检查预测号码生成
    if len(hot_digits) >= 3:
        top3 = [str(h['digit']) for h in hot_digits[:3]]
        predicted_number = ''.join(top3)
        print(f"\n🎯 前3个热号组成的预测: {predicted_number}")
        
        # 检查是否总是056
        if predicted_number == "056":
            print("⚠️ 发现问题：预测结果固定为056！")
            print("🔍 分析原因：")
            print("  - 可能是热号分析逻辑有问题")
            print("  - 可能是数据中0、5、6确实频率最高")
            print("  - 可能是算法缺乏真正的随机性")
        else:
            print("✅ 预测结果不是固定的056")
    
    # 多次测试看是否有变化
    print("\n🔄 多次测试预测变化性...")
    predictions = []
    for i in range(5):
        trend_pred = analyzer.predict_next_trends(records)
        hot_list = trend_pred.get('hot_digits', [])
        if len(hot_list) >= 3:
            top3 = [str(h['digit']) for h in hot_list[:3]]
            pred_num = ''.join(top3)
            predictions.append(pred_num)
            print(f"  测试{i+1}: {pred_num}")
    
    unique_predictions = set(predictions)
    print(f"\n📊 变化性分析:")
    print(f"  总测试次数: {len(predictions)}")
    print(f"  不同结果数: {len(unique_predictions)}")
    print(f"  所有结果: {list(unique_predictions)}")
    
    if len(unique_predictions) == 1:
        print("❌ 预测结果完全固定，存在算法问题！")
    else:
        print("✅ 预测结果有变化性")

if __name__ == "__main__":
    test_hot_digits()
