"""
福彩3D数据清洗器

专门负责数据清洗和格式化处理
"""

import re
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, date
import logging

from .models import LotteryRecord, DataValidator

logger = logging.getLogger(__name__)


class DataCleaner:
    """数据清洗器"""
    
    def __init__(self):
        """初始化清洗器"""
        self.encoding_candidates = ['utf-8', 'gbk', 'gb2312', 'utf-16']
        self.cleaned_count = 0
        self.error_count = 0
        
    def detect_encoding(self, raw_bytes: bytes) -> str:
        """
        自动检测数据编码
        
        Args:
            raw_bytes: 原始字节数据
            
        Returns:
            检测到的编码格式
        """
        for encoding in self.encoding_candidates:
            try:
                raw_bytes.decode(encoding)
                logger.info(f"检测到编码格式: {encoding}")
                return encoding
            except UnicodeDecodeError:
                continue
        
        logger.warning("无法检测编码格式，使用utf-8并忽略错误")
        return 'utf-8'
    
    def normalize_line_endings(self, text: str) -> str:
        """
        标准化行结束符
        
        Args:
            text: 原始文本
            
        Returns:
            标准化后的文本
        """
        # 统一使用\n作为行结束符
        text = text.replace('\r\n', '\n')
        text = text.replace('\r', '\n')
        return text
    
    def remove_bom(self, text: str) -> str:
        """
        移除BOM标记
        
        Args:
            text: 原始文本
            
        Returns:
            移除BOM后的文本
        """
        # 移除UTF-8 BOM
        if text.startswith('\ufeff'):
            text = text[1:]
        return text
    
    def clean_line(self, line: str) -> Optional[str]:
        """
        清洗单行数据
        
        Args:
            line: 原始数据行
            
        Returns:
            清洗后的数据行，无效时返回None
        """
        if not line:
            return None
        
        # 移除首尾空白
        line = line.strip()
        
        # 跳过空行
        if not line:
            return None
        
        # 跳过注释行
        if line.startswith('#') or line.startswith('//') or line.startswith(';'):
            return None
        
        # 移除行内注释
        comment_patterns = [r'#.*$', r'//.*$', r';.*$']
        for pattern in comment_patterns:
            line = re.sub(pattern, '', line)
            line = line.strip()
        
        # 标准化空白字符
        line = re.sub(r'\s+', ' ', line)
        
        # 移除特殊字符
        line = re.sub(r'[^\w\s\-:]', ' ', line)
        line = re.sub(r'\s+', ' ', line).strip()
        
        return line if line else None
    
    def clean_raw_data(self, raw_data: str) -> List[str]:
        """
        清洗原始数据
        
        Args:
            raw_data: 原始数据字符串
            
        Returns:
            清洗后的数据行列表
        """
        if not raw_data:
            return []
        
        # 标准化文本
        text = self.normalize_line_endings(raw_data)
        text = self.remove_bom(text)
        
        # 分割并清洗每一行
        lines = []
        raw_lines = text.split('\n')
        
        for i, line in enumerate(raw_lines, 1):
            cleaned_line = self.clean_line(line)
            if cleaned_line:
                lines.append(cleaned_line)
                self.cleaned_count += 1
            elif line.strip():  # 非空行但清洗失败
                self.error_count += 1
                logger.debug(f"第{i}行清洗失败: {line[:50]}...")
        
        logger.info(f"数据清洗完成，有效行数: {len(lines)}, 清洗行数: {self.cleaned_count}, 错误行数: {self.error_count}")
        return lines
    
    def standardize_date_format(self, date_str: str) -> Optional[str]:
        """
        标准化日期格式
        
        Args:
            date_str: 原始日期字符串
            
        Returns:
            标准化的日期字符串 (YYYY-MM-DD)，失败时返回None
        """
        # 常见日期格式
        date_patterns = [
            r'(\d{4})-(\d{1,2})-(\d{1,2})',  # YYYY-MM-DD 或 YYYY-M-D
            r'(\d{4})/(\d{1,2})/(\d{1,2})',  # YYYY/MM/DD 或 YYYY/M/D
            r'(\d{4})\.(\d{1,2})\.(\d{1,2})', # YYYY.MM.DD 或 YYYY.M.D
            r'(\d{4})(\d{2})(\d{2})',        # YYYYMMDD
        ]
        
        for pattern in date_patterns:
            match = re.match(pattern, date_str.strip())
            if match:
                year, month, day = match.groups()
                try:
                    # 验证日期有效性
                    date_obj = datetime(int(year), int(month), int(day))
                    return date_obj.strftime('%Y-%m-%d')
                except ValueError:
                    continue
        
        return None
    
    def standardize_period_format(self, period_str: str) -> Optional[str]:
        """
        标准化期号格式
        
        Args:
            period_str: 原始期号字符串
            
        Returns:
            标准化的期号字符串 (7位数字)，失败时返回None
        """
        # 移除非数字字符
        period_digits = re.sub(r'\D', '', period_str)
        
        if len(period_digits) == 7 and period_digits.isdigit():
            return period_digits
        elif len(period_digits) == 6 and period_digits.isdigit():
            # 6位期号，可能缺少年份前缀
            year_prefix = datetime.now().year % 100  # 当前年份后两位
            return f"{year_prefix:02d}{period_digits}"
        
        return None
    
    def standardize_numbers_format(self, numbers_str: str) -> Optional[str]:
        """
        标准化号码格式
        
        Args:
            numbers_str: 原始号码字符串
            
        Returns:
            标准化的号码字符串 (3位数字)，失败时返回None
        """
        # 移除非数字字符
        numbers_digits = re.sub(r'\D', '', numbers_str)
        
        if len(numbers_digits) == 3 and numbers_digits.isdigit():
            # 验证每个数字都在0-9范围内
            for digit in numbers_digits:
                if not (0 <= int(digit) <= 9):
                    return None
            return numbers_digits
        
        return None
    
    def format_record_data(self, raw_record: Dict[str, str]) -> Optional[Dict[str, str]]:
        """
        格式化记录数据
        
        Args:
            raw_record: 原始记录数据
            
        Returns:
            格式化后的记录数据，失败时返回None
        """
        try:
            # 标准化各个字段
            period = self.standardize_period_format(raw_record.get('period', ''))
            date_str = self.standardize_date_format(raw_record.get('date', ''))
            numbers = self.standardize_numbers_format(raw_record.get('numbers', ''))
            
            if period and date_str and numbers:
                return {
                    'period': period,
                    'date': date_str,
                    'numbers': numbers
                }
            
        except Exception as e:
            logger.debug(f"格式化记录失败: {raw_record} - {e}")
        
        return None
    
    def validate_and_clean_records(self, records: List[LotteryRecord]) -> List[LotteryRecord]:
        """
        验证并清洗记录列表
        
        Args:
            records: 原始记录列表
            
        Returns:
            清洗后的记录列表
        """
        cleaned_records = []
        
        for record in records:
            try:
                # 验证记录有效性
                if (DataValidator.validate_period(record.period) and
                    DataValidator.validate_numbers(record.numbers)):
                    
                    cleaned_records.append(record)
                else:
                    logger.debug(f"记录验证失败: {record}")
                    
            except Exception as e:
                logger.debug(f"记录处理失败: {record} - {e}")
        
        return cleaned_records
    
    def remove_duplicates(self, records: List[LotteryRecord]) -> List[LotteryRecord]:
        """
        移除重复记录
        
        Args:
            records: 记录列表
            
        Returns:
            去重后的记录列表
        """
        seen_periods = set()
        unique_records = []
        duplicate_count = 0
        
        for record in records:
            if record.period not in seen_periods:
                seen_periods.add(record.period)
                unique_records.append(record)
            else:
                duplicate_count += 1
                logger.debug(f"发现重复记录: {record.period}")
        
        if duplicate_count > 0:
            logger.info(f"移除了 {duplicate_count} 条重复记录")
        
        return unique_records
    
    def sort_records(self, records: List[LotteryRecord]) -> List[LotteryRecord]:
        """
        排序记录
        
        Args:
            records: 记录列表
            
        Returns:
            排序后的记录列表
        """
        return sorted(records, key=lambda x: (x.date, x.period))
    
    def get_cleaning_statistics(self) -> Dict[str, Any]:
        """
        获取清洗统计信息
        
        Returns:
            清洗统计信息
        """
        return {
            "cleaned_lines": self.cleaned_count,
            "error_lines": self.error_count,
            "success_rate": (
                self.cleaned_count / (self.cleaned_count + self.error_count) * 100
                if (self.cleaned_count + self.error_count) > 0 else 0
            )
        }


# 便捷函数
def clean_lottery_data(raw_data: str) -> Tuple[List[str], Dict[str, Any]]:
    """
    便捷的数据清洗函数
    
    Args:
        raw_data: 原始数据字符串
        
    Returns:
        (清洗后的数据行列表, 清洗统计信息)
    """
    cleaner = DataCleaner()
    cleaned_lines = cleaner.clean_raw_data(raw_data)
    stats = cleaner.get_cleaning_statistics()
    
    return cleaned_lines, stats


if __name__ == "__main__":
    # 测试代码
    test_data = """
    # 福彩3D历史数据测试
    2024001 2024-01-01 1 2 3 4 5 6 7 8 9
    2024002	2024/01/02	4 5 6 7 8 9 0 1 2  # 这是注释
    invalid line without proper format
    2024003,2024.01.03,7,8,9,0,1,2,3,4,5
    
    2024004 20240104 0 1 2 3 4 5 6 7 8
    """
    
    print("测试数据清洗器...")
    cleaner = DataCleaner()
    
    # 清洗数据
    cleaned_lines = cleaner.clean_raw_data(test_data)
    
    print(f"\n清洗结果:")
    print(f"有效行数: {len(cleaned_lines)}")
    
    for i, line in enumerate(cleaned_lines, 1):
        print(f"  {i}. {line}")
    
    # 统计信息
    stats = cleaner.get_cleaning_statistics()
    print(f"\n清洗统计:")
    print(f"  清洗行数: {stats['cleaned_lines']}")
    print(f"  错误行数: {stats['error_lines']}")
    print(f"  成功率: {stats['success_rate']:.1f}%")
