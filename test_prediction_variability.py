#!/usr/bin/env python3
"""
预测变化性验证工具
自动化测试预测结果的合理变化性，确保相同数据在不同时间产生合理变化
"""

import sys
sys.path.append('src')

import time
import json
from datetime import datetime
from typing import Dict, List, Any, Tuple
from prediction.intelligent_fusion import IntelligentFusionSystem
from prediction.prediction_validator import PredictionValidator


class PredictionVariabilityTester:
    """预测变化性测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.validator = PredictionValidator()
        self.test_results = []
    
    def test_temporal_variability(self, test_duration_minutes: int = 5, 
                                test_interval_seconds: int = 30) -> Dict[str, Any]:
        """
        测试时间变化性
        
        Args:
            test_duration_minutes: 测试持续时间（分钟）
            test_interval_seconds: 测试间隔（秒）
            
        Returns:
            时间变化性测试结果
        """
        print(f"🕒 开始时间变化性测试，持续{test_duration_minutes}分钟，间隔{test_interval_seconds}秒")
        
        predictions = []
        start_time = time.time()
        end_time = start_time + (test_duration_minutes * 60)
        test_count = 0
        
        system = IntelligentFusionSystem()
        
        while time.time() < end_time:
            test_count += 1
            current_time = datetime.now()
            
            print(f"  执行第{test_count}次预测... ({current_time.strftime('%H:%M:%S')})")
            
            try:
                # 进行预测
                prediction = system.generate_fusion_prediction(
                    [], max_candidates=10, confidence_threshold=0.01
                )
                
                if 'error' not in prediction:
                    prediction['test_timestamp'] = current_time.isoformat()
                    prediction['test_sequence'] = test_count
                    predictions.append(prediction)
                    
                    print(f"    预测结果: {prediction.get('numbers', 'N/A')} "
                          f"(置信度: {prediction.get('confidence', 0):.3f})")
                else:
                    print(f"    预测失败: {prediction['error']}")
                
            except Exception as e:
                print(f"    预测异常: {e}")
            
            # 等待下次测试
            if time.time() < end_time:
                time.sleep(test_interval_seconds)
        
        # 分析结果
        print(f"\n📊 分析{len(predictions)}个预测结果...")
        
        variability_report = self.validator.validate_prediction_variability(
            predictions, "temporal_variability_test"
        )
        
        # 添加时间分析
        time_analysis = self._analyze_temporal_patterns(predictions)
        variability_report['temporal_analysis'] = time_analysis
        
        return variability_report
    
    def test_repeated_predictions(self, repeat_count: int = 10, 
                                delay_seconds: float = 1.0) -> Dict[str, Any]:
        """
        测试重复预测的变化性
        
        Args:
            repeat_count: 重复次数
            delay_seconds: 每次预测间的延迟
            
        Returns:
            重复预测测试结果
        """
        print(f"🔄 开始重复预测测试，重复{repeat_count}次，间隔{delay_seconds}秒")
        
        predictions = []
        system = IntelligentFusionSystem()
        
        for i in range(repeat_count):
            print(f"  执行第{i+1}/{repeat_count}次预测...")
            
            try:
                prediction = system.generate_fusion_prediction(
                    [], max_candidates=10, confidence_threshold=0.01
                )
                
                if 'error' not in prediction:
                    prediction['test_timestamp'] = datetime.now().isoformat()
                    prediction['test_sequence'] = i + 1
                    predictions.append(prediction)
                    
                    print(f"    预测结果: {prediction.get('numbers', 'N/A')} "
                          f"(置信度: {prediction.get('confidence', 0):.3f})")
                
            except Exception as e:
                print(f"    预测异常: {e}")
            
            if i < repeat_count - 1:
                time.sleep(delay_seconds)
        
        # 分析结果
        print(f"\n📊 分析{len(predictions)}个重复预测结果...")
        
        variability_report = self.validator.validate_prediction_variability(
            predictions, "repeated_predictions_test"
        )
        
        return variability_report
    
    def test_system_restart_consistency(self, restart_count: int = 3) -> Dict[str, Any]:
        """
        测试系统重启后的一致性
        
        Args:
            restart_count: 重启测试次数
            
        Returns:
            重启一致性测试结果
        """
        print(f"🔄 开始系统重启一致性测试，重启{restart_count}次")
        
        predictions = []
        
        for i in range(restart_count):
            print(f"  第{i+1}/{restart_count}次系统初始化...")
            
            # 创建新的系统实例（模拟重启）
            system = IntelligentFusionSystem()
            
            try:
                prediction = system.generate_fusion_prediction(
                    [], max_candidates=10, confidence_threshold=0.01
                )
                
                if 'error' not in prediction:
                    prediction['test_timestamp'] = datetime.now().isoformat()
                    prediction['restart_sequence'] = i + 1
                    predictions.append(prediction)
                    
                    print(f"    预测结果: {prediction.get('numbers', 'N/A')} "
                          f"(置信度: {prediction.get('confidence', 0):.3f})")
                
            except Exception as e:
                print(f"    预测异常: {e}")
            
            # 短暂延迟
            time.sleep(2)
        
        # 分析结果
        print(f"\n📊 分析{len(predictions)}个重启预测结果...")
        
        variability_report = self.validator.validate_prediction_variability(
            predictions, "system_restart_test"
        )
        
        return variability_report
    
    def run_comprehensive_variability_test(self) -> Dict[str, Any]:
        """
        运行综合变化性测试
        
        Returns:
            综合测试结果
        """
        print("🚀 开始综合预测变化性测试")
        print("=" * 60)
        
        comprehensive_results = {
            'test_start_time': datetime.now().isoformat(),
            'tests': {},
            'summary': {}
        }
        
        # 1. 时间变化性测试
        print("\n1️⃣ 时间变化性测试")
        print("-" * 30)
        temporal_result = self.test_temporal_variability(
            test_duration_minutes=2, test_interval_seconds=15
        )
        comprehensive_results['tests']['temporal_variability'] = temporal_result
        
        # 2. 重复预测测试
        print("\n2️⃣ 重复预测测试")
        print("-" * 30)
        repeated_result = self.test_repeated_predictions(
            repeat_count=8, delay_seconds=2
        )
        comprehensive_results['tests']['repeated_predictions'] = repeated_result
        
        # 3. 系统重启一致性测试
        print("\n3️⃣ 系统重启一致性测试")
        print("-" * 30)
        restart_result = self.test_system_restart_consistency(restart_count=5)
        comprehensive_results['tests']['system_restart'] = restart_result
        
        # 生成综合评估
        print("\n📋 生成综合评估报告...")
        summary = self._generate_comprehensive_summary(comprehensive_results['tests'])
        comprehensive_results['summary'] = summary
        
        # 保存结果
        result_file = f"variability_test_results_{int(time.time())}.json"
        try:
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(comprehensive_results, f, ensure_ascii=False, indent=2)
            print(f"✅ 测试结果已保存到: {result_file}")
        except Exception as e:
            print(f"❌ 保存测试结果失败: {e}")
        
        return comprehensive_results
    
    def _analyze_temporal_patterns(self, predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析时间模式"""
        if len(predictions) < 2:
            return {'error': '预测数量不足，无法分析时间模式'}
        
        # 分析时间间隔内的变化
        time_groups = {}
        for pred in predictions:
            timestamp = pred.get('test_timestamp')
            if timestamp:
                # 按分钟分组
                minute = timestamp[:16]  # YYYY-MM-DDTHH:MM
                if minute not in time_groups:
                    time_groups[minute] = []
                time_groups[minute].append(pred)
        
        # 分析每个时间组内的变化
        group_analysis = {}
        for minute, group_preds in time_groups.items():
            if len(group_preds) > 1:
                numbers = [p.get('numbers') for p in group_preds]
                unique_numbers = set(numbers)
                group_analysis[minute] = {
                    'prediction_count': len(group_preds),
                    'unique_numbers': len(unique_numbers),
                    'variability_ratio': len(unique_numbers) / len(group_preds)
                }
        
        return {
            'time_groups_count': len(time_groups),
            'group_analysis': group_analysis,
            'overall_temporal_variability': len(group_analysis) > 0
        }
    
    def _generate_comprehensive_summary(self, test_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成综合评估摘要"""
        summary = {
            'overall_score': 0,
            'quality_level': 'unknown',
            'key_findings': [],
            'recommendations': [],
            'test_scores': {}
        }
        
        scores = []
        
        # 分析各项测试结果
        for test_name, result in test_results.items():
            if 'quality_score' in result:
                test_score = result['quality_score']
                scores.append(test_score)
                summary['test_scores'][test_name] = test_score
                
                # 提取关键发现
                if result.get('fixed_pattern_detected'):
                    summary['key_findings'].append(f"{test_name}: 检测到固定预测模式")
                
                if result.get('number_variability_ratio', 0) < 0.2:
                    summary['key_findings'].append(f"{test_name}: 预测变化性不足")
                
                # 提取建议
                if 'recommendations' in result:
                    summary['recommendations'].extend(result['recommendations'])
        
        # 计算综合评分
        if scores:
            summary['overall_score'] = sum(scores) / len(scores)
            
            # 确定质量级别
            if summary['overall_score'] >= 80:
                summary['quality_level'] = 'excellent'
            elif summary['overall_score'] >= 60:
                summary['quality_level'] = 'good'
            elif summary['overall_score'] >= 40:
                summary['quality_level'] = 'fair'
            else:
                summary['quality_level'] = 'poor'
        
        # 去重建议
        summary['recommendations'] = list(set(summary['recommendations']))
        
        return summary
    
    def print_summary_report(self, comprehensive_results: Dict[str, Any]):
        """打印摘要报告"""
        print("\n" + "=" * 60)
        print("📊 综合变化性测试报告")
        print("=" * 60)
        
        summary = comprehensive_results.get('summary', {})
        
        print(f"🎯 综合评分: {summary.get('overall_score', 0):.1f}/100")
        print(f"📈 质量级别: {summary.get('quality_level', 'unknown').upper()}")
        
        # 各项测试评分
        test_scores = summary.get('test_scores', {})
        if test_scores:
            print(f"\n📋 各项测试评分:")
            for test_name, score in test_scores.items():
                print(f"  • {test_name}: {score:.1f}/100")
        
        # 关键发现
        key_findings = summary.get('key_findings', [])
        if key_findings:
            print(f"\n🔍 关键发现:")
            for finding in key_findings:
                print(f"  • {finding}")
        
        # 建议
        recommendations = summary.get('recommendations', [])
        if recommendations:
            print(f"\n💡 改进建议:")
            for rec in recommendations:
                print(f"  • {rec}")
        
        print("\n" + "=" * 60)


def main():
    """主函数"""
    print("🧪 预测变化性验证工具")
    print("=" * 60)
    
    tester = PredictionVariabilityTester()
    
    try:
        # 运行综合测试
        results = tester.run_comprehensive_variability_test()
        
        # 打印摘要报告
        tester.print_summary_report(results)
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")


if __name__ == "__main__":
    main()
