"""
模型工具类
提供模型保存、加载、评估等工具函数
"""

import torch
import torch.nn as nn
import numpy as np
import os
import json
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
import matplotlib.pyplot as plt


class ModelUtils:
    """模型工具类"""
    
    @staticmethod
    def save_model(model: nn.Module, filepath: str, config: Dict = None, metrics: Dict = None):
        """
        保存模型
        
        Args:
            model: 要保存的模型
            filepath: 保存路径
            config: 模型配置
            metrics: 训练指标
        """
        # 创建保存目录
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        # 保存内容
        save_dict = {
            'model_state_dict': model.state_dict(),
            'model_class': model.__class__.__name__,
            'timestamp': datetime.now().isoformat(),
        }
        
        if config:
            save_dict['config'] = config
        if metrics:
            save_dict['metrics'] = metrics
            
        torch.save(save_dict, filepath)
        print(f"模型已保存到: {filepath}")
    
    @staticmethod
    def load_model(model: nn.Module, filepath: str) -> Tuple[nn.Module, Dict]:
        """
        加载模型
        
        Args:
            model: 模型实例
            filepath: 模型文件路径
            
        Returns:
            加载的模型和元数据
        """
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"模型文件不存在: {filepath}")
        
        checkpoint = torch.load(filepath, map_location='cpu')
        model.load_state_dict(checkpoint['model_state_dict'])
        
        metadata = {
            'timestamp': checkpoint.get('timestamp'),
            'config': checkpoint.get('config'),
            'metrics': checkpoint.get('metrics')
        }
        
        print(f"模型已从 {filepath} 加载")
        return model, metadata
    
    @staticmethod
    def count_parameters(model: nn.Module) -> Dict[str, int]:
        """统计模型参数"""
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'non_trainable_parameters': total_params - trainable_params
        }
    
    @staticmethod
    def calculate_model_size(model: nn.Module) -> Dict[str, float]:
        """计算模型大小"""
        param_size = 0
        buffer_size = 0
        
        for param in model.parameters():
            param_size += param.nelement() * param.element_size()
        
        for buffer in model.buffers():
            buffer_size += buffer.nelement() * buffer.element_size()
        
        size_mb = (param_size + buffer_size) / 1024 / 1024
        
        return {
            'parameter_size_mb': param_size / 1024 / 1024,
            'buffer_size_mb': buffer_size / 1024 / 1024,
            'total_size_mb': size_mb
        }
    
    @staticmethod
    def evaluate_model(model: nn.Module, data_loader, device: str = 'cpu') -> Dict[str, float]:
        """
        评估模型性能
        
        Args:
            model: 要评估的模型
            data_loader: 数据加载器
            device: 设备
            
        Returns:
            评估指标
        """
        model.eval()
        model.to(device)
        
        total_loss = 0.0
        correct_predictions = 0
        total_samples = 0
        top5_correct = 0
        top10_correct = 0
        
        criterion = nn.CrossEntropyLoss()
        
        with torch.no_grad():
            for batch_idx, (data, targets) in enumerate(data_loader):
                data, targets = data.to(device), targets.to(device)
                
                # 前向传播
                outputs = model(data)
                loss = criterion(outputs, targets)
                
                # 统计
                total_loss += loss.item()
                total_samples += targets.size(0)
                
                # Top-1准确率
                _, predicted = torch.max(outputs.data, 1)
                correct_predictions += (predicted == targets).sum().item()
                
                # Top-5准确率
                _, top5_pred = torch.topk(outputs, 5, dim=1)
                top5_correct += sum([targets[i] in top5_pred[i] for i in range(targets.size(0))])
                
                # Top-10准确率
                _, top10_pred = torch.topk(outputs, 10, dim=1)
                top10_correct += sum([targets[i] in top10_pred[i] for i in range(targets.size(0))])
        
        metrics = {
            'average_loss': total_loss / len(data_loader),
            'accuracy': correct_predictions / total_samples,
            'top5_accuracy': top5_correct / total_samples,
            'top10_accuracy': top10_correct / total_samples,
            'total_samples': total_samples
        }
        
        return metrics
    
    @staticmethod
    def plot_training_history(train_losses: List[float], val_losses: List[float], 
                            train_accs: List[float], val_accs: List[float],
                            save_path: str = None):
        """
        绘制训练历史
        
        Args:
            train_losses: 训练损失
            val_losses: 验证损失
            train_accs: 训练准确率
            val_accs: 验证准确率
            save_path: 保存路径
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
        
        # 损失曲线
        ax1.plot(train_losses, label='Training Loss', color='blue')
        ax1.plot(val_losses, label='Validation Loss', color='red')
        ax1.set_title('Training and Validation Loss')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True)
        
        # 准确率曲线
        ax2.plot(train_accs, label='Training Accuracy', color='blue')
        ax2.plot(val_accs, label='Validation Accuracy', color='red')
        ax2.set_title('Training and Validation Accuracy')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Accuracy')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"训练历史图已保存到: {save_path}")
        
        plt.show()
    
    @staticmethod
    def analyze_predictions(model: nn.Module, data_loader, device: str = 'cpu', 
                          num_samples: int = 100) -> Dict[str, Any]:
        """
        分析模型预测结果
        
        Args:
            model: 模型
            data_loader: 数据加载器
            device: 设备
            num_samples: 分析样本数
            
        Returns:
            预测分析结果
        """
        model.eval()
        model.to(device)
        
        predictions = []
        true_labels = []
        confidences = []
        
        with torch.no_grad():
            sample_count = 0
            for data, targets in data_loader:
                if sample_count >= num_samples:
                    break
                    
                data, targets = data.to(device), targets.to(device)
                outputs = model(data)
                probs = torch.softmax(outputs, dim=1)
                
                # 获取预测和置信度
                max_probs, predicted = torch.max(probs, 1)
                
                predictions.extend(predicted.cpu().numpy())
                true_labels.extend(targets.cpu().numpy())
                confidences.extend(max_probs.cpu().numpy())
                
                sample_count += data.size(0)
        
        # 计算分析指标
        predictions = np.array(predictions)
        true_labels = np.array(true_labels)
        confidences = np.array(confidences)
        
        correct_mask = predictions == true_labels
        
        analysis = {
            'total_samples': len(predictions),
            'correct_predictions': np.sum(correct_mask),
            'accuracy': np.mean(correct_mask),
            'average_confidence': np.mean(confidences),
            'correct_confidence': np.mean(confidences[correct_mask]),
            'incorrect_confidence': np.mean(confidences[~correct_mask]) if np.sum(~correct_mask) > 0 else 0,
            'confidence_std': np.std(confidences),
            'prediction_distribution': np.bincount(predictions, minlength=1000)[:1000].tolist()
        }
        
        return analysis
    
    @staticmethod
    def get_model_summary(model: nn.Module) -> str:
        """获取模型摘要"""
        param_info = ModelUtils.count_parameters(model)
        size_info = ModelUtils.calculate_model_size(model)
        
        summary = f"""
模型摘要:
========
模型类型: {model.__class__.__name__}
总参数数量: {param_info['total_parameters']:,}
可训练参数: {param_info['trainable_parameters']:,}
不可训练参数: {param_info['non_trainable_parameters']:,}
模型大小: {size_info['total_size_mb']:.2f} MB
参数大小: {size_info['parameter_size_mb']:.2f} MB
缓冲区大小: {size_info['buffer_size_mb']:.2f} MB
"""
        return summary


class EarlyStopping:
    """早停机制"""
    
    def __init__(self, patience: int = 10, min_delta: float = 0.001, 
                 restore_best_weights: bool = True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        
        self.best_loss = float('inf')
        self.counter = 0
        self.best_weights = None
        
    def __call__(self, val_loss: float, model: nn.Module) -> bool:
        """
        检查是否应该早停
        
        Args:
            val_loss: 验证损失
            model: 模型
            
        Returns:
            是否应该停止训练
        """
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0
            if self.restore_best_weights:
                self.best_weights = model.state_dict().copy()
        else:
            self.counter += 1
        
        if self.counter >= self.patience:
            if self.restore_best_weights and self.best_weights:
                model.load_state_dict(self.best_weights)
            return True
        
        return False


def test_model_utils():
    """测试模型工具"""
    print("=== 测试模型工具 ===")
    
    # 创建一个简单的测试模型
    class SimpleModel(nn.Module):
        def __init__(self):
            super().__init__()
            self.linear = nn.Linear(10, 5)
        
        def forward(self, x):
            return self.linear(x)
    
    model = SimpleModel()
    
    # 测试参数统计
    param_info = ModelUtils.count_parameters(model)
    print(f"参数信息: {param_info}")
    
    # 测试模型大小
    size_info = ModelUtils.calculate_model_size(model)
    print(f"大小信息: {size_info}")
    
    # 测试模型摘要
    summary = ModelUtils.get_model_summary(model)
    print(summary)
    
    # 测试早停机制
    early_stopping = EarlyStopping(patience=3)
    
    # 模拟训练过程
    val_losses = [1.0, 0.8, 0.9, 0.85, 0.87, 0.88]
    for epoch, val_loss in enumerate(val_losses):
        should_stop = early_stopping(val_loss, model)
        print(f"Epoch {epoch}: val_loss={val_loss}, should_stop={should_stop}")
        if should_stop:
            break
    
    print("✓ 模型工具测试成功!")


if __name__ == "__main__":
    test_model_utils()
