"""
元学习优化引擎
实现MetaLearner、TaskFeatureExtractor、跨任务知识迁移
"""

import numpy as np
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import logging

try:
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False


@dataclass
class Task:
    """任务定义"""
    task_id: str
    name: str
    description: str
    task_type: str  # "classification", "regression", "time_series"
    
    # 任务特征
    data_size: int
    feature_count: int
    target_type: str
    complexity_score: float
    
    # 历史性能
    best_accuracy: float = 0.0
    best_hyperparameters: Dict[str, Any] = field(default_factory=dict)
    training_history: List[Dict[str, Any]] = field(default_factory=list)
    
    # 元数据
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)


@dataclass
class MetaKnowledge:
    """元知识"""
    knowledge_id: str
    source_tasks: List[str]
    knowledge_type: str  # "hyperparameter_pattern", "architecture_pattern", "optimization_strategy"
    
    # 知识内容
    pattern: Dict[str, Any]
    applicability_conditions: Dict[str, Any]
    confidence: float
    
    # 验证信息
    validation_count: int = 0
    success_count: int = 0
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.validation_count == 0:
            return 0.0
        return self.success_count / self.validation_count


class TaskFeatureExtractor:
    """任务特征提取器"""
    
    def __init__(self):
        self.feature_extractors = {
            "data_characteristics": self._extract_data_features,
            "statistical_properties": self._extract_statistical_features,
            "complexity_metrics": self._extract_complexity_features,
            "domain_features": self._extract_domain_features
        }
    
    def extract_task_features(self, task: Task, data_sample: Optional[np.ndarray] = None) -> Dict[str, float]:
        """提取任务特征"""
        features = {}
        
        # 基础特征
        features.update({
            "data_size_log": np.log10(max(1, task.data_size)),
            "feature_count_log": np.log10(max(1, task.feature_count)),
            "complexity_score": task.complexity_score,
            "data_feature_ratio": task.data_size / max(1, task.feature_count)
        })
        
        # 任务类型编码
        task_type_encoding = {
            "classification": [1, 0, 0],
            "regression": [0, 1, 0],
            "time_series": [0, 0, 1]
        }
        
        encoding = task_type_encoding.get(task.task_type, [0, 0, 0])
        for i, val in enumerate(encoding):
            features[f"task_type_{i}"] = val
        
        # 如果有数据样本，提取更详细的特征
        if data_sample is not None:
            for extractor_name, extractor_func in self.feature_extractors.items():
                try:
                    extracted_features = extractor_func(data_sample)
                    features.update(extracted_features)
                except Exception as e:
                    logging.warning(f"特征提取失败 {extractor_name}: {e}")
        
        return features
    
    def _extract_data_features(self, data: np.ndarray) -> Dict[str, float]:
        """提取数据特征"""
        if len(data.shape) != 2:
            return {}
        
        n_samples, n_features = data.shape
        
        return {
            "data_density": np.count_nonzero(data) / data.size,
            "data_sparsity": 1 - (np.count_nonzero(data) / data.size),
            "mean_feature_correlation": np.mean(np.abs(np.corrcoef(data.T))),
            "feature_variance_ratio": np.var(np.var(data, axis=0)) / (np.mean(np.var(data, axis=0)) + 1e-8)
        }
    
    def _extract_statistical_features(self, data: np.ndarray) -> Dict[str, float]:
        """提取统计特征"""
        try:
            return {
                "data_mean": np.mean(data),
                "data_std": np.std(data),
                "data_skewness": self._calculate_skewness(data.flatten()),
                "data_kurtosis": self._calculate_kurtosis(data.flatten()),
                "data_range": np.max(data) - np.min(data)
            }
        except:
            return {}
    
    def _extract_complexity_features(self, data: np.ndarray) -> Dict[str, float]:
        """提取复杂度特征"""
        try:
            # 简化的复杂度度量
            return {
                "intrinsic_dimensionality": min(data.shape),
                "effective_rank": np.linalg.matrix_rank(data) if len(data.shape) == 2 else 1,
                "condition_number": np.linalg.cond(data) if len(data.shape) == 2 else 1
            }
        except:
            return {"intrinsic_dimensionality": 1, "effective_rank": 1, "condition_number": 1}
    
    def _extract_domain_features(self, data: np.ndarray) -> Dict[str, float]:
        """提取领域特征"""
        # 福彩3D特定的领域特征
        return {
            "lottery_specific_pattern": self._detect_lottery_patterns(data),
            "sequence_regularity": self._measure_sequence_regularity(data),
            "number_distribution_entropy": self._calculate_distribution_entropy(data)
        }
    
    def _detect_lottery_patterns(self, data: np.ndarray) -> float:
        """检测福彩3D特定模式"""
        try:
            # 简化的模式检测
            if len(data.shape) == 2 and data.shape[1] >= 3:
                # 检查连号、重号等模式
                patterns = 0
                for row in data:
                    if len(row) >= 3:
                        # 连号检测
                        if abs(row[1] - row[0]) == 1 and abs(row[2] - row[1]) == 1:
                            patterns += 1
                        # 重号检测
                        if len(set(row[:3])) < 3:
                            patterns += 1
                
                return patterns / len(data) if len(data) > 0 else 0
            return 0
        except:
            return 0
    
    def _measure_sequence_regularity(self, data: np.ndarray) -> float:
        """测量序列规律性"""
        try:
            flat_data = data.flatten()
            if len(flat_data) < 2:
                return 0
            
            # 计算相邻元素差值的标准差
            diffs = np.diff(flat_data)
            return 1 / (1 + np.std(diffs))
        except:
            return 0
    
    def _calculate_distribution_entropy(self, data: np.ndarray) -> float:
        """计算分布熵"""
        try:
            flat_data = data.flatten()
            unique, counts = np.unique(flat_data, return_counts=True)
            probabilities = counts / len(flat_data)
            entropy = -np.sum(probabilities * np.log2(probabilities + 1e-8))
            return entropy
        except:
            return 0
    
    def _calculate_skewness(self, data: np.ndarray) -> float:
        """计算偏度"""
        try:
            mean = np.mean(data)
            std = np.std(data)
            if std == 0:
                return 0
            return np.mean(((data - mean) / std) ** 3)
        except:
            return 0
    
    def _calculate_kurtosis(self, data: np.ndarray) -> float:
        """计算峰度"""
        try:
            mean = np.mean(data)
            std = np.std(data)
            if std == 0:
                return 0
            return np.mean(((data - mean) / std) ** 4) - 3
        except:
            return 0


class MetaLearner:
    """元学习器"""
    
    def __init__(self):
        self.task_feature_extractor = TaskFeatureExtractor()
        self.tasks: Dict[str, Task] = {}
        self.meta_knowledge: Dict[str, MetaKnowledge] = {}
        
        # 元模型
        self.hyperparameter_predictor = None
        self.performance_predictor = None
        self.scaler = StandardScaler() if SKLEARN_AVAILABLE else None
        
        self.logger = logging.getLogger("MetaLearner")
    
    def add_task(self, task: Task):
        """添加任务"""
        self.tasks[task.task_id] = task
        self.logger.info(f"任务已添加: {task.task_id}")
    
    def update_task_performance(self, task_id: str, hyperparameters: Dict[str, Any], 
                              performance: float):
        """更新任务性能"""
        if task_id not in self.tasks:
            return
        
        task = self.tasks[task_id]
        
        # 更新历史记录
        task.training_history.append({
            "timestamp": datetime.now().isoformat(),
            "hyperparameters": hyperparameters,
            "performance": performance
        })
        
        # 更新最佳性能
        if performance > task.best_accuracy:
            task.best_accuracy = performance
            task.best_hyperparameters = hyperparameters.copy()
        
        task.updated_at = datetime.now()
        
        # 触发元知识更新
        self._update_meta_knowledge()
    
    def recommend_hyperparameters(self, target_task: Task, 
                                data_sample: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """为目标任务推荐超参数"""
        
        # 提取目标任务特征
        target_features = self.task_feature_extractor.extract_task_features(target_task, data_sample)
        
        # 寻找相似任务
        similar_tasks = self._find_similar_tasks(target_task, target_features)
        
        if not similar_tasks:
            return self._get_default_hyperparameters(target_task.task_type)
        
        # 基于相似任务推荐
        recommendations = self._generate_recommendations_from_similar_tasks(similar_tasks)
        
        # 如果有元模型，使用元模型预测
        if self.hyperparameter_predictor and SKLEARN_AVAILABLE:
            meta_recommendations = self._predict_with_meta_model(target_features)
            recommendations = self._combine_recommendations(recommendations, meta_recommendations)
        
        return recommendations
    
    def _find_similar_tasks(self, target_task: Task, target_features: Dict[str, float]) -> List[Tuple[str, float]]:
        """寻找相似任务"""
        similarities = []
        
        target_feature_vector = np.array(list(target_features.values())).reshape(1, -1)
        
        for task_id, task in self.tasks.items():
            if task_id == target_task.task_id:
                continue
            
            # 计算任务相似度
            task_features = self.task_feature_extractor.extract_task_features(task)
            
            # 确保特征维度一致
            common_features = set(target_features.keys()) & set(task_features.keys())
            if not common_features:
                continue
            
            target_vec = np.array([target_features[f] for f in common_features]).reshape(1, -1)
            task_vec = np.array([task_features[f] for f in common_features]).reshape(1, -1)
            
            if SKLEARN_AVAILABLE:
                similarity = cosine_similarity(target_vec, task_vec)[0, 0]
            else:
                # 简化的相似度计算
                similarity = 1 / (1 + np.linalg.norm(target_vec - task_vec))
            
            similarities.append((task_id, similarity))
        
        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        return similarities[:5]  # 返回前5个最相似的任务
    
    def _generate_recommendations_from_similar_tasks(self, similar_tasks: List[Tuple[str, float]]) -> Dict[str, Any]:
        """基于相似任务生成推荐"""
        if not similar_tasks:
            return {}
        
        # 收集相似任务的最佳超参数
        hyperparams_list = []
        weights = []
        
        for task_id, similarity in similar_tasks:
            task = self.tasks[task_id]
            if task.best_hyperparameters:
                hyperparams_list.append(task.best_hyperparameters)
                weights.append(similarity)
        
        if not hyperparams_list:
            return {}
        
        # 加权平均超参数
        recommendations = {}
        all_param_names = set()
        for params in hyperparams_list:
            all_param_names.update(params.keys())
        
        total_weight = sum(weights)
        
        for param_name in all_param_names:
            weighted_sum = 0
            weight_sum = 0
            
            for i, params in enumerate(hyperparams_list):
                if param_name in params:
                    value = params[param_name]
                    if isinstance(value, (int, float)):
                        weighted_sum += value * weights[i]
                        weight_sum += weights[i]
                    elif isinstance(value, str):
                        # 对于字符串参数，选择最相似任务的值
                        if i == 0:  # 最相似的任务
                            recommendations[param_name] = value
            
            if weight_sum > 0 and param_name not in recommendations:
                recommendations[param_name] = weighted_sum / weight_sum
        
        return recommendations
    
    def _predict_with_meta_model(self, target_features: Dict[str, float]) -> Dict[str, Any]:
        """使用元模型预测"""
        if not self.hyperparameter_predictor:
            return {}
        
        try:
            feature_vector = np.array(list(target_features.values())).reshape(1, -1)
            
            if self.scaler:
                feature_vector = self.scaler.transform(feature_vector)
            
            # 这里应该预测具体的超参数值
            # 简化实现，返回默认值
            return {
                "learning_rate": 0.001,
                "batch_size": 64,
                "epochs": 100
            }
        except Exception as e:
            self.logger.error(f"元模型预测失败: {e}")
            return {}
    
    def _combine_recommendations(self, task_based: Dict[str, Any], 
                               meta_based: Dict[str, Any]) -> Dict[str, Any]:
        """组合推荐结果"""
        combined = task_based.copy()
        
        # 简单的组合策略：优先使用任务相似度推荐，元模型作为补充
        for param, value in meta_based.items():
            if param not in combined:
                combined[param] = value
        
        return combined
    
    def _get_default_hyperparameters(self, task_type: str) -> Dict[str, Any]:
        """获取默认超参数"""
        defaults = {
            "classification": {
                "learning_rate": 0.001,
                "batch_size": 64,
                "epochs": 100,
                "optimizer": "adam"
            },
            "regression": {
                "learning_rate": 0.001,
                "batch_size": 32,
                "epochs": 150,
                "optimizer": "adam"
            },
            "time_series": {
                "learning_rate": 0.0005,
                "batch_size": 32,
                "epochs": 200,
                "optimizer": "adam"
            }
        }
        
        return defaults.get(task_type, defaults["classification"])
    
    def _update_meta_knowledge(self):
        """更新元知识"""
        # 分析任务间的模式
        self._extract_hyperparameter_patterns()
        self._extract_performance_patterns()
    
    def _extract_hyperparameter_patterns(self):
        """提取超参数模式"""
        # 简化实现：寻找高性能超参数的共同模式
        high_performance_configs = []
        
        for task in self.tasks.values():
            if task.best_accuracy > 0.8:  # 高性能阈值
                high_performance_configs.append({
                    "task_type": task.task_type,
                    "hyperparameters": task.best_hyperparameters,
                    "performance": task.best_accuracy
                })
        
        # 这里可以实现更复杂的模式挖掘算法
        # 简化实现：记录高性能配置
        if high_performance_configs:
            knowledge = MetaKnowledge(
                knowledge_id=f"hp_pattern_{int(datetime.now().timestamp())}",
                source_tasks=[task.task_id for task in self.tasks.values()],
                knowledge_type="hyperparameter_pattern",
                pattern={"high_performance_configs": high_performance_configs},
                applicability_conditions={"min_performance": 0.8},
                confidence=0.8
            )
            
            self.meta_knowledge[knowledge.knowledge_id] = knowledge
    
    def _extract_performance_patterns(self):
        """提取性能模式"""
        # 分析不同任务特征与性能的关系
        pass
    
    def get_meta_learning_insights(self) -> Dict[str, Any]:
        """获取元学习洞察"""
        insights = {
            "total_tasks": len(self.tasks),
            "total_meta_knowledge": len(self.meta_knowledge),
            "average_task_performance": np.mean([task.best_accuracy for task in self.tasks.values()]),
            "task_type_distribution": {},
            "knowledge_type_distribution": {}
        }
        
        # 任务类型分布
        for task in self.tasks.values():
            task_type = task.task_type
            if task_type not in insights["task_type_distribution"]:
                insights["task_type_distribution"][task_type] = 0
            insights["task_type_distribution"][task_type] += 1
        
        # 知识类型分布
        for knowledge in self.meta_knowledge.values():
            knowledge_type = knowledge.knowledge_type
            if knowledge_type not in insights["knowledge_type_distribution"]:
                insights["knowledge_type_distribution"][knowledge_type] = 0
            insights["knowledge_type_distribution"][knowledge_type] += 1
        
        return insights


def test_meta_learning():
    """测试元学习系统"""
    print("🧪 测试元学习优化引擎...")
    
    # 创建元学习器
    meta_learner = MetaLearner()
    
    # 创建测试任务
    task1 = Task(
        task_id="task_1",
        name="福彩3D预测任务1",
        description="基础预测任务",
        task_type="time_series",
        data_size=5000,
        feature_count=10,
        target_type="categorical",
        complexity_score=0.7
    )
    
    task2 = Task(
        task_id="task_2", 
        name="福彩3D预测任务2",
        description="高级预测任务",
        task_type="time_series",
        data_size=8000,
        feature_count=15,
        target_type="categorical",
        complexity_score=0.8
    )
    
    # 添加任务
    meta_learner.add_task(task1)
    meta_learner.add_task(task2)
    
    # 模拟训练历史
    meta_learner.update_task_performance("task_1", {"learning_rate": 0.001, "batch_size": 64}, 0.85)
    meta_learner.update_task_performance("task_1", {"learning_rate": 0.005, "batch_size": 32}, 0.82)
    meta_learner.update_task_performance("task_2", {"learning_rate": 0.002, "batch_size": 64}, 0.87)
    
    # 创建新任务并获取推荐
    new_task = Task(
        task_id="task_3",
        name="新预测任务",
        description="需要推荐的新任务",
        task_type="time_series",
        data_size=6000,
        feature_count=12,
        target_type="categorical",
        complexity_score=0.75
    )
    
    recommendations = meta_learner.recommend_hyperparameters(new_task)
    print(f"📊 超参数推荐: {recommendations}")
    
    # 获取元学习洞察
    insights = meta_learner.get_meta_learning_insights()
    print(f"🔍 元学习洞察: {insights}")
    
    print("✅ 元学习优化引擎测试完成！")


if __name__ == "__main__":
    test_meta_learning()
