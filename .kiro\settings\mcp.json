{"mcpServers": {"Context 7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "Playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest"]}, "Sequential thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "mcp-deepwiki": {"command": "npx", "args": ["-y", "mcp-deep<PERSON><PERSON>@latest"]}, "knowledge-graph": {"command": "npx", "args": ["-y", "mcp-knowledge-graph", "--memory-path", "./knowledge-graph/memory.jsonl"]}}}