-- 福彩3D预测系统核心预测逻辑优化项目
-- 数据库结构扩展脚本
-- 创建预测跟踪相关的数据表

-- ============================================================================
-- 1. 模型预测历史表 (model_predictions)
-- 记录每个模型的历史预测结果，用于准确率统计和性能跟踪
-- ============================================================================

CREATE TABLE IF NOT EXISTS model_predictions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    period_number TEXT NOT NULL,                -- 期号
    model_name TEXT NOT NULL,                   -- 模型名称
    predicted_number TEXT NOT NULL,             -- 预测号码
    confidence REAL NOT NULL,                   -- 预测置信度
    actual_number TEXT,                         -- 实际开奖号码
    is_hit BOOLEAN,                            -- 是否命中
    prediction_date DATETIME DEFAULT CURRENT_TIMESTAMP,  -- 预测时间
    
    -- 唯一约束：每个期号每个模型只能有一条预测记录
    UNIQUE(period_number, model_name)
);

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_model_predictions_period ON model_predictions(period_number);
CREATE INDEX IF NOT EXISTS idx_model_predictions_model ON model_predictions(model_name);
CREATE INDEX IF NOT EXISTS idx_model_predictions_date ON model_predictions(prediction_date);
CREATE INDEX IF NOT EXISTS idx_model_predictions_hit ON model_predictions(is_hit);

-- ============================================================================
-- 2. 模型准确率统计表 (model_accuracy)
-- 存储各模型在不同窗口大小下的准确率统计信息
-- ============================================================================

CREATE TABLE IF NOT EXISTS model_accuracy (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    model_name TEXT NOT NULL,                   -- 模型名称
    window_size INTEGER NOT NULL,               -- 统计窗口大小
    accuracy_rate REAL NOT NULL,                -- 准确率
    total_predictions INTEGER NOT NULL,         -- 总预测次数
    correct_predictions INTEGER NOT NULL,       -- 正确预测次数
    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,  -- 最后更新时间
    
    -- 唯一约束：每个模型每个窗口大小只能有一条统计记录
    UNIQUE(model_name, window_size)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_model_accuracy_model ON model_accuracy(model_name);
CREATE INDEX IF NOT EXISTS idx_model_accuracy_window ON model_accuracy(window_size);
CREATE INDEX IF NOT EXISTS idx_model_accuracy_rate ON model_accuracy(accuracy_rate);

-- ============================================================================
-- 3. 预测排行榜表 (prediction_rankings)
-- 存储每期的候选号码排行榜信息
-- ============================================================================

CREATE TABLE IF NOT EXISTS prediction_rankings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    period_number TEXT NOT NULL,                -- 期号
    number TEXT NOT NULL,                       -- 候选号码
    rank INTEGER NOT NULL,                      -- 排名
    confidence REAL NOT NULL,                   -- 预测置信度
    composite_score REAL NOT NULL,              -- 综合评分
    model_support_count INTEGER NOT NULL,       -- 支持模型数量
    historical_hit_rate REAL NOT NULL,          -- 历史命中率
    recommendation_level TEXT NOT NULL,         -- 推荐等级
    prediction_method TEXT NOT NULL,            -- 预测方法
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,  -- 创建时间
    
    -- 复合索引：期号+排名
    UNIQUE(period_number, rank)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_prediction_rankings_period ON prediction_rankings(period_number);
CREATE INDEX IF NOT EXISTS idx_prediction_rankings_number ON prediction_rankings(number);
CREATE INDEX IF NOT EXISTS idx_prediction_rankings_rank ON prediction_rankings(rank);
CREATE INDEX IF NOT EXISTS idx_prediction_rankings_confidence ON prediction_rankings(confidence);
CREATE INDEX IF NOT EXISTS idx_prediction_rankings_level ON prediction_rankings(recommendation_level);

-- ============================================================================
-- 4. 融合预测结果表 (fusion_predictions)
-- 存储融合算法的最终预测结果
-- ============================================================================

CREATE TABLE IF NOT EXISTS fusion_predictions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    period_number TEXT NOT NULL,                -- 期号
    best_number TEXT NOT NULL,                  -- 最佳推荐号码
    confidence REAL NOT NULL,                   -- 预测置信度
    fusion_method TEXT NOT NULL,                -- 融合方法
    model_weights TEXT NOT NULL,                -- 模型权重 (JSON格式)
    fusion_details TEXT,                        -- 融合详情 (JSON格式)
    prediction_date DATETIME DEFAULT CURRENT_TIMESTAMP,  -- 预测时间
    actual_number TEXT,                         -- 实际开奖号码
    is_hit BOOLEAN,                            -- 是否命中
    
    -- 唯一约束：每个期号只能有一条融合预测记录
    UNIQUE(period_number)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_fusion_predictions_period ON fusion_predictions(period_number);
CREATE INDEX IF NOT EXISTS idx_fusion_predictions_method ON fusion_predictions(fusion_method);
CREATE INDEX IF NOT EXISTS idx_fusion_predictions_date ON fusion_predictions(prediction_date);
CREATE INDEX IF NOT EXISTS idx_fusion_predictions_hit ON fusion_predictions(is_hit);

-- ============================================================================
-- 5. 预测性能监控表 (prediction_performance_log)
-- 记录预测系统的性能指标和运行状态
-- ============================================================================

CREATE TABLE IF NOT EXISTS prediction_performance_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    log_date DATETIME DEFAULT CURRENT_TIMESTAMP,  -- 日志时间
    metric_name TEXT NOT NULL,                   -- 指标名称
    metric_value REAL NOT NULL,                  -- 指标值
    metric_unit TEXT,                           -- 指标单位
    period_number TEXT,                         -- 相关期号
    model_name TEXT,                           -- 相关模型
    additional_info TEXT                        -- 附加信息 (JSON格式)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_performance_log_date ON prediction_performance_log(log_date);
CREATE INDEX IF NOT EXISTS idx_performance_log_metric ON prediction_performance_log(metric_name);
CREATE INDEX IF NOT EXISTS idx_performance_log_model ON prediction_performance_log(model_name);

-- ============================================================================
-- 6. 用户预测偏好表 (user_prediction_preferences)
-- 存储用户的个性化预测设置
-- ============================================================================

CREATE TABLE IF NOT EXISTS user_prediction_preferences (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL DEFAULT 'default',    -- 用户ID
    preference_name TEXT NOT NULL,              -- 偏好名称
    preference_value TEXT NOT NULL,             -- 偏好值
    preference_type TEXT NOT NULL,              -- 偏好类型 (string, number, boolean)
    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,  -- 最后更新时间
    
    -- 唯一约束：每个用户每个偏好只能有一条记录
    UNIQUE(user_id, preference_name)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_preferences_user ON user_prediction_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_user_preferences_name ON user_prediction_preferences(preference_name);

-- ============================================================================
-- 7. 数据视图创建
-- 创建便于查询的视图
-- ============================================================================

-- 模型性能汇总视图
CREATE VIEW IF NOT EXISTS v_model_performance_summary AS
SELECT 
    mp.model_name,
    COUNT(*) as total_predictions,
    SUM(CASE WHEN mp.is_hit = 1 THEN 1 ELSE 0 END) as correct_predictions,
    ROUND(AVG(CASE WHEN mp.is_hit = 1 THEN 1.0 ELSE 0.0 END), 4) as accuracy_rate,
    MAX(mp.prediction_date) as last_prediction_date,
    AVG(mp.confidence) as avg_confidence
FROM model_predictions mp
WHERE mp.actual_number IS NOT NULL
GROUP BY mp.model_name;

-- 最新排行榜视图
CREATE VIEW IF NOT EXISTS v_latest_ranking AS
SELECT 
    pr.*
FROM prediction_rankings pr
INNER JOIN (
    SELECT period_number, MAX(created_date) as max_date
    FROM prediction_rankings
    GROUP BY period_number
    ORDER BY period_number DESC
    LIMIT 1
) latest ON pr.period_number = latest.period_number 
    AND pr.created_date = latest.max_date
ORDER BY pr.rank;

-- 融合预测准确率视图
CREATE VIEW IF NOT EXISTS v_fusion_accuracy AS
SELECT 
    fusion_method,
    COUNT(*) as total_predictions,
    SUM(CASE WHEN is_hit = 1 THEN 1 ELSE 0 END) as correct_predictions,
    ROUND(AVG(CASE WHEN is_hit = 1 THEN 1.0 ELSE 0.0 END), 4) as accuracy_rate,
    AVG(confidence) as avg_confidence
FROM fusion_predictions
WHERE actual_number IS NOT NULL
GROUP BY fusion_method;

-- ============================================================================
-- 8. 初始化默认数据
-- 插入一些默认配置和初始数据
-- ============================================================================

-- 插入默认用户偏好设置
INSERT OR IGNORE INTO user_prediction_preferences (user_id, preference_name, preference_value, preference_type)
VALUES 
    ('default', 'ranking_count', '10', 'number'),
    ('default', 'confidence_threshold', '0.3', 'number'),
    ('default', 'window_size', '50', 'number'),
    ('default', 'enable_historical_bonus', 'true', 'boolean'),
    ('default', 'fusion_strategy', 'comprehensive', 'string'),
    ('default', 'show_technical_details', 'true', 'boolean');

-- ============================================================================
-- 9. 数据库维护触发器
-- 创建自动维护数据的触发器
-- ============================================================================

-- 自动更新模型准确率的触发器
CREATE TRIGGER IF NOT EXISTS tr_update_model_accuracy
AFTER UPDATE OF actual_number ON model_predictions
WHEN NEW.actual_number IS NOT NULL AND OLD.actual_number IS NULL
BEGIN
    -- 这里可以添加自动更新准确率统计的逻辑
    -- 实际实现会在应用层处理，这里只是占位符
    INSERT INTO prediction_performance_log (metric_name, metric_value, period_number, model_name)
    VALUES ('prediction_recorded', 1, NEW.period_number, NEW.model_name);
END;

-- 自动清理旧数据的触发器（可选）
-- CREATE TRIGGER IF NOT EXISTS tr_cleanup_old_data
-- AFTER INSERT ON model_predictions
-- BEGIN
--     DELETE FROM model_predictions 
--     WHERE prediction_date < datetime('now', '-1 year');
-- END;

-- ============================================================================
-- 10. 数据完整性检查
-- 添加数据完整性约束和检查
-- ============================================================================

-- 检查置信度范围
-- ALTER TABLE model_predictions ADD CONSTRAINT chk_confidence_range 
-- CHECK (confidence >= 0.0 AND confidence <= 1.0);

-- 检查准确率范围
-- ALTER TABLE model_accuracy ADD CONSTRAINT chk_accuracy_range 
-- CHECK (accuracy_rate >= 0.0 AND accuracy_rate <= 1.0);

-- 检查排名范围
-- ALTER TABLE prediction_rankings ADD CONSTRAINT chk_rank_positive 
-- CHECK (rank > 0);

-- ============================================================================
-- 脚本执行完成标记
-- ============================================================================

-- 记录脚本执行
INSERT OR REPLACE INTO prediction_performance_log (metric_name, metric_value, additional_info)
VALUES ('database_migration', 1, '{"script": "add_prediction_tracking.sql", "version": "1.0", "executed_at": "' || datetime('now') || '"}');

-- 输出执行结果
SELECT 'Database migration completed successfully. Prediction tracking tables created.' as result;
