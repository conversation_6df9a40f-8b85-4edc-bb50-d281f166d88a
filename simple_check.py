import sqlite3
import os

# 检查数据库文件
db_files = ["data/model_library.db", "data/lottery.db"]

for db_file in db_files:
    if os.path.exists(db_file):
        print(f"\n=== {db_file} ===")
        try:
            with sqlite3.connect(db_file) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = [row[0] for row in cursor.fetchall()]
                print(f"表: {tables}")
                
                for table in tables:
                    if 'prediction' in table.lower():
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        print(f"{table}: {count} 条记录")
                        
                        if count > 0:
                            cursor.execute(f"SELECT * FROM {table} LIMIT 3")
                            records = cursor.fetchall()
                            print(f"示例记录: {records}")
        except Exception as e:
            print(f"错误: {e}")
    else:
        print(f"{db_file} 不存在")

print("\n完成")
