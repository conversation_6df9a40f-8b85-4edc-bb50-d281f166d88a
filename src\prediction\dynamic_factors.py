#!/usr/bin/env python3
"""
动态因子和随机性增强模块
为所有预测算法提供基于当前时间和最新期号的动态因子
"""

import time
import random
import hashlib
from datetime import datetime
from typing import Dict, List, Any, Optional


class DynamicFactorManager:
    """动态因子管理器"""
    
    def __init__(self):
        """初始化动态因子管理器"""
        self.last_seed_time = 0
        self.seed_update_interval = 1  # 每秒更新一次种子
    
    def get_time_based_factor(self, scale: float = 1.0, offset: float = 0.0) -> float:
        """
        获取基于时间的动态因子
        
        Args:
            scale: 缩放因子
            offset: 偏移量
            
        Returns:
            时间动态因子
        """
        current_time = time.time()
        # 使用时间的小数部分作为动态因子
        time_factor = (current_time % 1) * scale + offset
        return time_factor
    
    def get_period_based_factor(self, latest_period: str, scale: float = 1.0) -> float:
        """
        获取基于期号的动态因子
        
        Args:
            latest_period: 最新期号
            scale: 缩放因子
            
        Returns:
            期号动态因子
        """
        try:
            # 提取期号数字
            period_num = int(latest_period) if latest_period.isdigit() else hash(latest_period)
            # 使用期号的某些位作为动态因子
            factor = ((period_num % 1000) / 1000.0) * scale
            return factor
        except:
            return 0.5 * scale
    
    def get_data_entropy_factor(self, recent_numbers: List[str]) -> float:
        """
        获取基于数据熵的动态因子
        
        Args:
            recent_numbers: 最近的开奖号码列表
            
        Returns:
            数据熵因子
        """
        if not recent_numbers:
            return 0.5
        
        # 计算数字多样性
        unique_numbers = set(recent_numbers)
        diversity = len(unique_numbers) / len(recent_numbers)
        
        # 计算数字分布熵
        digit_counts = {}
        for numbers in recent_numbers:
            for digit in numbers:
                digit_counts[digit] = digit_counts.get(digit, 0) + 1
        
        total_digits = sum(digit_counts.values())
        entropy = 0
        for count in digit_counts.values():
            if count > 0:
                p = count / total_digits
                entropy -= p * (p.bit_length() - 1) if p > 0 else 0
        
        # 归一化熵值
        max_entropy = 10 * (10.bit_length() - 1)  # 10个数字的最大熵
        normalized_entropy = entropy / max_entropy if max_entropy > 0 else 0.5
        
        return (diversity + normalized_entropy) / 2
    
    def update_random_seed(self, context: str = "default") -> None:
        """
        更新随机种子
        
        Args:
            context: 上下文标识
        """
        current_time = time.time()
        
        # 检查是否需要更新种子
        if current_time - self.last_seed_time >= self.seed_update_interval:
            # 生成复合种子
            time_component = int(current_time * 1000) % 1000000
            context_component = hash(context) % 1000000
            
            # 组合种子
            combined_seed = (time_component + context_component) % 1000000
            
            # 设置随机种子
            random.seed(combined_seed)
            
            self.last_seed_time = current_time
    
    def get_dynamic_confidence_multiplier(self, base_confidence: float, 
                                        context: Dict[str, Any] = None) -> float:
        """
        获取动态置信度乘数
        
        Args:
            base_confidence: 基础置信度
            context: 上下文信息
            
        Returns:
            动态置信度乘数
        """
        if context is None:
            context = {}
        
        # 时间因子
        time_factor = self.get_time_based_factor(0.2, 0.9)  # 0.9-1.1范围
        
        # 随机因子
        self.update_random_seed("confidence_calculation")
        random_factor = 0.95 + (random.random() * 0.1)  # 0.95-1.05范围
        
        # 数据因子
        recent_numbers = context.get('recent_numbers', [])
        data_factor = 0.9 + (self.get_data_entropy_factor(recent_numbers) * 0.2)  # 0.9-1.1范围
        
        # 组合乘数
        multiplier = time_factor * random_factor * data_factor
        
        # 确保乘数在合理范围内
        return max(0.8, min(1.2, multiplier))
    
    def get_dynamic_weight_adjustment(self, model_name: str, 
                                    base_weight: float,
                                    context: Dict[str, Any] = None) -> float:
        """
        获取动态权重调整
        
        Args:
            model_name: 模型名称
            base_weight: 基础权重
            context: 上下文信息
            
        Returns:
            调整后的权重
        """
        if context is None:
            context = {}
        
        # 模型特定的动态因子
        model_hash = hash(model_name) % 1000
        model_factor = (model_hash / 1000.0) * 0.1 + 0.95  # 0.95-1.05范围
        
        # 时间因子
        time_factor = self.get_time_based_factor(0.1, 0.95)  # 0.95-1.05范围
        
        # 随机因子
        self.update_random_seed(f"weight_{model_name}")
        random_factor = 0.98 + (random.random() * 0.04)  # 0.98-1.02范围
        
        # 组合调整
        adjustment = model_factor * time_factor * random_factor
        adjusted_weight = base_weight * adjustment
        
        # 确保权重在合理范围内
        return max(0.1, min(2.0, adjusted_weight))
    
    def generate_dynamic_candidates(self, base_candidates: List[str], 
                                  additional_count: int,
                                  context: Dict[str, Any] = None) -> List[str]:
        """
        生成动态候选号码
        
        Args:
            base_candidates: 基础候选号码
            additional_count: 需要生成的额外候选数量
            context: 上下文信息
            
        Returns:
            动态候选号码列表
        """
        if context is None:
            context = {}
        
        dynamic_candidates = []
        existing_numbers = set(base_candidates)
        
        # 更新随机种子
        self.update_random_seed("candidate_generation")
        
        # 获取最近数据的数字频率
        recent_numbers = context.get('recent_numbers', [])
        digit_freq = {}
        
        for numbers in recent_numbers:
            for digit in numbers:
                digit_freq[digit] = digit_freq.get(digit, 0) + 1
        
        # 生成候选号码
        attempts = 0
        max_attempts = additional_count * 10
        
        while len(dynamic_candidates) < additional_count and attempts < max_attempts:
            attempts += 1
            
            # 使用多种策略生成候选
            if attempts % 3 == 0:
                # 基于频率的生成
                candidate = self._generate_frequency_based_candidate(digit_freq)
            elif attempts % 3 == 1:
                # 基于时间的生成
                candidate = self._generate_time_based_candidate()
            else:
                # 完全随机生成
                candidate = self._generate_random_candidate()
            
            if candidate and candidate not in existing_numbers:
                dynamic_candidates.append(candidate)
                existing_numbers.add(candidate)
        
        return dynamic_candidates
    
    def _generate_frequency_based_candidate(self, digit_freq: Dict[str, int]) -> str:
        """基于频率生成候选号码"""
        if not digit_freq:
            return self._generate_random_candidate()
        
        # 选择频率较高的数字，但添加随机性
        sorted_digits = sorted(digit_freq.keys(), key=lambda x: digit_freq[x], reverse=True)
        
        candidate_digits = []
        for _ in range(3):
            # 70%概率选择高频数字，30%概率随机选择
            if random.random() < 0.7 and sorted_digits:
                # 从前6个高频数字中随机选择
                top_digits = sorted_digits[:min(6, len(sorted_digits))]
                digit = random.choice(top_digits)
            else:
                digit = str(random.randint(0, 9))
            
            candidate_digits.append(digit)
        
        return ''.join(candidate_digits)
    
    def _generate_time_based_candidate(self) -> str:
        """基于时间生成候选号码"""
        current_time = time.time()
        
        # 使用时间的不同部分生成数字
        time_str = str(int(current_time * 1000))
        
        candidate_digits = []
        for i in range(3):
            # 从时间字符串中提取数字
            digit_index = (i * 3 + len(time_str)) % len(time_str)
            digit = time_str[digit_index]
            candidate_digits.append(digit)
        
        return ''.join(candidate_digits)
    
    def _generate_random_candidate(self) -> str:
        """生成完全随机的候选号码"""
        return ''.join([str(random.randint(0, 9)) for _ in range(3)])


# 全局动态因子管理器实例
_global_factor_manager = None

def get_factor_manager() -> DynamicFactorManager:
    """获取全局动态因子管理器实例"""
    global _global_factor_manager
    if _global_factor_manager is None:
        _global_factor_manager = DynamicFactorManager()
    return _global_factor_manager


def apply_dynamic_factors(func):
    """
    应用动态因子的装饰器
    
    Args:
        func: 要装饰的函数
    """
    def wrapper(*args, **kwargs):
        # 获取动态因子管理器
        factor_manager = get_factor_manager()
        
        # 更新随机种子
        factor_manager.update_random_seed(func.__name__)
        
        # 执行原函数
        result = func(*args, **kwargs)
        
        return result
    
    return wrapper
