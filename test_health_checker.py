#!/usr/bin/env python3
"""
系统健康检查器测试
"""

import asyncio
import sys
import logging
from pathlib import Path

# 添加项目路径
sys.path.append('src')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_health_checker():
    """测试健康检查器"""
    print("🔍 测试系统健康检查器...")
    
    try:
        from monitoring.health_checker import SystemHealthChecker
        print("✅ 健康检查器导入成功")
    except ImportError as e:
        print(f"❌ 健康检查器导入失败: {e}")
        return False
    
    # 创建健康检查器
    try:
        health_checker = SystemHealthChecker()
        print("✅ 健康检查器创建成功")
    except Exception as e:
        print(f"❌ 健康检查器创建失败: {e}")
        return False
    
    # 测试基本功能
    try:
        # 测试系统资源检查
        print("\n📊 测试系统资源检查...")
        resource_result = await health_checker.check_system_resources()
        
        if resource_result.get("healthy"):
            print("✅ 系统资源检查通过")
            print(f"   检查项目数: {len(resource_result.get('checks', []))}")
            print(f"   响应时间: {resource_result.get('response_time_ms', 0)}ms")
        else:
            print("⚠️  系统资源检查发现问题")
            for check in resource_result.get('checks', []):
                if check.get('status') != 'ok':
                    print(f"   - {check.get('name')}: {check.get('status')}")
    
    except Exception as e:
        print(f"❌ 系统资源检查失败: {e}")
        return False
    
    # 测试数据库健康检查（无实际数据库管理器）
    try:
        print("\n🗄️  测试数据库健康检查...")
        db_result = await health_checker.check_database_health()
        
        if db_result.get("error"):
            print("ℹ️  数据库健康检查: 数据库管理器未设置（预期结果）")
        else:
            print("✅ 数据库健康检查完成")
    
    except Exception as e:
        print(f"❌ 数据库健康检查失败: {e}")
        return False
    
    # 测试数据源健康检查（无实际数据源管理器）
    try:
        print("\n📡 测试数据源健康检查...")
        ds_result = await health_checker.check_data_source_health()
        
        if ds_result.get("error"):
            print("ℹ️  数据源健康检查: 数据源管理器未设置（预期结果）")
        else:
            print("✅ 数据源健康检查完成")
    
    except Exception as e:
        print(f"❌ 数据源健康检查失败: {e}")
        return False
    
    # 测试WebSocket健康检查（无实际WebSocket管理器）
    try:
        print("\n🔌 测试WebSocket健康检查...")
        ws_result = await health_checker.check_websocket_health()
        
        if ws_result.get("error"):
            print("ℹ️  WebSocket健康检查: WebSocket管理器未设置（预期结果）")
        else:
            print("✅ WebSocket健康检查完成")
    
    except Exception as e:
        print(f"❌ WebSocket健康检查失败: {e}")
        return False
    
    # 测试完整健康检查
    try:
        print("\n🏥 测试完整健康检查...")
        full_result = await health_checker.perform_full_health_check()
        
        print("✅ 完整健康检查完成")
        print(f"   总体健康状态: {'健康' if full_result.get('overall_healthy') else '不健康'}")
        print(f"   检查组件数: {full_result.get('summary', {}).get('total_components', 0)}")
        print(f"   健康组件数: {full_result.get('summary', {}).get('healthy_components', 0)}")
        print(f"   总响应时间: {full_result.get('total_response_time_ms', 0)}ms")
        
        # 显示各组件状态
        for component in full_result.get('components', []):
            comp_name = component.get('component', 'unknown')
            comp_healthy = component.get('healthy', False)
            comp_time = component.get('response_time_ms', 0)
            status_icon = "✅" if comp_healthy else "❌"
            print(f"   {status_icon} {comp_name}: {comp_time}ms")
    
    except Exception as e:
        print(f"❌ 完整健康检查失败: {e}")
        return False
    
    # 测试健康状态摘要
    try:
        print("\n📋 测试健康状态摘要...")
        summary = health_checker.get_health_summary()
        
        print("✅ 健康状态摘要获取成功")
        print(f"   状态: {summary.get('status', 'unknown')}")
        print(f"   最后检查: {summary.get('last_check', 'never')}")
        
        if 'components_status' in summary:
            print("   组件状态:")
            for comp, status in summary['components_status'].items():
                status_icon = "✅" if status else "❌"
                print(f"     {status_icon} {comp}")
    
    except Exception as e:
        print(f"❌ 健康状态摘要获取失败: {e}")
        return False
    
    # 测试健康检查历史
    try:
        print("\n📚 测试健康检查历史...")
        history = health_checker.get_health_history(limit=5)
        
        print(f"✅ 健康检查历史获取成功: {len(history)} 条记录")
        
        if history:
            latest = history[-1]
            print(f"   最新检查时间: {latest.get('timestamp', 'unknown')}")
            print(f"   最新检查结果: {'健康' if latest.get('overall_healthy') else '不健康'}")
    
    except Exception as e:
        print(f"❌ 健康检查历史获取失败: {e}")
        return False
    
    print("\n🎉 系统健康检查器测试全部完成！")
    return True

async def test_with_mock_components():
    """测试带有模拟组件的健康检查器"""
    print("\n🔧 测试带有模拟组件的健康检查器...")
    
    try:
        from monitoring.health_checker import SystemHealthChecker
        
        # 创建健康检查器
        health_checker = SystemHealthChecker()
        
        # 创建模拟组件
        class MockDatabaseManager:
            def get_records_count(self):
                return 8341
            
            def health_check(self):
                return {
                    "healthy": True,
                    "checks": [
                        {"name": "connection", "status": "ok"},
                        {"name": "table_structure", "status": "ok"},
                        {"name": "data_integrity", "status": "ok", "record_count": 8341}
                    ]
                }
        
        class MockDataSourceManager:
            def get_cache_status(self):
                return {
                    "cache_files": 3,
                    "cache_hit_rate": 0.85,
                    "total_cache_size_mb": 2.5
                }
            
            def health_check(self):
                return {
                    "healthy": True,
                    "checks": [
                        {"name": "cache_directory", "status": "ok"},
                        {"name": "network_connectivity", "status": "ok"}
                    ]
                }
        
        class MockWebSocketManager:
            def __init__(self):
                self.active_connections = {"session1": {}, "session2": {}}
            
            def get_stats(self):
                return {
                    "current_connections": 2,
                    "total_connections": 15,
                    "messages_sent": 234,
                    "errors": 1
                }
        
        # 设置模拟组件
        health_checker.set_components(
            database_manager=MockDatabaseManager(),
            data_source_manager=MockDataSourceManager(),
            websocket_manager=MockWebSocketManager()
        )
        
        # 执行完整健康检查
        full_result = await health_checker.perform_full_health_check()
        
        print("✅ 模拟组件健康检查完成")
        print(f"   总体健康状态: {'健康' if full_result.get('overall_healthy') else '不健康'}")
        print(f"   健康组件数: {full_result.get('summary', {}).get('healthy_components', 0)}")
        print(f"   总组件数: {full_result.get('summary', {}).get('total_components', 0)}")
        
        # 显示详细结果
        for component in full_result.get('components', []):
            comp_name = component.get('component', 'unknown')
            comp_healthy = component.get('healthy', False)
            comp_checks = len(component.get('checks', []))
            status_icon = "✅" if comp_healthy else "❌"
            print(f"   {status_icon} {comp_name}: {comp_checks} 项检查")
        
        return True
        
    except Exception as e:
        print(f"❌ 模拟组件测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🚀 开始系统健康检查器测试套件")
    
    # 运行基本测试
    test1_result = await test_health_checker()
    
    # 运行模拟组件测试
    test2_result = await test_with_mock_components()
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！系统健康检查器工作正常。")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查问题。")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
