#!/usr/bin/env python3
"""
检查性能监控数据
"""

import sqlite3
import sys
import os

def main():
    try:
        # 数据库路径
        db_path = "data/lottery_data.db"
        
        if not os.path.exists(db_path):
            print(f"❌ 数据库文件不存在: {db_path}")
            return
        
        print(f"📁 数据库路径: {db_path}")
        
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查performance_metrics表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='performance_metrics'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print('✅ performance_metrics表存在')
            
            # 查看表结构
            cursor.execute('PRAGMA table_info(performance_metrics)')
            columns = cursor.fetchall()
            print('\n📋 表结构:')
            for col in columns:
                print(f'  - {col[1]} ({col[2]})')
            
            # 查看数据
            cursor.execute('SELECT COUNT(*) FROM performance_metrics')
            count = cursor.fetchone()[0]
            print(f'\n📊 总记录数: {count}')
            
            if count > 0:
                cursor.execute('SELECT endpoint, response_time, status_code, timestamp FROM performance_metrics ORDER BY timestamp DESC LIMIT 10')
                records = cursor.fetchall()
                print('\n📈 最新10条记录:')
                for record in records:
                    print(f'  - {record[0]} | {record[1]}s | {record[2]} | {record[3]}')
                    
                # 查看不同端点的统计
                cursor.execute('''
                    SELECT 
                        endpoint,
                        AVG(response_time) as avg_time,
                        MAX(response_time) as max_time,
                        COUNT(*) as count
                    FROM performance_metrics 
                    WHERE timestamp > datetime('now', '-1 day')
                    GROUP BY endpoint
                ''')
                
                summary = cursor.fetchall()
                if summary:
                    print('\n📊 24小时内端点统计:')
                    for row in summary:
                        print(f'  - {row[0]}: 平均{row[1]:.3f}s, 最大{row[2]:.3f}s, 请求{row[3]}次')
                else:
                    print('\n⚠️  24小时内没有性能数据')
            else:
                print('\n⚠️  表中没有数据')
                print('\n💡 这解释了为什么Bug检测状态页面显示的是测试数据')
                print('   因为没有真实的API性能监控数据，所以页面可能显示默认或测试数据')
                
        else:
            print('❌ performance_metrics表不存在')
            print('\n💡 这解释了为什么Bug检测状态页面显示的是测试数据')
            print('   因为performance_metrics表不存在，所以无法获取真实的API性能数据')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ 检查性能数据时出错: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
