#!/usr/bin/env python3
"""
8343期 vs 8344期数据差异测试
验证使用不同期数数据训练的模型是否产生不同的预测结果
"""

import sys

sys.path.append('src')
import os
import sqlite3

from prediction.intelligent_fusion import IntelligentFusionSystem


def backup_current_data():
    """备份当前数据库"""
    db_path = 'data/lottery.db'
    if os.path.exists(db_path):
        import shutil
        shutil.copy(db_path, 'data/lottery_backup.db')
        print("✓ 数据库已备份")

def restore_backup_data():
    """恢复备份数据库"""
    backup_path = 'data/lottery_backup.db'
    db_path = 'data/lottery.db'
    if os.path.exists(backup_path):
        import shutil
        shutil.copy(backup_path, db_path)
        print("✓ 数据库已恢复")

def simulate_8343_data():
    """模拟只有8343期数据的情况"""
    try:
        db_path = 'data/lottery.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 删除8344期及之后的数据（假设最新期号是2025187）
        cursor.execute("DELETE FROM lottery_records WHERE CAST(period AS INTEGER) >= 2025187")
        conn.commit()

        # 检查剩余记录数
        cursor.execute("SELECT COUNT(*) FROM lottery_records")
        count = cursor.fetchone()[0]
        conn.close()

        print(f"✓ 模拟8343期数据环境，剩余记录数: {count}")
        return count

    except Exception as e:
        print(f"✗ 模拟8343期数据失败: {e}")
        return 0

def test_prediction_with_data_count(data_description, expected_count):
    """测试指定数据量下的预测结果"""
    print(f"\n=== 测试 {data_description} ===")
    
    try:
        # 创建新的智能融合系统实例
        system = IntelligentFusionSystem()
        
        # 强制重新训练
        print("强制重新训练模型...")
        train_result = system.train_all_models(force_retrain=True)
        
        if not train_result.get('success', False):
            print(f"✗ 训练失败: {train_result}")
            return None
        
        print(f"✓ 训练完成，训练数据量: {system.training_data_count}")
        
        # 进行多次预测测试
        predictions = []
        for i in range(3):
            prediction = system.generate_fusion_prediction([], max_candidates=10, confidence_threshold=0.01)
            
            if 'error' in prediction:
                print(f"✗ 预测失败: {prediction['error']}")
                continue
                
            result = {
                'numbers': prediction.get('numbers', 'N/A'),
                'confidence': prediction.get('confidence', 0),
                'candidates_count': len(prediction.get('candidates', []))
            }
            predictions.append(result)
            print(f"  预测{i+1}: {result['numbers']} (置信度: {result['confidence']:.3f})")
        
        return {
            'data_description': data_description,
            'training_count': system.training_data_count,
            'predictions': predictions
        }
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return None

def analyze_prediction_differences(result_8343, result_8344):
    """分析两种数据环境下的预测差异"""
    print("\n" + "="*60)
    print("📊 预测差异分析")
    print("="*60)
    
    if not result_8343 or not result_8344:
        print("❌ 无法进行差异分析，缺少有效的预测结果")
        return False
    
    print(f"8343期数据训练数量: {result_8343['training_count']}")
    print(f"8344期数据训练数量: {result_8344['training_count']}")
    
    # 比较主要预测号码
    numbers_8343 = [p['numbers'] for p in result_8343['predictions']]
    numbers_8344 = [p['numbers'] for p in result_8344['predictions']]
    
    print(f"\n8343期预测号码: {numbers_8343}")
    print(f"8344期预测号码: {numbers_8344}")
    
    # 检查是否有差异
    unique_8343 = set(numbers_8343)
    unique_8344 = set(numbers_8344)
    
    has_number_difference = unique_8343 != unique_8344
    
    # 比较置信度
    conf_8343 = [p['confidence'] for p in result_8343['predictions']]
    conf_8344 = [p['confidence'] for p in result_8344['predictions']]
    
    print(f"\n8343期置信度: {[f'{c:.3f}' for c in conf_8343]}")
    print(f"8344期置信度: {[f'{c:.3f}' for c in conf_8344]}")
    
    avg_conf_8343 = sum(conf_8343) / len(conf_8343) if conf_8343 else 0
    avg_conf_8344 = sum(conf_8344) / len(conf_8344) if conf_8344 else 0
    
    conf_difference = abs(avg_conf_8343 - avg_conf_8344)
    
    print(f"\n平均置信度差异: {conf_difference:.3f}")
    
    # 结论
    print("\n" + "="*60)
    print("🎯 测试结论")
    print("="*60)
    
    if has_number_difference:
        print("✅ 预测号码有差异 - 数据敏感性正常")
        success = True
    else:
        print("❌ 预测号码无差异 - 数据敏感性异常")
        success = False
    
    if conf_difference > 0.01:
        print("✅ 置信度有显著差异 - 算法动态性正常")
    else:
        print("⚠️ 置信度差异较小 - 算法动态性有限")
    
    return success

def main():
    """主测试流程"""
    print("🧪 开始8343期 vs 8344期数据差异测试")
    print("="*60)
    
    # 备份当前数据
    backup_current_data()
    
    try:
        # 测试8344期数据（当前完整数据）
        result_8344 = test_prediction_with_data_count("8344期完整数据", 8344)
        
        # 模拟8343期数据环境
        count_8343 = simulate_8343_data()
        if count_8343 == 0:
            print("❌ 无法模拟8343期数据环境")
            return
        
        # 测试8343期数据
        result_8343 = test_prediction_with_data_count("8343期数据", count_8343)
        
        # 分析差异
        success = analyze_prediction_differences(result_8343, result_8344)
        
        if success:
            print("\n🎉 测试通过！预测系统对数据变化敏感")
        else:
            print("\n❌ 测试失败！预测系统对数据变化不敏感")
            
    finally:
        # 恢复原始数据
        restore_backup_data()
        print("\n✓ 原始数据已恢复")

if __name__ == "__main__":
    main()
