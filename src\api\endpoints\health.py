#!/usr/bin/env python3
"""
健康检查API端点

提供系统健康状态的详细信息，包括数据库、数据源、WebSocket等组件的状态
"""

import asyncio
import logging
import sys
import time
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import JSONResponse

# 添加项目路径
sys.path.append('src')

logger = logging.getLogger(__name__)

# 导入性能监控器
try:
    from monitoring.performance_monitor import get_performance_monitor
    performance_monitor = get_performance_monitor()
    HAS_PERFORMANCE_MONITOR = True
except ImportError:
    HAS_PERFORMANCE_MONITOR = False
    logger.warning("性能监控器未安装")

# 创建路由器
router = APIRouter(prefix="/api/v1/health", tags=["health"])

# 全局健康检查器实例
health_checker = None

def get_health_checker():
    """获取健康检查器实例"""
    global health_checker
    if health_checker is None:
        try:
            from monitoring.health_checker import SystemHealthChecker
            health_checker = SystemHealthChecker()
            logger.info("健康检查器初始化成功")
        except Exception as e:
            logger.error(f"健康检查器初始化失败: {e}")
            health_checker = None
    return health_checker

def set_health_checker_components(database_manager=None, data_source_manager=None, websocket_manager=None):
    """设置健康检查器的组件"""
    checker = get_health_checker()
    if checker:
        checker.set_components(database_manager, data_source_manager, websocket_manager)

@router.get("/")
async def basic_health_check():
    """基本健康检查"""
    try:
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "service": "福彩3D预测系统",
            "version": "1.0.0",
            "uptime": time.time()
        }
    except Exception as e:
        logger.error(f"基本健康检查失败: {e}")
        raise HTTPException(status_code=500, detail="Health check failed")

@router.get("/ping")
async def ping():
    """简单的ping检查"""
    return {
        "status": "pong",
        "timestamp": datetime.now().isoformat()
    }

@router.get("/detailed")
async def detailed_health_check(
    include_history: bool = Query(False, description="是否包含历史记录"),
    history_limit: int = Query(5, description="历史记录数量限制", ge=1, le=50)
):
    """详细健康检查"""
    try:
        checker = get_health_checker()
        if not checker:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "service_unavailable",
                    "message": "健康检查器不可用",
                    "timestamp": datetime.now().isoformat()
                }
            )
        
        # 执行完整健康检查
        health_result = await checker.perform_full_health_check()
        
        # 构建响应
        response = {
            "status": "healthy" if health_result["overall_healthy"] else "unhealthy",
            "timestamp": health_result["timestamp"],
            "overall_healthy": health_result["overall_healthy"],
            "total_response_time_ms": health_result["total_response_time_ms"],
            "summary": health_result["summary"],
            "components": health_result["components"]
        }
        
        # 添加历史记录（如果请求）
        if include_history:
            response["history"] = checker.get_health_history(limit=history_limit)
        
        # 根据健康状态设置HTTP状态码
        status_code = 200 if health_result["overall_healthy"] else 503
        
        return JSONResponse(status_code=status_code, content=response)
        
    except Exception as e:
        logger.error(f"详细健康检查失败: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"健康检查执行失败: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )

@router.get("/summary")
async def health_summary():
    """健康状态摘要"""
    try:
        checker = get_health_checker()
        if not checker:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "service_unavailable",
                    "message": "健康检查器不可用",
                    "timestamp": datetime.now().isoformat()
                }
            )
        
        summary = checker.get_health_summary()
        
        # 添加时间戳
        summary["timestamp"] = datetime.now().isoformat()
        
        # 根据状态设置HTTP状态码
        if summary.get("status") == "healthy":
            status_code = 200
        elif summary.get("status") == "unhealthy":
            status_code = 503
        else:
            status_code = 200  # no_data状态
        
        return JSONResponse(status_code=status_code, content=summary)
        
    except Exception as e:
        logger.error(f"健康状态摘要获取失败: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"摘要获取失败: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )

@router.get("/components/{component_name}")
async def component_health_check(component_name: str):
    """单个组件健康检查"""
    try:
        checker = get_health_checker()
        if not checker:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "service_unavailable",
                    "message": "健康检查器不可用",
                    "timestamp": datetime.now().isoformat()
                }
            )
        
        # 根据组件名称执行相应的检查
        if component_name == "database":
            result = await checker.check_database_health()
        elif component_name == "data_source":
            result = await checker.check_data_source_health()
        elif component_name == "websocket":
            result = await checker.check_websocket_health()
        elif component_name == "system_resources":
            result = await checker.check_system_resources()
        else:
            return JSONResponse(
                status_code=404,
                content={
                    "status": "not_found",
                    "message": f"未知组件: {component_name}",
                    "available_components": ["database", "data_source", "websocket", "system_resources"],
                    "timestamp": datetime.now().isoformat()
                }
            )
        
        # 添加时间戳
        result["timestamp"] = datetime.now().isoformat()
        
        # 根据健康状态设置HTTP状态码
        status_code = 200 if result.get("healthy", False) else 503
        
        return JSONResponse(status_code=status_code, content=result)

    except Exception as e:
        logger.error(f"组件健康检查失败 [{component_name}]: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"组件检查失败: {str(e)}",
                "component": component_name,
                "timestamp": datetime.now().isoformat()
            }
        )

@router.get("/websocket/connections")
async def get_websocket_connections():
    """获取WebSocket连接统计信息"""
    try:
        checker = get_health_checker()
        if not checker:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "unavailable",
                    "message": "健康检查器不可用",
                    "timestamp": datetime.now().isoformat()
                }
            )

        # 获取WebSocket管理器
        websocket_manager = getattr(checker, 'websocket_manager', None)
        if not websocket_manager:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "unavailable",
                    "message": "WebSocket管理器不可用",
                    "timestamp": datetime.now().isoformat()
                }
            )

        # 获取连接统计
        connection_stats = {
            "total_connections": len(getattr(websocket_manager, 'connections', {})),
            "active_connections": len([
                conn for conn in getattr(websocket_manager, 'connections', {}).values()
                if getattr(conn, 'is_active', False)
            ]),
            "connection_details": []
        }

        # 获取连接详情
        for conn_id, connection in getattr(websocket_manager, 'connections', {}).items():
            try:
                connection_info = {
                    "id": conn_id,
                    "is_active": getattr(connection, 'is_active', False),
                    "connected_at": getattr(connection, 'connected_at', None),
                    "last_ping": getattr(connection, 'last_ping', None),
                    "message_count": getattr(connection, 'message_count', 0)
                }
                connection_stats["connection_details"].append(connection_info)
            except Exception as e:
                logger.warning(f"获取连接详情失败 {conn_id}: {e}")

        connection_stats["timestamp"] = datetime.now().isoformat()
        return JSONResponse(status_code=200, content=connection_stats)

    except Exception as e:
        logger.error(f"获取WebSocket连接统计失败: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"获取连接统计失败: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )

@router.get("/websocket/performance")
async def get_websocket_performance():
    """获取WebSocket性能指标"""
    try:
        checker = get_health_checker()
        if not checker:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "unavailable",
                    "message": "健康检查器不可用",
                    "timestamp": datetime.now().isoformat()
                }
            )

        # 获取WebSocket管理器
        websocket_manager = getattr(checker, 'websocket_manager', None)
        if not websocket_manager:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "unavailable",
                    "message": "WebSocket管理器不可用",
                    "timestamp": datetime.now().isoformat()
                }
            )

        # 收集性能指标
        performance_metrics = {
            "connection_metrics": {
                "total_connections": len(getattr(websocket_manager, 'connections', {})),
                "active_connections": len([
                    conn for conn in getattr(websocket_manager, 'connections', {}).values()
                    if getattr(conn, 'is_active', False)
                ]),
                "connection_success_rate": 0.0,
                "average_connection_duration": 0.0
            },
            "message_metrics": {
                "total_messages_sent": getattr(websocket_manager, 'total_messages_sent', 0),
                "total_messages_received": getattr(websocket_manager, 'total_messages_received', 0),
                "messages_per_second": 0.0,
                "average_message_size": 0.0
            },
            "error_metrics": {
                "connection_errors": getattr(websocket_manager, 'connection_errors', 0),
                "message_errors": getattr(websocket_manager, 'message_errors', 0),
                "error_rate": 0.0
            },
            "resource_usage": {
                "memory_usage_mb": 0.0,
                "cpu_usage_percent": 0.0,
                "network_io_bytes": 0
            }
        }

        # 计算成功率
        total_attempts = getattr(websocket_manager, 'connection_attempts', 0)
        if total_attempts > 0:
            success_count = total_attempts - performance_metrics["error_metrics"]["connection_errors"]
            performance_metrics["connection_metrics"]["connection_success_rate"] = success_count / total_attempts

        # 计算消息速率
        uptime_seconds = getattr(websocket_manager, 'uptime_seconds', 1)
        total_messages = (performance_metrics["message_metrics"]["total_messages_sent"] +
                         performance_metrics["message_metrics"]["total_messages_received"])
        performance_metrics["message_metrics"]["messages_per_second"] = total_messages / uptime_seconds

        # 计算错误率
        if total_messages > 0:
            total_errors = (performance_metrics["error_metrics"]["connection_errors"] +
                           performance_metrics["error_metrics"]["message_errors"])
            performance_metrics["error_metrics"]["error_rate"] = total_errors / total_messages

        performance_metrics["timestamp"] = datetime.now().isoformat()
        performance_metrics["uptime_seconds"] = uptime_seconds

        return JSONResponse(status_code=200, content=performance_metrics)

    except Exception as e:
        logger.error(f"获取WebSocket性能指标失败: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"获取性能指标失败: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )

@router.get("/history")
async def health_history(
    limit: int = Query(10, description="历史记录数量限制", ge=1, le=100)
):
    """健康检查历史记录"""
    try:
        checker = get_health_checker()
        if not checker:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "service_unavailable",
                    "message": "健康检查器不可用",
                    "timestamp": datetime.now().isoformat()
                }
            )
        
        history = checker.get_health_history(limit=limit)
        
        return {
            "status": "success",
            "timestamp": datetime.now().isoformat(),
            "history_count": len(history),
            "limit": limit,
            "history": history
        }
        
    except Exception as e:
        logger.error(f"健康检查历史获取失败: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"历史记录获取失败: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )

@router.post("/check")
async def trigger_health_check():
    """手动触发健康检查"""
    try:
        checker = get_health_checker()
        if not checker:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "service_unavailable",
                    "message": "健康检查器不可用",
                    "timestamp": datetime.now().isoformat()
                }
            )
        
        # 执行健康检查
        start_time = time.time()
        health_result = await checker.perform_full_health_check()
        execution_time = (time.time() - start_time) * 1000
        
        response = {
            "status": "check_completed",
            "timestamp": datetime.now().isoformat(),
            "execution_time_ms": int(execution_time),
            "overall_healthy": health_result["overall_healthy"],
            "summary": health_result["summary"],
            "components": health_result["components"]
        }
        
        # 根据健康状态设置HTTP状态码
        status_code = 200 if health_result["overall_healthy"] else 503
        
        return JSONResponse(status_code=status_code, content=response)
        
    except Exception as e:
        logger.error(f"手动健康检查失败: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"健康检查执行失败: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )

@router.get("/metrics")
async def health_metrics():
    """健康检查指标"""
    try:
        checker = get_health_checker()
        if not checker:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "service_unavailable",
                    "message": "健康检查器不可用",
                    "timestamp": datetime.now().isoformat()
                }
            )
        
        # 获取历史记录进行统计
        history = checker.get_health_history(limit=50)
        
        if not history:
            return {
                "status": "no_data",
                "message": "暂无健康检查数据",
                "timestamp": datetime.now().isoformat()
            }
        
        # 计算指标
        total_checks = len(history)
        healthy_checks = sum(1 for h in history if h.get("overall_healthy", False))
        unhealthy_checks = total_checks - healthy_checks
        
        # 计算平均响应时间
        response_times = [h.get("total_response_time_ms", 0) for h in history]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        # 组件健康率统计
        component_stats = {}
        for check in history:
            for component in check.get("components", []):
                comp_name = component.get("component", "unknown")
                if comp_name not in component_stats:
                    component_stats[comp_name] = {"total": 0, "healthy": 0}
                
                component_stats[comp_name]["total"] += 1
                if component.get("healthy", False):
                    component_stats[comp_name]["healthy"] += 1
        
        # 计算健康率
        for comp_name, stats in component_stats.items():
            stats["health_rate"] = stats["healthy"] / stats["total"] if stats["total"] > 0 else 0
        
        return {
            "status": "success",
            "timestamp": datetime.now().isoformat(),
            "metrics": {
                "total_checks": total_checks,
                "healthy_checks": healthy_checks,
                "unhealthy_checks": unhealthy_checks,
                "overall_health_rate": healthy_checks / total_checks if total_checks > 0 else 0,
                "average_response_time_ms": round(avg_response_time, 2),
                "component_health_rates": component_stats
            }
        }
        
    except Exception as e:
        logger.error(f"健康检查指标获取失败: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"指标获取失败: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )

@router.get("/performance/dashboard")
async def performance_dashboard():
    """性能监控仪表板"""
    try:
        if not HAS_PERFORMANCE_MONITOR:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "service_unavailable",
                    "message": "性能监控器不可用",
                    "timestamp": datetime.now().isoformat()
                }
            )

        dashboard_data = performance_monitor.get_performance_dashboard_data()

        return {
            "status": "success",
            "timestamp": datetime.now().isoformat(),
            "data": dashboard_data
        }

    except Exception as e:
        logger.error(f"获取性能仪表板失败: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"仪表板获取失败: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )

@router.get("/performance/metrics")
async def performance_metrics(
    category: str = Query(None, description="指标类别"),
    time_range: int = Query(60, description="时间范围（分钟）", ge=1, le=1440)
):
    """获取性能指标"""
    try:
        if not HAS_PERFORMANCE_MONITOR:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "service_unavailable",
                    "message": "性能监控器不可用",
                    "timestamp": datetime.now().isoformat()
                }
            )

        summary = performance_monitor.get_metrics_summary(
            category=category,
            time_range_minutes=time_range
        )

        return {
            "status": "success",
            "timestamp": datetime.now().isoformat(),
            "summary": summary
        }

    except Exception as e:
        logger.error(f"获取性能指标失败: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"指标获取失败: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )
