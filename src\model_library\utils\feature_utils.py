"""
特征工程工具函数
"""

import numpy as np
from typing import List, Dict, Any, Optional, Set
from collections import Counter, defaultdict
import math


class FeatureExtractor:
    """特征提取器"""
    
    @staticmethod
    def extract_basic_features(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """提取基础特征"""
        enhanced_data = []
        
        for record in data:
            enhanced_record = record.copy()
            
            # 基础数字特征
            if 'number' in record and record['number']:
                digits = [int(d) for d in record['number']]
                
                enhanced_record['百位'] = digits[0] if len(digits) > 0 else 0
                enhanced_record['十位'] = digits[1] if len(digits) > 1 else 0
                enhanced_record['个位'] = digits[2] if len(digits) > 2 else 0
                enhanced_record['和值'] = sum(digits)
                enhanced_record['跨度'] = max(digits) - min(digits) if digits else 0
                
                # 奇偶特征
                enhanced_record['奇数个数'] = sum(1 for d in digits if d % 2 == 1)
                enhanced_record['偶数个数'] = sum(1 for d in digits if d % 2 == 0)
                enhanced_record['奇偶比'] = enhanced_record['奇数个数'] / 3.0
                
                # 大小特征
                enhanced_record['大数个数'] = sum(1 for d in digits if d >= 5)
                enhanced_record['小数个数'] = sum(1 for d in digits if d < 5)
                enhanced_record['大小比'] = enhanced_record['大数个数'] / 3.0
                
                # 质合特征
                primes = {2, 3, 5, 7}
                enhanced_record['质数个数'] = sum(1 for d in digits if d in primes)
                enhanced_record['合数个数'] = 3 - enhanced_record['质数个数']
                
                # 连号特征
                sorted_digits = sorted(digits)
                enhanced_record['连号个数'] = FeatureExtractor._count_consecutive(sorted_digits)
                enhanced_record['是否有连号'] = enhanced_record['连号个数'] > 0
                
                # 重复号码特征
                digit_counts = Counter(digits)
                enhanced_record['重复个数'] = sum(1 for count in digit_counts.values() if count > 1)
                enhanced_record['是否有重复'] = enhanced_record['重复个数'] > 0
                
                # 组选类型
                if len(set(digits)) == 1:
                    enhanced_record['组选类型'] = '豹子'
                elif len(set(digits)) == 2:
                    enhanced_record['组选类型'] = '组三'
                else:
                    enhanced_record['组选类型'] = '组六'
            
            enhanced_data.append(enhanced_record)
        
        return enhanced_data
    
    @staticmethod
    def extract_frequency_features(data: List[Dict[str, Any]], window_size: int = 100) -> List[Dict[str, Any]]:
        """提取频率特征"""
        enhanced_data = []
        
        for i, record in enumerate(data):
            enhanced_record = record.copy()
            
            # 获取窗口数据
            start_idx = max(0, i - window_size)
            window_data = data[start_idx:i]
            
            if window_data:
                # 各位数字频率
                for pos, pos_name in enumerate(['百位', '十位', '个位']):
                    if pos_name in record:
                        digit = record[pos_name]
                        # 计算该数字在该位置的出现频率
                        pos_digits = [r.get(pos_name, 0) for r in window_data if pos_name in r]
                        frequency = pos_digits.count(digit) / len(pos_digits) if pos_digits else 0
                        enhanced_record[f'{pos_name}_频率'] = frequency
                
                # 和值频率
                if '和值' in record:
                    sum_values = [r.get('和值', 0) for r in window_data if '和值' in r]
                    sum_frequency = sum_values.count(record['和值']) / len(sum_values) if sum_values else 0
                    enhanced_record['和值_频率'] = sum_frequency
                
                # 跨度频率
                if '跨度' in record:
                    span_values = [r.get('跨度', 0) for r in window_data if '跨度' in r]
                    span_frequency = span_values.count(record['跨度']) / len(span_values) if span_values else 0
                    enhanced_record['跨度_频率'] = span_frequency
            
            enhanced_data.append(enhanced_record)
        
        return enhanced_data
    
    @staticmethod
    def extract_missing_features(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """提取遗漏特征"""
        enhanced_data = []
        
        # 记录每个数字在每个位置的最后出现位置
        last_occurrence = {
            '百位': {i: -1 for i in range(10)},
            '十位': {i: -1 for i in range(10)},
            '个位': {i: -1 for i in range(10)}
        }
        
        for i, record in enumerate(data):
            enhanced_record = record.copy()
            
            # 计算遗漏值
            for pos_name in ['百位', '十位', '个位']:
                if pos_name in record:
                    digit = record[pos_name]
                    
                    # 计算当前数字的遗漏期数
                    last_pos = last_occurrence[pos_name][digit]
                    missing_periods = i - last_pos - 1 if last_pos >= 0 else i
                    enhanced_record[f'{pos_name}_遗漏'] = missing_periods
                    
                    # 更新最后出现位置
                    last_occurrence[pos_name][digit] = i
                    
                    # 计算其他数字的遗漏值统计
                    other_missing = []
                    for other_digit in range(10):
                        if other_digit != digit:
                            other_last_pos = last_occurrence[pos_name][other_digit]
                            other_missing_periods = i - other_last_pos - 1 if other_last_pos >= 0 else i
                            other_missing.append(other_missing_periods)
                    
                    if other_missing:
                        enhanced_record[f'{pos_name}_平均遗漏'] = np.mean(other_missing)
                        enhanced_record[f'{pos_name}_最大遗漏'] = max(other_missing)
                        enhanced_record[f'{pos_name}_最小遗漏'] = min(other_missing)
            
            enhanced_data.append(enhanced_record)
        
        return enhanced_data
    
    @staticmethod
    def extract_trend_features(data: List[Dict[str, Any]], window_size: int = 10) -> List[Dict[str, Any]]:
        """提取趋势特征"""
        enhanced_data = []
        
        for i, record in enumerate(data):
            enhanced_record = record.copy()
            
            # 获取窗口数据
            start_idx = max(0, i - window_size)
            window_data = data[start_idx:i]
            
            if len(window_data) >= 3:
                # 和值趋势
                sum_values = [r.get('和值', 0) for r in window_data if '和值' in r]
                if len(sum_values) >= 3:
                    enhanced_record['和值_趋势'] = FeatureExtractor._calculate_trend(sum_values)
                    enhanced_record['和值_波动'] = np.std(sum_values) if sum_values else 0
                
                # 跨度趋势
                span_values = [r.get('跨度', 0) for r in window_data if '跨度' in r]
                if len(span_values) >= 3:
                    enhanced_record['跨度_趋势'] = FeatureExtractor._calculate_trend(span_values)
                    enhanced_record['跨度_波动'] = np.std(span_values) if span_values else 0
                
                # 奇偶比趋势
                odd_ratios = [r.get('奇偶比', 0) for r in window_data if '奇偶比' in r]
                if len(odd_ratios) >= 3:
                    enhanced_record['奇偶比_趋势'] = FeatureExtractor._calculate_trend(odd_ratios)
                
                # 大小比趋势
                size_ratios = [r.get('大小比', 0) for r in window_data if '大小比' in r]
                if len(size_ratios) >= 3:
                    enhanced_record['大小比_趋势'] = FeatureExtractor._calculate_trend(size_ratios)
            
            enhanced_data.append(enhanced_record)
        
        return enhanced_data
    
    @staticmethod
    def extract_combination_features(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """提取组合特征"""
        enhanced_data = []
        
        for record in data:
            enhanced_record = record.copy()
            
            if all(pos in record for pos in ['百位', '十位', '个位']):
                digits = [record['百位'], record['十位'], record['个位']]
                
                # AC值（数字复杂度）
                enhanced_record['AC值'] = FeatureExtractor._calculate_ac_value(digits)
                
                # 数字根
                enhanced_record['数字根'] = FeatureExtractor._calculate_digital_root(sum(digits))
                
                # 位置和值
                enhanced_record['位置和值'] = record['百位'] * 100 + record['十位'] * 10 + record['个位']
                
                # 数字乘积
                enhanced_record['数字乘积'] = record['百位'] * record['十位'] * record['个位']
                
                # 数字方差
                enhanced_record['数字方差'] = np.var(digits)
                
                # 数字标准差
                enhanced_record['数字标准差'] = np.std(digits)
                
                # 数字偏度
                if len(digits) > 2:
                    mean_val = np.mean(digits)
                    std_val = np.std(digits)
                    if std_val > 0:
                        skewness = np.mean([(x - mean_val) ** 3 for x in digits]) / (std_val ** 3)
                        enhanced_record['数字偏度'] = skewness
            
            enhanced_data.append(enhanced_record)
        
        return enhanced_data
    
    @staticmethod
    def _count_consecutive(sorted_digits: List[int]) -> int:
        """计算连续数字个数"""
        if len(sorted_digits) < 2:
            return 0
        
        consecutive_count = 0
        for i in range(len(sorted_digits) - 1):
            if sorted_digits[i + 1] - sorted_digits[i] == 1:
                consecutive_count += 1
        
        return consecutive_count
    
    @staticmethod
    def _calculate_trend(values: List[float]) -> float:
        """计算趋势值（简单线性回归斜率）"""
        if len(values) < 2:
            return 0.0
        
        n = len(values)
        x = list(range(n))
        
        # 计算线性回归斜率
        x_mean = np.mean(x)
        y_mean = np.mean(values)
        
        numerator = sum((x[i] - x_mean) * (values[i] - y_mean) for i in range(n))
        denominator = sum((x[i] - x_mean) ** 2 for i in range(n))
        
        if denominator == 0:
            return 0.0
        
        slope = numerator / denominator
        return slope
    
    @staticmethod
    def _calculate_ac_value(digits: List[int]) -> int:
        """计算AC值（数字复杂度）"""
        if len(digits) < 2:
            return 0
        
        differences = set()
        for i in range(len(digits)):
            for j in range(i + 1, len(digits)):
                differences.add(abs(digits[i] - digits[j]))
        
        return len(differences) - 1
    
    @staticmethod
    def _calculate_digital_root(number: int) -> int:
        """计算数字根"""
        while number >= 10:
            number = sum(int(digit) for digit in str(number))
        return number


class FeatureValidator:
    """特征验证器"""
    
    @staticmethod
    def validate_features(data: List[Dict[str, Any]], required_features: List[str]) -> Dict[str, Any]:
        """验证特征完整性"""
        if not data:
            return {"valid": False, "error": "数据为空"}
        
        missing_features = []
        for feature in required_features:
            if feature not in data[0]:
                missing_features.append(feature)
        
        if missing_features:
            return {
                "valid": False,
                "error": f"缺少必要特征: {missing_features}"
            }
        
        # 检查特征值的有效性
        invalid_records = 0
        for record in data:
            for feature in required_features:
                value = record.get(feature)
                if value is None or (isinstance(value, (int, float)) and math.isnan(value)):
                    invalid_records += 1
                    break
        
        validity_score = 1.0 - (invalid_records / len(data))
        
        return {
            "valid": validity_score >= 0.95,
            "validity_score": validity_score,
            "total_records": len(data),
            "invalid_records": invalid_records,
            "feature_completeness": f"{validity_score:.2%}"
        }
    
    @staticmethod
    def check_feature_distribution(data: List[Dict[str, Any]], feature: str) -> Dict[str, Any]:
        """检查特征分布"""
        if not data or feature not in data[0]:
            return {"error": f"特征 '{feature}' 不存在"}
        
        values = [record.get(feature, 0) for record in data if feature in record]
        
        if not values:
            return {"error": f"特征 '{feature}' 无有效值"}
        
        unique_values = len(set(values))
        total_values = len(values)
        
        return {
            "feature": feature,
            "total_values": total_values,
            "unique_values": unique_values,
            "uniqueness_ratio": unique_values / total_values,
            "mean": np.mean(values),
            "std": np.std(values),
            "min": min(values),
            "max": max(values),
            "distribution_type": "continuous" if unique_values > 10 else "discrete"
        }
