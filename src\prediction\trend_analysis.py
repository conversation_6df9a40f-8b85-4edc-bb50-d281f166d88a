"""
短期趋势捕捉算法模块
实现冷号回补、热号延续、温号转换等短期趋势分析
"""

import os
import sqlite3
from collections import Counter, defaultdict, deque
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd


class TrendAnalyzer:
    """短期趋势分析器"""
    
    def __init__(self, db_path: str = None, window_size: int = 30):
        """
        初始化趋势分析器
        
        Args:
            db_path: 数据库路径
            window_size: 分析窗口大小（期数）
        """
        self.db_path = db_path or os.path.join('data', 'lottery.db')
        self.window_size = window_size
        self.trend_patterns = {}
        
    def load_recent_data(self, limit: int = 200) -> List[Dict[str, Any]]:
        """
        加载最近的开奖数据
        
        Args:
            limit: 加载的记录数量
            
        Returns:
            最近的开奖记录列表
        """
        if not os.path.exists(self.db_path):
            raise FileNotFoundError(f"数据库文件不存在: {self.db_path}")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 查询最近的开奖数据
        cursor.execute("""
            SELECT period, date, numbers, trial_numbers, 
                   sales_amount, draw_machine, trial_machine
            FROM lottery_records 
            WHERE numbers IS NOT NULL 
            AND numbers != ''
            ORDER BY date DESC, period DESC
            LIMIT ?
        """, (limit,))
        
        records = []
        for row in cursor.fetchall():
            record = {
                'period': row[0],
                'date': row[1],
                'numbers': row[2],
                'trial_numbers': row[3],
                'sales_amount': row[4] or 0,
                'draw_machine': row[5],
                'trial_machine': row[6]
            }
            records.append(record)
        
        conn.close()
        
        # 按时间正序排列（最早的在前）
        records.reverse()
        return records
    
    def analyze_digit_frequency_trends(self, records: List[Dict]) -> Dict[str, Any]:
        """
        分析数字频率趋势
        
        Args:
            records: 开奖记录列表
            
        Returns:
            数字频率趋势分析结果
        """
        if len(records) < self.window_size:
            return {}
        
        # 统计每个数字在不同时间窗口的出现频率
        digit_trends = {}
        
        for digit in range(10):
            digit_trends[digit] = {
                'recent_frequency': 0,
                'historical_frequency': 0,
                'trend_direction': 'stable',
                'heat_level': 'warm',
                'last_appearance': -1,
                'gap_distribution': [],
                'frequency_timeline': []
            }
        
        # 分析最近窗口和历史窗口
        recent_window = records[-self.window_size:]
        historical_window = records[:-self.window_size] if len(records) > self.window_size else []
        
        # 统计最近窗口的频率
        recent_digit_counts = Counter()
        for record in recent_window:
            if len(record['numbers']) == 3:
                for digit_char in record['numbers']:
                    digit = int(digit_char)
                    recent_digit_counts[digit] += 1
        
        # 统计历史窗口的频率
        historical_digit_counts = Counter()
        if historical_window:
            for record in historical_window:
                if len(record['numbers']) == 3:
                    for digit_char in record['numbers']:
                        digit = int(digit_char)
                        historical_digit_counts[digit] += 1
        
        # 计算趋势
        total_recent = sum(recent_digit_counts.values())
        total_historical = sum(historical_digit_counts.values())
        
        for digit in range(10):
            recent_freq = recent_digit_counts[digit] / total_recent if total_recent > 0 else 0
            historical_freq = historical_digit_counts[digit] / total_historical if total_historical > 0 else 0
            
            digit_trends[digit]['recent_frequency'] = recent_freq
            digit_trends[digit]['historical_frequency'] = historical_freq
            
            # 判断趋势方向
            if recent_freq > historical_freq * 1.2:
                digit_trends[digit]['trend_direction'] = 'rising'
            elif recent_freq < historical_freq * 0.8:
                digit_trends[digit]['trend_direction'] = 'falling'
            else:
                digit_trends[digit]['trend_direction'] = 'stable'
            
            # 判断热度级别
            expected_freq = 0.1  # 理论期望频率
            if recent_freq > expected_freq * 1.5:
                digit_trends[digit]['heat_level'] = 'hot'
            elif recent_freq < expected_freq * 0.5:
                digit_trends[digit]['heat_level'] = 'cold'
            else:
                digit_trends[digit]['heat_level'] = 'warm'
            
            # 计算最后出现位置和间隔分布
            last_pos = -1
            gaps = []
            current_gap = 0
            
            for i, record in enumerate(reversed(records)):
                if len(record['numbers']) == 3 and str(digit) in record['numbers']:
                    if last_pos == -1:
                        last_pos = i
                    if current_gap > 0:
                        gaps.append(current_gap)
                    current_gap = 0
                else:
                    current_gap += 1
            
            digit_trends[digit]['last_appearance'] = last_pos
            digit_trends[digit]['gap_distribution'] = gaps[:10]  # 保留最近10个间隔
        
        return digit_trends
    
    def analyze_position_trends(self, records: List[Dict]) -> Dict[str, Any]:
        """
        分析位置趋势（百位、十位、个位）
        
        Args:
            records: 开奖记录列表
            
        Returns:
            位置趋势分析结果
        """
        position_trends = {
            'hundreds': {'digit_trends': {}, 'pattern_trends': {}},
            'tens': {'digit_trends': {}, 'pattern_trends': {}},
            'units': {'digit_trends': {}, 'pattern_trends': {}}
        }
        
        positions = ['hundreds', 'tens', 'units']
        
        for pos_idx, pos_name in enumerate(positions):
            # 统计每个位置上各数字的趋势
            position_digit_counts = Counter()
            recent_position_counts = Counter()
            
            # 分析最近窗口
            recent_window = records[-self.window_size:] if len(records) >= self.window_size else records
            
            for record in recent_window:
                if len(record['numbers']) == 3:
                    digit = int(record['numbers'][pos_idx])
                    recent_position_counts[digit] += 1
            
            # 分析历史数据
            historical_window = records[:-self.window_size] if len(records) > self.window_size else []
            historical_position_counts = Counter()
            
            for record in historical_window:
                if len(record['numbers']) == 3:
                    digit = int(record['numbers'][pos_idx])
                    historical_position_counts[digit] += 1
            
            # 计算位置数字趋势
            total_recent = sum(recent_position_counts.values())
            total_historical = sum(historical_position_counts.values())
            
            for digit in range(10):
                recent_freq = recent_position_counts[digit] / total_recent if total_recent > 0 else 0
                historical_freq = historical_position_counts[digit] / total_historical if total_historical > 0 else 0
                
                trend_strength = recent_freq - historical_freq
                
                position_trends[pos_name]['digit_trends'][digit] = {
                    'recent_frequency': recent_freq,
                    'historical_frequency': historical_freq,
                    'trend_strength': trend_strength,
                    'recommendation': self._get_digit_recommendation(recent_freq, historical_freq, trend_strength)
                }
            
            # 分析位置模式趋势
            position_trends[pos_name]['pattern_trends'] = self._analyze_position_patterns(
                recent_window, pos_idx
            )
        
        return position_trends
    
    def _get_digit_recommendation(self, recent_freq: float, historical_freq: float, trend_strength: float) -> str:
        """
        获取数字推荐策略
        
        Args:
            recent_freq: 最近频率
            historical_freq: 历史频率
            trend_strength: 趋势强度
            
        Returns:
            推荐策略
        """
        if trend_strength > 0.02:  # 上升趋势
            if recent_freq > 0.15:
                return 'hot_continue'  # 热号延续
            else:
                return 'warm_rising'   # 温号上升
        elif trend_strength < -0.02:  # 下降趋势
            if recent_freq < 0.05:
                return 'cold_rebound'  # 冷号回补
            else:
                return 'cooling_down'  # 降温
        else:
            return 'stable'  # 稳定
    
    def _analyze_position_patterns(self, records: List[Dict], position: int) -> Dict[str, Any]:
        """
        分析位置模式
        
        Args:
            records: 记录列表
            position: 位置索引（0=百位，1=十位，2=个位）
            
        Returns:
            位置模式分析结果
        """
        patterns = {
            'consecutive_runs': [],  # 连续出现
            'alternating_patterns': [],  # 交替模式
            'gap_patterns': [],  # 间隔模式
            'cycle_detection': {}  # 周期检测
        }
        
        if len(records) < 5:
            return patterns
        
        # 提取位置数字序列
        digit_sequence = []
        for record in records:
            if len(record['numbers']) == 3:
                digit_sequence.append(int(record['numbers'][position]))
        
        if len(digit_sequence) < 5:
            return patterns
        
        # 检测连续运行
        current_digit = digit_sequence[0]
        current_run = 1
        
        for i in range(1, len(digit_sequence)):
            if digit_sequence[i] == current_digit:
                current_run += 1
            else:
                if current_run >= 2:
                    patterns['consecutive_runs'].append({
                        'digit': current_digit,
                        'length': current_run,
                        'end_position': i - 1
                    })
                current_digit = digit_sequence[i]
                current_run = 1
        
        # 检测交替模式
        for i in range(len(digit_sequence) - 3):
            if (digit_sequence[i] == digit_sequence[i+2] and 
                digit_sequence[i+1] == digit_sequence[i+3] and
                digit_sequence[i] != digit_sequence[i+1]):
                patterns['alternating_patterns'].append({
                    'digits': [digit_sequence[i], digit_sequence[i+1]],
                    'start_position': i,
                    'length': 4
                })
        
        # 检测间隔模式
        digit_positions = defaultdict(list)
        for i, digit in enumerate(digit_sequence):
            digit_positions[digit].append(i)
        
        for digit, positions in digit_positions.items():
            if len(positions) >= 3:
                gaps = [positions[i+1] - positions[i] for i in range(len(positions)-1)]
                if len(set(gaps)) <= 2:  # 间隔相对规律
                    patterns['gap_patterns'].append({
                        'digit': digit,
                        'gaps': gaps,
                        'regularity': 1 - len(set(gaps)) / len(gaps)
                    })
        
        return patterns
    
    def analyze_combination_trends(self, records: List[Dict]) -> Dict[str, Any]:
        """
        分析组合趋势（组三、组六、奇偶、大小等）
        
        Args:
            records: 开奖记录列表
            
        Returns:
            组合趋势分析结果
        """
        combination_trends = {
            'form_trends': {},  # 形态趋势
            'parity_trends': {},  # 奇偶趋势
            'size_trends': {},  # 大小趋势
            'sum_trends': {},  # 和值趋势
            'span_trends': {}  # 跨度趋势
        }
        
        if len(records) < self.window_size:
            return combination_trends
        
        recent_window = records[-self.window_size:]
        historical_window = records[:-self.window_size] if len(records) > self.window_size else []
        
        # 分析形态趋势（组三/组六）
        recent_forms = Counter()
        historical_forms = Counter()
        
        for record in recent_window:
            if len(record['numbers']) == 3:
                digits = [int(d) for d in record['numbers']]
                if len(set(digits)) == 1:
                    form = 'triple'  # 豹子
                elif len(set(digits)) == 2:
                    form = 'group3'  # 组三
                else:
                    form = 'group6'  # 组六
                recent_forms[form] += 1
        
        for record in historical_window:
            if len(record['numbers']) == 3:
                digits = [int(d) for d in record['numbers']]
                if len(set(digits)) == 1:
                    form = 'triple'
                elif len(set(digits)) == 2:
                    form = 'group3'
                else:
                    form = 'group6'
                historical_forms[form] += 1
        
        # 计算形态趋势
        total_recent = sum(recent_forms.values())
        total_historical = sum(historical_forms.values())
        
        for form in ['triple', 'group3', 'group6']:
            recent_freq = recent_forms[form] / total_recent if total_recent > 0 else 0
            historical_freq = historical_forms[form] / total_historical if total_historical > 0 else 0
            
            combination_trends['form_trends'][form] = {
                'recent_frequency': recent_freq,
                'historical_frequency': historical_freq,
                'trend_direction': 'rising' if recent_freq > historical_freq * 1.2 else 
                                 'falling' if recent_freq < historical_freq * 0.8 else 'stable'
            }
        
        # 分析奇偶趋势
        parity_patterns = ['3odd', '2odd1even', '1odd2even', '3even']
        recent_parity = Counter()
        historical_parity = Counter()
        
        for record in recent_window:
            if len(record['numbers']) == 3:
                digits = [int(d) for d in record['numbers']]
                odd_count = sum(1 for d in digits if d % 2 == 1)
                if odd_count == 3:
                    pattern = '3odd'
                elif odd_count == 2:
                    pattern = '2odd1even'
                elif odd_count == 1:
                    pattern = '1odd2even'
                else:
                    pattern = '3even'
                recent_parity[pattern] += 1
        
        for record in historical_window:
            if len(record['numbers']) == 3:
                digits = [int(d) for d in record['numbers']]
                odd_count = sum(1 for d in digits if d % 2 == 1)
                if odd_count == 3:
                    pattern = '3odd'
                elif odd_count == 2:
                    pattern = '2odd1even'
                elif odd_count == 1:
                    pattern = '1odd2even'
                else:
                    pattern = '3even'
                historical_parity[pattern] += 1
        
        # 计算奇偶趋势
        for pattern in parity_patterns:
            recent_freq = recent_parity[pattern] / total_recent if total_recent > 0 else 0
            historical_freq = historical_parity[pattern] / total_historical if total_historical > 0 else 0
            
            combination_trends['parity_trends'][pattern] = {
                'recent_frequency': recent_freq,
                'historical_frequency': historical_freq,
                'trend_strength': recent_freq - historical_freq
            }
        
        # 分析大小趋势（类似奇偶分析）
        size_patterns = ['3big', '2big1small', '1big2small', '3small']
        recent_size = Counter()
        historical_size = Counter()
        
        for record in recent_window:
            if len(record['numbers']) == 3:
                digits = [int(d) for d in record['numbers']]
                big_count = sum(1 for d in digits if d >= 5)
                if big_count == 3:
                    pattern = '3big'
                elif big_count == 2:
                    pattern = '2big1small'
                elif big_count == 1:
                    pattern = '1big2small'
                else:
                    pattern = '3small'
                recent_size[pattern] += 1
        
        for record in historical_window:
            if len(record['numbers']) == 3:
                digits = [int(d) for d in record['numbers']]
                big_count = sum(1 for d in digits if d >= 5)
                if big_count == 3:
                    pattern = '3big'
                elif big_count == 2:
                    pattern = '2big1small'
                elif big_count == 1:
                    pattern = '1big2small'
                else:
                    pattern = '3small'
                historical_size[pattern] += 1
        
        # 计算大小趋势
        for pattern in size_patterns:
            recent_freq = recent_size[pattern] / total_recent if total_recent > 0 else 0
            historical_freq = historical_size[pattern] / total_historical if total_historical > 0 else 0
            
            combination_trends['size_trends'][pattern] = {
                'recent_frequency': recent_freq,
                'historical_frequency': historical_freq,
                'trend_strength': recent_freq - historical_freq
            }
        
        return combination_trends

    def _calculate_period_impact_weight(self, records: List[Dict]) -> float:
        """
        计算期号差异影响权重

        Args:
            records: 开奖记录列表

        Returns:
            期号影响权重 (0.8-1.2)
        """
        if not records or len(records) < 2:
            return 1.0

        # 获取最新两期的期号
        latest_periods = []
        for record in records[:2]:
            if 'period' in record:
                try:
                    period_num = int(record['period'])
                    latest_periods.append(period_num)
                except (ValueError, TypeError):
                    continue

        if len(latest_periods) < 2:
            return 1.0

        # 计算期号差异
        period_diff = abs(latest_periods[0] - latest_periods[1])

        # 基于期号差异计算影响权重
        if period_diff == 1:  # 连续期号
            impact_weight = 1.1  # 增强权重
        elif period_diff <= 3:  # 近期期号
            impact_weight = 1.05
        else:  # 较远期号
            impact_weight = 0.95

        return impact_weight

    def predict_next_trends(self, records: List[Dict]) -> Dict[str, Any]:
        """
        基于趋势分析预测下期趋势
        
        Args:
            records: 开奖记录列表
            
        Returns:
            下期趋势预测结果
        """
        if len(records) < self.window_size:
            return {}
        
        # 获取各种趋势分析结果
        digit_trends = self.analyze_digit_frequency_trends(records)
        position_trends = self.analyze_position_trends(records)
        combination_trends = self.analyze_combination_trends(records)
        
        predictions = {
            'hot_digits': [],  # 热号推荐
            'cold_rebound_digits': [],  # 冷号回补推荐
            'position_recommendations': {},  # 位置推荐
            'form_prediction': {},  # 形态预测
            'confidence_scores': {}  # 置信度分数
        }
        
        # 基于数字趋势生成推荐
        import random
        import time

        # 使用当前时间增加动态性
        time_factor = (int(time.time()) % 100) / 100.0  # 0-1之间的时间因子

        # 计算期号差异影响权重
        period_impact_weight = self._calculate_period_impact_weight(records)

        for digit, trend_info in digit_trends.items():
            if trend_info['heat_level'] == 'hot' and trend_info['trend_direction'] == 'rising':
                # 添加时间因子、随机性和期号影响权重
                base_confidence = trend_info['recent_frequency'] * (1.5 + time_factor * 0.5)
                random_factor = 0.9 + (random.random() * 0.2)  # 0.9-1.1之间
                final_confidence = base_confidence * random_factor * period_impact_weight
                predictions['hot_digits'].append({
                    'digit': digit,
                    'confidence': final_confidence,
                    'reason': 'hot_continue',
                    'period_impact': period_impact_weight
                })
            elif (trend_info['heat_level'] == 'cold' and
                  trend_info['last_appearance'] > 5):
                # 添加动态性到冷号回补
                base_confidence = (trend_info['last_appearance'] - 5) * (0.08 + time_factor * 0.04)
                random_factor = 0.9 + (random.random() * 0.2)
                final_confidence = base_confidence * random_factor * period_impact_weight
                predictions['cold_rebound_digits'].append({
                    'digit': digit,
                    'confidence': final_confidence,
                    'reason': 'cold_rebound',
                    'period_impact': period_impact_weight
                })
        
        # 基于位置趋势生成推荐
        for position, pos_trends in position_trends.items():
            position_recs = []
            for digit, digit_trend in pos_trends['digit_trends'].items():
                if digit_trend['recommendation'] in ['hot_continue', 'cold_rebound']:
                    position_recs.append({
                        'digit': digit,
                        'confidence': abs(digit_trend['trend_strength']) * 5,
                        'strategy': digit_trend['recommendation']
                    })
            
            predictions['position_recommendations'][position] = sorted(
                position_recs, key=lambda x: x['confidence'], reverse=True
            )[:3]  # 取前3个推荐
        
        # 基于组合趋势生成形态预测
        form_scores = {}
        for form, trend_info in combination_trends['form_trends'].items():
            if trend_info['trend_direction'] == 'rising':
                form_scores[form] = trend_info['recent_frequency'] * 1.5
            elif trend_info['trend_direction'] == 'falling':
                form_scores[form] = trend_info['recent_frequency'] * 0.5
            else:
                form_scores[form] = trend_info['recent_frequency']
        
        if form_scores:
            best_form = max(form_scores.items(), key=lambda x: x[1])
            predictions['form_prediction'] = {
                'recommended_form': best_form[0],
                'confidence': best_form[1],
                'all_scores': form_scores
            }
        
        # 计算整体置信度
        predictions['confidence_scores'] = {
            'digit_trend_confidence': len(predictions['hot_digits'] + predictions['cold_rebound_digits']) / 10,
            'position_trend_confidence': sum(len(recs) for recs in predictions['position_recommendations'].values()) / 9,
            'form_trend_confidence': predictions['form_prediction'].get('confidence', 0) if predictions['form_prediction'] else 0
        }
        
        return predictions
    
    def train_model(self) -> Dict[str, Any]:
        """
        训练短期趋势捕捉模型
        
        Returns:
            训练结果和模型性能
        """
        print("开始训练短期趋势捕捉模型...")
        
        # 加载数据
        records = self.load_recent_data(limit=500)  # 加载最近500期数据
        print(f"加载了 {len(records)} 条开奖记录")
        
        if len(records) < self.window_size * 2:
            raise ValueError(f"数据不足，至少需要 {self.window_size * 2} 条记录")
        
        # 执行各种趋势分析
        print("分析数字频率趋势...")
        digit_trends = self.analyze_digit_frequency_trends(records)
        
        print("分析位置趋势...")
        position_trends = self.analyze_position_trends(records)
        
        print("分析组合趋势...")
        combination_trends = self.analyze_combination_trends(records)
        
        print("生成趋势预测...")
        trend_predictions = self.predict_next_trends(records)
        
        # 保存分析结果
        self.trend_patterns = {
            'digit_trends': digit_trends,
            'position_trends': position_trends,
            'combination_trends': combination_trends,
            'trend_predictions': trend_predictions,
            'data_summary': {
                'total_records': len(records),
                'analysis_window': self.window_size,
                'date_range': (records[0]['date'], records[-1]['date']) if records else None
            }
        }
        
        print("短期趋势捕捉模型训练完成!")
        return {
            'success': True,
            'trend_patterns': self.trend_patterns
        }


if __name__ == "__main__":
    # 测试代码
    analyzer = TrendAnalyzer(window_size=30)
    
    try:
        # 训练模型
        result = analyzer.train_model()
        print("训练结果:", result['success'])
        
        if result['success']:
            # 显示一些趋势信息
            trends = result['trend_patterns']
            print("\n数字趋势摘要:")
            for digit, info in trends['digit_trends'].items():
                if info['heat_level'] != 'warm':
                    print(f"  数字{digit}: {info['heat_level']} ({info['trend_direction']})")
            
            print("\n形态趋势摘要:")
            for form, info in trends['combination_trends']['form_trends'].items():
                print(f"  {form}: {info['recent_frequency']:.3f} ({info['trend_direction']})")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
