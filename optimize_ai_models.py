#!/usr/bin/env python3
"""
AI模型优化脚本
解决AI模型加载问题，实现离线模式和模型缓存优化
"""

import os
import sys
import logging
from pathlib import Path
import requests
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AIModelOptimizer:
    """AI模型优化器"""
    
    def __init__(self):
        self.models_dir = Path("./models")
        self.cache_dir = self.models_dir / "cache"
        self.sentence_transformer_dir = self.models_dir / "sentence-transformers"
        
        # 创建目录
        self.models_dir.mkdir(exist_ok=True)
        self.cache_dir.mkdir(exist_ok=True)
        self.sentence_transformer_dir.mkdir(exist_ok=True)
        
        # 模型配置
        self.models_config = {
            'sentence_transformer': {
                'name': 'all-MiniLM-L6-v2',
                'url': 'https://huggingface.co/sentence-transformers/all-MiniLM-L6-v2',
                'local_path': self.sentence_transformer_dir / 'all-MiniLM-L6-v2'
            }
        }
    
    def analyze_model_issues(self) -> dict:
        """分析AI模型问题"""
        logger.info("🔍 分析AI模型加载问题...")
        
        issues = {
            'network_connectivity': False,
            'model_cache_exists': False,
            'transformers_available': False,
            'sentence_transformers_available': False,
            'offline_mode_configured': False
        }
        
        # 检查网络连接
        try:
            response = requests.get('https://huggingface.co', timeout=10)
            issues['network_connectivity'] = response.status_code == 200
            logger.info(f"🌐 网络连接: {'正常' if issues['network_connectivity'] else '异常'}")
        except Exception as e:
            logger.warning(f"⚠️ 网络连接检查失败: {e}")
        
        # 检查模型缓存
        cache_exists = any(self.cache_dir.iterdir()) if self.cache_dir.exists() else False
        issues['model_cache_exists'] = cache_exists
        logger.info(f"💾 模型缓存: {'存在' if cache_exists else '不存在'}")
        
        # 检查库可用性
        try:
            import transformers
            issues['transformers_available'] = True
            logger.info(f"✅ transformers库: 可用 (版本 {transformers.__version__})")
        except ImportError:
            logger.warning("❌ transformers库: 不可用")
        
        try:
            import sentence_transformers
            issues['sentence_transformers_available'] = True
            logger.info(f"✅ sentence-transformers库: 可用")
        except ImportError:
            logger.warning("❌ sentence-transformers库: 不可用")
        
        # 检查离线模式配置
        offline_vars = ['TRANSFORMERS_OFFLINE', 'HF_DATASETS_OFFLINE']
        offline_configured = all(os.environ.get(var) == '1' for var in offline_vars)
        issues['offline_mode_configured'] = offline_configured
        logger.info(f"🔒 离线模式: {'已配置' if offline_configured else '未配置'}")
        
        return issues
    
    def configure_offline_mode(self) -> bool:
        """配置离线模式"""
        logger.info("🔒 配置AI模型离线模式...")
        
        try:
            # 设置环境变量
            offline_env_vars = {
                'TRANSFORMERS_OFFLINE': '1',
                'HF_DATASETS_OFFLINE': '1',
                'TRANSFORMERS_CACHE': str(self.cache_dir),
                'HF_HOME': str(self.cache_dir),
                'SENTENCE_TRANSFORMERS_HOME': str(self.sentence_transformer_dir)
            }
            
            for var, value in offline_env_vars.items():
                os.environ[var] = value
                logger.info(f"✅ 设置环境变量: {var}={value}")
            
            # 创建配置文件
            config_content = f"""# AI模型离线配置
# 生成时间: {datetime.now().isoformat()}

import os

# 设置离线模式
os.environ['TRANSFORMERS_OFFLINE'] = '1'
os.environ['HF_DATASETS_OFFLINE'] = '1'
os.environ['TRANSFORMERS_CACHE'] = '{self.cache_dir}'
os.environ['HF_HOME'] = '{self.cache_dir}'
os.environ['SENTENCE_TRANSFORMERS_HOME'] = '{self.sentence_transformer_dir}'

# 模型路径配置
MODEL_CACHE_DIR = '{self.cache_dir}'
SENTENCE_TRANSFORMER_DIR = '{self.sentence_transformer_dir}'
OFFLINE_MODE = True
"""
            
            config_file = self.models_dir / 'offline_config.py'
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(config_content)
            
            logger.info(f"📝 离线配置文件已创建: {config_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 离线模式配置失败: {e}")
            return False
    
    def download_models(self) -> bool:
        """下载和缓存模型"""
        logger.info("📥 开始下载和缓存AI模型...")
        
        try:
            # 下载sentence-transformers模型
            logger.info("📝 下载Sentence Transformers模型...")
            
            try:
                from sentence_transformers import SentenceTransformer
                
                model_name = self.models_config['sentence_transformer']['name']
                local_path = self.models_config['sentence_transformer']['local_path']
                
                # 尝试下载模型
                model = SentenceTransformer(model_name)
                
                # 保存到本地
                model.save(str(local_path))
                logger.info(f"✅ 模型已保存到: {local_path}")
                
                # 测试本地加载
                local_model = SentenceTransformer(str(local_path))
                test_embedding = local_model.encode(["测试文本"])
                logger.info(f"✅ 本地模型测试成功，嵌入维度: {test_embedding.shape}")
                
                return True
                
            except Exception as e:
                logger.warning(f"⚠️ Sentence Transformers模型下载失败: {e}")
                
                # 尝试使用轻量级替代方案
                logger.info("🔄 尝试使用轻量级替代方案...")
                return self._setup_lightweight_models()
                
        except Exception as e:
            logger.error(f"❌ 模型下载失败: {e}")
            return False
    
    def _setup_lightweight_models(self) -> bool:
        """设置轻量级模型替代方案"""
        try:
            logger.info("🪶 设置轻量级AI模型...")
            
            # 创建简化的模型配置
            lightweight_config = {
                'model_type': 'lightweight',
                'use_tfidf': True,
                'use_simple_similarity': True,
                'fallback_to_rules': True
            }
            
            config_file = self.models_dir / 'lightweight_config.json'
            import json
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(lightweight_config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ 轻量级配置已创建: {config_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 轻量级模型设置失败: {e}")
            return False
    
    def optimize_model_loading(self) -> bool:
        """优化模型加载逻辑"""
        logger.info("⚡ 优化AI模型加载逻辑...")
        
        try:
            # 创建优化的模型加载器
            loader_code = f'''#!/usr/bin/env python3
"""
优化的AI模型加载器
实现懒加载和缓存机制
"""

import os
import logging
from pathlib import Path
from typing import Optional, Any

logger = logging.getLogger(__name__)

class OptimizedModelLoader:
    """优化的模型加载器"""
    
    def __init__(self):
        self.models_cache = {{}}
        self.models_dir = Path("{self.models_dir}")
        self.cache_dir = Path("{self.cache_dir}")
        
        # 设置离线模式
        self._configure_offline_mode()
    
    def _configure_offline_mode(self):
        """配置离线模式"""
        os.environ['TRANSFORMERS_OFFLINE'] = '1'
        os.environ['HF_DATASETS_OFFLINE'] = '1'
        os.environ['TRANSFORMERS_CACHE'] = str(self.cache_dir)
        os.environ['HF_HOME'] = str(self.cache_dir)
    
    def load_sentence_transformer(self, model_name: str = 'all-MiniLM-L6-v2') -> Optional[Any]:
        """懒加载Sentence Transformer模型"""
        if model_name in self.models_cache:
            return self.models_cache[model_name]
        
        try:
            # 尝试从本地加载
            local_path = self.models_dir / "sentence-transformers" / model_name
            if local_path.exists():
                from sentence_transformers import SentenceTransformer
                model = SentenceTransformer(str(local_path))
                self.models_cache[model_name] = model
                logger.info(f"✅ 从本地加载模型: {{model_name}}")
                return model
            
            # 尝试在线加载
            from sentence_transformers import SentenceTransformer
            model = SentenceTransformer(model_name)
            self.models_cache[model_name] = model
            logger.info(f"✅ 在线加载模型: {{model_name}}")
            return model
            
        except Exception as e:
            logger.warning(f"⚠️ 模型加载失败: {{e}}")
            return None
    
    def get_tfidf_vectorizer(self):
        """获取TF-IDF向量化器"""
        if 'tfidf' not in self.models_cache:
            try:
                from sklearn.feature_extraction.text import TfidfVectorizer
                vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
                self.models_cache['tfidf'] = vectorizer
                logger.info("✅ TF-IDF向量化器已加载")
            except Exception as e:
                logger.error(f"❌ TF-IDF向量化器加载失败: {{e}}")
                return None
        
        return self.models_cache.get('tfidf')

# 全局模型加载器实例
_model_loader = None

def get_model_loader():
    """获取全局模型加载器实例"""
    global _model_loader
    if _model_loader is None:
        _model_loader = OptimizedModelLoader()
    return _model_loader
'''
            
            loader_file = self.models_dir / 'optimized_loader.py'
            with open(loader_file, 'w', encoding='utf-8') as f:
                f.write(loader_code)
            
            logger.info(f"✅ 优化加载器已创建: {loader_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 模型加载优化失败: {e}")
            return False
    
    def test_ai_functionality(self) -> bool:
        """测试AI功能"""
        logger.info("🧪 测试优化后的AI功能...")
        
        try:
            # 添加模型目录到Python路径
            sys.path.insert(0, str(self.models_dir))
            
            # 导入优化的加载器
            from optimized_loader import get_model_loader
            
            loader = get_model_loader()
            
            # 测试TF-IDF
            tfidf = loader.get_tfidf_vectorizer()
            if tfidf:
                test_texts = ["这是一个测试错误", "另一个测试错误"]
                try:
                    tfidf_matrix = tfidf.fit_transform(test_texts)
                    logger.info(f"✅ TF-IDF测试成功: {tfidf_matrix.shape}")
                except Exception as e:
                    logger.warning(f"⚠️ TF-IDF测试失败: {e}")
            
            # 测试Sentence Transformer
            st_model = loader.load_sentence_transformer()
            if st_model:
                try:
                    embeddings = st_model.encode(["测试文本"])
                    logger.info(f"✅ Sentence Transformer测试成功: {embeddings.shape}")
                except Exception as e:
                    logger.warning(f"⚠️ Sentence Transformer测试失败: {e}")
            
            logger.info("🎉 AI功能测试完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ AI功能测试失败: {e}")
            return False
    
    def optimize_models(self) -> bool:
        """执行完整的AI模型优化流程"""
        logger.info("🚀 开始AI模型优化...")
        logger.info("=" * 60)
        
        # 1. 分析问题
        issues = self.analyze_model_issues()
        logger.info(f"📊 问题分析完成: {sum(issues.values())}/{len(issues)} 项正常")
        
        # 2. 配置离线模式
        if not self.configure_offline_mode():
            logger.error("❌ 离线模式配置失败")
            return False
        
        # 3. 下载模型
        if not self.download_models():
            logger.warning("⚠️ 模型下载失败，将使用轻量级方案")
        
        # 4. 优化加载逻辑
        if not self.optimize_model_loading():
            logger.error("❌ 模型加载优化失败")
            return False
        
        # 5. 测试功能
        if not self.test_ai_functionality():
            logger.warning("⚠️ AI功能测试部分失败")
        
        logger.info("=" * 60)
        logger.info("🎉 AI模型优化完成！")
        return True

def main():
    """主函数"""
    logger.info("🤖 AI模型优化工具")
    logger.info("=" * 60)
    
    optimizer = AIModelOptimizer()
    
    success = optimizer.optimize_models()
    
    if success:
        print("\\n🚀 下一步:")
        print("1. 重启API和Streamlit服务")
        print("2. 验证AI错误分类功能")
        print("3. 测试相似度分析功能")
        print("4. 检查模型加载性能")
    else:
        print("\\n❌ AI模型优化失败")
        print("系统将继续使用规则方法作为回退方案")
    
    return success

if __name__ == "__main__":
    main()
