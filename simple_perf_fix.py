#!/usr/bin/env python3
"""
简化的性能优化脚本
"""

import os
import psutil
from pathlib import Path

def optimize_performance():
    """优化系统性能"""
    print("⚡ 开始系统性能优化...")
    
    # 创建优化目录
    opt_dir = Path("optimizations")
    opt_dir.mkdir(exist_ok=True)
    
    print(f"📁 创建优化目录: {opt_dir}")
    
    # 检查当前性能
    process = psutil.Process()
    memory_mb = process.memory_info().rss / 1024 / 1024
    cpu_percent = process.cpu_percent()
    
    print(f"📊 当前性能指标:")
    print(f"   内存使用: {memory_mb:.1f}MB")
    print(f"   CPU使用: {cpu_percent:.1f}%")
    
    # 创建单例模式实现
    singleton_code = '''#!/usr/bin/env python3
"""
单例模式基类
"""

import threading

class SingletonMeta(type):
    """单例元类"""
    _instances = {}
    _lock = threading.Lock()
    
    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            with cls._lock:
                if cls not in cls._instances:
                    instance = super().__call__(*args, **kwargs)
                    cls._instances[cls] = instance
        return cls._instances[cls]

class Singleton(metaclass=SingletonMeta):
    """单例基类"""
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        self._initialized = True
        self._setup()
    
    def _setup(self):
        """子类重写此方法进行初始化"""
        pass
'''
    
    singleton_file = opt_dir / "singleton_pattern.py"
    with open(singleton_file, 'w', encoding='utf-8') as f:
        f.write(singleton_code)
    
    print(f"✅ 单例模式实现已创建: {singleton_file}")
    
    # 创建连接池优化
    pool_code = '''#!/usr/bin/env python3
"""
数据库连接池优化
"""

import sqlite3
import threading
from queue import Queue, Empty
from contextlib import contextmanager

class ConnectionPool:
    """数据库连接池"""
    
    def __init__(self, db_path, max_connections=10):
        self.db_path = db_path
        self.max_connections = max_connections
        self.pool = Queue(maxsize=max_connections)
        self.current_connections = 0
        self.lock = threading.Lock()
        
        # 预创建连接
        for _ in range(min(3, max_connections)):
            conn = self._create_connection()
            if conn:
                self.pool.put(conn)
    
    def _create_connection(self):
        """创建新连接"""
        try:
            conn = sqlite3.connect(self.db_path, check_same_thread=False)
            with self.lock:
                self.current_connections += 1
            return conn
        except Exception:
            return None
    
    @contextmanager
    def get_connection(self):
        """获取连接"""
        conn = None
        try:
            try:
                conn = self.pool.get(timeout=5.0)
            except Empty:
                if self.current_connections < self.max_connections:
                    conn = self._create_connection()
                else:
                    conn = self.pool.get(timeout=30.0)
            
            yield conn
            
        finally:
            if conn:
                try:
                    self.pool.put_nowait(conn)
                except:
                    conn.close()
                    with self.lock:
                        self.current_connections -= 1

# 全局连接池
_pools = {}

def get_pool(db_path):
    """获取连接池"""
    if db_path not in _pools:
        _pools[db_path] = ConnectionPool(db_path)
    return _pools[db_path]
'''
    
    pool_file = opt_dir / "connection_pool.py"
    with open(pool_file, 'w', encoding='utf-8') as f:
        f.write(pool_code)
    
    print(f"✅ 连接池优化已创建: {pool_file}")
    
    # 创建性能监控
    monitor_code = '''#!/usr/bin/env python3
"""
性能监控模块
"""

import psutil
import time
import threading
from datetime import datetime

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = []
        self.monitoring = False
        self.thread = None
    
    def start(self):
        """开始监控"""
        if not self.monitoring:
            self.monitoring = True
            self.thread = threading.Thread(target=self._monitor, daemon=True)
            self.thread.start()
    
    def stop(self):
        """停止监控"""
        self.monitoring = False
    
    def _monitor(self):
        """监控循环"""
        while self.monitoring:
            try:
                process = psutil.Process()
                metric = {
                    'timestamp': datetime.now(),
                    'memory_mb': process.memory_info().rss / 1024 / 1024,
                    'cpu_percent': process.cpu_percent()
                }
                self.metrics.append(metric)
                
                # 保留最近100个数据点
                if len(self.metrics) > 100:
                    self.metrics.pop(0)
                
                time.sleep(30)  # 30秒间隔
            except Exception:
                pass
    
    def get_current_metrics(self):
        """获取当前指标"""
        if self.metrics:
            return self.metrics[-1]
        return {}

# 全局监控实例
_monitor = PerformanceMonitor()
_monitor.start()

def get_monitor():
    """获取监控实例"""
    return _monitor
'''
    
    monitor_file = opt_dir / "performance_monitor.py"
    with open(monitor_file, 'w', encoding='utf-8') as f:
        f.write(monitor_code)
    
    print(f"✅ 性能监控已创建: {monitor_file}")
    
    # 创建性能指标文件
    metrics = {
        'timestamp': str(process.creation_time()),
        'memory_mb': memory_mb,
        'cpu_percent': cpu_percent,
        'optimization_applied': True
    }
    
    import json
    metrics_file = opt_dir / "performance_metrics.json"
    with open(metrics_file, 'w', encoding='utf-8') as f:
        json.dump(metrics, f, indent=2)
    
    print(f"📊 性能指标已保存: {metrics_file}")
    
    print("🎉 系统性能优化完成！")
    return True

if __name__ == "__main__":
    optimize_performance()
