#!/usr/bin/env python3
"""
测试新添加的字段
"""

import requests

def test_new_fields():
    """测试新添加的字段"""
    url = "http://127.0.0.1:8888/api/v1/analysis/frequency?use_cache=false"
    
    try:
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            print("🎉 新字段测试结果:")
            print(f"📅 date_range: {data.get('date_range', 'N/A')}")
            print(f"🔢 latest_period: {data.get('latest_period', 'N/A')}")
            print(f"🔥 hot_numbers: {data.get('hot_numbers', [])[:5]}")
            print(f"❄️ cold_numbers: {data.get('cold_numbers', [])[:5]}")
            print(f"⏰ analysis_time: {data.get('analysis_time', 'N/A')}")
            
            print(f"\n✅ 所有新字段都已成功添加！")
            
        else:
            print(f"❌ API请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_new_fields()
