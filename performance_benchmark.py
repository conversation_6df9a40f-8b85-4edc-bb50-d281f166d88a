#!/usr/bin/env python3
"""
福彩3D预测系统性能基准测试
测试系统各项性能指标，生成详细的性能报告
"""

import time
import requests
import statistics
import concurrent.futures
from datetime import datetime
from typing import Dict, List, Any
import json


class PerformanceBenchmark:
    """性能基准测试类"""
    
    def __init__(self):
        self.api_base_url = "http://127.0.0.1:8888"
        self.streamlit_url = "http://127.0.0.1:8501"
        self.results = {}
        
    def test_api_response_time(self) -> Dict[str, Any]:
        """测试API响应时间"""
        print("🔍 测试API响应时间...")
        
        endpoints = [
            "/health",
            "/api/v1/stats/basic",
            "/api/v1/data/status",
            "/api/v1/data/update/status",
            "/api/v1/analysis/frequency"
        ]
        
        results = {}
        
        for endpoint in endpoints:
            print(f"  测试端点: {endpoint}")
            response_times = []
            
            # 进行10次测试
            for i in range(10):
                try:
                    start_time = time.time()
                    response = requests.get(f"{self.api_base_url}{endpoint}", timeout=10)
                    end_time = time.time()
                    
                    if response.status_code == 200:
                        response_times.append((end_time - start_time) * 1000)  # 转换为毫秒
                    else:
                        print(f"    警告: 请求失败，状态码: {response.status_code}")
                        
                except Exception as e:
                    print(f"    错误: {e}")
                    
                time.sleep(0.1)  # 短暂延迟
            
            if response_times:
                results[endpoint] = {
                    "avg_ms": round(statistics.mean(response_times), 2),
                    "min_ms": round(min(response_times), 2),
                    "max_ms": round(max(response_times), 2),
                    "median_ms": round(statistics.median(response_times), 2),
                    "success_rate": len(response_times) / 10 * 100
                }
                print(f"    平均响应时间: {results[endpoint]['avg_ms']}ms")
            else:
                results[endpoint] = {"error": "所有请求都失败了"}
        
        return results
    
    def test_concurrent_requests(self) -> Dict[str, Any]:
        """测试并发请求性能"""
        print("🚀 测试并发请求性能...")
        
        def make_request():
            try:
                start_time = time.time()
                response = requests.get(f"{self.api_base_url}/health", timeout=5)
                end_time = time.time()
                return {
                    "success": response.status_code == 200,
                    "response_time": (end_time - start_time) * 1000
                }
            except Exception:
                return {"success": False, "response_time": None}
        
        concurrent_levels = [1, 5, 10, 20]
        results = {}
        
        for level in concurrent_levels:
            print(f"  测试并发级别: {level}")
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=level) as executor:
                start_time = time.time()
                futures = [executor.submit(make_request) for _ in range(level * 5)]
                responses = [future.result() for future in concurrent.futures.as_completed(futures)]
                end_time = time.time()
            
            successful_responses = [r for r in responses if r["success"]]
            response_times = [r["response_time"] for r in successful_responses if r["response_time"]]
            
            results[f"concurrent_{level}"] = {
                "total_requests": len(responses),
                "successful_requests": len(successful_responses),
                "success_rate": len(successful_responses) / len(responses) * 100,
                "total_time_s": round(end_time - start_time, 2),
                "avg_response_time_ms": round(statistics.mean(response_times), 2) if response_times else 0,
                "requests_per_second": round(len(successful_responses) / (end_time - start_time), 2)
            }
            
            print(f"    成功率: {results[f'concurrent_{level}']['success_rate']:.1f}%")
            print(f"    RPS: {results[f'concurrent_{level}']['requests_per_second']}")
        
        return results
    
    def test_data_processing_performance(self) -> Dict[str, Any]:
        """测试数据处理性能"""
        print("📊 测试数据处理性能...")
        
        results = {}
        
        # 测试基础统计API
        try:
            start_time = time.time()
            response = requests.get(f"{self.api_base_url}/api/v1/stats/basic", timeout=15)
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                results["basic_stats"] = {
                    "processing_time_ms": round((end_time - start_time) * 1000, 2),
                    "data_size": len(json.dumps(data)),
                    "total_records": data.get("total_records", 0)
                }
                print(f"  基础统计处理时间: {results['basic_stats']['processing_time_ms']}ms")
            else:
                results["basic_stats"] = {"error": f"请求失败: {response.status_code}"}
                
        except Exception as e:
            results["basic_stats"] = {"error": str(e)}
        
        # 测试频率分析API
        try:
            start_time = time.time()
            response = requests.get(f"{self.api_base_url}/api/v1/analysis/frequency", timeout=15)
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                results["frequency_analysis"] = {
                    "processing_time_ms": round((end_time - start_time) * 1000, 2),
                    "data_size": len(json.dumps(data))
                }
                print(f"  频率分析处理时间: {results['frequency_analysis']['processing_time_ms']}ms")
            else:
                results["frequency_analysis"] = {"error": f"请求失败: {response.status_code}"}
                
        except Exception as e:
            results["frequency_analysis"] = {"error": str(e)}
        
        return results
    
    def test_streamlit_accessibility(self) -> Dict[str, Any]:
        """测试Streamlit可访问性"""
        print("🌐 测试Streamlit可访问性...")
        
        try:
            start_time = time.time()
            response = requests.get(self.streamlit_url, timeout=10)
            end_time = time.time()
            
            return {
                "accessible": response.status_code == 200,
                "response_time_ms": round((end_time - start_time) * 1000, 2),
                "status_code": response.status_code,
                "content_length": len(response.content)
            }
            
        except Exception as e:
            return {
                "accessible": False,
                "error": str(e)
            }
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有性能测试"""
        print("🎯 开始性能基准测试...")
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)
        
        # 运行各项测试
        self.results["api_response_time"] = self.test_api_response_time()
        print()
        
        self.results["concurrent_requests"] = self.test_concurrent_requests()
        print()
        
        self.results["data_processing"] = self.test_data_processing_performance()
        print()
        
        self.results["streamlit_accessibility"] = self.test_streamlit_accessibility()
        print()
        
        # 添加测试元数据
        self.results["test_metadata"] = {
            "test_time": datetime.now().isoformat(),
            "api_base_url": self.api_base_url,
            "streamlit_url": self.streamlit_url
        }
        
        return self.results
    
    def generate_report(self) -> str:
        """生成性能测试报告"""
        if not self.results:
            return "没有测试结果可用"
        
        report = []
        report.append("# 🚀 福彩3D预测系统性能基准测试报告")
        report.append("")
        report.append(f"**测试时间**: {self.results['test_metadata']['test_time']}")
        report.append(f"**API地址**: {self.results['test_metadata']['api_base_url']}")
        report.append(f"**Web地址**: {self.results['test_metadata']['streamlit_url']}")
        report.append("")
        
        # API响应时间报告
        report.append("## 📊 API响应时间测试")
        report.append("")
        api_results = self.results.get("api_response_time", {})
        for endpoint, data in api_results.items():
            if "error" not in data:
                report.append(f"### {endpoint}")
                report.append(f"- 平均响应时间: {data['avg_ms']}ms")
                report.append(f"- 最小响应时间: {data['min_ms']}ms")
                report.append(f"- 最大响应时间: {data['max_ms']}ms")
                report.append(f"- 中位数响应时间: {data['median_ms']}ms")
                report.append(f"- 成功率: {data['success_rate']}%")
                report.append("")
        
        # 并发请求报告
        report.append("## 🚀 并发请求性能测试")
        report.append("")
        concurrent_results = self.results.get("concurrent_requests", {})
        for level, data in concurrent_results.items():
            report.append(f"### {level.replace('_', ' ').title()}")
            report.append(f"- 总请求数: {data['total_requests']}")
            report.append(f"- 成功请求数: {data['successful_requests']}")
            report.append(f"- 成功率: {data['success_rate']:.1f}%")
            report.append(f"- 总耗时: {data['total_time_s']}秒")
            report.append(f"- 平均响应时间: {data['avg_response_time_ms']}ms")
            report.append(f"- 每秒请求数: {data['requests_per_second']} RPS")
            report.append("")
        
        # 数据处理性能报告
        report.append("## 📈 数据处理性能测试")
        report.append("")
        data_results = self.results.get("data_processing", {})
        for test_name, data in data_results.items():
            if "error" not in data:
                report.append(f"### {test_name.replace('_', ' ').title()}")
                report.append(f"- 处理时间: {data['processing_time_ms']}ms")
                report.append(f"- 数据大小: {data['data_size']} bytes")
                if "total_records" in data:
                    report.append(f"- 记录总数: {data['total_records']}")
                report.append("")
        
        # Streamlit可访问性报告
        report.append("## 🌐 Streamlit可访问性测试")
        report.append("")
        streamlit_results = self.results.get("streamlit_accessibility", {})
        if streamlit_results.get("accessible"):
            report.append("- ✅ Streamlit服务可访问")
            report.append(f"- 响应时间: {streamlit_results['response_time_ms']}ms")
            report.append(f"- 状态码: {streamlit_results['status_code']}")
            report.append(f"- 内容长度: {streamlit_results['content_length']} bytes")
        else:
            report.append("- ❌ Streamlit服务不可访问")
            if "error" in streamlit_results:
                report.append(f"- 错误: {streamlit_results['error']}")
        
        return "\n".join(report)


def main():
    """主函数"""
    benchmark = PerformanceBenchmark()
    
    # 运行所有测试
    results = benchmark.run_all_tests()
    
    # 生成报告
    report = benchmark.generate_report()
    
    # 保存报告到文件
    with open("performance_benchmark_report.md", "w", encoding="utf-8") as f:
        f.write(report)
    
    # 保存原始数据
    with open("performance_benchmark_data.json", "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print("=" * 50)
    print("✅ 性能基准测试完成!")
    print("📄 报告已保存到: performance_benchmark_report.md")
    print("📊 原始数据已保存到: performance_benchmark_data.json")
    print("=" * 50)


if __name__ == "__main__":
    main()
