#!/usr/bin/env python3
"""
测试UI融合分数修复
专门测试src/ui/main.py中的apply错误修复
"""

import os
import sys

sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import numpy as np
import pandas as pd


def test_original_error():
    """测试原始错误场景"""
    print("=== 测试原始错误场景 ===")
    
    # 模拟缺少fusion_score字段的候选数据
    candidates = [
        {'numbers': '123', 'confidence': 0.75, 'strategy': 'test'},
        {'numbers': '456', 'confidence': 0.68, 'strategy': 'test'},
        {'numbers': '789', 'confidence': 0.62, 'strategy': 'test'}
    ]
    
    df_candidates = pd.DataFrame(candidates)
    print(f"创建测试DataFrame: {df_candidates.shape}")
    print("列名:", df_candidates.columns.tolist())
    
    # 测试原始的错误代码
    try:
        print("\n--- 测试原始错误代码 ---")
        # 这应该会出错
        result = df_candidates.get('fusion_score', 0).apply(lambda x: f"{x:.3f}")
        print("❌ 原始代码没有出错，这不符合预期")
        return False
    except AttributeError as e:
        if "'int' object has no attribute 'apply'" in str(e):
            print("✅ 成功重现原始错误:", str(e))
            return True
        else:
            print("❌ 出现了不同的错误:", str(e))
            return False
    except Exception as e:
        print("❌ 出现了意外错误:", str(e))
        return False

def test_fixed_version():
    """测试修复后的版本"""
    print("\n=== 测试修复后的版本 ===")
    
    # 模拟缺少fusion_score字段的候选数据
    candidates = [
        {'numbers': '123', 'confidence': 0.75, 'strategy': 'test'},
        {'numbers': '456', 'confidence': 0.68, 'strategy': 'test'},
        {'numbers': '789', 'confidence': 0.62, 'strategy': 'test'}
    ]
    
    df_candidates = pd.DataFrame(candidates)
    
    try:
        print("--- 测试修复后的代码 ---")
        # 修复后的安全代码
        if 'fusion_score' in df_candidates.columns:
            df_candidates['融合分数'] = df_candidates['fusion_score'].apply(lambda x: f"{x:.3f}")
        else:
            df_candidates['融合分数'] = "0.000"
        
        print("✅ 修复后的代码执行成功")
        print("融合分数列:", df_candidates['融合分数'].tolist())
        return True
        
    except Exception as e:
        print("❌ 修复后的代码仍然出错:", str(e))
        return False

def test_with_fusion_score():
    """测试包含fusion_score字段的情况"""
    print("\n=== 测试包含fusion_score字段的情况 ===")
    
    # 模拟包含fusion_score字段的候选数据
    candidates = [
        {'numbers': '123', 'confidence': 0.75, 'fusion_score': 0.750, 'strategy': 'test'},
        {'numbers': '456', 'confidence': 0.68, 'fusion_score': 0.680, 'strategy': 'test'},
        {'numbers': '789', 'confidence': 0.62, 'fusion_score': 0.620, 'strategy': 'test'}
    ]
    
    df_candidates = pd.DataFrame(candidates)
    
    try:
        print("--- 测试有fusion_score字段的情况 ---")
        # 修复后的安全代码
        if 'fusion_score' in df_candidates.columns:
            df_candidates['融合分数'] = df_candidates['fusion_score'].apply(lambda x: f"{x:.3f}")
        else:
            df_candidates['融合分数'] = "0.000"
        
        print("✅ 有fusion_score字段时代码执行成功")
        print("融合分数列:", df_candidates['融合分数'].tolist())
        return True
        
    except Exception as e:
        print("❌ 有fusion_score字段时代码出错:", str(e))
        return False

def test_confidence_column():
    """测试confidence列的安全性"""
    print("\n=== 测试confidence列的安全性 ===")
    
    # 测试confidence列是否存在类似问题
    candidates = [
        {'numbers': '123', 'confidence': 0.75, 'strategy': 'test'},
        {'numbers': '456', 'confidence': 0.68, 'strategy': 'test'},
        {'numbers': '789', 'confidence': 0.62, 'strategy': 'test'}
    ]
    
    df_candidates = pd.DataFrame(candidates)
    
    try:
        print("--- 测试confidence列的apply操作 ---")
        df_candidates['置信度'] = df_candidates['confidence'].apply(lambda x: f"{x:.1%}")
        print("✅ confidence列的apply操作正常")
        print("置信度列:", df_candidates['置信度'].tolist())
        return True
        
    except Exception as e:
        print("❌ confidence列的apply操作出错:", str(e))
        return False

def test_safe_apply_column_function():
    """测试新的safe_apply_column函数"""
    print("\n=== 测试safe_apply_column函数 ===")

    try:
        # 导入修复后的函数
        sys.path.append('src')
        from ui.main import safe_apply_column

        # 测试1: 列存在的情况
        df_with_column = pd.DataFrame({
            'test_column': [1, 2, 3],
            'other_column': ['a', 'b', 'c']
        })

        result1 = safe_apply_column(df_with_column, 'test_column', lambda x: x * 2, "default")
        expected1 = pd.Series([2, 4, 6])

        if result1.equals(expected1):
            print("✅ 列存在时函数工作正常")
        else:
            print("❌ 列存在时函数工作异常")
            return False

        # 测试2: 列不存在的情况
        result2 = safe_apply_column(df_with_column, 'missing_column', lambda x: x * 2, "default")
        expected2 = pd.Series(["default", "default", "default"])

        if result2.equals(expected2):
            print("✅ 列不存在时函数返回默认值")
        else:
            print("❌ 列不存在时函数工作异常")
            return False

        return True

    except Exception as e:
        print(f"❌ safe_apply_column函数测试失败: {e}")
        return False

def test_validate_candidates_function():
    """测试候选数据验证函数"""
    print("\n=== 测试validate_candidates_data函数 ===")

    try:
        # 导入修复后的函数
        sys.path.append('src')
        from ui.main import validate_candidates_data

        # 测试1: 有效数据
        valid_candidates = [
            {'numbers': '123', 'confidence': 0.75, 'fusion_score': 0.750},
            {'numbers': '456', 'confidence': 0.68}  # 缺少fusion_score是允许的
        ]

        if validate_candidates_data(valid_candidates):
            print("✅ 有效候选数据验证通过")
        else:
            print("❌ 有效候选数据验证失败")
            return False

        # 测试2: 无效数据 - 缺少必需字段
        invalid_candidates1 = [
            {'confidence': 0.75, 'fusion_score': 0.750}  # 缺少numbers字段
        ]

        if not validate_candidates_data(invalid_candidates1):
            print("✅ 缺少必需字段时验证正确失败")
        else:
            print("❌ 缺少必需字段时验证应该失败")
            return False

        # 测试3: 无效数据 - 错误数据类型
        invalid_candidates2 = [
            {'numbers': '123', 'confidence': 'invalid_type'}  # confidence应该是数字
        ]

        if not validate_candidates_data(invalid_candidates2):
            print("✅ 错误数据类型时验证正确失败")
        else:
            print("❌ 错误数据类型时验证应该失败")
            return False

        # 测试4: 空数据
        if not validate_candidates_data([]):
            print("✅ 空数据时验证正确失败")
        else:
            print("❌ 空数据时验证应该失败")
            return False

        return True

    except Exception as e:
        print(f"❌ validate_candidates_data函数测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始UI融合分数修复测试...")

    results = []

    # 运行所有测试
    results.append(("原始错误重现", test_original_error()))
    results.append(("修复版本测试", test_fixed_version()))
    results.append(("包含fusion_score测试", test_with_fusion_score()))
    results.append(("confidence列安全性测试", test_confidence_column()))
    results.append(("safe_apply_column函数测试", test_safe_apply_column_function()))
    results.append(("validate_candidates_data函数测试", test_validate_candidates_function()))

    # 汇总结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print(f"\n总计: {passed}/{total} 个测试通过")

    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
