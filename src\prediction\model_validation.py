"""
马尔可夫模型验证模块

实现k折交叉验证、训练-测试分割、性能评估指标和AIC/BIC准则
"""

import numpy as np
import sqlite3
import os
from typing import List, Dict, Any, Tuple, Optional
from collections import Counter
import json
from datetime import datetime
import math


class MarkovCrossValidator:
    """马尔可夫模型交叉验证器"""
    
    def __init__(self, db_path: str = None):
        """
        初始化验证器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path or os.path.join('data', 'lottery.db')
        self.validation_results = {}
        
    def load_validation_data(self, limit: int = 2000) -> List[Dict[str, Any]]:
        """
        加载用于验证的数据
        
        Args:
            limit: 数据条数限制
            
        Returns:
            验证数据列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT period, date, numbers 
                    FROM lottery_records 
                    ORDER BY date DESC 
                    LIMIT ?
                """, (limit,))
                
                data = []
                for row in cursor.fetchall():
                    data.append({
                        'period': row[0],
                        'date': row[1],
                        'numbers': row[2]
                    })
                
                # 按时间顺序排序（最早的在前）
                data.reverse()
                return data
                
        except Exception as e:
            print(f"加载验证数据失败: {e}")
            return []
    
    def time_series_split(self, data: List[Dict], test_ratio: float = 0.2) -> Tuple[List[Dict], List[Dict]]:
        """
        时间序列分割，避免未来信息泄露
        
        Args:
            data: 原始数据
            test_ratio: 测试集比例
            
        Returns:
            (训练集, 测试集)
        """
        split_point = int(len(data) * (1 - test_ratio))
        train_data = data[:split_point]
        test_data = data[split_point:]
        
        return train_data, test_data
    
    def k_fold_time_series_split(self, data: List[Dict], k: int = 5) -> List[Tuple[List[Dict], List[Dict]]]:
        """
        k折时间序列交叉验证分割
        
        Args:
            data: 原始数据
            k: 折数
            
        Returns:
            k个(训练集, 验证集)元组的列表
        """
        n = len(data)
        fold_size = n // k
        folds = []
        
        for i in range(k):
            # 计算验证集的起始和结束位置
            val_start = i * fold_size
            val_end = (i + 1) * fold_size if i < k - 1 else n
            
            # 训练集是验证集之前的所有数据
            train_data = data[:val_start] if val_start > 0 else []
            val_data = data[val_start:val_end]
            
            # 确保训练集有足够的数据
            if len(train_data) >= 100:  # 至少需要100期数据
                folds.append((train_data, val_data))
        
        return folds
    
    def calculate_prediction_accuracy(self, predictions: List[str], actuals: List[str]) -> Dict[str, float]:
        """
        计算预测准确率
        
        Args:
            predictions: 预测结果列表
            actuals: 实际结果列表
            
        Returns:
            准确率指标字典
        """
        if len(predictions) != len(actuals):
            raise ValueError("预测结果和实际结果长度不匹配")
        
        total = len(predictions)
        if total == 0:
            return {'exact_match': 0.0, 'digit_accuracy': 0.0, 'position_accuracy': [0.0, 0.0, 0.0]}
        
        # 完全匹配准确率
        exact_matches = sum(1 for p, a in zip(predictions, actuals) if p == a)
        exact_match_rate = exact_matches / total
        
        # 数字准确率（预测的数字在实际结果中出现）
        digit_matches = 0
        total_digits = 0
        
        # 位置准确率
        position_matches = [0, 0, 0]
        
        for pred, actual in zip(predictions, actuals):
            if len(pred) == 3 and len(actual) == 3:
                # 数字准确率
                pred_digits = set(pred)
                actual_digits = set(actual)
                digit_matches += len(pred_digits & actual_digits)
                total_digits += 3
                
                # 位置准确率
                for i in range(3):
                    if pred[i] == actual[i]:
                        position_matches[i] += 1
        
        digit_accuracy = digit_matches / total_digits if total_digits > 0 else 0.0
        position_accuracy = [pos_match / total for pos_match in position_matches]
        
        return {
            'exact_match': exact_match_rate,
            'digit_accuracy': digit_accuracy,
            'position_accuracy': position_accuracy,
            'total_predictions': total
        }
    
    def calculate_diversity_metrics(self, predictions: List[str]) -> Dict[str, float]:
        """
        计算预测多样性指标
        
        Args:
            predictions: 预测结果列表
            
        Returns:
            多样性指标字典
        """
        if not predictions:
            return {'simpson_diversity': 0.0, 'unique_ratio': 0.0, 'entropy': 0.0}
        
        # 计算频率
        counts = Counter(predictions)
        total = len(predictions)
        
        # 辛普森多样性指数
        simpson_index = 1 - sum((count/total)**2 for count in counts.values())
        
        # 唯一预测比率
        unique_ratio = len(counts) / total
        
        # 信息熵
        entropy = -sum((count/total) * math.log2(count/total) for count in counts.values())
        
        return {
            'simpson_diversity': simpson_index,
            'unique_ratio': unique_ratio,
            'entropy': entropy,
            'unique_count': len(counts),
            'total_count': total
        }
    
    def calculate_stability_metrics(self, predictions: List[str]) -> Dict[str, float]:
        """
        计算预测稳定性指标
        
        Args:
            predictions: 预测结果列表
            
        Returns:
            稳定性指标字典
        """
        if len(predictions) < 2:
            return {'variance': 0.0, 'std_dev': 0.0, 'coefficient_of_variation': 0.0}
        
        # 将预测转换为数值
        numeric_predictions = []
        for pred in predictions:
            if len(pred) == 3 and pred.isdigit():
                numeric_predictions.append(int(pred))
        
        if not numeric_predictions:
            return {'variance': 0.0, 'std_dev': 0.0, 'coefficient_of_variation': 0.0}
        
        # 计算统计指标
        mean_val = np.mean(numeric_predictions)
        variance = np.var(numeric_predictions)
        std_dev = np.std(numeric_predictions)
        cv = std_dev / mean_val if mean_val != 0 else 0.0
        
        return {
            'variance': float(variance),
            'std_dev': float(std_dev),
            'coefficient_of_variation': float(cv),
            'mean': float(mean_val)
        }
    
    def calculate_aic_bic(self, log_likelihood: float, num_params: int, num_samples: int) -> Dict[str, float]:
        """
        计算AIC和BIC准则
        
        Args:
            log_likelihood: 对数似然值
            num_params: 参数数量
            num_samples: 样本数量
            
        Returns:
            AIC和BIC值
        """
        # AIC = 2k - 2ln(L)
        aic = 2 * num_params - 2 * log_likelihood
        
        # BIC = ln(n)k - 2ln(L)
        bic = math.log(num_samples) * num_params - 2 * log_likelihood
        
        return {
            'aic': aic,
            'bic': bic,
            'log_likelihood': log_likelihood,
            'num_params': num_params,
            'num_samples': num_samples
        }
    
    def estimate_model_likelihood(self, predictions: List[str], actuals: List[str]) -> float:
        """
        估计模型的对数似然值
        
        Args:
            predictions: 预测结果
            actuals: 实际结果
            
        Returns:
            对数似然值
        """
        if len(predictions) != len(actuals):
            return float('-inf')
        
        # 简化的似然估计：基于预测准确率
        accuracy_metrics = self.calculate_prediction_accuracy(predictions, actuals)
        exact_match_rate = accuracy_metrics['exact_match']
        
        # 避免log(0)
        if exact_match_rate == 0:
            exact_match_rate = 1e-10
        
        # 对数似然 = n * log(p)，其中p是成功概率
        log_likelihood = len(predictions) * math.log(exact_match_rate)
        
        return log_likelihood
    
    def save_validation_report(self, results: Dict[str, Any], filename: str = None) -> str:
        """
        保存验证报告
        
        Args:
            results: 验证结果
            filename: 文件名
            
        Returns:
            保存的文件路径
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"markov_validation_report_{timestamp}.json"
        
        # 确保目录存在
        report_dir = os.path.join('data', 'validation_reports')
        os.makedirs(report_dir, exist_ok=True)
        
        filepath = os.path.join(report_dir, filename)
        
        # 添加元数据
        results['metadata'] = {
            'generated_at': datetime.now().isoformat(),
            'validator_version': '1.0',
            'database_path': self.db_path
        }
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            print(f"验证报告已保存: {filepath}")
            return filepath
            
        except Exception as e:
            print(f"保存验证报告失败: {e}")
            return ""


if __name__ == "__main__":
    # 测试代码
    validator = MarkovCrossValidator()
    
    # 加载测试数据
    data = validator.load_validation_data(limit=500)
    print(f"加载了 {len(data)} 条验证数据")
    
    # 测试时间序列分割
    train_data, test_data = validator.time_series_split(data, test_ratio=0.2)
    print(f"训练集: {len(train_data)} 条，测试集: {len(test_data)} 条")
    
    # 测试k折交叉验证
    folds = validator.k_fold_time_series_split(data, k=5)
    print(f"生成了 {len(folds)} 个交叉验证折")
    
    # 测试多样性指标计算
    test_predictions = ['123', '456', '789', '123', '456', '012', '345', '678', '901', '234']
    diversity_metrics = validator.calculate_diversity_metrics(test_predictions)
    print(f"多样性指标: {diversity_metrics}")
