#!/usr/bin/env python3
"""
Bug检测系统集成验证 - 简化版
创建日期: 2025年7月24日
用途: 快速验证Bug检测系统是否成功集成
"""

import sys
import os

def main():
    """主验证函数"""
    print("🔍 Bug检测系统集成验证")
    print("="*50)
    
    success_count = 0
    total_tests = 6
    
    # 测试1: 数据库管理器
    print("\n📋 测试1: 数据库管理器")
    try:
        from src.bug_detection.core.database_manager import DatabaseManager
        db_manager = DatabaseManager()
        
        # 测试保存Bug报告
        test_bug = {
            'error_type': 'integration_test',
            'severity': 'low',
            'page_name': 'test_page',
            'error_message': '集成测试Bug报告',
            'stack_trace': 'Test stack trace'
        }
        
        bug_id = db_manager.save_bug_report(test_bug)
        print(f"✅ 数据库管理器工作正常，Bug ID: {bug_id[:12]}...")
        success_count += 1
    except Exception as e:
        print(f"❌ 数据库管理器测试失败: {e}")
    
    # 测试2: JavaScript监控器
    print("\n📋 测试2: JavaScript监控器")
    try:
        from src.bug_detection.monitoring.js_monitor import JavaScriptMonitor
        js_monitor = JavaScriptMonitor()
        session_id = js_monitor._get_session_id()
        print(f"✅ JavaScript监控器工作正常，会话ID: {session_id[:12]}...")
        success_count += 1
    except Exception as e:
        print(f"❌ JavaScript监控器测试失败: {e}")
    
    # 测试3: Bug报告生成器
    print("\n📋 测试3: Bug报告生成器")
    try:
        from src.bug_detection.feedback.bug_reporter import BugReporter
        bug_reporter = BugReporter()
        
        test_error = {
            'type': 'javascript',
            'message': 'TypeError: Cannot read property of undefined',
            'source': 'test.js',
            'line_number': 1
        }
        
        bug_report = bug_reporter.generate_bug_report(test_error)
        print(f"✅ Bug报告生成器工作正常，报告ID: {bug_report['id'][:12]}...")
        success_count += 1
    except Exception as e:
        print(f"❌ Bug报告生成器测试失败: {e}")
    
    # 测试4: UI页面集成
    print("\n📋 测试4: UI页面集成")
    try:
        # 检查Bug检测状态页面文件是否存在
        if os.path.exists('src/ui/pages/bug_detection_status.py'):
            print("✅ Bug检测状态页面文件存在")
            success_count += 1
        else:
            print("❌ Bug检测状态页面文件不存在")
    except Exception as e:
        print(f"❌ UI页面集成测试失败: {e}")
    
    # 测试5: 导航集成
    print("\n📋 测试5: 导航集成")
    try:
        # 检查导航文件是否包含Bug检测页面
        with open('src/ui/components/navigation.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if '🔍 Bug检测状态' in content:
                print("✅ Bug检测状态页面已添加到导航菜单")
                success_count += 1
            else:
                print("❌ Bug检测状态页面未在导航菜单中找到")
    except Exception as e:
        print(f"❌ 导航集成测试失败: {e}")
    
    # 测试6: 主UI集成
    print("\n📋 测试6: 主UI集成")
    try:
        # 检查main.py是否包含Bug检测集成代码
        with open('src/ui/main.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'inject_js_monitor' in content:
                print("✅ Bug检测系统已集成到主UI")
                success_count += 1
            else:
                print("❌ Bug检测系统未在主UI中找到")
    except Exception as e:
        print(f"❌ 主UI集成测试失败: {e}")
    
    # 显示结果
    print("\n" + "="*50)
    print("🎯 集成验证结果")
    print(f"✅ 成功: {success_count}/{total_tests}")
    print(f"📊 成功率: {success_count/total_tests*100:.1f}%")
    
    if success_count == total_tests:
        print("\n🎉 Bug检测系统集成验证全部通过！")
        show_usage_guide()
        return True
    elif success_count >= total_tests * 0.8:
        print("\n🟡 Bug检测系统基本集成成功，有少量问题")
        show_usage_guide()
        return True
    else:
        print("\n❌ Bug检测系统集成存在较多问题，需要修复")
        return False

def show_usage_guide():
    """显示使用指南"""
    print("\n" + "="*60)
    print("🎯 Bug检测系统使用指南")
    print("="*60)
    
    print("\n🚀 启动应用:")
    print("   streamlit run src/ui/main.py")
    print("   (或使用您现有的启动脚本)")
    
    print("\n🔍 查看Bug检测功能:")
    print("   1. 启动应用后，在左侧导航菜单中找到 '系统管理'")
    print("   2. 点击 '🔍 Bug检测状态'")
    print("   3. 查看实时的Bug检测和性能监控数据")
    
    print("\n📊 自动监控功能:")
    print("   ✅ JavaScript错误自动捕获 - 所有页面的JS错误都会被记录")
    print("   ✅ 智能Bug报告生成 - 自动分析错误并提供修复建议")
    print("   ✅ 性能数据收集 - API响应时间和错误率统计")
    print("   ✅ 系统健康监控 - 实时系统状态检查")
    
    print("\n🎮 测试Bug检测:")
    print("   • 在浏览器控制台中故意触发JavaScript错误")
    print("   • 错误会被自动捕获并显示在Bug检测状态页面")
    print("   • 查看自动生成的Bug报告和修复建议")
    
    print("\n📈 监控数据:")
    print("   • Bug统计图表和趋势分析")
    print("   • API性能监控和告警")
    print("   • 系统健康度评估")
    print("   • 错误分类和优先级排序")
    
    print("\n🔧 技术特性:")
    print("   • 零配置自动启动")
    print("   • 不影响现有功能")
    print("   • 实时数据更新")
    print("   • 企业级错误处理")

def check_file_structure():
    """检查文件结构"""
    print("\n📁 检查文件结构:")
    
    required_files = [
        'src/bug_detection/core/database_manager.py',
        'src/bug_detection/monitoring/js_monitor.py',
        'src/bug_detection/feedback/bug_reporter.py',
        'src/ui/pages/bug_detection_status.py',
        'src/api/bug_detection/monitoring.py',
        'src/api/bug_detection/reporting.py'
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} (缺失)")

if __name__ == "__main__":
    print("🔍 开始Bug检测系统集成验证...")
    
    # 检查文件结构
    check_file_structure()
    
    # 运行主验证
    success = main()
    
    if success:
        print("\n🎊 验证完成！Bug检测系统已成功集成到福彩3D预测系统中。")
        print("🚀 现在您可以启动应用并使用Bug检测功能了！")
    else:
        print("\n⚠️ 验证未完全通过，但基本功能应该可以使用。")
    
    sys.exit(0 if success else 1)
