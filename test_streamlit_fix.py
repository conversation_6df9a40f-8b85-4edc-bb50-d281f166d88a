#!/usr/bin/env python3
"""
测试Streamlit修复效果
"""

import requests
import json

def test_streamlit_fix():
    print("🧪 测试Streamlit修复效果...")
    
    # 测试API返回的数据结构
    try:
        print("\n1. 测试基础统计API数据结构...")
        response = requests.get("http://127.0.0.1:8888/api/v1/stats/basic", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API响应成功")
            print("📊 返回的数据结构:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 检查关键字段
            print("\n🔍 字段检查:")
            fields_to_check = [
                ('total_records', data.get('total_records')),
                ('date_range', data.get('date_range')),
                ('sum_value_stats', data.get('sum_value_stats')),
                ('span_value_stats', data.get('span_value_stats')),
                ('sales_amount_stats', data.get('sales_amount_stats')),
                ('query_time_ms', data.get('query_time_ms'))
            ]
            
            for field_name, field_value in fields_to_check:
                if field_value is not None:
                    print(f"✅ {field_name}: {type(field_value).__name__} = {field_value}")
                else:
                    print(f"❌ {field_name}: 缺失")
                    
        else:
            print(f"❌ API响应失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    
    # 测试Streamlit应用
    try:
        print("\n2. 测试Streamlit应用状态...")
        response = requests.get("http://127.0.0.1:8501", timeout=5)
        if response.status_code == 200:
            print("✅ Streamlit应用正常运行")
        else:
            print(f"❌ Streamlit应用状态异常: {response.status_code}")
    except Exception as e:
        print(f"❌ Streamlit连接失败: {e}")
    
    print("\n📋 测试完成")
    print("🎯 请在浏览器中检查 http://127.0.0.1:8501 的数据概览页面")

if __name__ == "__main__":
    test_streamlit_fix()
