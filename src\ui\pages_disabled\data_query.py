"""
数据查询页面模块
提供福彩3D历史数据的查询功能
"""

import requests
import streamlit as st
import pandas as pd
from datetime import date

# API配置
API_BASE_URL = "http://127.0.0.1:8888"


def query_data(start_date=None, end_date=None, min_sum=None, max_sum=None, limit=100):
    """查询数据"""
    try:
        params = {"limit": limit}
        if start_date and end_date:
            params.update({"start_date": start_date, "end_date": end_date})
        elif min_sum is not None and max_sum is not None:
            params.update({"min_sum": min_sum, "max_sum": max_sum})
        
        response = requests.get(
            f"{API_BASE_URL}/api/v1/data/query",
            params=params,
            timeout=15
        )
        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"数据查询失败: {response.status_code}")
            return None
    except Exception as e:
        st.error(f"请求失败: {str(e)}")
        return None


def show_data_query():
    """显示数据查询页面"""
    st.header("🔍 数据查询")
    
    # 页面说明
    st.markdown("""
    ### 📋 功能说明
    - **按日期范围查询**：查询指定日期范围内的开奖记录
    - **按和值范围查询**：查询指定和值范围内的开奖记录
    - 支持最多查询10,000条记录
    """)
    
    # 查询选项
    query_type = st.radio(
        "选择查询类型",
        ["按日期范围", "按和值范围"],
        help="选择不同的查询方式来筛选历史数据"
    )
    
    if query_type == "按日期范围":
        st.subheader("📅 按日期范围查询")
        
        col1, col2, col3 = st.columns([2, 2, 1])
        with col1:
            start_date = st.date_input(
                "开始日期", 
                value=date(2025, 1, 1),
                help="选择查询的开始日期"
            )
        with col2:
            end_date = st.date_input(
                "结束日期", 
                value=date.today(),
                help="选择查询的结束日期"
            )
        with col3:
            limit = st.number_input(
                "记录数限制",
                min_value=10,
                max_value=10000,
                value=200,
                step=50,
                help="限制返回的记录数量"
            )
        
        if st.button("🔍 查询数据", type="primary"):
            if start_date > end_date:
                st.error("❌ 开始日期不能晚于结束日期")
            else:
                with st.spinner("正在查询数据..."):
                    result = query_data(
                        start_date=start_date.isoformat(),
                        end_date=end_date.isoformat(),
                        limit=limit
                    )
                
                if result and result.get('records'):
                    st.success(f"✅ 查询到 {result['total_count']} 条记录")
                    
                    # 转换为DataFrame并显示
                    df = pd.DataFrame(result['records'])
                    
                    # 数据预览
                    st.subheader("📊 查询结果")
                    st.dataframe(df, use_container_width=True)
                    
                    # 统计信息
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("记录总数", result['total_count'])
                    with col2:
                        st.metric("查询耗时", f"{result['query_time_ms']:.2f}ms")
                    with col3:
                        if 'period' in df.columns:
                            st.metric("期号范围", f"{df['period'].min()} - {df['period'].max()}")
                    
                    # 下载功能
                    csv = df.to_csv(index=False, encoding='utf-8-sig')
                    st.download_button(
                        label="📥 下载CSV文件",
                        data=csv,
                        file_name=f"福彩3D数据_{start_date}_{end_date}.csv",
                        mime="text/csv"
                    )
                    
                else:
                    st.warning("⚠️ 查询失败或无数据")
                    if result and 'message' in result:
                        st.error(f"错误信息: {result['message']}")
    
    else:  # 按和值范围
        st.subheader("🔢 按和值范围查询")
        
        col1, col2, col3 = st.columns([2, 2, 1])
        with col1:
            min_sum = st.number_input(
                "最小和值", 
                min_value=0, 
                max_value=27, 
                value=10,
                help="三位数字之和的最小值"
            )
        with col2:
            max_sum = st.number_input(
                "最大和值", 
                min_value=0, 
                max_value=27, 
                value=15,
                help="三位数字之和的最大值"
            )
        with col3:
            limit = st.number_input(
                "记录数限制",
                min_value=10,
                max_value=10000,
                value=200,
                step=50,
                help="限制返回的记录数量",
                key="sum_limit"
            )
        
        if st.button("🔍 查询数据", type="primary", key="query_sum_btn"):
            if min_sum > max_sum:
                st.error("❌ 最小和值不能大于最大和值")
            else:
                with st.spinner("正在查询数据..."):
                    result = query_data(
                        min_sum=min_sum, 
                        max_sum=max_sum, 
                        limit=limit
                    )
                
                if result and result.get('records'):
                    st.success(f"✅ 查询到 {result['total_count']} 条记录")
                    
                    # 转换为DataFrame并显示
                    df = pd.DataFrame(result['records'])
                    
                    # 数据预览
                    st.subheader("📊 查询结果")
                    st.dataframe(df, use_container_width=True)
                    
                    # 统计信息
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("记录总数", result['total_count'])
                    with col2:
                        st.metric("查询耗时", f"{result['query_time_ms']:.2f}ms")
                    with col3:
                        if 'numbers' in df.columns:
                            # 计算实际和值分布
                            sums = df['numbers'].apply(lambda x: sum(int(d) for d in str(x).zfill(3)))
                            st.metric("实际和值范围", f"{sums.min()} - {sums.max()}")
                    
                    # 和值分布图
                    if 'numbers' in df.columns:
                        st.subheader("📈 和值分布")
                        sums = df['numbers'].apply(lambda x: sum(int(d) for d in str(x).zfill(3)))
                        sum_counts = sums.value_counts().sort_index()
                        st.bar_chart(sum_counts)
                    
                    # 下载功能
                    csv = df.to_csv(index=False, encoding='utf-8-sig')
                    st.download_button(
                        label="📥 下载CSV文件",
                        data=csv,
                        file_name=f"福彩3D数据_和值{min_sum}-{max_sum}.csv",
                        mime="text/csv"
                    )
                    
                else:
                    st.warning("⚠️ 查询失败或无数据")
                    if result and 'message' in result:
                        st.error(f"错误信息: {result['message']}")
    
    # 使用说明
    with st.expander("💡 使用说明"):
        st.markdown("""
        ### 查询说明
        1. **日期查询**：适合查看特定时间段的开奖情况
        2. **和值查询**：适合分析特定和值范围的号码分布
        3. **记录限制**：为了性能考虑，建议单次查询不超过1000条记录
        
        ### 数据字段说明
        - **period**：期号
        - **date**：开奖日期
        - **numbers**：开奖号码
        - **sum_value**：和值（三位数字之和）
        
        ### 注意事项
        - 查询结果按日期倒序排列（最新的在前）
        - 支持CSV格式下载，方便进一步分析
        - 查询超时时间为15秒
        """)


if __name__ == "__main__":
    show_data_query()
