#!/usr/bin/env python3
"""
修复数据库中的奖金字段错误
"""

import sqlite3
import os

def fix_database_records():
    """修复数据库中的奖金字段错误"""
    print("开始修复数据库中的奖金字段错误...")
    
    db_path = os.path.join('data', 'lottery.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 检查当前状态
        print("\n=== 检查当前数据库状态 ===")
        cursor.execute("""
            SELECT period, date, numbers, direct_prize, group3_prize, group6_prize
            FROM lottery_records 
            ORDER BY date DESC, period DESC 
            LIMIT 5
        """)
        
        records = cursor.fetchall()
        print(f"最新的 {len(records)} 条记录:")
        
        wrong_count = 0
        for i, record in enumerate(records, 1):
            period, date, numbers, direct_prize, group3_prize, group6_prize = record
            print(f"\n记录 {i}: 期号{period} ({date})")
            print(f"  开奖号码: {numbers}")
            print(f"  直选奖金: {direct_prize}")
            print(f"  组三奖金: {group3_prize}")
            print(f"  组六奖金: {group6_prize}")
            
            if direct_prize == 0 and group3_prize == 1040 and group6_prize == 0:
                print("  ❌ 奖金字段错误（需要修复）")
                wrong_count += 1
            elif direct_prize == 1040 and group3_prize == 346 and group6_prize == 173:
                print("  ✅ 奖金字段正确")
            else:
                print(f"  ⚠️ 奖金字段异常")
        
        # 2. 查找所有需要修复的记录
        print(f"\n=== 查找需要修复的记录 ===")
        cursor.execute("""
            SELECT COUNT(*) 
            FROM lottery_records 
            WHERE direct_prize = 0 AND group3_prize = 1040 AND group6_prize = 0
        """)
        
        total_wrong = cursor.fetchone()[0]
        print(f"发现 {total_wrong} 条需要修复的记录")
        
        if total_wrong > 0:
            # 3. 执行修复
            print(f"\n=== 执行修复 ===")
            cursor.execute("""
                UPDATE lottery_records 
                SET direct_prize = 1040, group3_prize = 346, group6_prize = 173
                WHERE direct_prize = 0 AND group3_prize = 1040 AND group6_prize = 0
            """)
            
            affected_rows = cursor.rowcount
            conn.commit()
            print(f"✅ 成功修复 {affected_rows} 条记录")
            
            # 4. 验证修复结果
            print(f"\n=== 验证修复结果 ===")
            cursor.execute("""
                SELECT period, date, numbers, direct_prize, group3_prize, group6_prize
                FROM lottery_records 
                ORDER BY date DESC, period DESC 
                LIMIT 5
            """)
            
            fixed_records = cursor.fetchall()
            print(f"修复后最新的 {len(fixed_records)} 条记录:")
            
            all_correct = True
            for i, record in enumerate(fixed_records, 1):
                period, date, numbers, direct_prize, group3_prize, group6_prize = record
                print(f"\n记录 {i}: 期号{period} ({date})")
                print(f"  开奖号码: {numbers}")
                print(f"  直选奖金: {direct_prize}")
                print(f"  组三奖金: {group3_prize}")
                print(f"  组六奖金: {group6_prize}")
                
                if direct_prize == 1040 and group3_prize == 346 and group6_prize == 173:
                    print("  ✅ 奖金字段正确")
                else:
                    print("  ❌ 奖金字段仍然错误")
                    all_correct = False
            
            if all_correct:
                print(f"\n🎉 所有记录修复成功！")
            else:
                print(f"\n⚠️ 部分记录修复失败，需要进一步检查")
                
        else:
            print("✅ 没有发现需要修复的记录")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库修复失败: {e}")
        return False

def main():
    """主函数"""
    print("福彩3D数据库奖金字段修复工具")
    print("=" * 50)
    
    success = fix_database_records()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 数据库修复完成！")
        print("现在可以刷新Streamlit页面查看修复结果")
    else:
        print("❌ 数据库修复失败，请检查错误信息")

if __name__ == "__main__":
    main()
