#!/usr/bin/env python3
"""
系统性能优化脚本
优化数据库初始化和系统性能，减少资源消耗
"""

import os
import sys
import logging
import psutil
import time
from pathlib import Path
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self):
        self.src_dir = Path("src")
        self.optimization_dir = Path("optimizations")
        self.optimization_dir.mkdir(exist_ok=True)
        
        # 性能基准
        self.baseline_metrics = {}
        self.optimized_metrics = {}
    
    def analyze_performance_issues(self) -> dict:
        """分析性能问题"""
        logger.info("📊 分析系统性能问题...")
        
        issues = {
            'high_memory_usage': False,
            'frequent_db_init': False,
            'no_connection_pooling': False,
            'no_singleton_pattern': False,
            'missing_caching': False
        }
        
        # 检查内存使用
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        issues['high_memory_usage'] = memory_mb > 500
        logger.info(f"💾 当前内存使用: {memory_mb:.1f}MB")
        
        # 检查数据库管理器
        db_manager_file = self.src_dir / "bug_detection" / "core" / "database_manager.py"
        if db_manager_file.exists():
            with open(db_manager_file, 'r', encoding='utf-8') as f:
                content = f.read()
                issues['no_singleton_pattern'] = '_instance' not in content
                issues['no_connection_pooling'] = 'connection_pool' not in content
        
        # 检查AI管理器
        ai_manager_file = self.src_dir / "bug_detection" / "ai" / "ai_manager.py"
        if ai_manager_file.exists():
            with open(ai_manager_file, 'r', encoding='utf-8') as f:
                content = f.read()
                issues['missing_caching'] = 'cache' not in content.lower()
        
        logger.info(f"🔍 性能问题分析: {sum(issues.values())}/{len(issues)} 项需要优化")
        return issues
    
    def implement_singleton_pattern(self) -> bool:
        """实现单例模式"""
        logger.info("🔧 实现单例模式...")
        
        try:
            # 创建单例基类
            singleton_code = '''#!/usr/bin/env python3
"""
单例模式基类
确保每个类只有一个实例
"""

import threading
from typing import Any, Dict

class SingletonMeta(type):
    """单例元类"""
    _instances: Dict[Any, Any] = {}
    _lock: threading.Lock = threading.Lock()
    
    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            with cls._lock:
                if cls not in cls._instances:
                    instance = super().__call__(*args, **kwargs)
                    cls._instances[cls] = instance
        return cls._instances[cls]

class Singleton(metaclass=SingletonMeta):
    """单例基类"""
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        self._initialized = True
        self._setup()
    
    def _setup(self):
        """子类重写此方法进行初始化"""
        pass

class DatabaseManagerSingleton(Singleton):
    """数据库管理器单例"""
    
    def _setup(self):
        """初始化数据库管理器"""
        self.connection_pool = {}
        self.max_connections = 10
        self.current_connections = 0
        
    def get_connection(self, db_path: str):
        """获取数据库连接"""
        if db_path not in self.connection_pool:
            import sqlite3
            self.connection_pool[db_path] = sqlite3.connect(
                db_path, 
                check_same_thread=False,
                timeout=30.0
            )
            self.current_connections += 1
        
        return self.connection_pool[db_path]
    
    def close_connections(self):
        """关闭所有连接"""
        for conn in self.connection_pool.values():
            conn.close()
        self.connection_pool.clear()
        self.current_connections = 0

class AIManagerSingleton(Singleton):
    """AI管理器单例"""
    
    def _setup(self):
        """初始化AI管理器"""
        self.model_cache = {}
        self.analysis_cache = {}
        self.cache_size_limit = 1000
        
    def get_cached_analysis(self, key: str):
        """获取缓存的分析结果"""
        return self.analysis_cache.get(key)
    
    def cache_analysis(self, key: str, result: dict):
        """缓存分析结果"""
        if len(self.analysis_cache) >= self.cache_size_limit:
            # 清理最旧的缓存
            oldest_key = next(iter(self.analysis_cache))
            del self.analysis_cache[oldest_key]
        
        self.analysis_cache[key] = result
    
    def clear_cache(self):
        """清理缓存"""
        self.analysis_cache.clear()
        self.model_cache.clear()
'''
            
            singleton_file = self.optimization_dir / "singleton_pattern.py"
            with open(singleton_file, 'w', encoding='utf-8') as f:
                f.write(singleton_code)
            
            logger.info(f"✅ 单例模式实现已创建: {singleton_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 单例模式实现失败: {e}")
            return False
    
    def optimize_database_connections(self) -> bool:
        """优化数据库连接"""
        logger.info("🗄️ 优化数据库连接...")
        
        try:
            # 创建连接池优化代码
            connection_pool_code = '''#!/usr/bin/env python3
"""
数据库连接池优化
减少连接创建和销毁的开销
"""

import sqlite3
import threading
import time
from queue import Queue, Empty
from contextlib import contextmanager
from typing import Optional

class ConnectionPool:
    """数据库连接池"""
    
    def __init__(self, db_path: str, max_connections: int = 10, timeout: float = 30.0):
        self.db_path = db_path
        self.max_connections = max_connections
        self.timeout = timeout
        self.pool = Queue(maxsize=max_connections)
        self.current_connections = 0
        self.lock = threading.Lock()
        
        # 预创建一些连接
        self._initialize_pool()
    
    def _initialize_pool(self):
        """初始化连接池"""
        initial_connections = min(3, self.max_connections)
        for _ in range(initial_connections):
            conn = self._create_connection()
            if conn:
                self.pool.put(conn)
    
    def _create_connection(self) -> Optional[sqlite3.Connection]:
        """创建新连接"""
        try:
            conn = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=self.timeout
            )
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=10000")
            conn.execute("PRAGMA temp_store=MEMORY")
            
            with self.lock:
                self.current_connections += 1
            
            return conn
        except Exception as e:
            print(f"创建数据库连接失败: {e}")
            return None
    
    @contextmanager
    def get_connection(self):
        """获取连接的上下文管理器"""
        conn = None
        try:
            # 尝试从池中获取连接
            try:
                conn = self.pool.get(timeout=5.0)
            except Empty:
                # 池中没有可用连接，创建新连接
                if self.current_connections < self.max_connections:
                    conn = self._create_connection()
                else:
                    # 等待连接可用
                    conn = self.pool.get(timeout=self.timeout)
            
            if conn is None:
                raise Exception("无法获取数据库连接")
            
            yield conn
            
        finally:
            if conn:
                # 将连接返回池中
                try:
                    self.pool.put_nowait(conn)
                except:
                    # 池已满，关闭连接
                    conn.close()
                    with self.lock:
                        self.current_connections -= 1
    
    def close_all(self):
        """关闭所有连接"""
        while not self.pool.empty():
            try:
                conn = self.pool.get_nowait()
                conn.close()
            except Empty:
                break
        
        with self.lock:
            self.current_connections = 0

# 全局连接池实例
_connection_pools = {}
_pool_lock = threading.Lock()

def get_connection_pool(db_path: str) -> ConnectionPool:
    """获取连接池实例"""
    if db_path not in _connection_pools:
        with _pool_lock:
            if db_path not in _connection_pools:
                _connection_pools[db_path] = ConnectionPool(db_path)
    return _connection_pools[db_path]
'''
            
            pool_file = self.optimization_dir / "connection_pool.py"
            with open(pool_file, 'w', encoding='utf-8') as f:
                f.write(connection_pool_code)
            
            logger.info(f"✅ 连接池优化已创建: {pool_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 连接池优化失败: {e}")
            return False
    
    def add_performance_monitoring(self) -> bool:
        """添加性能监控"""
        logger.info("📊 添加性能监控...")
        
        try:
            # 创建性能监控代码
            monitor_code = '''#!/usr/bin/env python3
"""
性能监控模块
实时监控系统资源使用和性能指标
"""

import psutil
import time
import threading
import logging
from datetime import datetime
from typing import Dict, List
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: datetime
    cpu_percent: float
    memory_mb: float
    memory_percent: float
    disk_io_read: int
    disk_io_write: int
    network_sent: int
    network_recv: int

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, interval: float = 30.0):
        self.interval = interval
        self.metrics_history: List[PerformanceMetrics] = []
        self.max_history = 1000
        self.monitoring = False
        self.monitor_thread = None
        
        # 性能阈值
        self.thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'memory_mb': 1000.0
        }
        
        # 告警回调
        self.alert_callbacks = []
    
    def start_monitoring(self):
        """开始监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("🚀 性能监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
        logger.info("⏹️ 性能监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                metrics = self._collect_metrics()
                self._store_metrics(metrics)
                self._check_thresholds(metrics)
                time.sleep(self.interval)
            except Exception as e:
                logger.error(f"性能监控错误: {e}")
                time.sleep(self.interval)
    
    def _collect_metrics(self) -> PerformanceMetrics:
        """收集性能指标"""
        process = psutil.Process()
        
        # CPU和内存
        cpu_percent = process.cpu_percent()
        memory_info = process.memory_info()
        memory_mb = memory_info.rss / 1024 / 1024
        memory_percent = process.memory_percent()
        
        # 磁盘IO
        io_counters = process.io_counters()
        disk_io_read = io_counters.read_bytes
        disk_io_write = io_counters.write_bytes
        
        # 网络IO (系统级别)
        net_io = psutil.net_io_counters()
        network_sent = net_io.bytes_sent
        network_recv = net_io.bytes_recv
        
        return PerformanceMetrics(
            timestamp=datetime.now(),
            cpu_percent=cpu_percent,
            memory_mb=memory_mb,
            memory_percent=memory_percent,
            disk_io_read=disk_io_read,
            disk_io_write=disk_io_write,
            network_sent=network_sent,
            network_recv=network_recv
        )
    
    def _store_metrics(self, metrics: PerformanceMetrics):
        """存储性能指标"""
        self.metrics_history.append(metrics)
        
        # 限制历史记录数量
        if len(self.metrics_history) > self.max_history:
            self.metrics_history.pop(0)
    
    def _check_thresholds(self, metrics: PerformanceMetrics):
        """检查性能阈值"""
        alerts = []
        
        if metrics.cpu_percent > self.thresholds['cpu_percent']:
            alerts.append(f"CPU使用率过高: {metrics.cpu_percent:.1f}%")
        
        if metrics.memory_percent > self.thresholds['memory_percent']:
            alerts.append(f"内存使用率过高: {metrics.memory_percent:.1f}%")
        
        if metrics.memory_mb > self.thresholds['memory_mb']:
            alerts.append(f"内存使用量过高: {metrics.memory_mb:.1f}MB")
        
        for alert in alerts:
            logger.warning(f"⚠️ 性能告警: {alert}")
            self._trigger_alerts(alert, metrics)
    
    def _trigger_alerts(self, alert: str, metrics: PerformanceMetrics):
        """触发告警"""
        for callback in self.alert_callbacks:
            try:
                callback(alert, metrics)
            except Exception as e:
                logger.error(f"告警回调执行失败: {e}")
    
    def add_alert_callback(self, callback):
        """添加告警回调"""
        self.alert_callbacks.append(callback)
    
    def get_current_metrics(self) -> Dict:
        """获取当前性能指标"""
        if not self.metrics_history:
            return {}
        
        latest = self.metrics_history[-1]
        return {
            'timestamp': latest.timestamp.isoformat(),
            'cpu_percent': latest.cpu_percent,
            'memory_mb': latest.memory_mb,
            'memory_percent': latest.memory_percent,
            'disk_io_read_mb': latest.disk_io_read / 1024 / 1024,
            'disk_io_write_mb': latest.disk_io_write / 1024 / 1024
        }
    
    def get_metrics_summary(self) -> Dict:
        """获取性能指标摘要"""
        if not self.metrics_history:
            return {}
        
        recent_metrics = self.metrics_history[-10:]  # 最近10个数据点
        
        cpu_values = [m.cpu_percent for m in recent_metrics]
        memory_values = [m.memory_mb for m in recent_metrics]
        
        return {
            'avg_cpu_percent': sum(cpu_values) / len(cpu_values),
            'max_cpu_percent': max(cpu_values),
            'avg_memory_mb': sum(memory_values) / len(memory_values),
            'max_memory_mb': max(memory_values),
            'data_points': len(recent_metrics),
            'monitoring_duration_minutes': (
                (recent_metrics[-1].timestamp - recent_metrics[0].timestamp).total_seconds() / 60
                if len(recent_metrics) > 1 else 0
            )
        }

# 全局性能监控实例
_performance_monitor = None

def get_performance_monitor() -> PerformanceMonitor:
    """获取全局性能监控实例"""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
        _performance_monitor.start_monitoring()
    return _performance_monitor
'''
            
            monitor_file = self.optimization_dir / "performance_monitor.py"
            with open(monitor_file, 'w', encoding='utf-8') as f:
                f.write(monitor_code)
            
            logger.info(f"✅ 性能监控已创建: {monitor_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 性能监控添加失败: {e}")
            return False
    
    def measure_performance_improvement(self) -> dict:
        """测量性能改进效果"""
        logger.info("📏 测量性能改进效果...")
        
        try:
            # 收集当前性能指标
            process = psutil.Process()
            
            current_metrics = {
                'memory_mb': process.memory_info().rss / 1024 / 1024,
                'cpu_percent': process.cpu_percent(interval=1),
                'num_threads': process.num_threads(),
                'num_fds': process.num_fds() if hasattr(process, 'num_fds') else 0,
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"📊 当前性能指标:")
            logger.info(f"   内存使用: {current_metrics['memory_mb']:.1f}MB")
            logger.info(f"   CPU使用: {current_metrics['cpu_percent']:.1f}%")
            logger.info(f"   线程数: {current_metrics['num_threads']}")
            
            # 保存指标到文件
            metrics_file = self.optimization_dir / "performance_metrics.json"
            import json
            with open(metrics_file, 'w', encoding='utf-8') as f:
                json.dump(current_metrics, f, indent=2, ensure_ascii=False)
            
            return current_metrics
            
        except Exception as e:
            logger.error(f"❌ 性能测量失败: {e}")
            return {}
    
    def optimize_performance(self) -> bool:
        """执行完整的性能优化流程"""
        logger.info("🚀 开始系统性能优化...")
        logger.info("=" * 60)
        
        # 1. 分析性能问题
        issues = self.analyze_performance_issues()
        logger.info(f"📊 性能问题分析: {sum(issues.values())}/{len(issues)} 项需要优化")
        
        # 2. 实现单例模式
        if not self.implement_singleton_pattern():
            logger.error("❌ 单例模式实现失败")
            return False
        
        # 3. 优化数据库连接
        if not self.optimize_database_connections():
            logger.error("❌ 数据库连接优化失败")
            return False
        
        # 4. 添加性能监控
        if not self.add_performance_monitoring():
            logger.error("❌ 性能监控添加失败")
            return False
        
        # 5. 测量改进效果
        metrics = self.measure_performance_improvement()
        if metrics:
            logger.info(f"📈 性能优化完成，当前内存使用: {metrics['memory_mb']:.1f}MB")
        
        logger.info("=" * 60)
        logger.info("🎉 系统性能优化完成！")
        return True

def main():
    """主函数"""
    logger.info("⚡ 系统性能优化工具")
    logger.info("=" * 60)
    
    optimizer = PerformanceOptimizer()
    
    success = optimizer.optimize_performance()
    
    if success:
        print("\\n🚀 下一步:")
        print("1. 重启API和Streamlit服务")
        print("2. 监控系统资源使用")
        print("3. 验证性能改进效果")
        print("4. 检查性能监控仪表板")
    else:
        print("\\n❌ 性能优化失败")
        print("请检查错误日志并手动优化")
    
    return success

if __name__ == "__main__":
    main()
