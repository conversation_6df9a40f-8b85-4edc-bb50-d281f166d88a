#!/usr/bin/env python3
"""
检查预测模型状态
"""

import requests
import json

def check_prediction_status():
    """检查预测模型状态"""
    try:
        # 检查预测器信息
        print("🔍 检查预测器信息...")
        response = requests.get("http://127.0.0.1:8888/api/v1/prediction/info", timeout=5)
        if response.status_code == 200:
            info = response.json()
            print("✅ 预测器信息:")
            print(json.dumps(info, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 预测器信息获取失败: {response.status_code}")
            
        # 检查数据库状态
        print("\n🔍 检查数据库状态...")
        response = requests.get("http://127.0.0.1:8888/api/v1/data/stats", timeout=5)
        if response.status_code == 200:
            stats = response.json()
            print("✅ 数据库状态:")
            print(json.dumps(stats, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 数据库状态获取失败: {response.status_code}")
            
        # 检查是否有训练API
        print("\n🔍 检查训练API...")
        response = requests.post("http://127.0.0.1:8888/api/v1/prediction/train", 
                               json={"force_retrain": False}, timeout=10)
        if response.status_code == 200:
            train_result = response.json()
            print("✅ 训练API响应:")
            print(json.dumps(train_result, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 训练API调用失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 检查过程出错: {e}")

if __name__ == "__main__":
    check_prediction_status()
