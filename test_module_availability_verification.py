#!/usr/bin/env python3
"""
模块可用性验证测试
验证verify_intelligent_fusion_availability()函数的工作
"""

import sys
import os

# 添加src路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_module_availability_function():
    """测试模块可用性验证函数"""
    print("=== 测试模块可用性验证函数 ===")
    
    try:
        from ui.intelligent_fusion_components import verify_intelligent_fusion_availability
        
        # 测试函数调用
        result = verify_intelligent_fusion_availability()
        
        print(f"✓ verify_intelligent_fusion_availability() 执行成功")
        print(f"  返回结果: {result}")
        print(f"  结果类型: {type(result)}")
        
        # 验证返回值是布尔类型
        if isinstance(result, bool):
            print("✓ 返回值类型正确 (bool)")
            
            if result:
                print("✓ 智能融合模块可用性验证通过")
            else:
                print("⚠️ 智能融合模块可用性验证失败（可能是正常的）")
            
            return True
        else:
            print(f"✗ 返回值类型错误，期望bool，实际{type(result)}")
            return False
        
    except Exception as e:
        print(f"✗ 模块可用性验证函数测试失败: {e}")
        return False

def test_function_integration():
    """测试函数与其他组件的集成"""
    print("\n=== 测试函数集成 ===")
    
    try:
        from ui.intelligent_fusion_components import (
            verify_intelligent_fusion_availability,
            INTELLIGENT_FUSION_AVAILABLE,
            safe_initialize_intelligent_system
        )
        
        # 测试与INTELLIGENT_FUSION_AVAILABLE标志的一致性
        availability_result = verify_intelligent_fusion_availability()
        
        print(f"INTELLIGENT_FUSION_AVAILABLE标志: {INTELLIGENT_FUSION_AVAILABLE}")
        print(f"verify_intelligent_fusion_availability()结果: {availability_result}")
        
        # 如果标志为False，函数结果也应该为False
        if not INTELLIGENT_FUSION_AVAILABLE and availability_result:
            print("⚠️ 标志与函数结果不一致")
            return False
        
        # 测试与安全初始化函数的配合
        system = safe_initialize_intelligent_system()
        
        if availability_result and system is None:
            print("⚠️ 可用性验证通过但初始化失败")
            return False
        elif not availability_result and system is not None:
            print("⚠️ 可用性验证失败但初始化成功")
            return False
        
        print("✓ 函数集成测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 函数集成测试失败: {e}")
        return False

def test_error_conditions():
    """测试错误条件处理"""
    print("\n=== 测试错误条件处理 ===")
    
    try:
        from ui.intelligent_fusion_components import verify_intelligent_fusion_availability
        
        # 多次调用测试稳定性
        results = []
        for i in range(5):
            result = verify_intelligent_fusion_availability()
            results.append(result)
        
        # 检查结果一致性
        if len(set(results)) == 1:
            print(f"✓ 多次调用结果一致: {results[0]}")
        else:
            print(f"⚠️ 多次调用结果不一致: {results}")
            return False
        
        print("✓ 错误条件处理测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 错误条件处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 开始模块可用性验证测试...")
    print("="*60)
    
    tests = [
        ("模块可用性验证函数", test_module_availability_function),
        ("函数集成", test_function_integration),
        ("错误条件处理", test_error_conditions)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试执行失败: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*60)
    print("🎯 模块可用性验证测试结果:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n📊 测试统计: {passed}/{total} 项通过")
    
    if passed == total:
        print("\n🎉 模块可用性验证测试完全通过！")
        print("✅ verify_intelligent_fusion_availability()函数工作正常")
        print("✅ 与其他组件集成良好")
        print("✅ 错误处理稳定")
        return True
    else:
        print(f"\n⚠️ 部分测试失败 ({total-passed}项)")
        print("❌ 需要检查函数实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
