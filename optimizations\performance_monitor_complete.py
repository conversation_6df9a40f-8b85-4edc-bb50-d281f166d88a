#!/usr/bin/env python3
"""
性能监控模块
实时监控系统资源使用和性能指标
"""

import psutil
import time
import threading
import logging
import json
from datetime import datetime
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: str
    cpu_percent: float
    memory_mb: float
    memory_percent: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_sent_mb: float
    network_recv_mb: float
    active_connections: int
    thread_count: int

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, interval: float = 30.0, max_history: int = 1000):
        self.interval = interval
        self.max_history = max_history
        self.metrics_history: List[PerformanceMetrics] = []
        self.monitoring = False
        self.monitor_thread = None
        
        # 性能阈值
        self.thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'memory_mb': 1000.0,
            'active_connections': 50
        }
        
        # 告警回调
        self.alert_callbacks = []
        
        # 基准指标
        self.baseline_metrics = None
        
        # 统计信息
        self.stats = {
            'total_alerts': 0,
            'monitoring_start_time': None,
            'last_alert_time': None
        }
    
    def start_monitoring(self):
        """开始监控"""
        if self.monitoring:
            logger.warning("性能监控已在运行")
            return
        
        self.monitoring = True
        self.stats['monitoring_start_time'] = datetime.now()
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("🚀 性能监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
        logger.info("⏹️ 性能监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                metrics = self._collect_metrics()
                self._store_metrics(metrics)
                self._check_thresholds(metrics)
                time.sleep(self.interval)
            except Exception as e:
                logger.error(f"性能监控错误: {e}")
                time.sleep(self.interval)
    
    def _collect_metrics(self) -> PerformanceMetrics:
        """收集性能指标"""
        try:
            process = psutil.Process()
            
            # CPU和内存
            cpu_percent = process.cpu_percent()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            memory_percent = process.memory_percent()
            
            # 磁盘IO
            try:
                io_counters = process.io_counters()
                disk_io_read_mb = io_counters.read_bytes / 1024 / 1024
                disk_io_write_mb = io_counters.write_bytes / 1024 / 1024
            except (AttributeError, psutil.AccessDenied):
                disk_io_read_mb = 0
                disk_io_write_mb = 0
            
            # 网络IO (系统级别)
            try:
                net_io = psutil.net_io_counters()
                network_sent_mb = net_io.bytes_sent / 1024 / 1024
                network_recv_mb = net_io.bytes_recv / 1024 / 1024
            except (AttributeError, psutil.AccessDenied):
                network_sent_mb = 0
                network_recv_mb = 0
            
            # 连接和线程
            try:
                active_connections = len(process.connections())
            except (AttributeError, psutil.AccessDenied):
                active_connections = 0
            
            thread_count = process.num_threads()
            
            return PerformanceMetrics(
                timestamp=datetime.now().isoformat(),
                cpu_percent=cpu_percent,
                memory_mb=memory_mb,
                memory_percent=memory_percent,
                disk_io_read_mb=disk_io_read_mb,
                disk_io_write_mb=disk_io_write_mb,
                network_sent_mb=network_sent_mb,
                network_recv_mb=network_recv_mb,
                active_connections=active_connections,
                thread_count=thread_count
            )
        except Exception as e:
            logger.error(f"收集性能指标失败: {e}")
            # 返回默认指标
            return PerformanceMetrics(
                timestamp=datetime.now().isoformat(),
                cpu_percent=0.0,
                memory_mb=0.0,
                memory_percent=0.0,
                disk_io_read_mb=0.0,
                disk_io_write_mb=0.0,
                network_sent_mb=0.0,
                network_recv_mb=0.0,
                active_connections=0,
                thread_count=0
            )
    
    def _store_metrics(self, metrics: PerformanceMetrics):
        """存储性能指标"""
        self.metrics_history.append(metrics)
        
        # 限制历史记录数量
        if len(self.metrics_history) > self.max_history:
            self.metrics_history.pop(0)
        
        # 设置基准指标
        if self.baseline_metrics is None:
            self.baseline_metrics = metrics
    
    def _check_thresholds(self, metrics: PerformanceMetrics):
        """检查性能阈值"""
        alerts = []
        
        if metrics.cpu_percent > self.thresholds['cpu_percent']:
            alerts.append(f"CPU使用率过高: {metrics.cpu_percent:.1f}%")
        
        if metrics.memory_percent > self.thresholds['memory_percent']:
            alerts.append(f"内存使用率过高: {metrics.memory_percent:.1f}%")
        
        if metrics.memory_mb > self.thresholds['memory_mb']:
            alerts.append(f"内存使用量过高: {metrics.memory_mb:.1f}MB")
        
        if metrics.active_connections > self.thresholds['active_connections']:
            alerts.append(f"活跃连接数过高: {metrics.active_connections}")
        
        for alert in alerts:
            logger.warning(f"⚠️ 性能告警: {alert}")
            self.stats['total_alerts'] += 1
            self.stats['last_alert_time'] = datetime.now()
            self._trigger_alerts(alert, metrics)
    
    def _trigger_alerts(self, alert: str, metrics: PerformanceMetrics):
        """触发告警"""
        for callback in self.alert_callbacks:
            try:
                callback(alert, metrics)
            except Exception as e:
                logger.error(f"告警回调执行失败: {e}")
    
    def add_alert_callback(self, callback):
        """添加告警回调"""
        self.alert_callbacks.append(callback)
    
    def get_current_metrics(self) -> Optional[Dict]:
        """获取当前性能指标"""
        if not self.metrics_history:
            return None
        
        latest = self.metrics_history[-1]
        return asdict(latest)
    
    def get_metrics_summary(self) -> Dict:
        """获取性能指标摘要"""
        if not self.metrics_history:
            return {}
        
        recent_metrics = self.metrics_history[-10:]  # 最近10个数据点
        
        cpu_values = [m.cpu_percent for m in recent_metrics]
        memory_values = [m.memory_mb for m in recent_metrics]
        
        summary = {
            'avg_cpu_percent': sum(cpu_values) / len(cpu_values),
            'max_cpu_percent': max(cpu_values),
            'avg_memory_mb': sum(memory_values) / len(memory_values),
            'max_memory_mb': max(memory_values),
            'data_points': len(recent_metrics),
            'total_alerts': self.stats['total_alerts'],
            'monitoring_duration_minutes': 0
        }
        
        if len(recent_metrics) > 1:
            start_time = datetime.fromisoformat(recent_metrics[0].timestamp)
            end_time = datetime.fromisoformat(recent_metrics[-1].timestamp)
            summary['monitoring_duration_minutes'] = (end_time - start_time).total_seconds() / 60
        
        return summary
    
    def export_metrics(self, file_path: str):
        """导出性能指标到文件"""
        try:
            data = {
                'export_time': datetime.now().isoformat(),
                'stats': self.stats,
                'thresholds': self.thresholds,
                'metrics': [asdict(m) for m in self.metrics_history]
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"性能指标已导出到: {file_path}")
        except Exception as e:
            logger.error(f"导出性能指标失败: {e}")
    
    def get_performance_report(self) -> Dict:
        """生成性能报告"""
        if not self.metrics_history:
            return {"error": "没有性能数据"}
        
        current = self.get_current_metrics()
        summary = self.get_metrics_summary()
        
        # 与基准对比
        improvement = {}
        if self.baseline_metrics and current:
            improvement = {
                'memory_improvement_mb': self.baseline_metrics.memory_mb - current['memory_mb'],
                'cpu_improvement_percent': self.baseline_metrics.cpu_percent - current['cpu_percent'],
                'connection_improvement': self.baseline_metrics.active_connections - current['active_connections']
            }
        
        return {
            'current_metrics': current,
            'summary': summary,
            'improvement': improvement,
            'monitoring_stats': self.stats,
            'thresholds': self.thresholds
        }

# 全局性能监控实例
_performance_monitor = None
_monitor_lock = threading.Lock()

def get_performance_monitor() -> PerformanceMonitor:
    """获取全局性能监控实例"""
    global _performance_monitor
    if _performance_monitor is None:
        with _monitor_lock:
            if _performance_monitor is None:
                _performance_monitor = PerformanceMonitor()
                _performance_monitor.start_monitoring()
    return _performance_monitor

def get_current_performance() -> Optional[Dict]:
    """获取当前性能指标"""
    monitor = get_performance_monitor()
    return monitor.get_current_metrics()

def get_performance_summary() -> Dict:
    """获取性能摘要"""
    monitor = get_performance_monitor()
    return monitor.get_metrics_summary()

def export_performance_data(file_path: str = None):
    """导出性能数据"""
    if file_path is None:
        file_path = f"performance_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    monitor = get_performance_monitor()
    monitor.export_metrics(file_path)
    return file_path
