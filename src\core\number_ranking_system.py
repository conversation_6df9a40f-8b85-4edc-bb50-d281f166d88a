"""
号码排行榜系统
实现NumberRankingSystem类，开发候选号码排行榜生成算法
"""

import sqlite3
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging
import numpy as np
from .accuracy_focused_fusion import AccuracyFocusedFusion, ModelPrediction
from .model_performance_tracker import ModelPerformanceTracker

@dataclass
class RankingItem:
    """排行榜项目数据结构"""
    rank: int
    number: str
    confidence: float
    composite_score: float
    model_support_count: int
    historical_hit_rate: float
    recommendation_level: str
    prediction_method: str
    avg_confidence: float = 0.0
    max_confidence: float = 0.0
    support_models: List[str] = None

class NumberRankingSystem:
    """号码排行榜系统"""
    
    def __init__(self, fusion_engine: AccuracyFocusedFusion, performance_tracker: ModelPerformanceTracker, 
                 db_path: str = "data/lottery.db"):
        """
        初始化排行榜系统
        
        Args:
            fusion_engine: 融合引擎
            performance_tracker: 性能跟踪器
            db_path: 数据库路径
        """
        self.fusion = fusion_engine
        self.tracker = performance_tracker
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
    
    def generate_ranking_list(self, model_predictions: List[ModelPrediction], top_n: int = 10) -> List[RankingItem]:
        """
        生成候选号码排行榜
        
        Args:
            model_predictions: 模型预测列表
            top_n: 返回前N个候选
            
        Returns:
            排行榜列表
        """
        try:
            self.logger.info(f"开始生成排行榜，目标数量: {top_n}")
            
            # 获取动态权重
            weights = self.tracker.calculate_dynamic_weights()
            
            # 使用融合引擎的置信度排序方法
            ranked_candidates = self._calculate_comprehensive_ranking(model_predictions, weights)
            
            # 转换为排行榜格式
            ranking_list = []
            for i, candidate in enumerate(ranked_candidates[:top_n]):
                # 计算历史命中率
                historical_hit_rate = self.get_historical_hit_rate(candidate['number'])
                
                # 确定推荐等级
                recommendation_level = self.get_recommendation_level(candidate['composite_score'])
                
                # 获取支持模型列表
                support_models = [detail['model'] for detail in candidate['details']]
                
                ranking_item = RankingItem(
                    rank=i + 1,
                    number=candidate['number'],
                    confidence=candidate['composite_score'],
                    composite_score=candidate['composite_score'],
                    model_support_count=candidate['model_count'],
                    historical_hit_rate=historical_hit_rate,
                    recommendation_level=recommendation_level,
                    prediction_method="多模型综合评分",
                    avg_confidence=candidate['avg_confidence'],
                    max_confidence=candidate['max_confidence'],
                    support_models=support_models
                )
                
                ranking_list.append(ranking_item)
            
            self.logger.info(f"排行榜生成完成，共 {len(ranking_list)} 个候选")
            return ranking_list
            
        except Exception as e:
            self.logger.error(f"生成排行榜失败: {e}")
            return []
    
    def _calculate_comprehensive_ranking(self, model_predictions: List[ModelPrediction], 
                                       weights: Dict[str, float]) -> List[Dict[str, Any]]:
        """
        计算综合排名
        
        Args:
            model_predictions: 模型预测列表
            weights: 模型权重
            
        Returns:
            排序后的候选列表
        """
        try:
            all_candidates = {}
            
            # 收集所有候选号码及其置信度
            for pred in model_predictions:
                model_weight = weights.get(pred.model_name, 0.25)
                
                for candidate, confidence in pred.get_all_candidates():
                    if candidate not in all_candidates:
                        all_candidates[candidate] = []
                    
                    all_candidates[candidate].append({
                        'model': pred.model_name,
                        'confidence': confidence,
                        'weighted_confidence': confidence * model_weight,
                        'model_weight': model_weight
                    })
            
            # 计算综合评分
            ranked_candidates = []
            for number, confidences in all_candidates.items():
                # 基础统计
                weighted_confidences = [c['weighted_confidence'] for c in confidences]
                raw_confidences = [c['confidence'] for c in confidences]
                
                avg_confidence = np.mean(weighted_confidences)
                max_confidence = max(weighted_confidences)
                model_count = len(confidences)
                
                # 综合评分算法
                # 1. 基础分：平均置信度 * 0.6 + 最高置信度 * 0.4
                base_score = avg_confidence * 0.6 + max_confidence * 0.4
                
                # 2. 模型支持度加成：支持模型越多，加成越高
                support_bonus = min(model_count / len(model_predictions), 1.0) * 0.1
                
                # 3. 历史命中率加成
                historical_rate = self.get_historical_hit_rate(number)
                history_bonus = historical_rate * 0.05
                
                # 4. 最终综合评分
                composite_score = base_score + support_bonus + history_bonus
                
                ranked_candidates.append({
                    'number': number,
                    'composite_score': composite_score,
                    'avg_confidence': avg_confidence,
                    'max_confidence': max_confidence,
                    'model_count': model_count,
                    'historical_hit_rate': historical_rate,
                    'base_score': base_score,
                    'support_bonus': support_bonus,
                    'history_bonus': history_bonus,
                    'details': confidences
                })
            
            # 按综合评分排序
            ranked_candidates.sort(key=lambda x: x['composite_score'], reverse=True)
            
            return ranked_candidates
            
        except Exception as e:
            self.logger.error(f"计算综合排名失败: {e}")
            return []
    
    def get_historical_hit_rate(self, number: str, window_size: int = 100) -> float:
        """
        计算号码的历史命中率
        
        Args:
            number: 号码
            window_size: 统计窗口大小
            
        Returns:
            历史命中率 (0.0-1.0)
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取最近window_size期的开奖记录
                cursor.execute("""
                    SELECT numbers FROM lottery_records 
                    ORDER BY date DESC 
                    LIMIT ?
                """, (window_size,))
                
                records = cursor.fetchall()
                
                if not records:
                    return 0.0
                
                # 计算命中次数
                hit_count = sum(1 for (winning_number,) in records if winning_number == number)
                
                return hit_count / len(records)
                
        except Exception as e:
            self.logger.error(f"计算历史命中率失败: {e}")
            return 0.0
    
    def get_recommendation_level(self, confidence: float) -> str:
        """
        根据置信度确定推荐等级
        
        Args:
            confidence: 置信度
            
        Returns:
            推荐等级字符串
        """
        if confidence >= 0.8:
            return "强烈推荐"
        elif confidence >= 0.6:
            return "推荐"
        elif confidence >= 0.4:
            return "可考虑"
        elif confidence >= 0.2:
            return "谨慎"
        else:
            return "不推荐"
    
    def get_ranking_statistics(self, ranking_list: List[RankingItem]) -> Dict[str, Any]:
        """
        获取排行榜统计信息
        
        Args:
            ranking_list: 排行榜列表
            
        Returns:
            统计信息字典
        """
        try:
            if not ranking_list:
                return {}
            
            # 基础统计
            total_candidates = len(ranking_list)
            avg_confidence = np.mean([item.confidence for item in ranking_list])
            max_confidence = max([item.confidence for item in ranking_list])
            min_confidence = min([item.confidence for item in ranking_list])
            
            # 推荐等级分布
            level_distribution = {}
            for item in ranking_list:
                level = item.recommendation_level
                level_distribution[level] = level_distribution.get(level, 0) + 1
            
            # 模型支持度统计
            support_counts = [item.model_support_count for item in ranking_list]
            avg_support = np.mean(support_counts)
            max_support = max(support_counts)
            
            # 历史命中率统计
            hit_rates = [item.historical_hit_rate for item in ranking_list]
            avg_hit_rate = np.mean(hit_rates)
            max_hit_rate = max(hit_rates)
            
            return {
                'total_candidates': total_candidates,
                'confidence_stats': {
                    'average': avg_confidence,
                    'maximum': max_confidence,
                    'minimum': min_confidence,
                    'range': max_confidence - min_confidence
                },
                'recommendation_distribution': level_distribution,
                'model_support_stats': {
                    'average': avg_support,
                    'maximum': max_support
                },
                'historical_hit_rate_stats': {
                    'average': avg_hit_rate,
                    'maximum': max_hit_rate
                },
                'top_3_numbers': [item.number for item in ranking_list[:3]],
                'top_3_confidences': [item.confidence for item in ranking_list[:3]]
            }
            
        except Exception as e:
            self.logger.error(f"获取排行榜统计失败: {e}")
            return {}
    
    def save_ranking_to_database(self, ranking_list: List[RankingItem], period_number: str) -> None:
        """
        保存排行榜到数据库
        
        Args:
            ranking_list: 排行榜列表
            period_number: 期号
        """
        try:
            with sqlite3.connect(self.tracker.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建预测排行榜表（如果不存在）
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS prediction_rankings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        period_number TEXT NOT NULL,
                        number TEXT NOT NULL,
                        rank INTEGER NOT NULL,
                        confidence REAL NOT NULL,
                        composite_score REAL NOT NULL,
                        model_support_count INTEGER NOT NULL,
                        historical_hit_rate REAL NOT NULL,
                        recommendation_level TEXT NOT NULL,
                        prediction_method TEXT NOT NULL,
                        created_date DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 删除该期号的旧记录
                cursor.execute("DELETE FROM prediction_rankings WHERE period_number = ?", (period_number,))
                
                # 插入新记录
                for item in ranking_list:
                    cursor.execute("""
                        INSERT INTO prediction_rankings 
                        (period_number, number, rank, confidence, composite_score, 
                         model_support_count, historical_hit_rate, recommendation_level, prediction_method)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        period_number, item.number, item.rank, item.confidence,
                        item.composite_score, item.model_support_count,
                        item.historical_hit_rate, item.recommendation_level, item.prediction_method
                    ))
                
                conn.commit()
                self.logger.info(f"排行榜已保存到数据库，期号: {period_number}, 记录数: {len(ranking_list)}")
                
        except Exception as e:
            self.logger.error(f"保存排行榜到数据库失败: {e}")
    
    def get_ranking_from_database(self, period_number: str) -> List[RankingItem]:
        """
        从数据库获取排行榜
        
        Args:
            period_number: 期号
            
        Returns:
            排行榜列表
        """
        try:
            with sqlite3.connect(self.tracker.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT rank, number, confidence, composite_score, model_support_count,
                           historical_hit_rate, recommendation_level, prediction_method
                    FROM prediction_rankings 
                    WHERE period_number = ?
                    ORDER BY rank
                """, (period_number,))
                
                ranking_list = []
                for row in cursor.fetchall():
                    item = RankingItem(
                        rank=row[0],
                        number=row[1],
                        confidence=row[2],
                        composite_score=row[3],
                        model_support_count=row[4],
                        historical_hit_rate=row[5],
                        recommendation_level=row[6],
                        prediction_method=row[7]
                    )
                    ranking_list.append(item)
                
                return ranking_list
                
        except Exception as e:
            self.logger.error(f"从数据库获取排行榜失败: {e}")
            return []
    
    def compare_rankings(self, current_ranking: List[RankingItem], 
                        previous_ranking: List[RankingItem]) -> Dict[str, Any]:
        """
        比较两期排行榜的变化
        
        Args:
            current_ranking: 当前排行榜
            previous_ranking: 上期排行榜
            
        Returns:
            变化分析结果
        """
        try:
            if not current_ranking or not previous_ranking:
                return {}
            
            # 创建号码到排名的映射
            current_ranks = {item.number: item.rank for item in current_ranking}
            previous_ranks = {item.number: item.rank for item in previous_ranking}
            
            # 分析变化
            new_entries = []  # 新进入排行榜的号码
            dropped_entries = []  # 退出排行榜的号码
            rank_changes = []  # 排名变化
            
            # 找新进入的号码
            for number in current_ranks:
                if number not in previous_ranks:
                    new_entries.append({
                        'number': number,
                        'current_rank': current_ranks[number]
                    })
            
            # 找退出的号码
            for number in previous_ranks:
                if number not in current_ranks:
                    dropped_entries.append({
                        'number': number,
                        'previous_rank': previous_ranks[number]
                    })
            
            # 找排名变化
            for number in current_ranks:
                if number in previous_ranks:
                    current_rank = current_ranks[number]
                    previous_rank = previous_ranks[number]
                    change = previous_rank - current_rank  # 正数表示上升
                    
                    if change != 0:
                        rank_changes.append({
                            'number': number,
                            'current_rank': current_rank,
                            'previous_rank': previous_rank,
                            'change': change,
                            'direction': '上升' if change > 0 else '下降'
                        })
            
            # 排序变化（按变化幅度）
            rank_changes.sort(key=lambda x: abs(x['change']), reverse=True)
            
            return {
                'new_entries': new_entries,
                'dropped_entries': dropped_entries,
                'rank_changes': rank_changes,
                'stability_score': 1.0 - (len(new_entries) + len(dropped_entries)) / max(len(current_ranking), 1)
            }
            
        except Exception as e:
            self.logger.error(f"比较排行榜失败: {e}")
            return {}
