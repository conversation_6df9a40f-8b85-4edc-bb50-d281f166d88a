# RIPER-5协议 v2.0 快速参考指南

## 🚀 立即生效的新要求

### 自动记忆触发 (必须执行)
当用户说出以下内容时，**立即**触发记忆存储：

#### 偏好表达类
```
"我希望..." → 创建偏好设置实体
"我需要..." → 创建需求实体  
"我偏好..." → 创建偏好设置实体
"请记住..." → 创建相应类型实体
"以后要..." → 创建流程或偏好实体
```

#### 流程要求类
```
"流程应该是..." → 创建流程实体
"步骤包括..." → 创建流程实体
"工作方式是..." → 创建流程实体
"标准操作..." → 创建流程实体
```

#### 技术配置类
```
"使用...版本" → 创建技术组件实体
"配置为..." → 创建偏好设置实体
"技术栈包括..." → 创建技术架构实体
"开发环境..." → 创建偏好设置实体
```

### EXECUTE模式强制检查点 (不可跳过)

```
1. 任务开始前检查点
   ↓ search_nodes(查找相关偏好)
   ↓ 确认执行标准

2. 关键决策检查点  
   ↓ add_observations(记录用户选择)
   ↓ 存储决策依据

3. 任务完成检查点
   ↓ create_entities(存储执行结果)
   ↓ 记录经验教训

4. 用户反馈检查点
   ↓ 根据反馈调整记忆
   ↓ 存储满意度信息

5. 模式转换检查点
   ↓ 确保记忆完整性
   ↓ 传递关键信息
```

## 🔧 实用工具命令

### 记忆存储工具
```
create_entities_knowledge-graph
- 创建新的实体（偏好、流程、项目等）

add_observations_knowledge-graph  
- 向现有实体添加新观察

create_relations_knowledge-graph
- 建立实体间的关系

search_nodes_knowledge-graph
- 搜索相关记忆

open_nodes_knowledge-graph
- 打开特定实体查看详情
```

### 标准实体类型
```
偏好设置 (Preference)
流程 (Procedure)  
要求 (Requirement)
项目 (Project)
技术组件 (Technical Component)
经验教训 (Lesson Learned)
开发阶段 (Development Stage)
数据资源 (Data Resource)
```

### 标准关系类型
```
遵循 (follows)
使用 (uses)
包含 (contains)
依赖 (depends_on)
实现 (implements)
基于 (based_on)
触发了 (triggered)
升级为 (upgraded_to)
```

## 📋 执行检查清单

### 每次对话开始时
- [ ] 搜索相关历史记忆
- [ ] 加载用户偏好和流程
- [ ] 确认当前任务背景

### 用户表达偏好时
- [ ] 识别触发词汇
- [ ] 提取关键信息
- [ ] 分类信息类型
- [ ] 调用存储工具
- [ ] 验证存储成功
- [ ] 向用户确认

### EXECUTE模式执行时
- [ ] 任务开始前检查点
- [ ] 实时监控用户表达
- [ ] 关键决策检查点
- [ ] 任务完成检查点
- [ ] 用户反馈检查点

### 模式转换时
- [ ] 保存当前模式记忆
- [ ] 传递关键信息
- [ ] 确保记忆连续性

## ⚡ 快速示例

### 示例1：自动偏好记录
```
用户："我希望以后都使用Python 3.11.9"

AI执行：
1. 识别触发词"我希望"
2. 提取偏好"使用Python 3.11.9"  
3. create_entities([{
     "name": "Python版本偏好",
     "entityType": "偏好设置",
     "observations": ["用户偏好使用Python 3.11.9版本"]
   }])
4. 确认："✅ 已记录您的Python版本偏好"
```

### 示例2：执行模式检查点
```
EXECUTE模式：创建数据库连接

检查点1 (任务开始)：
- search_nodes("数据库") 
- 发现用户偏好PostgreSQL

检查点2 (关键决策)：
- 用户选择连接池大小为10
- add_observations("数据库偏好", ["连接池大小偏好10"])

检查点3 (任务完成)：
- create_entities("数据库连接实现经验")
- 记录成功的配置方法

检查点4 (用户反馈)：
- 用户满意连接性能
- add_observations("数据库偏好", ["连接性能满意"])
```

## 🚨 常见错误避免

### ❌ 错误做法
```
- 忽略用户偏好表达
- 跳过强制检查点
- 不验证存储结果
- 记忆信息不完整
- 关系建立不准确
```

### ✅ 正确做法
```
- 实时监控触发词汇
- 严格执行所有检查点
- 每次存储后验证成功
- 包含完整上下文信息
- 使用标准关系类型
```

## 📞 问题处理

### 记忆工具失败时
```
1. 立即报告问题
2. 使用备用记录方式
3. 请求用户手动确认
4. 记录问题以便改进
```

### 触发词汇识别困难时
```
1. 主动询问用户意图
2. 确认是否需要记录
3. 手动执行记忆存储
4. 优化识别算法
```

### 检查点执行冲突时
```
1. 优先保证记忆完整性
2. 暂停当前任务
3. 完成记忆存储
4. 恢复任务执行
```

---

**版本**: RIPER-5 v2.0  
**生效日期**: 2025-01-14  
**更新频率**: 根据使用反馈持续优化  
**支持**: 遇到问题请及时反馈
