#!/usr/bin/env python3
"""
测试数据源连接问题的诊断脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

print(f"项目根目录: {project_root}")
print(f"Python路径: {sys.path[:3]}")

def test_imports():
    """测试导入"""
    print("\n=== 测试导入 ===")
    
    try:
        import httpx
        print(f"✅ httpx 可用，版本: {httpx.__version__}")
        return True
    except ImportError as e:
        print(f"❌ httpx 不可用: {e}")
        return False

def test_direct_request():
    """直接测试网络请求"""
    print("\n=== 直接测试网络请求 ===")
    
    try:
        import urllib.request
        import urllib.error
        
        url = "https://data.17500.cn/3d_asc.txt"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        request = urllib.request.Request(url, headers=headers)
        
        with urllib.request.urlopen(request, timeout=30) as response:
            content = response.read().decode('utf-8', errors='ignore')
            
        print(f"✅ 直接请求成功")
        print(f"   数据长度: {len(content)} 字符")
        print(f"   前100字符: {content[:100]}")
        return content
        
    except Exception as e:
        print(f"❌ 直接请求失败: {e}")
        return None

async def test_httpx_request():
    """测试httpx异步请求"""
    print("\n=== 测试httpx异步请求 ===")
    
    try:
        import httpx
        
        url = "https://data.17500.cn/3d_asc.txt"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        async with httpx.AsyncClient(timeout=30, headers=headers) as client:
            response = await client.get(url)
            response.raise_for_status()
            content = response.text
            
        print(f"✅ httpx异步请求成功")
        print(f"   数据长度: {len(content)} 字符")
        print(f"   前100字符: {content[:100]}")
        return content
        
    except Exception as e:
        print(f"❌ httpx异步请求失败: {e}")
        return None

def test_collector_import():
    """测试数据收集器导入"""
    print("\n=== 测试数据收集器导入 ===")
    
    try:
        from data.collector import LotteryDataCollector
        print("✅ LotteryDataCollector 导入成功")
        
        collector = LotteryDataCollector()
        print("✅ LotteryDataCollector 实例化成功")
        return collector
        
    except Exception as e:
        print(f"❌ LotteryDataCollector 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_collector_async():
    """测试数据收集器异步方法"""
    print("\n=== 测试数据收集器异步方法 ===")
    
    collector = test_collector_import()
    if not collector:
        return None
        
    try:
        result = await collector.fetch_data_async()
        if result:
            print(f"✅ 收集器异步方法成功")
            print(f"   数据长度: {len(result)} 字符")
            print(f"   前100字符: {result[:100]}")
        else:
            print("❌ 收集器异步方法返回None")
        return result
        
    except Exception as e:
        print(f"❌ 收集器异步方法失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_collector_sync():
    """测试数据收集器同步方法"""
    print("\n=== 测试数据收集器同步方法 ===")
    
    collector = test_collector_import()
    if not collector:
        return None
        
    try:
        result = collector.fetch_data_sync()
        if result:
            print(f"✅ 收集器同步方法成功")
            print(f"   数据长度: {len(result)} 字符")
            print(f"   前100字符: {result[:100]}")
        else:
            print("❌ 收集器同步方法返回None")
        return result
        
    except Exception as e:
        print(f"❌ 收集器同步方法失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def main():
    """主测试函数"""
    print("🔍 开始诊断数据源连接问题...")
    
    # 1. 测试导入
    httpx_available = test_imports()
    
    # 2. 直接测试网络请求
    direct_result = test_direct_request()
    
    # 3. 测试httpx请求（如果可用）
    if httpx_available:
        httpx_result = await test_httpx_request()
    else:
        httpx_result = None
    
    # 4. 测试数据收集器异步方法
    collector_async_result = await test_collector_async()
    
    # 5. 测试数据收集器同步方法
    collector_sync_result = test_collector_sync()
    
    # 总结
    print("\n" + "="*50)
    print("📊 诊断结果总结:")
    print(f"   httpx可用: {'✅' if httpx_available else '❌'}")
    print(f"   直接请求: {'✅' if direct_result else '❌'}")
    print(f"   httpx请求: {'✅' if httpx_result else '❌'}")
    print(f"   收集器异步: {'✅' if collector_async_result else '❌'}")
    print(f"   收集器同步: {'✅' if collector_sync_result else '❌'}")
    
    if not any([direct_result, httpx_result, collector_async_result, collector_sync_result]):
        print("\n❌ 所有方法都失败了，可能的原因:")
        print("   1. 网络连接问题")
        print("   2. 防火墙或代理设置")
        print("   3. 数据源网站临时不可用")
        print("   4. Python环境配置问题")
    elif direct_result and not collector_async_result:
        print("\n⚠️  直接请求成功但收集器失败，可能的原因:")
        print("   1. 收集器代码逻辑问题")
        print("   2. 依赖库版本问题")
        print("   3. 路径或导入问题")

if __name__ == "__main__":
    asyncio.run(main())
