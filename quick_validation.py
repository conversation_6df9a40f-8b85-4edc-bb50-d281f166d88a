"""
快速验证脚本
"""

import sys
import os

# 添加src路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def main():
    print("=== 快速验证测试 ===")
    
    # 1. 测试基础导入
    try:
        import numpy as np
        import torch
        print("✓ 基础库导入成功")
    except Exception as e:
        print(f"✗ 基础库导入失败: {e}")
        return False
    
    # 2. 测试特征工程
    try:
        from prediction.feature_engineering import FeatureEngineeringPipeline
        pipeline = FeatureEngineeringPipeline()
        features = pipeline.extract_all_features(['123', '456', '789'])
        print(f"✓ 特征工程成功: {len(features)} 个特征")
    except Exception as e:
        print(f"✗ 特征工程失败: {e}")
        return False
    
    # 3. 测试模型
    try:
        from prediction.deep_learning.cnn_lstm_attention import CNNLSTMAttentionPredictor
        model = CNNLSTMAttentionPredictor(input_dim=20, num_classes=100)
        test_input = torch.randn(1, 5, 20)
        with torch.no_grad():
            output = model(test_input)
        print(f"✓ 模型测试成功: {output.shape}")
    except Exception as e:
        print(f"✗ 模型测试失败: {e}")
        return False
    
    print("\n🎉 快速验证成功!")
    print("✅ 阶段A核心功能已实现并可用")
    return True

if __name__ == "__main__":
    success = main()
    print(f"状态: {'成功' if success else '失败'}")
