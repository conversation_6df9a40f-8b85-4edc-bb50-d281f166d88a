#!/usr/bin/env python3
"""
简单API测试
"""

import requests
import json

def test_api():
    base_url = "http://localhost:8000"
    
    print("🧪 测试FastAPI服务...")
    
    # 测试基础统计
    try:
        print("\n1. 测试基础统计接口...")
        response = requests.get(f"{base_url}/api/v1/stats/basic", timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if "error" in data:
                print(f"   API错误: {data['error']}")
            else:
                print(f"   ✅ 成功 - 总记录数: {data.get('total_records', 'N/A')}")
                print(f"   查询时间: {data.get('query_time_ms', 'N/A')}ms")
        else:
            print(f"   ❌ 失败 - 状态码: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 测试频率分析
    try:
        print("\n2. 测试频率分析接口...")
        response = requests.get(f"{base_url}/api/v1/analysis/frequency", timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if "error" in data:
                print(f"   API错误: {data['error']}")
            else:
                print(f"   ✅ 成功 - 分析位置: {data.get('position', 'N/A')}")
                print(f"   查询时间: {data.get('query_time_ms', 'N/A')}ms")
        else:
            print(f"   ❌ 失败 - 状态码: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    print("\n📋 API测试完成")

if __name__ == "__main__":
    test_api()
