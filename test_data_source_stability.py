#!/usr/bin/env python3
"""
数据源连接稳定性测试
"""

import sys
import time
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_data_source_stability():
    """测试数据源连接稳定性"""
    print("🔍 开始数据源连接稳定性测试...")
    
    # 测试1: 检查缓存目录
    print("\n1. 测试缓存目录...")
    try:
        cache_dir = Path("data/cache")
        cache_dir.mkdir(parents=True, exist_ok=True)
        
        if cache_dir.exists() and cache_dir.is_dir():
            print("✅ 缓存目录创建成功")
        else:
            print("❌ 缓存目录创建失败")
            return False
    except Exception as e:
        print(f"❌ 缓存目录测试失败: {e}")
        return False
    
    # 测试2: 模拟缓存文件操作
    print("\n2. 测试缓存文件操作...")
    try:
        test_cache_file = cache_dir / "test_data.txt"
        test_data = "测试数据内容\n期号,日期,号码\n2025001,2025-01-01,123"
        
        # 写入测试数据
        test_cache_file.write_text(test_data, encoding='utf-8')
        print("✅ 缓存文件写入成功")
        
        # 读取测试数据
        read_data = test_cache_file.read_text(encoding='utf-8')
        if read_data == test_data:
            print("✅ 缓存文件读取成功")
        else:
            print("❌ 缓存文件读取数据不匹配")
            return False
        
        # 检查文件时间戳
        file_time = test_cache_file.stat().st_mtime
        current_time = time.time()
        if current_time - file_time < 60:  # 1分钟内
            print("✅ 缓存文件时间戳正常")
        else:
            print("⚠️  缓存文件时间戳异常")
        
        # 清理测试文件
        test_cache_file.unlink()
        print("✅ 测试文件清理成功")
        
    except Exception as e:
        print(f"❌ 缓存文件操作测试失败: {e}")
        return False
    
    # 测试3: 数据格式验证
    print("\n3. 测试数据格式验证...")
    try:
        # 模拟有效数据
        valid_data = """2025001 2025-01-01 123 456 1 2 1000000 1040 346 173 0 0 0
2025002 2025-01-02 789 012 2 3 1200000 1040 346 173 0 0 0
2025003 2025-01-03 345 678 3 4 1100000 1040 346 173 0 0 0"""
        
        # 验证数据行数
        lines = valid_data.strip().split('\n')
        if len(lines) >= 3:
            print("✅ 数据行数验证通过")
        else:
            print("❌ 数据行数不足")
            return False
        
        # 验证数据格式
        for i, line in enumerate(lines[:3]):
            fields = line.strip().split()
            if len(fields) >= 13:  # 至少13个字段
                print(f"✅ 第{i+1}行数据格式验证通过")
            else:
                print(f"❌ 第{i+1}行数据格式错误")
                return False
        
        # 验证期号格式
        first_line = lines[0].strip().split()
        period = first_line[0]
        if period.startswith('2025') and len(period) == 7:
            print("✅ 期号格式验证通过")
        else:
            print("❌ 期号格式验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据格式验证失败: {e}")
        return False
    
    # 测试4: 错误处理机制
    print("\n4. 测试错误处理机制...")
    try:
        # 模拟无效数据
        invalid_data = "invalid data format"
        
        # 验证错误数据检测
        lines = invalid_data.strip().split('\n')
        if len(lines) < 10:  # 少于10行认为是无效数据
            print("✅ 无效数据检测成功")
        else:
            print("❌ 无效数据检测失败")
            return False
        
        # 模拟空数据
        empty_data = ""
        if not empty_data or len(empty_data) < 100:
            print("✅ 空数据检测成功")
        else:
            print("❌ 空数据检测失败")
            return False
            
    except Exception as e:
        print(f"❌ 错误处理机制测试失败: {e}")
        return False
    
    # 测试5: 重试机制模拟
    print("\n5. 测试重试机制模拟...")
    try:
        max_retries = 3
        retry_delay = 1.0
        
        for attempt in range(max_retries):
            print(f"   模拟重试 {attempt + 1}/{max_retries}")
            
            # 模拟网络延迟
            time.sleep(0.1)
            
            # 模拟成功（第3次尝试成功）
            if attempt == 2:
                print("✅ 重试机制模拟成功")
                break
            else:
                print(f"   第{attempt + 1}次尝试失败，等待重试...")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
        
    except Exception as e:
        print(f"❌ 重试机制测试失败: {e}")
        return False
    
    # 测试6: 反爬虫对策模拟
    print("\n6. 测试反爬虫对策模拟...")
    try:
        # 模拟User-Agent设置
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
        ]
        
        if len(user_agents) >= 3:
            print("✅ User-Agent池配置成功")
        else:
            print("❌ User-Agent池配置不足")
            return False
        
        # 模拟请求间隔
        request_intervals = [1.0, 2.0, 3.0]
        for i, interval in enumerate(request_intervals):
            print(f"   模拟请求间隔: {interval}秒")
            time.sleep(0.1)  # 实际测试中使用较短时间
        
        print("✅ 反爬虫对策模拟成功")
        
    except Exception as e:
        print(f"❌ 反爬虫对策测试失败: {e}")
        return False
    
    # 测试7: 性能基准
    print("\n7. 测试性能基准...")
    try:
        # 模拟数据处理性能
        start_time = time.time()
        
        # 模拟处理1000行数据
        for i in range(1000):
            # 模拟数据解析
            line = f"2025{i:03d} 2025-01-01 123 456 1 2 1000000 1040 346 173 0 0 0"
            fields = line.split()
            if len(fields) >= 13:
                continue
        
        processing_time = time.time() - start_time
        print(f"✅ 1000行数据处理耗时: {processing_time:.3f}秒")
        
        if processing_time < 1.0:  # 1秒内完成
            print("✅ 性能基准测试通过")
        else:
            print("⚠️  性能可能需要优化")
            
    except Exception as e:
        print(f"❌ 性能基准测试失败: {e}")
        return False
    
    print("\n🎉 数据源连接稳定性测试全部完成！")
    return True

def test_cache_management():
    """测试缓存管理功能"""
    print("\n🔍 开始缓存管理测试...")
    
    try:
        cache_dir = Path("data/cache")
        
        # 创建多个测试缓存文件
        test_files = []
        for i in range(5):
            test_file = cache_dir / f"test_cache_{i}.txt"
            test_file.write_text(f"测试缓存数据 {i}", encoding='utf-8')
            test_files.append(test_file)
        
        print(f"✅ 创建了 {len(test_files)} 个测试缓存文件")
        
        # 统计缓存大小
        total_size = sum(f.stat().st_size for f in test_files)
        print(f"✅ 缓存总大小: {total_size} 字节")
        
        # 清理测试文件
        cleared_count = 0
        for test_file in test_files:
            if test_file.exists():
                test_file.unlink()
                cleared_count += 1
        
        print(f"✅ 清理了 {cleared_count} 个缓存文件")
        return True
        
    except Exception as e:
        print(f"❌ 缓存管理测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始数据源连接稳定性测试套件")
    
    # 确保数据目录存在
    Path("data").mkdir(exist_ok=True)
    
    # 运行测试
    test1_result = test_data_source_stability()
    test2_result = test_cache_management()
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！数据源连接稳定性验证成功。")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，请检查问题。")
        sys.exit(1)
