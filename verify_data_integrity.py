#!/usr/bin/env python3
"""
验证数据完整性
"""

import sqlite3
from datetime import datetime

def verify_record_count():
    """验证记录数"""
    print("1. 验证记录数...")
    with sqlite3.connect('data/lottery.db') as conn:
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM lottery_records')
        count = cursor.fetchone()[0]
        print(f"✅ 当前记录数: {count}")
        
        if count >= 8343:
            print("✅ 记录数符合预期（≥8343）")
            return True
        else:
            print(f"❌ 记录数不足，期望≥8343，实际{count}")
            return False

def verify_field_completeness():
    """验证字段完整性"""
    print("\n2. 验证字段完整性...")
    with sqlite3.connect('data/lottery.db') as conn:
        cursor = conn.cursor()
        
        # 检查是否有NULL值在NOT NULL字段中
        null_checks = [
            ('period', 'period IS NULL'),
            ('date', 'date IS NULL'),
            ('numbers', 'numbers IS NULL'),
            ('trial_numbers', 'trial_numbers IS NULL'),
            ('draw_machine', 'draw_machine IS NULL'),
            ('trial_machine', 'trial_machine IS NULL'),
            ('sales_amount', 'sales_amount IS NULL'),
            ('direct_prize', 'direct_prize IS NULL'),
            ('group3_prize', 'group3_prize IS NULL'),
            ('group6_prize', 'group6_prize IS NULL'),
            ('sum_value', 'sum_value IS NULL'),
            ('trial_sum_value', 'trial_sum_value IS NULL'),
            ('span_value', 'span_value IS NULL'),
            ('trial_span_value', 'trial_span_value IS NULL')
        ]
        
        all_good = True
        for field_name, condition in null_checks:
            cursor.execute(f'SELECT COUNT(*) FROM lottery_records WHERE {condition}')
            null_count = cursor.fetchone()[0]
            if null_count > 0:
                print(f"❌ {field_name} 字段有 {null_count} 个NULL值")
                all_good = False
            else:
                print(f"✅ {field_name} 字段完整")
        
        return all_good

def verify_calculated_fields():
    """验证计算字段准确性"""
    print("\n3. 验证计算字段准确性...")
    with sqlite3.connect('data/lottery.db') as conn:
        cursor = conn.cursor()
        
        # 检查最新10条记录的计算字段
        cursor.execute('''
            SELECT period, numbers, trial_numbers, sum_value, trial_sum_value, 
                   span_value, trial_span_value
            FROM lottery_records 
            ORDER BY period DESC 
            LIMIT 10
        ''')
        
        records = cursor.fetchall()
        all_correct = True
        
        for record in records:
            period, numbers, trial_numbers, sum_value, trial_sum_value, span_value, trial_span_value = record
            
            # 计算期望值
            if len(numbers) == 3 and numbers.isdigit():
                number_list = [int(d) for d in numbers]
                expected_sum = sum(number_list)
                expected_span = max(number_list) - min(number_list)
            else:
                expected_sum = 0
                expected_span = 0
            
            if len(trial_numbers) == 3 and trial_numbers.isdigit():
                trial_number_list = [int(d) for d in trial_numbers]
                expected_trial_sum = sum(trial_number_list)
                expected_trial_span = max(trial_number_list) - min(trial_number_list)
            else:
                expected_trial_sum = 0
                expected_trial_span = 0
            
            # 验证计算结果
            if (sum_value == expected_sum and span_value == expected_span and
                trial_sum_value == expected_trial_sum and trial_span_value == expected_trial_span):
                print(f"✅ 期号 {period}: 计算字段正确")
            else:
                print(f"❌ 期号 {period}: 计算字段错误")
                print(f"   号码: {numbers}, 和值: {sum_value}(期望{expected_sum}), 跨度: {span_value}(期望{expected_span})")
                print(f"   试机号: {trial_numbers}, 和值: {trial_sum_value}(期望{expected_trial_sum}), 跨度: {trial_span_value}(期望{expected_trial_span})")
                all_correct = False
        
        return all_correct

def verify_new_records():
    """验证新增的记录"""
    print("\n4. 验证新增的记录...")
    with sqlite3.connect('data/lottery.db') as conn:
        cursor = conn.cursor()
        
        # 获取最新的记录
        cursor.execute('''
            SELECT period, date, numbers, trial_numbers, sum_value, span_value
            FROM lottery_records 
            ORDER BY period DESC 
            LIMIT 5
        ''')
        
        latest_records = cursor.fetchall()
        print("最新5条记录:")
        
        for record in latest_records:
            period, date, numbers, trial_numbers, sum_value, span_value = record
            print(f"  期号: {period}, 日期: {date}, 号码: {numbers}, 试机号: {trial_numbers}")
            print(f"    和值: {sum_value}, 跨度: {span_value}")
        
        # 检查是否有2025185和2025186期
        target_periods = ['2025185', '2025186']
        found_periods = []
        
        for record in latest_records:
            if record[0] in target_periods:
                found_periods.append(record[0])
        
        if len(found_periods) >= 2:
            print(f"✅ 找到新增记录: {found_periods}")
            return True
        else:
            print(f"⚠️ 只找到部分新增记录: {found_periods}")
            return len(found_periods) > 0

def verify_constraints():
    """验证数据库约束"""
    print("\n5. 验证数据库约束...")
    with sqlite3.connect('data/lottery.db') as conn:
        cursor = conn.cursor()
        
        # 检查期号唯一性
        cursor.execute('''
            SELECT period, COUNT(*) as count 
            FROM lottery_records 
            GROUP BY period 
            HAVING COUNT(*) > 1
        ''')
        
        duplicates = cursor.fetchall()
        if duplicates:
            print(f"❌ 发现重复期号: {len(duplicates)}个")
            for period, count in duplicates[:5]:
                print(f"   期号 {period} 重复 {count} 次")
            return False
        else:
            print("✅ 期号唯一性约束满足")
        
        # 检查数据范围
        cursor.execute('''
            SELECT COUNT(*) FROM lottery_records 
            WHERE sum_value < 0 OR sum_value > 27 
               OR span_value < 0 OR span_value > 9
               OR trial_sum_value < 0 OR trial_sum_value > 27
               OR trial_span_value < 0 OR trial_span_value > 9
        ''')
        
        invalid_ranges = cursor.fetchone()[0]
        if invalid_ranges > 0:
            print(f"❌ 发现 {invalid_ranges} 条记录的计算字段超出合理范围")
            return False
        else:
            print("✅ 计算字段范围合理")
        
        return True

def main():
    """主验证函数"""
    print("开始验证数据完整性...")
    print("=" * 50)
    
    tests = [
        verify_record_count,
        verify_field_completeness,
        verify_calculated_fields,
        verify_new_records,
        verify_constraints
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ 验证失败: {test.__name__}")
        except Exception as e:
            print(f"❌ 验证异常: {test.__name__} - {e}")
    
    print("\n" + "=" * 50)
    print(f"验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 数据完整性验证全部通过！")
        print("✅ 所有字段都有正确值")
        print("✅ 计算字段准确无误")
        print("✅ 数据库约束满足")
        print("✅ 新增记录验证成功")
        return True
    else:
        print("⚠️ 部分验证失败，需要进一步检查")
        return False

if __name__ == "__main__":
    main()
