#!/usr/bin/env python3
"""
Bug检测系统演示启动脚本
创建日期: 2025年7月24日
用途: 一键启动Bug检测系统的各种演示
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                🔍 全自动Bug检测与反馈系统                      ║
    ║                     实际效果演示启动器                         ║
    ║                                                              ║
    ║  🎯 目标: 展示Bug检测系统的实际工作效果                        ║
    ║  🚀 功能: 深度检查、实时监控、智能分析                        ║
    ║  💡 特色: 零配置启动、全功能演示                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major != 3 or python_version.minor < 8:
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
        print("   需要Python 3.8或更高版本")
        return False
    else:
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查必要的包
    required_packages = ['streamlit', 'pandas', 'sqlite3']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}: 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}: 未安装")
    
    if missing_packages:
        print(f"\n⚠️  缺少必要包: {', '.join(missing_packages)}")
        print("请运行: pip install streamlit pandas")
        return False
    
    # 检查文件结构
    required_files = [
        'bug_detection_demo.py',
        'deep_bug_inspection.py',
        'integrate_bug_detection_example.py',
        'src/bug_detection/core/database_manager.py',
        'src/bug_detection/monitoring/js_monitor.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
            print(f"❌ {file_path}: 文件不存在")
        else:
            print(f"✅ {file_path}: 存在")
    
    if missing_files:
        print(f"\n⚠️  缺少必要文件: {len(missing_files)}个")
        return False
    
    print("🎉 环境检查通过！")
    return True

def initialize_database():
    """初始化数据库"""
    print("\n🗄️ 初始化数据库...")
    
    try:
        # 运行数据库初始化脚本
        if os.path.exists('src/database/init_bug_detection.py'):
            result = subprocess.run([
                sys.executable, 'src/database/init_bug_detection.py'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("✅ 数据库初始化成功")
                return True
            else:
                print(f"❌ 数据库初始化失败: {result.stderr}")
        
        # 备用初始化方法
        print("🔄 使用备用方法初始化数据库...")
        sys.path.insert(0, '.')
        
        from src.bug_detection.core.database_manager import DatabaseManager
        db_manager = DatabaseManager()
        print("✅ 数据库管理器初始化成功")
        return True
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False

def show_demo_menu():
    """显示演示菜单"""
    print("\n" + "="*60)
    print("🎮 Bug检测系统演示菜单")
    print("="*60)
    print("1. 🌐 完整功能演示 (Streamlit Web界面)")
    print("2. 🔧 集成示例演示 (福彩3D系统集成)")
    print("3. 🔍 深度检查演示 (命令行工具)")
    print("4. ⚡ 快速健康检查")
    print("5. 📊 系统状态报告")
    print("6. 🧪 Bug测试实验室")
    print("7. 📖 使用指南")
    print("8. 🚪 退出")
    print("-"*60)

def run_streamlit_demo():
    """运行Streamlit演示"""
    print("\n🌐 启动完整功能演示...")
    print("📍 URL: http://localhost:8501")
    print("⏰ 启动中，请稍候...")
    
    try:
        # 启动Streamlit应用
        cmd = [sys.executable, '-m', 'streamlit', 'run', 'bug_detection_demo.py', '--server.port=8501']
        
        print(f"🚀 执行命令: {' '.join(cmd)}")
        
        # 延迟打开浏览器
        def open_browser():
            time.sleep(3)
            webbrowser.open('http://localhost:8501')
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.start()
        
        # 启动Streamlit
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n⏹️ 演示已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def run_integration_demo():
    """运行集成演示"""
    print("\n🔧 启动集成示例演示...")
    print("📍 URL: http://localhost:8502")
    print("⏰ 启动中，请稍候...")
    
    try:
        cmd = [sys.executable, '-m', 'streamlit', 'run', 'integrate_bug_detection_example.py', '--server.port=8502']
        
        print(f"🚀 执行命令: {' '.join(cmd)}")
        
        def open_browser():
            time.sleep(3)
            webbrowser.open('http://localhost:8502')
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.start()
        
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n⏹️ 演示已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def run_deep_inspection():
    """运行深度检查"""
    print("\n🔍 启动深度检查演示...")
    
    try:
        result = subprocess.run([
            sys.executable, 'deep_bug_inspection.py', '--mode=deep'
        ], text=True)
        
        if result.returncode == 0:
            print("✅ 深度检查完成")
        else:
            print("❌ 深度检查失败")
            
    except Exception as e:
        print(f"❌ 深度检查失败: {e}")

def run_quick_health_check():
    """运行快速健康检查"""
    print("\n⚡ 执行快速健康检查...")
    
    try:
        result = subprocess.run([
            sys.executable, 'deep_bug_inspection.py', '--mode=quick'
        ], text=True)
        
        if result.returncode == 0:
            print("✅ 快速检查完成")
        else:
            print("❌ 快速检查失败")
            
    except Exception as e:
        print(f"❌ 快速检查失败: {e}")

def show_system_status():
    """显示系统状态"""
    print("\n📊 系统状态报告")
    print("-"*40)
    
    try:
        sys.path.insert(0, '.')
        from src.bug_detection.core.database_manager import DatabaseManager
        
        # 数据库状态
        db_manager = DatabaseManager()
        print(f"✅ 数据库: {db_manager.db_path}")
        
        # Bug报告统计
        bug_reports = db_manager.get_bug_reports(limit=100)
        print(f"📊 Bug报告总数: {len(bug_reports)}")
        
        if bug_reports:
            severity_stats = {}
            for bug in bug_reports:
                severity = bug.get('severity', 'unknown')
                severity_stats[severity] = severity_stats.get(severity, 0) + 1
            
            print("📈 严重程度分布:")
            for severity, count in severity_stats.items():
                print(f"   {severity}: {count}")
        
        # 性能统计
        performance_summary = db_manager.get_performance_summary()
        print(f"📈 监控的API端点: {len(performance_summary)}")
        
        for endpoint, stats in list(performance_summary.items())[:3]:
            print(f"   {endpoint}: 平均{stats['avg_time']:.3f}s, {stats['count']}次请求")
        
        print("✅ 系统运行正常")
        
    except Exception as e:
        print(f"❌ 获取系统状态失败: {e}")

def run_bug_testing_lab():
    """运行Bug测试实验室"""
    print("\n🧪 Bug测试实验室")
    print("-"*40)
    print("1. 🔧 JavaScript错误测试")
    print("2. 📡 API错误测试") 
    print("3. 💾 数据库错误测试")
    print("4. 🔄 返回主菜单")
    
    choice = input("\n请选择测试类型 (1-4): ").strip()
    
    if choice == '1':
        test_javascript_errors()
    elif choice == '2':
        test_api_errors()
    elif choice == '3':
        test_database_errors()
    elif choice == '4':
        return
    else:
        print("❌ 无效选择")

def test_javascript_errors():
    """测试JavaScript错误"""
    print("\n🔧 JavaScript错误测试")
    print("💡 这将创建一个测试HTML文件来触发JavaScript错误")
    
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>JavaScript错误测试</title>
    </head>
    <body>
        <h1>Bug检测系统 - JavaScript错误测试</h1>
        <button onclick="triggerError()">触发错误</button>
        
        <script>
        function triggerError() {
            // 故意触发错误
            const obj = null;
            console.log(obj.property); // TypeError
        }
        
        // 自动触发错误
        setTimeout(() => {
            throw new Error('自动测试错误');
        }, 2000);
        </script>
    </body>
    </html>
    """
    
    with open('js_error_test.html', 'w', encoding='utf-8') as f:
        f.write(test_html)
    
    print("✅ 测试文件已创建: js_error_test.html")
    print("🌐 在浏览器中打开此文件来测试JavaScript错误检测")

def test_api_errors():
    """测试API错误"""
    print("\n📡 API错误测试")
    
    try:
        sys.path.insert(0, '.')
        from src.bug_detection.core.database_manager import DatabaseManager
        from src.bug_detection.feedback.bug_reporter import IntelligentBugReporter
        
        db_manager = DatabaseManager()
        bug_reporter = IntelligentBugReporter(db_manager)
        
        # 模拟API错误
        error_data = {
            'type': 'api_error',
            'message': 'HTTP 500: Internal Server Error - Test',
            'source': '/api/test/error',
            'page_url': 'http://localhost:8501/test',
            'session_id': f'test_session_{int(time.time())}'
        }
        
        print("🧪 生成测试API错误报告...")
        bug_report = bug_reporter.generate_bug_report(error_data)
        
        print(f"✅ Bug报告已生成: {bug_report['id']}")
        print(f"📊 严重程度: {bug_report['error']['severity']}")
        print(f"🏷️ 分类: {bug_report['category']}")
        
    except Exception as e:
        print(f"❌ API错误测试失败: {e}")

def test_database_errors():
    """测试数据库错误"""
    print("\n💾 数据库错误测试")
    
    try:
        sys.path.insert(0, '.')
        from src.bug_detection.core.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 测试数据库操作
        print("🧪 测试数据库连接...")
        
        # 保存测试数据
        test_bug_data = {
            'error_type': 'database_test',
            'severity': 'low',
            'page_name': 'test_page',
            'error_message': '数据库测试错误',
            'stack_trace': 'Test stack trace'
        }
        
        bug_id = db_manager.save_bug_report(test_bug_data)
        print(f"✅ 测试Bug报告已保存: {bug_id}")
        
        # 获取报告
        reports = db_manager.get_bug_reports(limit=1)
        if reports:
            print(f"✅ 成功获取Bug报告: {len(reports)}个")
        
        print("✅ 数据库功能测试通过")
        
    except Exception as e:
        print(f"❌ 数据库错误测试失败: {e}")

def show_usage_guide():
    """显示使用指南"""
    guide = """
    📖 Bug检测系统使用指南
    ========================
    
    🎯 系统概述
    -----------
    全自动Bug检测与反馈系统是一个企业级的错误监控和分析平台，
    专为福彩3D预测系统设计，提供全方位的Bug检测和智能分析功能。
    
    🚀 快速开始
    -----------
    1. 运行本启动脚本: python start_bug_detection_demo.py
    2. 选择"完整功能演示"查看所有功能
    3. 选择"集成示例演示"查看实际集成效果
    4. 使用"深度检查"进行系统诊断
    
    🔍 主要功能
    -----------
    • JavaScript错误监控: 实时捕获前端错误
    • API性能监控: 监控后端接口性能
    • 智能Bug报告: 自动生成详细的Bug分析报告
    • 用户行为分析: 追踪用户操作模式
    • 自动化测试: E2E测试、单元测试、集成测试
    • 可视化仪表板: 实时监控和数据展示
    
    💡 使用技巧
    -----------
    • 在每个页面都集成JavaScript监控
    • 定期查看Bug报告和性能指标
    • 根据智能建议优先处理高影响问题
    • 建立错误处理的标准流程
    
    🆘 故障排除
    -----------
    • 如果启动失败，请检查Python版本和依赖包
    • 如果数据库错误，请重新运行数据库初始化
    • 如果端口被占用，请修改端口号
    
    📞 技术支持
    -----------
    如有问题，请查看系统日志或联系开发团队。
    """
    
    print(guide)

def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请修复后重试")
        return
    
    # 初始化数据库
    if not initialize_database():
        print("\n⚠️ 数据库初始化失败，某些功能可能受限")
    
    # 主循环
    while True:
        show_demo_menu()
        
        try:
            choice = input("请选择演示类型 (1-8): ").strip()
            
            if choice == '1':
                run_streamlit_demo()
            elif choice == '2':
                run_integration_demo()
            elif choice == '3':
                run_deep_inspection()
            elif choice == '4':
                run_quick_health_check()
            elif choice == '5':
                show_system_status()
            elif choice == '6':
                run_bug_testing_lab()
            elif choice == '7':
                show_usage_guide()
            elif choice == '8':
                print("\n👋 感谢使用Bug检测系统演示！")
                break
            else:
                print("❌ 无效选择，请输入1-8")
                
        except KeyboardInterrupt:
            print("\n\n👋 演示已退出")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")
        
        input("\n按Enter键继续...")

if __name__ == "__main__":
    main()
