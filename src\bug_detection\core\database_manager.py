"""
Bug检测系统数据库管理器
创建日期: 2025年7月24日
"""

import json
import logging
import os
import sqlite3
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

# 导入数据流追踪器
from ..monitoring.data_flow_tracer import (FlowStage, FlowStatus,
                                           add_trace_point)

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Bug检测系统数据库管理器"""
    
    def __init__(self, db_path: str = None):
        """初始化数据库管理器"""
        self.db_path = db_path or self._find_database()
        self._init_tables()
    
    def _find_database(self) -> str:
        """查找现有数据库文件"""
        possible_paths = [
            "lottery_data.db",
            "data/lottery_data.db", 
            "src/database/lottery_data.db"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        return "lottery_data.db"
    
    def _init_tables(self):
        """初始化Bug检测相关表"""
        schema_sql = """
        -- Bug报告表
        CREATE TABLE IF NOT EXISTS bug_reports (
            id TEXT PRIMARY KEY,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            error_type TEXT NOT NULL,
            severity TEXT DEFAULT 'medium',
            page_name TEXT,
            error_message TEXT,
            stack_trace TEXT,
            environment TEXT DEFAULT 'production',
            category TEXT DEFAULT 'general',
            priority TEXT DEFAULT 'medium',
            tags TEXT,
            source TEXT DEFAULT 'user',
            component_name TEXT,
            reproduction_steps TEXT,
            system_context TEXT,
            user_journey TEXT,
            screenshots TEXT,
            status TEXT DEFAULT 'open',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        
        -- 用户行为表
        CREATE TABLE IF NOT EXISTS user_behaviors (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id TEXT NOT NULL,
            page_name TEXT,
            action_type TEXT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        
        -- 性能监控表
        CREATE TABLE IF NOT EXISTS performance_metrics (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            endpoint TEXT,
            response_time REAL,
            status_code INTEGER,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        
        -- JavaScript错误表
        CREATE TABLE IF NOT EXISTS js_errors (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id TEXT,
            error_message TEXT,
            page_url TEXT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        -- 实时事件表
        CREATE TABLE IF NOT EXISTS realtime_events (
            id TEXT PRIMARY KEY,
            event_type TEXT NOT NULL,
            priority INTEGER NOT NULL,
            source TEXT NOT NULL,
            timestamp REAL NOT NULL,
            data TEXT,  -- JSON格式存储
            tags TEXT,  -- JSON格式存储
            correlation_id TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        -- 为realtime_events表创建索引
        CREATE INDEX IF NOT EXISTS idx_event_type ON realtime_events (event_type);
        CREATE INDEX IF NOT EXISTS idx_timestamp ON realtime_events (timestamp);
        CREATE INDEX IF NOT EXISTS idx_priority ON realtime_events (priority);

        -- 事件处理状态表
        CREATE TABLE IF NOT EXISTS event_processing_status (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            event_id TEXT NOT NULL,
            processor_name TEXT NOT NULL,
            status TEXT NOT NULL,  -- pending, processing, completed, failed
            started_at DATETIME,
            completed_at DATETIME,
            error_message TEXT,
            retry_count INTEGER DEFAULT 0,
            FOREIGN KEY (event_id) REFERENCES realtime_events (id)
        );

        -- 性能基线表
        CREATE TABLE IF NOT EXISTS performance_baselines (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            endpoint TEXT NOT NULL UNIQUE,
            avg_response_time REAL,
            error_rate REAL,
            throughput REAL,
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        -- Bug模式表
        CREATE TABLE IF NOT EXISTS bug_patterns (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            pattern_type TEXT NOT NULL,
            regex_patterns TEXT,  -- JSON格式存储
            keywords TEXT,        -- JSON格式存储
            severity_score REAL,
            frequency INTEGER DEFAULT 0,
            confidence REAL DEFAULT 0.0,
            last_seen REAL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        -- 异常告警表
        CREATE TABLE IF NOT EXISTS anomaly_alerts (
            id TEXT PRIMARY KEY,
            alert_type TEXT NOT NULL,
            severity TEXT NOT NULL,
            message TEXT NOT NULL,
            data TEXT,  -- JSON格式存储
            confidence REAL,
            timestamp REAL NOT NULL,
            acknowledged BOOLEAN DEFAULT FALSE,
            acknowledged_by TEXT,
            acknowledged_at DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        """
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 执行schema
            for statement in schema_sql.split(';'):
                if statement.strip():
                    cursor.execute(statement)
            
            conn.commit()
            conn.close()
            logger.info("Bug detection tables initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing tables: {e}")
    
    def save_bug_report(self, bug_data: Dict[str, Any]) -> str:
        """保存Bug报告"""
        # 获取追踪ID（如果存在）
        trace_id = bug_data.get('trace_id')

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            bug_id = f"BUG_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # 添加数据流追踪
            if trace_id:
                add_trace_point(
                    trace_id,
                    FlowStage.DATABASE_STORE,
                    FlowStatus.SUCCESS,
                    {"bug_id": bug_id, "operation": "save_bug_report"}
                )

            # 从增强分析结果中提取信息
            enhanced_analysis = bug_data.get('enhanced_analysis', {})

            # 安全地处理复杂数据类型
            def safe_str(value, default=''):
                if isinstance(value, (dict, list)):
                    import json
                    return json.dumps(value, ensure_ascii=False)
                return str(value) if value is not None else default

            def safe_join_list(value, default=''):
                if isinstance(value, list):
                    return ','.join(str(item) for item in value)
                return str(value) if value is not None else default

            cursor.execute("""
                INSERT INTO bug_reports
                (id, error_type, severity, page_name, error_message, stack_trace,
                 environment, category, priority, tags, source, component_name,
                 reproduction_steps, system_context, user_journey, screenshots)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                bug_id,
                # 优先使用增强分析的分类结果
                enhanced_analysis.get('category', bug_data.get('error_type', 'unknown')),
                enhanced_analysis.get('severity', bug_data.get('severity', 'medium')),
                safe_str(bug_data.get('page_name', '')),
                safe_str(bug_data.get('error_message', '')),
                safe_str(bug_data.get('stack_trace', '')),
                enhanced_analysis.get('environment', bug_data.get('environment', 'production')),
                enhanced_analysis.get('category', bug_data.get('category', 'general')),
                enhanced_analysis.get('priority', bug_data.get('priority', 'medium')),
                safe_join_list(enhanced_analysis.get('tags', [])) or safe_str(bug_data.get('tags', '')),
                bug_data.get('source', 'user'),
                safe_str(bug_data.get('component_name', '')),
                safe_join_list(bug_data.get('reproduction_steps', [])) or safe_str(bug_data.get('reproduction_steps', '')),
                safe_str(bug_data.get('system_context', '')),
                safe_str(bug_data.get('user_journey', '')),
                safe_str(bug_data.get('screenshots', ''))
            ))

            conn.commit()
            conn.close()

            logger.info(f"Bug report saved: {bug_id}")
            return bug_id

        except Exception as e:
            # 添加数据流追踪失败记录
            if trace_id:
                add_trace_point(
                    trace_id,
                    FlowStage.DATABASE_STORE,
                    FlowStatus.FAILED,
                    {},
                    f"数据库保存失败: {str(e)}"
                )
            logger.error(f"Error saving bug report: {e}")
            return ""
    
    def save_performance_metric(self, endpoint: str, response_time: float, status_code: int):
        """保存性能指标"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO performance_metrics (endpoint, response_time, status_code)
                VALUES (?, ?, ?)
            """, (endpoint, response_time, status_code))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error saving performance metric: {e}")
    
    def save_js_error(self, session_id: str, error_message: str, page_url: str):
        """保存JavaScript错误"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO js_errors (session_id, error_message, page_url)
                VALUES (?, ?, ?)
            """, (session_id, error_message, page_url))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error saving JS error: {e}")
    
    def get_bug_reports(self, limit: int = 100) -> List[Dict]:
        """获取Bug报告列表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT * FROM bug_reports
                ORDER BY created_at DESC
                LIMIT ?
            """, (limit,))

            columns = [desc[0] for desc in cursor.description]
            results = []

            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))

            conn.close()
            return results

        except Exception as e:
            logger.error(f"Error getting bug reports: {e}")
            return []

    def update_bug_status(self, bug_id: str, new_status: str, updated_by: str = "system") -> bool:
        """更新Bug状态"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 验证状态值
            valid_statuses = ['open', 'in_progress', 'resolved', 'closed']
            if new_status not in valid_statuses:
                logger.error(f"无效的Bug状态: {new_status}")
                return False

            # 更新Bug状态
            cursor.execute("""
                UPDATE bug_reports
                SET status = ?, updated_at = CURRENT_TIMESTAMP, updated_by = ?
                WHERE id = ?
            """, (new_status, updated_by, bug_id))

            if cursor.rowcount == 0:
                logger.warning(f"未找到Bug ID: {bug_id}")
                conn.close()
                return False

            conn.commit()
            conn.close()

            logger.info(f"Bug状态更新成功: {bug_id} -> {new_status}")
            return True

        except Exception as e:
            logger.error(f"更新Bug状态失败: {e}")
            return False

    def get_bug_by_id(self, bug_id: str) -> Optional[Dict]:
        """根据ID获取单个Bug报告"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT * FROM bug_reports
                WHERE id = ?
            """, (bug_id,))

            row = cursor.fetchone()
            if not row:
                conn.close()
                return None

            columns = [description[0] for description in cursor.description]
            bug_dict = dict(zip(columns, row))

            conn.close()
            return bug_dict

        except Exception as e:
            logger.error(f"获取Bug详情失败: {e}")
            return None

    def get_bugs_by_environment(self, environment: str, limit: int = 100) -> List[Dict]:
        """根据环境获取Bug报告"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT * FROM bug_reports
                WHERE environment = ?
                ORDER BY created_at DESC
                LIMIT ?
            """, (environment, limit))

            columns = [description[0] for description in cursor.description]
            results = []

            for row in cursor.fetchall():
                bug_dict = dict(zip(columns, row))
                results.append(bug_dict)

            conn.close()
            return results

        except Exception as e:
            logger.error(f"获取环境Bug报告失败: {e}")
            return []

    def get_bugs_by_category(self, category: str, limit: int = 100) -> List[Dict]:
        """根据分类获取Bug报告"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT * FROM bug_reports
                WHERE category = ?
                ORDER BY created_at DESC
                LIMIT ?
            """, (category, limit))

            columns = [description[0] for description in cursor.description]
            results = []

            for row in cursor.fetchall():
                bug_dict = dict(zip(columns, row))
                results.append(bug_dict)

            conn.close()
            return results

        except Exception as e:
            logger.error(f"获取分类Bug报告失败: {e}")
            return []

    def get_bug_statistics_by_classification(self) -> Dict[str, Any]:
        """获取按分类统计的Bug数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 按环境统计
            cursor.execute("""
                SELECT
                    environment,
                    COUNT(*) as total,
                    COUNT(CASE WHEN status = 'resolved' THEN 1 END) as resolved,
                    COUNT(CASE WHEN status = 'open' THEN 1 END) as open,
                    COUNT(CASE WHEN priority = 'critical' THEN 1 END) as critical,
                    COUNT(CASE WHEN priority = 'high' THEN 1 END) as high
                FROM bug_reports
                GROUP BY environment
            """)

            env_stats = {}
            for row in cursor.fetchall():
                env, total, resolved, open_bugs, critical, high = row
                env_stats[env] = {
                    'total': total,
                    'resolved': resolved,
                    'open': open_bugs,
                    'critical': critical,
                    'high': high,
                    'resolution_rate': round(resolved / total * 100, 2) if total > 0 else 0
                }

            # 按分类统计
            cursor.execute("""
                SELECT
                    category,
                    COUNT(*) as total,
                    COUNT(CASE WHEN status = 'resolved' THEN 1 END) as resolved
                FROM bug_reports
                GROUP BY category
            """)

            category_stats = {}
            for row in cursor.fetchall():
                category, total, resolved = row
                category_stats[category] = {
                    'total': total,
                    'resolved': resolved,
                    'resolution_rate': round(resolved / total * 100, 2) if total > 0 else 0
                }

            conn.close()

            return {
                'environment': env_stats,
                'category': category_stats
            }

        except Exception as e:
            logger.error(f"获取Bug分类统计失败: {e}")
            return {'environment': {}, 'category': {}}
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT 
                    endpoint,
                    AVG(response_time) as avg_time,
                    MAX(response_time) as max_time,
                    COUNT(*) as count
                FROM performance_metrics 
                WHERE timestamp > datetime('now', '-1 day')
                GROUP BY endpoint
            """)
            
            results = {}
            for row in cursor.fetchall():
                results[row[0]] = {
                    'avg_time': row[1],
                    'max_time': row[2], 
                    'count': row[3]
                }
            
            conn.close()
            return results
            
        except Exception as e:
            logger.error(f"Error getting performance summary: {e}")
            return {}

    # ==================== 实时事件相关方法 ====================

    def save_realtime_event(self, event_data: Dict[str, Any]) -> bool:
        """保存实时事件"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO realtime_events
                (id, event_type, priority, source, timestamp, data, tags, correlation_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                event_data['id'],
                event_data['type'],
                event_data['priority'],
                event_data['source'],
                event_data['timestamp'],
                json.dumps(event_data.get('data', {}), ensure_ascii=False),
                json.dumps(event_data.get('tags', []), ensure_ascii=False),
                event_data.get('correlation_id')
            ))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            logger.error(f"Error saving realtime event: {e}")
            return False

    def get_realtime_events(self, event_type: str = None, limit: int = 100,
                           start_time: float = None, end_time: float = None) -> List[Dict[str, Any]]:
        """获取实时事件"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 构建查询条件
            conditions = []
            params = []

            if event_type:
                conditions.append("event_type = ?")
                params.append(event_type)

            if start_time:
                conditions.append("timestamp >= ?")
                params.append(start_time)

            if end_time:
                conditions.append("timestamp <= ?")
                params.append(end_time)

            where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""

            cursor.execute(f"""
                SELECT id, event_type, priority, source, timestamp, data, tags, correlation_id, created_at
                FROM realtime_events
                {where_clause}
                ORDER BY timestamp DESC
                LIMIT ?
            """, params + [limit])

            events = []
            for row in cursor.fetchall():
                events.append({
                    'id': row[0],
                    'event_type': row[1],
                    'priority': row[2],
                    'source': row[3],
                    'timestamp': row[4],
                    'data': json.loads(row[5]) if row[5] else {},
                    'tags': json.loads(row[6]) if row[6] else [],
                    'correlation_id': row[7],
                    'created_at': row[8]
                })

            conn.close()
            return events

        except Exception as e:
            logger.error(f"Error getting realtime events: {e}")
            return []

    def save_performance_baseline(self, endpoint: str, metrics: Dict[str, float]) -> bool:
        """保存性能基线"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT OR REPLACE INTO performance_baselines
                (endpoint, avg_response_time, error_rate, throughput, last_updated)
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (
                endpoint,
                metrics.get('avg_response_time', 0),
                metrics.get('error_rate', 0),
                metrics.get('throughput', 0)
            ))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            logger.error(f"Error saving performance baseline: {e}")
            return False

    def get_performance_baseline(self, endpoint: str) -> Optional[Dict[str, Any]]:
        """获取性能基线"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT avg_response_time, error_rate, throughput, last_updated
                FROM performance_baselines
                WHERE endpoint = ?
            """, (endpoint,))

            row = cursor.fetchone()
            conn.close()

            if row:
                return {
                    'avg_response_time': row[0],
                    'error_rate': row[1],
                    'throughput': row[2],
                    'last_updated': row[3]
                }
            return None

        except Exception as e:
            logger.error(f"Error getting performance baseline: {e}")
            return None

    def save_bug_pattern(self, pattern_data: Dict[str, Any]) -> bool:
        """保存Bug模式"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT OR REPLACE INTO bug_patterns
                (id, name, pattern_type, regex_patterns, keywords, severity_score,
                 frequency, confidence, last_seen, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (
                pattern_data['id'],
                pattern_data['name'],
                pattern_data['pattern_type'],
                json.dumps(pattern_data.get('regex_patterns', []), ensure_ascii=False),
                json.dumps(pattern_data.get('keywords', []), ensure_ascii=False),
                pattern_data.get('severity_score', 0),
                pattern_data.get('frequency', 0),
                pattern_data.get('confidence', 0),
                pattern_data.get('last_seen')
            ))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            logger.error(f"Error saving bug pattern: {e}")
            return False

    def get_bug_patterns(self) -> List[Dict[str, Any]]:
        """获取Bug模式"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, name, pattern_type, regex_patterns, keywords,
                       severity_score, frequency, confidence, last_seen, updated_at
                FROM bug_patterns
                ORDER BY frequency DESC, confidence DESC
            """)

            patterns = []
            for row in cursor.fetchall():
                patterns.append({
                    'id': row[0],
                    'name': row[1],
                    'pattern_type': row[2],
                    'regex_patterns': json.loads(row[3]) if row[3] else [],
                    'keywords': json.loads(row[4]) if row[4] else [],
                    'severity_score': row[5],
                    'frequency': row[6],
                    'confidence': row[7],
                    'last_seen': row[8],
                    'updated_at': row[9]
                })

            conn.close()
            return patterns

        except Exception as e:
            logger.error(f"Error getting bug patterns: {e}")
            return []

    def save_anomaly_alert(self, alert_data: Dict[str, Any]) -> bool:
        """保存异常告警"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO anomaly_alerts
                (id, alert_type, severity, message, data, confidence, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                alert_data['id'],
                alert_data['alert_type'],
                alert_data['severity'],
                alert_data['message'],
                json.dumps(alert_data.get('data', {}), ensure_ascii=False),
                alert_data.get('confidence', 0),
                alert_data['timestamp']
            ))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            logger.error(f"Error saving anomaly alert: {e}")
            return False

    def get_anomaly_alerts(self, limit: int = 50, acknowledged: bool = None) -> List[Dict[str, Any]]:
        """获取异常告警"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            where_clause = ""
            params = []

            if acknowledged is not None:
                where_clause = "WHERE acknowledged = ?"
                params.append(acknowledged)

            cursor.execute(f"""
                SELECT id, alert_type, severity, message, data, confidence, timestamp,
                       acknowledged, acknowledged_by, acknowledged_at, created_at
                FROM anomaly_alerts
                {where_clause}
                ORDER BY timestamp DESC
                LIMIT ?
            """, params + [limit])

            alerts = []
            for row in cursor.fetchall():
                alerts.append({
                    'id': row[0],
                    'alert_type': row[1],
                    'severity': row[2],
                    'message': row[3],
                    'data': json.loads(row[4]) if row[4] else {},
                    'confidence': row[5],
                    'timestamp': row[6],
                    'acknowledged': bool(row[7]),
                    'acknowledged_by': row[8],
                    'acknowledged_at': row[9],
                    'created_at': row[10]
                })

            conn.close()
            return alerts

        except Exception as e:
            logger.error(f"Error getting anomaly alerts: {e}")
            return []

    def acknowledge_alert(self, alert_id: str, acknowledged_by: str) -> bool:
        """确认告警"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                UPDATE anomaly_alerts
                SET acknowledged = TRUE, acknowledged_by = ?, acknowledged_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (acknowledged_by, alert_id))

            conn.commit()
            conn.close()
            return cursor.rowcount > 0

        except Exception as e:
            logger.error(f"Error acknowledging alert: {e}")
            return False

    def get_realtime_stats(self) -> Dict[str, Any]:
        """获取实时统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            stats = {}

            # 事件统计
            cursor.execute("""
                SELECT event_type, COUNT(*) as count
                FROM realtime_events
                WHERE timestamp > ?
                GROUP BY event_type
            """, (time.time() - 3600,))  # 最近1小时

            stats['events_last_hour'] = dict(cursor.fetchall())

            # 告警统计
            cursor.execute("""
                SELECT severity, COUNT(*) as count
                FROM anomaly_alerts
                WHERE timestamp > ? AND acknowledged = FALSE
                GROUP BY severity
            """, (time.time() - 86400,))  # 最近24小时未确认告警

            stats['unacknowledged_alerts'] = dict(cursor.fetchall())

            # Bug模式统计
            cursor.execute("""
                SELECT COUNT(*) as total_patterns,
                       AVG(confidence) as avg_confidence,
                       MAX(frequency) as max_frequency
                FROM bug_patterns
            """)

            row = cursor.fetchone()
            if row:
                stats['bug_patterns'] = {
                    'total_patterns': row[0],
                    'avg_confidence': row[1] or 0,
                    'max_frequency': row[2] or 0
                }

            conn.close()
            return stats

        except Exception as e:
            logger.error(f"Error getting realtime stats: {e}")
            return {}

    def cleanup_old_events(self, days_to_keep: int = 7) -> int:
        """清理旧事件"""
        try:
            import time
            cutoff_timestamp = time.time() - (days_to_keep * 24 * 3600)

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 清理旧事件
            cursor.execute("DELETE FROM realtime_events WHERE timestamp < ?", (cutoff_timestamp,))
            deleted_events = cursor.rowcount

            # 清理旧告警
            cursor.execute("DELETE FROM anomaly_alerts WHERE timestamp < ? AND acknowledged = TRUE", (cutoff_timestamp,))
            deleted_alerts = cursor.rowcount

            conn.commit()
            conn.close()

            logger.info(f"Cleaned up {deleted_events} old events and {deleted_alerts} old alerts")
            return deleted_events + deleted_alerts

        except Exception as e:
            logger.error(f"Error cleaning up old events: {e}")
            return 0
