import time
from datetime import datetime

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import requests
import streamlit as st


def show_real_time_monitoring():
    """实时监控页面 - 连接真实API服务监控"""
    st.header("📊 实时系统监控")

    # 侧边栏控制
    with st.sidebar:
        st.subheader("🎛️ 监控控制")

        # 自动刷新设置
        auto_refresh = st.checkbox("🔄 自动刷新", value=False)
        if auto_refresh:
            refresh_interval = st.selectbox(
                "刷新间隔",
                options=[5, 10, 30, 60],
                index=1,
                format_func=lambda x: f"{x}秒"
            )
            time.sleep(refresh_interval)
            st.rerun()

        # 手动刷新按钮
        if st.button("🔄 立即刷新", type="primary"):
            st.rerun()

        # 显示设置
        show_performance = st.checkbox("📈 显示性能图表", value=True)
        show_alerts = st.checkbox("🚨 显示系统告警", value=True)
        show_logs = st.checkbox("📋 显示系统日志", value=True)

    # 获取真实的系统状态
    api_base_url = "http://127.0.0.1:8888"

    try:
        # 获取系统健康状态
        health_response = requests.get(f"{api_base_url}/health", timeout=5)
        health_data = health_response.json() if health_response.status_code == 200 else {}

        # 获取系统监控数据
        monitor_response = requests.get(f"{api_base_url}/api/v1/system/monitor", timeout=5)
        monitor_data = monitor_response.json() if monitor_response.status_code == 200 else {}

        # 系统状态概览
        st.subheader("🔧 系统状态概览")

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            api_status = "🟢 正常" if health_response.status_code == 200 else "🔴 异常"
            st.metric("API服务", api_status)

        with col2:
            db_status = "🟢 已连接" if health_data.get('database', {}).get('connected') else "🔴 断开"
            st.metric("数据库连接", db_status)

        with col3:
            model_status = "🟢 就绪" if health_data.get('models', {}).get('loaded') else "🟡 加载中"
            st.metric("模型状态", model_status)

        with col4:
            current_time = datetime.now().strftime("%H:%M:%S")
            st.metric("最后更新", current_time)

        # 数据库状态详情
        if health_data.get('database'):
            st.subheader("💾 数据库状态")

            db_info = health_data['database']
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                total_records = db_info.get('total_records', 0)
                st.metric("总记录数", f"{total_records:,}")

            with col2:
                date_range = db_info.get('date_range', {})
                start_date = date_range.get('start', 'N/A')
                end_date = date_range.get('end', 'N/A')
                st.metric("数据范围", f"{start_date} 至 {end_date}")

            with col3:
                last_update = db_info.get('last_update', 'N/A')
                st.metric("最后更新", last_update)

            with col4:
                data_quality = db_info.get('data_quality', 0)
                st.metric("数据质量", f"{data_quality:.1%}")

        # 模型状态详情
        if health_data.get('models'):
            st.subheader("🤖 模型状态")

            models_info = health_data['models']

            col1, col2, col3, col4 = st.columns(4)

            with col1:
                loaded_models = models_info.get('loaded_count', 0)
                total_models = models_info.get('total_count', 0)
                st.metric("已加载模型", f"{loaded_models}/{total_models}")

            with col2:
                fusion_ready = models_info.get('fusion_ready', False)
                fusion_status = "🟢 就绪" if fusion_ready else "🟡 准备中"
                st.metric("融合系统", fusion_status)

            with col3:
                last_training = models_info.get('last_training', 'N/A')
                st.metric("最后训练", last_training)

            with col4:
                model_accuracy = models_info.get('average_accuracy', 0)
                st.metric("平均准确率", f"{model_accuracy:.2%}")

        # 系统性能监控
        if monitor_data.get('success') and show_performance:
            st.subheader("📈 系统性能监控")

            performance = monitor_data.get('performance', {})

            col1, col2, col3, col4 = st.columns(4)

            with col1:
                cpu_usage = performance.get('cpu_usage', 0)
                cpu_delta = performance.get('cpu_delta', 0)
                st.metric("CPU使用率", f"{cpu_usage:.1f}%", delta=f"{cpu_delta:+.1f}%")

            with col2:
                memory_usage = performance.get('memory_usage', 0)
                memory_delta = performance.get('memory_delta', 0)
                st.metric("内存使用率", f"{memory_usage:.1f}%", delta=f"{memory_delta:+.1f}%")

            with col3:
                response_time = performance.get('avg_response_time', 0)
                response_delta = performance.get('response_delta', 0)
                st.metric("平均响应时间", f"{response_time:.0f}ms", delta=f"{response_delta:+.0f}ms")

            with col4:
                request_count = performance.get('request_count', 0)
                request_delta = performance.get('request_delta', 0)
                st.metric("请求数/分钟", f"{request_count}", delta=f"{request_delta:+d}")

            # 性能趋势图表
            if 'performance_history' in monitor_data:
                st.subheader("📊 性能趋势")

                history = monitor_data['performance_history']

                if history:
                    # 创建性能趋势图
                    timestamps = [item['timestamp'] for item in history]
                    cpu_values = [item['cpu_usage'] for item in history]
                    memory_values = [item['memory_usage'] for item in history]
                    response_values = [item['response_time'] for item in history]

                    fig = go.Figure()

                    fig.add_trace(go.Scatter(
                        x=timestamps,
                        y=cpu_values,
                        mode='lines+markers',
                        name='CPU使用率(%)',
                        line=dict(color='red', width=2)
                    ))

                    fig.add_trace(go.Scatter(
                        x=timestamps,
                        y=memory_values,
                        mode='lines+markers',
                        name='内存使用率(%)',
                        line=dict(color='blue', width=2)
                    ))

                    fig.add_trace(go.Scatter(
                        x=timestamps,
                        y=[rt/10 for rt in response_values],  # 缩放响应时间以便显示
                        mode='lines+markers',
                        name='响应时间(ms/10)',
                        line=dict(color='green', width=2)
                    ))

                    fig.update_layout(
                        title="系统性能趋势（最近1小时）",
                        xaxis_title="时间",
                        yaxis_title="使用率(%)",
                        hovermode='x unified',
                        height=400
                    )

                    st.plotly_chart(fig, use_container_width=True)

        # 预测性能监控
        try:
            prediction_monitor_response = requests.get(f"{api_base_url}/api/v1/prediction/monitor", timeout=5)
            if prediction_monitor_response.status_code == 200:
                prediction_monitor = prediction_monitor_response.json()

                st.subheader("🎯 预测性能监控")

                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    total_predictions = prediction_monitor.get('total_predictions', 0)
                    st.metric("总预测次数", f"{total_predictions:,}")

                with col2:
                    success_rate = prediction_monitor.get('success_rate', 0)
                    st.metric("预测成功率", f"{success_rate:.2%}")

                with col3:
                    avg_confidence = prediction_monitor.get('avg_confidence', 0)
                    st.metric("平均置信度", f"{avg_confidence:.3f}")

                with col4:
                    last_prediction = prediction_monitor.get('last_prediction_time', 'N/A')
                    st.metric("最后预测", last_prediction)

                # 预测准确率趋势
                if 'accuracy_history' in prediction_monitor:
                    accuracy_history = prediction_monitor['accuracy_history']

                    if accuracy_history:
                        periods = [item['period'] for item in accuracy_history]
                        accuracies = [item['accuracy'] for item in accuracy_history]

                        fig_accuracy = px.line(
                            x=periods,
                            y=accuracies,
                            title="预测准确率趋势",
                            labels={'x': '期号', 'y': '准确率'},
                            markers=True
                        )
                        fig_accuracy.update_layout(height=300)
                        st.plotly_chart(fig_accuracy, use_container_width=True)

        except:
            st.warning("预测性能监控数据暂时不可用")

        # 系统告警
        if show_alerts:
            st.subheader("🚨 系统告警")

            try:
                alerts_response = requests.get(f"{api_base_url}/api/v1/system/alerts", timeout=5)
                if alerts_response.status_code == 200:
                    alerts_data = alerts_response.json()
                    alerts = alerts_data.get('alerts', [])

                    if alerts:
                        for alert in alerts[:10]:  # 显示最近10条告警
                            level = alert.get('level', 'info')
                            message = alert.get('message', '')
                            timestamp = alert.get('timestamp', '')

                            level_color = {
                                'critical': '🔴',
                                'warning': '🟡',
                                'info': '🔵',
                                'success': '🟢'
                            }.get(level, '⚪')

                            st.write(f"{level_color} {timestamp} - {message}")
                    else:
                        st.success("✅ 当前无系统告警")
                else:
                    st.info("告警系统暂时不可用")
            except:
                st.warning("无法获取系统告警信息")

        # 系统日志
        if show_logs:
            st.subheader("📋 系统日志")

            try:
                logs_response = requests.get(f"{api_base_url}/api/v1/system/logs", timeout=5)
                if logs_response.status_code == 200:
                    logs_data = logs_response.json()
                    logs = logs_data.get('logs', [])

                    if logs:
                        # 创建日志数据框
                        logs_df = pd.DataFrame(logs[:20])  # 显示最近20条日志

                        if not logs_df.empty:
                            # 格式化时间列
                            if 'timestamp' in logs_df.columns:
                                logs_df['时间'] = pd.to_datetime(logs_df['timestamp']).dt.strftime('%H:%M:%S')

                            # 重命名列
                            column_mapping = {
                                'level': '级别',
                                'message': '消息',
                                'module': '模块'
                            }

                            for old_col, new_col in column_mapping.items():
                                if old_col in logs_df.columns:
                                    logs_df[new_col] = logs_df[old_col]

                            # 选择要显示的列
                            display_cols = ['时间', '级别', '模块', '消息']
                            available_cols = [col for col in display_cols if col in logs_df.columns]

                            if available_cols:
                                st.dataframe(
                                    logs_df[available_cols],
                                    hide_index=True,
                                    use_container_width=True
                                )
                    else:
                        st.info("暂无系统日志")
                else:
                    st.info("日志系统暂时不可用")
            except:
                st.warning("无法获取系统日志")

        # 系统信息摘要
        st.subheader("ℹ️ 系统信息")

        col1, col2 = st.columns(2)

        with col1:
            st.write("**服务状态:**")
            st.write(f"- API服务: {api_status}")
            st.write(f"- 数据库: {db_status}")
            st.write(f"- 模型系统: {model_status}")

        with col2:
            st.write("**监控配置:**")
            st.write(f"- 自动刷新: {'启用' if auto_refresh else '禁用'}")
            st.write(f"- 监控端点: {api_base_url}")
            st.write(f"- 更新时间: {current_time}")

        st.success("✅ 实时监控系统运行正常")

    except requests.exceptions.RequestException as e:
        st.error(f"无法连接到监控API: {str(e)}")
        st.info("请确保API服务(127.0.0.1:8888)正在运行")

        # 显示连接诊断信息
        with st.expander("🔍 连接诊断信息"):
            st.write("**监控端点**: http://127.0.0.1:8888/health")
            st.write("**错误类型**: 连接错误")
            st.write("**可能原因**:")
            st.write("- API服务未启动")
            st.write("- 监控模块未加载")
            st.write("- 网络连接问题")
            st.write("**解决建议**:")
            st.write("- 检查API服务状态")
            st.write("- 重启API服务")
            st.write("- 检查防火墙设置")

    except Exception as e:
        st.error(f"监控系统出现错误: {str(e)}")

        # 显示详细错误信息
        with st.expander("🔍 详细错误信息"):
            st.code(str(e))

if __name__ == "__main__":
    show_real_time_monitoring()
