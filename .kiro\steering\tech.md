# 技术栈和构建系统

## 核心技术栈

### 开发语言
- **Python 3.11.9**: 主要编程语言，严格版本要求

### Web框架
- **Streamlit 1.28+**: 前端用户界面框架
- **FastAPI 0.104+**: 后端API服务框架
- **Uvicorn**: ASGI服务器

### 数据处理
- **Polars 0.19+**: 高性能数据分析引擎（主要）
- **Pandas 2.1+**: 数据处理库（辅助）
- **NumPy 1.24+**: 数值计算库

### 机器学习
- **PyTorch 2.1+**: 深度学习框架
- **Scikit-learn 1.3+**: 机器学习库
- **XGBoost/LightGBM**: 梯度提升算法

### 数据存储
- **SQLite**: 主数据库，使用WAL模式
- **文件缓存**: 临时数据存储

### 数据采集
- **httpx 0.25+**: HTTP客户端
- **BeautifulSoup4**: HTML解析
- **APScheduler**: 任务调度

### 可视化
- **Plotly 5.17+**: 交互式图表
- **Altair 5.1+**: 统计可视化

### 开发工具
- **Ruff**: 代码检查和格式化
- **Pytest**: 测试框架
- **MyPy**: 类型检查
- **Pre-commit**: Git钩子

## 构建和依赖管理

### 项目配置
- **pyproject.toml**: 项目元数据和依赖配置
- **requirements-prod.txt**: 生产环境依赖

### 虚拟环境
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境 (Windows)
venv\Scripts\activate

# 安装依赖
pip install -e .
```

## 常用命令

### 开发环境启动
```bash
# 启动API服务（生产版，推荐）
python start_production_api.py

# 启动Streamlit界面
python start_streamlit.py

# 一键启动（Windows）
start.bat
```

### 代码质量检查
```bash
# 代码检查
ruff check .

# 代码格式化
ruff format .

# 类型检查
mypy src/

# 运行测试
pytest --cov=src
```

### 数据管理
```bash
# 数据更新测试
python test_data_update_complete.py

# 数据库健康检查
python check_database.py

# 性能测试
python performance_benchmark.py
```

### 系统验证
```bash
# API服务测试
python quick_api_test.py

# 完整系统测试
python comprehensive_test.py

# 最终验收测试
python final_acceptance_report.py
```

## 服务端口配置

- **Streamlit UI**: 127.0.0.1:8501
- **FastAPI服务**: 127.0.0.1:8888
- **API文档**: http://127.0.0.1:8888/docs
- **健康检查**: http://127.0.0.1:8888/health

## 性能优化配置

### 数据库优化
- SQLite WAL模式启用
- 连接池管理
- 索引优化策略

### 缓存策略
- 内存缓存（Streamlit session_state）
- 应用缓存（FastAPI内存）
- 数据库查询缓存

### 异步处理
- FastAPI异步端点
- 并发数据处理
- WebSocket实时通信

## 部署要求

### 系统要求
- Windows 10 64位 (版本1903或更高)
- 内存：8GB RAM (推荐16GB)
- 存储：2GB可用空间

### Python环境
- Python 3.11.9（严格版本要求）
- pip 最新版本
- 虚拟环境支持

## 代码规范

### 编码标准
- 遵循PEP 8标准
- 使用类型提示
- 函数和类必须有docstring
- 代码覆盖率 > 80%

### 文件命名
- Python文件：snake_case
- 类名：PascalCase
- 函数名：snake_case
- 常量：UPPER_CASE

### 导入规范
```python
# 标准库导入
import os
import sys

# 第三方库导入
import pandas as pd
import streamlit as st

# 本地模块导入
from src.core.database import DatabaseManager
```

## 错误处理

### 日志配置
- 使用loguru进行日志记录
- 结构化日志格式
- 不同级别的日志输出

### 异常处理
- 统一异常处理机制
- 用户友好的错误提示
- 详细的错误日志记录