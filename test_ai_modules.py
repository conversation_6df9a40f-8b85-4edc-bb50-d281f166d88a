#!/usr/bin/env python3
"""
测试AI模块初始化
"""

import os
import sys
import time

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_error_classifier():
    """测试错误分类器"""
    print("\n🔍 测试错误分类器...")
    try:
        from bug_detection.ai.nlp.error_classifier import ErrorClassifier

        # 初始化分类器
        classifier = ErrorClassifier()
        print("✅ ErrorClassifier初始化成功")
        
        # 测试分类功能
        test_error = "TypeError: Cannot read property 'value' of undefined"
        error_type = classifier.classify_error(test_error)
        print(f"✅ 错误分类结果: {error_type}")
        
        return True
    except Exception as e:
        print(f"❌ ErrorClassifier测试失败: {e}")
        return False

def test_similarity_analyzer():
    """测试相似度分析器"""
    print("\n🔍 测试相似度分析器...")
    try:
        from bug_detection.ai.nlp.similarity_analyzer import SimilarityAnalyzer

        # 初始化分析器
        analyzer = SimilarityAnalyzer()
        print("✅ SimilarityAnalyzer初始化成功")
        
        # 测试相似度计算
        text1 = "Cannot read property 'value' of undefined"
        text2 = "TypeError: Cannot read property 'length' of undefined"
        similarity = analyzer.calculate_similarity(text1, text2)
        print(f"✅ 相似度计算结果: {similarity:.2f}")
        
        return True
    except Exception as e:
        print(f"❌ SimilarityAnalyzer测试失败: {e}")
        return False

def test_ai_manager():
    """测试AI管理器"""
    print("\n🔍 测试AI管理器...")
    try:
        from bug_detection.ai.ai_manager import AIBugDetectionManager

        # 初始化AI管理器
        manager = AIBugDetectionManager()
        print("✅ AIBugDetectionManager初始化成功")
        
        # 测试分析功能
        test_error = {
            'error_message': "TypeError: Cannot read property 'value' of undefined",
            'stack_trace': "at Object.onSubmit (/app/src/components/Form.js:24:10)"
        }
        
        result = manager.analyze_error(test_error)
        print(f"✅ 错误分析结果: {result}")
        
        return True
    except Exception as e:
        print(f"❌ AIManager测试失败: {e}")
        return False

def test_all_modules():
    """测试所有AI模块"""
    print("🚀 开始测试AI模块初始化...")
    
    results = []
    
    # 测试错误分类器
    results.append(test_error_classifier())
    
    # 测试相似度分析器
    results.append(test_similarity_analyzer())
    
    # 测试AI管理器
    results.append(test_ai_manager())
    
    # 汇总结果
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n📊 测试结果: {success_count}/{total_count} 模块初始化成功")
    
    if success_count == total_count:
        print("🎉 所有AI模块初始化测试通过！")
        return True
    else:
        print("⚠️ 部分AI模块初始化测试失败")
        return False

if __name__ == "__main__":
    test_all_modules()
