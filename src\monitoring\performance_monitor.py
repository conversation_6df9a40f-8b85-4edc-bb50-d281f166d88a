#!/usr/bin/env python3
"""
性能监控系统

配置数据库性能监控、WebSocket连接监控、API响应时间监控，建立完整的监控体系
"""

import asyncio
import json
import logging
import statistics
import sys
import time
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Deque
from dataclasses import dataclass, asdict
from threading import Lock

# 添加项目路径
sys.path.append('src')

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetric:
    """性能指标数据类"""
    timestamp: float
    metric_name: str
    value: float
    unit: str
    category: str
    metadata: Dict[str, Any] = None

@dataclass
class AlertRule:
    """告警规则数据类"""
    metric_name: str
    threshold: float
    operator: str  # '>', '<', '>=', '<=', '=='
    duration_seconds: int
    severity: str  # 'low', 'medium', 'high', 'critical'
    enabled: bool = True

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_metrics_per_category: int = 1000):
        """初始化性能监控器"""
        self.max_metrics_per_category = max_metrics_per_category
        self.metrics: Dict[str, Deque[PerformanceMetric]] = defaultdict(lambda: deque(maxlen=max_metrics_per_category))
        self.alert_rules: List[AlertRule] = []
        self.active_alerts: List[Dict[str, Any]] = []
        self.lock = Lock()
        
        # 监控配置
        self.monitoring_enabled = True
        self.alert_cooldown = 300  # 5分钟告警冷却时间
        self.last_alert_times: Dict[str, float] = {}
        
        # 性能阈值配置
        self.performance_thresholds = {
            "api_response_time": 2000,      # API响应时间阈值(ms)
            "database_query_time": 1000,    # 数据库查询时间阈值(ms)
            "websocket_latency": 100,       # WebSocket延迟阈值(ms)
            "memory_usage": 80,             # 内存使用率阈值(%)
            "cpu_usage": 80,                # CPU使用率阈值(%)
            "disk_usage": 90                # 磁盘使用率阈值(%)
        }
        
        # 初始化默认告警规则
        self._setup_default_alert_rules()
        
        logger.info("性能监控器初始化完成")
    
    def _setup_default_alert_rules(self):
        """设置默认告警规则"""
        default_rules = [
            AlertRule("api_response_time", 3000, ">", 60, "high"),
            AlertRule("database_query_time", 2000, ">", 30, "medium"),
            AlertRule("websocket_latency", 200, ">", 30, "medium"),
            AlertRule("memory_usage", 90, ">", 120, "high"),
            AlertRule("cpu_usage", 90, ">", 120, "high"),
            AlertRule("disk_usage", 95, ">", 300, "critical"),
            AlertRule("error_rate", 0.1, ">", 60, "high"),
            AlertRule("connection_count", 100, ">", 60, "medium")
        ]
        
        self.alert_rules.extend(default_rules)
    
    def record_metric(self, metric_name: str, value: float, unit: str = "", 
                     category: str = "general", metadata: Dict[str, Any] = None):
        """记录性能指标"""
        if not self.monitoring_enabled:
            return
        
        try:
            with self.lock:
                metric = PerformanceMetric(
                    timestamp=time.time(),
                    metric_name=metric_name,
                    value=value,
                    unit=unit,
                    category=category,
                    metadata=metadata or {}
                )
                
                self.metrics[category].append(metric)
                
                # 检查告警规则
                self._check_alert_rules(metric)
                
        except Exception as e:
            logger.error(f"记录性能指标失败: {e}")
    
    def record_api_response_time(self, endpoint: str, response_time_ms: float, 
                                status_code: int = 200):
        """记录API响应时间"""
        self.record_metric(
            metric_name="api_response_time",
            value=response_time_ms,
            unit="ms",
            category="api",
            metadata={
                "endpoint": endpoint,
                "status_code": status_code
            }
        )
    
    def record_database_query_time(self, query_type: str, execution_time_ms: float,
                                  table_name: str = None):
        """记录数据库查询时间"""
        self.record_metric(
            metric_name="database_query_time",
            value=execution_time_ms,
            unit="ms",
            category="database",
            metadata={
                "query_type": query_type,
                "table_name": table_name
            }
        )
    
    def record_websocket_latency(self, connection_id: str, latency_ms: float,
                                message_type: str = None):
        """记录WebSocket延迟"""
        self.record_metric(
            metric_name="websocket_latency",
            value=latency_ms,
            unit="ms",
            category="websocket",
            metadata={
                "connection_id": connection_id,
                "message_type": message_type
            }
        )
    
    def record_system_resource(self, resource_type: str, usage_percent: float):
        """记录系统资源使用情况"""
        self.record_metric(
            metric_name=f"{resource_type}_usage",
            value=usage_percent,
            unit="%",
            category="system",
            metadata={
                "resource_type": resource_type
            }
        )
    
    def record_error_rate(self, service: str, error_count: int, total_count: int):
        """记录错误率"""
        error_rate = error_count / total_count if total_count > 0 else 0
        self.record_metric(
            metric_name="error_rate",
            value=error_rate,
            unit="ratio",
            category="reliability",
            metadata={
                "service": service,
                "error_count": error_count,
                "total_count": total_count
            }
        )
    
    def record_connection_count(self, service: str, connection_count: int):
        """记录连接数"""
        self.record_metric(
            metric_name="connection_count",
            value=connection_count,
            unit="count",
            category="connectivity",
            metadata={
                "service": service
            }
        )
    
    def get_metrics_summary(self, category: str = None, 
                           time_range_minutes: int = 60) -> Dict[str, Any]:
        """获取性能指标摘要"""
        try:
            with self.lock:
                current_time = time.time()
                cutoff_time = current_time - (time_range_minutes * 60)
                
                summary = {
                    "timestamp": datetime.now().isoformat(),
                    "time_range_minutes": time_range_minutes,
                    "categories": {}
                }
                
                categories_to_process = [category] if category else self.metrics.keys()
                
                for cat in categories_to_process:
                    if cat not in self.metrics:
                        continue
                    
                    # 过滤时间范围内的指标
                    recent_metrics = [
                        m for m in self.metrics[cat] 
                        if m.timestamp >= cutoff_time
                    ]
                    
                    if not recent_metrics:
                        continue
                    
                    # 按指标名称分组
                    metrics_by_name = defaultdict(list)
                    for metric in recent_metrics:
                        metrics_by_name[metric.metric_name].append(metric.value)
                    
                    category_summary = {}
                    for metric_name, values in metrics_by_name.items():
                        if values:
                            category_summary[metric_name] = {
                                "count": len(values),
                                "avg": round(statistics.mean(values), 2),
                                "min": round(min(values), 2),
                                "max": round(max(values), 2),
                                "median": round(statistics.median(values), 2),
                                "p95": round(statistics.quantiles(values, n=20)[18], 2) if len(values) >= 20 else round(max(values), 2)
                            }
                    
                    summary["categories"][cat] = category_summary
                
                return summary
                
        except Exception as e:
            logger.error(f"获取性能指标摘要失败: {e}")
            return {"error": str(e)}
    
    def get_real_time_metrics(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取实时性能指标"""
        try:
            with self.lock:
                all_metrics = []
                
                for category, metrics in self.metrics.items():
                    for metric in list(metrics)[-limit:]:
                        all_metrics.append({
                            "timestamp": metric.timestamp,
                            "metric_name": metric.metric_name,
                            "value": metric.value,
                            "unit": metric.unit,
                            "category": category,
                            "metadata": metric.metadata
                        })
                
                # 按时间戳排序
                all_metrics.sort(key=lambda x: x["timestamp"], reverse=True)
                
                return all_metrics[:limit]
                
        except Exception as e:
            logger.error(f"获取实时性能指标失败: {e}")
            return []
    
    def _check_alert_rules(self, metric: PerformanceMetric):
        """检查告警规则"""
        try:
            current_time = time.time()
            
            for rule in self.alert_rules:
                if not rule.enabled or rule.metric_name != metric.metric_name:
                    continue
                
                # 检查告警冷却时间
                alert_key = f"{rule.metric_name}_{rule.threshold}_{rule.operator}"
                if alert_key in self.last_alert_times:
                    if current_time - self.last_alert_times[alert_key] < self.alert_cooldown:
                        continue
                
                # 评估告警条件
                if self._evaluate_alert_condition(metric.value, rule.threshold, rule.operator):
                    # 检查持续时间
                    if self._check_alert_duration(rule):
                        self._trigger_alert(rule, metric)
                        self.last_alert_times[alert_key] = current_time
                        
        except Exception as e:
            logger.error(f"检查告警规则失败: {e}")
    
    def _evaluate_alert_condition(self, value: float, threshold: float, operator: str) -> bool:
        """评估告警条件"""
        if operator == ">":
            return value > threshold
        elif operator == "<":
            return value < threshold
        elif operator == ">=":
            return value >= threshold
        elif operator == "<=":
            return value <= threshold
        elif operator == "==":
            return value == threshold
        else:
            return False
    
    def _check_alert_duration(self, rule: AlertRule) -> bool:
        """检查告警持续时间"""
        # 简化实现：如果有规则就触发
        # 实际实现中应该检查指标在指定时间内是否持续超过阈值
        return True
    
    def _trigger_alert(self, rule: AlertRule, metric: PerformanceMetric):
        """触发告警"""
        try:
            alert = {
                "id": f"alert_{int(time.time())}_{rule.metric_name}",
                "timestamp": datetime.now().isoformat(),
                "metric_name": rule.metric_name,
                "current_value": metric.value,
                "threshold": rule.threshold,
                "operator": rule.operator,
                "severity": rule.severity,
                "category": metric.category,
                "metadata": metric.metadata,
                "message": f"{rule.metric_name} ({metric.value}{metric.unit}) {rule.operator} {rule.threshold}{metric.unit}"
            }
            
            self.active_alerts.append(alert)
            
            # 保持告警列表大小
            if len(self.active_alerts) > 100:
                self.active_alerts = self.active_alerts[-100:]
            
            logger.warning(f"性能告警触发: {alert['message']}")
            
        except Exception as e:
            logger.error(f"触发告警失败: {e}")
    
    def get_active_alerts(self, severity: str = None) -> List[Dict[str, Any]]:
        """获取活跃告警"""
        try:
            with self.lock:
                if severity:
                    return [alert for alert in self.active_alerts if alert.get("severity") == severity]
                return list(self.active_alerts)
                
        except Exception as e:
            logger.error(f"获取活跃告警失败: {e}")
            return []
    
    def clear_alerts(self, alert_ids: List[str] = None):
        """清除告警"""
        try:
            with self.lock:
                if alert_ids:
                    self.active_alerts = [
                        alert for alert in self.active_alerts 
                        if alert.get("id") not in alert_ids
                    ]
                else:
                    self.active_alerts.clear()
                    
        except Exception as e:
            logger.error(f"清除告警失败: {e}")
    
    def add_alert_rule(self, rule: AlertRule):
        """添加告警规则"""
        try:
            with self.lock:
                self.alert_rules.append(rule)
                logger.info(f"添加告警规则: {rule.metric_name}")
                
        except Exception as e:
            logger.error(f"添加告警规则失败: {e}")
    
    def remove_alert_rule(self, metric_name: str, threshold: float = None):
        """移除告警规则"""
        try:
            with self.lock:
                if threshold is not None:
                    self.alert_rules = [
                        rule for rule in self.alert_rules 
                        if not (rule.metric_name == metric_name and rule.threshold == threshold)
                    ]
                else:
                    self.alert_rules = [
                        rule for rule in self.alert_rules 
                        if rule.metric_name != metric_name
                    ]
                logger.info(f"移除告警规则: {metric_name}")
                
        except Exception as e:
            logger.error(f"移除告警规则失败: {e}")
    
    def get_performance_dashboard_data(self) -> Dict[str, Any]:
        """获取性能仪表板数据"""
        try:
            current_time = time.time()
            
            # 获取最近1小时的指标摘要
            summary = self.get_metrics_summary(time_range_minutes=60)
            
            # 获取最近的指标
            recent_metrics = self.get_real_time_metrics(limit=50)
            
            # 获取活跃告警
            active_alerts = self.get_active_alerts()
            
            # 计算健康评分
            health_score = self._calculate_health_score()
            
            dashboard_data = {
                "timestamp": datetime.now().isoformat(),
                "health_score": health_score,
                "summary": summary,
                "recent_metrics": recent_metrics,
                "active_alerts": active_alerts,
                "alert_counts": {
                    "critical": len([a for a in active_alerts if a.get("severity") == "critical"]),
                    "high": len([a for a in active_alerts if a.get("severity") == "high"]),
                    "medium": len([a for a in active_alerts if a.get("severity") == "medium"]),
                    "low": len([a for a in active_alerts if a.get("severity") == "low"])
                },
                "monitoring_status": {
                    "enabled": self.monitoring_enabled,
                    "total_metrics": sum(len(metrics) for metrics in self.metrics.values()),
                    "categories": list(self.metrics.keys()),
                    "alert_rules_count": len(self.alert_rules)
                }
            }
            
            return dashboard_data
            
        except Exception as e:
            logger.error(f"获取性能仪表板数据失败: {e}")
            return {"error": str(e)}
    
    def _calculate_health_score(self) -> float:
        """计算系统健康评分"""
        try:
            # 基础评分
            base_score = 10.0
            
            # 根据活跃告警扣分
            for alert in self.active_alerts:
                severity = alert.get("severity", "low")
                if severity == "critical":
                    base_score -= 2.0
                elif severity == "high":
                    base_score -= 1.5
                elif severity == "medium":
                    base_score -= 1.0
                elif severity == "low":
                    base_score -= 0.5
            
            # 根据最近性能指标调整评分
            recent_summary = self.get_metrics_summary(time_range_minutes=15)
            
            for category, metrics in recent_summary.get("categories", {}).items():
                for metric_name, stats in metrics.items():
                    threshold = self.performance_thresholds.get(metric_name)
                    if threshold and stats.get("avg", 0) > threshold:
                        base_score -= 0.5
            
            return max(0.0, min(10.0, base_score))
            
        except Exception as e:
            logger.error(f"计算健康评分失败: {e}")
            return 5.0  # 默认中等评分
    
    def export_metrics(self, category: str = None, 
                      time_range_minutes: int = 60) -> Dict[str, Any]:
        """导出性能指标数据"""
        try:
            with self.lock:
                current_time = time.time()
                cutoff_time = current_time - (time_range_minutes * 60)
                
                export_data = {
                    "export_timestamp": datetime.now().isoformat(),
                    "time_range_minutes": time_range_minutes,
                    "metrics": []
                }
                
                categories_to_export = [category] if category else self.metrics.keys()
                
                for cat in categories_to_export:
                    if cat not in self.metrics:
                        continue
                    
                    for metric in self.metrics[cat]:
                        if metric.timestamp >= cutoff_time:
                            export_data["metrics"].append(asdict(metric))
                
                return export_data
                
        except Exception as e:
            logger.error(f"导出性能指标失败: {e}")
            return {"error": str(e)}
    
    def enable_monitoring(self):
        """启用监控"""
        self.monitoring_enabled = True
        logger.info("性能监控已启用")
    
    def disable_monitoring(self):
        """禁用监控"""
        self.monitoring_enabled = False
        logger.info("性能监控已禁用")
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        with self.lock:
            return {
                "enabled": self.monitoring_enabled,
                "total_metrics": sum(len(metrics) for metrics in self.metrics.values()),
                "categories": list(self.metrics.keys()),
                "alert_rules_count": len(self.alert_rules),
                "active_alerts_count": len(self.active_alerts),
                "last_metric_time": max(
                    (max(m.timestamp for m in metrics) if metrics else 0)
                    for metrics in self.metrics.values()
                ) if self.metrics else 0
            }

# 全局性能监控器实例
performance_monitor = PerformanceMonitor()

def get_performance_monitor() -> PerformanceMonitor:
    """获取全局性能监控器实例"""
    return performance_monitor
