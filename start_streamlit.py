#!/usr/bin/env python3
"""
启动Streamlit应用
"""

import os
import subprocess
import sys
from pathlib import Path


def check_virtual_env():
    """检查是否在虚拟环境中运行"""
    venv_path = Path(__file__).parent / "venv"
    if venv_path.exists():
        current_python = sys.executable

        if not str(current_python).startswith(str(venv_path)):
            print("⚠️ 检测到虚拟环境但未激活")
            print(f"🔧 推荐使用: venv\\Scripts\\activate && python {Path(__file__).name}")
            print(f"🔧 或直接使用: venv\\Scripts\\python.exe {Path(__file__).name}")
            print("🚀 继续使用当前Python环境...")


def start_streamlit():
    """启动Streamlit应用"""
    check_virtual_env()

    print("🚀 启动Streamlit应用...")
    print("📱 应用地址: http://127.0.0.1:8501")
    print("🔗 确保FastAPI服务正在运行: http://127.0.0.1:8888")
    
    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONPATH'] = 'src'
    
    # 启动Streamlit
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run",
            "src/ui/main.py",
            "--server.port=8501",
            "--server.address=127.0.0.1",
            "--browser.gatherUsageStats=false"
        ], env=env)
    except KeyboardInterrupt:
        print("\n👋 Streamlit应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    start_streamlit()
