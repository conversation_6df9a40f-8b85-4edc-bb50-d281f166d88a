#!/usr/bin/env python3
"""
数据更新脚本

用于手动或定时执行数据更新
"""

import sys
import os
from pathlib import Path

# 添加src目录到路径
sys.path.append(str(Path(__file__).parent.parent / 'src'))

from data.incremental_updater import IncrementalUpdater
import argparse
import json
from datetime import datetime


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='福彩3D数据更新工具')
    parser.add_argument('--force', '-f', action='store_true', help='强制更新，即使数据未变化')
    parser.add_argument('--status', '-s', action='store_true', help='只检查状态，不执行更新')
    parser.add_argument('--cleanup', '-c', action='store_true', help='清理旧文件')
    parser.add_argument('--keep', type=int, default=5, help='清理时保留的文件数量（默认5）')
    parser.add_argument('--data-dir', default='data', help='数据目录路径（默认data）')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 设置工作目录
    script_dir = Path(__file__).parent
    project_dir = script_dir.parent
    os.chdir(project_dir)
    
    print("🔄 福彩3D数据更新工具")
    print(f"📁 工作目录: {project_dir}")
    print(f"📊 数据目录: {args.data_dir}")
    print(f"⏰ 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 50)
    
    try:
        # 创建更新器
        updater = IncrementalUpdater(args.data_dir)
        
        # 检查状态
        if args.status:
            print("📋 检查更新状态...")
            status = updater.get_update_status()
            
            print(f"✅ 状态信息:")
            print(f"   上次更新: {status['last_update'] or '从未更新'}")
            print(f"   最新期号: {status['last_period'] or 'N/A'}")
            print(f"   最新日期: {status['last_date'] or 'N/A'}")
            print(f"   记录数量: {status['record_count']:,}")
            print(f"   更新次数: {status['update_count']}")
            print(f"   原始文件: {status['raw_files']}")
            print(f"   处理文件: {status['processed_files']}")
            
            return
        
        # 清理文件
        if args.cleanup:
            print(f"🧹 清理旧文件（保留最新 {args.keep} 个）...")
            cleanup_result = updater.cleanup_old_files(args.keep)
            
            if cleanup_result["success"]:
                print(f"✅ 清理完成:")
                print(f"   删除原始文件: {cleanup_result['deleted_raw_files']}")
                print(f"   删除处理文件: {cleanup_result['deleted_processed_files']}")
            else:
                print(f"❌ 清理失败: {cleanup_result.get('error', 'Unknown error')}")
                return
        
        # 执行更新
        print("🔄 执行数据更新...")
        if args.force:
            print("⚠️  强制更新模式")
        
        result = updater.perform_incremental_update(force_update=args.force)
        
        if result["success"]:
            print(f"✅ 更新成功!")
            
            if "update_info" in result:
                update_info = result["update_info"]
                print(f"📈 更新详情:")
                print(f"   新增记录: {update_info.get('records_added', 0):,}")
                print(f"   总记录数: {update_info.get('total_records', 0):,}")
                print(f"   质量评分: {update_info.get('quality_score', 0):.2f}")
                
                # 显示最新记录
                new_records = result.get("new_records", [])
                if new_records and args.verbose:
                    print(f"   最新记录:")
                    for record in new_records:
                        print(f"     {record['period']} {record['date']} {record['numbers']}")
                
                # 导出结果
                export_results = result.get("export_results", {})
                if export_results:
                    print(f"   导出结果:")
                    for fmt, success in export_results.items():
                        status_icon = "✅" if success else "❌"
                        print(f"     {fmt.upper()}: {status_icon}")
            
            print(f"💾 数据文件已更新")
            
        else:
            message = result.get("message", "Unknown error")
            if "未发生变化" in message:
                print(f"ℹ️  {message}")
                print("   数据源没有新数据，这是正常情况")
            else:
                print(f"❌ 更新失败: {message}")
                return
        
        # 显示最终状态
        if args.verbose:
            print("\n📊 更新后状态:")
            final_status = updater.get_update_status()
            print(f"   最新期号: {final_status['last_period']}")
            print(f"   最新日期: {final_status['last_date']}")
            print(f"   记录数量: {final_status['record_count']:,}")
            print(f"   更新次数: {final_status['update_count']}")
        
        print(f"\n🎉 数据更新完成!")
        
    except KeyboardInterrupt:
        print(f"\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
