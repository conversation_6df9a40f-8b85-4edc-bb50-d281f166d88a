#!/usr/bin/env python3
"""
AI智能Bug检测系统 - 核心管理器
整合NLP、ML、CV、LLM四大AI模块的统一管理接口
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
import json
import os

# 导入AI模块
try:
    from .nlp.error_classifier import ErrorClassifier
    from .nlp.similarity_analyzer import SimilarityAnalyzer
    AI_MODULES_AVAILABLE = True
except ImportError as e:
    logging.warning(f"AI模块导入失败: {e}")
    AI_MODULES_AVAILABLE = False

# 导入现有系统组件
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

try:
    from bug_detection.core.database_manager import DatabaseManager
    from bug_detection.algorithms.enhanced_detection import EnhancedBugDetector
except ImportError as e:
    logging.warning(f"系统组件导入失败: {e}")

logger = logging.getLogger(__name__)

class AIBugDetectionManager:
    """AI智能Bug检测管理器"""
    
    def __init__(self, db_manager: Optional[DatabaseManager] = None):
        self.db_manager = db_manager or DatabaseManager()
        
        # AI模块初始化
        self.error_classifier = None
        self.similarity_analyzer = None
        self.ml_predictor = None  # 待实现
        self.cv_detector = None   # 待实现
        self.llm_generator = None # 待实现
        
        # 配置参数
        self.config = {
            'similarity_threshold': 0.8,
            'ai_analysis_enabled': True,
            'batch_processing_size': 10,
            'cache_results': True,
            'async_processing': True
        }
        
        # 结果缓存
        self.analysis_cache = {}
        
        # 初始化AI模块
        self._initialize_ai_modules()
    
    def _initialize_ai_modules(self):
        """初始化AI模块"""
        try:
            if AI_MODULES_AVAILABLE:
                # 初始化NLP模块
                self.error_classifier = ErrorClassifier()
                self.similarity_analyzer = SimilarityAnalyzer(
                    similarity_threshold=self.config['similarity_threshold']
                )
                
                logger.info("✅ AI模块初始化成功")
            else:
                logger.warning("⚠️ AI模块不可用，将使用传统检测方法")
                
        except Exception as e:
            logger.error(f"AI模块初始化失败: {e}")
    
    async def analyze_error_async(self, error_data: Dict) -> Dict:
        """异步错误分析"""
        try:
            # 检查缓存
            cache_key = self._generate_cache_key(error_data)
            if self.config['cache_results'] and cache_key in self.analysis_cache:
                logger.debug("使用缓存结果")
                return self.analysis_cache[cache_key]
            
            # 并行执行多种AI分析
            tasks = []
            
            # NLP分析任务
            if self.error_classifier:
                tasks.append(self._nlp_analysis_task(error_data))
            
            # 相似度分析任务
            if self.similarity_analyzer:
                tasks.append(self._similarity_analysis_task(error_data))
            
            # ML预测任务 (待实现)
            # if self.ml_predictor:
            #     tasks.append(self._ml_prediction_task(error_data))
            
            # 执行并行分析
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 合并分析结果
                combined_result = self._combine_analysis_results(results, error_data)
            else:
                # 回退到传统分析
                combined_result = self._fallback_analysis(error_data)
            
            # 缓存结果
            if self.config['cache_results']:
                self.analysis_cache[cache_key] = combined_result
            
            return combined_result
            
        except Exception as e:
            logger.error(f"异步错误分析失败: {e}")
            return self._fallback_analysis(error_data)
    
    def analyze_error(self, error_data: Dict) -> Dict:
        """同步错误分析 (兼容现有接口)"""
        try:
            if self.config['async_processing']:
                # 使用异步分析
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                result = loop.run_until_complete(self.analyze_error_async(error_data))
                loop.close()
                return result
            else:
                # 同步分析
                return self._sync_analysis(error_data)
                
        except Exception as e:
            logger.error(f"错误分析失败: {e}")
            return self._fallback_analysis(error_data)
    
    async def _nlp_analysis_task(self, error_data: Dict) -> Dict:
        """NLP分析任务"""
        try:
            error_message = error_data.get('message', '')
            context = {
                'page_url': error_data.get('page_url', ''),
                'source': error_data.get('source', ''),
                'stack_trace': error_data.get('stack_trace', '')
            }
            
            # 执行NLP分类
            classification_result = self.error_classifier.classify_error(error_message, context)
            
            return {
                'module': 'nlp',
                'success': True,
                'result': classification_result
            }
            
        except Exception as e:
            logger.error(f"NLP分析任务失败: {e}")
            return {
                'module': 'nlp',
                'success': False,
                'error': str(e)
            }
    
    async def _similarity_analysis_task(self, error_data: Dict) -> Dict:
        """相似度分析任务"""
        try:
            error_message = error_data.get('message', '')
            
            # 获取历史错误数据
            historical_errors = self.db_manager.get_bug_reports(limit=50)
            
            # 查找相似错误
            similar_errors = self.similarity_analyzer.find_similar_errors(
                error_message, 
                historical_errors,
                threshold=0.7
            )
            
            return {
                'module': 'similarity',
                'success': True,
                'result': {
                    'similar_errors': similar_errors[:5],  # 返回前5个最相似的
                    'similarity_count': len(similar_errors)
                }
            }
            
        except Exception as e:
            logger.error(f"相似度分析任务失败: {e}")
            return {
                'module': 'similarity',
                'success': False,
                'error': str(e)
            }
    
    def _sync_analysis(self, error_data: Dict) -> Dict:
        """同步分析"""
        try:
            result = {
                'analysis_time': datetime.now().isoformat(),
                'ai_enabled': True,
                'modules_used': []
            }
            
            # NLP分析
            if self.error_classifier:
                error_message = error_data.get('message', '')
                context = {
                    'page_url': error_data.get('page_url', ''),
                    'source': error_data.get('source', ''),
                    'stack_trace': error_data.get('stack_trace', '')
                }
                
                nlp_result = self.error_classifier.classify_error(error_message, context)
                result['nlp_analysis'] = nlp_result
                result['modules_used'].append('nlp')
                
                # 使用NLP结果更新主要字段
                result['category'] = nlp_result.get('category', 'general')
                result['severity'] = nlp_result.get('severity', 'medium')
                result['priority'] = nlp_result.get('priority', 'P3')
                result['confidence'] = nlp_result.get('confidence', 0.5)
            
            # 相似度分析
            if self.similarity_analyzer:
                error_message = error_data.get('message', '')
                historical_errors = self.db_manager.get_bug_reports(limit=30)
                
                similar_errors = self.similarity_analyzer.find_similar_errors(
                    error_message, historical_errors, threshold=0.7
                )
                
                result['similarity_analysis'] = {
                    'similar_errors': similar_errors[:3],
                    'similarity_count': len(similar_errors)
                }
                result['modules_used'].append('similarity')
            
            return result
            
        except Exception as e:
            logger.error(f"同步分析失败: {e}")
            return self._fallback_analysis(error_data)
    
    def _combine_analysis_results(self, results: List, error_data: Dict) -> Dict:
        """合并分析结果"""
        try:
            combined = {
                'analysis_time': datetime.now().isoformat(),
                'ai_enabled': True,
                'modules_used': [],
                'category': 'general',
                'severity': 'medium',
                'priority': 'P3',
                'confidence': 0.5
            }
            
            # 处理各模块结果
            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"模块分析异常: {result}")
                    continue
                
                if not result.get('success', False):
                    continue
                
                module = result.get('module', '')
                module_result = result.get('result', {})
                
                combined['modules_used'].append(module)
                
                if module == 'nlp':
                    # NLP结果优先级最高
                    combined['category'] = module_result.get('category', combined['category'])
                    combined['severity'] = module_result.get('severity', combined['severity'])
                    combined['priority'] = module_result.get('priority', combined['priority'])
                    combined['confidence'] = module_result.get('confidence', combined['confidence'])
                    combined['nlp_analysis'] = module_result
                
                elif module == 'similarity':
                    combined['similarity_analysis'] = module_result
            
            return combined
            
        except Exception as e:
            logger.error(f"结果合并失败: {e}")
            return self._fallback_analysis(error_data)
    
    def _fallback_analysis(self, error_data: Dict) -> Dict:
        """回退分析 (使用传统方法)"""
        try:
            # 使用现有的增强检测算法
            enhanced_detector = EnhancedBugDetector(self.db_manager)
            result = enhanced_detector.detect_and_classify(error_data)
            
            # 添加AI标识
            result['ai_enabled'] = False
            result['analysis_method'] = 'traditional'
            result['analysis_time'] = datetime.now().isoformat()
            
            return result
            
        except Exception as e:
            logger.error(f"回退分析失败: {e}")
            return {
                'category': 'general',
                'severity': 'medium',
                'priority': 'P3',
                'confidence': 0.3,
                'ai_enabled': False,
                'analysis_method': 'fallback',
                'error': str(e),
                'analysis_time': datetime.now().isoformat()
            }
    
    def _generate_cache_key(self, error_data: Dict) -> str:
        """生成缓存键"""
        key_data = {
            'message': error_data.get('message', ''),
            'type': error_data.get('type', ''),
            'source': error_data.get('source', '')
        }
        return str(hash(json.dumps(key_data, sort_keys=True)))
    
    def batch_analyze(self, error_list: List[Dict]) -> List[Dict]:
        """批量分析错误"""
        try:
            results = []
            batch_size = self.config['batch_processing_size']
            
            # 分批处理
            for i in range(0, len(error_list), batch_size):
                batch = error_list[i:i + batch_size]
                
                # 处理当前批次
                batch_results = []
                for error_data in batch:
                    result = self.analyze_error(error_data)
                    batch_results.append(result)
                
                results.extend(batch_results)
                
                # 记录进度
                logger.info(f"批量分析进度: {min(i + batch_size, len(error_list))}/{len(error_list)}")
            
            return results
            
        except Exception as e:
            logger.error(f"批量分析失败: {e}")
            return []
    
    def get_ai_status(self) -> Dict:
        """获取AI系统状态"""
        return {
            'ai_modules_available': AI_MODULES_AVAILABLE,
            'error_classifier_loaded': self.error_classifier is not None,
            'similarity_analyzer_loaded': self.similarity_analyzer is not None,
            'ml_predictor_loaded': self.ml_predictor is not None,
            'cv_detector_loaded': self.cv_detector is not None,
            'llm_generator_loaded': self.llm_generator is not None,
            'config': self.config,
            'cache_size': len(self.analysis_cache)
        }
    
    def clear_cache(self):
        """清空分析缓存"""
        self.analysis_cache.clear()
        logger.info("AI分析缓存已清空")
    
    def update_config(self, new_config: Dict):
        """更新配置"""
        self.config.update(new_config)
        logger.info(f"AI配置已更新: {new_config}")
        
        # 重新初始化相关组件
        if 'similarity_threshold' in new_config and self.similarity_analyzer:
            self.similarity_analyzer.similarity_threshold = new_config['similarity_threshold']

# 全局AI管理器实例
_ai_manager_instance = None

def get_ai_manager(db_manager: Optional[DatabaseManager] = None) -> AIBugDetectionManager:
    """获取AI管理器单例"""
    global _ai_manager_instance
    if _ai_manager_instance is None:
        _ai_manager_instance = AIBugDetectionManager(db_manager)
    return _ai_manager_instance

# 使用示例
if __name__ == "__main__":
    # 初始化AI管理器
    ai_manager = AIBugDetectionManager()
    
    # 测试错误数据
    test_error = {
        'message': 'Cannot read property "innerHTML" of null',
        'type': 'javascript',
        'page_url': 'http://127.0.0.1:8501/dashboard',
        'source': 'component.js',
        'stack_trace': 'at HTMLElement.render (component.js:45:12)'
    }
    
    print("🤖 AI智能Bug检测管理器测试")
    print("=" * 50)
    
    # 单个错误分析
    print("\n📊 单个错误分析:")
    result = ai_manager.analyze_error(test_error)
    print(f"分类: {result.get('category', 'unknown')}")
    print(f"严重程度: {result.get('severity', 'unknown')}")
    print(f"优先级: {result.get('priority', 'unknown')}")
    print(f"置信度: {result.get('confidence', 0):.3f}")
    print(f"使用的模块: {result.get('modules_used', [])}")
    
    # AI状态检查
    print(f"\n🔧 AI系统状态:")
    status = ai_manager.get_ai_status()
    for key, value in status.items():
        if key != 'config':
            print(f"  {key}: {value}")
    
    print("\n✅ AI智能Bug检测系统集成完成！")
